﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.IntPtr UnityEngine.AndroidJNIHelper::GetConstructorID(System.IntPtr,System.String)
extern void AndroidJNIHelper_GetConstructorID_m06EE0475AA9AA8D8656E1F5835FFCE2C0EFB92BC (void);
// 0x00000002 System.IntPtr UnityEngine.AndroidJNIHelper::GetMethodID(System.IntPtr,System.String,System.String,System.Boolean)
extern void AndroidJNIHelper_GetMethodID_m25A23BE9C141B9575BB8A332CD7B6F7F394202E3 (void);
// 0x00000003 System.IntPtr UnityEngine.AndroidJNIHelper::GetFieldID(System.IntPtr,System.String,System.String,System.Boolean)
extern void AndroidJNIHelper_GetFieldID_m9ABAB24D635611ADEDF376E9B4F7E3E0D271D38E (void);
// 0x00000004 System.IntPtr UnityEngine.AndroidJNIHelper::CreateJavaRunnable(UnityEngine.AndroidJavaRunnable)
extern void AndroidJNIHelper_CreateJavaRunnable_m1B89587372069109C43FAD8C4C96DA816DA04A03 (void);
// 0x00000005 System.IntPtr UnityEngine.AndroidJNIHelper::CreateJavaProxy(UnityEngine.AndroidJavaProxy)
extern void AndroidJNIHelper_CreateJavaProxy_m9E58A55A573E055FA242C5C6251C715522FC91DB (void);
// 0x00000006 UnityEngine.jvalue[] UnityEngine.AndroidJNIHelper::CreateJNIArgArray(System.Object[])
extern void AndroidJNIHelper_CreateJNIArgArray_mB806BBD1920C763CCDB0BA6DE336425A45D4C361 (void);
// 0x00000007 System.Void UnityEngine.AndroidJNIHelper::DeleteJNIArgArray(System.Object[],UnityEngine.jvalue[])
extern void AndroidJNIHelper_DeleteJNIArgArray_m672B3991A3FF891888AFFF4D6F22767F17C05EA5 (void);
// 0x00000008 System.IntPtr UnityEngine.AndroidJNIHelper::GetConstructorID(System.IntPtr,System.Object[])
extern void AndroidJNIHelper_GetConstructorID_m075C4952D43C603134C0D4CD6FDF98F3041956A6 (void);
// 0x00000009 System.IntPtr UnityEngine.AndroidJNIHelper::GetMethodID(System.IntPtr,System.String,System.Object[],System.Boolean)
extern void AndroidJNIHelper_GetMethodID_m8F55F58B1EC4C8011E42FD6C800176D3D3AE0EE6 (void);
// 0x0000000A ArrayType UnityEngine.AndroidJNIHelper::ConvertFromJNIArray(System.IntPtr)
// 0x0000000B System.IntPtr UnityEngine.AndroidJNIHelper::GetMethodID(System.IntPtr,System.String,System.Object[],System.Boolean)
// 0x0000000C System.IntPtr UnityEngine.AndroidJNIHelper::GetFieldID(System.IntPtr,System.String,System.Boolean)
// 0x0000000D System.IntPtr UnityEngine.AndroidJNI::FindClass(System.String)
extern void AndroidJNI_FindClass_m35CA47A458527E778188E0DAB30E5C7123568C6A (void);
// 0x0000000E System.IntPtr UnityEngine.AndroidJNI::FromReflectedMethod(System.IntPtr)
extern void AndroidJNI_FromReflectedMethod_m5CD5D729593B5F0847D47CBDBE9A8945302C1CE3 (void);
// 0x0000000F System.IntPtr UnityEngine.AndroidJNI::ExceptionOccurred()
extern void AndroidJNI_ExceptionOccurred_mFC131075AAAFFD4A8D511363B749E42B172B42C0 (void);
// 0x00000010 System.Void UnityEngine.AndroidJNI::ExceptionClear()
extern void AndroidJNI_ExceptionClear_m6FA4D64946FB39496A416E7CE6868E337A8C29FB (void);
// 0x00000011 System.Int32 UnityEngine.AndroidJNI::PushLocalFrame(System.Int32)
extern void AndroidJNI_PushLocalFrame_mBE32A038C6E4E5FE5C0661981EDCC57D79D5B20D (void);
// 0x00000012 System.IntPtr UnityEngine.AndroidJNI::PopLocalFrame(System.IntPtr)
extern void AndroidJNI_PopLocalFrame_m0BE71C6B792A2E3F744FEBCC799FD5511F74CD6E (void);
// 0x00000013 System.IntPtr UnityEngine.AndroidJNI::NewGlobalRef(System.IntPtr)
extern void AndroidJNI_NewGlobalRef_m0CBCEAE2F24C059B441287FD8878E69C97A4609C (void);
// 0x00000014 System.Void UnityEngine.AndroidJNI::DeleteGlobalRef(System.IntPtr)
extern void AndroidJNI_DeleteGlobalRef_m5D1E60290B99019EB4CFF1FF2BB965E5B690F5DD (void);
// 0x00000015 System.IntPtr UnityEngine.AndroidJNI::NewWeakGlobalRef(System.IntPtr)
extern void AndroidJNI_NewWeakGlobalRef_m984CDA34234D997E64ACCBCC71D53A97454EA162 (void);
// 0x00000016 System.Void UnityEngine.AndroidJNI::DeleteWeakGlobalRef(System.IntPtr)
extern void AndroidJNI_DeleteWeakGlobalRef_m49B0AC00D8B95435A95013BCE656E3F04A72598E (void);
// 0x00000017 System.IntPtr UnityEngine.AndroidJNI::NewLocalRef(System.IntPtr)
extern void AndroidJNI_NewLocalRef_m87E92FA6F371946D739C72EE34A021C9E5779DF6 (void);
// 0x00000018 System.Void UnityEngine.AndroidJNI::DeleteLocalRef(System.IntPtr)
extern void AndroidJNI_DeleteLocalRef_m0640E773BAD2A500DB3A70F13072B5155B855D91 (void);
// 0x00000019 System.IntPtr UnityEngine.AndroidJNI::NewObject(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_NewObject_m02A159629121FF8B03A612F2B7BB412E95BEED9C (void);
// 0x0000001A System.IntPtr UnityEngine.AndroidJNI::GetObjectClass(System.IntPtr)
extern void AndroidJNI_GetObjectClass_mDF3386F6A5A606B1772741A6706B69861CCBFDFE (void);
// 0x0000001B System.IntPtr UnityEngine.AndroidJNI::GetMethodID(System.IntPtr,System.String,System.String)
extern void AndroidJNI_GetMethodID_mFF49BAEF9F697F41B4325F9C6B2E7443D91AA0B3 (void);
// 0x0000001C System.IntPtr UnityEngine.AndroidJNI::GetFieldID(System.IntPtr,System.String,System.String)
extern void AndroidJNI_GetFieldID_m7E66B43BBF9FFF6578039F549C33DC531C17ADAE (void);
// 0x0000001D System.IntPtr UnityEngine.AndroidJNI::GetStaticMethodID(System.IntPtr,System.String,System.String)
extern void AndroidJNI_GetStaticMethodID_m501CE9457D8834DB97B45AB1EF3FBCF5C0052B36 (void);
// 0x0000001E System.IntPtr UnityEngine.AndroidJNI::GetStaticFieldID(System.IntPtr,System.String,System.String)
extern void AndroidJNI_GetStaticFieldID_m2A9971AE72A90833F8610F069D7E57D44D78F2AB (void);
// 0x0000001F System.IntPtr UnityEngine.AndroidJNI::NewString(System.String)
extern void AndroidJNI_NewString_m67DD4C3BF62BE2EF854288AE991E9C49CA97C8E8 (void);
// 0x00000020 System.IntPtr UnityEngine.AndroidJNI::NewStringFromStr(System.String)
extern void AndroidJNI_NewStringFromStr_mF0F3F777C3349DFFBCA76B342D1CC885C13BC52C (void);
// 0x00000021 System.String UnityEngine.AndroidJNI::GetStringChars(System.IntPtr)
extern void AndroidJNI_GetStringChars_mA34525423F3506039FFF8F4E6F3F0F263A5FE723 (void);
// 0x00000022 System.String UnityEngine.AndroidJNI::CallStringMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStringMethod_mE09892DC06071212833B3C708EC47EE5EC059C80 (void);
// 0x00000023 System.IntPtr UnityEngine.AndroidJNI::CallObjectMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallObjectMethod_m3EBB2273B0EA84B457CBCF76A5E9F090EEA093A8 (void);
// 0x00000024 System.Int32 UnityEngine.AndroidJNI::CallIntMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallIntMethod_m127D249914A292870FAB52D12B28144FC8E7275E (void);
// 0x00000025 System.Boolean UnityEngine.AndroidJNI::CallBooleanMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallBooleanMethod_m0CDF725FF2C96E5B72EA5F2343A2B8FD8BDA20A7 (void);
// 0x00000026 System.Int16 UnityEngine.AndroidJNI::CallShortMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallShortMethod_m663510B7F4E94A030DE4A05E70682A6EFA90F13C (void);
// 0x00000027 System.SByte UnityEngine.AndroidJNI::CallSByteMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallSByteMethod_mE213EDEB1EEF789DE37BEA74A05D046E7807E20E (void);
// 0x00000028 System.Char UnityEngine.AndroidJNI::CallCharMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallCharMethod_m17E6E525A5D1B048FDAE20B99C61A43BA32FC8E2 (void);
// 0x00000029 System.Single UnityEngine.AndroidJNI::CallFloatMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallFloatMethod_mD2438B99FF13BFFE6D834FC7C1C3D925E47CA317 (void);
// 0x0000002A System.Double UnityEngine.AndroidJNI::CallDoubleMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallDoubleMethod_m810BC723EBEFFCFF698965D9A588064CFC40232C (void);
// 0x0000002B System.Int64 UnityEngine.AndroidJNI::CallLongMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallLongMethod_m9232E3B51D89ABAA7D9BC58B42B444156102EABC (void);
// 0x0000002C System.Void UnityEngine.AndroidJNI::CallVoidMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallVoidMethod_m0BF2683A2CD5BA81C057DFC07F417115310B0283 (void);
// 0x0000002D System.String UnityEngine.AndroidJNI::GetStringField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStringField_m15CDC58189B81C49C19276E00BE3F80656342DDF (void);
// 0x0000002E System.IntPtr UnityEngine.AndroidJNI::GetObjectField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetObjectField_m2ECDAF74FFF7EFC858205F87C7264C25C976ED28 (void);
// 0x0000002F System.Boolean UnityEngine.AndroidJNI::GetBooleanField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetBooleanField_mC6F5A0CC5D55A970EB7AAC480395BC8FD6CB17C8 (void);
// 0x00000030 System.SByte UnityEngine.AndroidJNI::GetSByteField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetSByteField_m0B33376868BE72599A879612BD4968A3451AB3F7 (void);
// 0x00000031 System.Char UnityEngine.AndroidJNI::GetCharField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetCharField_mA62DC950E6EE8C88F8126389843A0F9408F33554 (void);
// 0x00000032 System.Int16 UnityEngine.AndroidJNI::GetShortField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetShortField_mC3CD99C2CBFA7F0B11C8527D64FE023344F59E2C (void);
// 0x00000033 System.Int32 UnityEngine.AndroidJNI::GetIntField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetIntField_m5404C0E1CF43B015DE5BA6B27E8BA3EFEA596098 (void);
// 0x00000034 System.Int64 UnityEngine.AndroidJNI::GetLongField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetLongField_m96A3820B70125505E5A9134EB79E16687A992A59 (void);
// 0x00000035 System.Single UnityEngine.AndroidJNI::GetFloatField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetFloatField_m3B29211A1F4FBD7DBFD3D6F39A2CAF30F02947F5 (void);
// 0x00000036 System.Double UnityEngine.AndroidJNI::GetDoubleField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetDoubleField_mA9724BDC49822C2C8F1E733B3333FB27207FB122 (void);
// 0x00000037 System.String UnityEngine.AndroidJNI::CallStaticStringMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticStringMethod_m864F9AB1371B0FDF09703634526B10347F2C2F15 (void);
// 0x00000038 System.IntPtr UnityEngine.AndroidJNI::CallStaticObjectMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticObjectMethod_m0601132D53321BDD71EC8825442D425D6A62AD85 (void);
// 0x00000039 System.Int32 UnityEngine.AndroidJNI::CallStaticIntMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticIntMethod_m55AFC39C5E59C66C4773524231A41002A9A301C1 (void);
// 0x0000003A System.Boolean UnityEngine.AndroidJNI::CallStaticBooleanMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticBooleanMethod_mF0CF8B276B05D318DD21001D33C717D623CF0AE7 (void);
// 0x0000003B System.Int16 UnityEngine.AndroidJNI::CallStaticShortMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticShortMethod_mBBA7160FA2070E2062D8A2CFDD1C5B77643B15C8 (void);
// 0x0000003C System.SByte UnityEngine.AndroidJNI::CallStaticSByteMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticSByteMethod_m72D27748B5B1FD9446F2820039D17F5BFF6E94A4 (void);
// 0x0000003D System.Char UnityEngine.AndroidJNI::CallStaticCharMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticCharMethod_m9C08344C6A639451AABE718F0ED27D24C7EDD6A5 (void);
// 0x0000003E System.Single UnityEngine.AndroidJNI::CallStaticFloatMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticFloatMethod_m01779727D9A97191F0A4C54B5241E8D7D81CEEF3 (void);
// 0x0000003F System.Double UnityEngine.AndroidJNI::CallStaticDoubleMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticDoubleMethod_m120E0E4E343D41C41FB8E0B37272E6D4AB284ECA (void);
// 0x00000040 System.Int64 UnityEngine.AndroidJNI::CallStaticLongMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticLongMethod_m432BF984FAD4465BD4A4B4ED18430063EEA6AFEB (void);
// 0x00000041 System.Void UnityEngine.AndroidJNI::CallStaticVoidMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticVoidMethod_mB21A9A17469B148C29C36852C91C835D621B8B31 (void);
// 0x00000042 System.String UnityEngine.AndroidJNI::GetStaticStringField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStaticStringField_m42CE78EBEFF78787FBD077F75B30616ACF924312 (void);
// 0x00000043 System.IntPtr UnityEngine.AndroidJNI::GetStaticObjectField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStaticObjectField_m94337EA629C5FE55CFB37F8F02A609434FEB5D89 (void);
// 0x00000044 System.Boolean UnityEngine.AndroidJNI::GetStaticBooleanField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStaticBooleanField_m909A15A903875C5D6B3BFA29A67570F96A701C0D (void);
// 0x00000045 System.SByte UnityEngine.AndroidJNI::GetStaticSByteField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStaticSByteField_m591172C28B493955970211BF6AD6F8C7B37AA035 (void);
// 0x00000046 System.Char UnityEngine.AndroidJNI::GetStaticCharField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStaticCharField_m5832AF70C4800FFF72B41A16E388A2CB720202B8 (void);
// 0x00000047 System.Int16 UnityEngine.AndroidJNI::GetStaticShortField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStaticShortField_m407C3A7C748C7E740CF0E5672D839F6ACF14DC05 (void);
// 0x00000048 System.Int32 UnityEngine.AndroidJNI::GetStaticIntField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStaticIntField_mC9C4BFE272E82E8DC6628BA07AA0ADDEB5433217 (void);
// 0x00000049 System.Int64 UnityEngine.AndroidJNI::GetStaticLongField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStaticLongField_m9C1B0514A77EB5D18C5251B8B9F4A3156BD3F28A (void);
// 0x0000004A System.Single UnityEngine.AndroidJNI::GetStaticFloatField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStaticFloatField_m475E8F19BCF56B33B4CF70FF00E44C6E2592E5B1 (void);
// 0x0000004B System.Double UnityEngine.AndroidJNI::GetStaticDoubleField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStaticDoubleField_mED2640B91B2C70640ED82CE175D8B3DF8CE581F7 (void);
// 0x0000004C System.IntPtr UnityEngine.AndroidJNI::ToBooleanArray(System.Boolean[])
extern void AndroidJNI_ToBooleanArray_m0F6247E9CD971CFC3195DF56228139AD6944C454 (void);
// 0x0000004D System.IntPtr UnityEngine.AndroidJNI::ToByteArray(System.Byte[])
extern void AndroidJNI_ToByteArray_m1D13415CE31007C396246CFD721D7CC0B6BE3D89 (void);
// 0x0000004E System.IntPtr UnityEngine.AndroidJNI::ToSByteArray(System.SByte[])
extern void AndroidJNI_ToSByteArray_m3733221CDDCBE6F6E16CFD52194998F49F5739B2 (void);
// 0x0000004F System.IntPtr UnityEngine.AndroidJNI::ToCharArray(System.Char[])
extern void AndroidJNI_ToCharArray_m7A762D085ECCE53C849F82DE1F7D31D0FC0ECCE6 (void);
// 0x00000050 System.IntPtr UnityEngine.AndroidJNI::ToShortArray(System.Int16[])
extern void AndroidJNI_ToShortArray_mE291B1FC62FC5C4C97A1BEDBF79CFE087873E44B (void);
// 0x00000051 System.IntPtr UnityEngine.AndroidJNI::ToIntArray(System.Int32[])
extern void AndroidJNI_ToIntArray_m746B3A15E782ED454B6EB8574C887FE7E5B0E1C3 (void);
// 0x00000052 System.IntPtr UnityEngine.AndroidJNI::ToLongArray(System.Int64[])
extern void AndroidJNI_ToLongArray_m6C231C8FE5E31C148C644FD4EEEEE7A824EFA5E5 (void);
// 0x00000053 System.IntPtr UnityEngine.AndroidJNI::ToFloatArray(System.Single[])
extern void AndroidJNI_ToFloatArray_mB0C6CEEFAD5F590F6E7DD8D6C039F724012B6DA9 (void);
// 0x00000054 System.IntPtr UnityEngine.AndroidJNI::ToDoubleArray(System.Double[])
extern void AndroidJNI_ToDoubleArray_m222C645E8DDA4E4ED85528396DB67012B0BC25F8 (void);
// 0x00000055 System.IntPtr UnityEngine.AndroidJNI::ToObjectArray(System.IntPtr[],System.IntPtr)
extern void AndroidJNI_ToObjectArray_mABBA3396E1FAB07B18841109375EA9FE6EC7D638 (void);
// 0x00000056 System.Boolean[] UnityEngine.AndroidJNI::FromBooleanArray(System.IntPtr)
extern void AndroidJNI_FromBooleanArray_m720A735A65351612CAE3B2A945CDB0D5F28410D0 (void);
// 0x00000057 System.Byte[] UnityEngine.AndroidJNI::FromByteArray(System.IntPtr)
extern void AndroidJNI_FromByteArray_mF9E0A8B97ADE25C3DDC59AB6CECF597AB346AAB2 (void);
// 0x00000058 System.SByte[] UnityEngine.AndroidJNI::FromSByteArray(System.IntPtr)
extern void AndroidJNI_FromSByteArray_mA20709B6C87AF63CBD8DDD4D2DDD57E299A52B3E (void);
// 0x00000059 System.Char[] UnityEngine.AndroidJNI::FromCharArray(System.IntPtr)
extern void AndroidJNI_FromCharArray_m856090D7B9D19A40FFE562BA46F4DAF43D43D883 (void);
// 0x0000005A System.Int16[] UnityEngine.AndroidJNI::FromShortArray(System.IntPtr)
extern void AndroidJNI_FromShortArray_m2730004110D2EEB54E1476957F268792DE68F2F6 (void);
// 0x0000005B System.Int32[] UnityEngine.AndroidJNI::FromIntArray(System.IntPtr)
extern void AndroidJNI_FromIntArray_m774925C30510F5504BEA17EA9B863E97955D6EAA (void);
// 0x0000005C System.Int64[] UnityEngine.AndroidJNI::FromLongArray(System.IntPtr)
extern void AndroidJNI_FromLongArray_m7112B7F6C234E7CD36DEC86064790C59B8DE0C6F (void);
// 0x0000005D System.Single[] UnityEngine.AndroidJNI::FromFloatArray(System.IntPtr)
extern void AndroidJNI_FromFloatArray_mCD844F6AC98D81C0089CE1332D5E660EAC8A45F5 (void);
// 0x0000005E System.Double[] UnityEngine.AndroidJNI::FromDoubleArray(System.IntPtr)
extern void AndroidJNI_FromDoubleArray_m4214A1F5FE02F602A573257C7D8933F0AC851712 (void);
// 0x0000005F System.Int32 UnityEngine.AndroidJNI::GetArrayLength(System.IntPtr)
extern void AndroidJNI_GetArrayLength_m4FF062F3EA299E730221E1F704C0EF5754289628 (void);
// 0x00000060 System.IntPtr UnityEngine.AndroidJNI::NewObjectArray(System.Int32,System.IntPtr,System.IntPtr)
extern void AndroidJNI_NewObjectArray_m0DB0214451F4A4D19F04652627B2F47026E63BCA (void);
// 0x00000061 System.IntPtr UnityEngine.AndroidJNI::GetObjectArrayElement(System.IntPtr,System.Int32)
extern void AndroidJNI_GetObjectArrayElement_m9D4E78BE1E314141634A26ACB948719708054727 (void);
// 0x00000062 System.Void UnityEngine.AndroidJNI::SetObjectArrayElement(System.IntPtr,System.Int32,System.IntPtr)
extern void AndroidJNI_SetObjectArrayElement_mDD16A48E1DA055EE8ACD4065552B2D5EA9CAA0E0 (void);
// 0x00000063 System.Void UnityEngine.AndroidJNISafe::CheckException()
extern void AndroidJNISafe_CheckException_mB129CD38BBE25FCA12F4566ED2DDC0216D7201D9 (void);
// 0x00000064 System.Void UnityEngine.AndroidJNISafe::DeleteGlobalRef(System.IntPtr)
extern void AndroidJNISafe_DeleteGlobalRef_m3F1A798E9B36CFADA2B4D9642AB518D97FB9A975 (void);
// 0x00000065 System.Void UnityEngine.AndroidJNISafe::DeleteWeakGlobalRef(System.IntPtr)
extern void AndroidJNISafe_DeleteWeakGlobalRef_m680EB63DEF7D2B378CD76EC14270F436D7E9CCA6 (void);
// 0x00000066 System.Void UnityEngine.AndroidJNISafe::DeleteLocalRef(System.IntPtr)
extern void AndroidJNISafe_DeleteLocalRef_mA01249A781F9364DFEA78DE907B9CDA1A174E8AE (void);
// 0x00000067 System.IntPtr UnityEngine.AndroidJNISafe::NewString(System.String)
extern void AndroidJNISafe_NewString_mB3309A8C075C006A7C1E0EC91C69374EA6AE5356 (void);
// 0x00000068 System.String UnityEngine.AndroidJNISafe::GetStringChars(System.IntPtr)
extern void AndroidJNISafe_GetStringChars_m9BF01A532EDDB97D55A552AD493FCA0F556669E8 (void);
// 0x00000069 System.IntPtr UnityEngine.AndroidJNISafe::GetObjectClass(System.IntPtr)
extern void AndroidJNISafe_GetObjectClass_m51FDFF60A2A56B970BE41A28D7CBF53D9BA6FC24 (void);
// 0x0000006A System.IntPtr UnityEngine.AndroidJNISafe::GetStaticMethodID(System.IntPtr,System.String,System.String)
extern void AndroidJNISafe_GetStaticMethodID_m9A12CADA3B27CFC904F060CA20D52EF5ED789CCE (void);
// 0x0000006B System.IntPtr UnityEngine.AndroidJNISafe::GetMethodID(System.IntPtr,System.String,System.String)
extern void AndroidJNISafe_GetMethodID_m8F93B005622C963DBB76F72E9A308D2B63EF1CAC (void);
// 0x0000006C System.IntPtr UnityEngine.AndroidJNISafe::GetFieldID(System.IntPtr,System.String,System.String)
extern void AndroidJNISafe_GetFieldID_m9236F3315104F566CD96C3FC88804357D04F3D22 (void);
// 0x0000006D System.IntPtr UnityEngine.AndroidJNISafe::GetStaticFieldID(System.IntPtr,System.String,System.String)
extern void AndroidJNISafe_GetStaticFieldID_m1069199092B17A90933B51436F4F1EEE07FADF5E (void);
// 0x0000006E System.IntPtr UnityEngine.AndroidJNISafe::FromReflectedMethod(System.IntPtr)
extern void AndroidJNISafe_FromReflectedMethod_mA8CBBABDB2EBF0E44052B4E622AB175F79DFF02A (void);
// 0x0000006F System.IntPtr UnityEngine.AndroidJNISafe::FindClass(System.String)
extern void AndroidJNISafe_FindClass_m6A098856373606201AE9A3FB503865D788AE6906 (void);
// 0x00000070 System.IntPtr UnityEngine.AndroidJNISafe::NewObject(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_NewObject_mA904997739C2D552EB2E63ED33D2555BB399A3FB (void);
// 0x00000071 System.IntPtr UnityEngine.AndroidJNISafe::GetStaticObjectField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStaticObjectField_m1E160752B81117A3011BA08491C76D605C928A3A (void);
// 0x00000072 System.String UnityEngine.AndroidJNISafe::GetStaticStringField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStaticStringField_m797C4BF9E360C59AEA19CEB7D968B4F890AFCB92 (void);
// 0x00000073 System.Char UnityEngine.AndroidJNISafe::GetStaticCharField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStaticCharField_m4FF6966837A76028C6DEF9F9627596BF15AF853D (void);
// 0x00000074 System.Double UnityEngine.AndroidJNISafe::GetStaticDoubleField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStaticDoubleField_mD3FC8D3BF793D3EEF5E09C323EFD1AA9940713BE (void);
// 0x00000075 System.Single UnityEngine.AndroidJNISafe::GetStaticFloatField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStaticFloatField_m9A0B6D8422348C435BF0B162BBA3BF2398C367E7 (void);
// 0x00000076 System.Int64 UnityEngine.AndroidJNISafe::GetStaticLongField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStaticLongField_mE21F6072A6C667F03099C76251D3AD55F7290B3C (void);
// 0x00000077 System.Int16 UnityEngine.AndroidJNISafe::GetStaticShortField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStaticShortField_m27844C1495DC221421632E69F435BE06FA358AE8 (void);
// 0x00000078 System.SByte UnityEngine.AndroidJNISafe::GetStaticSByteField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStaticSByteField_mEFF4FF349D74554A04B46AAD9BAD2FC38D127FA8 (void);
// 0x00000079 System.Boolean UnityEngine.AndroidJNISafe::GetStaticBooleanField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStaticBooleanField_m4BE0DB50C689A33BD2155CA3E374F8DCDA1D3175 (void);
// 0x0000007A System.Int32 UnityEngine.AndroidJNISafe::GetStaticIntField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStaticIntField_m72F81AA991CA4BD8FAD87B3EE2A6531A1B049424 (void);
// 0x0000007B System.Void UnityEngine.AndroidJNISafe::CallStaticVoidMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticVoidMethod_m82D3FEEC204FB4AC380A38D21F3CBA1C39C96641 (void);
// 0x0000007C System.IntPtr UnityEngine.AndroidJNISafe::CallStaticObjectMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticObjectMethod_m4D0D7694AF57731B895D3EE6624EEE24B90264B2 (void);
// 0x0000007D System.String UnityEngine.AndroidJNISafe::CallStaticStringMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticStringMethod_mAA0A83FC01520241CE71A63608D0930FD2CCC49B (void);
// 0x0000007E System.Char UnityEngine.AndroidJNISafe::CallStaticCharMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticCharMethod_mA7DABFA2640148CE4D78623C74BD07ADBB798E09 (void);
// 0x0000007F System.Double UnityEngine.AndroidJNISafe::CallStaticDoubleMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticDoubleMethod_m91329AD3D9164B4805FF3129D226F5E176F544F9 (void);
// 0x00000080 System.Single UnityEngine.AndroidJNISafe::CallStaticFloatMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticFloatMethod_mD2EE2202A22B2DE17B216401EC9806CBDF6A5023 (void);
// 0x00000081 System.Int64 UnityEngine.AndroidJNISafe::CallStaticLongMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticLongMethod_mBEF3828B588E18CE4366429338D06A1D2C7D70E5 (void);
// 0x00000082 System.Int16 UnityEngine.AndroidJNISafe::CallStaticShortMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticShortMethod_m9ECDEE47679B84EF709795E9FCA95BA0153E1D72 (void);
// 0x00000083 System.SByte UnityEngine.AndroidJNISafe::CallStaticSByteMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticSByteMethod_mF76AA217926E57302DB89470B5C089FFE0D667E6 (void);
// 0x00000084 System.Boolean UnityEngine.AndroidJNISafe::CallStaticBooleanMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticBooleanMethod_m6A4705BF643A65F96244E6CF6FE72AB583049A9E (void);
// 0x00000085 System.Int32 UnityEngine.AndroidJNISafe::CallStaticIntMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticIntMethod_mE3503898F47B13B069695EB68CA11C93D7B112EB (void);
// 0x00000086 System.IntPtr UnityEngine.AndroidJNISafe::GetObjectField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetObjectField_m91545B311AC4DAA8F38A937F0679277917FA7892 (void);
// 0x00000087 System.String UnityEngine.AndroidJNISafe::GetStringField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStringField_mBC1716407F932E89B1110ECE547770480EEE07CA (void);
// 0x00000088 System.Char UnityEngine.AndroidJNISafe::GetCharField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetCharField_m007EC43BEB159100767F6485C768824DC9EF90B2 (void);
// 0x00000089 System.Double UnityEngine.AndroidJNISafe::GetDoubleField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetDoubleField_mEDE5E15ACDDA40E0F673D88BE62784F5A0A06957 (void);
// 0x0000008A System.Single UnityEngine.AndroidJNISafe::GetFloatField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetFloatField_m9637EF8B8E964B7F7CF9C23871EDE411A3362746 (void);
// 0x0000008B System.Int64 UnityEngine.AndroidJNISafe::GetLongField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetLongField_mCBB0B800D627FF2A3EBDFB0117B6A5AB1890BC70 (void);
// 0x0000008C System.Int16 UnityEngine.AndroidJNISafe::GetShortField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetShortField_mF275D46B8DA641949E39B250C338F147F8BFB701 (void);
// 0x0000008D System.SByte UnityEngine.AndroidJNISafe::GetSByteField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetSByteField_mB5DD4AA1B8986BD295A8EA4E638E456BCCFAE566 (void);
// 0x0000008E System.Boolean UnityEngine.AndroidJNISafe::GetBooleanField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetBooleanField_m32AF9A0F0538463F3475B3DF86CAA20482C97EFA (void);
// 0x0000008F System.Int32 UnityEngine.AndroidJNISafe::GetIntField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetIntField_mC6BE70ED933532599667024D89F5AE1029DDA1E0 (void);
// 0x00000090 System.Void UnityEngine.AndroidJNISafe::CallVoidMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallVoidMethod_mC2A5489151E3D56467FEA9762684A1FBCC201C76 (void);
// 0x00000091 System.IntPtr UnityEngine.AndroidJNISafe::CallObjectMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallObjectMethod_m223C63966A73E23145180079470BD49C56D2F9BA (void);
// 0x00000092 System.String UnityEngine.AndroidJNISafe::CallStringMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStringMethod_mB268997CA18EF3343019308959A619C4C3820891 (void);
// 0x00000093 System.Char UnityEngine.AndroidJNISafe::CallCharMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallCharMethod_mB306445351D496DBAC531F3DAB699A30024AEA07 (void);
// 0x00000094 System.Double UnityEngine.AndroidJNISafe::CallDoubleMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallDoubleMethod_mB5942D848CF372EADF39CCF3CF5B5F4385C927C0 (void);
// 0x00000095 System.Single UnityEngine.AndroidJNISafe::CallFloatMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallFloatMethod_mB14DBDEB900A35991155EE7C1739C1DEB112B28F (void);
// 0x00000096 System.Int64 UnityEngine.AndroidJNISafe::CallLongMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallLongMethod_m41D5D8467D274916EC33C33AC7540F88F8F46172 (void);
// 0x00000097 System.Int16 UnityEngine.AndroidJNISafe::CallShortMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallShortMethod_m5FA668201182B432D880AF3F035C1DD542FC6D6B (void);
// 0x00000098 System.SByte UnityEngine.AndroidJNISafe::CallSByteMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallSByteMethod_m04C4E22429DCA9E3271A905387CBA406DD170F16 (void);
// 0x00000099 System.Boolean UnityEngine.AndroidJNISafe::CallBooleanMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallBooleanMethod_m3253095C758D3CE4C3C1FCB2E55B956BB6D832A4 (void);
// 0x0000009A System.Int32 UnityEngine.AndroidJNISafe::CallIntMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallIntMethod_m2B2E4495CF5E2135E8F42D3E90F7C0902AD78771 (void);
// 0x0000009B System.Char[] UnityEngine.AndroidJNISafe::FromCharArray(System.IntPtr)
extern void AndroidJNISafe_FromCharArray_m89C34EFEB01E5CD0BE1BC887DCDAFEA08D109CC5 (void);
// 0x0000009C System.Double[] UnityEngine.AndroidJNISafe::FromDoubleArray(System.IntPtr)
extern void AndroidJNISafe_FromDoubleArray_mE04CDB2DB5C370D911932A6E39129373BCA61F78 (void);
// 0x0000009D System.Single[] UnityEngine.AndroidJNISafe::FromFloatArray(System.IntPtr)
extern void AndroidJNISafe_FromFloatArray_m6AAD54CA85C4DDBCE6B97452C70C9499D87C1870 (void);
// 0x0000009E System.Int64[] UnityEngine.AndroidJNISafe::FromLongArray(System.IntPtr)
extern void AndroidJNISafe_FromLongArray_mF288E2462595D10E09175957923103B5AA77AE81 (void);
// 0x0000009F System.Int16[] UnityEngine.AndroidJNISafe::FromShortArray(System.IntPtr)
extern void AndroidJNISafe_FromShortArray_m967E0145E1CCD91B7A71A8CA1EC571887B637808 (void);
// 0x000000A0 System.Byte[] UnityEngine.AndroidJNISafe::FromByteArray(System.IntPtr)
extern void AndroidJNISafe_FromByteArray_mD8574154381ED1F115082B1C729FDFB6B38AB452 (void);
// 0x000000A1 System.SByte[] UnityEngine.AndroidJNISafe::FromSByteArray(System.IntPtr)
extern void AndroidJNISafe_FromSByteArray_m09370C783EB0D689FF1BBF662BD14EC6E83967B2 (void);
// 0x000000A2 System.Boolean[] UnityEngine.AndroidJNISafe::FromBooleanArray(System.IntPtr)
extern void AndroidJNISafe_FromBooleanArray_m59EBFEDF2F02FBBC0E6F5C438DDC1046D792283F (void);
// 0x000000A3 System.Int32[] UnityEngine.AndroidJNISafe::FromIntArray(System.IntPtr)
extern void AndroidJNISafe_FromIntArray_m637AA08C7F6FFC5CCE3A548AFC9A5F649139E7FA (void);
// 0x000000A4 System.IntPtr UnityEngine.AndroidJNISafe::ToObjectArray(System.IntPtr[],System.IntPtr)
extern void AndroidJNISafe_ToObjectArray_mC6B3036691E5731978E2CF792759AA10D7BBC337 (void);
// 0x000000A5 System.IntPtr UnityEngine.AndroidJNISafe::ToCharArray(System.Char[])
extern void AndroidJNISafe_ToCharArray_mCA6E04C48615372BEF89C7FDF2CE7FDD1E001BF4 (void);
// 0x000000A6 System.IntPtr UnityEngine.AndroidJNISafe::ToDoubleArray(System.Double[])
extern void AndroidJNISafe_ToDoubleArray_mC3E4F76BE9EA0FBF0F9C31D99DAC5A56C30BD58E (void);
// 0x000000A7 System.IntPtr UnityEngine.AndroidJNISafe::ToFloatArray(System.Single[])
extern void AndroidJNISafe_ToFloatArray_m19DFDC1CBE592872AEB7B50021456C265687E07E (void);
// 0x000000A8 System.IntPtr UnityEngine.AndroidJNISafe::ToLongArray(System.Int64[])
extern void AndroidJNISafe_ToLongArray_mE0E955A472A920B2F189B49DDADE22F00E2C8CB8 (void);
// 0x000000A9 System.IntPtr UnityEngine.AndroidJNISafe::ToShortArray(System.Int16[])
extern void AndroidJNISafe_ToShortArray_mFA01F104310D594C3884A8BD697A963568D07B91 (void);
// 0x000000AA System.IntPtr UnityEngine.AndroidJNISafe::ToByteArray(System.Byte[])
extern void AndroidJNISafe_ToByteArray_m7843B87143DAC4FC16F3C0B5D44BA44158D7DBCF (void);
// 0x000000AB System.IntPtr UnityEngine.AndroidJNISafe::ToSByteArray(System.SByte[])
extern void AndroidJNISafe_ToSByteArray_mF90F6599E51AA285738E2013753DEA1917C3B224 (void);
// 0x000000AC System.IntPtr UnityEngine.AndroidJNISafe::ToBooleanArray(System.Boolean[])
extern void AndroidJNISafe_ToBooleanArray_m423EA13D4D1D1B253DD04ED7EE2CAA8E916C5DD5 (void);
// 0x000000AD System.IntPtr UnityEngine.AndroidJNISafe::ToIntArray(System.Int32[])
extern void AndroidJNISafe_ToIntArray_m6942A9FD3416839F741B033C85E1ABFDF1549BE4 (void);
// 0x000000AE System.IntPtr UnityEngine.AndroidJNISafe::GetObjectArrayElement(System.IntPtr,System.Int32)
extern void AndroidJNISafe_GetObjectArrayElement_m58038654EA99F36B2B6E3B992DA845FF340D4B46 (void);
// 0x000000AF System.Int32 UnityEngine.AndroidJNISafe::GetArrayLength(System.IntPtr)
extern void AndroidJNISafe_GetArrayLength_m606B22284E0BB24E92FB7A3A8B5A878798281AA8 (void);
// 0x000000B0 System.Void UnityEngine.AndroidJavaRunnable::.ctor(System.Object,System.IntPtr)
extern void AndroidJavaRunnable__ctor_m385F79E7C422595C3F8504D588807799A7CF4727 (void);
// 0x000000B1 System.Void UnityEngine.AndroidJavaRunnable::Invoke()
extern void AndroidJavaRunnable_Invoke_m014FD2895FDB86ADBD97D9CEC201592673D9593B (void);
// 0x000000B2 System.IAsyncResult UnityEngine.AndroidJavaRunnable::BeginInvoke(System.AsyncCallback,System.Object)
extern void AndroidJavaRunnable_BeginInvoke_mC5CE46E7743D650AFC705153AEACED181B1FC15C (void);
// 0x000000B3 System.Void UnityEngine.AndroidJavaRunnable::EndInvoke(System.IAsyncResult)
extern void AndroidJavaRunnable_EndInvoke_m5ED563F2A458EB2F5A614815B85EF79ACB60BFAF (void);
// 0x000000B4 System.Void UnityEngine.AndroidJavaException::.ctor(System.String,System.String)
extern void AndroidJavaException__ctor_mD451AB9B54A333C79F0F0650DD4DC2B173CAA910 (void);
// 0x000000B5 System.String UnityEngine.AndroidJavaException::get_StackTrace()
extern void AndroidJavaException_get_StackTrace_m3A87C920F33472028A05370C7C320C0196B209E9 (void);
// 0x000000B6 System.Void UnityEngine.GlobalJavaObjectRef::.ctor(System.IntPtr)
extern void GlobalJavaObjectRef__ctor_mC4872ABD6EDA3531D1012C2FB7B73EE2B1652C5D (void);
// 0x000000B7 System.Void UnityEngine.GlobalJavaObjectRef::Finalize()
extern void GlobalJavaObjectRef_Finalize_m455E5786F72D4A0DBBA209EE67AE763130FBCE3A (void);
// 0x000000B8 System.IntPtr UnityEngine.GlobalJavaObjectRef::op_Implicit(UnityEngine.GlobalJavaObjectRef)
extern void GlobalJavaObjectRef_op_Implicit_m8573DFC0939E91FAA4E32C87D7652907626D3746 (void);
// 0x000000B9 System.Void UnityEngine.GlobalJavaObjectRef::Dispose()
extern void GlobalJavaObjectRef_Dispose_mDAF2DE01EBD608E63CB661954D0B50015D08FF28 (void);
// 0x000000BA System.Void UnityEngine.AndroidJavaRunnableProxy::.ctor(UnityEngine.AndroidJavaRunnable)
extern void AndroidJavaRunnableProxy__ctor_mDF1B01EBE4C312B67A43B452B440303855973A89 (void);
// 0x000000BB System.Void UnityEngine.AndroidJavaProxy::.ctor(System.String)
extern void AndroidJavaProxy__ctor_m1E8F4C0D87B74B81C64A54A584B2FF4ECE571238 (void);
// 0x000000BC System.Void UnityEngine.AndroidJavaProxy::.ctor(UnityEngine.AndroidJavaClass)
extern void AndroidJavaProxy__ctor_m17BDD42A24CEBD07722B68A25CAD6DEAF64241E1 (void);
// 0x000000BD System.Void UnityEngine.AndroidJavaProxy::Finalize()
extern void AndroidJavaProxy_Finalize_m7F5D1C533F2D1A4A1A286D00E14282E48A55F96F (void);
// 0x000000BE UnityEngine.AndroidJavaObject UnityEngine.AndroidJavaProxy::Invoke(System.String,System.Object[])
extern void AndroidJavaProxy_Invoke_m0908B96EF03FC0465EAC584963213D227A3048D0 (void);
// 0x000000BF UnityEngine.AndroidJavaObject UnityEngine.AndroidJavaProxy::Invoke(System.String,UnityEngine.AndroidJavaObject[])
extern void AndroidJavaProxy_Invoke_m7DD1AD609B664D23BB571F86B0EFCA65DBEF0561 (void);
// 0x000000C0 UnityEngine.AndroidJavaObject UnityEngine.AndroidJavaProxy::GetProxyObject()
extern void AndroidJavaProxy_GetProxyObject_mEDC1D96AAE0A4FC2B4E57E1FC3AE5C47C27FDC6A (void);
// 0x000000C1 System.IntPtr UnityEngine.AndroidJavaProxy::GetRawProxy()
extern void AndroidJavaProxy_GetRawProxy_m125686B87104A00FD20DEB87F24074A3AD68D354 (void);
// 0x000000C2 System.Void UnityEngine.AndroidJavaProxy::.cctor()
extern void AndroidJavaProxy__cctor_m495EBB573F2215B69BE68C609BAFAC202401C488 (void);
// 0x000000C3 System.Void UnityEngine.AndroidJavaObject::.ctor(System.String,System.Object[])
extern void AndroidJavaObject__ctor_m6146DBD19BCFFDB3D4F42C8D38491F354B58B001 (void);
// 0x000000C4 System.Void UnityEngine.AndroidJavaObject::Dispose()
extern void AndroidJavaObject_Dispose_mFF9FA58B47A7FC0C083E034B364EAAE2E7282C45 (void);
// 0x000000C5 System.Void UnityEngine.AndroidJavaObject::Call(System.String,System.Object[])
extern void AndroidJavaObject_Call_mBB226DA52CE5A2FCD9A2D42BC7FB4272E094B76D (void);
// 0x000000C6 FieldType UnityEngine.AndroidJavaObject::Get(System.String)
// 0x000000C7 FieldType UnityEngine.AndroidJavaObject::GetStatic(System.String)
// 0x000000C8 System.IntPtr UnityEngine.AndroidJavaObject::GetRawObject()
extern void AndroidJavaObject_GetRawObject_m09E30F9AF9DD2AE07D34CE3915BDA557E206271D (void);
// 0x000000C9 System.IntPtr UnityEngine.AndroidJavaObject::GetRawClass()
extern void AndroidJavaObject_GetRawClass_m051337FD2D7C0E765641B192D86FF4A06257836B (void);
// 0x000000CA ReturnType UnityEngine.AndroidJavaObject::Call(System.String,System.Object[])
// 0x000000CB ReturnType UnityEngine.AndroidJavaObject::CallStatic(System.String,System.Object[])
// 0x000000CC System.Void UnityEngine.AndroidJavaObject::DebugPrint(System.String)
extern void AndroidJavaObject_DebugPrint_m4F2A8B6447F29A548C6AE6F74CBD3AF51B587F7D (void);
// 0x000000CD System.Void UnityEngine.AndroidJavaObject::_AndroidJavaObject(System.String,System.Object[])
extern void AndroidJavaObject__AndroidJavaObject_m54584F0F147ECA1A0B7DEB875742B3331CC1666A (void);
// 0x000000CE System.Void UnityEngine.AndroidJavaObject::.ctor(System.IntPtr)
extern void AndroidJavaObject__ctor_m880F6533139DF0BD36C6EF428E45E9F44B6534A3 (void);
// 0x000000CF System.Void UnityEngine.AndroidJavaObject::.ctor()
extern void AndroidJavaObject__ctor_m76D62BEE18E830EBCDE4B6860AC3C8A5979C9F18 (void);
// 0x000000D0 System.Void UnityEngine.AndroidJavaObject::Finalize()
extern void AndroidJavaObject_Finalize_mE74A8B42EE112C9889752419D2AA209CD08704A2 (void);
// 0x000000D1 System.Void UnityEngine.AndroidJavaObject::Dispose(System.Boolean)
extern void AndroidJavaObject_Dispose_m881D1EC65C5FE625E9F84D81308E966CAC607AFC (void);
// 0x000000D2 System.Void UnityEngine.AndroidJavaObject::_Call(System.String,System.Object[])
extern void AndroidJavaObject__Call_m5F23B0D456587BD5B01C41EB4C472AF59FF40946 (void);
// 0x000000D3 ReturnType UnityEngine.AndroidJavaObject::_Call(System.String,System.Object[])
// 0x000000D4 FieldType UnityEngine.AndroidJavaObject::_Get(System.String)
// 0x000000D5 ReturnType UnityEngine.AndroidJavaObject::_CallStatic(System.String,System.Object[])
// 0x000000D6 FieldType UnityEngine.AndroidJavaObject::_GetStatic(System.String)
// 0x000000D7 UnityEngine.AndroidJavaObject UnityEngine.AndroidJavaObject::AndroidJavaObjectDeleteLocalRef(System.IntPtr)
extern void AndroidJavaObject_AndroidJavaObjectDeleteLocalRef_m894328C5BFEE7BE03298425049B09815D0CAB572 (void);
// 0x000000D8 UnityEngine.AndroidJavaClass UnityEngine.AndroidJavaObject::AndroidJavaClassDeleteLocalRef(System.IntPtr)
extern void AndroidJavaObject_AndroidJavaClassDeleteLocalRef_mB3FBC86ACFCE28F09F85D5D8777806294C5A2965 (void);
// 0x000000D9 ReturnType UnityEngine.AndroidJavaObject::FromJavaArrayDeleteLocalRef(System.IntPtr)
// 0x000000DA System.IntPtr UnityEngine.AndroidJavaObject::_GetRawObject()
extern void AndroidJavaObject__GetRawObject_mD52629909E16A092896CD884BB710F876BE14975 (void);
// 0x000000DB System.IntPtr UnityEngine.AndroidJavaObject::_GetRawClass()
extern void AndroidJavaObject__GetRawClass_mCEFEDEA158099706B9B2F1CB40C843D2E899932C (void);
// 0x000000DC System.Void UnityEngine.AndroidJavaObject::.cctor()
extern void AndroidJavaObject__cctor_m920EF92A44A20E2EA89BCF989E7855C2A0AF65F5 (void);
// 0x000000DD System.Void UnityEngine.AndroidJavaClass::.ctor(System.String)
extern void AndroidJavaClass__ctor_mEFF9F51871F231955D97DABDE9AB4A6B4EDA5541 (void);
// 0x000000DE System.Void UnityEngine.AndroidJavaClass::_AndroidJavaClass(System.String)
extern void AndroidJavaClass__AndroidJavaClass_mD69A3836B7DD13F6514499FFE167BCB136A5BBAB (void);
// 0x000000DF System.Void UnityEngine.AndroidJavaClass::.ctor(System.IntPtr)
extern void AndroidJavaClass__ctor_m61AE95F9D20CA648AEFC18AE922FE70F0B90786F (void);
// 0x000000E0 System.Boolean UnityEngine.AndroidReflection::IsPrimitive(System.Type)
extern void AndroidReflection_IsPrimitive_m6AC935960F806BD35938B70EB70E9FF90F03521E (void);
// 0x000000E1 System.Boolean UnityEngine.AndroidReflection::IsAssignableFrom(System.Type,System.Type)
extern void AndroidReflection_IsAssignableFrom_mD2754AFA331CB318E960432DB3F4270B860A9284 (void);
// 0x000000E2 System.IntPtr UnityEngine.AndroidReflection::GetStaticMethodID(System.String,System.String,System.String)
extern void AndroidReflection_GetStaticMethodID_m4C6530446E01E0CCD82437DC5E0301EA265699F3 (void);
// 0x000000E3 System.IntPtr UnityEngine.AndroidReflection::GetMethodID(System.String,System.String,System.String)
extern void AndroidReflection_GetMethodID_m551139A025A4DDC58D0F076FD26E77879F50BA05 (void);
// 0x000000E4 System.IntPtr UnityEngine.AndroidReflection::GetConstructorMember(System.IntPtr,System.String)
extern void AndroidReflection_GetConstructorMember_mB320ED3A0990352093EAA566BAB4A57054FCF973 (void);
// 0x000000E5 System.IntPtr UnityEngine.AndroidReflection::GetMethodMember(System.IntPtr,System.String,System.String,System.Boolean)
extern void AndroidReflection_GetMethodMember_mEB2499209A0103545F59AF367168355BE863D93D (void);
// 0x000000E6 System.IntPtr UnityEngine.AndroidReflection::GetFieldMember(System.IntPtr,System.String,System.String,System.Boolean)
extern void AndroidReflection_GetFieldMember_mDF940ADAC9599730E7673B122259222B70937F52 (void);
// 0x000000E7 System.IntPtr UnityEngine.AndroidReflection::GetFieldClass(System.IntPtr)
extern void AndroidReflection_GetFieldClass_mE162BDF6E215EF685FEE87558868C0C0A92FF5EB (void);
// 0x000000E8 System.String UnityEngine.AndroidReflection::GetFieldSignature(System.IntPtr)
extern void AndroidReflection_GetFieldSignature_m875FCA6402DFB60FB02EC45DF542525BD4328938 (void);
// 0x000000E9 System.IntPtr UnityEngine.AndroidReflection::NewProxyInstance(System.IntPtr,System.IntPtr,System.IntPtr)
extern void AndroidReflection_NewProxyInstance_m0F4311ECD23AC3EBB415E5A1986F24AACB7F855E (void);
// 0x000000EA System.Void UnityEngine.AndroidReflection::SetNativeExceptionOnProxy(System.IntPtr,System.Exception,System.Boolean)
extern void AndroidReflection_SetNativeExceptionOnProxy_m128E0E4E225DE9CE787306D76741E72D25CB1F8E (void);
// 0x000000EB System.Void UnityEngine.AndroidReflection::.cctor()
extern void AndroidReflection__cctor_mF0B578A28D9AE0EDA442E2DB3E5CC549CC221904 (void);
// 0x000000EC System.IntPtr UnityEngine._AndroidJNIHelper::CreateJavaProxy(System.IntPtr,System.IntPtr,UnityEngine.AndroidJavaProxy)
extern void _AndroidJNIHelper_CreateJavaProxy_mD55122FBE783E484184BB68E2F982CB521064D69 (void);
// 0x000000ED System.IntPtr UnityEngine._AndroidJNIHelper::CreateJavaRunnable(UnityEngine.AndroidJavaRunnable)
extern void _AndroidJNIHelper_CreateJavaRunnable_mC9F0EBABCA5C6FAFF1EBCDC6758F915417EEC5F2 (void);
// 0x000000EE System.IntPtr UnityEngine._AndroidJNIHelper::InvokeJavaProxyMethod(UnityEngine.AndroidJavaProxy,System.IntPtr,System.IntPtr)
extern void _AndroidJNIHelper_InvokeJavaProxyMethod_m61E64CCD15A3694432F887DDE0AFE3ECF1ECC516 (void);
// 0x000000EF UnityEngine.jvalue[] UnityEngine._AndroidJNIHelper::CreateJNIArgArray(System.Object[])
extern void _AndroidJNIHelper_CreateJNIArgArray_m1F064AACE1C251B395968F94C16089775E33EFCA (void);
// 0x000000F0 System.Object UnityEngine._AndroidJNIHelper::UnboxArray(UnityEngine.AndroidJavaObject)
extern void _AndroidJNIHelper_UnboxArray_mEBA7A10DBA37D9BA5308CD68E602DE7B17A1CBC9 (void);
// 0x000000F1 System.Object UnityEngine._AndroidJNIHelper::Unbox(UnityEngine.AndroidJavaObject)
extern void _AndroidJNIHelper_Unbox_m9E2B0719D492499720FAC6CA08FF261BB1563FF5 (void);
// 0x000000F2 UnityEngine.AndroidJavaObject UnityEngine._AndroidJNIHelper::Box(System.Object)
extern void _AndroidJNIHelper_Box_mCA5D1BD48FAAE9BD098E2CC61CA86857D7589F40 (void);
// 0x000000F3 System.Void UnityEngine._AndroidJNIHelper::DeleteJNIArgArray(System.Object[],UnityEngine.jvalue[])
extern void _AndroidJNIHelper_DeleteJNIArgArray_m376FFEE6187587454E7E20331B0473DFB1B1351E (void);
// 0x000000F4 System.IntPtr UnityEngine._AndroidJNIHelper::ConvertToJNIArray(System.Array)
extern void _AndroidJNIHelper_ConvertToJNIArray_m25F591EAFE69F17716AFBE9F079F2E259E051ACD (void);
// 0x000000F5 ArrayType UnityEngine._AndroidJNIHelper::ConvertFromJNIArray(System.IntPtr)
// 0x000000F6 System.IntPtr UnityEngine._AndroidJNIHelper::GetConstructorID(System.IntPtr,System.Object[])
extern void _AndroidJNIHelper_GetConstructorID_m2370ACC5A85EBDDBCE5F0C17753887CDCB878F43 (void);
// 0x000000F7 System.IntPtr UnityEngine._AndroidJNIHelper::GetMethodID(System.IntPtr,System.String,System.Object[],System.Boolean)
extern void _AndroidJNIHelper_GetMethodID_m922CE6031D2DDCF47B5C90931A46EE5C1F23F82A (void);
// 0x000000F8 System.IntPtr UnityEngine._AndroidJNIHelper::GetMethodID(System.IntPtr,System.String,System.Object[],System.Boolean)
// 0x000000F9 System.IntPtr UnityEngine._AndroidJNIHelper::GetFieldID(System.IntPtr,System.String,System.Boolean)
// 0x000000FA System.IntPtr UnityEngine._AndroidJNIHelper::GetConstructorID(System.IntPtr,System.String)
extern void _AndroidJNIHelper_GetConstructorID_m785D81C52A5063D9B4F308462927BCA1336DCE97 (void);
// 0x000000FB System.IntPtr UnityEngine._AndroidJNIHelper::GetMethodID(System.IntPtr,System.String,System.String,System.Boolean)
extern void _AndroidJNIHelper_GetMethodID_mE97AC51E9CADFCEE717720FF1FC73779B03152AF (void);
// 0x000000FC System.IntPtr UnityEngine._AndroidJNIHelper::GetMethodIDFallback(System.IntPtr,System.String,System.String,System.Boolean)
extern void _AndroidJNIHelper_GetMethodIDFallback_mA7CE78D8661C476F05B8FE0C4CC9B14F94E7702A (void);
// 0x000000FD System.IntPtr UnityEngine._AndroidJNIHelper::GetFieldID(System.IntPtr,System.String,System.String,System.Boolean)
extern void _AndroidJNIHelper_GetFieldID_m114CD556A40B3AE3BFC1183055B4CD71ECB70BEA (void);
// 0x000000FE System.String UnityEngine._AndroidJNIHelper::GetSignature(System.Object)
extern void _AndroidJNIHelper_GetSignature_m3A803B2695DB1EB9344C5596B97E8302A71BFD73 (void);
// 0x000000FF System.String UnityEngine._AndroidJNIHelper::GetSignature(System.Object[])
extern void _AndroidJNIHelper_GetSignature_m639489618B4D11AD75002B211C607CA794A01C7B (void);
// 0x00000100 System.String UnityEngine._AndroidJNIHelper::GetSignature(System.Object[])
// 0x00000101 UnityEngine.AndroidJavaObject UnityEngine.Android.Permission::GetActivity()
extern void Permission_GetActivity_m4C2DB9AC19D53AD609D4BAF65E10D5248DE928B2 (void);
static Il2CppMethodPointer s_methodPointers[257] = 
{
	AndroidJNIHelper_GetConstructorID_m06EE0475AA9AA8D8656E1F5835FFCE2C0EFB92BC,
	AndroidJNIHelper_GetMethodID_m25A23BE9C141B9575BB8A332CD7B6F7F394202E3,
	AndroidJNIHelper_GetFieldID_m9ABAB24D635611ADEDF376E9B4F7E3E0D271D38E,
	AndroidJNIHelper_CreateJavaRunnable_m1B89587372069109C43FAD8C4C96DA816DA04A03,
	AndroidJNIHelper_CreateJavaProxy_m9E58A55A573E055FA242C5C6251C715522FC91DB,
	AndroidJNIHelper_CreateJNIArgArray_mB806BBD1920C763CCDB0BA6DE336425A45D4C361,
	AndroidJNIHelper_DeleteJNIArgArray_m672B3991A3FF891888AFFF4D6F22767F17C05EA5,
	AndroidJNIHelper_GetConstructorID_m075C4952D43C603134C0D4CD6FDF98F3041956A6,
	AndroidJNIHelper_GetMethodID_m8F55F58B1EC4C8011E42FD6C800176D3D3AE0EE6,
	NULL,
	NULL,
	NULL,
	AndroidJNI_FindClass_m35CA47A458527E778188E0DAB30E5C7123568C6A,
	AndroidJNI_FromReflectedMethod_m5CD5D729593B5F0847D47CBDBE9A8945302C1CE3,
	AndroidJNI_ExceptionOccurred_mFC131075AAAFFD4A8D511363B749E42B172B42C0,
	AndroidJNI_ExceptionClear_m6FA4D64946FB39496A416E7CE6868E337A8C29FB,
	AndroidJNI_PushLocalFrame_mBE32A038C6E4E5FE5C0661981EDCC57D79D5B20D,
	AndroidJNI_PopLocalFrame_m0BE71C6B792A2E3F744FEBCC799FD5511F74CD6E,
	AndroidJNI_NewGlobalRef_m0CBCEAE2F24C059B441287FD8878E69C97A4609C,
	AndroidJNI_DeleteGlobalRef_m5D1E60290B99019EB4CFF1FF2BB965E5B690F5DD,
	AndroidJNI_NewWeakGlobalRef_m984CDA34234D997E64ACCBCC71D53A97454EA162,
	AndroidJNI_DeleteWeakGlobalRef_m49B0AC00D8B95435A95013BCE656E3F04A72598E,
	AndroidJNI_NewLocalRef_m87E92FA6F371946D739C72EE34A021C9E5779DF6,
	AndroidJNI_DeleteLocalRef_m0640E773BAD2A500DB3A70F13072B5155B855D91,
	AndroidJNI_NewObject_m02A159629121FF8B03A612F2B7BB412E95BEED9C,
	AndroidJNI_GetObjectClass_mDF3386F6A5A606B1772741A6706B69861CCBFDFE,
	AndroidJNI_GetMethodID_mFF49BAEF9F697F41B4325F9C6B2E7443D91AA0B3,
	AndroidJNI_GetFieldID_m7E66B43BBF9FFF6578039F549C33DC531C17ADAE,
	AndroidJNI_GetStaticMethodID_m501CE9457D8834DB97B45AB1EF3FBCF5C0052B36,
	AndroidJNI_GetStaticFieldID_m2A9971AE72A90833F8610F069D7E57D44D78F2AB,
	AndroidJNI_NewString_m67DD4C3BF62BE2EF854288AE991E9C49CA97C8E8,
	AndroidJNI_NewStringFromStr_mF0F3F777C3349DFFBCA76B342D1CC885C13BC52C,
	AndroidJNI_GetStringChars_mA34525423F3506039FFF8F4E6F3F0F263A5FE723,
	AndroidJNI_CallStringMethod_mE09892DC06071212833B3C708EC47EE5EC059C80,
	AndroidJNI_CallObjectMethod_m3EBB2273B0EA84B457CBCF76A5E9F090EEA093A8,
	AndroidJNI_CallIntMethod_m127D249914A292870FAB52D12B28144FC8E7275E,
	AndroidJNI_CallBooleanMethod_m0CDF725FF2C96E5B72EA5F2343A2B8FD8BDA20A7,
	AndroidJNI_CallShortMethod_m663510B7F4E94A030DE4A05E70682A6EFA90F13C,
	AndroidJNI_CallSByteMethod_mE213EDEB1EEF789DE37BEA74A05D046E7807E20E,
	AndroidJNI_CallCharMethod_m17E6E525A5D1B048FDAE20B99C61A43BA32FC8E2,
	AndroidJNI_CallFloatMethod_mD2438B99FF13BFFE6D834FC7C1C3D925E47CA317,
	AndroidJNI_CallDoubleMethod_m810BC723EBEFFCFF698965D9A588064CFC40232C,
	AndroidJNI_CallLongMethod_m9232E3B51D89ABAA7D9BC58B42B444156102EABC,
	AndroidJNI_CallVoidMethod_m0BF2683A2CD5BA81C057DFC07F417115310B0283,
	AndroidJNI_GetStringField_m15CDC58189B81C49C19276E00BE3F80656342DDF,
	AndroidJNI_GetObjectField_m2ECDAF74FFF7EFC858205F87C7264C25C976ED28,
	AndroidJNI_GetBooleanField_mC6F5A0CC5D55A970EB7AAC480395BC8FD6CB17C8,
	AndroidJNI_GetSByteField_m0B33376868BE72599A879612BD4968A3451AB3F7,
	AndroidJNI_GetCharField_mA62DC950E6EE8C88F8126389843A0F9408F33554,
	AndroidJNI_GetShortField_mC3CD99C2CBFA7F0B11C8527D64FE023344F59E2C,
	AndroidJNI_GetIntField_m5404C0E1CF43B015DE5BA6B27E8BA3EFEA596098,
	AndroidJNI_GetLongField_m96A3820B70125505E5A9134EB79E16687A992A59,
	AndroidJNI_GetFloatField_m3B29211A1F4FBD7DBFD3D6F39A2CAF30F02947F5,
	AndroidJNI_GetDoubleField_mA9724BDC49822C2C8F1E733B3333FB27207FB122,
	AndroidJNI_CallStaticStringMethod_m864F9AB1371B0FDF09703634526B10347F2C2F15,
	AndroidJNI_CallStaticObjectMethod_m0601132D53321BDD71EC8825442D425D6A62AD85,
	AndroidJNI_CallStaticIntMethod_m55AFC39C5E59C66C4773524231A41002A9A301C1,
	AndroidJNI_CallStaticBooleanMethod_mF0CF8B276B05D318DD21001D33C717D623CF0AE7,
	AndroidJNI_CallStaticShortMethod_mBBA7160FA2070E2062D8A2CFDD1C5B77643B15C8,
	AndroidJNI_CallStaticSByteMethod_m72D27748B5B1FD9446F2820039D17F5BFF6E94A4,
	AndroidJNI_CallStaticCharMethod_m9C08344C6A639451AABE718F0ED27D24C7EDD6A5,
	AndroidJNI_CallStaticFloatMethod_m01779727D9A97191F0A4C54B5241E8D7D81CEEF3,
	AndroidJNI_CallStaticDoubleMethod_m120E0E4E343D41C41FB8E0B37272E6D4AB284ECA,
	AndroidJNI_CallStaticLongMethod_m432BF984FAD4465BD4A4B4ED18430063EEA6AFEB,
	AndroidJNI_CallStaticVoidMethod_mB21A9A17469B148C29C36852C91C835D621B8B31,
	AndroidJNI_GetStaticStringField_m42CE78EBEFF78787FBD077F75B30616ACF924312,
	AndroidJNI_GetStaticObjectField_m94337EA629C5FE55CFB37F8F02A609434FEB5D89,
	AndroidJNI_GetStaticBooleanField_m909A15A903875C5D6B3BFA29A67570F96A701C0D,
	AndroidJNI_GetStaticSByteField_m591172C28B493955970211BF6AD6F8C7B37AA035,
	AndroidJNI_GetStaticCharField_m5832AF70C4800FFF72B41A16E388A2CB720202B8,
	AndroidJNI_GetStaticShortField_m407C3A7C748C7E740CF0E5672D839F6ACF14DC05,
	AndroidJNI_GetStaticIntField_mC9C4BFE272E82E8DC6628BA07AA0ADDEB5433217,
	AndroidJNI_GetStaticLongField_m9C1B0514A77EB5D18C5251B8B9F4A3156BD3F28A,
	AndroidJNI_GetStaticFloatField_m475E8F19BCF56B33B4CF70FF00E44C6E2592E5B1,
	AndroidJNI_GetStaticDoubleField_mED2640B91B2C70640ED82CE175D8B3DF8CE581F7,
	AndroidJNI_ToBooleanArray_m0F6247E9CD971CFC3195DF56228139AD6944C454,
	AndroidJNI_ToByteArray_m1D13415CE31007C396246CFD721D7CC0B6BE3D89,
	AndroidJNI_ToSByteArray_m3733221CDDCBE6F6E16CFD52194998F49F5739B2,
	AndroidJNI_ToCharArray_m7A762D085ECCE53C849F82DE1F7D31D0FC0ECCE6,
	AndroidJNI_ToShortArray_mE291B1FC62FC5C4C97A1BEDBF79CFE087873E44B,
	AndroidJNI_ToIntArray_m746B3A15E782ED454B6EB8574C887FE7E5B0E1C3,
	AndroidJNI_ToLongArray_m6C231C8FE5E31C148C644FD4EEEEE7A824EFA5E5,
	AndroidJNI_ToFloatArray_mB0C6CEEFAD5F590F6E7DD8D6C039F724012B6DA9,
	AndroidJNI_ToDoubleArray_m222C645E8DDA4E4ED85528396DB67012B0BC25F8,
	AndroidJNI_ToObjectArray_mABBA3396E1FAB07B18841109375EA9FE6EC7D638,
	AndroidJNI_FromBooleanArray_m720A735A65351612CAE3B2A945CDB0D5F28410D0,
	AndroidJNI_FromByteArray_mF9E0A8B97ADE25C3DDC59AB6CECF597AB346AAB2,
	AndroidJNI_FromSByteArray_mA20709B6C87AF63CBD8DDD4D2DDD57E299A52B3E,
	AndroidJNI_FromCharArray_m856090D7B9D19A40FFE562BA46F4DAF43D43D883,
	AndroidJNI_FromShortArray_m2730004110D2EEB54E1476957F268792DE68F2F6,
	AndroidJNI_FromIntArray_m774925C30510F5504BEA17EA9B863E97955D6EAA,
	AndroidJNI_FromLongArray_m7112B7F6C234E7CD36DEC86064790C59B8DE0C6F,
	AndroidJNI_FromFloatArray_mCD844F6AC98D81C0089CE1332D5E660EAC8A45F5,
	AndroidJNI_FromDoubleArray_m4214A1F5FE02F602A573257C7D8933F0AC851712,
	AndroidJNI_GetArrayLength_m4FF062F3EA299E730221E1F704C0EF5754289628,
	AndroidJNI_NewObjectArray_m0DB0214451F4A4D19F04652627B2F47026E63BCA,
	AndroidJNI_GetObjectArrayElement_m9D4E78BE1E314141634A26ACB948719708054727,
	AndroidJNI_SetObjectArrayElement_mDD16A48E1DA055EE8ACD4065552B2D5EA9CAA0E0,
	AndroidJNISafe_CheckException_mB129CD38BBE25FCA12F4566ED2DDC0216D7201D9,
	AndroidJNISafe_DeleteGlobalRef_m3F1A798E9B36CFADA2B4D9642AB518D97FB9A975,
	AndroidJNISafe_DeleteWeakGlobalRef_m680EB63DEF7D2B378CD76EC14270F436D7E9CCA6,
	AndroidJNISafe_DeleteLocalRef_mA01249A781F9364DFEA78DE907B9CDA1A174E8AE,
	AndroidJNISafe_NewString_mB3309A8C075C006A7C1E0EC91C69374EA6AE5356,
	AndroidJNISafe_GetStringChars_m9BF01A532EDDB97D55A552AD493FCA0F556669E8,
	AndroidJNISafe_GetObjectClass_m51FDFF60A2A56B970BE41A28D7CBF53D9BA6FC24,
	AndroidJNISafe_GetStaticMethodID_m9A12CADA3B27CFC904F060CA20D52EF5ED789CCE,
	AndroidJNISafe_GetMethodID_m8F93B005622C963DBB76F72E9A308D2B63EF1CAC,
	AndroidJNISafe_GetFieldID_m9236F3315104F566CD96C3FC88804357D04F3D22,
	AndroidJNISafe_GetStaticFieldID_m1069199092B17A90933B51436F4F1EEE07FADF5E,
	AndroidJNISafe_FromReflectedMethod_mA8CBBABDB2EBF0E44052B4E622AB175F79DFF02A,
	AndroidJNISafe_FindClass_m6A098856373606201AE9A3FB503865D788AE6906,
	AndroidJNISafe_NewObject_mA904997739C2D552EB2E63ED33D2555BB399A3FB,
	AndroidJNISafe_GetStaticObjectField_m1E160752B81117A3011BA08491C76D605C928A3A,
	AndroidJNISafe_GetStaticStringField_m797C4BF9E360C59AEA19CEB7D968B4F890AFCB92,
	AndroidJNISafe_GetStaticCharField_m4FF6966837A76028C6DEF9F9627596BF15AF853D,
	AndroidJNISafe_GetStaticDoubleField_mD3FC8D3BF793D3EEF5E09C323EFD1AA9940713BE,
	AndroidJNISafe_GetStaticFloatField_m9A0B6D8422348C435BF0B162BBA3BF2398C367E7,
	AndroidJNISafe_GetStaticLongField_mE21F6072A6C667F03099C76251D3AD55F7290B3C,
	AndroidJNISafe_GetStaticShortField_m27844C1495DC221421632E69F435BE06FA358AE8,
	AndroidJNISafe_GetStaticSByteField_mEFF4FF349D74554A04B46AAD9BAD2FC38D127FA8,
	AndroidJNISafe_GetStaticBooleanField_m4BE0DB50C689A33BD2155CA3E374F8DCDA1D3175,
	AndroidJNISafe_GetStaticIntField_m72F81AA991CA4BD8FAD87B3EE2A6531A1B049424,
	AndroidJNISafe_CallStaticVoidMethod_m82D3FEEC204FB4AC380A38D21F3CBA1C39C96641,
	AndroidJNISafe_CallStaticObjectMethod_m4D0D7694AF57731B895D3EE6624EEE24B90264B2,
	AndroidJNISafe_CallStaticStringMethod_mAA0A83FC01520241CE71A63608D0930FD2CCC49B,
	AndroidJNISafe_CallStaticCharMethod_mA7DABFA2640148CE4D78623C74BD07ADBB798E09,
	AndroidJNISafe_CallStaticDoubleMethod_m91329AD3D9164B4805FF3129D226F5E176F544F9,
	AndroidJNISafe_CallStaticFloatMethod_mD2EE2202A22B2DE17B216401EC9806CBDF6A5023,
	AndroidJNISafe_CallStaticLongMethod_mBEF3828B588E18CE4366429338D06A1D2C7D70E5,
	AndroidJNISafe_CallStaticShortMethod_m9ECDEE47679B84EF709795E9FCA95BA0153E1D72,
	AndroidJNISafe_CallStaticSByteMethod_mF76AA217926E57302DB89470B5C089FFE0D667E6,
	AndroidJNISafe_CallStaticBooleanMethod_m6A4705BF643A65F96244E6CF6FE72AB583049A9E,
	AndroidJNISafe_CallStaticIntMethod_mE3503898F47B13B069695EB68CA11C93D7B112EB,
	AndroidJNISafe_GetObjectField_m91545B311AC4DAA8F38A937F0679277917FA7892,
	AndroidJNISafe_GetStringField_mBC1716407F932E89B1110ECE547770480EEE07CA,
	AndroidJNISafe_GetCharField_m007EC43BEB159100767F6485C768824DC9EF90B2,
	AndroidJNISafe_GetDoubleField_mEDE5E15ACDDA40E0F673D88BE62784F5A0A06957,
	AndroidJNISafe_GetFloatField_m9637EF8B8E964B7F7CF9C23871EDE411A3362746,
	AndroidJNISafe_GetLongField_mCBB0B800D627FF2A3EBDFB0117B6A5AB1890BC70,
	AndroidJNISafe_GetShortField_mF275D46B8DA641949E39B250C338F147F8BFB701,
	AndroidJNISafe_GetSByteField_mB5DD4AA1B8986BD295A8EA4E638E456BCCFAE566,
	AndroidJNISafe_GetBooleanField_m32AF9A0F0538463F3475B3DF86CAA20482C97EFA,
	AndroidJNISafe_GetIntField_mC6BE70ED933532599667024D89F5AE1029DDA1E0,
	AndroidJNISafe_CallVoidMethod_mC2A5489151E3D56467FEA9762684A1FBCC201C76,
	AndroidJNISafe_CallObjectMethod_m223C63966A73E23145180079470BD49C56D2F9BA,
	AndroidJNISafe_CallStringMethod_mB268997CA18EF3343019308959A619C4C3820891,
	AndroidJNISafe_CallCharMethod_mB306445351D496DBAC531F3DAB699A30024AEA07,
	AndroidJNISafe_CallDoubleMethod_mB5942D848CF372EADF39CCF3CF5B5F4385C927C0,
	AndroidJNISafe_CallFloatMethod_mB14DBDEB900A35991155EE7C1739C1DEB112B28F,
	AndroidJNISafe_CallLongMethod_m41D5D8467D274916EC33C33AC7540F88F8F46172,
	AndroidJNISafe_CallShortMethod_m5FA668201182B432D880AF3F035C1DD542FC6D6B,
	AndroidJNISafe_CallSByteMethod_m04C4E22429DCA9E3271A905387CBA406DD170F16,
	AndroidJNISafe_CallBooleanMethod_m3253095C758D3CE4C3C1FCB2E55B956BB6D832A4,
	AndroidJNISafe_CallIntMethod_m2B2E4495CF5E2135E8F42D3E90F7C0902AD78771,
	AndroidJNISafe_FromCharArray_m89C34EFEB01E5CD0BE1BC887DCDAFEA08D109CC5,
	AndroidJNISafe_FromDoubleArray_mE04CDB2DB5C370D911932A6E39129373BCA61F78,
	AndroidJNISafe_FromFloatArray_m6AAD54CA85C4DDBCE6B97452C70C9499D87C1870,
	AndroidJNISafe_FromLongArray_mF288E2462595D10E09175957923103B5AA77AE81,
	AndroidJNISafe_FromShortArray_m967E0145E1CCD91B7A71A8CA1EC571887B637808,
	AndroidJNISafe_FromByteArray_mD8574154381ED1F115082B1C729FDFB6B38AB452,
	AndroidJNISafe_FromSByteArray_m09370C783EB0D689FF1BBF662BD14EC6E83967B2,
	AndroidJNISafe_FromBooleanArray_m59EBFEDF2F02FBBC0E6F5C438DDC1046D792283F,
	AndroidJNISafe_FromIntArray_m637AA08C7F6FFC5CCE3A548AFC9A5F649139E7FA,
	AndroidJNISafe_ToObjectArray_mC6B3036691E5731978E2CF792759AA10D7BBC337,
	AndroidJNISafe_ToCharArray_mCA6E04C48615372BEF89C7FDF2CE7FDD1E001BF4,
	AndroidJNISafe_ToDoubleArray_mC3E4F76BE9EA0FBF0F9C31D99DAC5A56C30BD58E,
	AndroidJNISafe_ToFloatArray_m19DFDC1CBE592872AEB7B50021456C265687E07E,
	AndroidJNISafe_ToLongArray_mE0E955A472A920B2F189B49DDADE22F00E2C8CB8,
	AndroidJNISafe_ToShortArray_mFA01F104310D594C3884A8BD697A963568D07B91,
	AndroidJNISafe_ToByteArray_m7843B87143DAC4FC16F3C0B5D44BA44158D7DBCF,
	AndroidJNISafe_ToSByteArray_mF90F6599E51AA285738E2013753DEA1917C3B224,
	AndroidJNISafe_ToBooleanArray_m423EA13D4D1D1B253DD04ED7EE2CAA8E916C5DD5,
	AndroidJNISafe_ToIntArray_m6942A9FD3416839F741B033C85E1ABFDF1549BE4,
	AndroidJNISafe_GetObjectArrayElement_m58038654EA99F36B2B6E3B992DA845FF340D4B46,
	AndroidJNISafe_GetArrayLength_m606B22284E0BB24E92FB7A3A8B5A878798281AA8,
	AndroidJavaRunnable__ctor_m385F79E7C422595C3F8504D588807799A7CF4727,
	AndroidJavaRunnable_Invoke_m014FD2895FDB86ADBD97D9CEC201592673D9593B,
	AndroidJavaRunnable_BeginInvoke_mC5CE46E7743D650AFC705153AEACED181B1FC15C,
	AndroidJavaRunnable_EndInvoke_m5ED563F2A458EB2F5A614815B85EF79ACB60BFAF,
	AndroidJavaException__ctor_mD451AB9B54A333C79F0F0650DD4DC2B173CAA910,
	AndroidJavaException_get_StackTrace_m3A87C920F33472028A05370C7C320C0196B209E9,
	GlobalJavaObjectRef__ctor_mC4872ABD6EDA3531D1012C2FB7B73EE2B1652C5D,
	GlobalJavaObjectRef_Finalize_m455E5786F72D4A0DBBA209EE67AE763130FBCE3A,
	GlobalJavaObjectRef_op_Implicit_m8573DFC0939E91FAA4E32C87D7652907626D3746,
	GlobalJavaObjectRef_Dispose_mDAF2DE01EBD608E63CB661954D0B50015D08FF28,
	AndroidJavaRunnableProxy__ctor_mDF1B01EBE4C312B67A43B452B440303855973A89,
	AndroidJavaProxy__ctor_m1E8F4C0D87B74B81C64A54A584B2FF4ECE571238,
	AndroidJavaProxy__ctor_m17BDD42A24CEBD07722B68A25CAD6DEAF64241E1,
	AndroidJavaProxy_Finalize_m7F5D1C533F2D1A4A1A286D00E14282E48A55F96F,
	AndroidJavaProxy_Invoke_m0908B96EF03FC0465EAC584963213D227A3048D0,
	AndroidJavaProxy_Invoke_m7DD1AD609B664D23BB571F86B0EFCA65DBEF0561,
	AndroidJavaProxy_GetProxyObject_mEDC1D96AAE0A4FC2B4E57E1FC3AE5C47C27FDC6A,
	AndroidJavaProxy_GetRawProxy_m125686B87104A00FD20DEB87F24074A3AD68D354,
	AndroidJavaProxy__cctor_m495EBB573F2215B69BE68C609BAFAC202401C488,
	AndroidJavaObject__ctor_m6146DBD19BCFFDB3D4F42C8D38491F354B58B001,
	AndroidJavaObject_Dispose_mFF9FA58B47A7FC0C083E034B364EAAE2E7282C45,
	AndroidJavaObject_Call_mBB226DA52CE5A2FCD9A2D42BC7FB4272E094B76D,
	NULL,
	NULL,
	AndroidJavaObject_GetRawObject_m09E30F9AF9DD2AE07D34CE3915BDA557E206271D,
	AndroidJavaObject_GetRawClass_m051337FD2D7C0E765641B192D86FF4A06257836B,
	NULL,
	NULL,
	AndroidJavaObject_DebugPrint_m4F2A8B6447F29A548C6AE6F74CBD3AF51B587F7D,
	AndroidJavaObject__AndroidJavaObject_m54584F0F147ECA1A0B7DEB875742B3331CC1666A,
	AndroidJavaObject__ctor_m880F6533139DF0BD36C6EF428E45E9F44B6534A3,
	AndroidJavaObject__ctor_m76D62BEE18E830EBCDE4B6860AC3C8A5979C9F18,
	AndroidJavaObject_Finalize_mE74A8B42EE112C9889752419D2AA209CD08704A2,
	AndroidJavaObject_Dispose_m881D1EC65C5FE625E9F84D81308E966CAC607AFC,
	AndroidJavaObject__Call_m5F23B0D456587BD5B01C41EB4C472AF59FF40946,
	NULL,
	NULL,
	NULL,
	NULL,
	AndroidJavaObject_AndroidJavaObjectDeleteLocalRef_m894328C5BFEE7BE03298425049B09815D0CAB572,
	AndroidJavaObject_AndroidJavaClassDeleteLocalRef_mB3FBC86ACFCE28F09F85D5D8777806294C5A2965,
	NULL,
	AndroidJavaObject__GetRawObject_mD52629909E16A092896CD884BB710F876BE14975,
	AndroidJavaObject__GetRawClass_mCEFEDEA158099706B9B2F1CB40C843D2E899932C,
	AndroidJavaObject__cctor_m920EF92A44A20E2EA89BCF989E7855C2A0AF65F5,
	AndroidJavaClass__ctor_mEFF9F51871F231955D97DABDE9AB4A6B4EDA5541,
	AndroidJavaClass__AndroidJavaClass_mD69A3836B7DD13F6514499FFE167BCB136A5BBAB,
	AndroidJavaClass__ctor_m61AE95F9D20CA648AEFC18AE922FE70F0B90786F,
	AndroidReflection_IsPrimitive_m6AC935960F806BD35938B70EB70E9FF90F03521E,
	AndroidReflection_IsAssignableFrom_mD2754AFA331CB318E960432DB3F4270B860A9284,
	AndroidReflection_GetStaticMethodID_m4C6530446E01E0CCD82437DC5E0301EA265699F3,
	AndroidReflection_GetMethodID_m551139A025A4DDC58D0F076FD26E77879F50BA05,
	AndroidReflection_GetConstructorMember_mB320ED3A0990352093EAA566BAB4A57054FCF973,
	AndroidReflection_GetMethodMember_mEB2499209A0103545F59AF367168355BE863D93D,
	AndroidReflection_GetFieldMember_mDF940ADAC9599730E7673B122259222B70937F52,
	AndroidReflection_GetFieldClass_mE162BDF6E215EF685FEE87558868C0C0A92FF5EB,
	AndroidReflection_GetFieldSignature_m875FCA6402DFB60FB02EC45DF542525BD4328938,
	AndroidReflection_NewProxyInstance_m0F4311ECD23AC3EBB415E5A1986F24AACB7F855E,
	AndroidReflection_SetNativeExceptionOnProxy_m128E0E4E225DE9CE787306D76741E72D25CB1F8E,
	AndroidReflection__cctor_mF0B578A28D9AE0EDA442E2DB3E5CC549CC221904,
	_AndroidJNIHelper_CreateJavaProxy_mD55122FBE783E484184BB68E2F982CB521064D69,
	_AndroidJNIHelper_CreateJavaRunnable_mC9F0EBABCA5C6FAFF1EBCDC6758F915417EEC5F2,
	_AndroidJNIHelper_InvokeJavaProxyMethod_m61E64CCD15A3694432F887DDE0AFE3ECF1ECC516,
	_AndroidJNIHelper_CreateJNIArgArray_m1F064AACE1C251B395968F94C16089775E33EFCA,
	_AndroidJNIHelper_UnboxArray_mEBA7A10DBA37D9BA5308CD68E602DE7B17A1CBC9,
	_AndroidJNIHelper_Unbox_m9E2B0719D492499720FAC6CA08FF261BB1563FF5,
	_AndroidJNIHelper_Box_mCA5D1BD48FAAE9BD098E2CC61CA86857D7589F40,
	_AndroidJNIHelper_DeleteJNIArgArray_m376FFEE6187587454E7E20331B0473DFB1B1351E,
	_AndroidJNIHelper_ConvertToJNIArray_m25F591EAFE69F17716AFBE9F079F2E259E051ACD,
	NULL,
	_AndroidJNIHelper_GetConstructorID_m2370ACC5A85EBDDBCE5F0C17753887CDCB878F43,
	_AndroidJNIHelper_GetMethodID_m922CE6031D2DDCF47B5C90931A46EE5C1F23F82A,
	NULL,
	NULL,
	_AndroidJNIHelper_GetConstructorID_m785D81C52A5063D9B4F308462927BCA1336DCE97,
	_AndroidJNIHelper_GetMethodID_mE97AC51E9CADFCEE717720FF1FC73779B03152AF,
	_AndroidJNIHelper_GetMethodIDFallback_mA7CE78D8661C476F05B8FE0C4CC9B14F94E7702A,
	_AndroidJNIHelper_GetFieldID_m114CD556A40B3AE3BFC1183055B4CD71ECB70BEA,
	_AndroidJNIHelper_GetSignature_m3A803B2695DB1EB9344C5596B97E8302A71BFD73,
	_AndroidJNIHelper_GetSignature_m639489618B4D11AD75002B211C607CA794A01C7B,
	NULL,
	Permission_GetActivity_m4C2DB9AC19D53AD609D4BAF65E10D5248DE928B2,
};
static const int32_t s_InvokerIndices[257] = 
{
	5549,
	4787,
	4787,
	6032,
	6032,
	6091,
	5834,
	5549,
	4787,
	-1,
	-1,
	-1,
	6032,
	6031,
	6265,
	6289,
	6000,
	6031,
	6031,
	6215,
	6031,
	6215,
	6031,
	6215,
	5131,
	6031,
	5133,
	5133,
	5133,
	5133,
	6032,
	6032,
	6090,
	5157,
	5131,
	5090,
	5027,
	5075,
	5233,
	5248,
	5237,
	5073,
	5119,
	5304,
	5586,
	5548,
	5428,
	5647,
	5682,
	5506,
	5519,
	5542,
	5658,
	5496,
	5157,
	5131,
	5090,
	5027,
	5075,
	5233,
	5248,
	5237,
	5073,
	5119,
	5304,
	5586,
	5548,
	5428,
	5647,
	5682,
	5506,
	5519,
	5542,
	5658,
	5496,
	6032,
	6032,
	6032,
	6032,
	6032,
	6032,
	6032,
	6032,
	6032,
	5552,
	6090,
	6090,
	6090,
	6090,
	6090,
	6090,
	6090,
	6090,
	6090,
	6002,
	5124,
	5547,
	5300,
	6289,
	6215,
	6215,
	6215,
	6032,
	6090,
	6031,
	5133,
	5133,
	5133,
	5133,
	6031,
	6032,
	5131,
	5548,
	5586,
	5682,
	5496,
	5658,
	5542,
	5506,
	5647,
	5428,
	5519,
	5304,
	5131,
	5157,
	5248,
	5073,
	5237,
	5119,
	5075,
	5233,
	5027,
	5090,
	5548,
	5586,
	5682,
	5496,
	5658,
	5542,
	5506,
	5647,
	5428,
	5519,
	5304,
	5131,
	5157,
	5248,
	5073,
	5237,
	5119,
	5075,
	5233,
	5027,
	5090,
	6090,
	6090,
	6090,
	6090,
	6090,
	6090,
	6090,
	6090,
	6090,
	5552,
	6032,
	6032,
	6032,
	6032,
	6032,
	6032,
	6032,
	6032,
	6032,
	5547,
	6002,
	2169,
	3967,
	1717,
	3348,
	2171,
	3916,
	3315,
	3967,
	6032,
	3967,
	3348,
	3348,
	3348,
	3967,
	1717,
	1717,
	3916,
	3895,
	6289,
	2171,
	3967,
	2171,
	-1,
	-1,
	3895,
	3895,
	-1,
	-1,
	3348,
	2171,
	3315,
	3967,
	3967,
	3276,
	2171,
	-1,
	-1,
	-1,
	-1,
	6090,
	6090,
	-1,
	3895,
	3895,
	6289,
	3348,
	3348,
	3315,
	5923,
	5433,
	5135,
	5135,
	5549,
	4787,
	4787,
	6031,
	6090,
	5130,
	5306,
	6289,
	5131,
	6032,
	5134,
	6091,
	6091,
	6091,
	6091,
	5834,
	6032,
	-1,
	5549,
	4787,
	-1,
	-1,
	5549,
	4787,
	4787,
	4787,
	6091,
	6091,
	-1,
	6270,
};
static const Il2CppTokenRangePair s_rgctxIndices[16] = 
{
	{ 0x0600000A, { 0, 1 } },
	{ 0x0600000B, { 1, 1 } },
	{ 0x0600000C, { 2, 1 } },
	{ 0x060000C6, { 3, 1 } },
	{ 0x060000C7, { 4, 1 } },
	{ 0x060000CA, { 5, 1 } },
	{ 0x060000CB, { 6, 1 } },
	{ 0x060000D3, { 7, 4 } },
	{ 0x060000D4, { 11, 4 } },
	{ 0x060000D5, { 15, 4 } },
	{ 0x060000D6, { 19, 4 } },
	{ 0x060000D9, { 23, 2 } },
	{ 0x060000F5, { 25, 2 } },
	{ 0x060000F8, { 27, 1 } },
	{ 0x060000F9, { 28, 1 } },
	{ 0x06000100, { 29, 1 } },
};
static const Il2CppRGCTXDefinition s_rgctxValues[30] = 
{
	{ (Il2CppRGCTXDataType)3, 31261 },
	{ (Il2CppRGCTXDataType)3, 31273 },
	{ (Il2CppRGCTXDataType)3, 31271 },
	{ (Il2CppRGCTXDataType)3, 25565 },
	{ (Il2CppRGCTXDataType)3, 25567 },
	{ (Il2CppRGCTXDataType)3, 25552 },
	{ (Il2CppRGCTXDataType)3, 25562 },
	{ (Il2CppRGCTXDataType)3, 25507 },
	{ (Il2CppRGCTXDataType)1, 11 },
	{ (Il2CppRGCTXDataType)2, 11 },
	{ (Il2CppRGCTXDataType)3, 25537 },
	{ (Il2CppRGCTXDataType)3, 25504 },
	{ (Il2CppRGCTXDataType)1, 6 },
	{ (Il2CppRGCTXDataType)2, 6 },
	{ (Il2CppRGCTXDataType)3, 25535 },
	{ (Il2CppRGCTXDataType)3, 25508 },
	{ (Il2CppRGCTXDataType)1, 12 },
	{ (Il2CppRGCTXDataType)2, 12 },
	{ (Il2CppRGCTXDataType)3, 25538 },
	{ (Il2CppRGCTXDataType)3, 25505 },
	{ (Il2CppRGCTXDataType)1, 7 },
	{ (Il2CppRGCTXDataType)2, 7 },
	{ (Il2CppRGCTXDataType)3, 25536 },
	{ (Il2CppRGCTXDataType)3, 25494 },
	{ (Il2CppRGCTXDataType)2, 10 },
	{ (Il2CppRGCTXDataType)1, 374 },
	{ (Il2CppRGCTXDataType)2, 374 },
	{ (Il2CppRGCTXDataType)3, 31283 },
	{ (Il2CppRGCTXDataType)1, 375 },
	{ (Il2CppRGCTXDataType)1, 377 },
};
extern const CustomAttributesCacheGenerator g_UnityEngine_AndroidJNIModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_AndroidJNIModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_AndroidJNIModule_CodeGenModule = 
{
	"UnityEngine.AndroidJNIModule.dll",
	257,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	16,
	s_rgctxIndices,
	30,
	s_rgctxValues,
	NULL,
	g_UnityEngine_AndroidJNIModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

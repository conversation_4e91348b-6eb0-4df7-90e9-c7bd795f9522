void RegisterAllStrippedInternalCalls()
{
	//Start Registrations for type : Unity.Collections.LowLevel.Unsafe.UnsafeUtility

		//System.Void Unity.Collections.LowLevel.Unsafe.UnsafeUtility::Free(System.Void*,Unity.Collections.Allocator)
		void Register_Unity_Collections_LowLevel_Unsafe_UnsafeUtility_Free();
		Register_Unity_Collections_LowLevel_Unsafe_UnsafeUtility_Free();

	//End Registrations for type : Unity.Collections.LowLevel.Unsafe.UnsafeUtility

	//Start Registrations for type : Unity.Jobs.JobHandle

		//System.Void Unity.Jobs.JobHandle::ScheduleBatchedJobs()
		void Register_Unity_Jobs_JobHandle_ScheduleBatchedJobs();
		Register_Unity_Jobs_JobHandle_ScheduleBatchedJobs();

	//End Registrations for type : Unity.Jobs.JobHandle

	//Start Registrations for type : Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility

		//System.IntPtr Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::CreateMarker(System.String,System.UInt16,Unity.Profiling.LowLevel.MarkerFlags,System.Int32)
		void Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_CreateMarker();
		Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_CreateMarker();

		//System.Void Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::BeginSample(System.IntPtr)
		void Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_BeginSample();
		Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_BeginSample();

		//System.Void Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::EndSample(System.IntPtr)
		void Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_EndSample();
		Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_EndSample();

	//End Registrations for type : Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility

	//Start Registrations for type : UnityEngine.AnchoredJoint2D

		//System.Void UnityEngine.AnchoredJoint2D::set_connectedAnchor_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_AnchoredJoint2D_set_connectedAnchor_Injected();
		Register_UnityEngine_AnchoredJoint2D_set_connectedAnchor_Injected();

	//End Registrations for type : UnityEngine.AnchoredJoint2D

	//Start Registrations for type : UnityEngine.AndroidJNI

		//System.Boolean UnityEngine.AndroidJNI::CallBooleanMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallBooleanMethod();
		Register_UnityEngine_AndroidJNI_CallBooleanMethod();

		//System.Boolean UnityEngine.AndroidJNI::CallStaticBooleanMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallStaticBooleanMethod();
		Register_UnityEngine_AndroidJNI_CallStaticBooleanMethod();

		//System.Boolean UnityEngine.AndroidJNI::GetBooleanField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetBooleanField();
		Register_UnityEngine_AndroidJNI_GetBooleanField();

		//System.Boolean UnityEngine.AndroidJNI::GetStaticBooleanField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStaticBooleanField();
		Register_UnityEngine_AndroidJNI_GetStaticBooleanField();

		//System.Boolean[] UnityEngine.AndroidJNI::FromBooleanArray(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_FromBooleanArray();
		Register_UnityEngine_AndroidJNI_FromBooleanArray();

		//System.Byte[] UnityEngine.AndroidJNI::FromByteArray(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_FromByteArray();
		Register_UnityEngine_AndroidJNI_FromByteArray();

		//System.Char UnityEngine.AndroidJNI::CallCharMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallCharMethod();
		Register_UnityEngine_AndroidJNI_CallCharMethod();

		//System.Char UnityEngine.AndroidJNI::CallStaticCharMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallStaticCharMethod();
		Register_UnityEngine_AndroidJNI_CallStaticCharMethod();

		//System.Char UnityEngine.AndroidJNI::GetCharField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetCharField();
		Register_UnityEngine_AndroidJNI_GetCharField();

		//System.Char UnityEngine.AndroidJNI::GetStaticCharField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStaticCharField();
		Register_UnityEngine_AndroidJNI_GetStaticCharField();

		//System.Char[] UnityEngine.AndroidJNI::FromCharArray(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_FromCharArray();
		Register_UnityEngine_AndroidJNI_FromCharArray();

		//System.Double UnityEngine.AndroidJNI::CallDoubleMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallDoubleMethod();
		Register_UnityEngine_AndroidJNI_CallDoubleMethod();

		//System.Double UnityEngine.AndroidJNI::CallStaticDoubleMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallStaticDoubleMethod();
		Register_UnityEngine_AndroidJNI_CallStaticDoubleMethod();

		//System.Double UnityEngine.AndroidJNI::GetDoubleField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetDoubleField();
		Register_UnityEngine_AndroidJNI_GetDoubleField();

		//System.Double UnityEngine.AndroidJNI::GetStaticDoubleField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStaticDoubleField();
		Register_UnityEngine_AndroidJNI_GetStaticDoubleField();

		//System.Double[] UnityEngine.AndroidJNI::FromDoubleArray(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_FromDoubleArray();
		Register_UnityEngine_AndroidJNI_FromDoubleArray();

		//System.Int16 UnityEngine.AndroidJNI::CallShortMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallShortMethod();
		Register_UnityEngine_AndroidJNI_CallShortMethod();

		//System.Int16 UnityEngine.AndroidJNI::CallStaticShortMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallStaticShortMethod();
		Register_UnityEngine_AndroidJNI_CallStaticShortMethod();

		//System.Int16 UnityEngine.AndroidJNI::GetShortField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetShortField();
		Register_UnityEngine_AndroidJNI_GetShortField();

		//System.Int16 UnityEngine.AndroidJNI::GetStaticShortField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStaticShortField();
		Register_UnityEngine_AndroidJNI_GetStaticShortField();

		//System.Int16[] UnityEngine.AndroidJNI::FromShortArray(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_FromShortArray();
		Register_UnityEngine_AndroidJNI_FromShortArray();

		//System.Int32 UnityEngine.AndroidJNI::CallIntMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallIntMethod();
		Register_UnityEngine_AndroidJNI_CallIntMethod();

		//System.Int32 UnityEngine.AndroidJNI::CallStaticIntMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallStaticIntMethod();
		Register_UnityEngine_AndroidJNI_CallStaticIntMethod();

		//System.Int32 UnityEngine.AndroidJNI::GetArrayLength(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetArrayLength();
		Register_UnityEngine_AndroidJNI_GetArrayLength();

		//System.Int32 UnityEngine.AndroidJNI::GetIntField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetIntField();
		Register_UnityEngine_AndroidJNI_GetIntField();

		//System.Int32 UnityEngine.AndroidJNI::GetStaticIntField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStaticIntField();
		Register_UnityEngine_AndroidJNI_GetStaticIntField();

		//System.Int32 UnityEngine.AndroidJNI::PushLocalFrame(System.Int32)
		void Register_UnityEngine_AndroidJNI_PushLocalFrame();
		Register_UnityEngine_AndroidJNI_PushLocalFrame();

		//System.Int32[] UnityEngine.AndroidJNI::FromIntArray(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_FromIntArray();
		Register_UnityEngine_AndroidJNI_FromIntArray();

		//System.Int64 UnityEngine.AndroidJNI::CallLongMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallLongMethod();
		Register_UnityEngine_AndroidJNI_CallLongMethod();

		//System.Int64 UnityEngine.AndroidJNI::CallStaticLongMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallStaticLongMethod();
		Register_UnityEngine_AndroidJNI_CallStaticLongMethod();

		//System.Int64 UnityEngine.AndroidJNI::GetLongField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetLongField();
		Register_UnityEngine_AndroidJNI_GetLongField();

		//System.Int64 UnityEngine.AndroidJNI::GetStaticLongField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStaticLongField();
		Register_UnityEngine_AndroidJNI_GetStaticLongField();

		//System.Int64[] UnityEngine.AndroidJNI::FromLongArray(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_FromLongArray();
		Register_UnityEngine_AndroidJNI_FromLongArray();

		//System.IntPtr UnityEngine.AndroidJNI::CallObjectMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallObjectMethod();
		Register_UnityEngine_AndroidJNI_CallObjectMethod();

		//System.IntPtr UnityEngine.AndroidJNI::CallStaticObjectMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallStaticObjectMethod();
		Register_UnityEngine_AndroidJNI_CallStaticObjectMethod();

		//System.IntPtr UnityEngine.AndroidJNI::ExceptionOccurred()
		void Register_UnityEngine_AndroidJNI_ExceptionOccurred();
		Register_UnityEngine_AndroidJNI_ExceptionOccurred();

		//System.IntPtr UnityEngine.AndroidJNI::FindClass(System.String)
		void Register_UnityEngine_AndroidJNI_FindClass();
		Register_UnityEngine_AndroidJNI_FindClass();

		//System.IntPtr UnityEngine.AndroidJNI::FromReflectedMethod(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_FromReflectedMethod();
		Register_UnityEngine_AndroidJNI_FromReflectedMethod();

		//System.IntPtr UnityEngine.AndroidJNI::GetFieldID(System.IntPtr,System.String,System.String)
		void Register_UnityEngine_AndroidJNI_GetFieldID();
		Register_UnityEngine_AndroidJNI_GetFieldID();

		//System.IntPtr UnityEngine.AndroidJNI::GetMethodID(System.IntPtr,System.String,System.String)
		void Register_UnityEngine_AndroidJNI_GetMethodID();
		Register_UnityEngine_AndroidJNI_GetMethodID();

		//System.IntPtr UnityEngine.AndroidJNI::GetObjectArrayElement(System.IntPtr,System.Int32)
		void Register_UnityEngine_AndroidJNI_GetObjectArrayElement();
		Register_UnityEngine_AndroidJNI_GetObjectArrayElement();

		//System.IntPtr UnityEngine.AndroidJNI::GetObjectClass(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetObjectClass();
		Register_UnityEngine_AndroidJNI_GetObjectClass();

		//System.IntPtr UnityEngine.AndroidJNI::GetObjectField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetObjectField();
		Register_UnityEngine_AndroidJNI_GetObjectField();

		//System.IntPtr UnityEngine.AndroidJNI::GetStaticFieldID(System.IntPtr,System.String,System.String)
		void Register_UnityEngine_AndroidJNI_GetStaticFieldID();
		Register_UnityEngine_AndroidJNI_GetStaticFieldID();

		//System.IntPtr UnityEngine.AndroidJNI::GetStaticMethodID(System.IntPtr,System.String,System.String)
		void Register_UnityEngine_AndroidJNI_GetStaticMethodID();
		Register_UnityEngine_AndroidJNI_GetStaticMethodID();

		//System.IntPtr UnityEngine.AndroidJNI::GetStaticObjectField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStaticObjectField();
		Register_UnityEngine_AndroidJNI_GetStaticObjectField();

		//System.IntPtr UnityEngine.AndroidJNI::NewGlobalRef(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_NewGlobalRef();
		Register_UnityEngine_AndroidJNI_NewGlobalRef();

		//System.IntPtr UnityEngine.AndroidJNI::NewLocalRef(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_NewLocalRef();
		Register_UnityEngine_AndroidJNI_NewLocalRef();

		//System.IntPtr UnityEngine.AndroidJNI::NewObject(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_NewObject();
		Register_UnityEngine_AndroidJNI_NewObject();

		//System.IntPtr UnityEngine.AndroidJNI::NewObjectArray(System.Int32,System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_NewObjectArray();
		Register_UnityEngine_AndroidJNI_NewObjectArray();

		//System.IntPtr UnityEngine.AndroidJNI::NewStringFromStr(System.String)
		void Register_UnityEngine_AndroidJNI_NewStringFromStr();
		Register_UnityEngine_AndroidJNI_NewStringFromStr();

		//System.IntPtr UnityEngine.AndroidJNI::NewWeakGlobalRef(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_NewWeakGlobalRef();
		Register_UnityEngine_AndroidJNI_NewWeakGlobalRef();

		//System.IntPtr UnityEngine.AndroidJNI::PopLocalFrame(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_PopLocalFrame();
		Register_UnityEngine_AndroidJNI_PopLocalFrame();

		//System.IntPtr UnityEngine.AndroidJNI::ToBooleanArray(System.Boolean[])
		void Register_UnityEngine_AndroidJNI_ToBooleanArray();
		Register_UnityEngine_AndroidJNI_ToBooleanArray();

		//System.IntPtr UnityEngine.AndroidJNI::ToByteArray(System.Byte[])
		void Register_UnityEngine_AndroidJNI_ToByteArray();
		Register_UnityEngine_AndroidJNI_ToByteArray();

		//System.IntPtr UnityEngine.AndroidJNI::ToCharArray(System.Char[])
		void Register_UnityEngine_AndroidJNI_ToCharArray();
		Register_UnityEngine_AndroidJNI_ToCharArray();

		//System.IntPtr UnityEngine.AndroidJNI::ToDoubleArray(System.Double[])
		void Register_UnityEngine_AndroidJNI_ToDoubleArray();
		Register_UnityEngine_AndroidJNI_ToDoubleArray();

		//System.IntPtr UnityEngine.AndroidJNI::ToFloatArray(System.Single[])
		void Register_UnityEngine_AndroidJNI_ToFloatArray();
		Register_UnityEngine_AndroidJNI_ToFloatArray();

		//System.IntPtr UnityEngine.AndroidJNI::ToIntArray(System.Int32[])
		void Register_UnityEngine_AndroidJNI_ToIntArray();
		Register_UnityEngine_AndroidJNI_ToIntArray();

		//System.IntPtr UnityEngine.AndroidJNI::ToLongArray(System.Int64[])
		void Register_UnityEngine_AndroidJNI_ToLongArray();
		Register_UnityEngine_AndroidJNI_ToLongArray();

		//System.IntPtr UnityEngine.AndroidJNI::ToObjectArray(System.IntPtr[],System.IntPtr)
		void Register_UnityEngine_AndroidJNI_ToObjectArray();
		Register_UnityEngine_AndroidJNI_ToObjectArray();

		//System.IntPtr UnityEngine.AndroidJNI::ToSByteArray(System.SByte[])
		void Register_UnityEngine_AndroidJNI_ToSByteArray();
		Register_UnityEngine_AndroidJNI_ToSByteArray();

		//System.IntPtr UnityEngine.AndroidJNI::ToShortArray(System.Int16[])
		void Register_UnityEngine_AndroidJNI_ToShortArray();
		Register_UnityEngine_AndroidJNI_ToShortArray();

		//System.SByte UnityEngine.AndroidJNI::CallSByteMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallSByteMethod();
		Register_UnityEngine_AndroidJNI_CallSByteMethod();

		//System.SByte UnityEngine.AndroidJNI::CallStaticSByteMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallStaticSByteMethod();
		Register_UnityEngine_AndroidJNI_CallStaticSByteMethod();

		//System.SByte UnityEngine.AndroidJNI::GetSByteField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetSByteField();
		Register_UnityEngine_AndroidJNI_GetSByteField();

		//System.SByte UnityEngine.AndroidJNI::GetStaticSByteField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStaticSByteField();
		Register_UnityEngine_AndroidJNI_GetStaticSByteField();

		//System.SByte[] UnityEngine.AndroidJNI::FromSByteArray(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_FromSByteArray();
		Register_UnityEngine_AndroidJNI_FromSByteArray();

		//System.Single UnityEngine.AndroidJNI::CallFloatMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallFloatMethod();
		Register_UnityEngine_AndroidJNI_CallFloatMethod();

		//System.Single UnityEngine.AndroidJNI::CallStaticFloatMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallStaticFloatMethod();
		Register_UnityEngine_AndroidJNI_CallStaticFloatMethod();

		//System.Single UnityEngine.AndroidJNI::GetFloatField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetFloatField();
		Register_UnityEngine_AndroidJNI_GetFloatField();

		//System.Single UnityEngine.AndroidJNI::GetStaticFloatField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStaticFloatField();
		Register_UnityEngine_AndroidJNI_GetStaticFloatField();

		//System.Single[] UnityEngine.AndroidJNI::FromFloatArray(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_FromFloatArray();
		Register_UnityEngine_AndroidJNI_FromFloatArray();

		//System.String UnityEngine.AndroidJNI::CallStaticStringMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallStaticStringMethod();
		Register_UnityEngine_AndroidJNI_CallStaticStringMethod();

		//System.String UnityEngine.AndroidJNI::CallStringMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallStringMethod();
		Register_UnityEngine_AndroidJNI_CallStringMethod();

		//System.String UnityEngine.AndroidJNI::GetStaticStringField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStaticStringField();
		Register_UnityEngine_AndroidJNI_GetStaticStringField();

		//System.String UnityEngine.AndroidJNI::GetStringChars(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStringChars();
		Register_UnityEngine_AndroidJNI_GetStringChars();

		//System.String UnityEngine.AndroidJNI::GetStringField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStringField();
		Register_UnityEngine_AndroidJNI_GetStringField();

		//System.Void UnityEngine.AndroidJNI::CallStaticVoidMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallStaticVoidMethod();
		Register_UnityEngine_AndroidJNI_CallStaticVoidMethod();

		//System.Void UnityEngine.AndroidJNI::CallVoidMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
		void Register_UnityEngine_AndroidJNI_CallVoidMethod();
		Register_UnityEngine_AndroidJNI_CallVoidMethod();

		//System.Void UnityEngine.AndroidJNI::DeleteGlobalRef(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_DeleteGlobalRef();
		Register_UnityEngine_AndroidJNI_DeleteGlobalRef();

		//System.Void UnityEngine.AndroidJNI::DeleteLocalRef(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_DeleteLocalRef();
		Register_UnityEngine_AndroidJNI_DeleteLocalRef();

		//System.Void UnityEngine.AndroidJNI::DeleteWeakGlobalRef(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_DeleteWeakGlobalRef();
		Register_UnityEngine_AndroidJNI_DeleteWeakGlobalRef();

		//System.Void UnityEngine.AndroidJNI::ExceptionClear()
		void Register_UnityEngine_AndroidJNI_ExceptionClear();
		Register_UnityEngine_AndroidJNI_ExceptionClear();

		//System.Void UnityEngine.AndroidJNI::SetObjectArrayElement(System.IntPtr,System.Int32,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_SetObjectArrayElement();
		Register_UnityEngine_AndroidJNI_SetObjectArrayElement();

	//End Registrations for type : UnityEngine.AndroidJNI

	//Start Registrations for type : UnityEngine.Animation

		//System.Boolean UnityEngine.Animation::IsPlaying(System.String)
		void Register_UnityEngine_Animation_IsPlaying();
		Register_UnityEngine_Animation_IsPlaying();

		//System.Boolean UnityEngine.Animation::Play(System.String,UnityEngine.PlayMode)
		void Register_UnityEngine_Animation_Play();
		Register_UnityEngine_Animation_Play();

		//System.Boolean UnityEngine.Animation::PlayDefaultAnimation(UnityEngine.PlayMode)
		void Register_UnityEngine_Animation_PlayDefaultAnimation();
		Register_UnityEngine_Animation_PlayDefaultAnimation();

		//System.Boolean UnityEngine.Animation::get_isPlaying()
		void Register_UnityEngine_Animation_get_isPlaying();
		Register_UnityEngine_Animation_get_isPlaying();

		//System.Int32 UnityEngine.Animation::GetStateCount()
		void Register_UnityEngine_Animation_GetStateCount();
		Register_UnityEngine_Animation_GetStateCount();

		//System.Void UnityEngine.Animation::CrossFade(System.String,System.Single,UnityEngine.PlayMode)
		void Register_UnityEngine_Animation_CrossFade();
		Register_UnityEngine_Animation_CrossFade();

		//System.Void UnityEngine.Animation::Sample()
		void Register_UnityEngine_Animation_Sample();
		Register_UnityEngine_Animation_Sample();

		//System.Void UnityEngine.Animation::Stop()
		void Register_UnityEngine_Animation_Stop();
		Register_UnityEngine_Animation_Stop();

		//UnityEngine.AnimationState UnityEngine.Animation::GetStateAtIndex(System.Int32)
		void Register_UnityEngine_Animation_GetStateAtIndex();
		Register_UnityEngine_Animation_GetStateAtIndex();

	//End Registrations for type : UnityEngine.Animation

	//Start Registrations for type : UnityEngine.AnimationClip

		//System.Single UnityEngine.AnimationClip::get_length()
		void Register_UnityEngine_AnimationClip_get_length();
		Register_UnityEngine_AnimationClip_get_length();

		//System.Void UnityEngine.AnimationClip::Internal_CreateAnimationClip(UnityEngine.AnimationClip)
		void Register_UnityEngine_AnimationClip_Internal_CreateAnimationClip();
		Register_UnityEngine_AnimationClip_Internal_CreateAnimationClip();

	//End Registrations for type : UnityEngine.AnimationClip

	//Start Registrations for type : UnityEngine.AnimationCurve

		//System.Boolean UnityEngine.AnimationCurve::Internal_Equals(System.IntPtr)
		void Register_UnityEngine_AnimationCurve_Internal_Equals();
		Register_UnityEngine_AnimationCurve_Internal_Equals();

		//System.Int32 UnityEngine.AnimationCurve::AddKey(System.Single,System.Single)
		void Register_UnityEngine_AnimationCurve_AddKey();
		Register_UnityEngine_AnimationCurve_AddKey();

		//System.Int32 UnityEngine.AnimationCurve::get_length()
		void Register_UnityEngine_AnimationCurve_get_length();
		Register_UnityEngine_AnimationCurve_get_length();

		//System.IntPtr UnityEngine.AnimationCurve::Internal_Create(UnityEngine.Keyframe[])
		void Register_UnityEngine_AnimationCurve_Internal_Create();
		Register_UnityEngine_AnimationCurve_Internal_Create();

		//System.Single UnityEngine.AnimationCurve::Evaluate(System.Single)
		void Register_UnityEngine_AnimationCurve_Evaluate();
		Register_UnityEngine_AnimationCurve_Evaluate();

		//System.Void UnityEngine.AnimationCurve::GetKey_Injected(System.Int32,UnityEngine.Keyframe&)
		void Register_UnityEngine_AnimationCurve_GetKey_Injected();
		Register_UnityEngine_AnimationCurve_GetKey_Injected();

		//System.Void UnityEngine.AnimationCurve::Internal_Destroy(System.IntPtr)
		void Register_UnityEngine_AnimationCurve_Internal_Destroy();
		Register_UnityEngine_AnimationCurve_Internal_Destroy();

		//System.Void UnityEngine.AnimationCurve::set_postWrapMode(UnityEngine.WrapMode)
		void Register_UnityEngine_AnimationCurve_set_postWrapMode();
		Register_UnityEngine_AnimationCurve_set_postWrapMode();

		//System.Void UnityEngine.AnimationCurve::set_preWrapMode(UnityEngine.WrapMode)
		void Register_UnityEngine_AnimationCurve_set_preWrapMode();
		Register_UnityEngine_AnimationCurve_set_preWrapMode();

		//UnityEngine.Keyframe[] UnityEngine.AnimationCurve::GetKeys()
		void Register_UnityEngine_AnimationCurve_GetKeys();
		Register_UnityEngine_AnimationCurve_GetKeys();

	//End Registrations for type : UnityEngine.AnimationCurve

	//Start Registrations for type : UnityEngine.AnimationState

		//System.Single UnityEngine.AnimationState::get_length()
		void Register_UnityEngine_AnimationState_get_length();
		Register_UnityEngine_AnimationState_get_length();

		//System.Single UnityEngine.AnimationState::get_speed()
		void Register_UnityEngine_AnimationState_get_speed();
		Register_UnityEngine_AnimationState_get_speed();

		//System.Single UnityEngine.AnimationState::get_time()
		void Register_UnityEngine_AnimationState_get_time();
		Register_UnityEngine_AnimationState_get_time();

		//System.String UnityEngine.AnimationState::get_name()
		void Register_UnityEngine_AnimationState_get_name();
		Register_UnityEngine_AnimationState_get_name();

		//System.Void UnityEngine.AnimationState::set_layer(System.Int32)
		void Register_UnityEngine_AnimationState_set_layer();
		Register_UnityEngine_AnimationState_set_layer();

		//System.Void UnityEngine.AnimationState::set_speed(System.Single)
		void Register_UnityEngine_AnimationState_set_speed();
		Register_UnityEngine_AnimationState_set_speed();

		//System.Void UnityEngine.AnimationState::set_time(System.Single)
		void Register_UnityEngine_AnimationState_set_time();
		Register_UnityEngine_AnimationState_set_time();

		//UnityEngine.AnimationClip UnityEngine.AnimationState::get_clip()
		void Register_UnityEngine_AnimationState_get_clip();
		Register_UnityEngine_AnimationState_get_clip();

	//End Registrations for type : UnityEngine.AnimationState

	//Start Registrations for type : UnityEngine.Animator

		//System.Boolean UnityEngine.Animator::get_hasBoundPlayables()
		void Register_UnityEngine_Animator_get_hasBoundPlayables();
		Register_UnityEngine_Animator_get_hasBoundPlayables();

		//System.Int32 UnityEngine.Animator::GetAnimatorClipInfoCount(System.Int32,System.Boolean)
		void Register_UnityEngine_Animator_GetAnimatorClipInfoCount();
		Register_UnityEngine_Animator_GetAnimatorClipInfoCount();

		//System.Int32 UnityEngine.Animator::StringToHash(System.String)
		void Register_UnityEngine_Animator_StringToHash();
		Register_UnityEngine_Animator_StringToHash();

		//System.Int32 UnityEngine.Animator::get_layerCount()
		void Register_UnityEngine_Animator_get_layerCount();
		Register_UnityEngine_Animator_get_layerCount();

		//System.Single UnityEngine.Animator::GetLayerWeight(System.Int32)
		void Register_UnityEngine_Animator_GetLayerWeight();
		Register_UnityEngine_Animator_GetLayerWeight();

		//System.Void UnityEngine.Animator::GetAnimatorClipInfoInternal(System.Int32,System.Boolean,System.Object)
		void Register_UnityEngine_Animator_GetAnimatorClipInfoInternal();
		Register_UnityEngine_Animator_GetAnimatorClipInfoInternal();

		//System.Void UnityEngine.Animator::GetAnimatorStateInfo(System.Int32,UnityEngine.StateInfoIndex,UnityEngine.AnimatorStateInfo&)
		void Register_UnityEngine_Animator_GetAnimatorStateInfo();
		Register_UnityEngine_Animator_GetAnimatorStateInfo();

		//System.Void UnityEngine.Animator::Play(System.Int32,System.Int32,System.Single)
		void Register_UnityEngine_Animator_Play();
		Register_UnityEngine_Animator_Play();

		//System.Void UnityEngine.Animator::ResetTriggerString(System.String)
		void Register_UnityEngine_Animator_ResetTriggerString();
		Register_UnityEngine_Animator_ResetTriggerString();

		//System.Void UnityEngine.Animator::SetBoolString(System.String,System.Boolean)
		void Register_UnityEngine_Animator_SetBoolString();
		Register_UnityEngine_Animator_SetBoolString();

		//System.Void UnityEngine.Animator::SetIntegerString(System.String,System.Int32)
		void Register_UnityEngine_Animator_SetIntegerString();
		Register_UnityEngine_Animator_SetIntegerString();

		//System.Void UnityEngine.Animator::SetTriggerString(System.String)
		void Register_UnityEngine_Animator_SetTriggerString();
		Register_UnityEngine_Animator_SetTriggerString();

		//System.Void UnityEngine.Animator::Update(System.Single)
		void Register_UnityEngine_Animator_Update();
		Register_UnityEngine_Animator_Update();

		//System.Void UnityEngine.Animator::set_runtimeAnimatorController(UnityEngine.RuntimeAnimatorController)
		void Register_UnityEngine_Animator_set_runtimeAnimatorController();
		Register_UnityEngine_Animator_set_runtimeAnimatorController();

		//System.Void UnityEngine.Animator::set_speed(System.Single)
		void Register_UnityEngine_Animator_set_speed();
		Register_UnityEngine_Animator_set_speed();

		//UnityEngine.RuntimeAnimatorController UnityEngine.Animator::get_runtimeAnimatorController()
		void Register_UnityEngine_Animator_get_runtimeAnimatorController();
		Register_UnityEngine_Animator_get_runtimeAnimatorController();

	//End Registrations for type : UnityEngine.Animator

	//Start Registrations for type : UnityEngine.AnimatorClipInfo

		//UnityEngine.AnimationClip UnityEngine.AnimatorClipInfo::InstanceIDToAnimationClipPPtr(System.Int32)
		void Register_UnityEngine_AnimatorClipInfo_InstanceIDToAnimationClipPPtr();
		Register_UnityEngine_AnimatorClipInfo_InstanceIDToAnimationClipPPtr();

	//End Registrations for type : UnityEngine.AnimatorClipInfo

	//Start Registrations for type : UnityEngine.Application

		//System.Boolean UnityEngine.Application::CanStreamedLevelBeLoaded(System.String)
		void Register_UnityEngine_Application_CanStreamedLevelBeLoaded();
		Register_UnityEngine_Application_CanStreamedLevelBeLoaded();

		//System.Boolean UnityEngine.Application::HasProLicense()
		void Register_UnityEngine_Application_HasProLicense();
		Register_UnityEngine_Application_HasProLicense();

		//System.Boolean UnityEngine.Application::HasUserAuthorization(UnityEngine.UserAuthorization)
		void Register_UnityEngine_Application_HasUserAuthorization();
		Register_UnityEngine_Application_HasUserAuthorization();

		//System.Boolean UnityEngine.Application::IsPlaying(UnityEngine.Object)
		void Register_UnityEngine_Application_IsPlaying();
		Register_UnityEngine_Application_IsPlaying();

		//System.Boolean UnityEngine.Application::RequestAdvertisingIdentifierAsync(UnityEngine.Application/AdvertisingIdentifierCallback)
		void Register_UnityEngine_Application_RequestAdvertisingIdentifierAsync();
		Register_UnityEngine_Application_RequestAdvertisingIdentifierAsync();

		//System.Boolean UnityEngine.Application::get_genuine()
		void Register_UnityEngine_Application_get_genuine();
		Register_UnityEngine_Application_get_genuine();

		//System.Boolean UnityEngine.Application::get_genuineCheckAvailable()
		void Register_UnityEngine_Application_get_genuineCheckAvailable();
		Register_UnityEngine_Application_get_genuineCheckAvailable();

		//System.Boolean UnityEngine.Application::get_isBatchMode()
		void Register_UnityEngine_Application_get_isBatchMode();
		Register_UnityEngine_Application_get_isBatchMode();

		//System.Boolean UnityEngine.Application::get_isFocused()
		void Register_UnityEngine_Application_get_isFocused();
		Register_UnityEngine_Application_get_isFocused();

		//System.Boolean UnityEngine.Application::get_isLoadingLevel()
		void Register_UnityEngine_Application_get_isLoadingLevel();
		Register_UnityEngine_Application_get_isLoadingLevel();

		//System.Boolean UnityEngine.Application::get_isPlaying()
		void Register_UnityEngine_Application_get_isPlaying();
		Register_UnityEngine_Application_get_isPlaying();

		//System.Boolean UnityEngine.Application::get_runInBackground()
		void Register_UnityEngine_Application_get_runInBackground();
		Register_UnityEngine_Application_get_runInBackground();

		//System.Int32 UnityEngine.Application::get_targetFrameRate()
		void Register_UnityEngine_Application_get_targetFrameRate();
		Register_UnityEngine_Application_get_targetFrameRate();

		//System.String UnityEngine.Application::get_absoluteURL()
		void Register_UnityEngine_Application_get_absoluteURL();
		Register_UnityEngine_Application_get_absoluteURL();

		//System.String UnityEngine.Application::get_buildGUID()
		void Register_UnityEngine_Application_get_buildGUID();
		Register_UnityEngine_Application_get_buildGUID();

		//System.String UnityEngine.Application::get_cloudProjectId()
		void Register_UnityEngine_Application_get_cloudProjectId();
		Register_UnityEngine_Application_get_cloudProjectId();

		//System.String UnityEngine.Application::get_companyName()
		void Register_UnityEngine_Application_get_companyName();
		Register_UnityEngine_Application_get_companyName();

		//System.String UnityEngine.Application::get_consoleLogPath()
		void Register_UnityEngine_Application_get_consoleLogPath();
		Register_UnityEngine_Application_get_consoleLogPath();

		//System.String UnityEngine.Application::get_dataPath()
		void Register_UnityEngine_Application_get_dataPath();
		Register_UnityEngine_Application_get_dataPath();

		//System.String UnityEngine.Application::get_identifier()
		void Register_UnityEngine_Application_get_identifier();
		Register_UnityEngine_Application_get_identifier();

		//System.String UnityEngine.Application::get_installerName()
		void Register_UnityEngine_Application_get_installerName();
		Register_UnityEngine_Application_get_installerName();

		//System.String UnityEngine.Application::get_persistentDataPath()
		void Register_UnityEngine_Application_get_persistentDataPath();
		Register_UnityEngine_Application_get_persistentDataPath();

		//System.String UnityEngine.Application::get_productName()
		void Register_UnityEngine_Application_get_productName();
		Register_UnityEngine_Application_get_productName();

		//System.String UnityEngine.Application::get_streamingAssetsPath()
		void Register_UnityEngine_Application_get_streamingAssetsPath();
		Register_UnityEngine_Application_get_streamingAssetsPath();

		//System.String UnityEngine.Application::get_temporaryCachePath()
		void Register_UnityEngine_Application_get_temporaryCachePath();
		Register_UnityEngine_Application_get_temporaryCachePath();

		//System.String UnityEngine.Application::get_unityVersion()
		void Register_UnityEngine_Application_get_unityVersion();
		Register_UnityEngine_Application_get_unityVersion();

		//System.String UnityEngine.Application::get_version()
		void Register_UnityEngine_Application_get_version();
		Register_UnityEngine_Application_get_version();

		//System.String[] UnityEngine.Application::GetBuildTags()
		void Register_UnityEngine_Application_GetBuildTags();
		Register_UnityEngine_Application_GetBuildTags();

		//System.Void UnityEngine.Application::OpenURL(System.String)
		void Register_UnityEngine_Application_OpenURL();
		Register_UnityEngine_Application_OpenURL();

		//System.Void UnityEngine.Application::Quit(System.Int32)
		void Register_UnityEngine_Application_Quit();
		Register_UnityEngine_Application_Quit();

		//System.Void UnityEngine.Application::SetBuildTags(System.String[])
		void Register_UnityEngine_Application_SetBuildTags();
		Register_UnityEngine_Application_SetBuildTags();

		//System.Void UnityEngine.Application::SetLogCallbackDefined(System.Boolean)
		void Register_UnityEngine_Application_SetLogCallbackDefined();
		Register_UnityEngine_Application_SetLogCallbackDefined();

		//System.Void UnityEngine.Application::SetStackTraceLogType(UnityEngine.LogType,UnityEngine.StackTraceLogType)
		void Register_UnityEngine_Application_SetStackTraceLogType();
		Register_UnityEngine_Application_SetStackTraceLogType();

		//System.Void UnityEngine.Application::Unload()
		void Register_UnityEngine_Application_Unload();
		Register_UnityEngine_Application_Unload();

		//System.Void UnityEngine.Application::set_backgroundLoadingPriority(UnityEngine.ThreadPriority)
		void Register_UnityEngine_Application_set_backgroundLoadingPriority();
		Register_UnityEngine_Application_set_backgroundLoadingPriority();

		//System.Void UnityEngine.Application::set_runInBackground(System.Boolean)
		void Register_UnityEngine_Application_set_runInBackground();
		Register_UnityEngine_Application_set_runInBackground();

		//System.Void UnityEngine.Application::set_targetFrameRate(System.Int32)
		void Register_UnityEngine_Application_set_targetFrameRate();
		Register_UnityEngine_Application_set_targetFrameRate();

		//UnityEngine.ApplicationInstallMode UnityEngine.Application::get_installMode()
		void Register_UnityEngine_Application_get_installMode();
		Register_UnityEngine_Application_get_installMode();

		//UnityEngine.ApplicationSandboxType UnityEngine.Application::get_sandboxType()
		void Register_UnityEngine_Application_get_sandboxType();
		Register_UnityEngine_Application_get_sandboxType();

		//UnityEngine.AsyncOperation UnityEngine.Application::RequestUserAuthorization(UnityEngine.UserAuthorization)
		void Register_UnityEngine_Application_RequestUserAuthorization();
		Register_UnityEngine_Application_RequestUserAuthorization();

		//UnityEngine.NetworkReachability UnityEngine.Application::get_internetReachability()
		void Register_UnityEngine_Application_get_internetReachability();
		Register_UnityEngine_Application_get_internetReachability();

		//UnityEngine.RuntimePlatform UnityEngine.Application::get_platform()
		void Register_UnityEngine_Application_get_platform();
		Register_UnityEngine_Application_get_platform();

		//UnityEngine.StackTraceLogType UnityEngine.Application::GetStackTraceLogType(UnityEngine.LogType)
		void Register_UnityEngine_Application_GetStackTraceLogType();
		Register_UnityEngine_Application_GetStackTraceLogType();

		//UnityEngine.SystemLanguage UnityEngine.Application::get_systemLanguage()
		void Register_UnityEngine_Application_get_systemLanguage();
		Register_UnityEngine_Application_get_systemLanguage();

		//UnityEngine.ThreadPriority UnityEngine.Application::get_backgroundLoadingPriority()
		void Register_UnityEngine_Application_get_backgroundLoadingPriority();
		Register_UnityEngine_Application_get_backgroundLoadingPriority();

	//End Registrations for type : UnityEngine.Application

	//Start Registrations for type : UnityEngine.AssetBundle

		//System.Boolean UnityEngine.AssetBundle::Contains(System.String)
		void Register_UnityEngine_AssetBundle_Contains();
		Register_UnityEngine_AssetBundle_Contains();

		//System.Boolean UnityEngine.AssetBundle::get_isStreamedSceneAssetBundle()
		void Register_UnityEngine_AssetBundle_get_isStreamedSceneAssetBundle();
		Register_UnityEngine_AssetBundle_get_isStreamedSceneAssetBundle();

		//System.String[] UnityEngine.AssetBundle::GetAllAssetNames()
		void Register_UnityEngine_AssetBundle_GetAllAssetNames();
		Register_UnityEngine_AssetBundle_GetAllAssetNames();

		//System.String[] UnityEngine.AssetBundle::GetAllScenePaths()
		void Register_UnityEngine_AssetBundle_GetAllScenePaths();
		Register_UnityEngine_AssetBundle_GetAllScenePaths();

		//System.Void UnityEngine.AssetBundle::Unload(System.Boolean)
		void Register_UnityEngine_AssetBundle_Unload();
		Register_UnityEngine_AssetBundle_Unload();

		//System.Void UnityEngine.AssetBundle::UnloadAllAssetBundles(System.Boolean)
		void Register_UnityEngine_AssetBundle_UnloadAllAssetBundles();
		Register_UnityEngine_AssetBundle_UnloadAllAssetBundles();

		//UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromFile_Internal(System.String,System.UInt32,System.UInt64)
		void Register_UnityEngine_AssetBundle_LoadFromFile_Internal();
		Register_UnityEngine_AssetBundle_LoadFromFile_Internal();

		//UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromMemory_Internal(System.Byte[],System.UInt32)
		void Register_UnityEngine_AssetBundle_LoadFromMemory_Internal();
		Register_UnityEngine_AssetBundle_LoadFromMemory_Internal();

		//UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromStreamInternal(System.IO.Stream,System.UInt32,System.UInt32)
		void Register_UnityEngine_AssetBundle_LoadFromStreamInternal();
		Register_UnityEngine_AssetBundle_LoadFromStreamInternal();

		//UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromFileAsync_Internal(System.String,System.UInt32,System.UInt64)
		void Register_UnityEngine_AssetBundle_LoadFromFileAsync_Internal();
		Register_UnityEngine_AssetBundle_LoadFromFileAsync_Internal();

		//UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromMemoryAsync_Internal(System.Byte[],System.UInt32)
		void Register_UnityEngine_AssetBundle_LoadFromMemoryAsync_Internal();
		Register_UnityEngine_AssetBundle_LoadFromMemoryAsync_Internal();

		//UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromStreamAsyncInternal(System.IO.Stream,System.UInt32,System.UInt32)
		void Register_UnityEngine_AssetBundle_LoadFromStreamAsyncInternal();
		Register_UnityEngine_AssetBundle_LoadFromStreamAsyncInternal();

		//UnityEngine.AssetBundleRecompressOperation UnityEngine.AssetBundle::RecompressAssetBundleAsync_Internal_Injected(System.String,System.String,UnityEngine.BuildCompression&,System.UInt32,UnityEngine.ThreadPriority)
		void Register_UnityEngine_AssetBundle_RecompressAssetBundleAsync_Internal_Injected();
		Register_UnityEngine_AssetBundle_RecompressAssetBundleAsync_Internal_Injected();

		//UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAssetAsync_Internal(System.String,System.Type)
		void Register_UnityEngine_AssetBundle_LoadAssetAsync_Internal();
		Register_UnityEngine_AssetBundle_LoadAssetAsync_Internal();

		//UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAssetWithSubAssetsAsync_Internal(System.String,System.Type)
		void Register_UnityEngine_AssetBundle_LoadAssetWithSubAssetsAsync_Internal();
		Register_UnityEngine_AssetBundle_LoadAssetWithSubAssetsAsync_Internal();

		//UnityEngine.AssetBundle[] UnityEngine.AssetBundle::GetAllLoadedAssetBundles_Native()
		void Register_UnityEngine_AssetBundle_GetAllLoadedAssetBundles_Native();
		Register_UnityEngine_AssetBundle_GetAllLoadedAssetBundles_Native();

		//UnityEngine.Object UnityEngine.AssetBundle::LoadAsset_Internal(System.String,System.Type)
		void Register_UnityEngine_AssetBundle_LoadAsset_Internal();
		Register_UnityEngine_AssetBundle_LoadAsset_Internal();

		//UnityEngine.Object UnityEngine.AssetBundle::returnMainAsset(UnityEngine.AssetBundle)
		void Register_UnityEngine_AssetBundle_returnMainAsset();
		Register_UnityEngine_AssetBundle_returnMainAsset();

		//UnityEngine.Object[] UnityEngine.AssetBundle::LoadAssetWithSubAssets_Internal(System.String,System.Type)
		void Register_UnityEngine_AssetBundle_LoadAssetWithSubAssets_Internal();
		Register_UnityEngine_AssetBundle_LoadAssetWithSubAssets_Internal();

	//End Registrations for type : UnityEngine.AssetBundle

	//Start Registrations for type : UnityEngine.AssetBundleCreateRequest

		//UnityEngine.AssetBundle UnityEngine.AssetBundleCreateRequest::get_assetBundle()
		void Register_UnityEngine_AssetBundleCreateRequest_get_assetBundle();
		Register_UnityEngine_AssetBundleCreateRequest_get_assetBundle();

	//End Registrations for type : UnityEngine.AssetBundleCreateRequest

	//Start Registrations for type : UnityEngine.AssetBundleLoadingCache

		//System.UInt32 UnityEngine.AssetBundleLoadingCache::get_blockCount()
		void Register_UnityEngine_AssetBundleLoadingCache_get_blockCount();
		Register_UnityEngine_AssetBundleLoadingCache_get_blockCount();

		//System.UInt32 UnityEngine.AssetBundleLoadingCache::get_blockSize()
		void Register_UnityEngine_AssetBundleLoadingCache_get_blockSize();
		Register_UnityEngine_AssetBundleLoadingCache_get_blockSize();

		//System.UInt32 UnityEngine.AssetBundleLoadingCache::get_maxBlocksPerFile()
		void Register_UnityEngine_AssetBundleLoadingCache_get_maxBlocksPerFile();
		Register_UnityEngine_AssetBundleLoadingCache_get_maxBlocksPerFile();

		//System.Void UnityEngine.AssetBundleLoadingCache::set_blockCount(System.UInt32)
		void Register_UnityEngine_AssetBundleLoadingCache_set_blockCount();
		Register_UnityEngine_AssetBundleLoadingCache_set_blockCount();

		//System.Void UnityEngine.AssetBundleLoadingCache::set_maxBlocksPerFile(System.UInt32)
		void Register_UnityEngine_AssetBundleLoadingCache_set_maxBlocksPerFile();
		Register_UnityEngine_AssetBundleLoadingCache_set_maxBlocksPerFile();

	//End Registrations for type : UnityEngine.AssetBundleLoadingCache

	//Start Registrations for type : UnityEngine.AssetBundleManifest

		//System.String[] UnityEngine.AssetBundleManifest::GetAllAssetBundles()
		void Register_UnityEngine_AssetBundleManifest_GetAllAssetBundles();
		Register_UnityEngine_AssetBundleManifest_GetAllAssetBundles();

		//System.String[] UnityEngine.AssetBundleManifest::GetAllAssetBundlesWithVariant()
		void Register_UnityEngine_AssetBundleManifest_GetAllAssetBundlesWithVariant();
		Register_UnityEngine_AssetBundleManifest_GetAllAssetBundlesWithVariant();

		//System.String[] UnityEngine.AssetBundleManifest::GetAllDependencies(System.String)
		void Register_UnityEngine_AssetBundleManifest_GetAllDependencies();
		Register_UnityEngine_AssetBundleManifest_GetAllDependencies();

		//System.Void UnityEngine.AssetBundleManifest::GetAssetBundleHash_Injected(System.String,UnityEngine.Hash128&)
		void Register_UnityEngine_AssetBundleManifest_GetAssetBundleHash_Injected();
		Register_UnityEngine_AssetBundleManifest_GetAssetBundleHash_Injected();

	//End Registrations for type : UnityEngine.AssetBundleManifest

	//Start Registrations for type : UnityEngine.AssetBundleRequest

		//UnityEngine.Object UnityEngine.AssetBundleRequest::GetResult()
		void Register_UnityEngine_AssetBundleRequest_GetResult();
		Register_UnityEngine_AssetBundleRequest_GetResult();

	//End Registrations for type : UnityEngine.AssetBundleRequest

	//Start Registrations for type : UnityEngine.AsyncOperation

		//System.Boolean UnityEngine.AsyncOperation::get_isDone()
		void Register_UnityEngine_AsyncOperation_get_isDone();
		Register_UnityEngine_AsyncOperation_get_isDone();

		//System.Single UnityEngine.AsyncOperation::get_progress()
		void Register_UnityEngine_AsyncOperation_get_progress();
		Register_UnityEngine_AsyncOperation_get_progress();

		//System.Void UnityEngine.AsyncOperation::InternalDestroy(System.IntPtr)
		void Register_UnityEngine_AsyncOperation_InternalDestroy();
		Register_UnityEngine_AsyncOperation_InternalDestroy();

		//System.Void UnityEngine.AsyncOperation::set_allowSceneActivation(System.Boolean)
		void Register_UnityEngine_AsyncOperation_set_allowSceneActivation();
		Register_UnityEngine_AsyncOperation_set_allowSceneActivation();

	//End Registrations for type : UnityEngine.AsyncOperation

	//Start Registrations for type : UnityEngine.Audio.AudioMixer

		//System.Boolean UnityEngine.Audio.AudioMixer::GetFloat(System.String,System.Single&)
		void Register_UnityEngine_Audio_AudioMixer_GetFloat();
		Register_UnityEngine_Audio_AudioMixer_GetFloat();

		//System.Boolean UnityEngine.Audio.AudioMixer::SetFloat(System.String,System.Single)
		void Register_UnityEngine_Audio_AudioMixer_SetFloat();
		Register_UnityEngine_Audio_AudioMixer_SetFloat();

	//End Registrations for type : UnityEngine.Audio.AudioMixer

	//Start Registrations for type : UnityEngine.AudioClip

		//System.Boolean UnityEngine.AudioClip::GetData(UnityEngine.AudioClip,System.Single[],System.Int32,System.Int32)
		void Register_UnityEngine_AudioClip_GetData();
		Register_UnityEngine_AudioClip_GetData();

		//System.Boolean UnityEngine.AudioClip::LoadAudioData()
		void Register_UnityEngine_AudioClip_LoadAudioData();
		Register_UnityEngine_AudioClip_LoadAudioData();

		//System.Boolean UnityEngine.AudioClip::SetData(UnityEngine.AudioClip,System.Single[],System.Int32,System.Int32)
		void Register_UnityEngine_AudioClip_SetData();
		Register_UnityEngine_AudioClip_SetData();

		//System.Boolean UnityEngine.AudioClip::UnloadAudioData()
		void Register_UnityEngine_AudioClip_UnloadAudioData();
		Register_UnityEngine_AudioClip_UnloadAudioData();

		//System.Boolean UnityEngine.AudioClip::get_ambisonic()
		void Register_UnityEngine_AudioClip_get_ambisonic();
		Register_UnityEngine_AudioClip_get_ambisonic();

		//System.Boolean UnityEngine.AudioClip::get_loadInBackground()
		void Register_UnityEngine_AudioClip_get_loadInBackground();
		Register_UnityEngine_AudioClip_get_loadInBackground();

		//System.Boolean UnityEngine.AudioClip::get_preloadAudioData()
		void Register_UnityEngine_AudioClip_get_preloadAudioData();
		Register_UnityEngine_AudioClip_get_preloadAudioData();

		//System.Int32 UnityEngine.AudioClip::get_channels()
		void Register_UnityEngine_AudioClip_get_channels();
		Register_UnityEngine_AudioClip_get_channels();

		//System.Int32 UnityEngine.AudioClip::get_frequency()
		void Register_UnityEngine_AudioClip_get_frequency();
		Register_UnityEngine_AudioClip_get_frequency();

		//System.Int32 UnityEngine.AudioClip::get_samples()
		void Register_UnityEngine_AudioClip_get_samples();
		Register_UnityEngine_AudioClip_get_samples();

		//System.Single UnityEngine.AudioClip::get_length()
		void Register_UnityEngine_AudioClip_get_length();
		Register_UnityEngine_AudioClip_get_length();

		//System.String UnityEngine.AudioClip::GetName()
		void Register_UnityEngine_AudioClip_GetName();
		Register_UnityEngine_AudioClip_GetName();

		//System.Void UnityEngine.AudioClip::CreateUserSound(System.String,System.Int32,System.Int32,System.Int32,System.Boolean)
		void Register_UnityEngine_AudioClip_CreateUserSound();
		Register_UnityEngine_AudioClip_CreateUserSound();

		//UnityEngine.AudioClip UnityEngine.AudioClip::Construct_Internal()
		void Register_UnityEngine_AudioClip_Construct_Internal();
		Register_UnityEngine_AudioClip_Construct_Internal();

		//UnityEngine.AudioClipLoadType UnityEngine.AudioClip::get_loadType()
		void Register_UnityEngine_AudioClip_get_loadType();
		Register_UnityEngine_AudioClip_get_loadType();

		//UnityEngine.AudioDataLoadState UnityEngine.AudioClip::get_loadState()
		void Register_UnityEngine_AudioClip_get_loadState();
		Register_UnityEngine_AudioClip_get_loadState();

	//End Registrations for type : UnityEngine.AudioClip

	//Start Registrations for type : UnityEngine.AudioListener

		//System.Single UnityEngine.AudioListener::get_volume()
		void Register_UnityEngine_AudioListener_get_volume();
		Register_UnityEngine_AudioListener_get_volume();

		//System.Void UnityEngine.AudioListener::set_volume(System.Single)
		void Register_UnityEngine_AudioListener_set_volume();
		Register_UnityEngine_AudioListener_set_volume();

	//End Registrations for type : UnityEngine.AudioListener

	//Start Registrations for type : UnityEngine.AudioSettings

		//System.Boolean UnityEngine.AudioSettings::StartAudioOutput()
		void Register_UnityEngine_AudioSettings_StartAudioOutput();
		Register_UnityEngine_AudioSettings_StartAudioOutput();

		//System.Boolean UnityEngine.AudioSettings::StopAudioOutput()
		void Register_UnityEngine_AudioSettings_StopAudioOutput();
		Register_UnityEngine_AudioSettings_StopAudioOutput();

	//End Registrations for type : UnityEngine.AudioSettings

	//Start Registrations for type : UnityEngine.AudioSource

		//System.Boolean UnityEngine.AudioSource::GetAmbisonicDecoderFloat(System.Int32,System.Single&)
		void Register_UnityEngine_AudioSource_GetAmbisonicDecoderFloat();
		Register_UnityEngine_AudioSource_GetAmbisonicDecoderFloat();

		//System.Boolean UnityEngine.AudioSource::GetSpatializerFloat(System.Int32,System.Single&)
		void Register_UnityEngine_AudioSource_GetSpatializerFloat();
		Register_UnityEngine_AudioSource_GetSpatializerFloat();

		//System.Boolean UnityEngine.AudioSource::SetAmbisonicDecoderFloat(System.Int32,System.Single)
		void Register_UnityEngine_AudioSource_SetAmbisonicDecoderFloat();
		Register_UnityEngine_AudioSource_SetAmbisonicDecoderFloat();

		//System.Boolean UnityEngine.AudioSource::SetSpatializerFloat(System.Int32,System.Single)
		void Register_UnityEngine_AudioSource_SetSpatializerFloat();
		Register_UnityEngine_AudioSource_SetSpatializerFloat();

		//System.Boolean UnityEngine.AudioSource::get_bypassEffects()
		void Register_UnityEngine_AudioSource_get_bypassEffects();
		Register_UnityEngine_AudioSource_get_bypassEffects();

		//System.Boolean UnityEngine.AudioSource::get_bypassListenerEffects()
		void Register_UnityEngine_AudioSource_get_bypassListenerEffects();
		Register_UnityEngine_AudioSource_get_bypassListenerEffects();

		//System.Boolean UnityEngine.AudioSource::get_bypassReverbZones()
		void Register_UnityEngine_AudioSource_get_bypassReverbZones();
		Register_UnityEngine_AudioSource_get_bypassReverbZones();

		//System.Boolean UnityEngine.AudioSource::get_ignoreListenerPause()
		void Register_UnityEngine_AudioSource_get_ignoreListenerPause();
		Register_UnityEngine_AudioSource_get_ignoreListenerPause();

		//System.Boolean UnityEngine.AudioSource::get_ignoreListenerVolume()
		void Register_UnityEngine_AudioSource_get_ignoreListenerVolume();
		Register_UnityEngine_AudioSource_get_ignoreListenerVolume();

		//System.Boolean UnityEngine.AudioSource::get_isPlaying()
		void Register_UnityEngine_AudioSource_get_isPlaying();
		Register_UnityEngine_AudioSource_get_isPlaying();

		//System.Boolean UnityEngine.AudioSource::get_isVirtual()
		void Register_UnityEngine_AudioSource_get_isVirtual();
		Register_UnityEngine_AudioSource_get_isVirtual();

		//System.Boolean UnityEngine.AudioSource::get_loop()
		void Register_UnityEngine_AudioSource_get_loop();
		Register_UnityEngine_AudioSource_get_loop();

		//System.Boolean UnityEngine.AudioSource::get_mute()
		void Register_UnityEngine_AudioSource_get_mute();
		Register_UnityEngine_AudioSource_get_mute();

		//System.Boolean UnityEngine.AudioSource::get_playOnAwake()
		void Register_UnityEngine_AudioSource_get_playOnAwake();
		Register_UnityEngine_AudioSource_get_playOnAwake();

		//System.Boolean UnityEngine.AudioSource::get_spatialize()
		void Register_UnityEngine_AudioSource_get_spatialize();
		Register_UnityEngine_AudioSource_get_spatialize();

		//System.Boolean UnityEngine.AudioSource::get_spatializePostEffects()
		void Register_UnityEngine_AudioSource_get_spatializePostEffects();
		Register_UnityEngine_AudioSource_get_spatializePostEffects();

		//System.Int32 UnityEngine.AudioSource::get_priority()
		void Register_UnityEngine_AudioSource_get_priority();
		Register_UnityEngine_AudioSource_get_priority();

		//System.Int32 UnityEngine.AudioSource::get_timeSamples()
		void Register_UnityEngine_AudioSource_get_timeSamples();
		Register_UnityEngine_AudioSource_get_timeSamples();

		//System.Single UnityEngine.AudioSource::GetPitch(UnityEngine.AudioSource)
		void Register_UnityEngine_AudioSource_GetPitch();
		Register_UnityEngine_AudioSource_GetPitch();

		//System.Single UnityEngine.AudioSource::get_dopplerLevel()
		void Register_UnityEngine_AudioSource_get_dopplerLevel();
		Register_UnityEngine_AudioSource_get_dopplerLevel();

		//System.Single UnityEngine.AudioSource::get_maxDistance()
		void Register_UnityEngine_AudioSource_get_maxDistance();
		Register_UnityEngine_AudioSource_get_maxDistance();

		//System.Single UnityEngine.AudioSource::get_minDistance()
		void Register_UnityEngine_AudioSource_get_minDistance();
		Register_UnityEngine_AudioSource_get_minDistance();

		//System.Single UnityEngine.AudioSource::get_panStereo()
		void Register_UnityEngine_AudioSource_get_panStereo();
		Register_UnityEngine_AudioSource_get_panStereo();

		//System.Single UnityEngine.AudioSource::get_reverbZoneMix()
		void Register_UnityEngine_AudioSource_get_reverbZoneMix();
		Register_UnityEngine_AudioSource_get_reverbZoneMix();

		//System.Single UnityEngine.AudioSource::get_spatialBlend()
		void Register_UnityEngine_AudioSource_get_spatialBlend();
		Register_UnityEngine_AudioSource_get_spatialBlend();

		//System.Single UnityEngine.AudioSource::get_spread()
		void Register_UnityEngine_AudioSource_get_spread();
		Register_UnityEngine_AudioSource_get_spread();

		//System.Single UnityEngine.AudioSource::get_time()
		void Register_UnityEngine_AudioSource_get_time();
		Register_UnityEngine_AudioSource_get_time();

		//System.Single UnityEngine.AudioSource::get_volume()
		void Register_UnityEngine_AudioSource_get_volume();
		Register_UnityEngine_AudioSource_get_volume();

		//System.Void UnityEngine.AudioSource::GetOutputDataHelper(UnityEngine.AudioSource,System.Single[],System.Int32)
		void Register_UnityEngine_AudioSource_GetOutputDataHelper();
		Register_UnityEngine_AudioSource_GetOutputDataHelper();

		//System.Void UnityEngine.AudioSource::GetSpectrumDataHelper(UnityEngine.AudioSource,System.Single[],System.Int32,UnityEngine.FFTWindow)
		void Register_UnityEngine_AudioSource_GetSpectrumDataHelper();
		Register_UnityEngine_AudioSource_GetSpectrumDataHelper();

		//System.Void UnityEngine.AudioSource::Pause()
		void Register_UnityEngine_AudioSource_Pause();
		Register_UnityEngine_AudioSource_Pause();

		//System.Void UnityEngine.AudioSource::Play(System.Double)
		void Register_UnityEngine_AudioSource_Play();
		Register_UnityEngine_AudioSource_Play();

		//System.Void UnityEngine.AudioSource::PlayHelper(UnityEngine.AudioSource,System.UInt64)
		void Register_UnityEngine_AudioSource_PlayHelper();
		Register_UnityEngine_AudioSource_PlayHelper();

		//System.Void UnityEngine.AudioSource::PlayOneShotHelper(UnityEngine.AudioSource,UnityEngine.AudioClip,System.Single)
		void Register_UnityEngine_AudioSource_PlayOneShotHelper();
		Register_UnityEngine_AudioSource_PlayOneShotHelper();

		//System.Void UnityEngine.AudioSource::SetCustomCurveHelper(UnityEngine.AudioSource,UnityEngine.AudioSourceCurveType,UnityEngine.AnimationCurve)
		void Register_UnityEngine_AudioSource_SetCustomCurveHelper();
		Register_UnityEngine_AudioSource_SetCustomCurveHelper();

		//System.Void UnityEngine.AudioSource::SetPitch(UnityEngine.AudioSource,System.Single)
		void Register_UnityEngine_AudioSource_SetPitch();
		Register_UnityEngine_AudioSource_SetPitch();

		//System.Void UnityEngine.AudioSource::SetScheduledEndTime(System.Double)
		void Register_UnityEngine_AudioSource_SetScheduledEndTime();
		Register_UnityEngine_AudioSource_SetScheduledEndTime();

		//System.Void UnityEngine.AudioSource::SetScheduledStartTime(System.Double)
		void Register_UnityEngine_AudioSource_SetScheduledStartTime();
		Register_UnityEngine_AudioSource_SetScheduledStartTime();

		//System.Void UnityEngine.AudioSource::Stop(System.Boolean)
		void Register_UnityEngine_AudioSource_Stop();
		Register_UnityEngine_AudioSource_Stop();

		//System.Void UnityEngine.AudioSource::UnPause()
		void Register_UnityEngine_AudioSource_UnPause();
		Register_UnityEngine_AudioSource_UnPause();

		//System.Void UnityEngine.AudioSource::set_bypassEffects(System.Boolean)
		void Register_UnityEngine_AudioSource_set_bypassEffects();
		Register_UnityEngine_AudioSource_set_bypassEffects();

		//System.Void UnityEngine.AudioSource::set_bypassListenerEffects(System.Boolean)
		void Register_UnityEngine_AudioSource_set_bypassListenerEffects();
		Register_UnityEngine_AudioSource_set_bypassListenerEffects();

		//System.Void UnityEngine.AudioSource::set_bypassReverbZones(System.Boolean)
		void Register_UnityEngine_AudioSource_set_bypassReverbZones();
		Register_UnityEngine_AudioSource_set_bypassReverbZones();

		//System.Void UnityEngine.AudioSource::set_clip(UnityEngine.AudioClip)
		void Register_UnityEngine_AudioSource_set_clip();
		Register_UnityEngine_AudioSource_set_clip();

		//System.Void UnityEngine.AudioSource::set_dopplerLevel(System.Single)
		void Register_UnityEngine_AudioSource_set_dopplerLevel();
		Register_UnityEngine_AudioSource_set_dopplerLevel();

		//System.Void UnityEngine.AudioSource::set_ignoreListenerPause(System.Boolean)
		void Register_UnityEngine_AudioSource_set_ignoreListenerPause();
		Register_UnityEngine_AudioSource_set_ignoreListenerPause();

		//System.Void UnityEngine.AudioSource::set_ignoreListenerVolume(System.Boolean)
		void Register_UnityEngine_AudioSource_set_ignoreListenerVolume();
		Register_UnityEngine_AudioSource_set_ignoreListenerVolume();

		//System.Void UnityEngine.AudioSource::set_loop(System.Boolean)
		void Register_UnityEngine_AudioSource_set_loop();
		Register_UnityEngine_AudioSource_set_loop();

		//System.Void UnityEngine.AudioSource::set_maxDistance(System.Single)
		void Register_UnityEngine_AudioSource_set_maxDistance();
		Register_UnityEngine_AudioSource_set_maxDistance();

		//System.Void UnityEngine.AudioSource::set_minDistance(System.Single)
		void Register_UnityEngine_AudioSource_set_minDistance();
		Register_UnityEngine_AudioSource_set_minDistance();

		//System.Void UnityEngine.AudioSource::set_mute(System.Boolean)
		void Register_UnityEngine_AudioSource_set_mute();
		Register_UnityEngine_AudioSource_set_mute();

		//System.Void UnityEngine.AudioSource::set_outputAudioMixerGroup(UnityEngine.Audio.AudioMixerGroup)
		void Register_UnityEngine_AudioSource_set_outputAudioMixerGroup();
		Register_UnityEngine_AudioSource_set_outputAudioMixerGroup();

		//System.Void UnityEngine.AudioSource::set_panStereo(System.Single)
		void Register_UnityEngine_AudioSource_set_panStereo();
		Register_UnityEngine_AudioSource_set_panStereo();

		//System.Void UnityEngine.AudioSource::set_playOnAwake(System.Boolean)
		void Register_UnityEngine_AudioSource_set_playOnAwake();
		Register_UnityEngine_AudioSource_set_playOnAwake();

		//System.Void UnityEngine.AudioSource::set_priority(System.Int32)
		void Register_UnityEngine_AudioSource_set_priority();
		Register_UnityEngine_AudioSource_set_priority();

		//System.Void UnityEngine.AudioSource::set_reverbZoneMix(System.Single)
		void Register_UnityEngine_AudioSource_set_reverbZoneMix();
		Register_UnityEngine_AudioSource_set_reverbZoneMix();

		//System.Void UnityEngine.AudioSource::set_rolloffMode(UnityEngine.AudioRolloffMode)
		void Register_UnityEngine_AudioSource_set_rolloffMode();
		Register_UnityEngine_AudioSource_set_rolloffMode();

		//System.Void UnityEngine.AudioSource::set_spatialBlend(System.Single)
		void Register_UnityEngine_AudioSource_set_spatialBlend();
		Register_UnityEngine_AudioSource_set_spatialBlend();

		//System.Void UnityEngine.AudioSource::set_spatialize(System.Boolean)
		void Register_UnityEngine_AudioSource_set_spatialize();
		Register_UnityEngine_AudioSource_set_spatialize();

		//System.Void UnityEngine.AudioSource::set_spatializePostEffects(System.Boolean)
		void Register_UnityEngine_AudioSource_set_spatializePostEffects();
		Register_UnityEngine_AudioSource_set_spatializePostEffects();

		//System.Void UnityEngine.AudioSource::set_spread(System.Single)
		void Register_UnityEngine_AudioSource_set_spread();
		Register_UnityEngine_AudioSource_set_spread();

		//System.Void UnityEngine.AudioSource::set_time(System.Single)
		void Register_UnityEngine_AudioSource_set_time();
		Register_UnityEngine_AudioSource_set_time();

		//System.Void UnityEngine.AudioSource::set_timeSamples(System.Int32)
		void Register_UnityEngine_AudioSource_set_timeSamples();
		Register_UnityEngine_AudioSource_set_timeSamples();

		//System.Void UnityEngine.AudioSource::set_velocityUpdateMode(UnityEngine.AudioVelocityUpdateMode)
		void Register_UnityEngine_AudioSource_set_velocityUpdateMode();
		Register_UnityEngine_AudioSource_set_velocityUpdateMode();

		//System.Void UnityEngine.AudioSource::set_volume(System.Single)
		void Register_UnityEngine_AudioSource_set_volume();
		Register_UnityEngine_AudioSource_set_volume();

		//UnityEngine.AnimationCurve UnityEngine.AudioSource::GetCustomCurveHelper(UnityEngine.AudioSource,UnityEngine.AudioSourceCurveType)
		void Register_UnityEngine_AudioSource_GetCustomCurveHelper();
		Register_UnityEngine_AudioSource_GetCustomCurveHelper();

		//UnityEngine.Audio.AudioMixerGroup UnityEngine.AudioSource::get_outputAudioMixerGroup()
		void Register_UnityEngine_AudioSource_get_outputAudioMixerGroup();
		Register_UnityEngine_AudioSource_get_outputAudioMixerGroup();

		//UnityEngine.AudioClip UnityEngine.AudioSource::get_clip()
		void Register_UnityEngine_AudioSource_get_clip();
		Register_UnityEngine_AudioSource_get_clip();

		//UnityEngine.AudioRolloffMode UnityEngine.AudioSource::get_rolloffMode()
		void Register_UnityEngine_AudioSource_get_rolloffMode();
		Register_UnityEngine_AudioSource_get_rolloffMode();

		//UnityEngine.AudioVelocityUpdateMode UnityEngine.AudioSource::get_velocityUpdateMode()
		void Register_UnityEngine_AudioSource_get_velocityUpdateMode();
		Register_UnityEngine_AudioSource_get_velocityUpdateMode();

	//End Registrations for type : UnityEngine.AudioSource

	//Start Registrations for type : UnityEngine.Behaviour

		//System.Boolean UnityEngine.Behaviour::get_enabled()
		void Register_UnityEngine_Behaviour_get_enabled();
		Register_UnityEngine_Behaviour_get_enabled();

		//System.Boolean UnityEngine.Behaviour::get_isActiveAndEnabled()
		void Register_UnityEngine_Behaviour_get_isActiveAndEnabled();
		Register_UnityEngine_Behaviour_get_isActiveAndEnabled();

		//System.Void UnityEngine.Behaviour::set_enabled(System.Boolean)
		void Register_UnityEngine_Behaviour_set_enabled();
		Register_UnityEngine_Behaviour_set_enabled();

	//End Registrations for type : UnityEngine.Behaviour

	//Start Registrations for type : UnityEngine.BoxCollider

		//System.Void UnityEngine.BoxCollider::get_center_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_BoxCollider_get_center_Injected();
		Register_UnityEngine_BoxCollider_get_center_Injected();

		//System.Void UnityEngine.BoxCollider::get_size_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_BoxCollider_get_size_Injected();
		Register_UnityEngine_BoxCollider_get_size_Injected();

		//System.Void UnityEngine.BoxCollider::set_center_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_BoxCollider_set_center_Injected();
		Register_UnityEngine_BoxCollider_set_center_Injected();

		//System.Void UnityEngine.BoxCollider::set_size_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_BoxCollider_set_size_Injected();
		Register_UnityEngine_BoxCollider_set_size_Injected();

	//End Registrations for type : UnityEngine.BoxCollider

	//Start Registrations for type : UnityEngine.BoxCollider2D

		//System.Void UnityEngine.BoxCollider2D::get_size_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_BoxCollider2D_get_size_Injected();
		Register_UnityEngine_BoxCollider2D_get_size_Injected();

		//System.Void UnityEngine.BoxCollider2D::set_size_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_BoxCollider2D_set_size_Injected();
		Register_UnityEngine_BoxCollider2D_set_size_Injected();

	//End Registrations for type : UnityEngine.BoxCollider2D

	//Start Registrations for type : UnityEngine.Camera

		//System.Boolean UnityEngine.Camera::GetCullingParameters_Internal(UnityEngine.Camera,System.Boolean,UnityEngine.Rendering.ScriptableCullingParameters&,System.Int32)
		void Register_UnityEngine_Camera_GetCullingParameters_Internal();
		Register_UnityEngine_Camera_GetCullingParameters_Internal();

		//System.Boolean UnityEngine.Camera::RenderToCubemapEyeImpl(UnityEngine.RenderTexture,System.Int32,UnityEngine.Camera/MonoOrStereoscopicEye)
		void Register_UnityEngine_Camera_RenderToCubemapEyeImpl();
		Register_UnityEngine_Camera_RenderToCubemapEyeImpl();

		//System.Boolean UnityEngine.Camera::RenderToCubemapImpl(UnityEngine.Texture,System.Int32)
		void Register_UnityEngine_Camera_RenderToCubemapImpl();
		Register_UnityEngine_Camera_RenderToCubemapImpl();

		//System.Boolean UnityEngine.Camera::get_allowDynamicResolution()
		void Register_UnityEngine_Camera_get_allowDynamicResolution();
		Register_UnityEngine_Camera_get_allowDynamicResolution();

		//System.Boolean UnityEngine.Camera::get_allowHDR()
		void Register_UnityEngine_Camera_get_allowHDR();
		Register_UnityEngine_Camera_get_allowHDR();

		//System.Boolean UnityEngine.Camera::get_allowMSAA()
		void Register_UnityEngine_Camera_get_allowMSAA();
		Register_UnityEngine_Camera_get_allowMSAA();

		//System.Boolean UnityEngine.Camera::get_areVRStereoViewMatricesWithinSingleCullTolerance()
		void Register_UnityEngine_Camera_get_areVRStereoViewMatricesWithinSingleCullTolerance();
		Register_UnityEngine_Camera_get_areVRStereoViewMatricesWithinSingleCullTolerance();

		//System.Boolean UnityEngine.Camera::get_clearStencilAfterLightingPass()
		void Register_UnityEngine_Camera_get_clearStencilAfterLightingPass();
		Register_UnityEngine_Camera_get_clearStencilAfterLightingPass();

		//System.Boolean UnityEngine.Camera::get_forceIntoRenderTexture()
		void Register_UnityEngine_Camera_get_forceIntoRenderTexture();
		Register_UnityEngine_Camera_get_forceIntoRenderTexture();

		//System.Boolean UnityEngine.Camera::get_layerCullSpherical()
		void Register_UnityEngine_Camera_get_layerCullSpherical();
		Register_UnityEngine_Camera_get_layerCullSpherical();

		//System.Boolean UnityEngine.Camera::get_orthographic()
		void Register_UnityEngine_Camera_get_orthographic();
		Register_UnityEngine_Camera_get_orthographic();

		//System.Boolean UnityEngine.Camera::get_stereoEnabled()
		void Register_UnityEngine_Camera_get_stereoEnabled();
		Register_UnityEngine_Camera_get_stereoEnabled();

		//System.Boolean UnityEngine.Camera::get_useJitteredProjectionMatrixForTransparentRendering()
		void Register_UnityEngine_Camera_get_useJitteredProjectionMatrixForTransparentRendering();
		Register_UnityEngine_Camera_get_useJitteredProjectionMatrixForTransparentRendering();

		//System.Boolean UnityEngine.Camera::get_useOcclusionCulling()
		void Register_UnityEngine_Camera_get_useOcclusionCulling();
		Register_UnityEngine_Camera_get_useOcclusionCulling();

		//System.Boolean UnityEngine.Camera::get_usePhysicalProperties()
		void Register_UnityEngine_Camera_get_usePhysicalProperties();
		Register_UnityEngine_Camera_get_usePhysicalProperties();

		//System.Int32 UnityEngine.Camera::GetAllCamerasCount()
		void Register_UnityEngine_Camera_GetAllCamerasCount();
		Register_UnityEngine_Camera_GetAllCamerasCount();

		//System.Int32 UnityEngine.Camera::GetAllCamerasImpl(UnityEngine.Camera[])
		void Register_UnityEngine_Camera_GetAllCamerasImpl();
		Register_UnityEngine_Camera_GetAllCamerasImpl();

		//System.Int32 UnityEngine.Camera::get_commandBufferCount()
		void Register_UnityEngine_Camera_get_commandBufferCount();
		Register_UnityEngine_Camera_get_commandBufferCount();

		//System.Int32 UnityEngine.Camera::get_cullingMask()
		void Register_UnityEngine_Camera_get_cullingMask();
		Register_UnityEngine_Camera_get_cullingMask();

		//System.Int32 UnityEngine.Camera::get_eventMask()
		void Register_UnityEngine_Camera_get_eventMask();
		Register_UnityEngine_Camera_get_eventMask();

		//System.Int32 UnityEngine.Camera::get_pixelHeight()
		void Register_UnityEngine_Camera_get_pixelHeight();
		Register_UnityEngine_Camera_get_pixelHeight();

		//System.Int32 UnityEngine.Camera::get_pixelWidth()
		void Register_UnityEngine_Camera_get_pixelWidth();
		Register_UnityEngine_Camera_get_pixelWidth();

		//System.Int32 UnityEngine.Camera::get_scaledPixelHeight()
		void Register_UnityEngine_Camera_get_scaledPixelHeight();
		Register_UnityEngine_Camera_get_scaledPixelHeight();

		//System.Int32 UnityEngine.Camera::get_scaledPixelWidth()
		void Register_UnityEngine_Camera_get_scaledPixelWidth();
		Register_UnityEngine_Camera_get_scaledPixelWidth();

		//System.Int32 UnityEngine.Camera::get_targetDisplay()
		void Register_UnityEngine_Camera_get_targetDisplay();
		Register_UnityEngine_Camera_get_targetDisplay();

		//System.Single UnityEngine.Camera::FieldOfViewToFocalLength(System.Single,System.Single)
		void Register_UnityEngine_Camera_FieldOfViewToFocalLength();
		Register_UnityEngine_Camera_FieldOfViewToFocalLength();

		//System.Single UnityEngine.Camera::FocalLengthToFieldOfView(System.Single,System.Single)
		void Register_UnityEngine_Camera_FocalLengthToFieldOfView();
		Register_UnityEngine_Camera_FocalLengthToFieldOfView();

		//System.Single UnityEngine.Camera::GetGateFittedFieldOfView()
		void Register_UnityEngine_Camera_GetGateFittedFieldOfView();
		Register_UnityEngine_Camera_GetGateFittedFieldOfView();

		//System.Single UnityEngine.Camera::HorizontalToVerticalFieldOfView(System.Single,System.Single)
		void Register_UnityEngine_Camera_HorizontalToVerticalFieldOfView();
		Register_UnityEngine_Camera_HorizontalToVerticalFieldOfView();

		//System.Single UnityEngine.Camera::VerticalToHorizontalFieldOfView(System.Single,System.Single)
		void Register_UnityEngine_Camera_VerticalToHorizontalFieldOfView();
		Register_UnityEngine_Camera_VerticalToHorizontalFieldOfView();

		//System.Single UnityEngine.Camera::get_aspect()
		void Register_UnityEngine_Camera_get_aspect();
		Register_UnityEngine_Camera_get_aspect();

		//System.Single UnityEngine.Camera::get_depth()
		void Register_UnityEngine_Camera_get_depth();
		Register_UnityEngine_Camera_get_depth();

		//System.Single UnityEngine.Camera::get_farClipPlane()
		void Register_UnityEngine_Camera_get_farClipPlane();
		Register_UnityEngine_Camera_get_farClipPlane();

		//System.Single UnityEngine.Camera::get_fieldOfView()
		void Register_UnityEngine_Camera_get_fieldOfView();
		Register_UnityEngine_Camera_get_fieldOfView();

		//System.Single UnityEngine.Camera::get_focalLength()
		void Register_UnityEngine_Camera_get_focalLength();
		Register_UnityEngine_Camera_get_focalLength();

		//System.Single UnityEngine.Camera::get_nearClipPlane()
		void Register_UnityEngine_Camera_get_nearClipPlane();
		Register_UnityEngine_Camera_get_nearClipPlane();

		//System.Single UnityEngine.Camera::get_orthographicSize()
		void Register_UnityEngine_Camera_get_orthographicSize();
		Register_UnityEngine_Camera_get_orthographicSize();

		//System.Single UnityEngine.Camera::get_stereoConvergence()
		void Register_UnityEngine_Camera_get_stereoConvergence();
		Register_UnityEngine_Camera_get_stereoConvergence();

		//System.Single UnityEngine.Camera::get_stereoSeparation()
		void Register_UnityEngine_Camera_get_stereoSeparation();
		Register_UnityEngine_Camera_get_stereoSeparation();

		//System.Single[] UnityEngine.Camera::GetLayerCullDistances()
		void Register_UnityEngine_Camera_GetLayerCullDistances();
		Register_UnityEngine_Camera_GetLayerCullDistances();

		//System.UInt64 UnityEngine.Camera::get_overrideSceneCullingMask()
		void Register_UnityEngine_Camera_get_overrideSceneCullingMask();
		Register_UnityEngine_Camera_get_overrideSceneCullingMask();

		//System.Void UnityEngine.Camera::AddCommandBufferAsyncImpl(UnityEngine.Rendering.CameraEvent,UnityEngine.Rendering.CommandBuffer,UnityEngine.Rendering.ComputeQueueType)
		void Register_UnityEngine_Camera_AddCommandBufferAsyncImpl();
		Register_UnityEngine_Camera_AddCommandBufferAsyncImpl();

		//System.Void UnityEngine.Camera::AddCommandBufferImpl(UnityEngine.Rendering.CameraEvent,UnityEngine.Rendering.CommandBuffer)
		void Register_UnityEngine_Camera_AddCommandBufferImpl();
		Register_UnityEngine_Camera_AddCommandBufferImpl();

		//System.Void UnityEngine.Camera::CalculateFrustumCornersInternal_Injected(UnityEngine.Rect&,System.Single,UnityEngine.Camera/MonoOrStereoscopicEye,UnityEngine.Vector3[])
		void Register_UnityEngine_Camera_CalculateFrustumCornersInternal_Injected();
		Register_UnityEngine_Camera_CalculateFrustumCornersInternal_Injected();

		//System.Void UnityEngine.Camera::CalculateObliqueMatrix_Injected(UnityEngine.Vector4&,UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Camera_CalculateObliqueMatrix_Injected();
		Register_UnityEngine_Camera_CalculateObliqueMatrix_Injected();

		//System.Void UnityEngine.Camera::CalculateProjectionMatrixFromPhysicalPropertiesInternal_Injected(UnityEngine.Matrix4x4&,System.Single,UnityEngine.Vector2&,UnityEngine.Vector2&,System.Single,System.Single,System.Single,UnityEngine.Camera/GateFitMode)
		void Register_UnityEngine_Camera_CalculateProjectionMatrixFromPhysicalPropertiesInternal_Injected();
		Register_UnityEngine_Camera_CalculateProjectionMatrixFromPhysicalPropertiesInternal_Injected();

		//System.Void UnityEngine.Camera::CopyFrom(UnityEngine.Camera)
		void Register_UnityEngine_Camera_CopyFrom();
		Register_UnityEngine_Camera_CopyFrom();

		//System.Void UnityEngine.Camera::CopyStereoDeviceProjectionMatrixToNonJittered(UnityEngine.Camera/StereoscopicEye)
		void Register_UnityEngine_Camera_CopyStereoDeviceProjectionMatrixToNonJittered();
		Register_UnityEngine_Camera_CopyStereoDeviceProjectionMatrixToNonJittered();

		//System.Void UnityEngine.Camera::GetGateFittedLensShift_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Camera_GetGateFittedLensShift_Injected();
		Register_UnityEngine_Camera_GetGateFittedLensShift_Injected();

		//System.Void UnityEngine.Camera::GetStereoNonJitteredProjectionMatrix_Injected(UnityEngine.Camera/StereoscopicEye,UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Camera_GetStereoNonJitteredProjectionMatrix_Injected();
		Register_UnityEngine_Camera_GetStereoNonJitteredProjectionMatrix_Injected();

		//System.Void UnityEngine.Camera::GetStereoProjectionMatrix_Injected(UnityEngine.Camera/StereoscopicEye,UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Camera_GetStereoProjectionMatrix_Injected();
		Register_UnityEngine_Camera_GetStereoProjectionMatrix_Injected();

		//System.Void UnityEngine.Camera::GetStereoViewMatrix_Injected(UnityEngine.Camera/StereoscopicEye,UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Camera_GetStereoViewMatrix_Injected();
		Register_UnityEngine_Camera_GetStereoViewMatrix_Injected();

		//System.Void UnityEngine.Camera::RemoveAllCommandBuffers()
		void Register_UnityEngine_Camera_RemoveAllCommandBuffers();
		Register_UnityEngine_Camera_RemoveAllCommandBuffers();

		//System.Void UnityEngine.Camera::RemoveCommandBufferImpl(UnityEngine.Rendering.CameraEvent,UnityEngine.Rendering.CommandBuffer)
		void Register_UnityEngine_Camera_RemoveCommandBufferImpl();
		Register_UnityEngine_Camera_RemoveCommandBufferImpl();

		//System.Void UnityEngine.Camera::RemoveCommandBuffers(UnityEngine.Rendering.CameraEvent)
		void Register_UnityEngine_Camera_RemoveCommandBuffers();
		Register_UnityEngine_Camera_RemoveCommandBuffers();

		//System.Void UnityEngine.Camera::Render()
		void Register_UnityEngine_Camera_Render();
		Register_UnityEngine_Camera_Render();

		//System.Void UnityEngine.Camera::RenderDontRestore()
		void Register_UnityEngine_Camera_RenderDontRestore();
		Register_UnityEngine_Camera_RenderDontRestore();

		//System.Void UnityEngine.Camera::RenderWithShader(UnityEngine.Shader,System.String)
		void Register_UnityEngine_Camera_RenderWithShader();
		Register_UnityEngine_Camera_RenderWithShader();

		//System.Void UnityEngine.Camera::Reset()
		void Register_UnityEngine_Camera_Reset();
		Register_UnityEngine_Camera_Reset();

		//System.Void UnityEngine.Camera::ResetAspect()
		void Register_UnityEngine_Camera_ResetAspect();
		Register_UnityEngine_Camera_ResetAspect();

		//System.Void UnityEngine.Camera::ResetCullingMatrix()
		void Register_UnityEngine_Camera_ResetCullingMatrix();
		Register_UnityEngine_Camera_ResetCullingMatrix();

		//System.Void UnityEngine.Camera::ResetProjectionMatrix()
		void Register_UnityEngine_Camera_ResetProjectionMatrix();
		Register_UnityEngine_Camera_ResetProjectionMatrix();

		//System.Void UnityEngine.Camera::ResetReplacementShader()
		void Register_UnityEngine_Camera_ResetReplacementShader();
		Register_UnityEngine_Camera_ResetReplacementShader();

		//System.Void UnityEngine.Camera::ResetStereoProjectionMatrices()
		void Register_UnityEngine_Camera_ResetStereoProjectionMatrices();
		Register_UnityEngine_Camera_ResetStereoProjectionMatrices();

		//System.Void UnityEngine.Camera::ResetStereoViewMatrices()
		void Register_UnityEngine_Camera_ResetStereoViewMatrices();
		Register_UnityEngine_Camera_ResetStereoViewMatrices();

		//System.Void UnityEngine.Camera::ResetTransparencySortSettings()
		void Register_UnityEngine_Camera_ResetTransparencySortSettings();
		Register_UnityEngine_Camera_ResetTransparencySortSettings();

		//System.Void UnityEngine.Camera::ResetWorldToCameraMatrix()
		void Register_UnityEngine_Camera_ResetWorldToCameraMatrix();
		Register_UnityEngine_Camera_ResetWorldToCameraMatrix();

		//System.Void UnityEngine.Camera::ScreenPointToRay_Injected(UnityEngine.Vector2&,UnityEngine.Camera/MonoOrStereoscopicEye,UnityEngine.Ray&)
		void Register_UnityEngine_Camera_ScreenPointToRay_Injected();
		Register_UnityEngine_Camera_ScreenPointToRay_Injected();

		//System.Void UnityEngine.Camera::ScreenToViewportPoint_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)
		void Register_UnityEngine_Camera_ScreenToViewportPoint_Injected();
		Register_UnityEngine_Camera_ScreenToViewportPoint_Injected();

		//System.Void UnityEngine.Camera::ScreenToWorldPoint_Injected(UnityEngine.Vector3&,UnityEngine.Camera/MonoOrStereoscopicEye,UnityEngine.Vector3&)
		void Register_UnityEngine_Camera_ScreenToWorldPoint_Injected();
		Register_UnityEngine_Camera_ScreenToWorldPoint_Injected();

		//System.Void UnityEngine.Camera::SetLayerCullDistances(System.Single[])
		void Register_UnityEngine_Camera_SetLayerCullDistances();
		Register_UnityEngine_Camera_SetLayerCullDistances();

		//System.Void UnityEngine.Camera::SetReplacementShader(UnityEngine.Shader,System.String)
		void Register_UnityEngine_Camera_SetReplacementShader();
		Register_UnityEngine_Camera_SetReplacementShader();

		//System.Void UnityEngine.Camera::SetStereoProjectionMatrix_Injected(UnityEngine.Camera/StereoscopicEye,UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Camera_SetStereoProjectionMatrix_Injected();
		Register_UnityEngine_Camera_SetStereoProjectionMatrix_Injected();

		//System.Void UnityEngine.Camera::SetStereoViewMatrix_Injected(UnityEngine.Camera/StereoscopicEye,UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Camera_SetStereoViewMatrix_Injected();
		Register_UnityEngine_Camera_SetStereoViewMatrix_Injected();

		//System.Void UnityEngine.Camera::SetTargetBuffersImpl_Injected(UnityEngine.RenderBuffer&,UnityEngine.RenderBuffer&)
		void Register_UnityEngine_Camera_SetTargetBuffersImpl_Injected();
		Register_UnityEngine_Camera_SetTargetBuffersImpl_Injected();

		//System.Void UnityEngine.Camera::SetTargetBuffersMRTImpl_Injected(UnityEngine.RenderBuffer[],UnityEngine.RenderBuffer&)
		void Register_UnityEngine_Camera_SetTargetBuffersMRTImpl_Injected();
		Register_UnityEngine_Camera_SetTargetBuffersMRTImpl_Injected();

		//System.Void UnityEngine.Camera::SetupCurrent(UnityEngine.Camera)
		void Register_UnityEngine_Camera_SetupCurrent();
		Register_UnityEngine_Camera_SetupCurrent();

		//System.Void UnityEngine.Camera::ViewportPointToRay_Injected(UnityEngine.Vector2&,UnityEngine.Camera/MonoOrStereoscopicEye,UnityEngine.Ray&)
		void Register_UnityEngine_Camera_ViewportPointToRay_Injected();
		Register_UnityEngine_Camera_ViewportPointToRay_Injected();

		//System.Void UnityEngine.Camera::ViewportToScreenPoint_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)
		void Register_UnityEngine_Camera_ViewportToScreenPoint_Injected();
		Register_UnityEngine_Camera_ViewportToScreenPoint_Injected();

		//System.Void UnityEngine.Camera::ViewportToWorldPoint_Injected(UnityEngine.Vector3&,UnityEngine.Camera/MonoOrStereoscopicEye,UnityEngine.Vector3&)
		void Register_UnityEngine_Camera_ViewportToWorldPoint_Injected();
		Register_UnityEngine_Camera_ViewportToWorldPoint_Injected();

		//System.Void UnityEngine.Camera::WorldToScreenPoint_Injected(UnityEngine.Vector3&,UnityEngine.Camera/MonoOrStereoscopicEye,UnityEngine.Vector3&)
		void Register_UnityEngine_Camera_WorldToScreenPoint_Injected();
		Register_UnityEngine_Camera_WorldToScreenPoint_Injected();

		//System.Void UnityEngine.Camera::WorldToViewportPoint_Injected(UnityEngine.Vector3&,UnityEngine.Camera/MonoOrStereoscopicEye,UnityEngine.Vector3&)
		void Register_UnityEngine_Camera_WorldToViewportPoint_Injected();
		Register_UnityEngine_Camera_WorldToViewportPoint_Injected();

		//System.Void UnityEngine.Camera::get_backgroundColor_Injected(UnityEngine.Color&)
		void Register_UnityEngine_Camera_get_backgroundColor_Injected();
		Register_UnityEngine_Camera_get_backgroundColor_Injected();

		//System.Void UnityEngine.Camera::get_cameraToWorldMatrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Camera_get_cameraToWorldMatrix_Injected();
		Register_UnityEngine_Camera_get_cameraToWorldMatrix_Injected();

		//System.Void UnityEngine.Camera::get_cullingMatrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Camera_get_cullingMatrix_Injected();
		Register_UnityEngine_Camera_get_cullingMatrix_Injected();

		//System.Void UnityEngine.Camera::get_lensShift_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Camera_get_lensShift_Injected();
		Register_UnityEngine_Camera_get_lensShift_Injected();

		//System.Void UnityEngine.Camera::get_nonJitteredProjectionMatrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Camera_get_nonJitteredProjectionMatrix_Injected();
		Register_UnityEngine_Camera_get_nonJitteredProjectionMatrix_Injected();

		//System.Void UnityEngine.Camera::get_pixelRect_Injected(UnityEngine.Rect&)
		void Register_UnityEngine_Camera_get_pixelRect_Injected();
		Register_UnityEngine_Camera_get_pixelRect_Injected();

		//System.Void UnityEngine.Camera::get_previousViewProjectionMatrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Camera_get_previousViewProjectionMatrix_Injected();
		Register_UnityEngine_Camera_get_previousViewProjectionMatrix_Injected();

		//System.Void UnityEngine.Camera::get_projectionMatrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Camera_get_projectionMatrix_Injected();
		Register_UnityEngine_Camera_get_projectionMatrix_Injected();

		//System.Void UnityEngine.Camera::get_rect_Injected(UnityEngine.Rect&)
		void Register_UnityEngine_Camera_get_rect_Injected();
		Register_UnityEngine_Camera_get_rect_Injected();

		//System.Void UnityEngine.Camera::get_scene_Injected(UnityEngine.SceneManagement.Scene&)
		void Register_UnityEngine_Camera_get_scene_Injected();
		Register_UnityEngine_Camera_get_scene_Injected();

		//System.Void UnityEngine.Camera::get_sensorSize_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Camera_get_sensorSize_Injected();
		Register_UnityEngine_Camera_get_sensorSize_Injected();

		//System.Void UnityEngine.Camera::get_transparencySortAxis_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Camera_get_transparencySortAxis_Injected();
		Register_UnityEngine_Camera_get_transparencySortAxis_Injected();

		//System.Void UnityEngine.Camera::get_velocity_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Camera_get_velocity_Injected();
		Register_UnityEngine_Camera_get_velocity_Injected();

		//System.Void UnityEngine.Camera::get_worldToCameraMatrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Camera_get_worldToCameraMatrix_Injected();
		Register_UnityEngine_Camera_get_worldToCameraMatrix_Injected();

		//System.Void UnityEngine.Camera::set_allowDynamicResolution(System.Boolean)
		void Register_UnityEngine_Camera_set_allowDynamicResolution();
		Register_UnityEngine_Camera_set_allowDynamicResolution();

		//System.Void UnityEngine.Camera::set_allowHDR(System.Boolean)
		void Register_UnityEngine_Camera_set_allowHDR();
		Register_UnityEngine_Camera_set_allowHDR();

		//System.Void UnityEngine.Camera::set_allowMSAA(System.Boolean)
		void Register_UnityEngine_Camera_set_allowMSAA();
		Register_UnityEngine_Camera_set_allowMSAA();

		//System.Void UnityEngine.Camera::set_aspect(System.Single)
		void Register_UnityEngine_Camera_set_aspect();
		Register_UnityEngine_Camera_set_aspect();

		//System.Void UnityEngine.Camera::set_backgroundColor_Injected(UnityEngine.Color&)
		void Register_UnityEngine_Camera_set_backgroundColor_Injected();
		Register_UnityEngine_Camera_set_backgroundColor_Injected();

		//System.Void UnityEngine.Camera::set_cameraType(UnityEngine.CameraType)
		void Register_UnityEngine_Camera_set_cameraType();
		Register_UnityEngine_Camera_set_cameraType();

		//System.Void UnityEngine.Camera::set_clearFlags(UnityEngine.CameraClearFlags)
		void Register_UnityEngine_Camera_set_clearFlags();
		Register_UnityEngine_Camera_set_clearFlags();

		//System.Void UnityEngine.Camera::set_clearStencilAfterLightingPass(System.Boolean)
		void Register_UnityEngine_Camera_set_clearStencilAfterLightingPass();
		Register_UnityEngine_Camera_set_clearStencilAfterLightingPass();

		//System.Void UnityEngine.Camera::set_cullingMask(System.Int32)
		void Register_UnityEngine_Camera_set_cullingMask();
		Register_UnityEngine_Camera_set_cullingMask();

		//System.Void UnityEngine.Camera::set_cullingMatrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Camera_set_cullingMatrix_Injected();
		Register_UnityEngine_Camera_set_cullingMatrix_Injected();

		//System.Void UnityEngine.Camera::set_depth(System.Single)
		void Register_UnityEngine_Camera_set_depth();
		Register_UnityEngine_Camera_set_depth();

		//System.Void UnityEngine.Camera::set_depthTextureMode(UnityEngine.DepthTextureMode)
		void Register_UnityEngine_Camera_set_depthTextureMode();
		Register_UnityEngine_Camera_set_depthTextureMode();

		//System.Void UnityEngine.Camera::set_eventMask(System.Int32)
		void Register_UnityEngine_Camera_set_eventMask();
		Register_UnityEngine_Camera_set_eventMask();

		//System.Void UnityEngine.Camera::set_farClipPlane(System.Single)
		void Register_UnityEngine_Camera_set_farClipPlane();
		Register_UnityEngine_Camera_set_farClipPlane();

		//System.Void UnityEngine.Camera::set_fieldOfView(System.Single)
		void Register_UnityEngine_Camera_set_fieldOfView();
		Register_UnityEngine_Camera_set_fieldOfView();

		//System.Void UnityEngine.Camera::set_focalLength(System.Single)
		void Register_UnityEngine_Camera_set_focalLength();
		Register_UnityEngine_Camera_set_focalLength();

		//System.Void UnityEngine.Camera::set_forceIntoRenderTexture(System.Boolean)
		void Register_UnityEngine_Camera_set_forceIntoRenderTexture();
		Register_UnityEngine_Camera_set_forceIntoRenderTexture();

		//System.Void UnityEngine.Camera::set_gateFit(UnityEngine.Camera/GateFitMode)
		void Register_UnityEngine_Camera_set_gateFit();
		Register_UnityEngine_Camera_set_gateFit();

		//System.Void UnityEngine.Camera::set_layerCullSpherical(System.Boolean)
		void Register_UnityEngine_Camera_set_layerCullSpherical();
		Register_UnityEngine_Camera_set_layerCullSpherical();

		//System.Void UnityEngine.Camera::set_lensShift_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Camera_set_lensShift_Injected();
		Register_UnityEngine_Camera_set_lensShift_Injected();

		//System.Void UnityEngine.Camera::set_nearClipPlane(System.Single)
		void Register_UnityEngine_Camera_set_nearClipPlane();
		Register_UnityEngine_Camera_set_nearClipPlane();

		//System.Void UnityEngine.Camera::set_nonJitteredProjectionMatrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Camera_set_nonJitteredProjectionMatrix_Injected();
		Register_UnityEngine_Camera_set_nonJitteredProjectionMatrix_Injected();

		//System.Void UnityEngine.Camera::set_opaqueSortMode(UnityEngine.Rendering.OpaqueSortMode)
		void Register_UnityEngine_Camera_set_opaqueSortMode();
		Register_UnityEngine_Camera_set_opaqueSortMode();

		//System.Void UnityEngine.Camera::set_orthographic(System.Boolean)
		void Register_UnityEngine_Camera_set_orthographic();
		Register_UnityEngine_Camera_set_orthographic();

		//System.Void UnityEngine.Camera::set_orthographicSize(System.Single)
		void Register_UnityEngine_Camera_set_orthographicSize();
		Register_UnityEngine_Camera_set_orthographicSize();

		//System.Void UnityEngine.Camera::set_overrideSceneCullingMask(System.UInt64)
		void Register_UnityEngine_Camera_set_overrideSceneCullingMask();
		Register_UnityEngine_Camera_set_overrideSceneCullingMask();

		//System.Void UnityEngine.Camera::set_pixelRect_Injected(UnityEngine.Rect&)
		void Register_UnityEngine_Camera_set_pixelRect_Injected();
		Register_UnityEngine_Camera_set_pixelRect_Injected();

		//System.Void UnityEngine.Camera::set_projectionMatrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Camera_set_projectionMatrix_Injected();
		Register_UnityEngine_Camera_set_projectionMatrix_Injected();

		//System.Void UnityEngine.Camera::set_rect_Injected(UnityEngine.Rect&)
		void Register_UnityEngine_Camera_set_rect_Injected();
		Register_UnityEngine_Camera_set_rect_Injected();

		//System.Void UnityEngine.Camera::set_renderingPath(UnityEngine.RenderingPath)
		void Register_UnityEngine_Camera_set_renderingPath();
		Register_UnityEngine_Camera_set_renderingPath();

		//System.Void UnityEngine.Camera::set_scene_Injected(UnityEngine.SceneManagement.Scene&)
		void Register_UnityEngine_Camera_set_scene_Injected();
		Register_UnityEngine_Camera_set_scene_Injected();

		//System.Void UnityEngine.Camera::set_sensorSize_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Camera_set_sensorSize_Injected();
		Register_UnityEngine_Camera_set_sensorSize_Injected();

		//System.Void UnityEngine.Camera::set_stereoConvergence(System.Single)
		void Register_UnityEngine_Camera_set_stereoConvergence();
		Register_UnityEngine_Camera_set_stereoConvergence();

		//System.Void UnityEngine.Camera::set_stereoSeparation(System.Single)
		void Register_UnityEngine_Camera_set_stereoSeparation();
		Register_UnityEngine_Camera_set_stereoSeparation();

		//System.Void UnityEngine.Camera::set_stereoTargetEye(UnityEngine.StereoTargetEyeMask)
		void Register_UnityEngine_Camera_set_stereoTargetEye();
		Register_UnityEngine_Camera_set_stereoTargetEye();

		//System.Void UnityEngine.Camera::set_targetDisplay(System.Int32)
		void Register_UnityEngine_Camera_set_targetDisplay();
		Register_UnityEngine_Camera_set_targetDisplay();

		//System.Void UnityEngine.Camera::set_targetTexture(UnityEngine.RenderTexture)
		void Register_UnityEngine_Camera_set_targetTexture();
		Register_UnityEngine_Camera_set_targetTexture();

		//System.Void UnityEngine.Camera::set_transparencySortAxis_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Camera_set_transparencySortAxis_Injected();
		Register_UnityEngine_Camera_set_transparencySortAxis_Injected();

		//System.Void UnityEngine.Camera::set_transparencySortMode(UnityEngine.TransparencySortMode)
		void Register_UnityEngine_Camera_set_transparencySortMode();
		Register_UnityEngine_Camera_set_transparencySortMode();

		//System.Void UnityEngine.Camera::set_useJitteredProjectionMatrixForTransparentRendering(System.Boolean)
		void Register_UnityEngine_Camera_set_useJitteredProjectionMatrixForTransparentRendering();
		Register_UnityEngine_Camera_set_useJitteredProjectionMatrixForTransparentRendering();

		//System.Void UnityEngine.Camera::set_useOcclusionCulling(System.Boolean)
		void Register_UnityEngine_Camera_set_useOcclusionCulling();
		Register_UnityEngine_Camera_set_useOcclusionCulling();

		//System.Void UnityEngine.Camera::set_usePhysicalProperties(System.Boolean)
		void Register_UnityEngine_Camera_set_usePhysicalProperties();
		Register_UnityEngine_Camera_set_usePhysicalProperties();

		//System.Void UnityEngine.Camera::set_worldToCameraMatrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Camera_set_worldToCameraMatrix_Injected();
		Register_UnityEngine_Camera_set_worldToCameraMatrix_Injected();

		//UnityEngine.Camera UnityEngine.Camera::get_current()
		void Register_UnityEngine_Camera_get_current();
		Register_UnityEngine_Camera_get_current();

		//UnityEngine.Camera UnityEngine.Camera::get_main()
		void Register_UnityEngine_Camera_get_main();
		Register_UnityEngine_Camera_get_main();

		//UnityEngine.Camera/GateFitMode UnityEngine.Camera::get_gateFit()
		void Register_UnityEngine_Camera_get_gateFit();
		Register_UnityEngine_Camera_get_gateFit();

		//UnityEngine.Camera/MonoOrStereoscopicEye UnityEngine.Camera::get_stereoActiveEye()
		void Register_UnityEngine_Camera_get_stereoActiveEye();
		Register_UnityEngine_Camera_get_stereoActiveEye();

		//UnityEngine.CameraClearFlags UnityEngine.Camera::get_clearFlags()
		void Register_UnityEngine_Camera_get_clearFlags();
		Register_UnityEngine_Camera_get_clearFlags();

		//UnityEngine.CameraType UnityEngine.Camera::get_cameraType()
		void Register_UnityEngine_Camera_get_cameraType();
		Register_UnityEngine_Camera_get_cameraType();

		//UnityEngine.DepthTextureMode UnityEngine.Camera::get_depthTextureMode()
		void Register_UnityEngine_Camera_get_depthTextureMode();
		Register_UnityEngine_Camera_get_depthTextureMode();

		//UnityEngine.RenderTexture UnityEngine.Camera::get_activeTexture()
		void Register_UnityEngine_Camera_get_activeTexture();
		Register_UnityEngine_Camera_get_activeTexture();

		//UnityEngine.RenderTexture UnityEngine.Camera::get_targetTexture()
		void Register_UnityEngine_Camera_get_targetTexture();
		Register_UnityEngine_Camera_get_targetTexture();

		//UnityEngine.Rendering.CommandBuffer[] UnityEngine.Camera::GetCommandBuffers(UnityEngine.Rendering.CameraEvent)
		void Register_UnityEngine_Camera_GetCommandBuffers();
		Register_UnityEngine_Camera_GetCommandBuffers();

		//UnityEngine.Rendering.OpaqueSortMode UnityEngine.Camera::get_opaqueSortMode()
		void Register_UnityEngine_Camera_get_opaqueSortMode();
		Register_UnityEngine_Camera_get_opaqueSortMode();

		//UnityEngine.RenderingPath UnityEngine.Camera::get_actualRenderingPath()
		void Register_UnityEngine_Camera_get_actualRenderingPath();
		Register_UnityEngine_Camera_get_actualRenderingPath();

		//UnityEngine.RenderingPath UnityEngine.Camera::get_renderingPath()
		void Register_UnityEngine_Camera_get_renderingPath();
		Register_UnityEngine_Camera_get_renderingPath();

		//UnityEngine.StereoTargetEyeMask UnityEngine.Camera::get_stereoTargetEye()
		void Register_UnityEngine_Camera_get_stereoTargetEye();
		Register_UnityEngine_Camera_get_stereoTargetEye();

		//UnityEngine.TransparencySortMode UnityEngine.Camera::get_transparencySortMode()
		void Register_UnityEngine_Camera_get_transparencySortMode();
		Register_UnityEngine_Camera_get_transparencySortMode();

	//End Registrations for type : UnityEngine.Camera

	//Start Registrations for type : UnityEngine.CameraRaycastHelper

		//UnityEngine.GameObject UnityEngine.CameraRaycastHelper::RaycastTry2D_Injected(UnityEngine.Camera,UnityEngine.Ray&,System.Single,System.Int32)
		void Register_UnityEngine_CameraRaycastHelper_RaycastTry2D_Injected();
		Register_UnityEngine_CameraRaycastHelper_RaycastTry2D_Injected();

		//UnityEngine.GameObject UnityEngine.CameraRaycastHelper::RaycastTry_Injected(UnityEngine.Camera,UnityEngine.Ray&,System.Single,System.Int32)
		void Register_UnityEngine_CameraRaycastHelper_RaycastTry_Injected();
		Register_UnityEngine_CameraRaycastHelper_RaycastTry_Injected();

	//End Registrations for type : UnityEngine.CameraRaycastHelper

	//Start Registrations for type : UnityEngine.Canvas

		//System.Boolean UnityEngine.Canvas::get_isRootCanvas()
		void Register_UnityEngine_Canvas_get_isRootCanvas();
		Register_UnityEngine_Canvas_get_isRootCanvas();

		//System.Boolean UnityEngine.Canvas::get_overrideSorting()
		void Register_UnityEngine_Canvas_get_overrideSorting();
		Register_UnityEngine_Canvas_get_overrideSorting();

		//System.Boolean UnityEngine.Canvas::get_pixelPerfect()
		void Register_UnityEngine_Canvas_get_pixelPerfect();
		Register_UnityEngine_Canvas_get_pixelPerfect();

		//System.Int32 UnityEngine.Canvas::get_renderOrder()
		void Register_UnityEngine_Canvas_get_renderOrder();
		Register_UnityEngine_Canvas_get_renderOrder();

		//System.Int32 UnityEngine.Canvas::get_sortingLayerID()
		void Register_UnityEngine_Canvas_get_sortingLayerID();
		Register_UnityEngine_Canvas_get_sortingLayerID();

		//System.Int32 UnityEngine.Canvas::get_sortingOrder()
		void Register_UnityEngine_Canvas_get_sortingOrder();
		Register_UnityEngine_Canvas_get_sortingOrder();

		//System.Int32 UnityEngine.Canvas::get_targetDisplay()
		void Register_UnityEngine_Canvas_get_targetDisplay();
		Register_UnityEngine_Canvas_get_targetDisplay();

		//System.Single UnityEngine.Canvas::get_referencePixelsPerUnit()
		void Register_UnityEngine_Canvas_get_referencePixelsPerUnit();
		Register_UnityEngine_Canvas_get_referencePixelsPerUnit();

		//System.Single UnityEngine.Canvas::get_scaleFactor()
		void Register_UnityEngine_Canvas_get_scaleFactor();
		Register_UnityEngine_Canvas_get_scaleFactor();

		//System.Void UnityEngine.Canvas::get_renderingDisplaySize_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Canvas_get_renderingDisplaySize_Injected();
		Register_UnityEngine_Canvas_get_renderingDisplaySize_Injected();

		//System.Void UnityEngine.Canvas::set_additionalShaderChannels(UnityEngine.AdditionalCanvasShaderChannels)
		void Register_UnityEngine_Canvas_set_additionalShaderChannels();
		Register_UnityEngine_Canvas_set_additionalShaderChannels();

		//System.Void UnityEngine.Canvas::set_overrideSorting(System.Boolean)
		void Register_UnityEngine_Canvas_set_overrideSorting();
		Register_UnityEngine_Canvas_set_overrideSorting();

		//System.Void UnityEngine.Canvas::set_pixelPerfect(System.Boolean)
		void Register_UnityEngine_Canvas_set_pixelPerfect();
		Register_UnityEngine_Canvas_set_pixelPerfect();

		//System.Void UnityEngine.Canvas::set_planeDistance(System.Single)
		void Register_UnityEngine_Canvas_set_planeDistance();
		Register_UnityEngine_Canvas_set_planeDistance();

		//System.Void UnityEngine.Canvas::set_referencePixelsPerUnit(System.Single)
		void Register_UnityEngine_Canvas_set_referencePixelsPerUnit();
		Register_UnityEngine_Canvas_set_referencePixelsPerUnit();

		//System.Void UnityEngine.Canvas::set_renderMode(UnityEngine.RenderMode)
		void Register_UnityEngine_Canvas_set_renderMode();
		Register_UnityEngine_Canvas_set_renderMode();

		//System.Void UnityEngine.Canvas::set_scaleFactor(System.Single)
		void Register_UnityEngine_Canvas_set_scaleFactor();
		Register_UnityEngine_Canvas_set_scaleFactor();

		//System.Void UnityEngine.Canvas::set_sortingLayerID(System.Int32)
		void Register_UnityEngine_Canvas_set_sortingLayerID();
		Register_UnityEngine_Canvas_set_sortingLayerID();

		//System.Void UnityEngine.Canvas::set_sortingOrder(System.Int32)
		void Register_UnityEngine_Canvas_set_sortingOrder();
		Register_UnityEngine_Canvas_set_sortingOrder();

		//UnityEngine.AdditionalCanvasShaderChannels UnityEngine.Canvas::get_additionalShaderChannels()
		void Register_UnityEngine_Canvas_get_additionalShaderChannels();
		Register_UnityEngine_Canvas_get_additionalShaderChannels();

		//UnityEngine.Camera UnityEngine.Canvas::get_worldCamera()
		void Register_UnityEngine_Canvas_get_worldCamera();
		Register_UnityEngine_Canvas_get_worldCamera();

		//UnityEngine.Canvas UnityEngine.Canvas::get_rootCanvas()
		void Register_UnityEngine_Canvas_get_rootCanvas();
		Register_UnityEngine_Canvas_get_rootCanvas();

		//UnityEngine.Material UnityEngine.Canvas::GetDefaultCanvasMaterial()
		void Register_UnityEngine_Canvas_GetDefaultCanvasMaterial();
		Register_UnityEngine_Canvas_GetDefaultCanvasMaterial();

		//UnityEngine.Material UnityEngine.Canvas::GetETC1SupportedCanvasMaterial()
		void Register_UnityEngine_Canvas_GetETC1SupportedCanvasMaterial();
		Register_UnityEngine_Canvas_GetETC1SupportedCanvasMaterial();

		//UnityEngine.RenderMode UnityEngine.Canvas::get_renderMode()
		void Register_UnityEngine_Canvas_get_renderMode();
		Register_UnityEngine_Canvas_get_renderMode();

	//End Registrations for type : UnityEngine.Canvas

	//Start Registrations for type : UnityEngine.CanvasGroup

		//System.Boolean UnityEngine.CanvasGroup::get_blocksRaycasts()
		void Register_UnityEngine_CanvasGroup_get_blocksRaycasts();
		Register_UnityEngine_CanvasGroup_get_blocksRaycasts();

		//System.Boolean UnityEngine.CanvasGroup::get_ignoreParentGroups()
		void Register_UnityEngine_CanvasGroup_get_ignoreParentGroups();
		Register_UnityEngine_CanvasGroup_get_ignoreParentGroups();

		//System.Boolean UnityEngine.CanvasGroup::get_interactable()
		void Register_UnityEngine_CanvasGroup_get_interactable();
		Register_UnityEngine_CanvasGroup_get_interactable();

		//System.Single UnityEngine.CanvasGroup::get_alpha()
		void Register_UnityEngine_CanvasGroup_get_alpha();
		Register_UnityEngine_CanvasGroup_get_alpha();

		//System.Void UnityEngine.CanvasGroup::set_alpha(System.Single)
		void Register_UnityEngine_CanvasGroup_set_alpha();
		Register_UnityEngine_CanvasGroup_set_alpha();

		//System.Void UnityEngine.CanvasGroup::set_blocksRaycasts(System.Boolean)
		void Register_UnityEngine_CanvasGroup_set_blocksRaycasts();
		Register_UnityEngine_CanvasGroup_set_blocksRaycasts();

	//End Registrations for type : UnityEngine.CanvasGroup

	//Start Registrations for type : UnityEngine.CanvasRenderer

		//System.Boolean UnityEngine.CanvasRenderer::get_cull()
		void Register_UnityEngine_CanvasRenderer_get_cull();
		Register_UnityEngine_CanvasRenderer_get_cull();

		//System.Boolean UnityEngine.CanvasRenderer::get_hasMoved()
		void Register_UnityEngine_CanvasRenderer_get_hasMoved();
		Register_UnityEngine_CanvasRenderer_get_hasMoved();

		//System.Int32 UnityEngine.CanvasRenderer::get_absoluteDepth()
		void Register_UnityEngine_CanvasRenderer_get_absoluteDepth();
		Register_UnityEngine_CanvasRenderer_get_absoluteDepth();

		//System.Int32 UnityEngine.CanvasRenderer::get_materialCount()
		void Register_UnityEngine_CanvasRenderer_get_materialCount();
		Register_UnityEngine_CanvasRenderer_get_materialCount();

		//System.Void UnityEngine.CanvasRenderer::Clear()
		void Register_UnityEngine_CanvasRenderer_Clear();
		Register_UnityEngine_CanvasRenderer_Clear();

		//System.Void UnityEngine.CanvasRenderer::CreateUIVertexStreamInternal(System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object)
		void Register_UnityEngine_CanvasRenderer_CreateUIVertexStreamInternal();
		Register_UnityEngine_CanvasRenderer_CreateUIVertexStreamInternal();

		//System.Void UnityEngine.CanvasRenderer::DisableRectClipping()
		void Register_UnityEngine_CanvasRenderer_DisableRectClipping();
		Register_UnityEngine_CanvasRenderer_DisableRectClipping();

		//System.Void UnityEngine.CanvasRenderer::EnableRectClipping_Injected(UnityEngine.Rect&)
		void Register_UnityEngine_CanvasRenderer_EnableRectClipping_Injected();
		Register_UnityEngine_CanvasRenderer_EnableRectClipping_Injected();

		//System.Void UnityEngine.CanvasRenderer::GetColor_Injected(UnityEngine.Color&)
		void Register_UnityEngine_CanvasRenderer_GetColor_Injected();
		Register_UnityEngine_CanvasRenderer_GetColor_Injected();

		//System.Void UnityEngine.CanvasRenderer::SetAlphaTexture(UnityEngine.Texture)
		void Register_UnityEngine_CanvasRenderer_SetAlphaTexture();
		Register_UnityEngine_CanvasRenderer_SetAlphaTexture();

		//System.Void UnityEngine.CanvasRenderer::SetColor_Injected(UnityEngine.Color&)
		void Register_UnityEngine_CanvasRenderer_SetColor_Injected();
		Register_UnityEngine_CanvasRenderer_SetColor_Injected();

		//System.Void UnityEngine.CanvasRenderer::SetMaterial(UnityEngine.Material,System.Int32)
		void Register_UnityEngine_CanvasRenderer_SetMaterial();
		Register_UnityEngine_CanvasRenderer_SetMaterial();

		//System.Void UnityEngine.CanvasRenderer::SetMesh(UnityEngine.Mesh)
		void Register_UnityEngine_CanvasRenderer_SetMesh();
		Register_UnityEngine_CanvasRenderer_SetMesh();

		//System.Void UnityEngine.CanvasRenderer::SetPopMaterial(UnityEngine.Material,System.Int32)
		void Register_UnityEngine_CanvasRenderer_SetPopMaterial();
		Register_UnityEngine_CanvasRenderer_SetPopMaterial();

		//System.Void UnityEngine.CanvasRenderer::SetTexture(UnityEngine.Texture)
		void Register_UnityEngine_CanvasRenderer_SetTexture();
		Register_UnityEngine_CanvasRenderer_SetTexture();

		//System.Void UnityEngine.CanvasRenderer::SplitIndicesStreamsInternal(System.Object,System.Object)
		void Register_UnityEngine_CanvasRenderer_SplitIndicesStreamsInternal();
		Register_UnityEngine_CanvasRenderer_SplitIndicesStreamsInternal();

		//System.Void UnityEngine.CanvasRenderer::SplitUIVertexStreamsInternal(System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object)
		void Register_UnityEngine_CanvasRenderer_SplitUIVertexStreamsInternal();
		Register_UnityEngine_CanvasRenderer_SplitUIVertexStreamsInternal();

		//System.Void UnityEngine.CanvasRenderer::set_clippingSoftness_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_CanvasRenderer_set_clippingSoftness_Injected();
		Register_UnityEngine_CanvasRenderer_set_clippingSoftness_Injected();

		//System.Void UnityEngine.CanvasRenderer::set_cull(System.Boolean)
		void Register_UnityEngine_CanvasRenderer_set_cull();
		Register_UnityEngine_CanvasRenderer_set_cull();

		//System.Void UnityEngine.CanvasRenderer::set_hasPopInstruction(System.Boolean)
		void Register_UnityEngine_CanvasRenderer_set_hasPopInstruction();
		Register_UnityEngine_CanvasRenderer_set_hasPopInstruction();

		//System.Void UnityEngine.CanvasRenderer::set_materialCount(System.Int32)
		void Register_UnityEngine_CanvasRenderer_set_materialCount();
		Register_UnityEngine_CanvasRenderer_set_materialCount();

		//System.Void UnityEngine.CanvasRenderer::set_popMaterialCount(System.Int32)
		void Register_UnityEngine_CanvasRenderer_set_popMaterialCount();
		Register_UnityEngine_CanvasRenderer_set_popMaterialCount();

		//UnityEngine.Material UnityEngine.CanvasRenderer::GetMaterial(System.Int32)
		void Register_UnityEngine_CanvasRenderer_GetMaterial();
		Register_UnityEngine_CanvasRenderer_GetMaterial();

	//End Registrations for type : UnityEngine.CanvasRenderer

	//Start Registrations for type : UnityEngine.CharacterController

		//System.Boolean UnityEngine.CharacterController::get_isGrounded()
		void Register_UnityEngine_CharacterController_get_isGrounded();
		Register_UnityEngine_CharacterController_get_isGrounded();

		//UnityEngine.CollisionFlags UnityEngine.CharacterController::Move_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_CharacterController_Move_Injected();
		Register_UnityEngine_CharacterController_Move_Injected();

	//End Registrations for type : UnityEngine.CharacterController

	//Start Registrations for type : UnityEngine.CircleCollider2D

		//System.Void UnityEngine.CircleCollider2D::set_radius(System.Single)
		void Register_UnityEngine_CircleCollider2D_set_radius();
		Register_UnityEngine_CircleCollider2D_set_radius();

	//End Registrations for type : UnityEngine.CircleCollider2D

	//Start Registrations for type : UnityEngine.Collider

		//System.Boolean UnityEngine.Collider::get_enabled()
		void Register_UnityEngine_Collider_get_enabled();
		Register_UnityEngine_Collider_get_enabled();

		//System.Boolean UnityEngine.Collider::get_isTrigger()
		void Register_UnityEngine_Collider_get_isTrigger();
		Register_UnityEngine_Collider_get_isTrigger();

		//System.Single UnityEngine.Collider::get_contactOffset()
		void Register_UnityEngine_Collider_get_contactOffset();
		Register_UnityEngine_Collider_get_contactOffset();

		//System.Void UnityEngine.Collider::ClosestPoint_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)
		void Register_UnityEngine_Collider_ClosestPoint_Injected();
		Register_UnityEngine_Collider_ClosestPoint_Injected();

		//System.Void UnityEngine.Collider::Internal_ClosestPointOnBounds_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single&)
		void Register_UnityEngine_Collider_Internal_ClosestPointOnBounds_Injected();
		Register_UnityEngine_Collider_Internal_ClosestPointOnBounds_Injected();

		//System.Void UnityEngine.Collider::Raycast_Injected(UnityEngine.Ray&,System.Single,System.Boolean&,UnityEngine.RaycastHit&)
		void Register_UnityEngine_Collider_Raycast_Injected();
		Register_UnityEngine_Collider_Raycast_Injected();

		//System.Void UnityEngine.Collider::get_bounds_Injected(UnityEngine.Bounds&)
		void Register_UnityEngine_Collider_get_bounds_Injected();
		Register_UnityEngine_Collider_get_bounds_Injected();

		//System.Void UnityEngine.Collider::set_contactOffset(System.Single)
		void Register_UnityEngine_Collider_set_contactOffset();
		Register_UnityEngine_Collider_set_contactOffset();

		//System.Void UnityEngine.Collider::set_enabled(System.Boolean)
		void Register_UnityEngine_Collider_set_enabled();
		Register_UnityEngine_Collider_set_enabled();

		//System.Void UnityEngine.Collider::set_isTrigger(System.Boolean)
		void Register_UnityEngine_Collider_set_isTrigger();
		Register_UnityEngine_Collider_set_isTrigger();

		//System.Void UnityEngine.Collider::set_material(UnityEngine.PhysicMaterial)
		void Register_UnityEngine_Collider_set_material();
		Register_UnityEngine_Collider_set_material();

		//System.Void UnityEngine.Collider::set_sharedMaterial(UnityEngine.PhysicMaterial)
		void Register_UnityEngine_Collider_set_sharedMaterial();
		Register_UnityEngine_Collider_set_sharedMaterial();

		//UnityEngine.PhysicMaterial UnityEngine.Collider::get_material()
		void Register_UnityEngine_Collider_get_material();
		Register_UnityEngine_Collider_get_material();

		//UnityEngine.PhysicMaterial UnityEngine.Collider::get_sharedMaterial()
		void Register_UnityEngine_Collider_get_sharedMaterial();
		Register_UnityEngine_Collider_get_sharedMaterial();

		//UnityEngine.Rigidbody UnityEngine.Collider::get_attachedRigidbody()
		void Register_UnityEngine_Collider_get_attachedRigidbody();
		Register_UnityEngine_Collider_get_attachedRigidbody();

	//End Registrations for type : UnityEngine.Collider

	//Start Registrations for type : UnityEngine.Collider2D

		//System.Void UnityEngine.Collider2D::get_offset_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Collider2D_get_offset_Injected();
		Register_UnityEngine_Collider2D_get_offset_Injected();

		//System.Void UnityEngine.Collider2D::set_isTrigger(System.Boolean)
		void Register_UnityEngine_Collider2D_set_isTrigger();
		Register_UnityEngine_Collider2D_set_isTrigger();

		//System.Void UnityEngine.Collider2D::set_offset_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Collider2D_set_offset_Injected();
		Register_UnityEngine_Collider2D_set_offset_Injected();

	//End Registrations for type : UnityEngine.Collider2D

	//Start Registrations for type : UnityEngine.Component

		//System.Void UnityEngine.Component::BroadcastMessage(System.String,System.Object,UnityEngine.SendMessageOptions)
		void Register_UnityEngine_Component_BroadcastMessage();
		Register_UnityEngine_Component_BroadcastMessage();

		//System.Void UnityEngine.Component::GetComponentFastPath(System.Type,System.IntPtr)
		void Register_UnityEngine_Component_GetComponentFastPath();
		Register_UnityEngine_Component_GetComponentFastPath();

		//System.Void UnityEngine.Component::GetComponentsForListInternal(System.Type,System.Object)
		void Register_UnityEngine_Component_GetComponentsForListInternal();
		Register_UnityEngine_Component_GetComponentsForListInternal();

		//System.Void UnityEngine.Component::SendMessage(System.String,System.Object,UnityEngine.SendMessageOptions)
		void Register_UnityEngine_Component_SendMessage();
		Register_UnityEngine_Component_SendMessage();

		//System.Void UnityEngine.Component::SendMessageUpwards(System.String,System.Object,UnityEngine.SendMessageOptions)
		void Register_UnityEngine_Component_SendMessageUpwards();
		Register_UnityEngine_Component_SendMessageUpwards();

		//UnityEngine.Component UnityEngine.Component::GetComponent(System.String)
		void Register_UnityEngine_Component_GetComponent();
		Register_UnityEngine_Component_GetComponent();

		//UnityEngine.GameObject UnityEngine.Component::get_gameObject()
		void Register_UnityEngine_Component_get_gameObject();
		Register_UnityEngine_Component_get_gameObject();

		//UnityEngine.Transform UnityEngine.Component::get_transform()
		void Register_UnityEngine_Component_get_transform();
		Register_UnityEngine_Component_get_transform();

	//End Registrations for type : UnityEngine.Component

	//Start Registrations for type : UnityEngine.ComputeBuffer

		//System.Void UnityEngine.ComputeBuffer::DestroyBuffer(UnityEngine.ComputeBuffer)
		void Register_UnityEngine_ComputeBuffer_DestroyBuffer();
		Register_UnityEngine_ComputeBuffer_DestroyBuffer();

	//End Registrations for type : UnityEngine.ComputeBuffer

	//Start Registrations for type : UnityEngine.ComputeShader

		//System.Int32 UnityEngine.ComputeShader::FindKernel(System.String)
		void Register_UnityEngine_ComputeShader_FindKernel();
		Register_UnityEngine_ComputeShader_FindKernel();

	//End Registrations for type : UnityEngine.ComputeShader

	//Start Registrations for type : UnityEngine.ContactFilter2D

		//System.Void UnityEngine.ContactFilter2D::CheckConsistency_Injected(UnityEngine.ContactFilter2D&)
		void Register_UnityEngine_ContactFilter2D_CheckConsistency_Injected();
		Register_UnityEngine_ContactFilter2D_CheckConsistency_Injected();

	//End Registrations for type : UnityEngine.ContactFilter2D

	//Start Registrations for type : UnityEngine.Coroutine

		//System.Void UnityEngine.Coroutine::ReleaseCoroutine(System.IntPtr)
		void Register_UnityEngine_Coroutine_ReleaseCoroutine();
		Register_UnityEngine_Coroutine_ReleaseCoroutine();

	//End Registrations for type : UnityEngine.Coroutine

	//Start Registrations for type : UnityEngine.Cubemap

		//System.Boolean UnityEngine.Cubemap::Internal_CreateImpl(UnityEngine.Cubemap,System.Int32,System.Int32,UnityEngine.Experimental.Rendering.GraphicsFormat,UnityEngine.Experimental.Rendering.TextureCreationFlags,System.IntPtr)
		void Register_UnityEngine_Cubemap_Internal_CreateImpl();
		Register_UnityEngine_Cubemap_Internal_CreateImpl();

		//System.Boolean UnityEngine.Cubemap::get_isReadable()
		void Register_UnityEngine_Cubemap_get_isReadable();
		Register_UnityEngine_Cubemap_get_isReadable();

	//End Registrations for type : UnityEngine.Cubemap

	//Start Registrations for type : UnityEngine.CubemapArray

		//System.Boolean UnityEngine.CubemapArray::Internal_CreateImpl(UnityEngine.CubemapArray,System.Int32,System.Int32,System.Int32,UnityEngine.Experimental.Rendering.GraphicsFormat,UnityEngine.Experimental.Rendering.TextureCreationFlags)
		void Register_UnityEngine_CubemapArray_Internal_CreateImpl();
		Register_UnityEngine_CubemapArray_Internal_CreateImpl();

		//System.Boolean UnityEngine.CubemapArray::get_isReadable()
		void Register_UnityEngine_CubemapArray_get_isReadable();
		Register_UnityEngine_CubemapArray_get_isReadable();

	//End Registrations for type : UnityEngine.CubemapArray

	//Start Registrations for type : UnityEngine.Cursor

		//System.Void UnityEngine.Cursor::set_lockState(UnityEngine.CursorLockMode)
		void Register_UnityEngine_Cursor_set_lockState();
		Register_UnityEngine_Cursor_set_lockState();

		//System.Void UnityEngine.Cursor::set_visible(System.Boolean)
		void Register_UnityEngine_Cursor_set_visible();
		Register_UnityEngine_Cursor_set_visible();

		//UnityEngine.CursorLockMode UnityEngine.Cursor::get_lockState()
		void Register_UnityEngine_Cursor_get_lockState();
		Register_UnityEngine_Cursor_get_lockState();

	//End Registrations for type : UnityEngine.Cursor

	//Start Registrations for type : UnityEngine.Debug

		//System.Int32 UnityEngine.Debug::ExtractStackTraceNoAlloc(System.Byte*,System.Int32,System.String)
		void Register_UnityEngine_Debug_ExtractStackTraceNoAlloc();
		Register_UnityEngine_Debug_ExtractStackTraceNoAlloc();

		//System.Void UnityEngine.Debug::Break()
		void Register_UnityEngine_Debug_Break();
		Register_UnityEngine_Debug_Break();

		//System.Void UnityEngine.Debug::DrawLine_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Color&,System.Single,System.Boolean)
		void Register_UnityEngine_Debug_DrawLine_Injected();
		Register_UnityEngine_Debug_DrawLine_Injected();

	//End Registrations for type : UnityEngine.Debug

	//Start Registrations for type : UnityEngine.DebugLogHandler

		//System.Void UnityEngine.DebugLogHandler::Internal_Log(UnityEngine.LogType,UnityEngine.LogOption,System.String,UnityEngine.Object)
		void Register_UnityEngine_DebugLogHandler_Internal_Log();
		Register_UnityEngine_DebugLogHandler_Internal_Log();

		//System.Void UnityEngine.DebugLogHandler::Internal_LogException(System.Exception,UnityEngine.Object)
		void Register_UnityEngine_DebugLogHandler_Internal_LogException();
		Register_UnityEngine_DebugLogHandler_Internal_LogException();

	//End Registrations for type : UnityEngine.DebugLogHandler

	//Start Registrations for type : UnityEngine.Display

		//System.Int32 UnityEngine.Display::RelativeMouseAtImpl(System.Int32,System.Int32,System.Int32&,System.Int32&)
		void Register_UnityEngine_Display_RelativeMouseAtImpl();
		Register_UnityEngine_Display_RelativeMouseAtImpl();

		//System.Void UnityEngine.Display::GetRenderingExtImpl(System.IntPtr,System.Int32&,System.Int32&)
		void Register_UnityEngine_Display_GetRenderingExtImpl();
		Register_UnityEngine_Display_GetRenderingExtImpl();

		//System.Void UnityEngine.Display::GetSystemExtImpl(System.IntPtr,System.Int32&,System.Int32&)
		void Register_UnityEngine_Display_GetSystemExtImpl();
		Register_UnityEngine_Display_GetSystemExtImpl();

	//End Registrations for type : UnityEngine.Display

	//Start Registrations for type : UnityEngine.Event

		//System.Boolean UnityEngine.Event::PopEvent(UnityEngine.Event)
		void Register_UnityEngine_Event_PopEvent();
		Register_UnityEngine_Event_PopEvent();

		//System.Char UnityEngine.Event::get_character()
		void Register_UnityEngine_Event_get_character();
		Register_UnityEngine_Event_get_character();

		//System.Int32 UnityEngine.Event::get_clickCount()
		void Register_UnityEngine_Event_get_clickCount();
		Register_UnityEngine_Event_get_clickCount();

		//System.IntPtr UnityEngine.Event::Internal_Create(System.Int32)
		void Register_UnityEngine_Event_Internal_Create();
		Register_UnityEngine_Event_Internal_Create();

		//System.String UnityEngine.Event::get_commandName()
		void Register_UnityEngine_Event_get_commandName();
		Register_UnityEngine_Event_get_commandName();

		//System.Void UnityEngine.Event::Internal_Destroy(System.IntPtr)
		void Register_UnityEngine_Event_Internal_Destroy();
		Register_UnityEngine_Event_Internal_Destroy();

		//System.Void UnityEngine.Event::Internal_SetNativeEvent(System.IntPtr)
		void Register_UnityEngine_Event_Internal_SetNativeEvent();
		Register_UnityEngine_Event_Internal_SetNativeEvent();

		//System.Void UnityEngine.Event::Internal_Use()
		void Register_UnityEngine_Event_Internal_Use();
		Register_UnityEngine_Event_Internal_Use();

		//System.Void UnityEngine.Event::get_delta_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Event_get_delta_Injected();
		Register_UnityEngine_Event_get_delta_Injected();

		//System.Void UnityEngine.Event::get_mousePosition_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Event_get_mousePosition_Injected();
		Register_UnityEngine_Event_get_mousePosition_Injected();

		//System.Void UnityEngine.Event::set_character(System.Char)
		void Register_UnityEngine_Event_set_character();
		Register_UnityEngine_Event_set_character();

		//System.Void UnityEngine.Event::set_displayIndex(System.Int32)
		void Register_UnityEngine_Event_set_displayIndex();
		Register_UnityEngine_Event_set_displayIndex();

		//System.Void UnityEngine.Event::set_keyCode(UnityEngine.KeyCode)
		void Register_UnityEngine_Event_set_keyCode();
		Register_UnityEngine_Event_set_keyCode();

		//System.Void UnityEngine.Event::set_modifiers(UnityEngine.EventModifiers)
		void Register_UnityEngine_Event_set_modifiers();
		Register_UnityEngine_Event_set_modifiers();

		//System.Void UnityEngine.Event::set_type(UnityEngine.EventType)
		void Register_UnityEngine_Event_set_type();
		Register_UnityEngine_Event_set_type();

		//UnityEngine.EventModifiers UnityEngine.Event::get_modifiers()
		void Register_UnityEngine_Event_get_modifiers();
		Register_UnityEngine_Event_get_modifiers();

		//UnityEngine.EventType UnityEngine.Event::GetTypeForControl(System.Int32)
		void Register_UnityEngine_Event_GetTypeForControl();
		Register_UnityEngine_Event_GetTypeForControl();

		//UnityEngine.EventType UnityEngine.Event::get_rawType()
		void Register_UnityEngine_Event_get_rawType();
		Register_UnityEngine_Event_get_rawType();

		//UnityEngine.EventType UnityEngine.Event::get_type()
		void Register_UnityEngine_Event_get_type();
		Register_UnityEngine_Event_get_type();

		//UnityEngine.KeyCode UnityEngine.Event::get_keyCode()
		void Register_UnityEngine_Event_get_keyCode();
		Register_UnityEngine_Event_get_keyCode();

		//UnityEngine.PointerType UnityEngine.Event::get_pointerType()
		void Register_UnityEngine_Event_get_pointerType();
		Register_UnityEngine_Event_get_pointerType();

	//End Registrations for type : UnityEngine.Event

	//Start Registrations for type : UnityEngine.Experimental.Rendering.BuiltinRuntimeReflectionSystem

		//System.Boolean UnityEngine.Experimental.Rendering.BuiltinRuntimeReflectionSystem::BuiltinUpdate()
		void Register_UnityEngine_Experimental_Rendering_BuiltinRuntimeReflectionSystem_BuiltinUpdate();
		Register_UnityEngine_Experimental_Rendering_BuiltinRuntimeReflectionSystem_BuiltinUpdate();

	//End Registrations for type : UnityEngine.Experimental.Rendering.BuiltinRuntimeReflectionSystem

	//Start Registrations for type : UnityEngine.Experimental.Rendering.GraphicsFormatUtility

		//System.Boolean UnityEngine.Experimental.Rendering.GraphicsFormatUtility::CanDecompressFormat(UnityEngine.Experimental.Rendering.GraphicsFormat,System.Boolean)
		void Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_CanDecompressFormat();
		Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_CanDecompressFormat();

		//System.Boolean UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsCompressedTextureFormat(UnityEngine.TextureFormat)
		void Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_IsCompressedTextureFormat();
		Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_IsCompressedTextureFormat();

		//System.Boolean UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsPVRTCFormat(UnityEngine.Experimental.Rendering.GraphicsFormat)
		void Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_IsPVRTCFormat();
		Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_IsPVRTCFormat();

		//System.Boolean UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsSRGBFormat(UnityEngine.Experimental.Rendering.GraphicsFormat)
		void Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_IsSRGBFormat();
		Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_IsSRGBFormat();

		//UnityEngine.Experimental.Rendering.GraphicsFormat UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetFormat(UnityEngine.Texture)
		void Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_GetFormat();
		Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_GetFormat();

		//UnityEngine.Experimental.Rendering.GraphicsFormat UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetGraphicsFormat_Native_RenderTextureFormat(UnityEngine.RenderTextureFormat,System.Boolean)
		void Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_GetGraphicsFormat_Native_RenderTextureFormat();
		Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_GetGraphicsFormat_Native_RenderTextureFormat();

		//UnityEngine.Experimental.Rendering.GraphicsFormat UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetGraphicsFormat_Native_TextureFormat(UnityEngine.TextureFormat,System.Boolean)
		void Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_GetGraphicsFormat_Native_TextureFormat();
		Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_GetGraphicsFormat_Native_TextureFormat();

	//End Registrations for type : UnityEngine.Experimental.Rendering.GraphicsFormatUtility

	//Start Registrations for type : UnityEngine.Experimental.Rendering.ScriptableRuntimeReflectionSystemSettings

		//System.Void UnityEngine.Experimental.Rendering.ScriptableRuntimeReflectionSystemSettings::ScriptingDirtyReflectionSystemInstance()
		void Register_UnityEngine_Experimental_Rendering_ScriptableRuntimeReflectionSystemSettings_ScriptingDirtyReflectionSystemInstance();
		Register_UnityEngine_Experimental_Rendering_ScriptableRuntimeReflectionSystemSettings_ScriptingDirtyReflectionSystemInstance();

	//End Registrations for type : UnityEngine.Experimental.Rendering.ScriptableRuntimeReflectionSystemSettings

	//Start Registrations for type : UnityEngine.Flare

		//System.Void UnityEngine.Flare::Internal_Create(UnityEngine.Flare)
		void Register_UnityEngine_Flare_Internal_Create();
		Register_UnityEngine_Flare_Internal_Create();

	//End Registrations for type : UnityEngine.Flare

	//Start Registrations for type : UnityEngine.Font

		//System.Boolean UnityEngine.Font::GetCharacterInfo(System.Char,UnityEngine.CharacterInfo&,System.Int32,UnityEngine.FontStyle)
		void Register_UnityEngine_Font_GetCharacterInfo();
		Register_UnityEngine_Font_GetCharacterInfo();

		//System.Boolean UnityEngine.Font::HasCharacter(System.Int32)
		void Register_UnityEngine_Font_HasCharacter();
		Register_UnityEngine_Font_HasCharacter();

		//System.Boolean UnityEngine.Font::get_dynamic()
		void Register_UnityEngine_Font_get_dynamic();
		Register_UnityEngine_Font_get_dynamic();

		//System.Int32 UnityEngine.Font::get_fontSize()
		void Register_UnityEngine_Font_get_fontSize();
		Register_UnityEngine_Font_get_fontSize();

		//System.String[] UnityEngine.Font::get_fontNames()
		void Register_UnityEngine_Font_get_fontNames();
		Register_UnityEngine_Font_get_fontNames();

		//System.Void UnityEngine.Font::Internal_CreateFont(UnityEngine.Font,System.String)
		void Register_UnityEngine_Font_Internal_CreateFont();
		Register_UnityEngine_Font_Internal_CreateFont();

		//System.Void UnityEngine.Font::RequestCharactersInTexture(System.String,System.Int32,UnityEngine.FontStyle)
		void Register_UnityEngine_Font_RequestCharactersInTexture();
		Register_UnityEngine_Font_RequestCharactersInTexture();

		//UnityEngine.Material UnityEngine.Font::get_material()
		void Register_UnityEngine_Font_get_material();
		Register_UnityEngine_Font_get_material();

	//End Registrations for type : UnityEngine.Font

	//Start Registrations for type : UnityEngine.GameObject

		//System.Array UnityEngine.GameObject::GetComponentsInternal(System.Type,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Object)
		void Register_UnityEngine_GameObject_GetComponentsInternal();
		Register_UnityEngine_GameObject_GetComponentsInternal();

		//System.Boolean UnityEngine.GameObject::CompareTag(System.String)
		void Register_UnityEngine_GameObject_CompareTag();
		Register_UnityEngine_GameObject_CompareTag();

		//System.Boolean UnityEngine.GameObject::get_active()
		void Register_UnityEngine_GameObject_get_active();
		Register_UnityEngine_GameObject_get_active();

		//System.Boolean UnityEngine.GameObject::get_activeInHierarchy()
		void Register_UnityEngine_GameObject_get_activeInHierarchy();
		Register_UnityEngine_GameObject_get_activeInHierarchy();

		//System.Boolean UnityEngine.GameObject::get_activeSelf()
		void Register_UnityEngine_GameObject_get_activeSelf();
		Register_UnityEngine_GameObject_get_activeSelf();

		//System.Boolean UnityEngine.GameObject::get_isStatic()
		void Register_UnityEngine_GameObject_get_isStatic();
		Register_UnityEngine_GameObject_get_isStatic();

		//System.Int32 UnityEngine.GameObject::get_layer()
		void Register_UnityEngine_GameObject_get_layer();
		Register_UnityEngine_GameObject_get_layer();

		//System.String UnityEngine.GameObject::get_tag()
		void Register_UnityEngine_GameObject_get_tag();
		Register_UnityEngine_GameObject_get_tag();

		//System.UInt64 UnityEngine.GameObject::get_sceneCullingMask()
		void Register_UnityEngine_GameObject_get_sceneCullingMask();
		Register_UnityEngine_GameObject_get_sceneCullingMask();

		//System.Void UnityEngine.GameObject::BroadcastMessage(System.String,System.Object,UnityEngine.SendMessageOptions)
		void Register_UnityEngine_GameObject_BroadcastMessage();
		Register_UnityEngine_GameObject_BroadcastMessage();

		//System.Void UnityEngine.GameObject::GetComponentFastPath(System.Type,System.IntPtr)
		void Register_UnityEngine_GameObject_GetComponentFastPath();
		Register_UnityEngine_GameObject_GetComponentFastPath();

		//System.Void UnityEngine.GameObject::Internal_CreateGameObject(UnityEngine.GameObject,System.String)
		void Register_UnityEngine_GameObject_Internal_CreateGameObject();
		Register_UnityEngine_GameObject_Internal_CreateGameObject();

		//System.Void UnityEngine.GameObject::SendMessage(System.String,System.Object,UnityEngine.SendMessageOptions)
		void Register_UnityEngine_GameObject_SendMessage();
		Register_UnityEngine_GameObject_SendMessage();

		//System.Void UnityEngine.GameObject::SendMessageUpwards(System.String,System.Object,UnityEngine.SendMessageOptions)
		void Register_UnityEngine_GameObject_SendMessageUpwards();
		Register_UnityEngine_GameObject_SendMessageUpwards();

		//System.Void UnityEngine.GameObject::SetActive(System.Boolean)
		void Register_UnityEngine_GameObject_SetActive();
		Register_UnityEngine_GameObject_SetActive();

		//System.Void UnityEngine.GameObject::TryGetComponentFastPath(System.Type,System.IntPtr)
		void Register_UnityEngine_GameObject_TryGetComponentFastPath();
		Register_UnityEngine_GameObject_TryGetComponentFastPath();

		//System.Void UnityEngine.GameObject::get_scene_Injected(UnityEngine.SceneManagement.Scene&)
		void Register_UnityEngine_GameObject_get_scene_Injected();
		Register_UnityEngine_GameObject_get_scene_Injected();

		//System.Void UnityEngine.GameObject::set_isStatic(System.Boolean)
		void Register_UnityEngine_GameObject_set_isStatic();
		Register_UnityEngine_GameObject_set_isStatic();

		//System.Void UnityEngine.GameObject::set_layer(System.Int32)
		void Register_UnityEngine_GameObject_set_layer();
		Register_UnityEngine_GameObject_set_layer();

		//System.Void UnityEngine.GameObject::set_tag(System.String)
		void Register_UnityEngine_GameObject_set_tag();
		Register_UnityEngine_GameObject_set_tag();

		//UnityEngine.Component UnityEngine.GameObject::GetComponent(System.Type)
		void Register_UnityEngine_GameObject_GetComponent();
		Register_UnityEngine_GameObject_GetComponent();

		//UnityEngine.Component UnityEngine.GameObject::GetComponentByName(System.String)
		void Register_UnityEngine_GameObject_GetComponentByName();
		Register_UnityEngine_GameObject_GetComponentByName();

		//UnityEngine.Component UnityEngine.GameObject::GetComponentInChildren(System.Type,System.Boolean)
		void Register_UnityEngine_GameObject_GetComponentInChildren();
		Register_UnityEngine_GameObject_GetComponentInChildren();

		//UnityEngine.Component UnityEngine.GameObject::GetComponentInParent(System.Type,System.Boolean)
		void Register_UnityEngine_GameObject_GetComponentInParent();
		Register_UnityEngine_GameObject_GetComponentInParent();

		//UnityEngine.Component UnityEngine.GameObject::Internal_AddComponentWithType(System.Type)
		void Register_UnityEngine_GameObject_Internal_AddComponentWithType();
		Register_UnityEngine_GameObject_Internal_AddComponentWithType();

		//UnityEngine.Component UnityEngine.GameObject::TryGetComponentInternal(System.Type)
		void Register_UnityEngine_GameObject_TryGetComponentInternal();
		Register_UnityEngine_GameObject_TryGetComponentInternal();

		//UnityEngine.GameObject UnityEngine.GameObject::CreatePrimitive(UnityEngine.PrimitiveType)
		void Register_UnityEngine_GameObject_CreatePrimitive();
		Register_UnityEngine_GameObject_CreatePrimitive();

		//UnityEngine.GameObject UnityEngine.GameObject::Find(System.String)
		void Register_UnityEngine_GameObject_Find();
		Register_UnityEngine_GameObject_Find();

		//UnityEngine.GameObject UnityEngine.GameObject::FindGameObjectWithTag(System.String)
		void Register_UnityEngine_GameObject_FindGameObjectWithTag();
		Register_UnityEngine_GameObject_FindGameObjectWithTag();

		//UnityEngine.GameObject[] UnityEngine.GameObject::FindGameObjectsWithTag(System.String)
		void Register_UnityEngine_GameObject_FindGameObjectsWithTag();
		Register_UnityEngine_GameObject_FindGameObjectsWithTag();

		//UnityEngine.Transform UnityEngine.GameObject::get_transform()
		void Register_UnityEngine_GameObject_get_transform();
		Register_UnityEngine_GameObject_get_transform();

	//End Registrations for type : UnityEngine.GameObject

	//Start Registrations for type : UnityEngine.Gizmos

		//System.Void UnityEngine.Gizmos::DrawCube_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)
		void Register_UnityEngine_Gizmos_DrawCube_Injected();
		Register_UnityEngine_Gizmos_DrawCube_Injected();

		//System.Void UnityEngine.Gizmos::DrawFrustum_Injected(UnityEngine.Vector3&,System.Single,System.Single,System.Single,System.Single)
		void Register_UnityEngine_Gizmos_DrawFrustum_Injected();
		Register_UnityEngine_Gizmos_DrawFrustum_Injected();

		//System.Void UnityEngine.Gizmos::DrawLine_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)
		void Register_UnityEngine_Gizmos_DrawLine_Injected();
		Register_UnityEngine_Gizmos_DrawLine_Injected();

		//System.Void UnityEngine.Gizmos::DrawSphere_Injected(UnityEngine.Vector3&,System.Single)
		void Register_UnityEngine_Gizmos_DrawSphere_Injected();
		Register_UnityEngine_Gizmos_DrawSphere_Injected();

		//System.Void UnityEngine.Gizmos::DrawWireCube_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)
		void Register_UnityEngine_Gizmos_DrawWireCube_Injected();
		Register_UnityEngine_Gizmos_DrawWireCube_Injected();

		//System.Void UnityEngine.Gizmos::DrawWireSphere_Injected(UnityEngine.Vector3&,System.Single)
		void Register_UnityEngine_Gizmos_DrawWireSphere_Injected();
		Register_UnityEngine_Gizmos_DrawWireSphere_Injected();

		//System.Void UnityEngine.Gizmos::get_matrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Gizmos_get_matrix_Injected();
		Register_UnityEngine_Gizmos_get_matrix_Injected();

		//System.Void UnityEngine.Gizmos::set_color_Injected(UnityEngine.Color&)
		void Register_UnityEngine_Gizmos_set_color_Injected();
		Register_UnityEngine_Gizmos_set_color_Injected();

		//System.Void UnityEngine.Gizmos::set_matrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Gizmos_set_matrix_Injected();
		Register_UnityEngine_Gizmos_set_matrix_Injected();

	//End Registrations for type : UnityEngine.Gizmos

	//Start Registrations for type : UnityEngine.Gradient

		//System.Boolean UnityEngine.Gradient::Internal_Equals(System.IntPtr)
		void Register_UnityEngine_Gradient_Internal_Equals();
		Register_UnityEngine_Gradient_Internal_Equals();

		//System.IntPtr UnityEngine.Gradient::Init()
		void Register_UnityEngine_Gradient_Init();
		Register_UnityEngine_Gradient_Init();

		//System.Void UnityEngine.Gradient::Cleanup()
		void Register_UnityEngine_Gradient_Cleanup();
		Register_UnityEngine_Gradient_Cleanup();

		//System.Void UnityEngine.Gradient::Evaluate_Injected(System.Single,UnityEngine.Color&)
		void Register_UnityEngine_Gradient_Evaluate_Injected();
		Register_UnityEngine_Gradient_Evaluate_Injected();

		//UnityEngine.GradientColorKey[] UnityEngine.Gradient::get_colorKeys()
		void Register_UnityEngine_Gradient_get_colorKeys();
		Register_UnityEngine_Gradient_get_colorKeys();

	//End Registrations for type : UnityEngine.Gradient

	//Start Registrations for type : UnityEngine.Graphics

		//System.Int32 UnityEngine.Graphics::Internal_GetMaxDrawMeshInstanceCount()
		void Register_UnityEngine_Graphics_Internal_GetMaxDrawMeshInstanceCount();
		Register_UnityEngine_Graphics_Internal_GetMaxDrawMeshInstanceCount();

		//System.Void UnityEngine.Graphics::CopyTexture_Region(UnityEngine.Texture,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,UnityEngine.Texture,System.Int32,System.Int32,System.Int32,System.Int32)
		void Register_UnityEngine_Graphics_CopyTexture_Region();
		Register_UnityEngine_Graphics_CopyTexture_Region();

		//System.Void UnityEngine.Graphics::Internal_BlitMaterial5(UnityEngine.Texture,UnityEngine.RenderTexture,UnityEngine.Material,System.Int32,System.Boolean)
		void Register_UnityEngine_Graphics_Internal_BlitMaterial5();
		Register_UnityEngine_Graphics_Internal_BlitMaterial5();

		//System.Void UnityEngine.Graphics::Internal_DrawTexture(UnityEngine.Internal_DrawTextureArguments&)
		void Register_UnityEngine_Graphics_Internal_DrawTexture();
		Register_UnityEngine_Graphics_Internal_DrawTexture();

	//End Registrations for type : UnityEngine.Graphics

	//Start Registrations for type : UnityEngine.GUI

		//System.Boolean UnityEngine.GUI::HasMouseControl(System.Int32)
		void Register_UnityEngine_GUI_HasMouseControl();
		Register_UnityEngine_GUI_HasMouseControl();

		//System.Boolean UnityEngine.GUI::get_usePageScrollbars()
		void Register_UnityEngine_GUI_get_usePageScrollbars();
		Register_UnityEngine_GUI_get_usePageScrollbars();

		//System.Void UnityEngine.GUI::GrabMouseControl(System.Int32)
		void Register_UnityEngine_GUI_GrabMouseControl();
		Register_UnityEngine_GUI_GrabMouseControl();

		//System.Void UnityEngine.GUI::InternalRepaintEditorWindow()
		void Register_UnityEngine_GUI_InternalRepaintEditorWindow();
		Register_UnityEngine_GUI_InternalRepaintEditorWindow();

		//System.Void UnityEngine.GUI::ReleaseMouseControl()
		void Register_UnityEngine_GUI_ReleaseMouseControl();
		Register_UnityEngine_GUI_ReleaseMouseControl();

		//System.Void UnityEngine.GUI::get_color_Injected(UnityEngine.Color&)
		void Register_UnityEngine_GUI_get_color_Injected();
		Register_UnityEngine_GUI_get_color_Injected();

		//System.Void UnityEngine.GUI::set_changed(System.Boolean)
		void Register_UnityEngine_GUI_set_changed();
		Register_UnityEngine_GUI_set_changed();

		//System.Void UnityEngine.GUI::set_color_Injected(UnityEngine.Color&)
		void Register_UnityEngine_GUI_set_color_Injected();
		Register_UnityEngine_GUI_set_color_Injected();

		//System.Void UnityEngine.GUI::set_enabled(System.Boolean)
		void Register_UnityEngine_GUI_set_enabled();
		Register_UnityEngine_GUI_set_enabled();

		//UnityEngine.Material UnityEngine.GUI::get_blendMaterial()
		void Register_UnityEngine_GUI_get_blendMaterial();
		Register_UnityEngine_GUI_get_blendMaterial();

		//UnityEngine.Material UnityEngine.GUI::get_blitMaterial()
		void Register_UnityEngine_GUI_get_blitMaterial();
		Register_UnityEngine_GUI_get_blitMaterial();

		//UnityEngine.Material UnityEngine.GUI::get_roundedRectMaterial()
		void Register_UnityEngine_GUI_get_roundedRectMaterial();
		Register_UnityEngine_GUI_get_roundedRectMaterial();

		//UnityEngine.Material UnityEngine.GUI::get_roundedRectWithColorPerBorderMaterial()
		void Register_UnityEngine_GUI_get_roundedRectWithColorPerBorderMaterial();
		Register_UnityEngine_GUI_get_roundedRectWithColorPerBorderMaterial();

	//End Registrations for type : UnityEngine.GUI

	//Start Registrations for type : UnityEngine.GUIClip

		//System.Void UnityEngine.GUIClip::GetMatrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_GUIClip_GetMatrix_Injected();
		Register_UnityEngine_GUIClip_GetMatrix_Injected();

		//System.Void UnityEngine.GUIClip::Internal_Pop()
		void Register_UnityEngine_GUIClip_Internal_Pop();
		Register_UnityEngine_GUIClip_Internal_Pop();

		//System.Void UnityEngine.GUIClip::Internal_Push_Injected(UnityEngine.Rect&,UnityEngine.Vector2&,UnityEngine.Vector2&,System.Boolean)
		void Register_UnityEngine_GUIClip_Internal_Push_Injected();
		Register_UnityEngine_GUIClip_Internal_Push_Injected();

		//System.Void UnityEngine.GUIClip::SetMatrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_GUIClip_SetMatrix_Injected();
		Register_UnityEngine_GUIClip_SetMatrix_Injected();

		//System.Void UnityEngine.GUIClip::UnclipToWindow_Vector2_Injected(UnityEngine.Vector2&,UnityEngine.Vector2&)
		void Register_UnityEngine_GUIClip_UnclipToWindow_Vector2_Injected();
		Register_UnityEngine_GUIClip_UnclipToWindow_Vector2_Injected();

		//System.Void UnityEngine.GUIClip::Unclip_Vector2_Injected(UnityEngine.Vector2&,UnityEngine.Vector2&)
		void Register_UnityEngine_GUIClip_Unclip_Vector2_Injected();
		Register_UnityEngine_GUIClip_Unclip_Vector2_Injected();

		//System.Void UnityEngine.GUIClip::get_visibleRect_Injected(UnityEngine.Rect&)
		void Register_UnityEngine_GUIClip_get_visibleRect_Injected();
		Register_UnityEngine_GUIClip_get_visibleRect_Injected();

	//End Registrations for type : UnityEngine.GUIClip

	//Start Registrations for type : UnityEngine.GUILayoutUtility

		//System.Void UnityEngine.GUILayoutUtility::Internal_GetWindowRect_Injected(System.Int32,UnityEngine.Rect&)
		void Register_UnityEngine_GUILayoutUtility_Internal_GetWindowRect_Injected();
		Register_UnityEngine_GUILayoutUtility_Internal_GetWindowRect_Injected();

		//System.Void UnityEngine.GUILayoutUtility::Internal_MoveWindow_Injected(System.Int32,UnityEngine.Rect&)
		void Register_UnityEngine_GUILayoutUtility_Internal_MoveWindow_Injected();
		Register_UnityEngine_GUILayoutUtility_Internal_MoveWindow_Injected();

	//End Registrations for type : UnityEngine.GUILayoutUtility

	//Start Registrations for type : UnityEngine.GUISettings

		//System.Single UnityEngine.GUISettings::Internal_GetCursorFlashSpeed()
		void Register_UnityEngine_GUISettings_Internal_GetCursorFlashSpeed();
		Register_UnityEngine_GUISettings_Internal_GetCursorFlashSpeed();

	//End Registrations for type : UnityEngine.GUISettings

	//Start Registrations for type : UnityEngine.GUIStyle

		//System.Boolean UnityEngine.GUIStyle::IsTooltipActive(System.String)
		void Register_UnityEngine_GUIStyle_IsTooltipActive();
		Register_UnityEngine_GUIStyle_IsTooltipActive();

		//System.Boolean UnityEngine.GUIStyle::get_stretchHeight()
		void Register_UnityEngine_GUIStyle_get_stretchHeight();
		Register_UnityEngine_GUIStyle_get_stretchHeight();

		//System.Boolean UnityEngine.GUIStyle::get_stretchWidth()
		void Register_UnityEngine_GUIStyle_get_stretchWidth();
		Register_UnityEngine_GUIStyle_get_stretchWidth();

		//System.Boolean UnityEngine.GUIStyle::get_wordWrap()
		void Register_UnityEngine_GUIStyle_get_wordWrap();
		Register_UnityEngine_GUIStyle_get_wordWrap();

		//System.Int32 UnityEngine.GUIStyle::Internal_GetCursorStringIndex_Injected(UnityEngine.Rect&,UnityEngine.GUIContent,UnityEngine.Vector2&)
		void Register_UnityEngine_GUIStyle_Internal_GetCursorStringIndex_Injected();
		Register_UnityEngine_GUIStyle_Internal_GetCursorStringIndex_Injected();

		//System.IntPtr UnityEngine.GUIStyle::GetRectOffsetPtr(System.Int32)
		void Register_UnityEngine_GUIStyle_GetRectOffsetPtr();
		Register_UnityEngine_GUIStyle_GetRectOffsetPtr();

		//System.IntPtr UnityEngine.GUIStyle::GetStyleStatePtr(System.Int32)
		void Register_UnityEngine_GUIStyle_GetStyleStatePtr();
		Register_UnityEngine_GUIStyle_GetStyleStatePtr();

		//System.IntPtr UnityEngine.GUIStyle::Internal_Create(UnityEngine.GUIStyle)
		void Register_UnityEngine_GUIStyle_Internal_Create();
		Register_UnityEngine_GUIStyle_Internal_Create();

		//System.Single UnityEngine.GUIStyle::Internal_CalcHeight(UnityEngine.GUIContent,System.Single)
		void Register_UnityEngine_GUIStyle_Internal_CalcHeight();
		Register_UnityEngine_GUIStyle_Internal_CalcHeight();

		//System.Single UnityEngine.GUIStyle::Internal_GetCursorFlashOffset()
		void Register_UnityEngine_GUIStyle_Internal_GetCursorFlashOffset();
		Register_UnityEngine_GUIStyle_Internal_GetCursorFlashOffset();

		//System.Single UnityEngine.GUIStyle::Internal_GetLineHeight(System.IntPtr)
		void Register_UnityEngine_GUIStyle_Internal_GetLineHeight();
		Register_UnityEngine_GUIStyle_Internal_GetLineHeight();

		//System.Single UnityEngine.GUIStyle::get_fixedHeight()
		void Register_UnityEngine_GUIStyle_get_fixedHeight();
		Register_UnityEngine_GUIStyle_get_fixedHeight();

		//System.Single UnityEngine.GUIStyle::get_fixedWidth()
		void Register_UnityEngine_GUIStyle_get_fixedWidth();
		Register_UnityEngine_GUIStyle_get_fixedWidth();

		//System.String UnityEngine.GUIStyle::Internal_GetSelectedRenderedText_Injected(UnityEngine.Rect&,UnityEngine.GUIContent,System.Int32,System.Int32)
		void Register_UnityEngine_GUIStyle_Internal_GetSelectedRenderedText_Injected();
		Register_UnityEngine_GUIStyle_Internal_GetSelectedRenderedText_Injected();

		//System.String UnityEngine.GUIStyle::get_rawName()
		void Register_UnityEngine_GUIStyle_get_rawName();
		Register_UnityEngine_GUIStyle_get_rawName();

		//System.Void UnityEngine.GUIStyle::AssignRectOffset(System.Int32,System.IntPtr)
		void Register_UnityEngine_GUIStyle_AssignRectOffset();
		Register_UnityEngine_GUIStyle_AssignRectOffset();

		//System.Void UnityEngine.GUIStyle::Internal_CalcMinMaxWidth_Injected(UnityEngine.GUIContent,UnityEngine.Vector2&)
		void Register_UnityEngine_GUIStyle_Internal_CalcMinMaxWidth_Injected();
		Register_UnityEngine_GUIStyle_Internal_CalcMinMaxWidth_Injected();

		//System.Void UnityEngine.GUIStyle::Internal_CalcSizeWithConstraints_Injected(UnityEngine.GUIContent,UnityEngine.Vector2&,UnityEngine.Vector2&)
		void Register_UnityEngine_GUIStyle_Internal_CalcSizeWithConstraints_Injected();
		Register_UnityEngine_GUIStyle_Internal_CalcSizeWithConstraints_Injected();

		//System.Void UnityEngine.GUIStyle::Internal_CalcSize_Injected(UnityEngine.GUIContent,UnityEngine.Vector2&)
		void Register_UnityEngine_GUIStyle_Internal_CalcSize_Injected();
		Register_UnityEngine_GUIStyle_Internal_CalcSize_Injected();

		//System.Void UnityEngine.GUIStyle::Internal_Destroy(System.IntPtr)
		void Register_UnityEngine_GUIStyle_Internal_Destroy();
		Register_UnityEngine_GUIStyle_Internal_Destroy();

		//System.Void UnityEngine.GUIStyle::Internal_Draw2_Injected(UnityEngine.Rect&,UnityEngine.GUIContent,System.Int32,System.Boolean)
		void Register_UnityEngine_GUIStyle_Internal_Draw2_Injected();
		Register_UnityEngine_GUIStyle_Internal_Draw2_Injected();

		//System.Void UnityEngine.GUIStyle::Internal_DrawCursor_Injected(UnityEngine.Rect&,UnityEngine.GUIContent,System.Int32,UnityEngine.Color&)
		void Register_UnityEngine_GUIStyle_Internal_DrawCursor_Injected();
		Register_UnityEngine_GUIStyle_Internal_DrawCursor_Injected();

		//System.Void UnityEngine.GUIStyle::Internal_DrawWithTextSelection_Injected(UnityEngine.Rect&,UnityEngine.GUIContent,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Int32,System.Int32,UnityEngine.Color&,UnityEngine.Color&)
		void Register_UnityEngine_GUIStyle_Internal_DrawWithTextSelection_Injected();
		Register_UnityEngine_GUIStyle_Internal_DrawWithTextSelection_Injected();

		//System.Void UnityEngine.GUIStyle::Internal_Draw_Injected(UnityEngine.Rect&,UnityEngine.GUIContent,System.Boolean,System.Boolean,System.Boolean,System.Boolean)
		void Register_UnityEngine_GUIStyle_Internal_Draw_Injected();
		Register_UnityEngine_GUIStyle_Internal_Draw_Injected();

		//System.Void UnityEngine.GUIStyle::Internal_GetCursorPixelPosition_Injected(UnityEngine.Rect&,UnityEngine.GUIContent,System.Int32,UnityEngine.Vector2&)
		void Register_UnityEngine_GUIStyle_Internal_GetCursorPixelPosition_Injected();
		Register_UnityEngine_GUIStyle_Internal_GetCursorPixelPosition_Injected();

		//System.Void UnityEngine.GUIStyle::SetDefaultFont(UnityEngine.Font)
		void Register_UnityEngine_GUIStyle_SetDefaultFont();
		Register_UnityEngine_GUIStyle_SetDefaultFont();

		//System.Void UnityEngine.GUIStyle::SetMouseTooltip_Injected(System.String,UnityEngine.Rect&)
		void Register_UnityEngine_GUIStyle_SetMouseTooltip_Injected();
		Register_UnityEngine_GUIStyle_SetMouseTooltip_Injected();

		//System.Void UnityEngine.GUIStyle::get_contentOffset_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_GUIStyle_get_contentOffset_Injected();
		Register_UnityEngine_GUIStyle_get_contentOffset_Injected();

		//System.Void UnityEngine.GUIStyle::set_Internal_clipOffset_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_GUIStyle_set_Internal_clipOffset_Injected();
		Register_UnityEngine_GUIStyle_set_Internal_clipOffset_Injected();

		//System.Void UnityEngine.GUIStyle::set_alignment(UnityEngine.TextAnchor)
		void Register_UnityEngine_GUIStyle_set_alignment();
		Register_UnityEngine_GUIStyle_set_alignment();

		//System.Void UnityEngine.GUIStyle::set_clipping(UnityEngine.TextClipping)
		void Register_UnityEngine_GUIStyle_set_clipping();
		Register_UnityEngine_GUIStyle_set_clipping();

		//System.Void UnityEngine.GUIStyle::set_contentOffset_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_GUIStyle_set_contentOffset_Injected();
		Register_UnityEngine_GUIStyle_set_contentOffset_Injected();

		//System.Void UnityEngine.GUIStyle::set_fixedHeight(System.Single)
		void Register_UnityEngine_GUIStyle_set_fixedHeight();
		Register_UnityEngine_GUIStyle_set_fixedHeight();

		//System.Void UnityEngine.GUIStyle::set_fixedWidth(System.Single)
		void Register_UnityEngine_GUIStyle_set_fixedWidth();
		Register_UnityEngine_GUIStyle_set_fixedWidth();

		//System.Void UnityEngine.GUIStyle::set_fontSize(System.Int32)
		void Register_UnityEngine_GUIStyle_set_fontSize();
		Register_UnityEngine_GUIStyle_set_fontSize();

		//System.Void UnityEngine.GUIStyle::set_fontStyle(UnityEngine.FontStyle)
		void Register_UnityEngine_GUIStyle_set_fontStyle();
		Register_UnityEngine_GUIStyle_set_fontStyle();

		//System.Void UnityEngine.GUIStyle::set_imagePosition(UnityEngine.ImagePosition)
		void Register_UnityEngine_GUIStyle_set_imagePosition();
		Register_UnityEngine_GUIStyle_set_imagePosition();

		//System.Void UnityEngine.GUIStyle::set_rawName(System.String)
		void Register_UnityEngine_GUIStyle_set_rawName();
		Register_UnityEngine_GUIStyle_set_rawName();

		//System.Void UnityEngine.GUIStyle::set_stretchHeight(System.Boolean)
		void Register_UnityEngine_GUIStyle_set_stretchHeight();
		Register_UnityEngine_GUIStyle_set_stretchHeight();

		//System.Void UnityEngine.GUIStyle::set_stretchWidth(System.Boolean)
		void Register_UnityEngine_GUIStyle_set_stretchWidth();
		Register_UnityEngine_GUIStyle_set_stretchWidth();

		//System.Void UnityEngine.GUIStyle::set_wordWrap(System.Boolean)
		void Register_UnityEngine_GUIStyle_set_wordWrap();
		Register_UnityEngine_GUIStyle_set_wordWrap();

		//UnityEngine.Font UnityEngine.GUIStyle::get_font()
		void Register_UnityEngine_GUIStyle_get_font();
		Register_UnityEngine_GUIStyle_get_font();

		//UnityEngine.ImagePosition UnityEngine.GUIStyle::get_imagePosition()
		void Register_UnityEngine_GUIStyle_get_imagePosition();
		Register_UnityEngine_GUIStyle_get_imagePosition();

	//End Registrations for type : UnityEngine.GUIStyle

	//Start Registrations for type : UnityEngine.GUIStyleState

		//System.IntPtr UnityEngine.GUIStyleState::Init()
		void Register_UnityEngine_GUIStyleState_Init();
		Register_UnityEngine_GUIStyleState_Init();

		//System.Void UnityEngine.GUIStyleState::Cleanup()
		void Register_UnityEngine_GUIStyleState_Cleanup();
		Register_UnityEngine_GUIStyleState_Cleanup();

		//System.Void UnityEngine.GUIStyleState::set_background(UnityEngine.Texture2D)
		void Register_UnityEngine_GUIStyleState_set_background();
		Register_UnityEngine_GUIStyleState_set_background();

		//System.Void UnityEngine.GUIStyleState::set_textColor_Injected(UnityEngine.Color&)
		void Register_UnityEngine_GUIStyleState_set_textColor_Injected();
		Register_UnityEngine_GUIStyleState_set_textColor_Injected();

	//End Registrations for type : UnityEngine.GUIStyleState

	//Start Registrations for type : UnityEngine.GUIUtility

		//System.Int32 UnityEngine.GUIUtility::Internal_GetControlID_Injected(System.Int32,UnityEngine.FocusType,UnityEngine.Rect&)
		void Register_UnityEngine_GUIUtility_Internal_GetControlID_Injected();
		Register_UnityEngine_GUIUtility_Internal_GetControlID_Injected();

		//System.Int32 UnityEngine.GUIUtility::Internal_GetHotControl()
		void Register_UnityEngine_GUIUtility_Internal_GetHotControl();
		Register_UnityEngine_GUIUtility_Internal_GetHotControl();

		//System.Int32 UnityEngine.GUIUtility::Internal_GetKeyboardControl()
		void Register_UnityEngine_GUIUtility_Internal_GetKeyboardControl();
		Register_UnityEngine_GUIUtility_Internal_GetKeyboardControl();

		//System.Int32 UnityEngine.GUIUtility::get_guiDepth()
		void Register_UnityEngine_GUIUtility_get_guiDepth();
		Register_UnityEngine_GUIUtility_get_guiDepth();

		//System.Object UnityEngine.GUIUtility::Internal_GetDefaultSkin(System.Int32)
		void Register_UnityEngine_GUIUtility_Internal_GetDefaultSkin();
		Register_UnityEngine_GUIUtility_Internal_GetDefaultSkin();

		//System.Single UnityEngine.GUIUtility::get_pixelsPerPoint()
		void Register_UnityEngine_GUIUtility_get_pixelsPerPoint();
		Register_UnityEngine_GUIUtility_get_pixelsPerPoint();

		//System.String UnityEngine.GUIUtility::get_compositionString()
		void Register_UnityEngine_GUIUtility_get_compositionString();
		Register_UnityEngine_GUIUtility_get_compositionString();

		//System.String UnityEngine.GUIUtility::get_systemCopyBuffer()
		void Register_UnityEngine_GUIUtility_get_systemCopyBuffer();
		Register_UnityEngine_GUIUtility_get_systemCopyBuffer();

		//System.Void UnityEngine.GUIUtility::Internal_ExitGUI()
		void Register_UnityEngine_GUIUtility_Internal_ExitGUI();
		Register_UnityEngine_GUIUtility_Internal_ExitGUI();

		//System.Void UnityEngine.GUIUtility::Internal_SetHotControl(System.Int32)
		void Register_UnityEngine_GUIUtility_Internal_SetHotControl();
		Register_UnityEngine_GUIUtility_Internal_SetHotControl();

		//System.Void UnityEngine.GUIUtility::Internal_SetKeyboardControl(System.Int32)
		void Register_UnityEngine_GUIUtility_Internal_SetKeyboardControl();
		Register_UnityEngine_GUIUtility_Internal_SetKeyboardControl();

		//System.Void UnityEngine.GUIUtility::set_compositionCursorPos_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_GUIUtility_set_compositionCursorPos_Injected();
		Register_UnityEngine_GUIUtility_set_compositionCursorPos_Injected();

		//System.Void UnityEngine.GUIUtility::set_mouseUsed(System.Boolean)
		void Register_UnityEngine_GUIUtility_set_mouseUsed();
		Register_UnityEngine_GUIUtility_set_mouseUsed();

		//System.Void UnityEngine.GUIUtility::set_systemCopyBuffer(System.String)
		void Register_UnityEngine_GUIUtility_set_systemCopyBuffer();
		Register_UnityEngine_GUIUtility_set_systemCopyBuffer();

		//System.Void UnityEngine.GUIUtility::set_textFieldInput(System.Boolean)
		void Register_UnityEngine_GUIUtility_set_textFieldInput();
		Register_UnityEngine_GUIUtility_set_textFieldInput();

	//End Registrations for type : UnityEngine.GUIUtility

	//Start Registrations for type : UnityEngine.Hash128

		//System.String UnityEngine.Hash128::Hash128ToStringImpl_Injected(UnityEngine.Hash128&)
		void Register_UnityEngine_Hash128_Hash128ToStringImpl_Injected();
		Register_UnityEngine_Hash128_Hash128ToStringImpl_Injected();

	//End Registrations for type : UnityEngine.Hash128

	//Start Registrations for type : UnityEngine.HingeJoint

		//System.Void UnityEngine.HingeJoint::set_limits_Injected(UnityEngine.JointLimits&)
		void Register_UnityEngine_HingeJoint_set_limits_Injected();
		Register_UnityEngine_HingeJoint_set_limits_Injected();

		//System.Void UnityEngine.HingeJoint::set_useLimits(System.Boolean)
		void Register_UnityEngine_HingeJoint_set_useLimits();
		Register_UnityEngine_HingeJoint_set_useLimits();

	//End Registrations for type : UnityEngine.HingeJoint

	//Start Registrations for type : UnityEngine.HingeJoint2D

		//System.Void UnityEngine.HingeJoint2D::set_limits_Injected(UnityEngine.JointAngleLimits2D&)
		void Register_UnityEngine_HingeJoint2D_set_limits_Injected();
		Register_UnityEngine_HingeJoint2D_set_limits_Injected();

		//System.Void UnityEngine.HingeJoint2D::set_useLimits(System.Boolean)
		void Register_UnityEngine_HingeJoint2D_set_useLimits();
		Register_UnityEngine_HingeJoint2D_set_useLimits();

	//End Registrations for type : UnityEngine.HingeJoint2D

	//Start Registrations for type : UnityEngine.ImageConversion

		//System.Boolean UnityEngine.ImageConversion::LoadImage(UnityEngine.Texture2D,System.Byte[],System.Boolean)
		void Register_UnityEngine_ImageConversion_LoadImage();
		Register_UnityEngine_ImageConversion_LoadImage();

		//System.Byte[] UnityEngine.ImageConversion::EncodeToJPG(UnityEngine.Texture2D,System.Int32)
		void Register_UnityEngine_ImageConversion_EncodeToJPG();
		Register_UnityEngine_ImageConversion_EncodeToJPG();

		//System.Byte[] UnityEngine.ImageConversion::EncodeToPNG(UnityEngine.Texture2D)
		void Register_UnityEngine_ImageConversion_EncodeToPNG();
		Register_UnityEngine_ImageConversion_EncodeToPNG();

	//End Registrations for type : UnityEngine.ImageConversion

	//Start Registrations for type : UnityEngine.Input

		//System.Boolean UnityEngine.Input::GetButtonDown(System.String)
		void Register_UnityEngine_Input_GetButtonDown();
		Register_UnityEngine_Input_GetButtonDown();

		//System.Boolean UnityEngine.Input::GetKeyDownInt(UnityEngine.KeyCode)
		void Register_UnityEngine_Input_GetKeyDownInt();
		Register_UnityEngine_Input_GetKeyDownInt();

		//System.Boolean UnityEngine.Input::GetKeyInt(UnityEngine.KeyCode)
		void Register_UnityEngine_Input_GetKeyInt();
		Register_UnityEngine_Input_GetKeyInt();

		//System.Boolean UnityEngine.Input::GetKeyUpInt(UnityEngine.KeyCode)
		void Register_UnityEngine_Input_GetKeyUpInt();
		Register_UnityEngine_Input_GetKeyUpInt();

		//System.Boolean UnityEngine.Input::GetMouseButton(System.Int32)
		void Register_UnityEngine_Input_GetMouseButton();
		Register_UnityEngine_Input_GetMouseButton();

		//System.Boolean UnityEngine.Input::GetMouseButtonDown(System.Int32)
		void Register_UnityEngine_Input_GetMouseButtonDown();
		Register_UnityEngine_Input_GetMouseButtonDown();

		//System.Boolean UnityEngine.Input::GetMouseButtonUp(System.Int32)
		void Register_UnityEngine_Input_GetMouseButtonUp();
		Register_UnityEngine_Input_GetMouseButtonUp();

		//System.Boolean UnityEngine.Input::get_anyKeyDown()
		void Register_UnityEngine_Input_get_anyKeyDown();
		Register_UnityEngine_Input_get_anyKeyDown();

		//System.Boolean UnityEngine.Input::get_mousePresent()
		void Register_UnityEngine_Input_get_mousePresent();
		Register_UnityEngine_Input_get_mousePresent();

		//System.Boolean UnityEngine.Input::get_touchSupported()
		void Register_UnityEngine_Input_get_touchSupported();
		Register_UnityEngine_Input_get_touchSupported();

		//System.Int32 UnityEngine.Input::get_touchCount()
		void Register_UnityEngine_Input_get_touchCount();
		Register_UnityEngine_Input_get_touchCount();

		//System.Single UnityEngine.Input::GetAxis(System.String)
		void Register_UnityEngine_Input_GetAxis();
		Register_UnityEngine_Input_GetAxis();

		//System.Single UnityEngine.Input::GetAxisRaw(System.String)
		void Register_UnityEngine_Input_GetAxisRaw();
		Register_UnityEngine_Input_GetAxisRaw();

		//System.String UnityEngine.Input::get_compositionString()
		void Register_UnityEngine_Input_get_compositionString();
		Register_UnityEngine_Input_get_compositionString();

		//System.String UnityEngine.Input::get_inputString()
		void Register_UnityEngine_Input_get_inputString();
		Register_UnityEngine_Input_get_inputString();

		//System.Void UnityEngine.Input::GetTouch_Injected(System.Int32,UnityEngine.Touch&)
		void Register_UnityEngine_Input_GetTouch_Injected();
		Register_UnityEngine_Input_GetTouch_Injected();

		//System.Void UnityEngine.Input::get_acceleration_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Input_get_acceleration_Injected();
		Register_UnityEngine_Input_get_acceleration_Injected();

		//System.Void UnityEngine.Input::get_compositionCursorPos_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Input_get_compositionCursorPos_Injected();
		Register_UnityEngine_Input_get_compositionCursorPos_Injected();

		//System.Void UnityEngine.Input::get_mousePosition_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Input_get_mousePosition_Injected();
		Register_UnityEngine_Input_get_mousePosition_Injected();

		//System.Void UnityEngine.Input::get_mouseScrollDelta_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Input_get_mouseScrollDelta_Injected();
		Register_UnityEngine_Input_get_mouseScrollDelta_Injected();

		//System.Void UnityEngine.Input::set_compositionCursorPos_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Input_set_compositionCursorPos_Injected();
		Register_UnityEngine_Input_set_compositionCursorPos_Injected();

		//System.Void UnityEngine.Input::set_imeCompositionMode(UnityEngine.IMECompositionMode)
		void Register_UnityEngine_Input_set_imeCompositionMode();
		Register_UnityEngine_Input_set_imeCompositionMode();

		//System.Void UnityEngine.Input::set_multiTouchEnabled(System.Boolean)
		void Register_UnityEngine_Input_set_multiTouchEnabled();
		Register_UnityEngine_Input_set_multiTouchEnabled();

		//UnityEngine.IMECompositionMode UnityEngine.Input::get_imeCompositionMode()
		void Register_UnityEngine_Input_get_imeCompositionMode();
		Register_UnityEngine_Input_get_imeCompositionMode();

	//End Registrations for type : UnityEngine.Input

	//Start Registrations for type : UnityEngine.IntegratedSubsystem

		//System.Void UnityEngine.IntegratedSubsystem::SetHandle(UnityEngine.IntegratedSubsystem)
		void Register_UnityEngine_IntegratedSubsystem_SetHandle();
		Register_UnityEngine_IntegratedSubsystem_SetHandle();

	//End Registrations for type : UnityEngine.IntegratedSubsystem

	//Start Registrations for type : UnityEngine.iOS.Device

		//System.Void UnityEngine.iOS.Device::SetNoBackupFlag(System.String)
		void Register_UnityEngine_iOS_Device_SetNoBackupFlag();
		Register_UnityEngine_iOS_Device_SetNoBackupFlag();

	//End Registrations for type : UnityEngine.iOS.Device

	//Start Registrations for type : UnityEngine.iOS.LocalNotification

		//System.String UnityEngine.iOS.LocalNotification::get_defaultSoundName()
		void Register_UnityEngine_iOS_LocalNotification_get_defaultSoundName();
		Register_UnityEngine_iOS_LocalNotification_get_defaultSoundName();

		//System.Void UnityEngine.iOS.LocalNotification::PresentNow()
		void Register_UnityEngine_iOS_LocalNotification_PresentNow();
		Register_UnityEngine_iOS_LocalNotification_PresentNow();

		//System.Void UnityEngine.iOS.LocalNotification::Schedule()
		void Register_UnityEngine_iOS_LocalNotification_Schedule();
		Register_UnityEngine_iOS_LocalNotification_Schedule();

		//System.Void UnityEngine.iOS.LocalNotification::set_alertBody(System.String)
		void Register_UnityEngine_iOS_LocalNotification_set_alertBody();
		Register_UnityEngine_iOS_LocalNotification_set_alertBody();

		//System.Void UnityEngine.iOS.LocalNotification::set_applicationIconBadgeNumber(System.Int32)
		void Register_UnityEngine_iOS_LocalNotification_set_applicationIconBadgeNumber();
		Register_UnityEngine_iOS_LocalNotification_set_applicationIconBadgeNumber();

		//System.Void UnityEngine.iOS.LocalNotification::set_fireDateImpl(System.Double)
		void Register_UnityEngine_iOS_LocalNotification_set_fireDateImpl();
		Register_UnityEngine_iOS_LocalNotification_set_fireDateImpl();

		//System.Void UnityEngine.iOS.LocalNotification::set_hasAction(System.Boolean)
		void Register_UnityEngine_iOS_LocalNotification_set_hasAction();
		Register_UnityEngine_iOS_LocalNotification_set_hasAction();

		//System.Void UnityEngine.iOS.LocalNotification::set_soundName(System.String)
		void Register_UnityEngine_iOS_LocalNotification_set_soundName();
		Register_UnityEngine_iOS_LocalNotification_set_soundName();

	//End Registrations for type : UnityEngine.iOS.LocalNotification

	//Start Registrations for type : UnityEngine.iOS.NotificationHelper

		//System.IntPtr UnityEngine.iOS.NotificationHelper::CreateLocal()
		void Register_UnityEngine_iOS_NotificationHelper_CreateLocal();
		Register_UnityEngine_iOS_NotificationHelper_CreateLocal();

		//System.Void UnityEngine.iOS.NotificationHelper::DestroyLocal(System.IntPtr)
		void Register_UnityEngine_iOS_NotificationHelper_DestroyLocal();
		Register_UnityEngine_iOS_NotificationHelper_DestroyLocal();

	//End Registrations for type : UnityEngine.iOS.NotificationHelper

	//Start Registrations for type : UnityEngine.iOS.NotificationServices

		//System.Void UnityEngine.iOS.NotificationServices::CancelAllLocalNotifications()
		void Register_UnityEngine_iOS_NotificationServices_CancelAllLocalNotifications();
		Register_UnityEngine_iOS_NotificationServices_CancelAllLocalNotifications();

		//System.Void UnityEngine.iOS.NotificationServices::ClearLocalNotifications()
		void Register_UnityEngine_iOS_NotificationServices_ClearLocalNotifications();
		Register_UnityEngine_iOS_NotificationServices_ClearLocalNotifications();

		//System.Void UnityEngine.iOS.NotificationServices::Internal_RegisterImpl(UnityEngine.iOS.NotificationType,System.Boolean)
		void Register_UnityEngine_iOS_NotificationServices_Internal_RegisterImpl();
		Register_UnityEngine_iOS_NotificationServices_Internal_RegisterImpl();

	//End Registrations for type : UnityEngine.iOS.NotificationServices

	//Start Registrations for type : UnityEngine.Joint

		//System.Void UnityEngine.Joint::get_connectedAnchor_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Joint_get_connectedAnchor_Injected();
		Register_UnityEngine_Joint_get_connectedAnchor_Injected();

		//System.Void UnityEngine.Joint::set_axis_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Joint_set_axis_Injected();
		Register_UnityEngine_Joint_set_axis_Injected();

		//System.Void UnityEngine.Joint::set_connectedAnchor_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Joint_set_connectedAnchor_Injected();
		Register_UnityEngine_Joint_set_connectedAnchor_Injected();

		//System.Void UnityEngine.Joint::set_connectedBody(UnityEngine.Rigidbody)
		void Register_UnityEngine_Joint_set_connectedBody();
		Register_UnityEngine_Joint_set_connectedBody();

		//System.Void UnityEngine.Joint::set_enableCollision(System.Boolean)
		void Register_UnityEngine_Joint_set_enableCollision();
		Register_UnityEngine_Joint_set_enableCollision();

		//UnityEngine.Rigidbody UnityEngine.Joint::get_connectedBody()
		void Register_UnityEngine_Joint_get_connectedBody();
		Register_UnityEngine_Joint_get_connectedBody();

	//End Registrations for type : UnityEngine.Joint

	//Start Registrations for type : UnityEngine.Joint2D

		//System.Void UnityEngine.Joint2D::set_connectedBody(UnityEngine.Rigidbody2D)
		void Register_UnityEngine_Joint2D_set_connectedBody();
		Register_UnityEngine_Joint2D_set_connectedBody();

		//UnityEngine.Rigidbody2D UnityEngine.Joint2D::get_connectedBody()
		void Register_UnityEngine_Joint2D_get_connectedBody();
		Register_UnityEngine_Joint2D_get_connectedBody();

	//End Registrations for type : UnityEngine.Joint2D

	//Start Registrations for type : UnityEngine.JsonUtility

		//System.String UnityEngine.JsonUtility::ToJsonInternal(System.Object,System.Boolean)
		void Register_UnityEngine_JsonUtility_ToJsonInternal();
		Register_UnityEngine_JsonUtility_ToJsonInternal();

	//End Registrations for type : UnityEngine.JsonUtility

	//Start Registrations for type : UnityEngine.LayerMask

		//System.Int32 UnityEngine.LayerMask::NameToLayer(System.String)
		void Register_UnityEngine_LayerMask_NameToLayer();
		Register_UnityEngine_LayerMask_NameToLayer();

		//System.String UnityEngine.LayerMask::LayerToName(System.Int32)
		void Register_UnityEngine_LayerMask_LayerToName();
		Register_UnityEngine_LayerMask_LayerToName();

	//End Registrations for type : UnityEngine.LayerMask

	//Start Registrations for type : UnityEngine.Light

		//System.Boolean UnityEngine.Light::get_useBoundingSphereOverride()
		void Register_UnityEngine_Light_get_useBoundingSphereOverride();
		Register_UnityEngine_Light_get_useBoundingSphereOverride();

		//System.Boolean UnityEngine.Light::get_useColorTemperature()
		void Register_UnityEngine_Light_get_useColorTemperature();
		Register_UnityEngine_Light_get_useColorTemperature();

		//System.Boolean UnityEngine.Light::get_useShadowMatrixOverride()
		void Register_UnityEngine_Light_get_useShadowMatrixOverride();
		Register_UnityEngine_Light_get_useShadowMatrixOverride();

		//System.Int32 UnityEngine.Light::get_commandBufferCount()
		void Register_UnityEngine_Light_get_commandBufferCount();
		Register_UnityEngine_Light_get_commandBufferCount();

		//System.Int32 UnityEngine.Light::get_cullingMask()
		void Register_UnityEngine_Light_get_cullingMask();
		Register_UnityEngine_Light_get_cullingMask();

		//System.Int32 UnityEngine.Light::get_renderingLayerMask()
		void Register_UnityEngine_Light_get_renderingLayerMask();
		Register_UnityEngine_Light_get_renderingLayerMask();

		//System.Int32 UnityEngine.Light::get_shadowCustomResolution()
		void Register_UnityEngine_Light_get_shadowCustomResolution();
		Register_UnityEngine_Light_get_shadowCustomResolution();

		//System.Single UnityEngine.Light::get_bounceIntensity()
		void Register_UnityEngine_Light_get_bounceIntensity();
		Register_UnityEngine_Light_get_bounceIntensity();

		//System.Single UnityEngine.Light::get_colorTemperature()
		void Register_UnityEngine_Light_get_colorTemperature();
		Register_UnityEngine_Light_get_colorTemperature();

		//System.Single UnityEngine.Light::get_cookieSize()
		void Register_UnityEngine_Light_get_cookieSize();
		Register_UnityEngine_Light_get_cookieSize();

		//System.Single UnityEngine.Light::get_innerSpotAngle()
		void Register_UnityEngine_Light_get_innerSpotAngle();
		Register_UnityEngine_Light_get_innerSpotAngle();

		//System.Single UnityEngine.Light::get_intensity()
		void Register_UnityEngine_Light_get_intensity();
		Register_UnityEngine_Light_get_intensity();

		//System.Single UnityEngine.Light::get_range()
		void Register_UnityEngine_Light_get_range();
		Register_UnityEngine_Light_get_range();

		//System.Single UnityEngine.Light::get_shadowBias()
		void Register_UnityEngine_Light_get_shadowBias();
		Register_UnityEngine_Light_get_shadowBias();

		//System.Single UnityEngine.Light::get_shadowNearPlane()
		void Register_UnityEngine_Light_get_shadowNearPlane();
		Register_UnityEngine_Light_get_shadowNearPlane();

		//System.Single UnityEngine.Light::get_shadowNormalBias()
		void Register_UnityEngine_Light_get_shadowNormalBias();
		Register_UnityEngine_Light_get_shadowNormalBias();

		//System.Single UnityEngine.Light::get_shadowStrength()
		void Register_UnityEngine_Light_get_shadowStrength();
		Register_UnityEngine_Light_get_shadowStrength();

		//System.Single UnityEngine.Light::get_spotAngle()
		void Register_UnityEngine_Light_get_spotAngle();
		Register_UnityEngine_Light_get_spotAngle();

		//System.Single[] UnityEngine.Light::get_layerShadowCullDistances()
		void Register_UnityEngine_Light_get_layerShadowCullDistances();
		Register_UnityEngine_Light_get_layerShadowCullDistances();

		//System.Void UnityEngine.Light::AddCommandBuffer(UnityEngine.Rendering.LightEvent,UnityEngine.Rendering.CommandBuffer,UnityEngine.Rendering.ShadowMapPass)
		void Register_UnityEngine_Light_AddCommandBuffer();
		Register_UnityEngine_Light_AddCommandBuffer();

		//System.Void UnityEngine.Light::AddCommandBufferAsync(UnityEngine.Rendering.LightEvent,UnityEngine.Rendering.CommandBuffer,UnityEngine.Rendering.ShadowMapPass,UnityEngine.Rendering.ComputeQueueType)
		void Register_UnityEngine_Light_AddCommandBufferAsync();
		Register_UnityEngine_Light_AddCommandBufferAsync();

		//System.Void UnityEngine.Light::RemoveAllCommandBuffers()
		void Register_UnityEngine_Light_RemoveAllCommandBuffers();
		Register_UnityEngine_Light_RemoveAllCommandBuffers();

		//System.Void UnityEngine.Light::RemoveCommandBuffer(UnityEngine.Rendering.LightEvent,UnityEngine.Rendering.CommandBuffer)
		void Register_UnityEngine_Light_RemoveCommandBuffer();
		Register_UnityEngine_Light_RemoveCommandBuffer();

		//System.Void UnityEngine.Light::RemoveCommandBuffers(UnityEngine.Rendering.LightEvent)
		void Register_UnityEngine_Light_RemoveCommandBuffers();
		Register_UnityEngine_Light_RemoveCommandBuffers();

		//System.Void UnityEngine.Light::Reset()
		void Register_UnityEngine_Light_Reset();
		Register_UnityEngine_Light_Reset();

		//System.Void UnityEngine.Light::get_bakingOutput_Injected(UnityEngine.LightBakingOutput&)
		void Register_UnityEngine_Light_get_bakingOutput_Injected();
		Register_UnityEngine_Light_get_bakingOutput_Injected();

		//System.Void UnityEngine.Light::get_boundingSphereOverride_Injected(UnityEngine.Vector4&)
		void Register_UnityEngine_Light_get_boundingSphereOverride_Injected();
		Register_UnityEngine_Light_get_boundingSphereOverride_Injected();

		//System.Void UnityEngine.Light::get_color_Injected(UnityEngine.Color&)
		void Register_UnityEngine_Light_get_color_Injected();
		Register_UnityEngine_Light_get_color_Injected();

		//System.Void UnityEngine.Light::get_shadowMatrixOverride_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Light_get_shadowMatrixOverride_Injected();
		Register_UnityEngine_Light_get_shadowMatrixOverride_Injected();

		//System.Void UnityEngine.Light::set_bakingOutput_Injected(UnityEngine.LightBakingOutput&)
		void Register_UnityEngine_Light_set_bakingOutput_Injected();
		Register_UnityEngine_Light_set_bakingOutput_Injected();

		//System.Void UnityEngine.Light::set_bounceIntensity(System.Single)
		void Register_UnityEngine_Light_set_bounceIntensity();
		Register_UnityEngine_Light_set_bounceIntensity();

		//System.Void UnityEngine.Light::set_boundingSphereOverride_Injected(UnityEngine.Vector4&)
		void Register_UnityEngine_Light_set_boundingSphereOverride_Injected();
		Register_UnityEngine_Light_set_boundingSphereOverride_Injected();

		//System.Void UnityEngine.Light::set_colorTemperature(System.Single)
		void Register_UnityEngine_Light_set_colorTemperature();
		Register_UnityEngine_Light_set_colorTemperature();

		//System.Void UnityEngine.Light::set_color_Injected(UnityEngine.Color&)
		void Register_UnityEngine_Light_set_color_Injected();
		Register_UnityEngine_Light_set_color_Injected();

		//System.Void UnityEngine.Light::set_cookie(UnityEngine.Texture)
		void Register_UnityEngine_Light_set_cookie();
		Register_UnityEngine_Light_set_cookie();

		//System.Void UnityEngine.Light::set_cookieSize(System.Single)
		void Register_UnityEngine_Light_set_cookieSize();
		Register_UnityEngine_Light_set_cookieSize();

		//System.Void UnityEngine.Light::set_cullingMask(System.Int32)
		void Register_UnityEngine_Light_set_cullingMask();
		Register_UnityEngine_Light_set_cullingMask();

		//System.Void UnityEngine.Light::set_flare(UnityEngine.Flare)
		void Register_UnityEngine_Light_set_flare();
		Register_UnityEngine_Light_set_flare();

		//System.Void UnityEngine.Light::set_innerSpotAngle(System.Single)
		void Register_UnityEngine_Light_set_innerSpotAngle();
		Register_UnityEngine_Light_set_innerSpotAngle();

		//System.Void UnityEngine.Light::set_intensity(System.Single)
		void Register_UnityEngine_Light_set_intensity();
		Register_UnityEngine_Light_set_intensity();

		//System.Void UnityEngine.Light::set_layerShadowCullDistances(System.Single[])
		void Register_UnityEngine_Light_set_layerShadowCullDistances();
		Register_UnityEngine_Light_set_layerShadowCullDistances();

		//System.Void UnityEngine.Light::set_lightShadowCasterMode(UnityEngine.LightShadowCasterMode)
		void Register_UnityEngine_Light_set_lightShadowCasterMode();
		Register_UnityEngine_Light_set_lightShadowCasterMode();

		//System.Void UnityEngine.Light::set_range(System.Single)
		void Register_UnityEngine_Light_set_range();
		Register_UnityEngine_Light_set_range();

		//System.Void UnityEngine.Light::set_renderMode(UnityEngine.LightRenderMode)
		void Register_UnityEngine_Light_set_renderMode();
		Register_UnityEngine_Light_set_renderMode();

		//System.Void UnityEngine.Light::set_renderingLayerMask(System.Int32)
		void Register_UnityEngine_Light_set_renderingLayerMask();
		Register_UnityEngine_Light_set_renderingLayerMask();

		//System.Void UnityEngine.Light::set_shadowBias(System.Single)
		void Register_UnityEngine_Light_set_shadowBias();
		Register_UnityEngine_Light_set_shadowBias();

		//System.Void UnityEngine.Light::set_shadowCustomResolution(System.Int32)
		void Register_UnityEngine_Light_set_shadowCustomResolution();
		Register_UnityEngine_Light_set_shadowCustomResolution();

		//System.Void UnityEngine.Light::set_shadowMatrixOverride_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Light_set_shadowMatrixOverride_Injected();
		Register_UnityEngine_Light_set_shadowMatrixOverride_Injected();

		//System.Void UnityEngine.Light::set_shadowNearPlane(System.Single)
		void Register_UnityEngine_Light_set_shadowNearPlane();
		Register_UnityEngine_Light_set_shadowNearPlane();

		//System.Void UnityEngine.Light::set_shadowNormalBias(System.Single)
		void Register_UnityEngine_Light_set_shadowNormalBias();
		Register_UnityEngine_Light_set_shadowNormalBias();

		//System.Void UnityEngine.Light::set_shadowResolution(UnityEngine.Rendering.LightShadowResolution)
		void Register_UnityEngine_Light_set_shadowResolution();
		Register_UnityEngine_Light_set_shadowResolution();

		//System.Void UnityEngine.Light::set_shadowStrength(System.Single)
		void Register_UnityEngine_Light_set_shadowStrength();
		Register_UnityEngine_Light_set_shadowStrength();

		//System.Void UnityEngine.Light::set_shadows(UnityEngine.LightShadows)
		void Register_UnityEngine_Light_set_shadows();
		Register_UnityEngine_Light_set_shadows();

		//System.Void UnityEngine.Light::set_shape(UnityEngine.LightShape)
		void Register_UnityEngine_Light_set_shape();
		Register_UnityEngine_Light_set_shape();

		//System.Void UnityEngine.Light::set_spotAngle(System.Single)
		void Register_UnityEngine_Light_set_spotAngle();
		Register_UnityEngine_Light_set_spotAngle();

		//System.Void UnityEngine.Light::set_type(UnityEngine.LightType)
		void Register_UnityEngine_Light_set_type();
		Register_UnityEngine_Light_set_type();

		//System.Void UnityEngine.Light::set_useBoundingSphereOverride(System.Boolean)
		void Register_UnityEngine_Light_set_useBoundingSphereOverride();
		Register_UnityEngine_Light_set_useBoundingSphereOverride();

		//System.Void UnityEngine.Light::set_useColorTemperature(System.Boolean)
		void Register_UnityEngine_Light_set_useColorTemperature();
		Register_UnityEngine_Light_set_useColorTemperature();

		//System.Void UnityEngine.Light::set_useShadowMatrixOverride(System.Boolean)
		void Register_UnityEngine_Light_set_useShadowMatrixOverride();
		Register_UnityEngine_Light_set_useShadowMatrixOverride();

		//UnityEngine.Flare UnityEngine.Light::get_flare()
		void Register_UnityEngine_Light_get_flare();
		Register_UnityEngine_Light_get_flare();

		//UnityEngine.LightRenderMode UnityEngine.Light::get_renderMode()
		void Register_UnityEngine_Light_get_renderMode();
		Register_UnityEngine_Light_get_renderMode();

		//UnityEngine.LightShadowCasterMode UnityEngine.Light::get_lightShadowCasterMode()
		void Register_UnityEngine_Light_get_lightShadowCasterMode();
		Register_UnityEngine_Light_get_lightShadowCasterMode();

		//UnityEngine.LightShadows UnityEngine.Light::get_shadows()
		void Register_UnityEngine_Light_get_shadows();
		Register_UnityEngine_Light_get_shadows();

		//UnityEngine.LightShape UnityEngine.Light::get_shape()
		void Register_UnityEngine_Light_get_shape();
		Register_UnityEngine_Light_get_shape();

		//UnityEngine.LightType UnityEngine.Light::get_type()
		void Register_UnityEngine_Light_get_type();
		Register_UnityEngine_Light_get_type();

		//UnityEngine.Light[] UnityEngine.Light::GetLights(UnityEngine.LightType,System.Int32)
		void Register_UnityEngine_Light_GetLights();
		Register_UnityEngine_Light_GetLights();

		//UnityEngine.Rendering.CommandBuffer[] UnityEngine.Light::GetCommandBuffers(UnityEngine.Rendering.LightEvent)
		void Register_UnityEngine_Light_GetCommandBuffers();
		Register_UnityEngine_Light_GetCommandBuffers();

		//UnityEngine.Rendering.LightShadowResolution UnityEngine.Light::get_shadowResolution()
		void Register_UnityEngine_Light_get_shadowResolution();
		Register_UnityEngine_Light_get_shadowResolution();

		//UnityEngine.Texture UnityEngine.Light::get_cookie()
		void Register_UnityEngine_Light_get_cookie();
		Register_UnityEngine_Light_get_cookie();

	//End Registrations for type : UnityEngine.Light

	//Start Registrations for type : UnityEngine.LineRenderer

		//System.Void UnityEngine.LineRenderer::SetPosition_Injected(System.Int32,UnityEngine.Vector3&)
		void Register_UnityEngine_LineRenderer_SetPosition_Injected();
		Register_UnityEngine_LineRenderer_SetPosition_Injected();

		//System.Void UnityEngine.LineRenderer::set_endColor_Injected(UnityEngine.Color&)
		void Register_UnityEngine_LineRenderer_set_endColor_Injected();
		Register_UnityEngine_LineRenderer_set_endColor_Injected();

		//System.Void UnityEngine.LineRenderer::set_startColor_Injected(UnityEngine.Color&)
		void Register_UnityEngine_LineRenderer_set_startColor_Injected();
		Register_UnityEngine_LineRenderer_set_startColor_Injected();

	//End Registrations for type : UnityEngine.LineRenderer

	//Start Registrations for type : UnityEngine.LocationService

		//System.Boolean UnityEngine.LocationService::IsServiceEnabledByUser()
		void Register_UnityEngine_LocationService_IsServiceEnabledByUser();
		Register_UnityEngine_LocationService_IsServiceEnabledByUser();

	//End Registrations for type : UnityEngine.LocationService

	//Start Registrations for type : UnityEngine.Material

		//System.Boolean UnityEngine.Material::GetShaderPassEnabled(System.String)
		void Register_UnityEngine_Material_GetShaderPassEnabled();
		Register_UnityEngine_Material_GetShaderPassEnabled();

		//System.Boolean UnityEngine.Material::HasProperty(System.Int32)
		void Register_UnityEngine_Material_HasProperty();
		Register_UnityEngine_Material_HasProperty();

		//System.Boolean UnityEngine.Material::IsKeywordEnabled(System.String)
		void Register_UnityEngine_Material_IsKeywordEnabled();
		Register_UnityEngine_Material_IsKeywordEnabled();

		//System.Boolean UnityEngine.Material::SetPass(System.Int32)
		void Register_UnityEngine_Material_SetPass();
		Register_UnityEngine_Material_SetPass();

		//System.Boolean UnityEngine.Material::get_doubleSidedGI()
		void Register_UnityEngine_Material_get_doubleSidedGI();
		Register_UnityEngine_Material_get_doubleSidedGI();

		//System.Boolean UnityEngine.Material::get_enableInstancing()
		void Register_UnityEngine_Material_get_enableInstancing();
		Register_UnityEngine_Material_get_enableInstancing();

		//System.Int32 UnityEngine.Material::ComputeCRC()
		void Register_UnityEngine_Material_ComputeCRC();
		Register_UnityEngine_Material_ComputeCRC();

		//System.Int32 UnityEngine.Material::FindPass(System.String)
		void Register_UnityEngine_Material_FindPass();
		Register_UnityEngine_Material_FindPass();

		//System.Int32 UnityEngine.Material::GetColorArrayCountImpl(System.Int32)
		void Register_UnityEngine_Material_GetColorArrayCountImpl();
		Register_UnityEngine_Material_GetColorArrayCountImpl();

		//System.Int32 UnityEngine.Material::GetFirstPropertyNameIdByAttribute(UnityEngine.Rendering.ShaderPropertyFlags)
		void Register_UnityEngine_Material_GetFirstPropertyNameIdByAttribute();
		Register_UnityEngine_Material_GetFirstPropertyNameIdByAttribute();

		//System.Int32 UnityEngine.Material::GetFloatArrayCountImpl(System.Int32)
		void Register_UnityEngine_Material_GetFloatArrayCountImpl();
		Register_UnityEngine_Material_GetFloatArrayCountImpl();

		//System.Int32 UnityEngine.Material::GetMatrixArrayCountImpl(System.Int32)
		void Register_UnityEngine_Material_GetMatrixArrayCountImpl();
		Register_UnityEngine_Material_GetMatrixArrayCountImpl();

		//System.Int32 UnityEngine.Material::GetVectorArrayCountImpl(System.Int32)
		void Register_UnityEngine_Material_GetVectorArrayCountImpl();
		Register_UnityEngine_Material_GetVectorArrayCountImpl();

		//System.Int32 UnityEngine.Material::get_passCount()
		void Register_UnityEngine_Material_get_passCount();
		Register_UnityEngine_Material_get_passCount();

		//System.Int32 UnityEngine.Material::get_renderQueue()
		void Register_UnityEngine_Material_get_renderQueue();
		Register_UnityEngine_Material_get_renderQueue();

		//System.Int32[] UnityEngine.Material::GetTexturePropertyNameIDs()
		void Register_UnityEngine_Material_GetTexturePropertyNameIDs();
		Register_UnityEngine_Material_GetTexturePropertyNameIDs();

		//System.Single UnityEngine.Material::GetFloatImpl(System.Int32)
		void Register_UnityEngine_Material_GetFloatImpl();
		Register_UnityEngine_Material_GetFloatImpl();

		//System.Single[] UnityEngine.Material::GetFloatArrayImpl(System.Int32)
		void Register_UnityEngine_Material_GetFloatArrayImpl();
		Register_UnityEngine_Material_GetFloatArrayImpl();

		//System.String UnityEngine.Material::GetPassName(System.Int32)
		void Register_UnityEngine_Material_GetPassName();
		Register_UnityEngine_Material_GetPassName();

		//System.String UnityEngine.Material::GetTagImpl(System.String,System.Boolean,System.String)
		void Register_UnityEngine_Material_GetTagImpl();
		Register_UnityEngine_Material_GetTagImpl();

		//System.String[] UnityEngine.Material::GetShaderKeywords()
		void Register_UnityEngine_Material_GetShaderKeywords();
		Register_UnityEngine_Material_GetShaderKeywords();

		//System.String[] UnityEngine.Material::GetTexturePropertyNames()
		void Register_UnityEngine_Material_GetTexturePropertyNames();
		Register_UnityEngine_Material_GetTexturePropertyNames();

		//System.Void UnityEngine.Material::CopyPropertiesFromMaterial(UnityEngine.Material)
		void Register_UnityEngine_Material_CopyPropertiesFromMaterial();
		Register_UnityEngine_Material_CopyPropertiesFromMaterial();

		//System.Void UnityEngine.Material::CreateWithMaterial(UnityEngine.Material,UnityEngine.Material)
		void Register_UnityEngine_Material_CreateWithMaterial();
		Register_UnityEngine_Material_CreateWithMaterial();

		//System.Void UnityEngine.Material::CreateWithShader(UnityEngine.Material,UnityEngine.Shader)
		void Register_UnityEngine_Material_CreateWithShader();
		Register_UnityEngine_Material_CreateWithShader();

		//System.Void UnityEngine.Material::CreateWithString(UnityEngine.Material)
		void Register_UnityEngine_Material_CreateWithString();
		Register_UnityEngine_Material_CreateWithString();

		//System.Void UnityEngine.Material::DisableKeyword(System.String)
		void Register_UnityEngine_Material_DisableKeyword();
		Register_UnityEngine_Material_DisableKeyword();

		//System.Void UnityEngine.Material::EnableKeyword(System.String)
		void Register_UnityEngine_Material_EnableKeyword();
		Register_UnityEngine_Material_EnableKeyword();

		//System.Void UnityEngine.Material::ExtractColorArrayImpl(System.Int32,UnityEngine.Color[])
		void Register_UnityEngine_Material_ExtractColorArrayImpl();
		Register_UnityEngine_Material_ExtractColorArrayImpl();

		//System.Void UnityEngine.Material::ExtractFloatArrayImpl(System.Int32,System.Single[])
		void Register_UnityEngine_Material_ExtractFloatArrayImpl();
		Register_UnityEngine_Material_ExtractFloatArrayImpl();

		//System.Void UnityEngine.Material::ExtractMatrixArrayImpl(System.Int32,UnityEngine.Matrix4x4[])
		void Register_UnityEngine_Material_ExtractMatrixArrayImpl();
		Register_UnityEngine_Material_ExtractMatrixArrayImpl();

		//System.Void UnityEngine.Material::ExtractVectorArrayImpl(System.Int32,UnityEngine.Vector4[])
		void Register_UnityEngine_Material_ExtractVectorArrayImpl();
		Register_UnityEngine_Material_ExtractVectorArrayImpl();

		//System.Void UnityEngine.Material::GetColorImpl_Injected(System.Int32,UnityEngine.Color&)
		void Register_UnityEngine_Material_GetColorImpl_Injected();
		Register_UnityEngine_Material_GetColorImpl_Injected();

		//System.Void UnityEngine.Material::GetMatrixImpl_Injected(System.Int32,UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Material_GetMatrixImpl_Injected();
		Register_UnityEngine_Material_GetMatrixImpl_Injected();

		//System.Void UnityEngine.Material::GetTexturePropertyNameIDsInternal(System.Object)
		void Register_UnityEngine_Material_GetTexturePropertyNameIDsInternal();
		Register_UnityEngine_Material_GetTexturePropertyNameIDsInternal();

		//System.Void UnityEngine.Material::GetTexturePropertyNamesInternal(System.Object)
		void Register_UnityEngine_Material_GetTexturePropertyNamesInternal();
		Register_UnityEngine_Material_GetTexturePropertyNamesInternal();

		//System.Void UnityEngine.Material::GetTextureScaleAndOffsetImpl_Injected(System.Int32,UnityEngine.Vector4&)
		void Register_UnityEngine_Material_GetTextureScaleAndOffsetImpl_Injected();
		Register_UnityEngine_Material_GetTextureScaleAndOffsetImpl_Injected();

		//System.Void UnityEngine.Material::Lerp(UnityEngine.Material,UnityEngine.Material,System.Single)
		void Register_UnityEngine_Material_Lerp();
		Register_UnityEngine_Material_Lerp();

		//System.Void UnityEngine.Material::SetBufferImpl(System.Int32,UnityEngine.ComputeBuffer)
		void Register_UnityEngine_Material_SetBufferImpl();
		Register_UnityEngine_Material_SetBufferImpl();

		//System.Void UnityEngine.Material::SetColorArrayImpl(System.Int32,UnityEngine.Color[],System.Int32)
		void Register_UnityEngine_Material_SetColorArrayImpl();
		Register_UnityEngine_Material_SetColorArrayImpl();

		//System.Void UnityEngine.Material::SetColorImpl_Injected(System.Int32,UnityEngine.Color&)
		void Register_UnityEngine_Material_SetColorImpl_Injected();
		Register_UnityEngine_Material_SetColorImpl_Injected();

		//System.Void UnityEngine.Material::SetConstantBufferImpl(System.Int32,UnityEngine.ComputeBuffer,System.Int32,System.Int32)
		void Register_UnityEngine_Material_SetConstantBufferImpl();
		Register_UnityEngine_Material_SetConstantBufferImpl();

		//System.Void UnityEngine.Material::SetFloatArrayImpl(System.Int32,System.Single[],System.Int32)
		void Register_UnityEngine_Material_SetFloatArrayImpl();
		Register_UnityEngine_Material_SetFloatArrayImpl();

		//System.Void UnityEngine.Material::SetFloatImpl(System.Int32,System.Single)
		void Register_UnityEngine_Material_SetFloatImpl();
		Register_UnityEngine_Material_SetFloatImpl();

		//System.Void UnityEngine.Material::SetMatrixArrayImpl(System.Int32,UnityEngine.Matrix4x4[],System.Int32)
		void Register_UnityEngine_Material_SetMatrixArrayImpl();
		Register_UnityEngine_Material_SetMatrixArrayImpl();

		//System.Void UnityEngine.Material::SetMatrixImpl_Injected(System.Int32,UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Material_SetMatrixImpl_Injected();
		Register_UnityEngine_Material_SetMatrixImpl_Injected();

		//System.Void UnityEngine.Material::SetOverrideTag(System.String,System.String)
		void Register_UnityEngine_Material_SetOverrideTag();
		Register_UnityEngine_Material_SetOverrideTag();

		//System.Void UnityEngine.Material::SetRenderTextureImpl(System.Int32,UnityEngine.RenderTexture,UnityEngine.Rendering.RenderTextureSubElement)
		void Register_UnityEngine_Material_SetRenderTextureImpl();
		Register_UnityEngine_Material_SetRenderTextureImpl();

		//System.Void UnityEngine.Material::SetShaderKeywords(System.String[])
		void Register_UnityEngine_Material_SetShaderKeywords();
		Register_UnityEngine_Material_SetShaderKeywords();

		//System.Void UnityEngine.Material::SetShaderPassEnabled(System.String,System.Boolean)
		void Register_UnityEngine_Material_SetShaderPassEnabled();
		Register_UnityEngine_Material_SetShaderPassEnabled();

		//System.Void UnityEngine.Material::SetTextureImpl(System.Int32,UnityEngine.Texture)
		void Register_UnityEngine_Material_SetTextureImpl();
		Register_UnityEngine_Material_SetTextureImpl();

		//System.Void UnityEngine.Material::SetTextureOffsetImpl_Injected(System.Int32,UnityEngine.Vector2&)
		void Register_UnityEngine_Material_SetTextureOffsetImpl_Injected();
		Register_UnityEngine_Material_SetTextureOffsetImpl_Injected();

		//System.Void UnityEngine.Material::SetTextureScaleImpl_Injected(System.Int32,UnityEngine.Vector2&)
		void Register_UnityEngine_Material_SetTextureScaleImpl_Injected();
		Register_UnityEngine_Material_SetTextureScaleImpl_Injected();

		//System.Void UnityEngine.Material::SetVectorArrayImpl(System.Int32,UnityEngine.Vector4[],System.Int32)
		void Register_UnityEngine_Material_SetVectorArrayImpl();
		Register_UnityEngine_Material_SetVectorArrayImpl();

		//System.Void UnityEngine.Material::set_doubleSidedGI(System.Boolean)
		void Register_UnityEngine_Material_set_doubleSidedGI();
		Register_UnityEngine_Material_set_doubleSidedGI();

		//System.Void UnityEngine.Material::set_enableInstancing(System.Boolean)
		void Register_UnityEngine_Material_set_enableInstancing();
		Register_UnityEngine_Material_set_enableInstancing();

		//System.Void UnityEngine.Material::set_globalIlluminationFlags(UnityEngine.MaterialGlobalIlluminationFlags)
		void Register_UnityEngine_Material_set_globalIlluminationFlags();
		Register_UnityEngine_Material_set_globalIlluminationFlags();

		//System.Void UnityEngine.Material::set_renderQueue(System.Int32)
		void Register_UnityEngine_Material_set_renderQueue();
		Register_UnityEngine_Material_set_renderQueue();

		//System.Void UnityEngine.Material::set_shader(UnityEngine.Shader)
		void Register_UnityEngine_Material_set_shader();
		Register_UnityEngine_Material_set_shader();

		//UnityEngine.Color[] UnityEngine.Material::GetColorArrayImpl(System.Int32)
		void Register_UnityEngine_Material_GetColorArrayImpl();
		Register_UnityEngine_Material_GetColorArrayImpl();

		//UnityEngine.MaterialGlobalIlluminationFlags UnityEngine.Material::get_globalIlluminationFlags()
		void Register_UnityEngine_Material_get_globalIlluminationFlags();
		Register_UnityEngine_Material_get_globalIlluminationFlags();

		//UnityEngine.Matrix4x4[] UnityEngine.Material::GetMatrixArrayImpl(System.Int32)
		void Register_UnityEngine_Material_GetMatrixArrayImpl();
		Register_UnityEngine_Material_GetMatrixArrayImpl();

		//UnityEngine.Shader UnityEngine.Material::get_shader()
		void Register_UnityEngine_Material_get_shader();
		Register_UnityEngine_Material_get_shader();

		//UnityEngine.Texture UnityEngine.Material::GetTextureImpl(System.Int32)
		void Register_UnityEngine_Material_GetTextureImpl();
		Register_UnityEngine_Material_GetTextureImpl();

		//UnityEngine.Vector4[] UnityEngine.Material::GetVectorArrayImpl(System.Int32)
		void Register_UnityEngine_Material_GetVectorArrayImpl();
		Register_UnityEngine_Material_GetVectorArrayImpl();

	//End Registrations for type : UnityEngine.Material

	//Start Registrations for type : UnityEngine.MaterialPropertyBlock

		//System.IntPtr UnityEngine.MaterialPropertyBlock::CreateImpl()
		void Register_UnityEngine_MaterialPropertyBlock_CreateImpl();
		Register_UnityEngine_MaterialPropertyBlock_CreateImpl();

		//System.Void UnityEngine.MaterialPropertyBlock::DestroyImpl(System.IntPtr)
		void Register_UnityEngine_MaterialPropertyBlock_DestroyImpl();
		Register_UnityEngine_MaterialPropertyBlock_DestroyImpl();

		//System.Void UnityEngine.MaterialPropertyBlock::SetColorImpl_Injected(System.Int32,UnityEngine.Color&)
		void Register_UnityEngine_MaterialPropertyBlock_SetColorImpl_Injected();
		Register_UnityEngine_MaterialPropertyBlock_SetColorImpl_Injected();

		//System.Void UnityEngine.MaterialPropertyBlock::SetTextureImpl(System.Int32,UnityEngine.Texture)
		void Register_UnityEngine_MaterialPropertyBlock_SetTextureImpl();
		Register_UnityEngine_MaterialPropertyBlock_SetTextureImpl();

	//End Registrations for type : UnityEngine.MaterialPropertyBlock

	//Start Registrations for type : UnityEngine.Mathf

		//System.Boolean UnityEngine.Mathf::IsPowerOfTwo(System.Int32)
		void Register_UnityEngine_Mathf_IsPowerOfTwo();
		Register_UnityEngine_Mathf_IsPowerOfTwo();

		//System.Single UnityEngine.Mathf::GammaToLinearSpace(System.Single)
		void Register_UnityEngine_Mathf_GammaToLinearSpace();
		Register_UnityEngine_Mathf_GammaToLinearSpace();

		//System.Single UnityEngine.Mathf::LinearToGammaSpace(System.Single)
		void Register_UnityEngine_Mathf_LinearToGammaSpace();
		Register_UnityEngine_Mathf_LinearToGammaSpace();

		//System.Single UnityEngine.Mathf::PerlinNoise(System.Single,System.Single)
		void Register_UnityEngine_Mathf_PerlinNoise();
		Register_UnityEngine_Mathf_PerlinNoise();

	//End Registrations for type : UnityEngine.Mathf

	//Start Registrations for type : UnityEngine.Matrix4x4

		//System.Void UnityEngine.Matrix4x4::Inverse_Injected(UnityEngine.Matrix4x4&,UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Matrix4x4_Inverse_Injected();
		Register_UnityEngine_Matrix4x4_Inverse_Injected();

		//System.Void UnityEngine.Matrix4x4::TRS_Injected(UnityEngine.Vector3&,UnityEngine.Quaternion&,UnityEngine.Vector3&,UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Matrix4x4_TRS_Injected();
		Register_UnityEngine_Matrix4x4_TRS_Injected();

	//End Registrations for type : UnityEngine.Matrix4x4

	//Start Registrations for type : UnityEngine.Mesh

		//System.Array UnityEngine.Mesh::GetAllocArrayFromChannelImpl(UnityEngine.Rendering.VertexAttribute,UnityEngine.Rendering.VertexAttributeFormat,System.Int32)
		void Register_UnityEngine_Mesh_GetAllocArrayFromChannelImpl();
		Register_UnityEngine_Mesh_GetAllocArrayFromChannelImpl();

		//System.Boolean UnityEngine.Mesh::HasVertexAttribute(UnityEngine.Rendering.VertexAttribute)
		void Register_UnityEngine_Mesh_HasVertexAttribute();
		Register_UnityEngine_Mesh_HasVertexAttribute();

		//System.Boolean UnityEngine.Mesh::get_canAccess()
		void Register_UnityEngine_Mesh_get_canAccess();
		Register_UnityEngine_Mesh_get_canAccess();

		//System.Int32 UnityEngine.Mesh::get_subMeshCount()
		void Register_UnityEngine_Mesh_get_subMeshCount();
		Register_UnityEngine_Mesh_get_subMeshCount();

		//System.Int32 UnityEngine.Mesh::get_vertexCount()
		void Register_UnityEngine_Mesh_get_vertexCount();
		Register_UnityEngine_Mesh_get_vertexCount();

		//System.Int32[] UnityEngine.Mesh::GetIndicesImpl(System.Int32,System.Boolean)
		void Register_UnityEngine_Mesh_GetIndicesImpl();
		Register_UnityEngine_Mesh_GetIndicesImpl();

		//System.Void UnityEngine.Mesh::ClearImpl(System.Boolean)
		void Register_UnityEngine_Mesh_ClearImpl();
		Register_UnityEngine_Mesh_ClearImpl();

		//System.Void UnityEngine.Mesh::GetArrayFromChannelImpl(UnityEngine.Rendering.VertexAttribute,UnityEngine.Rendering.VertexAttributeFormat,System.Int32,System.Array)
		void Register_UnityEngine_Mesh_GetArrayFromChannelImpl();
		Register_UnityEngine_Mesh_GetArrayFromChannelImpl();

		//System.Void UnityEngine.Mesh::Internal_Create(UnityEngine.Mesh)
		void Register_UnityEngine_Mesh_Internal_Create();
		Register_UnityEngine_Mesh_Internal_Create();

		//System.Void UnityEngine.Mesh::MarkDynamicImpl()
		void Register_UnityEngine_Mesh_MarkDynamicImpl();
		Register_UnityEngine_Mesh_MarkDynamicImpl();

		//System.Void UnityEngine.Mesh::PrintErrorCantAccessChannel(UnityEngine.Rendering.VertexAttribute)
		void Register_UnityEngine_Mesh_PrintErrorCantAccessChannel();
		Register_UnityEngine_Mesh_PrintErrorCantAccessChannel();

		//System.Void UnityEngine.Mesh::RecalculateBoundsImpl(UnityEngine.Rendering.MeshUpdateFlags)
		void Register_UnityEngine_Mesh_RecalculateBoundsImpl();
		Register_UnityEngine_Mesh_RecalculateBoundsImpl();

		//System.Void UnityEngine.Mesh::RecalculateNormalsImpl(UnityEngine.Rendering.MeshUpdateFlags)
		void Register_UnityEngine_Mesh_RecalculateNormalsImpl();
		Register_UnityEngine_Mesh_RecalculateNormalsImpl();

		//System.Void UnityEngine.Mesh::SetArrayForChannelImpl(UnityEngine.Rendering.VertexAttribute,UnityEngine.Rendering.VertexAttributeFormat,System.Int32,System.Array,System.Int32,System.Int32,System.Int32,UnityEngine.Rendering.MeshUpdateFlags)
		void Register_UnityEngine_Mesh_SetArrayForChannelImpl();
		Register_UnityEngine_Mesh_SetArrayForChannelImpl();

		//System.Void UnityEngine.Mesh::SetIndicesImpl(System.Int32,UnityEngine.MeshTopology,UnityEngine.Rendering.IndexFormat,System.Array,System.Int32,System.Int32,System.Boolean,System.Int32)
		void Register_UnityEngine_Mesh_SetIndicesImpl();
		Register_UnityEngine_Mesh_SetIndicesImpl();

		//System.Void UnityEngine.Mesh::get_bounds_Injected(UnityEngine.Bounds&)
		void Register_UnityEngine_Mesh_get_bounds_Injected();
		Register_UnityEngine_Mesh_get_bounds_Injected();

		//System.Void UnityEngine.Mesh::set_bounds_Injected(UnityEngine.Bounds&)
		void Register_UnityEngine_Mesh_set_bounds_Injected();
		Register_UnityEngine_Mesh_set_bounds_Injected();

		//System.Void UnityEngine.Mesh::set_subMeshCount(System.Int32)
		void Register_UnityEngine_Mesh_set_subMeshCount();
		Register_UnityEngine_Mesh_set_subMeshCount();

	//End Registrations for type : UnityEngine.Mesh

	//Start Registrations for type : UnityEngine.MeshFilter

		//System.Void UnityEngine.MeshFilter::set_mesh(UnityEngine.Mesh)
		void Register_UnityEngine_MeshFilter_set_mesh();
		Register_UnityEngine_MeshFilter_set_mesh();

		//System.Void UnityEngine.MeshFilter::set_sharedMesh(UnityEngine.Mesh)
		void Register_UnityEngine_MeshFilter_set_sharedMesh();
		Register_UnityEngine_MeshFilter_set_sharedMesh();

		//UnityEngine.Mesh UnityEngine.MeshFilter::get_mesh()
		void Register_UnityEngine_MeshFilter_get_mesh();
		Register_UnityEngine_MeshFilter_get_mesh();

		//UnityEngine.Mesh UnityEngine.MeshFilter::get_sharedMesh()
		void Register_UnityEngine_MeshFilter_get_sharedMesh();
		Register_UnityEngine_MeshFilter_get_sharedMesh();

	//End Registrations for type : UnityEngine.MeshFilter

	//Start Registrations for type : UnityEngine.Microphone

		//System.Boolean UnityEngine.Microphone::IsRecording(System.Int32)
		void Register_UnityEngine_Microphone_IsRecording();
		Register_UnityEngine_Microphone_IsRecording();

		//System.Int32 UnityEngine.Microphone::GetMicrophoneDeviceIDFromName(System.String)
		void Register_UnityEngine_Microphone_GetMicrophoneDeviceIDFromName();
		Register_UnityEngine_Microphone_GetMicrophoneDeviceIDFromName();

		//System.Int32 UnityEngine.Microphone::GetRecordPosition(System.Int32)
		void Register_UnityEngine_Microphone_GetRecordPosition();
		Register_UnityEngine_Microphone_GetRecordPosition();

		//System.String[] UnityEngine.Microphone::get_devices()
		void Register_UnityEngine_Microphone_get_devices();
		Register_UnityEngine_Microphone_get_devices();

		//System.Void UnityEngine.Microphone::EndRecord(System.Int32)
		void Register_UnityEngine_Microphone_EndRecord();
		Register_UnityEngine_Microphone_EndRecord();

		//UnityEngine.AudioClip UnityEngine.Microphone::StartRecord(System.Int32,System.Boolean,System.Single,System.Int32)
		void Register_UnityEngine_Microphone_StartRecord();
		Register_UnityEngine_Microphone_StartRecord();

	//End Registrations for type : UnityEngine.Microphone

	//Start Registrations for type : UnityEngine.MonoBehaviour

		//System.Boolean UnityEngine.MonoBehaviour::Internal_IsInvokingAll(UnityEngine.MonoBehaviour)
		void Register_UnityEngine_MonoBehaviour_Internal_IsInvokingAll();
		Register_UnityEngine_MonoBehaviour_Internal_IsInvokingAll();

		//System.Boolean UnityEngine.MonoBehaviour::IsInvoking(UnityEngine.MonoBehaviour,System.String)
		void Register_UnityEngine_MonoBehaviour_IsInvoking();
		Register_UnityEngine_MonoBehaviour_IsInvoking();

		//System.Boolean UnityEngine.MonoBehaviour::IsObjectMonoBehaviour(UnityEngine.Object)
		void Register_UnityEngine_MonoBehaviour_IsObjectMonoBehaviour();
		Register_UnityEngine_MonoBehaviour_IsObjectMonoBehaviour();

		//System.Boolean UnityEngine.MonoBehaviour::get_useGUILayout()
		void Register_UnityEngine_MonoBehaviour_get_useGUILayout();
		Register_UnityEngine_MonoBehaviour_get_useGUILayout();

		//System.String UnityEngine.MonoBehaviour::GetScriptClassName()
		void Register_UnityEngine_MonoBehaviour_GetScriptClassName();
		Register_UnityEngine_MonoBehaviour_GetScriptClassName();

		//System.Void UnityEngine.MonoBehaviour::CancelInvoke(UnityEngine.MonoBehaviour,System.String)
		void Register_UnityEngine_MonoBehaviour_CancelInvoke();
		Register_UnityEngine_MonoBehaviour_CancelInvoke();

		//System.Void UnityEngine.MonoBehaviour::Internal_CancelInvokeAll(UnityEngine.MonoBehaviour)
		void Register_UnityEngine_MonoBehaviour_Internal_CancelInvokeAll();
		Register_UnityEngine_MonoBehaviour_Internal_CancelInvokeAll();

		//System.Void UnityEngine.MonoBehaviour::InvokeDelayed(UnityEngine.MonoBehaviour,System.String,System.Single,System.Single)
		void Register_UnityEngine_MonoBehaviour_InvokeDelayed();
		Register_UnityEngine_MonoBehaviour_InvokeDelayed();

		//System.Void UnityEngine.MonoBehaviour::StopAllCoroutines()
		void Register_UnityEngine_MonoBehaviour_StopAllCoroutines();
		Register_UnityEngine_MonoBehaviour_StopAllCoroutines();

		//System.Void UnityEngine.MonoBehaviour::StopCoroutine(System.String)
		void Register_UnityEngine_MonoBehaviour_StopCoroutine();
		Register_UnityEngine_MonoBehaviour_StopCoroutine();

		//System.Void UnityEngine.MonoBehaviour::StopCoroutineFromEnumeratorManaged(System.Collections.IEnumerator)
		void Register_UnityEngine_MonoBehaviour_StopCoroutineFromEnumeratorManaged();
		Register_UnityEngine_MonoBehaviour_StopCoroutineFromEnumeratorManaged();

		//System.Void UnityEngine.MonoBehaviour::StopCoroutineManaged(UnityEngine.Coroutine)
		void Register_UnityEngine_MonoBehaviour_StopCoroutineManaged();
		Register_UnityEngine_MonoBehaviour_StopCoroutineManaged();

		//System.Void UnityEngine.MonoBehaviour::set_useGUILayout(System.Boolean)
		void Register_UnityEngine_MonoBehaviour_set_useGUILayout();
		Register_UnityEngine_MonoBehaviour_set_useGUILayout();

		//UnityEngine.Coroutine UnityEngine.MonoBehaviour::StartCoroutineManaged(System.String,System.Object)
		void Register_UnityEngine_MonoBehaviour_StartCoroutineManaged();
		Register_UnityEngine_MonoBehaviour_StartCoroutineManaged();

		//UnityEngine.Coroutine UnityEngine.MonoBehaviour::StartCoroutineManaged2(System.Collections.IEnumerator)
		void Register_UnityEngine_MonoBehaviour_StartCoroutineManaged2();
		Register_UnityEngine_MonoBehaviour_StartCoroutineManaged2();

	//End Registrations for type : UnityEngine.MonoBehaviour

	//Start Registrations for type : UnityEngine.Networking.CertificateHandler

		//System.Void UnityEngine.Networking.CertificateHandler::Release()
		void Register_UnityEngine_Networking_CertificateHandler_Release();
		Register_UnityEngine_Networking_CertificateHandler_Release();

	//End Registrations for type : UnityEngine.Networking.CertificateHandler

	//Start Registrations for type : UnityEngine.Networking.DownloadHandler

		//System.Byte[] UnityEngine.Networking.DownloadHandler::InternalGetByteArray(UnityEngine.Networking.DownloadHandler)
		void Register_UnityEngine_Networking_DownloadHandler_InternalGetByteArray();
		Register_UnityEngine_Networking_DownloadHandler_InternalGetByteArray();

		//System.String UnityEngine.Networking.DownloadHandler::GetContentType()
		void Register_UnityEngine_Networking_DownloadHandler_GetContentType();
		Register_UnityEngine_Networking_DownloadHandler_GetContentType();

		//System.Void UnityEngine.Networking.DownloadHandler::Release()
		void Register_UnityEngine_Networking_DownloadHandler_Release();
		Register_UnityEngine_Networking_DownloadHandler_Release();

	//End Registrations for type : UnityEngine.Networking.DownloadHandler

	//Start Registrations for type : UnityEngine.Networking.DownloadHandlerAssetBundle

		//System.IntPtr UnityEngine.Networking.DownloadHandlerAssetBundle::CreateCached_Injected(UnityEngine.Networking.DownloadHandlerAssetBundle,System.String,System.String,UnityEngine.Hash128&,System.UInt32)
		void Register_UnityEngine_Networking_DownloadHandlerAssetBundle_CreateCached_Injected();
		Register_UnityEngine_Networking_DownloadHandlerAssetBundle_CreateCached_Injected();

		//UnityEngine.AssetBundle UnityEngine.Networking.DownloadHandlerAssetBundle::get_assetBundle()
		void Register_UnityEngine_Networking_DownloadHandlerAssetBundle_get_assetBundle();
		Register_UnityEngine_Networking_DownloadHandlerAssetBundle_get_assetBundle();

	//End Registrations for type : UnityEngine.Networking.DownloadHandlerAssetBundle

	//Start Registrations for type : UnityEngine.Networking.DownloadHandlerBuffer

		//System.IntPtr UnityEngine.Networking.DownloadHandlerBuffer::Create(UnityEngine.Networking.DownloadHandlerBuffer)
		void Register_UnityEngine_Networking_DownloadHandlerBuffer_Create();
		Register_UnityEngine_Networking_DownloadHandlerBuffer_Create();

	//End Registrations for type : UnityEngine.Networking.DownloadHandlerBuffer

	//Start Registrations for type : UnityEngine.Networking.DownloadHandlerTexture

		//System.IntPtr UnityEngine.Networking.DownloadHandlerTexture::Create(UnityEngine.Networking.DownloadHandlerTexture,System.Boolean)
		void Register_UnityEngine_Networking_DownloadHandlerTexture_Create();
		Register_UnityEngine_Networking_DownloadHandlerTexture_Create();

		//System.Void UnityEngine.Networking.DownloadHandlerTexture::ClearNativeTexture()
		void Register_UnityEngine_Networking_DownloadHandlerTexture_ClearNativeTexture();
		Register_UnityEngine_Networking_DownloadHandlerTexture_ClearNativeTexture();

		//UnityEngine.Texture2D UnityEngine.Networking.DownloadHandlerTexture::InternalGetTextureNative()
		void Register_UnityEngine_Networking_DownloadHandlerTexture_InternalGetTextureNative();
		Register_UnityEngine_Networking_DownloadHandlerTexture_InternalGetTextureNative();

	//End Registrations for type : UnityEngine.Networking.DownloadHandlerTexture

	//Start Registrations for type : UnityEngine.Networking.UnityWebRequest

		//System.Boolean UnityEngine.Networking.UnityWebRequest::IsExecuting()
		void Register_UnityEngine_Networking_UnityWebRequest_IsExecuting();
		Register_UnityEngine_Networking_UnityWebRequest_IsExecuting();

		//System.Boolean UnityEngine.Networking.UnityWebRequest::get_isModifiable()
		void Register_UnityEngine_Networking_UnityWebRequest_get_isModifiable();
		Register_UnityEngine_Networking_UnityWebRequest_get_isModifiable();

		//System.Int64 UnityEngine.Networking.UnityWebRequest::get_responseCode()
		void Register_UnityEngine_Networking_UnityWebRequest_get_responseCode();
		Register_UnityEngine_Networking_UnityWebRequest_get_responseCode();

		//System.IntPtr UnityEngine.Networking.UnityWebRequest::Create()
		void Register_UnityEngine_Networking_UnityWebRequest_Create();
		Register_UnityEngine_Networking_UnityWebRequest_Create();

		//System.Single UnityEngine.Networking.UnityWebRequest::GetDownloadProgress()
		void Register_UnityEngine_Networking_UnityWebRequest_GetDownloadProgress();
		Register_UnityEngine_Networking_UnityWebRequest_GetDownloadProgress();

		//System.Single UnityEngine.Networking.UnityWebRequest::GetUploadProgress()
		void Register_UnityEngine_Networking_UnityWebRequest_GetUploadProgress();
		Register_UnityEngine_Networking_UnityWebRequest_GetUploadProgress();

		//System.String UnityEngine.Networking.UnityWebRequest::GetHTTPStatusString(System.Int64)
		void Register_UnityEngine_Networking_UnityWebRequest_GetHTTPStatusString();
		Register_UnityEngine_Networking_UnityWebRequest_GetHTTPStatusString();

		//System.String UnityEngine.Networking.UnityWebRequest::GetResponseHeader(System.String)
		void Register_UnityEngine_Networking_UnityWebRequest_GetResponseHeader();
		Register_UnityEngine_Networking_UnityWebRequest_GetResponseHeader();

		//System.String UnityEngine.Networking.UnityWebRequest::GetUrl()
		void Register_UnityEngine_Networking_UnityWebRequest_GetUrl();
		Register_UnityEngine_Networking_UnityWebRequest_GetUrl();

		//System.String UnityEngine.Networking.UnityWebRequest::GetWebErrorString(UnityEngine.Networking.UnityWebRequest/UnityWebRequestError)
		void Register_UnityEngine_Networking_UnityWebRequest_GetWebErrorString();
		Register_UnityEngine_Networking_UnityWebRequest_GetWebErrorString();

		//System.String[] UnityEngine.Networking.UnityWebRequest::GetResponseHeaderKeys()
		void Register_UnityEngine_Networking_UnityWebRequest_GetResponseHeaderKeys();
		Register_UnityEngine_Networking_UnityWebRequest_GetResponseHeaderKeys();

		//System.UInt64 UnityEngine.Networking.UnityWebRequest::get_downloadedBytes()
		void Register_UnityEngine_Networking_UnityWebRequest_get_downloadedBytes();
		Register_UnityEngine_Networking_UnityWebRequest_get_downloadedBytes();

		//System.Void UnityEngine.Networking.UnityWebRequest::Abort()
		void Register_UnityEngine_Networking_UnityWebRequest_Abort();
		Register_UnityEngine_Networking_UnityWebRequest_Abort();

		//System.Void UnityEngine.Networking.UnityWebRequest::Release()
		void Register_UnityEngine_Networking_UnityWebRequest_Release();
		Register_UnityEngine_Networking_UnityWebRequest_Release();

		//UnityEngine.Networking.UnityWebRequest/Result UnityEngine.Networking.UnityWebRequest::get_result()
		void Register_UnityEngine_Networking_UnityWebRequest_get_result();
		Register_UnityEngine_Networking_UnityWebRequest_get_result();

		//UnityEngine.Networking.UnityWebRequest/UnityWebRequestError UnityEngine.Networking.UnityWebRequest::GetError()
		void Register_UnityEngine_Networking_UnityWebRequest_GetError();
		Register_UnityEngine_Networking_UnityWebRequest_GetError();

		//UnityEngine.Networking.UnityWebRequest/UnityWebRequestError UnityEngine.Networking.UnityWebRequest::InternalSetRequestHeader(System.String,System.String)
		void Register_UnityEngine_Networking_UnityWebRequest_InternalSetRequestHeader();
		Register_UnityEngine_Networking_UnityWebRequest_InternalSetRequestHeader();

		//UnityEngine.Networking.UnityWebRequest/UnityWebRequestError UnityEngine.Networking.UnityWebRequest::SetChunked(System.Boolean)
		void Register_UnityEngine_Networking_UnityWebRequest_SetChunked();
		Register_UnityEngine_Networking_UnityWebRequest_SetChunked();

		//UnityEngine.Networking.UnityWebRequest/UnityWebRequestError UnityEngine.Networking.UnityWebRequest::SetCustomMethod(System.String)
		void Register_UnityEngine_Networking_UnityWebRequest_SetCustomMethod();
		Register_UnityEngine_Networking_UnityWebRequest_SetCustomMethod();

		//UnityEngine.Networking.UnityWebRequest/UnityWebRequestError UnityEngine.Networking.UnityWebRequest::SetDownloadHandler(UnityEngine.Networking.DownloadHandler)
		void Register_UnityEngine_Networking_UnityWebRequest_SetDownloadHandler();
		Register_UnityEngine_Networking_UnityWebRequest_SetDownloadHandler();

		//UnityEngine.Networking.UnityWebRequest/UnityWebRequestError UnityEngine.Networking.UnityWebRequest::SetMethod(UnityEngine.Networking.UnityWebRequest/UnityWebRequestMethod)
		void Register_UnityEngine_Networking_UnityWebRequest_SetMethod();
		Register_UnityEngine_Networking_UnityWebRequest_SetMethod();

		//UnityEngine.Networking.UnityWebRequest/UnityWebRequestError UnityEngine.Networking.UnityWebRequest::SetUploadHandler(UnityEngine.Networking.UploadHandler)
		void Register_UnityEngine_Networking_UnityWebRequest_SetUploadHandler();
		Register_UnityEngine_Networking_UnityWebRequest_SetUploadHandler();

		//UnityEngine.Networking.UnityWebRequest/UnityWebRequestError UnityEngine.Networking.UnityWebRequest::SetUrl(System.String)
		void Register_UnityEngine_Networking_UnityWebRequest_SetUrl();
		Register_UnityEngine_Networking_UnityWebRequest_SetUrl();

		//UnityEngine.Networking.UnityWebRequestAsyncOperation UnityEngine.Networking.UnityWebRequest::BeginWebRequest()
		void Register_UnityEngine_Networking_UnityWebRequest_BeginWebRequest();
		Register_UnityEngine_Networking_UnityWebRequest_BeginWebRequest();

	//End Registrations for type : UnityEngine.Networking.UnityWebRequest

	//Start Registrations for type : UnityEngine.Networking.UploadHandler

		//System.Void UnityEngine.Networking.UploadHandler::InternalSetContentType(System.String)
		void Register_UnityEngine_Networking_UploadHandler_InternalSetContentType();
		Register_UnityEngine_Networking_UploadHandler_InternalSetContentType();

		//System.Void UnityEngine.Networking.UploadHandler::Release()
		void Register_UnityEngine_Networking_UploadHandler_Release();
		Register_UnityEngine_Networking_UploadHandler_Release();

	//End Registrations for type : UnityEngine.Networking.UploadHandler

	//Start Registrations for type : UnityEngine.Networking.UploadHandlerRaw

		//System.IntPtr UnityEngine.Networking.UploadHandlerRaw::Create(UnityEngine.Networking.UploadHandlerRaw,System.Byte[])
		void Register_UnityEngine_Networking_UploadHandlerRaw_Create();
		Register_UnityEngine_Networking_UploadHandlerRaw_Create();

	//End Registrations for type : UnityEngine.Networking.UploadHandlerRaw

	//Start Registrations for type : UnityEngine.Networking.WebRequestWWW

		//UnityEngine.AudioClip UnityEngine.Networking.WebRequestWWW::InternalCreateAudioClipUsingDH(UnityEngine.Networking.DownloadHandler,System.String,System.Boolean,System.Boolean,UnityEngine.AudioType)
		void Register_UnityEngine_Networking_WebRequestWWW_InternalCreateAudioClipUsingDH();
		Register_UnityEngine_Networking_WebRequestWWW_InternalCreateAudioClipUsingDH();

	//End Registrations for type : UnityEngine.Networking.WebRequestWWW

	//Start Registrations for type : UnityEngine.NoAllocHelpers

		//System.Array UnityEngine.NoAllocHelpers::ExtractArrayFromList(System.Object)
		void Register_UnityEngine_NoAllocHelpers_ExtractArrayFromList();
		Register_UnityEngine_NoAllocHelpers_ExtractArrayFromList();

		//System.Void UnityEngine.NoAllocHelpers::Internal_ResizeList(System.Object,System.Int32)
		void Register_UnityEngine_NoAllocHelpers_Internal_ResizeList();
		Register_UnityEngine_NoAllocHelpers_Internal_ResizeList();

	//End Registrations for type : UnityEngine.NoAllocHelpers

	//Start Registrations for type : UnityEngine.Object

		//System.Int32 UnityEngine.Object::GetOffsetOfInstanceIDInCPlusPlusObject()
		void Register_UnityEngine_Object_GetOffsetOfInstanceIDInCPlusPlusObject();
		Register_UnityEngine_Object_GetOffsetOfInstanceIDInCPlusPlusObject();

		//System.String UnityEngine.Object::GetName(UnityEngine.Object)
		void Register_UnityEngine_Object_GetName();
		Register_UnityEngine_Object_GetName();

		//System.String UnityEngine.Object::ToString(UnityEngine.Object)
		void Register_UnityEngine_Object_ToString();
		Register_UnityEngine_Object_ToString();

		//System.Void UnityEngine.Object::Destroy(UnityEngine.Object,System.Single)
		void Register_UnityEngine_Object_Destroy();
		Register_UnityEngine_Object_Destroy();

		//System.Void UnityEngine.Object::DestroyImmediate(UnityEngine.Object,System.Boolean)
		void Register_UnityEngine_Object_DestroyImmediate();
		Register_UnityEngine_Object_DestroyImmediate();

		//System.Void UnityEngine.Object::DontDestroyOnLoad(UnityEngine.Object)
		void Register_UnityEngine_Object_DontDestroyOnLoad();
		Register_UnityEngine_Object_DontDestroyOnLoad();

		//System.Void UnityEngine.Object::SetName(UnityEngine.Object,System.String)
		void Register_UnityEngine_Object_SetName();
		Register_UnityEngine_Object_SetName();

		//System.Void UnityEngine.Object::set_hideFlags(UnityEngine.HideFlags)
		void Register_UnityEngine_Object_set_hideFlags();
		Register_UnityEngine_Object_set_hideFlags();

		//UnityEngine.HideFlags UnityEngine.Object::get_hideFlags()
		void Register_UnityEngine_Object_get_hideFlags();
		Register_UnityEngine_Object_get_hideFlags();

		//UnityEngine.Object UnityEngine.Object::FindObjectFromInstanceID(System.Int32)
		void Register_UnityEngine_Object_FindObjectFromInstanceID();
		Register_UnityEngine_Object_FindObjectFromInstanceID();

		//UnityEngine.Object UnityEngine.Object::Internal_CloneSingle(UnityEngine.Object)
		void Register_UnityEngine_Object_Internal_CloneSingle();
		Register_UnityEngine_Object_Internal_CloneSingle();

		//UnityEngine.Object UnityEngine.Object::Internal_CloneSingleWithParent(UnityEngine.Object,UnityEngine.Transform,System.Boolean)
		void Register_UnityEngine_Object_Internal_CloneSingleWithParent();
		Register_UnityEngine_Object_Internal_CloneSingleWithParent();

		//UnityEngine.Object UnityEngine.Object::Internal_InstantiateSingleWithParent_Injected(UnityEngine.Object,UnityEngine.Transform,UnityEngine.Vector3&,UnityEngine.Quaternion&)
		void Register_UnityEngine_Object_Internal_InstantiateSingleWithParent_Injected();
		Register_UnityEngine_Object_Internal_InstantiateSingleWithParent_Injected();

		//UnityEngine.Object UnityEngine.Object::Internal_InstantiateSingle_Injected(UnityEngine.Object,UnityEngine.Vector3&,UnityEngine.Quaternion&)
		void Register_UnityEngine_Object_Internal_InstantiateSingle_Injected();
		Register_UnityEngine_Object_Internal_InstantiateSingle_Injected();

		//UnityEngine.Object[] UnityEngine.Object::FindObjectsOfType(System.Type,System.Boolean)
		void Register_UnityEngine_Object_FindObjectsOfType();
		Register_UnityEngine_Object_FindObjectsOfType();

	//End Registrations for type : UnityEngine.Object

	//Start Registrations for type : UnityEngine.ParticleSystem

		//System.Boolean UnityEngine.ParticleSystem::IsAlive(System.Boolean)
		void Register_UnityEngine_ParticleSystem_IsAlive();
		Register_UnityEngine_ParticleSystem_IsAlive();

		//System.Void UnityEngine.ParticleSystem::Clear(System.Boolean)
		void Register_UnityEngine_ParticleSystem_Clear();
		Register_UnityEngine_ParticleSystem_Clear();

		//System.Void UnityEngine.ParticleSystem::EmitOld_Internal(UnityEngine.ParticleSystem/Particle&)
		void Register_UnityEngine_ParticleSystem_EmitOld_Internal();
		Register_UnityEngine_ParticleSystem_EmitOld_Internal();

		//System.Void UnityEngine.ParticleSystem::Emit_Injected(UnityEngine.ParticleSystem/EmitParams&,System.Int32)
		void Register_UnityEngine_ParticleSystem_Emit_Injected();
		Register_UnityEngine_ParticleSystem_Emit_Injected();

		//System.Void UnityEngine.ParticleSystem::Emit_Internal(System.Int32)
		void Register_UnityEngine_ParticleSystem_Emit_Internal();
		Register_UnityEngine_ParticleSystem_Emit_Internal();

		//System.Void UnityEngine.ParticleSystem::Pause(System.Boolean)
		void Register_UnityEngine_ParticleSystem_Pause();
		Register_UnityEngine_ParticleSystem_Pause();

		//System.Void UnityEngine.ParticleSystem::Play(System.Boolean)
		void Register_UnityEngine_ParticleSystem_Play();
		Register_UnityEngine_ParticleSystem_Play();

		//System.Void UnityEngine.ParticleSystem::Stop(System.Boolean,UnityEngine.ParticleSystemStopBehavior)
		void Register_UnityEngine_ParticleSystem_Stop();
		Register_UnityEngine_ParticleSystem_Stop();

	//End Registrations for type : UnityEngine.ParticleSystem

	//Start Registrations for type : UnityEngine.ParticleSystem/EmissionModule

		//System.Void UnityEngine.ParticleSystem/EmissionModule::set_rateOverTime_Injected(UnityEngine.ParticleSystem/EmissionModule&,UnityEngine.ParticleSystem/MinMaxCurve&)
		void Register_UnityEngine_ParticleSystem_EmissionModule_set_rateOverTime_Injected();
		Register_UnityEngine_ParticleSystem_EmissionModule_set_rateOverTime_Injected();

	//End Registrations for type : UnityEngine.ParticleSystem/EmissionModule

	//Start Registrations for type : UnityEngine.ParticleSystem/MainModule

		//System.Single UnityEngine.ParticleSystem/MainModule::get_duration_Injected(UnityEngine.ParticleSystem/MainModule&)
		void Register_UnityEngine_ParticleSystem_MainModule_get_duration_Injected();
		Register_UnityEngine_ParticleSystem_MainModule_get_duration_Injected();

		//System.Single UnityEngine.ParticleSystem/MainModule::get_gravityModifierMultiplier_Injected(UnityEngine.ParticleSystem/MainModule&)
		void Register_UnityEngine_ParticleSystem_MainModule_get_gravityModifierMultiplier_Injected();
		Register_UnityEngine_ParticleSystem_MainModule_get_gravityModifierMultiplier_Injected();

		//System.Single UnityEngine.ParticleSystem/MainModule::get_startDelayMultiplier_Injected(UnityEngine.ParticleSystem/MainModule&)
		void Register_UnityEngine_ParticleSystem_MainModule_get_startDelayMultiplier_Injected();
		Register_UnityEngine_ParticleSystem_MainModule_get_startDelayMultiplier_Injected();

		//System.Single UnityEngine.ParticleSystem/MainModule::get_startRotationMultiplier_Injected(UnityEngine.ParticleSystem/MainModule&)
		void Register_UnityEngine_ParticleSystem_MainModule_get_startRotationMultiplier_Injected();
		Register_UnityEngine_ParticleSystem_MainModule_get_startRotationMultiplier_Injected();

		//System.Single UnityEngine.ParticleSystem/MainModule::get_startSizeMultiplier_Injected(UnityEngine.ParticleSystem/MainModule&)
		void Register_UnityEngine_ParticleSystem_MainModule_get_startSizeMultiplier_Injected();
		Register_UnityEngine_ParticleSystem_MainModule_get_startSizeMultiplier_Injected();

		//System.Single UnityEngine.ParticleSystem/MainModule::get_startSpeedMultiplier_Injected(UnityEngine.ParticleSystem/MainModule&)
		void Register_UnityEngine_ParticleSystem_MainModule_get_startSpeedMultiplier_Injected();
		Register_UnityEngine_ParticleSystem_MainModule_get_startSpeedMultiplier_Injected();

		//System.Void UnityEngine.ParticleSystem/MainModule::get_startDelay_Injected(UnityEngine.ParticleSystem/MainModule&,UnityEngine.ParticleSystem/MinMaxCurve&)
		void Register_UnityEngine_ParticleSystem_MainModule_get_startDelay_Injected();
		Register_UnityEngine_ParticleSystem_MainModule_get_startDelay_Injected();

		//System.Void UnityEngine.ParticleSystem/MainModule::set_gravityModifierMultiplier_Injected(UnityEngine.ParticleSystem/MainModule&,System.Single)
		void Register_UnityEngine_ParticleSystem_MainModule_set_gravityModifierMultiplier_Injected();
		Register_UnityEngine_ParticleSystem_MainModule_set_gravityModifierMultiplier_Injected();

		//System.Void UnityEngine.ParticleSystem/MainModule::set_loop_Injected(UnityEngine.ParticleSystem/MainModule&,System.Boolean)
		void Register_UnityEngine_ParticleSystem_MainModule_set_loop_Injected();
		Register_UnityEngine_ParticleSystem_MainModule_set_loop_Injected();

		//System.Void UnityEngine.ParticleSystem/MainModule::set_playOnAwake_Injected(UnityEngine.ParticleSystem/MainModule&,System.Boolean)
		void Register_UnityEngine_ParticleSystem_MainModule_set_playOnAwake_Injected();
		Register_UnityEngine_ParticleSystem_MainModule_set_playOnAwake_Injected();

		//System.Void UnityEngine.ParticleSystem/MainModule::set_startColor_Injected(UnityEngine.ParticleSystem/MainModule&,UnityEngine.ParticleSystem/MinMaxGradient&)
		void Register_UnityEngine_ParticleSystem_MainModule_set_startColor_Injected();
		Register_UnityEngine_ParticleSystem_MainModule_set_startColor_Injected();

		//System.Void UnityEngine.ParticleSystem/MainModule::set_startRotationMultiplier_Injected(UnityEngine.ParticleSystem/MainModule&,System.Single)
		void Register_UnityEngine_ParticleSystem_MainModule_set_startRotationMultiplier_Injected();
		Register_UnityEngine_ParticleSystem_MainModule_set_startRotationMultiplier_Injected();

		//System.Void UnityEngine.ParticleSystem/MainModule::set_startSizeMultiplier_Injected(UnityEngine.ParticleSystem/MainModule&,System.Single)
		void Register_UnityEngine_ParticleSystem_MainModule_set_startSizeMultiplier_Injected();
		Register_UnityEngine_ParticleSystem_MainModule_set_startSizeMultiplier_Injected();

		//System.Void UnityEngine.ParticleSystem/MainModule::set_startSpeedMultiplier_Injected(UnityEngine.ParticleSystem/MainModule&,System.Single)
		void Register_UnityEngine_ParticleSystem_MainModule_set_startSpeedMultiplier_Injected();
		Register_UnityEngine_ParticleSystem_MainModule_set_startSpeedMultiplier_Injected();

	//End Registrations for type : UnityEngine.ParticleSystem/MainModule

	//Start Registrations for type : UnityEngine.ParticleSystemRenderer

		//System.Int32 UnityEngine.ParticleSystemRenderer::GetMeshes(UnityEngine.Mesh[])
		void Register_UnityEngine_ParticleSystemRenderer_GetMeshes();
		Register_UnityEngine_ParticleSystemRenderer_GetMeshes();

	//End Registrations for type : UnityEngine.ParticleSystemRenderer

	//Start Registrations for type : UnityEngine.PhysicMaterial

		//System.Void UnityEngine.PhysicMaterial::Internal_CreateDynamicsMaterial(UnityEngine.PhysicMaterial,System.String)
		void Register_UnityEngine_PhysicMaterial_Internal_CreateDynamicsMaterial();
		Register_UnityEngine_PhysicMaterial_Internal_CreateDynamicsMaterial();

	//End Registrations for type : UnityEngine.PhysicMaterial

	//Start Registrations for type : UnityEngine.Physics

		//System.Boolean UnityEngine.Physics::CheckBox_Internal_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Quaternion&,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_Physics_CheckBox_Internal_Injected();
		Register_UnityEngine_Physics_CheckBox_Internal_Injected();

		//System.Boolean UnityEngine.Physics::CheckCapsule_Internal_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_Physics_CheckCapsule_Internal_Injected();
		Register_UnityEngine_Physics_CheckCapsule_Internal_Injected();

		//System.Boolean UnityEngine.Physics::CheckSphere_Internal_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_Physics_CheckSphere_Internal_Injected();
		Register_UnityEngine_Physics_CheckSphere_Internal_Injected();

		//System.Boolean UnityEngine.Physics::GetIgnoreCollision(UnityEngine.Collider,UnityEngine.Collider)
		void Register_UnityEngine_Physics_GetIgnoreCollision();
		Register_UnityEngine_Physics_GetIgnoreCollision();

		//System.Boolean UnityEngine.Physics::GetIgnoreLayerCollision(System.Int32,System.Int32)
		void Register_UnityEngine_Physics_GetIgnoreLayerCollision();
		Register_UnityEngine_Physics_GetIgnoreLayerCollision();

		//System.Boolean UnityEngine.Physics::Query_ComputePenetration_Injected(UnityEngine.Collider,UnityEngine.Vector3&,UnityEngine.Quaternion&,UnityEngine.Collider,UnityEngine.Vector3&,UnityEngine.Quaternion&,UnityEngine.Vector3&,System.Single&)
		void Register_UnityEngine_Physics_Query_ComputePenetration_Injected();
		Register_UnityEngine_Physics_Query_ComputePenetration_Injected();

		//System.Boolean UnityEngine.Physics::get_autoSimulation()
		void Register_UnityEngine_Physics_get_autoSimulation();
		Register_UnityEngine_Physics_get_autoSimulation();

		//System.Boolean UnityEngine.Physics::get_autoSyncTransforms()
		void Register_UnityEngine_Physics_get_autoSyncTransforms();
		Register_UnityEngine_Physics_get_autoSyncTransforms();

		//System.Boolean UnityEngine.Physics::get_interCollisionSettingsToggle()
		void Register_UnityEngine_Physics_get_interCollisionSettingsToggle();
		Register_UnityEngine_Physics_get_interCollisionSettingsToggle();

		//System.Boolean UnityEngine.Physics::get_queriesHitBackfaces()
		void Register_UnityEngine_Physics_get_queriesHitBackfaces();
		Register_UnityEngine_Physics_get_queriesHitBackfaces();

		//System.Boolean UnityEngine.Physics::get_queriesHitTriggers()
		void Register_UnityEngine_Physics_get_queriesHitTriggers();
		Register_UnityEngine_Physics_get_queriesHitTriggers();

		//System.Boolean UnityEngine.Physics::get_reuseCollisionCallbacks()
		void Register_UnityEngine_Physics_get_reuseCollisionCallbacks();
		Register_UnityEngine_Physics_get_reuseCollisionCallbacks();

		//System.Int32 UnityEngine.Physics::get_defaultSolverIterations()
		void Register_UnityEngine_Physics_get_defaultSolverIterations();
		Register_UnityEngine_Physics_get_defaultSolverIterations();

		//System.Int32 UnityEngine.Physics::get_defaultSolverVelocityIterations()
		void Register_UnityEngine_Physics_get_defaultSolverVelocityIterations();
		Register_UnityEngine_Physics_get_defaultSolverVelocityIterations();

		//System.Single UnityEngine.Physics::get_bounceThreshold()
		void Register_UnityEngine_Physics_get_bounceThreshold();
		Register_UnityEngine_Physics_get_bounceThreshold();

		//System.Single UnityEngine.Physics::get_defaultContactOffset()
		void Register_UnityEngine_Physics_get_defaultContactOffset();
		Register_UnityEngine_Physics_get_defaultContactOffset();

		//System.Single UnityEngine.Physics::get_defaultMaxAngularSpeed()
		void Register_UnityEngine_Physics_get_defaultMaxAngularSpeed();
		Register_UnityEngine_Physics_get_defaultMaxAngularSpeed();

		//System.Single UnityEngine.Physics::get_interCollisionDistance()
		void Register_UnityEngine_Physics_get_interCollisionDistance();
		Register_UnityEngine_Physics_get_interCollisionDistance();

		//System.Single UnityEngine.Physics::get_interCollisionStiffness()
		void Register_UnityEngine_Physics_get_interCollisionStiffness();
		Register_UnityEngine_Physics_get_interCollisionStiffness();

		//System.Single UnityEngine.Physics::get_sleepThreshold()
		void Register_UnityEngine_Physics_get_sleepThreshold();
		Register_UnityEngine_Physics_get_sleepThreshold();

		//System.Void UnityEngine.Physics::BakeMesh(System.Int32,System.Boolean)
		void Register_UnityEngine_Physics_BakeMesh();
		Register_UnityEngine_Physics_BakeMesh();

		//System.Void UnityEngine.Physics::IgnoreCollision(UnityEngine.Collider,UnityEngine.Collider,System.Boolean)
		void Register_UnityEngine_Physics_IgnoreCollision();
		Register_UnityEngine_Physics_IgnoreCollision();

		//System.Void UnityEngine.Physics::IgnoreLayerCollision(System.Int32,System.Int32,System.Boolean)
		void Register_UnityEngine_Physics_IgnoreLayerCollision();
		Register_UnityEngine_Physics_IgnoreLayerCollision();

		//System.Void UnityEngine.Physics::Internal_RebuildBroadphaseRegions_Injected(UnityEngine.Bounds&,System.Int32)
		void Register_UnityEngine_Physics_Internal_RebuildBroadphaseRegions_Injected();
		Register_UnityEngine_Physics_Internal_RebuildBroadphaseRegions_Injected();

		//System.Void UnityEngine.Physics::Query_ClosestPoint_Injected(UnityEngine.Collider,UnityEngine.Vector3&,UnityEngine.Quaternion&,UnityEngine.Vector3&,UnityEngine.Vector3&)
		void Register_UnityEngine_Physics_Query_ClosestPoint_Injected();
		Register_UnityEngine_Physics_Query_ClosestPoint_Injected();

		//System.Void UnityEngine.Physics::Simulate_Internal_Injected(UnityEngine.PhysicsScene&,System.Single)
		void Register_UnityEngine_Physics_Simulate_Internal_Injected();
		Register_UnityEngine_Physics_Simulate_Internal_Injected();

		//System.Void UnityEngine.Physics::SyncTransforms()
		void Register_UnityEngine_Physics_SyncTransforms();
		Register_UnityEngine_Physics_SyncTransforms();

		//System.Void UnityEngine.Physics::get_clothGravity_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Physics_get_clothGravity_Injected();
		Register_UnityEngine_Physics_get_clothGravity_Injected();

		//System.Void UnityEngine.Physics::get_defaultPhysicsScene_Injected(UnityEngine.PhysicsScene&)
		void Register_UnityEngine_Physics_get_defaultPhysicsScene_Injected();
		Register_UnityEngine_Physics_get_defaultPhysicsScene_Injected();

		//System.Void UnityEngine.Physics::get_gravity_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Physics_get_gravity_Injected();
		Register_UnityEngine_Physics_get_gravity_Injected();

		//System.Void UnityEngine.Physics::set_autoSimulation(System.Boolean)
		void Register_UnityEngine_Physics_set_autoSimulation();
		Register_UnityEngine_Physics_set_autoSimulation();

		//System.Void UnityEngine.Physics::set_autoSyncTransforms(System.Boolean)
		void Register_UnityEngine_Physics_set_autoSyncTransforms();
		Register_UnityEngine_Physics_set_autoSyncTransforms();

		//System.Void UnityEngine.Physics::set_bounceThreshold(System.Single)
		void Register_UnityEngine_Physics_set_bounceThreshold();
		Register_UnityEngine_Physics_set_bounceThreshold();

		//System.Void UnityEngine.Physics::set_clothGravity_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Physics_set_clothGravity_Injected();
		Register_UnityEngine_Physics_set_clothGravity_Injected();

		//System.Void UnityEngine.Physics::set_defaultContactOffset(System.Single)
		void Register_UnityEngine_Physics_set_defaultContactOffset();
		Register_UnityEngine_Physics_set_defaultContactOffset();

		//System.Void UnityEngine.Physics::set_defaultMaxAngularSpeed(System.Single)
		void Register_UnityEngine_Physics_set_defaultMaxAngularSpeed();
		Register_UnityEngine_Physics_set_defaultMaxAngularSpeed();

		//System.Void UnityEngine.Physics::set_defaultSolverIterations(System.Int32)
		void Register_UnityEngine_Physics_set_defaultSolverIterations();
		Register_UnityEngine_Physics_set_defaultSolverIterations();

		//System.Void UnityEngine.Physics::set_defaultSolverVelocityIterations(System.Int32)
		void Register_UnityEngine_Physics_set_defaultSolverVelocityIterations();
		Register_UnityEngine_Physics_set_defaultSolverVelocityIterations();

		//System.Void UnityEngine.Physics::set_gravity_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Physics_set_gravity_Injected();
		Register_UnityEngine_Physics_set_gravity_Injected();

		//System.Void UnityEngine.Physics::set_interCollisionDistance(System.Single)
		void Register_UnityEngine_Physics_set_interCollisionDistance();
		Register_UnityEngine_Physics_set_interCollisionDistance();

		//System.Void UnityEngine.Physics::set_interCollisionSettingsToggle(System.Boolean)
		void Register_UnityEngine_Physics_set_interCollisionSettingsToggle();
		Register_UnityEngine_Physics_set_interCollisionSettingsToggle();

		//System.Void UnityEngine.Physics::set_interCollisionStiffness(System.Single)
		void Register_UnityEngine_Physics_set_interCollisionStiffness();
		Register_UnityEngine_Physics_set_interCollisionStiffness();

		//System.Void UnityEngine.Physics::set_queriesHitBackfaces(System.Boolean)
		void Register_UnityEngine_Physics_set_queriesHitBackfaces();
		Register_UnityEngine_Physics_set_queriesHitBackfaces();

		//System.Void UnityEngine.Physics::set_queriesHitTriggers(System.Boolean)
		void Register_UnityEngine_Physics_set_queriesHitTriggers();
		Register_UnityEngine_Physics_set_queriesHitTriggers();

		//System.Void UnityEngine.Physics::set_reuseCollisionCallbacks(System.Boolean)
		void Register_UnityEngine_Physics_set_reuseCollisionCallbacks();
		Register_UnityEngine_Physics_set_reuseCollisionCallbacks();

		//System.Void UnityEngine.Physics::set_sleepThreshold(System.Single)
		void Register_UnityEngine_Physics_set_sleepThreshold();
		Register_UnityEngine_Physics_set_sleepThreshold();

		//UnityEngine.Collider[] UnityEngine.Physics::OverlapBox_Internal_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Quaternion&,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_Physics_OverlapBox_Internal_Injected();
		Register_UnityEngine_Physics_OverlapBox_Internal_Injected();

		//UnityEngine.Collider[] UnityEngine.Physics::OverlapCapsule_Internal_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_Physics_OverlapCapsule_Internal_Injected();
		Register_UnityEngine_Physics_OverlapCapsule_Internal_Injected();

		//UnityEngine.Collider[] UnityEngine.Physics::OverlapSphere_Internal_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_Physics_OverlapSphere_Internal_Injected();
		Register_UnityEngine_Physics_OverlapSphere_Internal_Injected();

		//UnityEngine.RaycastHit[] UnityEngine.Physics::Internal_BoxCastAll_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Quaternion&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_Physics_Internal_BoxCastAll_Injected();
		Register_UnityEngine_Physics_Internal_BoxCastAll_Injected();

		//UnityEngine.RaycastHit[] UnityEngine.Physics::Internal_RaycastAll_Injected(UnityEngine.PhysicsScene&,UnityEngine.Ray&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_Physics_Internal_RaycastAll_Injected();
		Register_UnityEngine_Physics_Internal_RaycastAll_Injected();

		//UnityEngine.RaycastHit[] UnityEngine.Physics::Query_CapsuleCastAll_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,UnityEngine.Vector3&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_Physics_Query_CapsuleCastAll_Injected();
		Register_UnityEngine_Physics_Query_CapsuleCastAll_Injected();

		//UnityEngine.RaycastHit[] UnityEngine.Physics::Query_SphereCastAll_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,System.Single,UnityEngine.Vector3&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_Physics_Query_SphereCastAll_Injected();
		Register_UnityEngine_Physics_Query_SphereCastAll_Injected();

	//End Registrations for type : UnityEngine.Physics

	//Start Registrations for type : UnityEngine.Physics2D

		//System.Boolean UnityEngine.Physics2D::get_queriesHitTriggers()
		void Register_UnityEngine_Physics2D_get_queriesHitTriggers();
		Register_UnityEngine_Physics2D_get_queriesHitTriggers();

		//System.Void UnityEngine.Physics2D::IgnoreCollision(UnityEngine.Collider2D,UnityEngine.Collider2D,System.Boolean)
		void Register_UnityEngine_Physics2D_IgnoreCollision();
		Register_UnityEngine_Physics2D_IgnoreCollision();

		//UnityEngine.RaycastHit2D[] UnityEngine.Physics2D::GetRayIntersectionAll_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,System.Int32)
		void Register_UnityEngine_Physics2D_GetRayIntersectionAll_Internal_Injected();
		Register_UnityEngine_Physics2D_GetRayIntersectionAll_Internal_Injected();

	//End Registrations for type : UnityEngine.Physics2D

	//Start Registrations for type : UnityEngine.PhysicsScene

		//System.Boolean UnityEngine.PhysicsScene::Internal_RaycastTest_Injected(UnityEngine.PhysicsScene&,UnityEngine.Ray&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_PhysicsScene_Internal_RaycastTest_Injected();
		Register_UnityEngine_PhysicsScene_Internal_RaycastTest_Injected();

		//System.Boolean UnityEngine.PhysicsScene::Internal_Raycast_Injected(UnityEngine.PhysicsScene&,UnityEngine.Ray&,System.Single,UnityEngine.RaycastHit&,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_PhysicsScene_Internal_Raycast_Injected();
		Register_UnityEngine_PhysicsScene_Internal_Raycast_Injected();

		//System.Boolean UnityEngine.PhysicsScene::Query_BoxCast_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Quaternion&,System.Single,UnityEngine.RaycastHit&,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_PhysicsScene_Query_BoxCast_Injected();
		Register_UnityEngine_PhysicsScene_Query_BoxCast_Injected();

		//System.Boolean UnityEngine.PhysicsScene::Query_CapsuleCast_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,UnityEngine.Vector3&,System.Single,UnityEngine.RaycastHit&,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_PhysicsScene_Query_CapsuleCast_Injected();
		Register_UnityEngine_PhysicsScene_Query_CapsuleCast_Injected();

		//System.Boolean UnityEngine.PhysicsScene::Query_SphereCast_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,System.Single,UnityEngine.Vector3&,System.Single,UnityEngine.RaycastHit&,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_PhysicsScene_Query_SphereCast_Injected();
		Register_UnityEngine_PhysicsScene_Query_SphereCast_Injected();

		//System.Int32 UnityEngine.PhysicsScene::Internal_BoxCastNonAlloc_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.RaycastHit[],UnityEngine.Quaternion&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_PhysicsScene_Internal_BoxCastNonAlloc_Injected();
		Register_UnityEngine_PhysicsScene_Internal_BoxCastNonAlloc_Injected();

		//System.Int32 UnityEngine.PhysicsScene::Internal_CapsuleCastNonAlloc_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,UnityEngine.Vector3&,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_PhysicsScene_Internal_CapsuleCastNonAlloc_Injected();
		Register_UnityEngine_PhysicsScene_Internal_CapsuleCastNonAlloc_Injected();

		//System.Int32 UnityEngine.PhysicsScene::Internal_RaycastNonAlloc_Injected(UnityEngine.PhysicsScene&,UnityEngine.Ray&,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_PhysicsScene_Internal_RaycastNonAlloc_Injected();
		Register_UnityEngine_PhysicsScene_Internal_RaycastNonAlloc_Injected();

		//System.Int32 UnityEngine.PhysicsScene::Internal_SphereCastNonAlloc_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,System.Single,UnityEngine.Vector3&,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_PhysicsScene_Internal_SphereCastNonAlloc_Injected();
		Register_UnityEngine_PhysicsScene_Internal_SphereCastNonAlloc_Injected();

		//System.Int32 UnityEngine.PhysicsScene::OverlapBoxNonAlloc_Internal_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Collider[],UnityEngine.Quaternion&,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_PhysicsScene_OverlapBoxNonAlloc_Internal_Injected();
		Register_UnityEngine_PhysicsScene_OverlapBoxNonAlloc_Internal_Injected();

		//System.Int32 UnityEngine.PhysicsScene::OverlapCapsuleNonAlloc_Internal_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,UnityEngine.Collider[],System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_PhysicsScene_OverlapCapsuleNonAlloc_Internal_Injected();
		Register_UnityEngine_PhysicsScene_OverlapCapsuleNonAlloc_Internal_Injected();

		//System.Int32 UnityEngine.PhysicsScene::OverlapSphereNonAlloc_Internal_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,System.Single,UnityEngine.Collider[],System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_PhysicsScene_OverlapSphereNonAlloc_Internal_Injected();
		Register_UnityEngine_PhysicsScene_OverlapSphereNonAlloc_Internal_Injected();

	//End Registrations for type : UnityEngine.PhysicsScene

	//Start Registrations for type : UnityEngine.PhysicsScene2D

		//System.Int32 UnityEngine.PhysicsScene2D::GetRayIntersectionArray_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,System.Int32,UnityEngine.RaycastHit2D[])
		void Register_UnityEngine_PhysicsScene2D_GetRayIntersectionArray_Internal_Injected();
		Register_UnityEngine_PhysicsScene2D_GetRayIntersectionArray_Internal_Injected();

		//System.Int32 UnityEngine.PhysicsScene2D::OverlapPointArray_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector2&,UnityEngine.ContactFilter2D&,UnityEngine.Collider2D[])
		void Register_UnityEngine_PhysicsScene2D_OverlapPointArray_Internal_Injected();
		Register_UnityEngine_PhysicsScene2D_OverlapPointArray_Internal_Injected();

		//System.Int32 UnityEngine.PhysicsScene2D::RaycastArray_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector2&,UnityEngine.Vector2&,System.Single,UnityEngine.ContactFilter2D&,UnityEngine.RaycastHit2D[])
		void Register_UnityEngine_PhysicsScene2D_RaycastArray_Internal_Injected();
		Register_UnityEngine_PhysicsScene2D_RaycastArray_Internal_Injected();

		//System.Int32 UnityEngine.PhysicsScene2D::RaycastList_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector2&,UnityEngine.Vector2&,System.Single,UnityEngine.ContactFilter2D&,System.Collections.Generic.List`1<UnityEngine.RaycastHit2D>)
		void Register_UnityEngine_PhysicsScene2D_RaycastList_Internal_Injected();
		Register_UnityEngine_PhysicsScene2D_RaycastList_Internal_Injected();

		//System.Void UnityEngine.PhysicsScene2D::CircleCast_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector2&,System.Single,UnityEngine.Vector2&,System.Single,UnityEngine.ContactFilter2D&,UnityEngine.RaycastHit2D&)
		void Register_UnityEngine_PhysicsScene2D_CircleCast_Internal_Injected();
		Register_UnityEngine_PhysicsScene2D_CircleCast_Internal_Injected();

		//System.Void UnityEngine.PhysicsScene2D::Raycast_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector2&,UnityEngine.Vector2&,System.Single,UnityEngine.ContactFilter2D&,UnityEngine.RaycastHit2D&)
		void Register_UnityEngine_PhysicsScene2D_Raycast_Internal_Injected();
		Register_UnityEngine_PhysicsScene2D_Raycast_Internal_Injected();

		//UnityEngine.Collider2D UnityEngine.PhysicsScene2D::OverlapPoint_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector2&,UnityEngine.ContactFilter2D&)
		void Register_UnityEngine_PhysicsScene2D_OverlapPoint_Internal_Injected();
		Register_UnityEngine_PhysicsScene2D_OverlapPoint_Internal_Injected();

	//End Registrations for type : UnityEngine.PhysicsScene2D

	//Start Registrations for type : UnityEngine.Playables.PlayableHandle

		//System.Boolean UnityEngine.Playables.PlayableHandle::IsValid_Injected(UnityEngine.Playables.PlayableHandle&)
		void Register_UnityEngine_Playables_PlayableHandle_IsValid_Injected();
		Register_UnityEngine_Playables_PlayableHandle_IsValid_Injected();

		//System.Type UnityEngine.Playables.PlayableHandle::GetPlayableType_Injected(UnityEngine.Playables.PlayableHandle&)
		void Register_UnityEngine_Playables_PlayableHandle_GetPlayableType_Injected();
		Register_UnityEngine_Playables_PlayableHandle_GetPlayableType_Injected();

	//End Registrations for type : UnityEngine.Playables.PlayableHandle

	//Start Registrations for type : UnityEngine.PlayerConnectionInternal

		//System.Boolean UnityEngine.PlayerConnectionInternal::IsConnected()
		void Register_UnityEngine_PlayerConnectionInternal_IsConnected();
		Register_UnityEngine_PlayerConnectionInternal_IsConnected();

		//System.Boolean UnityEngine.PlayerConnectionInternal::TrySendMessage(System.String,System.Byte[],System.Int32)
		void Register_UnityEngine_PlayerConnectionInternal_TrySendMessage();
		Register_UnityEngine_PlayerConnectionInternal_TrySendMessage();

		//System.Void UnityEngine.PlayerConnectionInternal::DisconnectAll()
		void Register_UnityEngine_PlayerConnectionInternal_DisconnectAll();
		Register_UnityEngine_PlayerConnectionInternal_DisconnectAll();

		//System.Void UnityEngine.PlayerConnectionInternal::Initialize()
		void Register_UnityEngine_PlayerConnectionInternal_Initialize();
		Register_UnityEngine_PlayerConnectionInternal_Initialize();

		//System.Void UnityEngine.PlayerConnectionInternal::PollInternal()
		void Register_UnityEngine_PlayerConnectionInternal_PollInternal();
		Register_UnityEngine_PlayerConnectionInternal_PollInternal();

		//System.Void UnityEngine.PlayerConnectionInternal::RegisterInternal(System.String)
		void Register_UnityEngine_PlayerConnectionInternal_RegisterInternal();
		Register_UnityEngine_PlayerConnectionInternal_RegisterInternal();

		//System.Void UnityEngine.PlayerConnectionInternal::SendMessage(System.String,System.Byte[],System.Int32)
		void Register_UnityEngine_PlayerConnectionInternal_SendMessage();
		Register_UnityEngine_PlayerConnectionInternal_SendMessage();

		//System.Void UnityEngine.PlayerConnectionInternal::UnregisterInternal(System.String)
		void Register_UnityEngine_PlayerConnectionInternal_UnregisterInternal();
		Register_UnityEngine_PlayerConnectionInternal_UnregisterInternal();

	//End Registrations for type : UnityEngine.PlayerConnectionInternal

	//Start Registrations for type : UnityEngine.PlayerPrefs

		//System.Boolean UnityEngine.PlayerPrefs::HasKey(System.String)
		void Register_UnityEngine_PlayerPrefs_HasKey();
		Register_UnityEngine_PlayerPrefs_HasKey();

		//System.Boolean UnityEngine.PlayerPrefs::TrySetFloat(System.String,System.Single)
		void Register_UnityEngine_PlayerPrefs_TrySetFloat();
		Register_UnityEngine_PlayerPrefs_TrySetFloat();

		//System.Boolean UnityEngine.PlayerPrefs::TrySetInt(System.String,System.Int32)
		void Register_UnityEngine_PlayerPrefs_TrySetInt();
		Register_UnityEngine_PlayerPrefs_TrySetInt();

		//System.Boolean UnityEngine.PlayerPrefs::TrySetSetString(System.String,System.String)
		void Register_UnityEngine_PlayerPrefs_TrySetSetString();
		Register_UnityEngine_PlayerPrefs_TrySetSetString();

		//System.Int32 UnityEngine.PlayerPrefs::GetInt(System.String,System.Int32)
		void Register_UnityEngine_PlayerPrefs_GetInt();
		Register_UnityEngine_PlayerPrefs_GetInt();

		//System.Single UnityEngine.PlayerPrefs::GetFloat(System.String,System.Single)
		void Register_UnityEngine_PlayerPrefs_GetFloat();
		Register_UnityEngine_PlayerPrefs_GetFloat();

		//System.String UnityEngine.PlayerPrefs::GetString(System.String,System.String)
		void Register_UnityEngine_PlayerPrefs_GetString();
		Register_UnityEngine_PlayerPrefs_GetString();

		//System.Void UnityEngine.PlayerPrefs::DeleteKey(System.String)
		void Register_UnityEngine_PlayerPrefs_DeleteKey();
		Register_UnityEngine_PlayerPrefs_DeleteKey();

		//System.Void UnityEngine.PlayerPrefs::Save()
		void Register_UnityEngine_PlayerPrefs_Save();
		Register_UnityEngine_PlayerPrefs_Save();

	//End Registrations for type : UnityEngine.PlayerPrefs

	//Start Registrations for type : UnityEngine.PolygonCollider2D

		//System.Void UnityEngine.PolygonCollider2D::SetPath_Internal(System.Int32,UnityEngine.Vector2[])
		void Register_UnityEngine_PolygonCollider2D_SetPath_Internal();
		Register_UnityEngine_PolygonCollider2D_SetPath_Internal();

	//End Registrations for type : UnityEngine.PolygonCollider2D

	//Start Registrations for type : UnityEngine.QualitySettings

		//System.Void UnityEngine.QualitySettings::set_vSyncCount(System.Int32)
		void Register_UnityEngine_QualitySettings_set_vSyncCount();
		Register_UnityEngine_QualitySettings_set_vSyncCount();

		//UnityEngine.ColorSpace UnityEngine.QualitySettings::get_activeColorSpace()
		void Register_UnityEngine_QualitySettings_get_activeColorSpace();
		Register_UnityEngine_QualitySettings_get_activeColorSpace();

	//End Registrations for type : UnityEngine.QualitySettings

	//Start Registrations for type : UnityEngine.Quaternion

		//System.Void UnityEngine.Quaternion::AngleAxis_Injected(System.Single,UnityEngine.Vector3&,UnityEngine.Quaternion&)
		void Register_UnityEngine_Quaternion_AngleAxis_Injected();
		Register_UnityEngine_Quaternion_AngleAxis_Injected();

		//System.Void UnityEngine.Quaternion::FromToRotation_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Quaternion&)
		void Register_UnityEngine_Quaternion_FromToRotation_Injected();
		Register_UnityEngine_Quaternion_FromToRotation_Injected();

		//System.Void UnityEngine.Quaternion::Internal_FromEulerRad_Injected(UnityEngine.Vector3&,UnityEngine.Quaternion&)
		void Register_UnityEngine_Quaternion_Internal_FromEulerRad_Injected();
		Register_UnityEngine_Quaternion_Internal_FromEulerRad_Injected();

		//System.Void UnityEngine.Quaternion::Internal_ToEulerRad_Injected(UnityEngine.Quaternion&,UnityEngine.Vector3&)
		void Register_UnityEngine_Quaternion_Internal_ToEulerRad_Injected();
		Register_UnityEngine_Quaternion_Internal_ToEulerRad_Injected();

		//System.Void UnityEngine.Quaternion::Inverse_Injected(UnityEngine.Quaternion&,UnityEngine.Quaternion&)
		void Register_UnityEngine_Quaternion_Inverse_Injected();
		Register_UnityEngine_Quaternion_Inverse_Injected();

		//System.Void UnityEngine.Quaternion::Lerp_Injected(UnityEngine.Quaternion&,UnityEngine.Quaternion&,System.Single,UnityEngine.Quaternion&)
		void Register_UnityEngine_Quaternion_Lerp_Injected();
		Register_UnityEngine_Quaternion_Lerp_Injected();

		//System.Void UnityEngine.Quaternion::LookRotation_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Quaternion&)
		void Register_UnityEngine_Quaternion_LookRotation_Injected();
		Register_UnityEngine_Quaternion_LookRotation_Injected();

		//System.Void UnityEngine.Quaternion::SlerpUnclamped_Injected(UnityEngine.Quaternion&,UnityEngine.Quaternion&,System.Single,UnityEngine.Quaternion&)
		void Register_UnityEngine_Quaternion_SlerpUnclamped_Injected();
		Register_UnityEngine_Quaternion_SlerpUnclamped_Injected();

		//System.Void UnityEngine.Quaternion::Slerp_Injected(UnityEngine.Quaternion&,UnityEngine.Quaternion&,System.Single,UnityEngine.Quaternion&)
		void Register_UnityEngine_Quaternion_Slerp_Injected();
		Register_UnityEngine_Quaternion_Slerp_Injected();

	//End Registrations for type : UnityEngine.Quaternion

	//Start Registrations for type : UnityEngine.Random

		//System.Int32 UnityEngine.Random::RandomRangeInt(System.Int32,System.Int32)
		void Register_UnityEngine_Random_RandomRangeInt();
		Register_UnityEngine_Random_RandomRangeInt();

		//System.Single UnityEngine.Random::Range(System.Single,System.Single)
		void Register_UnityEngine_Random_Range();
		Register_UnityEngine_Random_Range();

		//System.Single UnityEngine.Random::get_value()
		void Register_UnityEngine_Random_get_value();
		Register_UnityEngine_Random_get_value();

		//System.Void UnityEngine.Random::get_insideUnitSphere_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Random_get_insideUnitSphere_Injected();
		Register_UnityEngine_Random_get_insideUnitSphere_Injected();

		//System.Void UnityEngine.Random::get_state_Injected(UnityEngine.Random/State&)
		void Register_UnityEngine_Random_get_state_Injected();
		Register_UnityEngine_Random_get_state_Injected();

		//System.Void UnityEngine.Random::set_state_Injected(UnityEngine.Random/State&)
		void Register_UnityEngine_Random_set_state_Injected();
		Register_UnityEngine_Random_set_state_Injected();

	//End Registrations for type : UnityEngine.Random

	//Start Registrations for type : UnityEngine.RectOffset

		//System.Int32 UnityEngine.RectOffset::get_bottom()
		void Register_UnityEngine_RectOffset_get_bottom();
		Register_UnityEngine_RectOffset_get_bottom();

		//System.Int32 UnityEngine.RectOffset::get_horizontal()
		void Register_UnityEngine_RectOffset_get_horizontal();
		Register_UnityEngine_RectOffset_get_horizontal();

		//System.Int32 UnityEngine.RectOffset::get_left()
		void Register_UnityEngine_RectOffset_get_left();
		Register_UnityEngine_RectOffset_get_left();

		//System.Int32 UnityEngine.RectOffset::get_right()
		void Register_UnityEngine_RectOffset_get_right();
		Register_UnityEngine_RectOffset_get_right();

		//System.Int32 UnityEngine.RectOffset::get_top()
		void Register_UnityEngine_RectOffset_get_top();
		Register_UnityEngine_RectOffset_get_top();

		//System.Int32 UnityEngine.RectOffset::get_vertical()
		void Register_UnityEngine_RectOffset_get_vertical();
		Register_UnityEngine_RectOffset_get_vertical();

		//System.IntPtr UnityEngine.RectOffset::InternalCreate()
		void Register_UnityEngine_RectOffset_InternalCreate();
		Register_UnityEngine_RectOffset_InternalCreate();

		//System.Void UnityEngine.RectOffset::InternalDestroy(System.IntPtr)
		void Register_UnityEngine_RectOffset_InternalDestroy();
		Register_UnityEngine_RectOffset_InternalDestroy();

		//System.Void UnityEngine.RectOffset::Remove_Injected(UnityEngine.Rect&,UnityEngine.Rect&)
		void Register_UnityEngine_RectOffset_Remove_Injected();
		Register_UnityEngine_RectOffset_Remove_Injected();

		//System.Void UnityEngine.RectOffset::set_bottom(System.Int32)
		void Register_UnityEngine_RectOffset_set_bottom();
		Register_UnityEngine_RectOffset_set_bottom();

		//System.Void UnityEngine.RectOffset::set_left(System.Int32)
		void Register_UnityEngine_RectOffset_set_left();
		Register_UnityEngine_RectOffset_set_left();

		//System.Void UnityEngine.RectOffset::set_right(System.Int32)
		void Register_UnityEngine_RectOffset_set_right();
		Register_UnityEngine_RectOffset_set_right();

		//System.Void UnityEngine.RectOffset::set_top(System.Int32)
		void Register_UnityEngine_RectOffset_set_top();
		Register_UnityEngine_RectOffset_set_top();

	//End Registrations for type : UnityEngine.RectOffset

	//Start Registrations for type : UnityEngine.RectTransform

		//System.Void UnityEngine.RectTransform::get_anchorMax_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransform_get_anchorMax_Injected();
		Register_UnityEngine_RectTransform_get_anchorMax_Injected();

		//System.Void UnityEngine.RectTransform::get_anchorMin_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransform_get_anchorMin_Injected();
		Register_UnityEngine_RectTransform_get_anchorMin_Injected();

		//System.Void UnityEngine.RectTransform::get_anchoredPosition_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransform_get_anchoredPosition_Injected();
		Register_UnityEngine_RectTransform_get_anchoredPosition_Injected();

		//System.Void UnityEngine.RectTransform::get_pivot_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransform_get_pivot_Injected();
		Register_UnityEngine_RectTransform_get_pivot_Injected();

		//System.Void UnityEngine.RectTransform::get_rect_Injected(UnityEngine.Rect&)
		void Register_UnityEngine_RectTransform_get_rect_Injected();
		Register_UnityEngine_RectTransform_get_rect_Injected();

		//System.Void UnityEngine.RectTransform::get_sizeDelta_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransform_get_sizeDelta_Injected();
		Register_UnityEngine_RectTransform_get_sizeDelta_Injected();

		//System.Void UnityEngine.RectTransform::set_anchorMax_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransform_set_anchorMax_Injected();
		Register_UnityEngine_RectTransform_set_anchorMax_Injected();

		//System.Void UnityEngine.RectTransform::set_anchorMin_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransform_set_anchorMin_Injected();
		Register_UnityEngine_RectTransform_set_anchorMin_Injected();

		//System.Void UnityEngine.RectTransform::set_anchoredPosition_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransform_set_anchoredPosition_Injected();
		Register_UnityEngine_RectTransform_set_anchoredPosition_Injected();

		//System.Void UnityEngine.RectTransform::set_pivot_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransform_set_pivot_Injected();
		Register_UnityEngine_RectTransform_set_pivot_Injected();

		//System.Void UnityEngine.RectTransform::set_sizeDelta_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransform_set_sizeDelta_Injected();
		Register_UnityEngine_RectTransform_set_sizeDelta_Injected();

	//End Registrations for type : UnityEngine.RectTransform

	//Start Registrations for type : UnityEngine.RectTransformUtility

		//System.Boolean UnityEngine.RectTransformUtility::PointInRectangle_Injected(UnityEngine.Vector2&,UnityEngine.RectTransform,UnityEngine.Camera,UnityEngine.Vector4&)
		void Register_UnityEngine_RectTransformUtility_PointInRectangle_Injected();
		Register_UnityEngine_RectTransformUtility_PointInRectangle_Injected();

		//System.Void UnityEngine.RectTransformUtility::PixelAdjustPoint_Injected(UnityEngine.Vector2&,UnityEngine.Transform,UnityEngine.Canvas,UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransformUtility_PixelAdjustPoint_Injected();
		Register_UnityEngine_RectTransformUtility_PixelAdjustPoint_Injected();

		//System.Void UnityEngine.RectTransformUtility::PixelAdjustRect_Injected(UnityEngine.RectTransform,UnityEngine.Canvas,UnityEngine.Rect&)
		void Register_UnityEngine_RectTransformUtility_PixelAdjustRect_Injected();
		Register_UnityEngine_RectTransformUtility_PixelAdjustRect_Injected();

	//End Registrations for type : UnityEngine.RectTransformUtility

	//Start Registrations for type : UnityEngine.Renderer

		//System.Boolean UnityEngine.Renderer::HasPropertyBlock()
		void Register_UnityEngine_Renderer_HasPropertyBlock();
		Register_UnityEngine_Renderer_HasPropertyBlock();

		//System.Boolean UnityEngine.Renderer::get_allowOcclusionWhenDynamic()
		void Register_UnityEngine_Renderer_get_allowOcclusionWhenDynamic();
		Register_UnityEngine_Renderer_get_allowOcclusionWhenDynamic();

		//System.Boolean UnityEngine.Renderer::get_enabled()
		void Register_UnityEngine_Renderer_get_enabled();
		Register_UnityEngine_Renderer_get_enabled();

		//System.Boolean UnityEngine.Renderer::get_forceRenderingOff()
		void Register_UnityEngine_Renderer_get_forceRenderingOff();
		Register_UnityEngine_Renderer_get_forceRenderingOff();

		//System.Boolean UnityEngine.Renderer::get_isPartOfStaticBatch()
		void Register_UnityEngine_Renderer_get_isPartOfStaticBatch();
		Register_UnityEngine_Renderer_get_isPartOfStaticBatch();

		//System.Boolean UnityEngine.Renderer::get_isVisible()
		void Register_UnityEngine_Renderer_get_isVisible();
		Register_UnityEngine_Renderer_get_isVisible();

		//System.Boolean UnityEngine.Renderer::get_receiveShadows()
		void Register_UnityEngine_Renderer_get_receiveShadows();
		Register_UnityEngine_Renderer_get_receiveShadows();

		//System.Int32 UnityEngine.Renderer::GetLightmapIndex(UnityEngineInternal.LightmapType)
		void Register_UnityEngine_Renderer_GetLightmapIndex();
		Register_UnityEngine_Renderer_GetLightmapIndex();

		//System.Int32 UnityEngine.Renderer::GetMaterialCount()
		void Register_UnityEngine_Renderer_GetMaterialCount();
		Register_UnityEngine_Renderer_GetMaterialCount();

		//System.Int32 UnityEngine.Renderer::get_rendererPriority()
		void Register_UnityEngine_Renderer_get_rendererPriority();
		Register_UnityEngine_Renderer_get_rendererPriority();

		//System.Int32 UnityEngine.Renderer::get_sortingGroupID()
		void Register_UnityEngine_Renderer_get_sortingGroupID();
		Register_UnityEngine_Renderer_get_sortingGroupID();

		//System.Int32 UnityEngine.Renderer::get_sortingGroupOrder()
		void Register_UnityEngine_Renderer_get_sortingGroupOrder();
		Register_UnityEngine_Renderer_get_sortingGroupOrder();

		//System.Int32 UnityEngine.Renderer::get_sortingLayerID()
		void Register_UnityEngine_Renderer_get_sortingLayerID();
		Register_UnityEngine_Renderer_get_sortingLayerID();

		//System.Int32 UnityEngine.Renderer::get_sortingOrder()
		void Register_UnityEngine_Renderer_get_sortingOrder();
		Register_UnityEngine_Renderer_get_sortingOrder();

		//System.String UnityEngine.Renderer::get_sortingLayerName()
		void Register_UnityEngine_Renderer_get_sortingLayerName();
		Register_UnityEngine_Renderer_get_sortingLayerName();

		//System.UInt32 UnityEngine.Renderer::get_renderingLayerMask()
		void Register_UnityEngine_Renderer_get_renderingLayerMask();
		Register_UnityEngine_Renderer_get_renderingLayerMask();

		//System.Void UnityEngine.Renderer::CopyMaterialArray(UnityEngine.Material[])
		void Register_UnityEngine_Renderer_CopyMaterialArray();
		Register_UnityEngine_Renderer_CopyMaterialArray();

		//System.Void UnityEngine.Renderer::CopySharedMaterialArray(UnityEngine.Material[])
		void Register_UnityEngine_Renderer_CopySharedMaterialArray();
		Register_UnityEngine_Renderer_CopySharedMaterialArray();

		//System.Void UnityEngine.Renderer::GetClosestReflectionProbesInternal(System.Object)
		void Register_UnityEngine_Renderer_GetClosestReflectionProbesInternal();
		Register_UnityEngine_Renderer_GetClosestReflectionProbesInternal();

		//System.Void UnityEngine.Renderer::GetLightmapST_Injected(UnityEngineInternal.LightmapType,UnityEngine.Vector4&)
		void Register_UnityEngine_Renderer_GetLightmapST_Injected();
		Register_UnityEngine_Renderer_GetLightmapST_Injected();

		//System.Void UnityEngine.Renderer::Internal_GetPropertyBlock(UnityEngine.MaterialPropertyBlock)
		void Register_UnityEngine_Renderer_Internal_GetPropertyBlock();
		Register_UnityEngine_Renderer_Internal_GetPropertyBlock();

		//System.Void UnityEngine.Renderer::Internal_GetPropertyBlockMaterialIndex(UnityEngine.MaterialPropertyBlock,System.Int32)
		void Register_UnityEngine_Renderer_Internal_GetPropertyBlockMaterialIndex();
		Register_UnityEngine_Renderer_Internal_GetPropertyBlockMaterialIndex();

		//System.Void UnityEngine.Renderer::Internal_SetPropertyBlock(UnityEngine.MaterialPropertyBlock)
		void Register_UnityEngine_Renderer_Internal_SetPropertyBlock();
		Register_UnityEngine_Renderer_Internal_SetPropertyBlock();

		//System.Void UnityEngine.Renderer::Internal_SetPropertyBlockMaterialIndex(UnityEngine.MaterialPropertyBlock,System.Int32)
		void Register_UnityEngine_Renderer_Internal_SetPropertyBlockMaterialIndex();
		Register_UnityEngine_Renderer_Internal_SetPropertyBlockMaterialIndex();

		//System.Void UnityEngine.Renderer::SetLightmapIndex(System.Int32,UnityEngineInternal.LightmapType)
		void Register_UnityEngine_Renderer_SetLightmapIndex();
		Register_UnityEngine_Renderer_SetLightmapIndex();

		//System.Void UnityEngine.Renderer::SetLightmapST_Injected(UnityEngine.Vector4&,UnityEngineInternal.LightmapType)
		void Register_UnityEngine_Renderer_SetLightmapST_Injected();
		Register_UnityEngine_Renderer_SetLightmapST_Injected();

		//System.Void UnityEngine.Renderer::SetMaterial(UnityEngine.Material)
		void Register_UnityEngine_Renderer_SetMaterial();
		Register_UnityEngine_Renderer_SetMaterial();

		//System.Void UnityEngine.Renderer::SetMaterialArray(UnityEngine.Material[])
		void Register_UnityEngine_Renderer_SetMaterialArray();
		Register_UnityEngine_Renderer_SetMaterialArray();

		//System.Void UnityEngine.Renderer::SetStaticLightmapST_Injected(UnityEngine.Vector4&)
		void Register_UnityEngine_Renderer_SetStaticLightmapST_Injected();
		Register_UnityEngine_Renderer_SetStaticLightmapST_Injected();

		//System.Void UnityEngine.Renderer::get_bounds_Injected(UnityEngine.Bounds&)
		void Register_UnityEngine_Renderer_get_bounds_Injected();
		Register_UnityEngine_Renderer_get_bounds_Injected();

		//System.Void UnityEngine.Renderer::get_localToWorldMatrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Renderer_get_localToWorldMatrix_Injected();
		Register_UnityEngine_Renderer_get_localToWorldMatrix_Injected();

		//System.Void UnityEngine.Renderer::get_worldToLocalMatrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Renderer_get_worldToLocalMatrix_Injected();
		Register_UnityEngine_Renderer_get_worldToLocalMatrix_Injected();

		//System.Void UnityEngine.Renderer::set_allowOcclusionWhenDynamic(System.Boolean)
		void Register_UnityEngine_Renderer_set_allowOcclusionWhenDynamic();
		Register_UnityEngine_Renderer_set_allowOcclusionWhenDynamic();

		//System.Void UnityEngine.Renderer::set_enabled(System.Boolean)
		void Register_UnityEngine_Renderer_set_enabled();
		Register_UnityEngine_Renderer_set_enabled();

		//System.Void UnityEngine.Renderer::set_forceRenderingOff(System.Boolean)
		void Register_UnityEngine_Renderer_set_forceRenderingOff();
		Register_UnityEngine_Renderer_set_forceRenderingOff();

		//System.Void UnityEngine.Renderer::set_lightProbeProxyVolumeOverride(UnityEngine.GameObject)
		void Register_UnityEngine_Renderer_set_lightProbeProxyVolumeOverride();
		Register_UnityEngine_Renderer_set_lightProbeProxyVolumeOverride();

		//System.Void UnityEngine.Renderer::set_lightProbeUsage(UnityEngine.Rendering.LightProbeUsage)
		void Register_UnityEngine_Renderer_set_lightProbeUsage();
		Register_UnityEngine_Renderer_set_lightProbeUsage();

		//System.Void UnityEngine.Renderer::set_motionVectorGenerationMode(UnityEngine.MotionVectorGenerationMode)
		void Register_UnityEngine_Renderer_set_motionVectorGenerationMode();
		Register_UnityEngine_Renderer_set_motionVectorGenerationMode();

		//System.Void UnityEngine.Renderer::set_probeAnchor(UnityEngine.Transform)
		void Register_UnityEngine_Renderer_set_probeAnchor();
		Register_UnityEngine_Renderer_set_probeAnchor();

		//System.Void UnityEngine.Renderer::set_rayTracingMode(UnityEngine.Experimental.Rendering.RayTracingMode)
		void Register_UnityEngine_Renderer_set_rayTracingMode();
		Register_UnityEngine_Renderer_set_rayTracingMode();

		//System.Void UnityEngine.Renderer::set_receiveShadows(System.Boolean)
		void Register_UnityEngine_Renderer_set_receiveShadows();
		Register_UnityEngine_Renderer_set_receiveShadows();

		//System.Void UnityEngine.Renderer::set_reflectionProbeUsage(UnityEngine.Rendering.ReflectionProbeUsage)
		void Register_UnityEngine_Renderer_set_reflectionProbeUsage();
		Register_UnityEngine_Renderer_set_reflectionProbeUsage();

		//System.Void UnityEngine.Renderer::set_rendererPriority(System.Int32)
		void Register_UnityEngine_Renderer_set_rendererPriority();
		Register_UnityEngine_Renderer_set_rendererPriority();

		//System.Void UnityEngine.Renderer::set_renderingLayerMask(System.UInt32)
		void Register_UnityEngine_Renderer_set_renderingLayerMask();
		Register_UnityEngine_Renderer_set_renderingLayerMask();

		//System.Void UnityEngine.Renderer::set_shadowCastingMode(UnityEngine.Rendering.ShadowCastingMode)
		void Register_UnityEngine_Renderer_set_shadowCastingMode();
		Register_UnityEngine_Renderer_set_shadowCastingMode();

		//System.Void UnityEngine.Renderer::set_sortingLayerID(System.Int32)
		void Register_UnityEngine_Renderer_set_sortingLayerID();
		Register_UnityEngine_Renderer_set_sortingLayerID();

		//System.Void UnityEngine.Renderer::set_sortingLayerName(System.String)
		void Register_UnityEngine_Renderer_set_sortingLayerName();
		Register_UnityEngine_Renderer_set_sortingLayerName();

		//System.Void UnityEngine.Renderer::set_sortingOrder(System.Int32)
		void Register_UnityEngine_Renderer_set_sortingOrder();
		Register_UnityEngine_Renderer_set_sortingOrder();

		//UnityEngine.Experimental.Rendering.RayTracingMode UnityEngine.Renderer::get_rayTracingMode()
		void Register_UnityEngine_Renderer_get_rayTracingMode();
		Register_UnityEngine_Renderer_get_rayTracingMode();

		//UnityEngine.GameObject UnityEngine.Renderer::get_lightProbeProxyVolumeOverride()
		void Register_UnityEngine_Renderer_get_lightProbeProxyVolumeOverride();
		Register_UnityEngine_Renderer_get_lightProbeProxyVolumeOverride();

		//UnityEngine.Material UnityEngine.Renderer::GetMaterial()
		void Register_UnityEngine_Renderer_GetMaterial();
		Register_UnityEngine_Renderer_GetMaterial();

		//UnityEngine.Material UnityEngine.Renderer::GetSharedMaterial()
		void Register_UnityEngine_Renderer_GetSharedMaterial();
		Register_UnityEngine_Renderer_GetSharedMaterial();

		//UnityEngine.Material[] UnityEngine.Renderer::GetMaterialArray()
		void Register_UnityEngine_Renderer_GetMaterialArray();
		Register_UnityEngine_Renderer_GetMaterialArray();

		//UnityEngine.Material[] UnityEngine.Renderer::GetSharedMaterialArray()
		void Register_UnityEngine_Renderer_GetSharedMaterialArray();
		Register_UnityEngine_Renderer_GetSharedMaterialArray();

		//UnityEngine.MotionVectorGenerationMode UnityEngine.Renderer::get_motionVectorGenerationMode()
		void Register_UnityEngine_Renderer_get_motionVectorGenerationMode();
		Register_UnityEngine_Renderer_get_motionVectorGenerationMode();

		//UnityEngine.Rendering.LightProbeUsage UnityEngine.Renderer::get_lightProbeUsage()
		void Register_UnityEngine_Renderer_get_lightProbeUsage();
		Register_UnityEngine_Renderer_get_lightProbeUsage();

		//UnityEngine.Rendering.ReflectionProbeUsage UnityEngine.Renderer::get_reflectionProbeUsage()
		void Register_UnityEngine_Renderer_get_reflectionProbeUsage();
		Register_UnityEngine_Renderer_get_reflectionProbeUsage();

		//UnityEngine.Rendering.ShadowCastingMode UnityEngine.Renderer::get_shadowCastingMode()
		void Register_UnityEngine_Renderer_get_shadowCastingMode();
		Register_UnityEngine_Renderer_get_shadowCastingMode();

		//UnityEngine.Transform UnityEngine.Renderer::get_probeAnchor()
		void Register_UnityEngine_Renderer_get_probeAnchor();
		Register_UnityEngine_Renderer_get_probeAnchor();

	//End Registrations for type : UnityEngine.Renderer

	//Start Registrations for type : UnityEngine.Rendering.CommandBuffer

		//System.IntPtr UnityEngine.Rendering.CommandBuffer::InitBuffer()
		void Register_UnityEngine_Rendering_CommandBuffer_InitBuffer();
		Register_UnityEngine_Rendering_CommandBuffer_InitBuffer();

		//System.Void UnityEngine.Rendering.CommandBuffer::ReleaseBuffer()
		void Register_UnityEngine_Rendering_CommandBuffer_ReleaseBuffer();
		Register_UnityEngine_Rendering_CommandBuffer_ReleaseBuffer();

	//End Registrations for type : UnityEngine.Rendering.CommandBuffer

	//Start Registrations for type : UnityEngine.Rendering.GraphicsSettings

		//System.Boolean UnityEngine.Rendering.GraphicsSettings::get_lightsUseLinearIntensity()
		void Register_UnityEngine_Rendering_GraphicsSettings_get_lightsUseLinearIntensity();
		Register_UnityEngine_Rendering_GraphicsSettings_get_lightsUseLinearIntensity();

	//End Registrations for type : UnityEngine.Rendering.GraphicsSettings

	//Start Registrations for type : UnityEngine.Rendering.ScriptableRenderContext

		//System.Int32 UnityEngine.Rendering.ScriptableRenderContext::GetNumberOfCameras_Internal_Injected(UnityEngine.Rendering.ScriptableRenderContext&)
		void Register_UnityEngine_Rendering_ScriptableRenderContext_GetNumberOfCameras_Internal_Injected();
		Register_UnityEngine_Rendering_ScriptableRenderContext_GetNumberOfCameras_Internal_Injected();

		//UnityEngine.Camera UnityEngine.Rendering.ScriptableRenderContext::GetCamera_Internal_Injected(UnityEngine.Rendering.ScriptableRenderContext&,System.Int32)
		void Register_UnityEngine_Rendering_ScriptableRenderContext_GetCamera_Internal_Injected();
		Register_UnityEngine_Rendering_ScriptableRenderContext_GetCamera_Internal_Injected();

	//End Registrations for type : UnityEngine.Rendering.ScriptableRenderContext

	//Start Registrations for type : UnityEngine.Rendering.SortingGroup

		//System.Int32 UnityEngine.Rendering.SortingGroup::get_invalidSortingGroupID()
		void Register_UnityEngine_Rendering_SortingGroup_get_invalidSortingGroupID();
		Register_UnityEngine_Rendering_SortingGroup_get_invalidSortingGroupID();

		//System.Int32 UnityEngine.Rendering.SortingGroup::get_sortingLayerID()
		void Register_UnityEngine_Rendering_SortingGroup_get_sortingLayerID();
		Register_UnityEngine_Rendering_SortingGroup_get_sortingLayerID();

		//System.Int32 UnityEngine.Rendering.SortingGroup::get_sortingOrder()
		void Register_UnityEngine_Rendering_SortingGroup_get_sortingOrder();
		Register_UnityEngine_Rendering_SortingGroup_get_sortingOrder();

		//UnityEngine.Rendering.SortingGroup UnityEngine.Rendering.SortingGroup::GetSortingGroupByIndex(System.Int32)
		void Register_UnityEngine_Rendering_SortingGroup_GetSortingGroupByIndex();
		Register_UnityEngine_Rendering_SortingGroup_GetSortingGroupByIndex();

	//End Registrations for type : UnityEngine.Rendering.SortingGroup

	//Start Registrations for type : UnityEngine.RenderTexture

		//System.Int32 UnityEngine.RenderTexture::get_height()
		void Register_UnityEngine_RenderTexture_get_height();
		Register_UnityEngine_RenderTexture_get_height();

		//System.Int32 UnityEngine.RenderTexture::get_width()
		void Register_UnityEngine_RenderTexture_get_width();
		Register_UnityEngine_RenderTexture_get_width();

		//System.Void UnityEngine.RenderTexture::GetDescriptor_Injected(UnityEngine.RenderTextureDescriptor&)
		void Register_UnityEngine_RenderTexture_GetDescriptor_Injected();
		Register_UnityEngine_RenderTexture_GetDescriptor_Injected();

		//System.Void UnityEngine.RenderTexture::Internal_Create(UnityEngine.RenderTexture)
		void Register_UnityEngine_RenderTexture_Internal_Create();
		Register_UnityEngine_RenderTexture_Internal_Create();

		//System.Void UnityEngine.RenderTexture::SetActive(UnityEngine.RenderTexture)
		void Register_UnityEngine_RenderTexture_SetActive();
		Register_UnityEngine_RenderTexture_SetActive();

		//System.Void UnityEngine.RenderTexture::SetRenderTextureDescriptor_Injected(UnityEngine.RenderTextureDescriptor&)
		void Register_UnityEngine_RenderTexture_SetRenderTextureDescriptor_Injected();
		Register_UnityEngine_RenderTexture_SetRenderTextureDescriptor_Injected();

		//System.Void UnityEngine.RenderTexture::SetSRGBReadWrite(System.Boolean)
		void Register_UnityEngine_RenderTexture_SetSRGBReadWrite();
		Register_UnityEngine_RenderTexture_SetSRGBReadWrite();

		//System.Void UnityEngine.RenderTexture::set_depth(System.Int32)
		void Register_UnityEngine_RenderTexture_set_depth();
		Register_UnityEngine_RenderTexture_set_depth();

		//System.Void UnityEngine.RenderTexture::set_dimension(UnityEngine.Rendering.TextureDimension)
		void Register_UnityEngine_RenderTexture_set_dimension();
		Register_UnityEngine_RenderTexture_set_dimension();

		//System.Void UnityEngine.RenderTexture::set_graphicsFormat(UnityEngine.Experimental.Rendering.GraphicsFormat)
		void Register_UnityEngine_RenderTexture_set_graphicsFormat();
		Register_UnityEngine_RenderTexture_set_graphicsFormat();

		//System.Void UnityEngine.RenderTexture::set_height(System.Int32)
		void Register_UnityEngine_RenderTexture_set_height();
		Register_UnityEngine_RenderTexture_set_height();

		//System.Void UnityEngine.RenderTexture::set_width(System.Int32)
		void Register_UnityEngine_RenderTexture_set_width();
		Register_UnityEngine_RenderTexture_set_width();

		//UnityEngine.Rendering.TextureDimension UnityEngine.RenderTexture::get_dimension()
		void Register_UnityEngine_RenderTexture_get_dimension();
		Register_UnityEngine_RenderTexture_get_dimension();

	//End Registrations for type : UnityEngine.RenderTexture

	//Start Registrations for type : UnityEngine.Resources

		//UnityEngine.AsyncOperation UnityEngine.Resources::UnloadUnusedAssets()
		void Register_UnityEngine_Resources_UnloadUnusedAssets();
		Register_UnityEngine_Resources_UnloadUnusedAssets();

		//UnityEngine.Object UnityEngine.Resources::GetBuiltinResource(System.Type,System.String)
		void Register_UnityEngine_Resources_GetBuiltinResource();
		Register_UnityEngine_Resources_GetBuiltinResource();

	//End Registrations for type : UnityEngine.Resources

	//Start Registrations for type : UnityEngine.ResourcesAPIInternal

		//System.Void UnityEngine.ResourcesAPIInternal::UnloadAsset(UnityEngine.Object)
		void Register_UnityEngine_ResourcesAPIInternal_UnloadAsset();
		Register_UnityEngine_ResourcesAPIInternal_UnloadAsset();

		//UnityEngine.Object UnityEngine.ResourcesAPIInternal::Load(System.String,System.Type)
		void Register_UnityEngine_ResourcesAPIInternal_Load();
		Register_UnityEngine_ResourcesAPIInternal_Load();

		//UnityEngine.Object[] UnityEngine.ResourcesAPIInternal::FindObjectsOfTypeAll(System.Type)
		void Register_UnityEngine_ResourcesAPIInternal_FindObjectsOfTypeAll();
		Register_UnityEngine_ResourcesAPIInternal_FindObjectsOfTypeAll();

		//UnityEngine.Object[] UnityEngine.ResourcesAPIInternal::LoadAll(System.String,System.Type)
		void Register_UnityEngine_ResourcesAPIInternal_LoadAll();
		Register_UnityEngine_ResourcesAPIInternal_LoadAll();

		//UnityEngine.Shader UnityEngine.ResourcesAPIInternal::FindShaderByName(System.String)
		void Register_UnityEngine_ResourcesAPIInternal_FindShaderByName();
		Register_UnityEngine_ResourcesAPIInternal_FindShaderByName();

	//End Registrations for type : UnityEngine.ResourcesAPIInternal

	//Start Registrations for type : UnityEngine.Rigidbody

		//System.Boolean UnityEngine.Rigidbody::IsSleeping()
		void Register_UnityEngine_Rigidbody_IsSleeping();
		Register_UnityEngine_Rigidbody_IsSleeping();

		//System.Boolean UnityEngine.Rigidbody::get_detectCollisions()
		void Register_UnityEngine_Rigidbody_get_detectCollisions();
		Register_UnityEngine_Rigidbody_get_detectCollisions();

		//System.Boolean UnityEngine.Rigidbody::get_freezeRotation()
		void Register_UnityEngine_Rigidbody_get_freezeRotation();
		Register_UnityEngine_Rigidbody_get_freezeRotation();

		//System.Boolean UnityEngine.Rigidbody::get_isKinematic()
		void Register_UnityEngine_Rigidbody_get_isKinematic();
		Register_UnityEngine_Rigidbody_get_isKinematic();

		//System.Boolean UnityEngine.Rigidbody::get_useGravity()
		void Register_UnityEngine_Rigidbody_get_useGravity();
		Register_UnityEngine_Rigidbody_get_useGravity();

		//System.Int32 UnityEngine.Rigidbody::get_solverIterations()
		void Register_UnityEngine_Rigidbody_get_solverIterations();
		Register_UnityEngine_Rigidbody_get_solverIterations();

		//System.Int32 UnityEngine.Rigidbody::get_solverVelocityIterations()
		void Register_UnityEngine_Rigidbody_get_solverVelocityIterations();
		Register_UnityEngine_Rigidbody_get_solverVelocityIterations();

		//System.Single UnityEngine.Rigidbody::get_angularDrag()
		void Register_UnityEngine_Rigidbody_get_angularDrag();
		Register_UnityEngine_Rigidbody_get_angularDrag();

		//System.Single UnityEngine.Rigidbody::get_drag()
		void Register_UnityEngine_Rigidbody_get_drag();
		Register_UnityEngine_Rigidbody_get_drag();

		//System.Single UnityEngine.Rigidbody::get_mass()
		void Register_UnityEngine_Rigidbody_get_mass();
		Register_UnityEngine_Rigidbody_get_mass();

		//System.Single UnityEngine.Rigidbody::get_maxAngularVelocity()
		void Register_UnityEngine_Rigidbody_get_maxAngularVelocity();
		Register_UnityEngine_Rigidbody_get_maxAngularVelocity();

		//System.Single UnityEngine.Rigidbody::get_maxDepenetrationVelocity()
		void Register_UnityEngine_Rigidbody_get_maxDepenetrationVelocity();
		Register_UnityEngine_Rigidbody_get_maxDepenetrationVelocity();

		//System.Single UnityEngine.Rigidbody::get_sleepThreshold()
		void Register_UnityEngine_Rigidbody_get_sleepThreshold();
		Register_UnityEngine_Rigidbody_get_sleepThreshold();

		//System.Void UnityEngine.Rigidbody::AddExplosionForce_Injected(System.Single,UnityEngine.Vector3&,System.Single,System.Single,UnityEngine.ForceMode)
		void Register_UnityEngine_Rigidbody_AddExplosionForce_Injected();
		Register_UnityEngine_Rigidbody_AddExplosionForce_Injected();

		//System.Void UnityEngine.Rigidbody::AddForceAtPosition_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.ForceMode)
		void Register_UnityEngine_Rigidbody_AddForceAtPosition_Injected();
		Register_UnityEngine_Rigidbody_AddForceAtPosition_Injected();

		//System.Void UnityEngine.Rigidbody::AddForce_Injected(UnityEngine.Vector3&,UnityEngine.ForceMode)
		void Register_UnityEngine_Rigidbody_AddForce_Injected();
		Register_UnityEngine_Rigidbody_AddForce_Injected();

		//System.Void UnityEngine.Rigidbody::AddRelativeForce_Injected(UnityEngine.Vector3&,UnityEngine.ForceMode)
		void Register_UnityEngine_Rigidbody_AddRelativeForce_Injected();
		Register_UnityEngine_Rigidbody_AddRelativeForce_Injected();

		//System.Void UnityEngine.Rigidbody::AddRelativeTorque_Injected(UnityEngine.Vector3&,UnityEngine.ForceMode)
		void Register_UnityEngine_Rigidbody_AddRelativeTorque_Injected();
		Register_UnityEngine_Rigidbody_AddRelativeTorque_Injected();

		//System.Void UnityEngine.Rigidbody::AddTorque_Injected(UnityEngine.Vector3&,UnityEngine.ForceMode)
		void Register_UnityEngine_Rigidbody_AddTorque_Injected();
		Register_UnityEngine_Rigidbody_AddTorque_Injected();

		//System.Void UnityEngine.Rigidbody::GetPointVelocity_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)
		void Register_UnityEngine_Rigidbody_GetPointVelocity_Injected();
		Register_UnityEngine_Rigidbody_GetPointVelocity_Injected();

		//System.Void UnityEngine.Rigidbody::GetRelativePointVelocity_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)
		void Register_UnityEngine_Rigidbody_GetRelativePointVelocity_Injected();
		Register_UnityEngine_Rigidbody_GetRelativePointVelocity_Injected();

		//System.Void UnityEngine.Rigidbody::Internal_ClosestPointOnBounds_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single&)
		void Register_UnityEngine_Rigidbody_Internal_ClosestPointOnBounds_Injected();
		Register_UnityEngine_Rigidbody_Internal_ClosestPointOnBounds_Injected();

		//System.Void UnityEngine.Rigidbody::MovePosition_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Rigidbody_MovePosition_Injected();
		Register_UnityEngine_Rigidbody_MovePosition_Injected();

		//System.Void UnityEngine.Rigidbody::MoveRotation_Injected(UnityEngine.Quaternion&)
		void Register_UnityEngine_Rigidbody_MoveRotation_Injected();
		Register_UnityEngine_Rigidbody_MoveRotation_Injected();

		//System.Void UnityEngine.Rigidbody::ResetCenterOfMass()
		void Register_UnityEngine_Rigidbody_ResetCenterOfMass();
		Register_UnityEngine_Rigidbody_ResetCenterOfMass();

		//System.Void UnityEngine.Rigidbody::ResetInertiaTensor()
		void Register_UnityEngine_Rigidbody_ResetInertiaTensor();
		Register_UnityEngine_Rigidbody_ResetInertiaTensor();

		//System.Void UnityEngine.Rigidbody::SetDensity(System.Single)
		void Register_UnityEngine_Rigidbody_SetDensity();
		Register_UnityEngine_Rigidbody_SetDensity();

		//System.Void UnityEngine.Rigidbody::Sleep()
		void Register_UnityEngine_Rigidbody_Sleep();
		Register_UnityEngine_Rigidbody_Sleep();

		//System.Void UnityEngine.Rigidbody::SweepTest_Injected(UnityEngine.Vector3&,System.Single,UnityEngine.QueryTriggerInteraction,System.Boolean&,UnityEngine.RaycastHit&)
		void Register_UnityEngine_Rigidbody_SweepTest_Injected();
		Register_UnityEngine_Rigidbody_SweepTest_Injected();

		//System.Void UnityEngine.Rigidbody::WakeUp()
		void Register_UnityEngine_Rigidbody_WakeUp();
		Register_UnityEngine_Rigidbody_WakeUp();

		//System.Void UnityEngine.Rigidbody::get_angularVelocity_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Rigidbody_get_angularVelocity_Injected();
		Register_UnityEngine_Rigidbody_get_angularVelocity_Injected();

		//System.Void UnityEngine.Rigidbody::get_centerOfMass_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Rigidbody_get_centerOfMass_Injected();
		Register_UnityEngine_Rigidbody_get_centerOfMass_Injected();

		//System.Void UnityEngine.Rigidbody::get_inertiaTensorRotation_Injected(UnityEngine.Quaternion&)
		void Register_UnityEngine_Rigidbody_get_inertiaTensorRotation_Injected();
		Register_UnityEngine_Rigidbody_get_inertiaTensorRotation_Injected();

		//System.Void UnityEngine.Rigidbody::get_inertiaTensor_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Rigidbody_get_inertiaTensor_Injected();
		Register_UnityEngine_Rigidbody_get_inertiaTensor_Injected();

		//System.Void UnityEngine.Rigidbody::get_position_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Rigidbody_get_position_Injected();
		Register_UnityEngine_Rigidbody_get_position_Injected();

		//System.Void UnityEngine.Rigidbody::get_rotation_Injected(UnityEngine.Quaternion&)
		void Register_UnityEngine_Rigidbody_get_rotation_Injected();
		Register_UnityEngine_Rigidbody_get_rotation_Injected();

		//System.Void UnityEngine.Rigidbody::get_velocity_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Rigidbody_get_velocity_Injected();
		Register_UnityEngine_Rigidbody_get_velocity_Injected();

		//System.Void UnityEngine.Rigidbody::get_worldCenterOfMass_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Rigidbody_get_worldCenterOfMass_Injected();
		Register_UnityEngine_Rigidbody_get_worldCenterOfMass_Injected();

		//System.Void UnityEngine.Rigidbody::set_angularDrag(System.Single)
		void Register_UnityEngine_Rigidbody_set_angularDrag();
		Register_UnityEngine_Rigidbody_set_angularDrag();

		//System.Void UnityEngine.Rigidbody::set_angularVelocity_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Rigidbody_set_angularVelocity_Injected();
		Register_UnityEngine_Rigidbody_set_angularVelocity_Injected();

		//System.Void UnityEngine.Rigidbody::set_centerOfMass_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Rigidbody_set_centerOfMass_Injected();
		Register_UnityEngine_Rigidbody_set_centerOfMass_Injected();

		//System.Void UnityEngine.Rigidbody::set_collisionDetectionMode(UnityEngine.CollisionDetectionMode)
		void Register_UnityEngine_Rigidbody_set_collisionDetectionMode();
		Register_UnityEngine_Rigidbody_set_collisionDetectionMode();

		//System.Void UnityEngine.Rigidbody::set_constraints(UnityEngine.RigidbodyConstraints)
		void Register_UnityEngine_Rigidbody_set_constraints();
		Register_UnityEngine_Rigidbody_set_constraints();

		//System.Void UnityEngine.Rigidbody::set_detectCollisions(System.Boolean)
		void Register_UnityEngine_Rigidbody_set_detectCollisions();
		Register_UnityEngine_Rigidbody_set_detectCollisions();

		//System.Void UnityEngine.Rigidbody::set_drag(System.Single)
		void Register_UnityEngine_Rigidbody_set_drag();
		Register_UnityEngine_Rigidbody_set_drag();

		//System.Void UnityEngine.Rigidbody::set_freezeRotation(System.Boolean)
		void Register_UnityEngine_Rigidbody_set_freezeRotation();
		Register_UnityEngine_Rigidbody_set_freezeRotation();

		//System.Void UnityEngine.Rigidbody::set_inertiaTensorRotation_Injected(UnityEngine.Quaternion&)
		void Register_UnityEngine_Rigidbody_set_inertiaTensorRotation_Injected();
		Register_UnityEngine_Rigidbody_set_inertiaTensorRotation_Injected();

		//System.Void UnityEngine.Rigidbody::set_inertiaTensor_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Rigidbody_set_inertiaTensor_Injected();
		Register_UnityEngine_Rigidbody_set_inertiaTensor_Injected();

		//System.Void UnityEngine.Rigidbody::set_interpolation(UnityEngine.RigidbodyInterpolation)
		void Register_UnityEngine_Rigidbody_set_interpolation();
		Register_UnityEngine_Rigidbody_set_interpolation();

		//System.Void UnityEngine.Rigidbody::set_isKinematic(System.Boolean)
		void Register_UnityEngine_Rigidbody_set_isKinematic();
		Register_UnityEngine_Rigidbody_set_isKinematic();

		//System.Void UnityEngine.Rigidbody::set_mass(System.Single)
		void Register_UnityEngine_Rigidbody_set_mass();
		Register_UnityEngine_Rigidbody_set_mass();

		//System.Void UnityEngine.Rigidbody::set_maxAngularVelocity(System.Single)
		void Register_UnityEngine_Rigidbody_set_maxAngularVelocity();
		Register_UnityEngine_Rigidbody_set_maxAngularVelocity();

		//System.Void UnityEngine.Rigidbody::set_maxDepenetrationVelocity(System.Single)
		void Register_UnityEngine_Rigidbody_set_maxDepenetrationVelocity();
		Register_UnityEngine_Rigidbody_set_maxDepenetrationVelocity();

		//System.Void UnityEngine.Rigidbody::set_position_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Rigidbody_set_position_Injected();
		Register_UnityEngine_Rigidbody_set_position_Injected();

		//System.Void UnityEngine.Rigidbody::set_rotation_Injected(UnityEngine.Quaternion&)
		void Register_UnityEngine_Rigidbody_set_rotation_Injected();
		Register_UnityEngine_Rigidbody_set_rotation_Injected();

		//System.Void UnityEngine.Rigidbody::set_sleepThreshold(System.Single)
		void Register_UnityEngine_Rigidbody_set_sleepThreshold();
		Register_UnityEngine_Rigidbody_set_sleepThreshold();

		//System.Void UnityEngine.Rigidbody::set_solverIterations(System.Int32)
		void Register_UnityEngine_Rigidbody_set_solverIterations();
		Register_UnityEngine_Rigidbody_set_solverIterations();

		//System.Void UnityEngine.Rigidbody::set_solverVelocityIterations(System.Int32)
		void Register_UnityEngine_Rigidbody_set_solverVelocityIterations();
		Register_UnityEngine_Rigidbody_set_solverVelocityIterations();

		//System.Void UnityEngine.Rigidbody::set_useGravity(System.Boolean)
		void Register_UnityEngine_Rigidbody_set_useGravity();
		Register_UnityEngine_Rigidbody_set_useGravity();

		//System.Void UnityEngine.Rigidbody::set_velocity_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Rigidbody_set_velocity_Injected();
		Register_UnityEngine_Rigidbody_set_velocity_Injected();

		//UnityEngine.CollisionDetectionMode UnityEngine.Rigidbody::get_collisionDetectionMode()
		void Register_UnityEngine_Rigidbody_get_collisionDetectionMode();
		Register_UnityEngine_Rigidbody_get_collisionDetectionMode();

		//UnityEngine.RaycastHit[] UnityEngine.Rigidbody::Internal_SweepTestAll_Injected(UnityEngine.Vector3&,System.Single,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_Rigidbody_Internal_SweepTestAll_Injected();
		Register_UnityEngine_Rigidbody_Internal_SweepTestAll_Injected();

		//UnityEngine.RigidbodyConstraints UnityEngine.Rigidbody::get_constraints()
		void Register_UnityEngine_Rigidbody_get_constraints();
		Register_UnityEngine_Rigidbody_get_constraints();

		//UnityEngine.RigidbodyInterpolation UnityEngine.Rigidbody::get_interpolation()
		void Register_UnityEngine_Rigidbody_get_interpolation();
		Register_UnityEngine_Rigidbody_get_interpolation();

	//End Registrations for type : UnityEngine.Rigidbody

	//Start Registrations for type : UnityEngine.Rigidbody2D

		//System.Single UnityEngine.Rigidbody2D::get_mass()
		void Register_UnityEngine_Rigidbody2D_get_mass();
		Register_UnityEngine_Rigidbody2D_get_mass();

		//System.Single UnityEngine.Rigidbody2D::get_rotation()
		void Register_UnityEngine_Rigidbody2D_get_rotation();
		Register_UnityEngine_Rigidbody2D_get_rotation();

		//System.Void UnityEngine.Rigidbody2D::MovePosition_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Rigidbody2D_MovePosition_Injected();
		Register_UnityEngine_Rigidbody2D_MovePosition_Injected();

		//System.Void UnityEngine.Rigidbody2D::MoveRotation_Angle(System.Single)
		void Register_UnityEngine_Rigidbody2D_MoveRotation_Angle();
		Register_UnityEngine_Rigidbody2D_MoveRotation_Angle();

		//System.Void UnityEngine.Rigidbody2D::get_position_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Rigidbody2D_get_position_Injected();
		Register_UnityEngine_Rigidbody2D_get_position_Injected();

		//System.Void UnityEngine.Rigidbody2D::set_bodyType(UnityEngine.RigidbodyType2D)
		void Register_UnityEngine_Rigidbody2D_set_bodyType();
		Register_UnityEngine_Rigidbody2D_set_bodyType();

		//System.Void UnityEngine.Rigidbody2D::set_gravityScale(System.Single)
		void Register_UnityEngine_Rigidbody2D_set_gravityScale();
		Register_UnityEngine_Rigidbody2D_set_gravityScale();

		//System.Void UnityEngine.Rigidbody2D::set_mass(System.Single)
		void Register_UnityEngine_Rigidbody2D_set_mass();
		Register_UnityEngine_Rigidbody2D_set_mass();

		//System.Void UnityEngine.Rigidbody2D::set_position_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Rigidbody2D_set_position_Injected();
		Register_UnityEngine_Rigidbody2D_set_position_Injected();

	//End Registrations for type : UnityEngine.Rigidbody2D

	//Start Registrations for type : UnityEngine.SceneManagement.Scene

		//System.Int32 UnityEngine.SceneManagement.Scene::GetBuildIndexInternal(System.Int32)
		void Register_UnityEngine_SceneManagement_Scene_GetBuildIndexInternal();
		Register_UnityEngine_SceneManagement_Scene_GetBuildIndexInternal();

		//System.String UnityEngine.SceneManagement.Scene::GetNameInternal(System.Int32)
		void Register_UnityEngine_SceneManagement_Scene_GetNameInternal();
		Register_UnityEngine_SceneManagement_Scene_GetNameInternal();

	//End Registrations for type : UnityEngine.SceneManagement.Scene

	//Start Registrations for type : UnityEngine.SceneManagement.SceneManager

		//System.Int32 UnityEngine.SceneManagement.SceneManager::get_sceneCount()
		void Register_UnityEngine_SceneManagement_SceneManager_get_sceneCount();
		Register_UnityEngine_SceneManagement_SceneManager_get_sceneCount();

		//System.Void UnityEngine.SceneManagement.SceneManager::GetActiveScene_Injected(UnityEngine.SceneManagement.Scene&)
		void Register_UnityEngine_SceneManagement_SceneManager_GetActiveScene_Injected();
		Register_UnityEngine_SceneManagement_SceneManager_GetActiveScene_Injected();

		//System.Void UnityEngine.SceneManagement.SceneManager::GetSceneAt_Injected(System.Int32,UnityEngine.SceneManagement.Scene&)
		void Register_UnityEngine_SceneManagement_SceneManager_GetSceneAt_Injected();
		Register_UnityEngine_SceneManagement_SceneManager_GetSceneAt_Injected();

		//System.Void UnityEngine.SceneManagement.SceneManager::MoveGameObjectToScene_Injected(UnityEngine.GameObject,UnityEngine.SceneManagement.Scene&)
		void Register_UnityEngine_SceneManagement_SceneManager_MoveGameObjectToScene_Injected();
		Register_UnityEngine_SceneManagement_SceneManager_MoveGameObjectToScene_Injected();

	//End Registrations for type : UnityEngine.SceneManagement.SceneManager

	//Start Registrations for type : UnityEngine.SceneManagement.SceneManagerAPIInternal

		//System.Int32 UnityEngine.SceneManagement.SceneManagerAPIInternal::GetNumScenesInBuildSettings()
		void Register_UnityEngine_SceneManagement_SceneManagerAPIInternal_GetNumScenesInBuildSettings();
		Register_UnityEngine_SceneManagement_SceneManagerAPIInternal_GetNumScenesInBuildSettings();

		//UnityEngine.AsyncOperation UnityEngine.SceneManagement.SceneManagerAPIInternal::LoadSceneAsyncNameIndexInternal_Injected(System.String,System.Int32,UnityEngine.SceneManagement.LoadSceneParameters&,System.Boolean)
		void Register_UnityEngine_SceneManagement_SceneManagerAPIInternal_LoadSceneAsyncNameIndexInternal_Injected();
		Register_UnityEngine_SceneManagement_SceneManagerAPIInternal_LoadSceneAsyncNameIndexInternal_Injected();

	//End Registrations for type : UnityEngine.SceneManagement.SceneManagerAPIInternal

	//Start Registrations for type : UnityEngine.Screen

		//System.Boolean UnityEngine.Screen::IsOrientationEnabled(UnityEngine.EnabledOrientation)
		void Register_UnityEngine_Screen_IsOrientationEnabled();
		Register_UnityEngine_Screen_IsOrientationEnabled();

		//System.Boolean UnityEngine.Screen::get_fullScreen()
		void Register_UnityEngine_Screen_get_fullScreen();
		Register_UnityEngine_Screen_get_fullScreen();

		//System.Int32 UnityEngine.Screen::get_height()
		void Register_UnityEngine_Screen_get_height();
		Register_UnityEngine_Screen_get_height();

		//System.Int32 UnityEngine.Screen::get_sleepTimeout()
		void Register_UnityEngine_Screen_get_sleepTimeout();
		Register_UnityEngine_Screen_get_sleepTimeout();

		//System.Int32 UnityEngine.Screen::get_width()
		void Register_UnityEngine_Screen_get_width();
		Register_UnityEngine_Screen_get_width();

		//System.Single UnityEngine.Screen::get_brightness()
		void Register_UnityEngine_Screen_get_brightness();
		Register_UnityEngine_Screen_get_brightness();

		//System.Single UnityEngine.Screen::get_dpi()
		void Register_UnityEngine_Screen_get_dpi();
		Register_UnityEngine_Screen_get_dpi();

		//System.Void UnityEngine.Screen::RequestOrientation(UnityEngine.ScreenOrientation)
		void Register_UnityEngine_Screen_RequestOrientation();
		Register_UnityEngine_Screen_RequestOrientation();

		//System.Void UnityEngine.Screen::SetOrientationEnabled(UnityEngine.EnabledOrientation,System.Boolean)
		void Register_UnityEngine_Screen_SetOrientationEnabled();
		Register_UnityEngine_Screen_SetOrientationEnabled();

		//System.Void UnityEngine.Screen::SetResolution(System.Int32,System.Int32,UnityEngine.FullScreenMode,System.Int32)
		void Register_UnityEngine_Screen_SetResolution();
		Register_UnityEngine_Screen_SetResolution();

		//System.Void UnityEngine.Screen::get_currentResolution_Injected(UnityEngine.Resolution&)
		void Register_UnityEngine_Screen_get_currentResolution_Injected();
		Register_UnityEngine_Screen_get_currentResolution_Injected();

		//System.Void UnityEngine.Screen::get_safeArea_Injected(UnityEngine.Rect&)
		void Register_UnityEngine_Screen_get_safeArea_Injected();
		Register_UnityEngine_Screen_get_safeArea_Injected();

		//System.Void UnityEngine.Screen::set_brightness(System.Single)
		void Register_UnityEngine_Screen_set_brightness();
		Register_UnityEngine_Screen_set_brightness();

		//System.Void UnityEngine.Screen::set_fullScreen(System.Boolean)
		void Register_UnityEngine_Screen_set_fullScreen();
		Register_UnityEngine_Screen_set_fullScreen();

		//System.Void UnityEngine.Screen::set_fullScreenMode(UnityEngine.FullScreenMode)
		void Register_UnityEngine_Screen_set_fullScreenMode();
		Register_UnityEngine_Screen_set_fullScreenMode();

		//System.Void UnityEngine.Screen::set_sleepTimeout(System.Int32)
		void Register_UnityEngine_Screen_set_sleepTimeout();
		Register_UnityEngine_Screen_set_sleepTimeout();

		//UnityEngine.FullScreenMode UnityEngine.Screen::get_fullScreenMode()
		void Register_UnityEngine_Screen_get_fullScreenMode();
		Register_UnityEngine_Screen_get_fullScreenMode();

		//UnityEngine.Rect[] UnityEngine.Screen::get_cutouts()
		void Register_UnityEngine_Screen_get_cutouts();
		Register_UnityEngine_Screen_get_cutouts();

		//UnityEngine.Resolution[] UnityEngine.Screen::get_resolutions()
		void Register_UnityEngine_Screen_get_resolutions();
		Register_UnityEngine_Screen_get_resolutions();

		//UnityEngine.ScreenOrientation UnityEngine.Screen::GetScreenOrientation()
		void Register_UnityEngine_Screen_GetScreenOrientation();
		Register_UnityEngine_Screen_GetScreenOrientation();

	//End Registrations for type : UnityEngine.Screen

	//Start Registrations for type : UnityEngine.ScriptableObject

		//System.Void UnityEngine.ScriptableObject::CreateScriptableObject(UnityEngine.ScriptableObject)
		void Register_UnityEngine_ScriptableObject_CreateScriptableObject();
		Register_UnityEngine_ScriptableObject_CreateScriptableObject();

		//UnityEngine.ScriptableObject UnityEngine.ScriptableObject::CreateScriptableObjectInstanceFromType(System.Type,System.Boolean)
		void Register_UnityEngine_ScriptableObject_CreateScriptableObjectInstanceFromType();
		Register_UnityEngine_ScriptableObject_CreateScriptableObjectInstanceFromType();

	//End Registrations for type : UnityEngine.ScriptableObject

	//Start Registrations for type : UnityEngine.Shader

		//System.Boolean UnityEngine.Shader::IsKeywordEnabled(System.String)
		void Register_UnityEngine_Shader_IsKeywordEnabled();
		Register_UnityEngine_Shader_IsKeywordEnabled();

		//System.Boolean UnityEngine.Shader::get_isSupported()
		void Register_UnityEngine_Shader_get_isSupported();
		Register_UnityEngine_Shader_get_isSupported();

		//System.Int32 UnityEngine.Shader::FindPropertyIndex(System.String)
		void Register_UnityEngine_Shader_FindPropertyIndex();
		Register_UnityEngine_Shader_FindPropertyIndex();

		//System.Int32 UnityEngine.Shader::GetGlobalFloatArrayCountImpl(System.Int32)
		void Register_UnityEngine_Shader_GetGlobalFloatArrayCountImpl();
		Register_UnityEngine_Shader_GetGlobalFloatArrayCountImpl();

		//System.Int32 UnityEngine.Shader::GetGlobalMatrixArrayCountImpl(System.Int32)
		void Register_UnityEngine_Shader_GetGlobalMatrixArrayCountImpl();
		Register_UnityEngine_Shader_GetGlobalMatrixArrayCountImpl();

		//System.Int32 UnityEngine.Shader::GetGlobalVectorArrayCountImpl(System.Int32)
		void Register_UnityEngine_Shader_GetGlobalVectorArrayCountImpl();
		Register_UnityEngine_Shader_GetGlobalVectorArrayCountImpl();

		//System.Int32 UnityEngine.Shader::GetPropertyCount()
		void Register_UnityEngine_Shader_GetPropertyCount();
		Register_UnityEngine_Shader_GetPropertyCount();

		//System.Int32 UnityEngine.Shader::GetPropertyNameId(UnityEngine.Shader,System.Int32)
		void Register_UnityEngine_Shader_GetPropertyNameId();
		Register_UnityEngine_Shader_GetPropertyNameId();

		//System.Int32 UnityEngine.Shader::Internal_FindPassTagValue(System.Int32,System.Int32)
		void Register_UnityEngine_Shader_Internal_FindPassTagValue();
		Register_UnityEngine_Shader_Internal_FindPassTagValue();

		//System.Int32 UnityEngine.Shader::PropertyToID(System.String)
		void Register_UnityEngine_Shader_PropertyToID();
		Register_UnityEngine_Shader_PropertyToID();

		//System.Int32 UnityEngine.Shader::TagToID(System.String)
		void Register_UnityEngine_Shader_TagToID();
		Register_UnityEngine_Shader_TagToID();

		//System.Int32 UnityEngine.Shader::get_globalMaximumLOD()
		void Register_UnityEngine_Shader_get_globalMaximumLOD();
		Register_UnityEngine_Shader_get_globalMaximumLOD();

		//System.Int32 UnityEngine.Shader::get_maximumLOD()
		void Register_UnityEngine_Shader_get_maximumLOD();
		Register_UnityEngine_Shader_get_maximumLOD();

		//System.Int32 UnityEngine.Shader::get_passCount()
		void Register_UnityEngine_Shader_get_passCount();
		Register_UnityEngine_Shader_get_passCount();

		//System.Int32 UnityEngine.Shader::get_renderQueue()
		void Register_UnityEngine_Shader_get_renderQueue();
		Register_UnityEngine_Shader_get_renderQueue();

		//System.Single UnityEngine.Shader::GetGlobalFloatImpl(System.Int32)
		void Register_UnityEngine_Shader_GetGlobalFloatImpl();
		Register_UnityEngine_Shader_GetGlobalFloatImpl();

		//System.Single[] UnityEngine.Shader::GetGlobalFloatArrayImpl(System.Int32)
		void Register_UnityEngine_Shader_GetGlobalFloatArrayImpl();
		Register_UnityEngine_Shader_GetGlobalFloatArrayImpl();

		//System.String UnityEngine.Shader::GetPropertyDescription(UnityEngine.Shader,System.Int32)
		void Register_UnityEngine_Shader_GetPropertyDescription();
		Register_UnityEngine_Shader_GetPropertyDescription();

		//System.String UnityEngine.Shader::GetPropertyName(UnityEngine.Shader,System.Int32)
		void Register_UnityEngine_Shader_GetPropertyName();
		Register_UnityEngine_Shader_GetPropertyName();

		//System.String UnityEngine.Shader::GetPropertyTextureDefaultName(UnityEngine.Shader,System.Int32)
		void Register_UnityEngine_Shader_GetPropertyTextureDefaultName();
		Register_UnityEngine_Shader_GetPropertyTextureDefaultName();

		//System.String UnityEngine.Shader::get_globalRenderPipeline()
		void Register_UnityEngine_Shader_get_globalRenderPipeline();
		Register_UnityEngine_Shader_get_globalRenderPipeline();

		//System.String[] UnityEngine.Shader::GetPropertyAttributes(UnityEngine.Shader,System.Int32)
		void Register_UnityEngine_Shader_GetPropertyAttributes();
		Register_UnityEngine_Shader_GetPropertyAttributes();

		//System.Void UnityEngine.Shader::DisableKeyword(System.String)
		void Register_UnityEngine_Shader_DisableKeyword();
		Register_UnityEngine_Shader_DisableKeyword();

		//System.Void UnityEngine.Shader::EnableKeyword(System.String)
		void Register_UnityEngine_Shader_EnableKeyword();
		Register_UnityEngine_Shader_EnableKeyword();

		//System.Void UnityEngine.Shader::ExtractGlobalFloatArrayImpl(System.Int32,System.Single[])
		void Register_UnityEngine_Shader_ExtractGlobalFloatArrayImpl();
		Register_UnityEngine_Shader_ExtractGlobalFloatArrayImpl();

		//System.Void UnityEngine.Shader::ExtractGlobalMatrixArrayImpl(System.Int32,UnityEngine.Matrix4x4[])
		void Register_UnityEngine_Shader_ExtractGlobalMatrixArrayImpl();
		Register_UnityEngine_Shader_ExtractGlobalMatrixArrayImpl();

		//System.Void UnityEngine.Shader::ExtractGlobalVectorArrayImpl(System.Int32,UnityEngine.Vector4[])
		void Register_UnityEngine_Shader_ExtractGlobalVectorArrayImpl();
		Register_UnityEngine_Shader_ExtractGlobalVectorArrayImpl();

		//System.Void UnityEngine.Shader::GetGlobalMatrixImpl_Injected(System.Int32,UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Shader_GetGlobalMatrixImpl_Injected();
		Register_UnityEngine_Shader_GetGlobalMatrixImpl_Injected();

		//System.Void UnityEngine.Shader::GetGlobalVectorImpl_Injected(System.Int32,UnityEngine.Vector4&)
		void Register_UnityEngine_Shader_GetGlobalVectorImpl_Injected();
		Register_UnityEngine_Shader_GetGlobalVectorImpl_Injected();

		//System.Void UnityEngine.Shader::GetPropertyDefaultValue_Injected(UnityEngine.Shader,System.Int32,UnityEngine.Vector4&)
		void Register_UnityEngine_Shader_GetPropertyDefaultValue_Injected();
		Register_UnityEngine_Shader_GetPropertyDefaultValue_Injected();

		//System.Void UnityEngine.Shader::SetGlobalBufferImpl(System.Int32,UnityEngine.ComputeBuffer)
		void Register_UnityEngine_Shader_SetGlobalBufferImpl();
		Register_UnityEngine_Shader_SetGlobalBufferImpl();

		//System.Void UnityEngine.Shader::SetGlobalConstantBufferImpl(System.Int32,UnityEngine.ComputeBuffer,System.Int32,System.Int32)
		void Register_UnityEngine_Shader_SetGlobalConstantBufferImpl();
		Register_UnityEngine_Shader_SetGlobalConstantBufferImpl();

		//System.Void UnityEngine.Shader::SetGlobalFloatArrayImpl(System.Int32,System.Single[],System.Int32)
		void Register_UnityEngine_Shader_SetGlobalFloatArrayImpl();
		Register_UnityEngine_Shader_SetGlobalFloatArrayImpl();

		//System.Void UnityEngine.Shader::SetGlobalFloatImpl(System.Int32,System.Single)
		void Register_UnityEngine_Shader_SetGlobalFloatImpl();
		Register_UnityEngine_Shader_SetGlobalFloatImpl();

		//System.Void UnityEngine.Shader::SetGlobalMatrixArrayImpl(System.Int32,UnityEngine.Matrix4x4[],System.Int32)
		void Register_UnityEngine_Shader_SetGlobalMatrixArrayImpl();
		Register_UnityEngine_Shader_SetGlobalMatrixArrayImpl();

		//System.Void UnityEngine.Shader::SetGlobalMatrixImpl_Injected(System.Int32,UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Shader_SetGlobalMatrixImpl_Injected();
		Register_UnityEngine_Shader_SetGlobalMatrixImpl_Injected();

		//System.Void UnityEngine.Shader::SetGlobalRenderTextureImpl(System.Int32,UnityEngine.RenderTexture,UnityEngine.Rendering.RenderTextureSubElement)
		void Register_UnityEngine_Shader_SetGlobalRenderTextureImpl();
		Register_UnityEngine_Shader_SetGlobalRenderTextureImpl();

		//System.Void UnityEngine.Shader::SetGlobalTextureImpl(System.Int32,UnityEngine.Texture)
		void Register_UnityEngine_Shader_SetGlobalTextureImpl();
		Register_UnityEngine_Shader_SetGlobalTextureImpl();

		//System.Void UnityEngine.Shader::SetGlobalVectorArrayImpl(System.Int32,UnityEngine.Vector4[],System.Int32)
		void Register_UnityEngine_Shader_SetGlobalVectorArrayImpl();
		Register_UnityEngine_Shader_SetGlobalVectorArrayImpl();

		//System.Void UnityEngine.Shader::SetGlobalVectorImpl_Injected(System.Int32,UnityEngine.Vector4&)
		void Register_UnityEngine_Shader_SetGlobalVectorImpl_Injected();
		Register_UnityEngine_Shader_SetGlobalVectorImpl_Injected();

		//System.Void UnityEngine.Shader::WarmupAllShaders()
		void Register_UnityEngine_Shader_WarmupAllShaders();
		Register_UnityEngine_Shader_WarmupAllShaders();

		//System.Void UnityEngine.Shader::set_globalMaximumLOD(System.Int32)
		void Register_UnityEngine_Shader_set_globalMaximumLOD();
		Register_UnityEngine_Shader_set_globalMaximumLOD();

		//System.Void UnityEngine.Shader::set_globalRenderPipeline(System.String)
		void Register_UnityEngine_Shader_set_globalRenderPipeline();
		Register_UnityEngine_Shader_set_globalRenderPipeline();

		//System.Void UnityEngine.Shader::set_maximumLOD(System.Int32)
		void Register_UnityEngine_Shader_set_maximumLOD();
		Register_UnityEngine_Shader_set_maximumLOD();

		//UnityEngine.Matrix4x4[] UnityEngine.Shader::GetGlobalMatrixArrayImpl(System.Int32)
		void Register_UnityEngine_Shader_GetGlobalMatrixArrayImpl();
		Register_UnityEngine_Shader_GetGlobalMatrixArrayImpl();

		//UnityEngine.Rendering.ShaderPropertyFlags UnityEngine.Shader::GetPropertyFlags(UnityEngine.Shader,System.Int32)
		void Register_UnityEngine_Shader_GetPropertyFlags();
		Register_UnityEngine_Shader_GetPropertyFlags();

		//UnityEngine.Rendering.ShaderPropertyType UnityEngine.Shader::GetPropertyType(UnityEngine.Shader,System.Int32)
		void Register_UnityEngine_Shader_GetPropertyType();
		Register_UnityEngine_Shader_GetPropertyType();

		//UnityEngine.Rendering.TextureDimension UnityEngine.Shader::GetPropertyTextureDimension(UnityEngine.Shader,System.Int32)
		void Register_UnityEngine_Shader_GetPropertyTextureDimension();
		Register_UnityEngine_Shader_GetPropertyTextureDimension();

		//UnityEngine.Shader UnityEngine.Shader::GetDependency(System.String)
		void Register_UnityEngine_Shader_GetDependency();
		Register_UnityEngine_Shader_GetDependency();

		//UnityEngine.Texture UnityEngine.Shader::GetGlobalTextureImpl(System.Int32)
		void Register_UnityEngine_Shader_GetGlobalTextureImpl();
		Register_UnityEngine_Shader_GetGlobalTextureImpl();

		//UnityEngine.Vector4[] UnityEngine.Shader::GetGlobalVectorArrayImpl(System.Int32)
		void Register_UnityEngine_Shader_GetGlobalVectorArrayImpl();
		Register_UnityEngine_Shader_GetGlobalVectorArrayImpl();

	//End Registrations for type : UnityEngine.Shader

	//Start Registrations for type : UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform

		//System.Boolean UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::GetAuthenticated()
		void Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_GetAuthenticated();
		Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_GetAuthenticated();

		//System.Boolean UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::GetIsUnderage()
		void Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_GetIsUnderage();
		Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_GetIsUnderage();

		//System.String UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::Internal_LegacyUserID()
		void Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_Internal_LegacyUserID();
		Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_Internal_LegacyUserID();

		//System.String UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::Internal_UserGameID()
		void Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_Internal_UserGameID();
		Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_Internal_UserGameID();

		//System.String UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::Internal_UserID()
		void Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_Internal_UserID();
		Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_Internal_UserID();

		//System.String UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::Internal_UserName()
		void Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_Internal_UserName();
		Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_Internal_UserName();

		//System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::Authenticate()
		void Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_Authenticate();
		Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_Authenticate();

		//System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::InternalLoadAchievementDescriptions(System.Object)
		void Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_InternalLoadAchievementDescriptions();
		Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_InternalLoadAchievementDescriptions();

		//System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::InternalLoadAchievements(System.Object)
		void Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_InternalLoadAchievements();
		Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_InternalLoadAchievements();

		//System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::InternalLoadScores(System.String,System.Object)
		void Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_InternalLoadScores();
		Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_InternalLoadScores();

		//System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::InternalReportProgress(System.String,System.Double,System.Object)
		void Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_InternalReportProgress();
		Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_InternalReportProgress();

		//System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::InternalReportScore(System.Int64,System.String,System.Object)
		void Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_InternalReportScore();
		Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_InternalReportScore();

		//System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::Internal_LoadUsers(System.String[],System.Object)
		void Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_Internal_LoadUsers();
		Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_Internal_LoadUsers();

		//System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::Internal_ShowAchievementsUI()
		void Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_Internal_ShowAchievementsUI();
		Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_Internal_ShowAchievementsUI();

		//System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::Internal_ShowLeaderboardUI()
		void Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_Internal_ShowLeaderboardUI();
		Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_Internal_ShowLeaderboardUI();

		//System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::LoadFriends(System.Object)
		void Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_LoadFriends();
		Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_LoadFriends();

		//System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::ResetAllAchievements()
		void Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_ResetAllAchievements();
		Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_ResetAllAchievements();

		//System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::ShowDefaultAchievementBanner(System.Boolean)
		void Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_ShowDefaultAchievementBanner();
		Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_ShowDefaultAchievementBanner();

		//System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::ShowSpecificLeaderboardUI(System.String,System.Int32)
		void Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_ShowSpecificLeaderboardUI();
		Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_ShowSpecificLeaderboardUI();

		//UnityEngine.Texture2D UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::GetUserImage()
		void Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_GetUserImage();
		Register_UnityEngine_SocialPlatforms_GameCenter_GameCenterPlatform_GetUserImage();

	//End Registrations for type : UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform

	//Start Registrations for type : UnityEngine.SocialPlatforms.GameCenter.GcLeaderboard

		//System.Boolean UnityEngine.SocialPlatforms.GameCenter.GcLeaderboard::GcLeaderboard_Loading(System.IntPtr)
		void Register_UnityEngine_SocialPlatforms_GameCenter_GcLeaderboard_GcLeaderboard_Loading();
		Register_UnityEngine_SocialPlatforms_GameCenter_GcLeaderboard_GcLeaderboard_Loading();

		//System.IntPtr UnityEngine.SocialPlatforms.GameCenter.GcLeaderboard::GcLeaderboard_LoadScores(System.Object,System.String,System.Int32,System.Int32,System.String[],System.Int32,System.Int32,System.Object)
		void Register_UnityEngine_SocialPlatforms_GameCenter_GcLeaderboard_GcLeaderboard_LoadScores();
		Register_UnityEngine_SocialPlatforms_GameCenter_GcLeaderboard_GcLeaderboard_LoadScores();

		//System.Void UnityEngine.SocialPlatforms.GameCenter.GcLeaderboard::GcLeaderboard_Dispose(System.IntPtr)
		void Register_UnityEngine_SocialPlatforms_GameCenter_GcLeaderboard_GcLeaderboard_Dispose();
		Register_UnityEngine_SocialPlatforms_GameCenter_GcLeaderboard_GcLeaderboard_Dispose();

	//End Registrations for type : UnityEngine.SocialPlatforms.GameCenter.GcLeaderboard

	//Start Registrations for type : UnityEngine.SortingLayer

		//System.Int32 UnityEngine.SortingLayer::GetLayerValueFromID(System.Int32)
		void Register_UnityEngine_SortingLayer_GetLayerValueFromID();
		Register_UnityEngine_SortingLayer_GetLayerValueFromID();

	//End Registrations for type : UnityEngine.SortingLayer

	//Start Registrations for type : UnityEngine.SphereCollider

		//System.Single UnityEngine.SphereCollider::get_radius()
		void Register_UnityEngine_SphereCollider_get_radius();
		Register_UnityEngine_SphereCollider_get_radius();

		//System.Void UnityEngine.SphereCollider::set_radius(System.Single)
		void Register_UnityEngine_SphereCollider_set_radius();
		Register_UnityEngine_SphereCollider_set_radius();

	//End Registrations for type : UnityEngine.SphereCollider

	//Start Registrations for type : UnityEngine.Sprite

		//System.Int32 UnityEngine.Sprite::GetPacked()
		void Register_UnityEngine_Sprite_GetPacked();
		Register_UnityEngine_Sprite_GetPacked();

		//System.Int32 UnityEngine.Sprite::GetPackingMode()
		void Register_UnityEngine_Sprite_GetPackingMode();
		Register_UnityEngine_Sprite_GetPackingMode();

		//System.Int32 UnityEngine.Sprite::GetPackingRotation()
		void Register_UnityEngine_Sprite_GetPackingRotation();
		Register_UnityEngine_Sprite_GetPackingRotation();

		//System.Single UnityEngine.Sprite::get_pixelsPerUnit()
		void Register_UnityEngine_Sprite_get_pixelsPerUnit();
		Register_UnityEngine_Sprite_get_pixelsPerUnit();

		//System.UInt16[] UnityEngine.Sprite::get_triangles()
		void Register_UnityEngine_Sprite_get_triangles();
		Register_UnityEngine_Sprite_get_triangles();

		//System.Void UnityEngine.Sprite::GetInnerUVs_Injected(UnityEngine.Vector4&)
		void Register_UnityEngine_Sprite_GetInnerUVs_Injected();
		Register_UnityEngine_Sprite_GetInnerUVs_Injected();

		//System.Void UnityEngine.Sprite::GetOuterUVs_Injected(UnityEngine.Vector4&)
		void Register_UnityEngine_Sprite_GetOuterUVs_Injected();
		Register_UnityEngine_Sprite_GetOuterUVs_Injected();

		//System.Void UnityEngine.Sprite::GetPadding_Injected(UnityEngine.Vector4&)
		void Register_UnityEngine_Sprite_GetPadding_Injected();
		Register_UnityEngine_Sprite_GetPadding_Injected();

		//System.Void UnityEngine.Sprite::GetTextureRectOffset_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Sprite_GetTextureRectOffset_Injected();
		Register_UnityEngine_Sprite_GetTextureRectOffset_Injected();

		//System.Void UnityEngine.Sprite::GetTextureRect_Injected(UnityEngine.Rect&)
		void Register_UnityEngine_Sprite_GetTextureRect_Injected();
		Register_UnityEngine_Sprite_GetTextureRect_Injected();

		//System.Void UnityEngine.Sprite::get_border_Injected(UnityEngine.Vector4&)
		void Register_UnityEngine_Sprite_get_border_Injected();
		Register_UnityEngine_Sprite_get_border_Injected();

		//System.Void UnityEngine.Sprite::get_bounds_Injected(UnityEngine.Bounds&)
		void Register_UnityEngine_Sprite_get_bounds_Injected();
		Register_UnityEngine_Sprite_get_bounds_Injected();

		//System.Void UnityEngine.Sprite::get_pivot_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Sprite_get_pivot_Injected();
		Register_UnityEngine_Sprite_get_pivot_Injected();

		//System.Void UnityEngine.Sprite::get_rect_Injected(UnityEngine.Rect&)
		void Register_UnityEngine_Sprite_get_rect_Injected();
		Register_UnityEngine_Sprite_get_rect_Injected();

		//UnityEngine.Sprite UnityEngine.Sprite::CreateSprite_Injected(UnityEngine.Texture2D,UnityEngine.Rect&,UnityEngine.Vector2&,System.Single,System.UInt32,UnityEngine.SpriteMeshType,UnityEngine.Vector4&,System.Boolean)
		void Register_UnityEngine_Sprite_CreateSprite_Injected();
		Register_UnityEngine_Sprite_CreateSprite_Injected();

		//UnityEngine.Texture2D UnityEngine.Sprite::get_associatedAlphaSplitTexture()
		void Register_UnityEngine_Sprite_get_associatedAlphaSplitTexture();
		Register_UnityEngine_Sprite_get_associatedAlphaSplitTexture();

		//UnityEngine.Texture2D UnityEngine.Sprite::get_texture()
		void Register_UnityEngine_Sprite_get_texture();
		Register_UnityEngine_Sprite_get_texture();

		//UnityEngine.Vector2[] UnityEngine.Sprite::get_uv()
		void Register_UnityEngine_Sprite_get_uv();
		Register_UnityEngine_Sprite_get_uv();

		//UnityEngine.Vector2[] UnityEngine.Sprite::get_vertices()
		void Register_UnityEngine_Sprite_get_vertices();
		Register_UnityEngine_Sprite_get_vertices();

	//End Registrations for type : UnityEngine.Sprite

	//Start Registrations for type : UnityEngine.SpriteRenderer

		//System.Void UnityEngine.SpriteRenderer::get_color_Injected(UnityEngine.Color&)
		void Register_UnityEngine_SpriteRenderer_get_color_Injected();
		Register_UnityEngine_SpriteRenderer_get_color_Injected();

		//System.Void UnityEngine.SpriteRenderer::set_color_Injected(UnityEngine.Color&)
		void Register_UnityEngine_SpriteRenderer_set_color_Injected();
		Register_UnityEngine_SpriteRenderer_set_color_Injected();

		//System.Void UnityEngine.SpriteRenderer::set_sprite(UnityEngine.Sprite)
		void Register_UnityEngine_SpriteRenderer_set_sprite();
		Register_UnityEngine_SpriteRenderer_set_sprite();

		//UnityEngine.Sprite UnityEngine.SpriteRenderer::get_sprite()
		void Register_UnityEngine_SpriteRenderer_get_sprite();
		Register_UnityEngine_SpriteRenderer_get_sprite();

	//End Registrations for type : UnityEngine.SpriteRenderer

	//Start Registrations for type : UnityEngine.SubsystemDescriptorBindings

		//System.String UnityEngine.SubsystemDescriptorBindings::GetId(System.IntPtr)
		void Register_UnityEngine_SubsystemDescriptorBindings_GetId();
		Register_UnityEngine_SubsystemDescriptorBindings_GetId();

	//End Registrations for type : UnityEngine.SubsystemDescriptorBindings

	//Start Registrations for type : UnityEngine.SubsystemManager

		//System.Void UnityEngine.SubsystemManager::StaticConstructScriptingClassMap()
		void Register_UnityEngine_SubsystemManager_StaticConstructScriptingClassMap();
		Register_UnityEngine_SubsystemManager_StaticConstructScriptingClassMap();

	//End Registrations for type : UnityEngine.SubsystemManager

	//Start Registrations for type : UnityEngine.SubsystemsImplementation.SubsystemDescriptorStore

		//System.Void UnityEngine.SubsystemsImplementation.SubsystemDescriptorStore::ReportSingleSubsystemAnalytics(System.String)
		void Register_UnityEngine_SubsystemsImplementation_SubsystemDescriptorStore_ReportSingleSubsystemAnalytics();
		Register_UnityEngine_SubsystemsImplementation_SubsystemDescriptorStore_ReportSingleSubsystemAnalytics();

	//End Registrations for type : UnityEngine.SubsystemsImplementation.SubsystemDescriptorStore

	//Start Registrations for type : UnityEngine.SystemInfo

		//System.Boolean UnityEngine.SystemInfo::IsFormatSupported(UnityEngine.Experimental.Rendering.GraphicsFormat,UnityEngine.Experimental.Rendering.FormatUsage)
		void Register_UnityEngine_SystemInfo_IsFormatSupported();
		Register_UnityEngine_SystemInfo_IsFormatSupported();

		//System.Boolean UnityEngine.SystemInfo::SupportsTextureFormatNative(UnityEngine.TextureFormat)
		void Register_UnityEngine_SystemInfo_SupportsTextureFormatNative();
		Register_UnityEngine_SystemInfo_SupportsTextureFormatNative();

		//System.Int32 UnityEngine.SystemInfo::GetGraphicsMemorySize()
		void Register_UnityEngine_SystemInfo_GetGraphicsMemorySize();
		Register_UnityEngine_SystemInfo_GetGraphicsMemorySize();

		//System.Int32 UnityEngine.SystemInfo::GetGraphicsShaderLevel()
		void Register_UnityEngine_SystemInfo_GetGraphicsShaderLevel();
		Register_UnityEngine_SystemInfo_GetGraphicsShaderLevel();

		//System.Int32 UnityEngine.SystemInfo::GetPhysicalMemoryMB()
		void Register_UnityEngine_SystemInfo_GetPhysicalMemoryMB();
		Register_UnityEngine_SystemInfo_GetPhysicalMemoryMB();

		//System.Int32 UnityEngine.SystemInfo::GetProcessorCount()
		void Register_UnityEngine_SystemInfo_GetProcessorCount();
		Register_UnityEngine_SystemInfo_GetProcessorCount();

		//System.String UnityEngine.SystemInfo::GetDeviceModel()
		void Register_UnityEngine_SystemInfo_GetDeviceModel();
		Register_UnityEngine_SystemInfo_GetDeviceModel();

		//System.String UnityEngine.SystemInfo::GetDeviceName()
		void Register_UnityEngine_SystemInfo_GetDeviceName();
		Register_UnityEngine_SystemInfo_GetDeviceName();

		//System.String UnityEngine.SystemInfo::GetDeviceUniqueIdentifier()
		void Register_UnityEngine_SystemInfo_GetDeviceUniqueIdentifier();
		Register_UnityEngine_SystemInfo_GetDeviceUniqueIdentifier();

		//System.String UnityEngine.SystemInfo::GetGraphicsDeviceName()
		void Register_UnityEngine_SystemInfo_GetGraphicsDeviceName();
		Register_UnityEngine_SystemInfo_GetGraphicsDeviceName();

		//System.String UnityEngine.SystemInfo::GetGraphicsDeviceVersion()
		void Register_UnityEngine_SystemInfo_GetGraphicsDeviceVersion();
		Register_UnityEngine_SystemInfo_GetGraphicsDeviceVersion();

		//System.String UnityEngine.SystemInfo::GetOperatingSystem()
		void Register_UnityEngine_SystemInfo_GetOperatingSystem();
		Register_UnityEngine_SystemInfo_GetOperatingSystem();

		//UnityEngine.DeviceType UnityEngine.SystemInfo::GetDeviceType()
		void Register_UnityEngine_SystemInfo_GetDeviceType();
		Register_UnityEngine_SystemInfo_GetDeviceType();

		//UnityEngine.Experimental.Rendering.GraphicsFormat UnityEngine.SystemInfo::GetCompatibleFormat(UnityEngine.Experimental.Rendering.GraphicsFormat,UnityEngine.Experimental.Rendering.FormatUsage)
		void Register_UnityEngine_SystemInfo_GetCompatibleFormat();
		Register_UnityEngine_SystemInfo_GetCompatibleFormat();

		//UnityEngine.Experimental.Rendering.GraphicsFormat UnityEngine.SystemInfo::GetGraphicsFormat(UnityEngine.Experimental.Rendering.DefaultFormat)
		void Register_UnityEngine_SystemInfo_GetGraphicsFormat();
		Register_UnityEngine_SystemInfo_GetGraphicsFormat();

		//UnityEngine.OperatingSystemFamily UnityEngine.SystemInfo::GetOperatingSystemFamily()
		void Register_UnityEngine_SystemInfo_GetOperatingSystemFamily();
		Register_UnityEngine_SystemInfo_GetOperatingSystemFamily();

		//UnityEngine.Rendering.CopyTextureSupport UnityEngine.SystemInfo::GetCopyTextureSupport()
		void Register_UnityEngine_SystemInfo_GetCopyTextureSupport();
		Register_UnityEngine_SystemInfo_GetCopyTextureSupport();

	//End Registrations for type : UnityEngine.SystemInfo

	//Start Registrations for type : UnityEngine.Terrain

		//System.Boolean UnityEngine.Terrain::get_allowAutoConnect()
		void Register_UnityEngine_Terrain_get_allowAutoConnect();
		Register_UnityEngine_Terrain_get_allowAutoConnect();

		//System.Int32 UnityEngine.Terrain::get_groupingID()
		void Register_UnityEngine_Terrain_get_groupingID();
		Register_UnityEngine_Terrain_get_groupingID();

		//System.Void UnityEngine.Terrain::SetNeighbors(UnityEngine.Terrain,UnityEngine.Terrain,UnityEngine.Terrain,UnityEngine.Terrain)
		void Register_UnityEngine_Terrain_SetNeighbors();
		Register_UnityEngine_Terrain_SetNeighbors();

		//UnityEngine.TerrainData UnityEngine.Terrain::get_terrainData()
		void Register_UnityEngine_Terrain_get_terrainData();
		Register_UnityEngine_Terrain_get_terrainData();

		//UnityEngine.Terrain[] UnityEngine.Terrain::get_activeTerrains()
		void Register_UnityEngine_Terrain_get_activeTerrains();
		Register_UnityEngine_Terrain_get_activeTerrains();

	//End Registrations for type : UnityEngine.Terrain

	//Start Registrations for type : UnityEngine.TerrainData

		//System.Int32 UnityEngine.TerrainData::GetBoundaryValue(UnityEngine.TerrainData/BoundaryValueType)
		void Register_UnityEngine_TerrainData_GetBoundaryValue();
		Register_UnityEngine_TerrainData_GetBoundaryValue();

		//System.Single UnityEngine.TerrainData::GetAlphamapResolutionInternal()
		void Register_UnityEngine_TerrainData_GetAlphamapResolutionInternal();
		Register_UnityEngine_TerrainData_GetAlphamapResolutionInternal();

		//System.Void UnityEngine.TerrainData::Internal_Create(UnityEngine.TerrainData)
		void Register_UnityEngine_TerrainData_Internal_Create();
		Register_UnityEngine_TerrainData_Internal_Create();

		//System.Void UnityEngine.TerrainData::get_size_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_TerrainData_get_size_Injected();
		Register_UnityEngine_TerrainData_get_size_Injected();

		//UnityEngine.Terrain[] UnityEngine.TerrainData::get_users()
		void Register_UnityEngine_TerrainData_get_users();
		Register_UnityEngine_TerrainData_get_users();

	//End Registrations for type : UnityEngine.TerrainData

	//Start Registrations for type : UnityEngine.TextAsset

		//System.Byte[] UnityEngine.TextAsset::get_bytes()
		void Register_UnityEngine_TextAsset_get_bytes();
		Register_UnityEngine_TextAsset_get_bytes();

	//End Registrations for type : UnityEngine.TextAsset

	//Start Registrations for type : UnityEngine.TextGenerator

		//System.Boolean UnityEngine.TextGenerator::Populate_Internal_Injected(System.String,UnityEngine.Font,UnityEngine.Color&,System.Int32,System.Single,System.Single,UnityEngine.FontStyle,System.Boolean,System.Boolean,System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean,UnityEngine.TextAnchor,System.Single,System.Single,System.Single,System.Single,System.Boolean,System.Boolean,System.UInt32&)
		void Register_UnityEngine_TextGenerator_Populate_Internal_Injected();
		Register_UnityEngine_TextGenerator_Populate_Internal_Injected();

		//System.Int32 UnityEngine.TextGenerator::get_characterCount()
		void Register_UnityEngine_TextGenerator_get_characterCount();
		Register_UnityEngine_TextGenerator_get_characterCount();

		//System.Int32 UnityEngine.TextGenerator::get_lineCount()
		void Register_UnityEngine_TextGenerator_get_lineCount();
		Register_UnityEngine_TextGenerator_get_lineCount();

		//System.IntPtr UnityEngine.TextGenerator::Internal_Create()
		void Register_UnityEngine_TextGenerator_Internal_Create();
		Register_UnityEngine_TextGenerator_Internal_Create();

		//System.Void UnityEngine.TextGenerator::GetCharactersInternal(System.Object)
		void Register_UnityEngine_TextGenerator_GetCharactersInternal();
		Register_UnityEngine_TextGenerator_GetCharactersInternal();

		//System.Void UnityEngine.TextGenerator::GetLinesInternal(System.Object)
		void Register_UnityEngine_TextGenerator_GetLinesInternal();
		Register_UnityEngine_TextGenerator_GetLinesInternal();

		//System.Void UnityEngine.TextGenerator::GetVerticesInternal(System.Object)
		void Register_UnityEngine_TextGenerator_GetVerticesInternal();
		Register_UnityEngine_TextGenerator_GetVerticesInternal();

		//System.Void UnityEngine.TextGenerator::Internal_Destroy(System.IntPtr)
		void Register_UnityEngine_TextGenerator_Internal_Destroy();
		Register_UnityEngine_TextGenerator_Internal_Destroy();

		//System.Void UnityEngine.TextGenerator::get_rectExtents_Injected(UnityEngine.Rect&)
		void Register_UnityEngine_TextGenerator_get_rectExtents_Injected();
		Register_UnityEngine_TextGenerator_get_rectExtents_Injected();

	//End Registrations for type : UnityEngine.TextGenerator

	//Start Registrations for type : UnityEngine.TextMesh

		//System.Void UnityEngine.TextMesh::set_font(UnityEngine.Font)
		void Register_UnityEngine_TextMesh_set_font();
		Register_UnityEngine_TextMesh_set_font();

		//System.Void UnityEngine.TextMesh::set_text(System.String)
		void Register_UnityEngine_TextMesh_set_text();
		Register_UnityEngine_TextMesh_set_text();

	//End Registrations for type : UnityEngine.TextMesh

	//Start Registrations for type : UnityEngine.Texture

		//System.Boolean UnityEngine.Texture::get_allowThreadedTextureCreation()
		void Register_UnityEngine_Texture_get_allowThreadedTextureCreation();
		Register_UnityEngine_Texture_get_allowThreadedTextureCreation();

		//System.Boolean UnityEngine.Texture::get_isReadable()
		void Register_UnityEngine_Texture_get_isReadable();
		Register_UnityEngine_Texture_get_isReadable();

		//System.Boolean UnityEngine.Texture::get_streamingTextureDiscardUnusedMips()
		void Register_UnityEngine_Texture_get_streamingTextureDiscardUnusedMips();
		Register_UnityEngine_Texture_get_streamingTextureDiscardUnusedMips();

		//System.Boolean UnityEngine.Texture::get_streamingTextureForceLoadAll()
		void Register_UnityEngine_Texture_get_streamingTextureForceLoadAll();
		Register_UnityEngine_Texture_get_streamingTextureForceLoadAll();

		//System.Int32 UnityEngine.Texture::GetDataHeight()
		void Register_UnityEngine_Texture_GetDataHeight();
		Register_UnityEngine_Texture_GetDataHeight();

		//System.Int32 UnityEngine.Texture::GetDataWidth()
		void Register_UnityEngine_Texture_GetDataWidth();
		Register_UnityEngine_Texture_GetDataWidth();

		//System.Int32 UnityEngine.Texture::Internal_GetActiveTextureColorSpace()
		void Register_UnityEngine_Texture_Internal_GetActiveTextureColorSpace();
		Register_UnityEngine_Texture_Internal_GetActiveTextureColorSpace();

		//System.Int32 UnityEngine.Texture::get_anisoLevel()
		void Register_UnityEngine_Texture_get_anisoLevel();
		Register_UnityEngine_Texture_get_anisoLevel();

		//System.Int32 UnityEngine.Texture::get_masterTextureLimit()
		void Register_UnityEngine_Texture_get_masterTextureLimit();
		Register_UnityEngine_Texture_get_masterTextureLimit();

		//System.Int32 UnityEngine.Texture::get_mipmapCount()
		void Register_UnityEngine_Texture_get_mipmapCount();
		Register_UnityEngine_Texture_get_mipmapCount();

		//System.IntPtr UnityEngine.Texture::GetNativeTexturePtr()
		void Register_UnityEngine_Texture_GetNativeTexturePtr();
		Register_UnityEngine_Texture_GetNativeTexturePtr();

		//System.Single UnityEngine.Texture::get_mipMapBias()
		void Register_UnityEngine_Texture_get_mipMapBias();
		Register_UnityEngine_Texture_get_mipMapBias();

		//System.UInt32 UnityEngine.Texture::get_updateCount()
		void Register_UnityEngine_Texture_get_updateCount();
		Register_UnityEngine_Texture_get_updateCount();

		//System.UInt64 UnityEngine.Texture::get_currentTextureMemory()
		void Register_UnityEngine_Texture_get_currentTextureMemory();
		Register_UnityEngine_Texture_get_currentTextureMemory();

		//System.UInt64 UnityEngine.Texture::get_desiredTextureMemory()
		void Register_UnityEngine_Texture_get_desiredTextureMemory();
		Register_UnityEngine_Texture_get_desiredTextureMemory();

		//System.UInt64 UnityEngine.Texture::get_nonStreamingTextureCount()
		void Register_UnityEngine_Texture_get_nonStreamingTextureCount();
		Register_UnityEngine_Texture_get_nonStreamingTextureCount();

		//System.UInt64 UnityEngine.Texture::get_nonStreamingTextureMemory()
		void Register_UnityEngine_Texture_get_nonStreamingTextureMemory();
		Register_UnityEngine_Texture_get_nonStreamingTextureMemory();

		//System.UInt64 UnityEngine.Texture::get_streamingMipmapUploadCount()
		void Register_UnityEngine_Texture_get_streamingMipmapUploadCount();
		Register_UnityEngine_Texture_get_streamingMipmapUploadCount();

		//System.UInt64 UnityEngine.Texture::get_streamingRendererCount()
		void Register_UnityEngine_Texture_get_streamingRendererCount();
		Register_UnityEngine_Texture_get_streamingRendererCount();

		//System.UInt64 UnityEngine.Texture::get_streamingTextureCount()
		void Register_UnityEngine_Texture_get_streamingTextureCount();
		Register_UnityEngine_Texture_get_streamingTextureCount();

		//System.UInt64 UnityEngine.Texture::get_streamingTextureLoadingCount()
		void Register_UnityEngine_Texture_get_streamingTextureLoadingCount();
		Register_UnityEngine_Texture_get_streamingTextureLoadingCount();

		//System.UInt64 UnityEngine.Texture::get_streamingTexturePendingLoadCount()
		void Register_UnityEngine_Texture_get_streamingTexturePendingLoadCount();
		Register_UnityEngine_Texture_get_streamingTexturePendingLoadCount();

		//System.UInt64 UnityEngine.Texture::get_targetTextureMemory()
		void Register_UnityEngine_Texture_get_targetTextureMemory();
		Register_UnityEngine_Texture_get_targetTextureMemory();

		//System.UInt64 UnityEngine.Texture::get_totalTextureMemory()
		void Register_UnityEngine_Texture_get_totalTextureMemory();
		Register_UnityEngine_Texture_get_totalTextureMemory();

		//System.Void UnityEngine.Texture::IncrementUpdateCount()
		void Register_UnityEngine_Texture_IncrementUpdateCount();
		Register_UnityEngine_Texture_IncrementUpdateCount();

		//System.Void UnityEngine.Texture::SetGlobalAnisotropicFilteringLimits(System.Int32,System.Int32)
		void Register_UnityEngine_Texture_SetGlobalAnisotropicFilteringLimits();
		Register_UnityEngine_Texture_SetGlobalAnisotropicFilteringLimits();

		//System.Void UnityEngine.Texture::SetStreamingTextureMaterialDebugProperties()
		void Register_UnityEngine_Texture_SetStreamingTextureMaterialDebugProperties();
		Register_UnityEngine_Texture_SetStreamingTextureMaterialDebugProperties();

		//System.Void UnityEngine.Texture::get_texelSize_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Texture_get_texelSize_Injected();
		Register_UnityEngine_Texture_get_texelSize_Injected();

		//System.Void UnityEngine.Texture::set_allowThreadedTextureCreation(System.Boolean)
		void Register_UnityEngine_Texture_set_allowThreadedTextureCreation();
		Register_UnityEngine_Texture_set_allowThreadedTextureCreation();

		//System.Void UnityEngine.Texture::set_anisoLevel(System.Int32)
		void Register_UnityEngine_Texture_set_anisoLevel();
		Register_UnityEngine_Texture_set_anisoLevel();

		//System.Void UnityEngine.Texture::set_anisotropicFiltering(UnityEngine.AnisotropicFiltering)
		void Register_UnityEngine_Texture_set_anisotropicFiltering();
		Register_UnityEngine_Texture_set_anisotropicFiltering();

		//System.Void UnityEngine.Texture::set_filterMode(UnityEngine.FilterMode)
		void Register_UnityEngine_Texture_set_filterMode();
		Register_UnityEngine_Texture_set_filterMode();

		//System.Void UnityEngine.Texture::set_masterTextureLimit(System.Int32)
		void Register_UnityEngine_Texture_set_masterTextureLimit();
		Register_UnityEngine_Texture_set_masterTextureLimit();

		//System.Void UnityEngine.Texture::set_mipMapBias(System.Single)
		void Register_UnityEngine_Texture_set_mipMapBias();
		Register_UnityEngine_Texture_set_mipMapBias();

		//System.Void UnityEngine.Texture::set_streamingTextureDiscardUnusedMips(System.Boolean)
		void Register_UnityEngine_Texture_set_streamingTextureDiscardUnusedMips();
		Register_UnityEngine_Texture_set_streamingTextureDiscardUnusedMips();

		//System.Void UnityEngine.Texture::set_streamingTextureForceLoadAll(System.Boolean)
		void Register_UnityEngine_Texture_set_streamingTextureForceLoadAll();
		Register_UnityEngine_Texture_set_streamingTextureForceLoadAll();

		//System.Void UnityEngine.Texture::set_wrapMode(UnityEngine.TextureWrapMode)
		void Register_UnityEngine_Texture_set_wrapMode();
		Register_UnityEngine_Texture_set_wrapMode();

		//System.Void UnityEngine.Texture::set_wrapModeU(UnityEngine.TextureWrapMode)
		void Register_UnityEngine_Texture_set_wrapModeU();
		Register_UnityEngine_Texture_set_wrapModeU();

		//System.Void UnityEngine.Texture::set_wrapModeV(UnityEngine.TextureWrapMode)
		void Register_UnityEngine_Texture_set_wrapModeV();
		Register_UnityEngine_Texture_set_wrapModeV();

		//System.Void UnityEngine.Texture::set_wrapModeW(UnityEngine.TextureWrapMode)
		void Register_UnityEngine_Texture_set_wrapModeW();
		Register_UnityEngine_Texture_set_wrapModeW();

		//UnityEngine.AnisotropicFiltering UnityEngine.Texture::get_anisotropicFiltering()
		void Register_UnityEngine_Texture_get_anisotropicFiltering();
		Register_UnityEngine_Texture_get_anisotropicFiltering();

		//UnityEngine.FilterMode UnityEngine.Texture::get_filterMode()
		void Register_UnityEngine_Texture_get_filterMode();
		Register_UnityEngine_Texture_get_filterMode();

		//UnityEngine.Rendering.TextureDimension UnityEngine.Texture::GetDimension()
		void Register_UnityEngine_Texture_GetDimension();
		Register_UnityEngine_Texture_GetDimension();

		//UnityEngine.TextureWrapMode UnityEngine.Texture::get_wrapMode()
		void Register_UnityEngine_Texture_get_wrapMode();
		Register_UnityEngine_Texture_get_wrapMode();

		//UnityEngine.TextureWrapMode UnityEngine.Texture::get_wrapModeU()
		void Register_UnityEngine_Texture_get_wrapModeU();
		Register_UnityEngine_Texture_get_wrapModeU();

		//UnityEngine.TextureWrapMode UnityEngine.Texture::get_wrapModeV()
		void Register_UnityEngine_Texture_get_wrapModeV();
		Register_UnityEngine_Texture_get_wrapModeV();

		//UnityEngine.TextureWrapMode UnityEngine.Texture::get_wrapModeW()
		void Register_UnityEngine_Texture_get_wrapModeW();
		Register_UnityEngine_Texture_get_wrapModeW();

	//End Registrations for type : UnityEngine.Texture

	//Start Registrations for type : UnityEngine.Texture2D

		//System.Boolean UnityEngine.Texture2D::Internal_CreateImpl(UnityEngine.Texture2D,System.Int32,System.Int32,System.Int32,UnityEngine.Experimental.Rendering.GraphicsFormat,UnityEngine.Experimental.Rendering.TextureCreationFlags,System.IntPtr)
		void Register_UnityEngine_Texture2D_Internal_CreateImpl();
		Register_UnityEngine_Texture2D_Internal_CreateImpl();

		//System.Boolean UnityEngine.Texture2D::IsRequestedMipmapLevelLoaded()
		void Register_UnityEngine_Texture2D_IsRequestedMipmapLevelLoaded();
		Register_UnityEngine_Texture2D_IsRequestedMipmapLevelLoaded();

		//System.Boolean UnityEngine.Texture2D::LoadRawTextureDataImpl(System.IntPtr,System.UInt64)
		void Register_UnityEngine_Texture2D_LoadRawTextureDataImpl();
		Register_UnityEngine_Texture2D_LoadRawTextureDataImpl();

		//System.Boolean UnityEngine.Texture2D::LoadRawTextureDataImplArray(System.Byte[])
		void Register_UnityEngine_Texture2D_LoadRawTextureDataImplArray();
		Register_UnityEngine_Texture2D_LoadRawTextureDataImplArray();

		//System.Boolean UnityEngine.Texture2D::ResizeImpl(System.Int32,System.Int32)
		void Register_UnityEngine_Texture2D_ResizeImpl();
		Register_UnityEngine_Texture2D_ResizeImpl();

		//System.Boolean UnityEngine.Texture2D::ResizeWithFormatImpl(System.Int32,System.Int32,UnityEngine.Experimental.Rendering.GraphicsFormat,System.Boolean)
		void Register_UnityEngine_Texture2D_ResizeWithFormatImpl();
		Register_UnityEngine_Texture2D_ResizeWithFormatImpl();

		//System.Boolean UnityEngine.Texture2D::get_isReadable()
		void Register_UnityEngine_Texture2D_get_isReadable();
		Register_UnityEngine_Texture2D_get_isReadable();

		//System.Boolean UnityEngine.Texture2D::get_streamingMipmaps()
		void Register_UnityEngine_Texture2D_get_streamingMipmaps();
		Register_UnityEngine_Texture2D_get_streamingMipmaps();

		//System.Byte[] UnityEngine.Texture2D::GetRawTextureData()
		void Register_UnityEngine_Texture2D_GetRawTextureData();
		Register_UnityEngine_Texture2D_GetRawTextureData();

		//System.Int32 UnityEngine.Texture2D::get_calculatedMipmapLevel()
		void Register_UnityEngine_Texture2D_get_calculatedMipmapLevel();
		Register_UnityEngine_Texture2D_get_calculatedMipmapLevel();

		//System.Int32 UnityEngine.Texture2D::get_desiredMipmapLevel()
		void Register_UnityEngine_Texture2D_get_desiredMipmapLevel();
		Register_UnityEngine_Texture2D_get_desiredMipmapLevel();

		//System.Int32 UnityEngine.Texture2D::get_loadedMipmapLevel()
		void Register_UnityEngine_Texture2D_get_loadedMipmapLevel();
		Register_UnityEngine_Texture2D_get_loadedMipmapLevel();

		//System.Int32 UnityEngine.Texture2D::get_loadingMipmapLevel()
		void Register_UnityEngine_Texture2D_get_loadingMipmapLevel();
		Register_UnityEngine_Texture2D_get_loadingMipmapLevel();

		//System.Int32 UnityEngine.Texture2D::get_minimumMipmapLevel()
		void Register_UnityEngine_Texture2D_get_minimumMipmapLevel();
		Register_UnityEngine_Texture2D_get_minimumMipmapLevel();

		//System.Int32 UnityEngine.Texture2D::get_requestedMipmapLevel()
		void Register_UnityEngine_Texture2D_get_requestedMipmapLevel();
		Register_UnityEngine_Texture2D_get_requestedMipmapLevel();

		//System.Int32 UnityEngine.Texture2D::get_streamingMipmapsPriority()
		void Register_UnityEngine_Texture2D_get_streamingMipmapsPriority();
		Register_UnityEngine_Texture2D_get_streamingMipmapsPriority();

		//System.Void UnityEngine.Texture2D::ApplyImpl(System.Boolean,System.Boolean)
		void Register_UnityEngine_Texture2D_ApplyImpl();
		Register_UnityEngine_Texture2D_ApplyImpl();

		//System.Void UnityEngine.Texture2D::ClearMinimumMipmapLevel()
		void Register_UnityEngine_Texture2D_ClearMinimumMipmapLevel();
		Register_UnityEngine_Texture2D_ClearMinimumMipmapLevel();

		//System.Void UnityEngine.Texture2D::ClearRequestedMipmapLevel()
		void Register_UnityEngine_Texture2D_ClearRequestedMipmapLevel();
		Register_UnityEngine_Texture2D_ClearRequestedMipmapLevel();

		//System.Void UnityEngine.Texture2D::Compress(System.Boolean)
		void Register_UnityEngine_Texture2D_Compress();
		Register_UnityEngine_Texture2D_Compress();

		//System.Void UnityEngine.Texture2D::GenerateAtlasImpl(UnityEngine.Vector2[],System.Int32,System.Int32,UnityEngine.Rect[])
		void Register_UnityEngine_Texture2D_GenerateAtlasImpl();
		Register_UnityEngine_Texture2D_GenerateAtlasImpl();

		//System.Void UnityEngine.Texture2D::GetPixelBilinearImpl_Injected(System.Int32,System.Int32,System.Single,System.Single,UnityEngine.Color&)
		void Register_UnityEngine_Texture2D_GetPixelBilinearImpl_Injected();
		Register_UnityEngine_Texture2D_GetPixelBilinearImpl_Injected();

		//System.Void UnityEngine.Texture2D::GetPixelImpl_Injected(System.Int32,System.Int32,System.Int32,System.Int32,UnityEngine.Color&)
		void Register_UnityEngine_Texture2D_GetPixelImpl_Injected();
		Register_UnityEngine_Texture2D_GetPixelImpl_Injected();

		//System.Void UnityEngine.Texture2D::ReadPixelsImpl_Injected(UnityEngine.Rect&,System.Int32,System.Int32,System.Boolean)
		void Register_UnityEngine_Texture2D_ReadPixelsImpl_Injected();
		Register_UnityEngine_Texture2D_ReadPixelsImpl_Injected();

		//System.Void UnityEngine.Texture2D::SetAllPixels32(UnityEngine.Color32[],System.Int32)
		void Register_UnityEngine_Texture2D_SetAllPixels32();
		Register_UnityEngine_Texture2D_SetAllPixels32();

		//System.Void UnityEngine.Texture2D::SetBlockOfPixels32(System.Int32,System.Int32,System.Int32,System.Int32,UnityEngine.Color32[],System.Int32)
		void Register_UnityEngine_Texture2D_SetBlockOfPixels32();
		Register_UnityEngine_Texture2D_SetBlockOfPixels32();

		//System.Void UnityEngine.Texture2D::SetPixelImpl_Injected(System.Int32,System.Int32,System.Int32,System.Int32,UnityEngine.Color&)
		void Register_UnityEngine_Texture2D_SetPixelImpl_Injected();
		Register_UnityEngine_Texture2D_SetPixelImpl_Injected();

		//System.Void UnityEngine.Texture2D::SetPixelsImpl(System.Int32,System.Int32,System.Int32,System.Int32,UnityEngine.Color[],System.Int32,System.Int32)
		void Register_UnityEngine_Texture2D_SetPixelsImpl();
		Register_UnityEngine_Texture2D_SetPixelsImpl();

		//System.Void UnityEngine.Texture2D::UpdateExternalTexture(System.IntPtr)
		void Register_UnityEngine_Texture2D_UpdateExternalTexture();
		Register_UnityEngine_Texture2D_UpdateExternalTexture();

		//System.Void UnityEngine.Texture2D::set_minimumMipmapLevel(System.Int32)
		void Register_UnityEngine_Texture2D_set_minimumMipmapLevel();
		Register_UnityEngine_Texture2D_set_minimumMipmapLevel();

		//System.Void UnityEngine.Texture2D::set_requestedMipmapLevel(System.Int32)
		void Register_UnityEngine_Texture2D_set_requestedMipmapLevel();
		Register_UnityEngine_Texture2D_set_requestedMipmapLevel();

		//UnityEngine.Color32[] UnityEngine.Texture2D::GetPixels32(System.Int32)
		void Register_UnityEngine_Texture2D_GetPixels32();
		Register_UnityEngine_Texture2D_GetPixels32();

		//UnityEngine.Color[] UnityEngine.Texture2D::GetPixels(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)
		void Register_UnityEngine_Texture2D_GetPixels();
		Register_UnityEngine_Texture2D_GetPixels();

		//UnityEngine.Rect[] UnityEngine.Texture2D::PackTextures(UnityEngine.Texture2D[],System.Int32,System.Int32,System.Boolean)
		void Register_UnityEngine_Texture2D_PackTextures();
		Register_UnityEngine_Texture2D_PackTextures();

		//UnityEngine.Texture2D UnityEngine.Texture2D::get_blackTexture()
		void Register_UnityEngine_Texture2D_get_blackTexture();
		Register_UnityEngine_Texture2D_get_blackTexture();

		//UnityEngine.Texture2D UnityEngine.Texture2D::get_grayTexture()
		void Register_UnityEngine_Texture2D_get_grayTexture();
		Register_UnityEngine_Texture2D_get_grayTexture();

		//UnityEngine.Texture2D UnityEngine.Texture2D::get_linearGrayTexture()
		void Register_UnityEngine_Texture2D_get_linearGrayTexture();
		Register_UnityEngine_Texture2D_get_linearGrayTexture();

		//UnityEngine.Texture2D UnityEngine.Texture2D::get_normalTexture()
		void Register_UnityEngine_Texture2D_get_normalTexture();
		Register_UnityEngine_Texture2D_get_normalTexture();

		//UnityEngine.Texture2D UnityEngine.Texture2D::get_redTexture()
		void Register_UnityEngine_Texture2D_get_redTexture();
		Register_UnityEngine_Texture2D_get_redTexture();

		//UnityEngine.Texture2D UnityEngine.Texture2D::get_whiteTexture()
		void Register_UnityEngine_Texture2D_get_whiteTexture();
		Register_UnityEngine_Texture2D_get_whiteTexture();

		//UnityEngine.TextureFormat UnityEngine.Texture2D::get_format()
		void Register_UnityEngine_Texture2D_get_format();
		Register_UnityEngine_Texture2D_get_format();

	//End Registrations for type : UnityEngine.Texture2D

	//Start Registrations for type : UnityEngine.Texture2DArray

		//System.Boolean UnityEngine.Texture2DArray::Internal_CreateImpl(UnityEngine.Texture2DArray,System.Int32,System.Int32,System.Int32,System.Int32,UnityEngine.Experimental.Rendering.GraphicsFormat,UnityEngine.Experimental.Rendering.TextureCreationFlags)
		void Register_UnityEngine_Texture2DArray_Internal_CreateImpl();
		Register_UnityEngine_Texture2DArray_Internal_CreateImpl();

		//System.Boolean UnityEngine.Texture2DArray::get_isReadable()
		void Register_UnityEngine_Texture2DArray_get_isReadable();
		Register_UnityEngine_Texture2DArray_get_isReadable();

	//End Registrations for type : UnityEngine.Texture2DArray

	//Start Registrations for type : UnityEngine.Texture3D

		//System.Boolean UnityEngine.Texture3D::Internal_CreateImpl(UnityEngine.Texture3D,System.Int32,System.Int32,System.Int32,System.Int32,UnityEngine.Experimental.Rendering.GraphicsFormat,UnityEngine.Experimental.Rendering.TextureCreationFlags,System.IntPtr)
		void Register_UnityEngine_Texture3D_Internal_CreateImpl();
		Register_UnityEngine_Texture3D_Internal_CreateImpl();

		//System.Boolean UnityEngine.Texture3D::get_isReadable()
		void Register_UnityEngine_Texture3D_get_isReadable();
		Register_UnityEngine_Texture3D_get_isReadable();

	//End Registrations for type : UnityEngine.Texture3D

	//Start Registrations for type : UnityEngine.Tilemaps.Tilemap

		//System.Void UnityEngine.Tilemaps.Tilemap::RefreshTile_Injected(UnityEngine.Vector3Int&)
		void Register_UnityEngine_Tilemaps_Tilemap_RefreshTile_Injected();
		Register_UnityEngine_Tilemaps_Tilemap_RefreshTile_Injected();

	//End Registrations for type : UnityEngine.Tilemaps.Tilemap

	//Start Registrations for type : UnityEngine.Tilemaps.TilemapRenderer

		//System.Void UnityEngine.Tilemaps.TilemapRenderer::OnSpriteAtlasRegistered(UnityEngine.U2D.SpriteAtlas)
		void Register_UnityEngine_Tilemaps_TilemapRenderer_OnSpriteAtlasRegistered();
		Register_UnityEngine_Tilemaps_TilemapRenderer_OnSpriteAtlasRegistered();

	//End Registrations for type : UnityEngine.Tilemaps.TilemapRenderer

	//Start Registrations for type : UnityEngine.Time

		//System.Boolean UnityEngine.Time::get_inFixedTimeStep()
		void Register_UnityEngine_Time_get_inFixedTimeStep();
		Register_UnityEngine_Time_get_inFixedTimeStep();

		//System.Int32 UnityEngine.Time::get_frameCount()
		void Register_UnityEngine_Time_get_frameCount();
		Register_UnityEngine_Time_get_frameCount();

		//System.Int32 UnityEngine.Time::get_renderedFrameCount()
		void Register_UnityEngine_Time_get_renderedFrameCount();
		Register_UnityEngine_Time_get_renderedFrameCount();

		//System.Single UnityEngine.Time::get_captureDeltaTime()
		void Register_UnityEngine_Time_get_captureDeltaTime();
		Register_UnityEngine_Time_get_captureDeltaTime();

		//System.Single UnityEngine.Time::get_deltaTime()
		void Register_UnityEngine_Time_get_deltaTime();
		Register_UnityEngine_Time_get_deltaTime();

		//System.Single UnityEngine.Time::get_fixedDeltaTime()
		void Register_UnityEngine_Time_get_fixedDeltaTime();
		Register_UnityEngine_Time_get_fixedDeltaTime();

		//System.Single UnityEngine.Time::get_fixedTime()
		void Register_UnityEngine_Time_get_fixedTime();
		Register_UnityEngine_Time_get_fixedTime();

		//System.Single UnityEngine.Time::get_fixedUnscaledDeltaTime()
		void Register_UnityEngine_Time_get_fixedUnscaledDeltaTime();
		Register_UnityEngine_Time_get_fixedUnscaledDeltaTime();

		//System.Single UnityEngine.Time::get_fixedUnscaledTime()
		void Register_UnityEngine_Time_get_fixedUnscaledTime();
		Register_UnityEngine_Time_get_fixedUnscaledTime();

		//System.Single UnityEngine.Time::get_maximumDeltaTime()
		void Register_UnityEngine_Time_get_maximumDeltaTime();
		Register_UnityEngine_Time_get_maximumDeltaTime();

		//System.Single UnityEngine.Time::get_maximumParticleDeltaTime()
		void Register_UnityEngine_Time_get_maximumParticleDeltaTime();
		Register_UnityEngine_Time_get_maximumParticleDeltaTime();

		//System.Single UnityEngine.Time::get_realtimeSinceStartup()
		void Register_UnityEngine_Time_get_realtimeSinceStartup();
		Register_UnityEngine_Time_get_realtimeSinceStartup();

		//System.Single UnityEngine.Time::get_smoothDeltaTime()
		void Register_UnityEngine_Time_get_smoothDeltaTime();
		Register_UnityEngine_Time_get_smoothDeltaTime();

		//System.Single UnityEngine.Time::get_time()
		void Register_UnityEngine_Time_get_time();
		Register_UnityEngine_Time_get_time();

		//System.Single UnityEngine.Time::get_timeScale()
		void Register_UnityEngine_Time_get_timeScale();
		Register_UnityEngine_Time_get_timeScale();

		//System.Single UnityEngine.Time::get_timeSinceLevelLoad()
		void Register_UnityEngine_Time_get_timeSinceLevelLoad();
		Register_UnityEngine_Time_get_timeSinceLevelLoad();

		//System.Single UnityEngine.Time::get_unscaledDeltaTime()
		void Register_UnityEngine_Time_get_unscaledDeltaTime();
		Register_UnityEngine_Time_get_unscaledDeltaTime();

		//System.Single UnityEngine.Time::get_unscaledTime()
		void Register_UnityEngine_Time_get_unscaledTime();
		Register_UnityEngine_Time_get_unscaledTime();

		//System.Void UnityEngine.Time::set_captureDeltaTime(System.Single)
		void Register_UnityEngine_Time_set_captureDeltaTime();
		Register_UnityEngine_Time_set_captureDeltaTime();

		//System.Void UnityEngine.Time::set_fixedDeltaTime(System.Single)
		void Register_UnityEngine_Time_set_fixedDeltaTime();
		Register_UnityEngine_Time_set_fixedDeltaTime();

		//System.Void UnityEngine.Time::set_maximumDeltaTime(System.Single)
		void Register_UnityEngine_Time_set_maximumDeltaTime();
		Register_UnityEngine_Time_set_maximumDeltaTime();

		//System.Void UnityEngine.Time::set_maximumParticleDeltaTime(System.Single)
		void Register_UnityEngine_Time_set_maximumParticleDeltaTime();
		Register_UnityEngine_Time_set_maximumParticleDeltaTime();

		//System.Void UnityEngine.Time::set_timeScale(System.Single)
		void Register_UnityEngine_Time_set_timeScale();
		Register_UnityEngine_Time_set_timeScale();

	//End Registrations for type : UnityEngine.Time

	//Start Registrations for type : UnityEngine.TouchScreenKeyboard

		//System.Boolean UnityEngine.TouchScreenKeyboard::GetDone(System.IntPtr)
		void Register_UnityEngine_TouchScreenKeyboard_GetDone();
		Register_UnityEngine_TouchScreenKeyboard_GetDone();

		//System.Boolean UnityEngine.TouchScreenKeyboard::GetWasCanceled(System.IntPtr)
		void Register_UnityEngine_TouchScreenKeyboard_GetWasCanceled();
		Register_UnityEngine_TouchScreenKeyboard_GetWasCanceled();

		//System.Boolean UnityEngine.TouchScreenKeyboard::IsRequiredToForceOpen()
		void Register_UnityEngine_TouchScreenKeyboard_IsRequiredToForceOpen();
		Register_UnityEngine_TouchScreenKeyboard_IsRequiredToForceOpen();

		//System.Boolean UnityEngine.TouchScreenKeyboard::get_active()
		void Register_UnityEngine_TouchScreenKeyboard_get_active();
		Register_UnityEngine_TouchScreenKeyboard_get_active();

		//System.Boolean UnityEngine.TouchScreenKeyboard::get_canGetSelection()
		void Register_UnityEngine_TouchScreenKeyboard_get_canGetSelection();
		Register_UnityEngine_TouchScreenKeyboard_get_canGetSelection();

		//System.Boolean UnityEngine.TouchScreenKeyboard::get_canSetSelection()
		void Register_UnityEngine_TouchScreenKeyboard_get_canSetSelection();
		Register_UnityEngine_TouchScreenKeyboard_get_canSetSelection();

		//System.IntPtr UnityEngine.TouchScreenKeyboard::TouchScreenKeyboard_InternalConstructorHelper(UnityEngine.TouchScreenKeyboard_InternalConstructorHelperArguments&,System.String,System.String)
		void Register_UnityEngine_TouchScreenKeyboard_TouchScreenKeyboard_InternalConstructorHelper();
		Register_UnityEngine_TouchScreenKeyboard_TouchScreenKeyboard_InternalConstructorHelper();

		//System.String UnityEngine.TouchScreenKeyboard::get_text()
		void Register_UnityEngine_TouchScreenKeyboard_get_text();
		Register_UnityEngine_TouchScreenKeyboard_get_text();

		//System.Void UnityEngine.TouchScreenKeyboard::GetSelection(System.Int32&,System.Int32&)
		void Register_UnityEngine_TouchScreenKeyboard_GetSelection();
		Register_UnityEngine_TouchScreenKeyboard_GetSelection();

		//System.Void UnityEngine.TouchScreenKeyboard::Internal_Destroy(System.IntPtr)
		void Register_UnityEngine_TouchScreenKeyboard_Internal_Destroy();
		Register_UnityEngine_TouchScreenKeyboard_Internal_Destroy();

		//System.Void UnityEngine.TouchScreenKeyboard::SetSelection(System.Int32,System.Int32)
		void Register_UnityEngine_TouchScreenKeyboard_SetSelection();
		Register_UnityEngine_TouchScreenKeyboard_SetSelection();

		//System.Void UnityEngine.TouchScreenKeyboard::get_area_Injected(UnityEngine.Rect&)
		void Register_UnityEngine_TouchScreenKeyboard_get_area_Injected();
		Register_UnityEngine_TouchScreenKeyboard_get_area_Injected();

		//System.Void UnityEngine.TouchScreenKeyboard::set_active(System.Boolean)
		void Register_UnityEngine_TouchScreenKeyboard_set_active();
		Register_UnityEngine_TouchScreenKeyboard_set_active();

		//System.Void UnityEngine.TouchScreenKeyboard::set_characterLimit(System.Int32)
		void Register_UnityEngine_TouchScreenKeyboard_set_characterLimit();
		Register_UnityEngine_TouchScreenKeyboard_set_characterLimit();

		//System.Void UnityEngine.TouchScreenKeyboard::set_hideInput(System.Boolean)
		void Register_UnityEngine_TouchScreenKeyboard_set_hideInput();
		Register_UnityEngine_TouchScreenKeyboard_set_hideInput();

		//System.Void UnityEngine.TouchScreenKeyboard::set_text(System.String)
		void Register_UnityEngine_TouchScreenKeyboard_set_text();
		Register_UnityEngine_TouchScreenKeyboard_set_text();

		//UnityEngine.TouchScreenKeyboard/Status UnityEngine.TouchScreenKeyboard::get_status()
		void Register_UnityEngine_TouchScreenKeyboard_get_status();
		Register_UnityEngine_TouchScreenKeyboard_get_status();

	//End Registrations for type : UnityEngine.TouchScreenKeyboard

	//Start Registrations for type : UnityEngine.TrailRenderer

		//System.Single UnityEngine.TrailRenderer::get_endWidth()
		void Register_UnityEngine_TrailRenderer_get_endWidth();
		Register_UnityEngine_TrailRenderer_get_endWidth();

		//System.Single UnityEngine.TrailRenderer::get_startWidth()
		void Register_UnityEngine_TrailRenderer_get_startWidth();
		Register_UnityEngine_TrailRenderer_get_startWidth();

		//System.Single UnityEngine.TrailRenderer::get_time()
		void Register_UnityEngine_TrailRenderer_get_time();
		Register_UnityEngine_TrailRenderer_get_time();

		//System.Void UnityEngine.TrailRenderer::set_endWidth(System.Single)
		void Register_UnityEngine_TrailRenderer_set_endWidth();
		Register_UnityEngine_TrailRenderer_set_endWidth();

		//System.Void UnityEngine.TrailRenderer::set_startWidth(System.Single)
		void Register_UnityEngine_TrailRenderer_set_startWidth();
		Register_UnityEngine_TrailRenderer_set_startWidth();

		//System.Void UnityEngine.TrailRenderer::set_time(System.Single)
		void Register_UnityEngine_TrailRenderer_set_time();
		Register_UnityEngine_TrailRenderer_set_time();

	//End Registrations for type : UnityEngine.TrailRenderer

	//Start Registrations for type : UnityEngine.Transform

		//System.Boolean UnityEngine.Transform::IsChildOf(UnityEngine.Transform)
		void Register_UnityEngine_Transform_IsChildOf();
		Register_UnityEngine_Transform_IsChildOf();

		//System.Boolean UnityEngine.Transform::get_hasChanged()
		void Register_UnityEngine_Transform_get_hasChanged();
		Register_UnityEngine_Transform_get_hasChanged();

		//System.Int32 UnityEngine.Transform::GetSiblingIndex()
		void Register_UnityEngine_Transform_GetSiblingIndex();
		Register_UnityEngine_Transform_GetSiblingIndex();

		//System.Int32 UnityEngine.Transform::get_childCount()
		void Register_UnityEngine_Transform_get_childCount();
		Register_UnityEngine_Transform_get_childCount();

		//System.Int32 UnityEngine.Transform::internal_getHierarchyCapacity()
		void Register_UnityEngine_Transform_internal_getHierarchyCapacity();
		Register_UnityEngine_Transform_internal_getHierarchyCapacity();

		//System.Int32 UnityEngine.Transform::internal_getHierarchyCount()
		void Register_UnityEngine_Transform_internal_getHierarchyCount();
		Register_UnityEngine_Transform_internal_getHierarchyCount();

		//System.Void UnityEngine.Transform::DetachChildren()
		void Register_UnityEngine_Transform_DetachChildren();
		Register_UnityEngine_Transform_DetachChildren();

		//System.Void UnityEngine.Transform::Internal_LookAt_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_Internal_LookAt_Injected();
		Register_UnityEngine_Transform_Internal_LookAt_Injected();

		//System.Void UnityEngine.Transform::InverseTransformDirection_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_InverseTransformDirection_Injected();
		Register_UnityEngine_Transform_InverseTransformDirection_Injected();

		//System.Void UnityEngine.Transform::InverseTransformPoint_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_InverseTransformPoint_Injected();
		Register_UnityEngine_Transform_InverseTransformPoint_Injected();

		//System.Void UnityEngine.Transform::InverseTransformVector_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_InverseTransformVector_Injected();
		Register_UnityEngine_Transform_InverseTransformVector_Injected();

		//System.Void UnityEngine.Transform::RotateAroundInternal_Injected(UnityEngine.Vector3&,System.Single)
		void Register_UnityEngine_Transform_RotateAroundInternal_Injected();
		Register_UnityEngine_Transform_RotateAroundInternal_Injected();

		//System.Void UnityEngine.Transform::SetAsFirstSibling()
		void Register_UnityEngine_Transform_SetAsFirstSibling();
		Register_UnityEngine_Transform_SetAsFirstSibling();

		//System.Void UnityEngine.Transform::SetAsLastSibling()
		void Register_UnityEngine_Transform_SetAsLastSibling();
		Register_UnityEngine_Transform_SetAsLastSibling();

		//System.Void UnityEngine.Transform::SetParent(UnityEngine.Transform,System.Boolean)
		void Register_UnityEngine_Transform_SetParent();
		Register_UnityEngine_Transform_SetParent();

		//System.Void UnityEngine.Transform::SetPositionAndRotation_Injected(UnityEngine.Vector3&,UnityEngine.Quaternion&)
		void Register_UnityEngine_Transform_SetPositionAndRotation_Injected();
		Register_UnityEngine_Transform_SetPositionAndRotation_Injected();

		//System.Void UnityEngine.Transform::SetSiblingIndex(System.Int32)
		void Register_UnityEngine_Transform_SetSiblingIndex();
		Register_UnityEngine_Transform_SetSiblingIndex();

		//System.Void UnityEngine.Transform::TransformDirection_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_TransformDirection_Injected();
		Register_UnityEngine_Transform_TransformDirection_Injected();

		//System.Void UnityEngine.Transform::TransformPoint_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_TransformPoint_Injected();
		Register_UnityEngine_Transform_TransformPoint_Injected();

		//System.Void UnityEngine.Transform::TransformVector_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_TransformVector_Injected();
		Register_UnityEngine_Transform_TransformVector_Injected();

		//System.Void UnityEngine.Transform::get_localPosition_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_get_localPosition_Injected();
		Register_UnityEngine_Transform_get_localPosition_Injected();

		//System.Void UnityEngine.Transform::get_localRotation_Injected(UnityEngine.Quaternion&)
		void Register_UnityEngine_Transform_get_localRotation_Injected();
		Register_UnityEngine_Transform_get_localRotation_Injected();

		//System.Void UnityEngine.Transform::get_localScale_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_get_localScale_Injected();
		Register_UnityEngine_Transform_get_localScale_Injected();

		//System.Void UnityEngine.Transform::get_localToWorldMatrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Transform_get_localToWorldMatrix_Injected();
		Register_UnityEngine_Transform_get_localToWorldMatrix_Injected();

		//System.Void UnityEngine.Transform::get_lossyScale_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_get_lossyScale_Injected();
		Register_UnityEngine_Transform_get_lossyScale_Injected();

		//System.Void UnityEngine.Transform::get_position_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_get_position_Injected();
		Register_UnityEngine_Transform_get_position_Injected();

		//System.Void UnityEngine.Transform::get_rotation_Injected(UnityEngine.Quaternion&)
		void Register_UnityEngine_Transform_get_rotation_Injected();
		Register_UnityEngine_Transform_get_rotation_Injected();

		//System.Void UnityEngine.Transform::get_worldToLocalMatrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Transform_get_worldToLocalMatrix_Injected();
		Register_UnityEngine_Transform_get_worldToLocalMatrix_Injected();

		//System.Void UnityEngine.Transform::internal_setHierarchyCapacity(System.Int32)
		void Register_UnityEngine_Transform_internal_setHierarchyCapacity();
		Register_UnityEngine_Transform_internal_setHierarchyCapacity();

		//System.Void UnityEngine.Transform::set_hasChanged(System.Boolean)
		void Register_UnityEngine_Transform_set_hasChanged();
		Register_UnityEngine_Transform_set_hasChanged();

		//System.Void UnityEngine.Transform::set_localPosition_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_set_localPosition_Injected();
		Register_UnityEngine_Transform_set_localPosition_Injected();

		//System.Void UnityEngine.Transform::set_localRotation_Injected(UnityEngine.Quaternion&)
		void Register_UnityEngine_Transform_set_localRotation_Injected();
		Register_UnityEngine_Transform_set_localRotation_Injected();

		//System.Void UnityEngine.Transform::set_localScale_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_set_localScale_Injected();
		Register_UnityEngine_Transform_set_localScale_Injected();

		//System.Void UnityEngine.Transform::set_position_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_set_position_Injected();
		Register_UnityEngine_Transform_set_position_Injected();

		//System.Void UnityEngine.Transform::set_rotation_Injected(UnityEngine.Quaternion&)
		void Register_UnityEngine_Transform_set_rotation_Injected();
		Register_UnityEngine_Transform_set_rotation_Injected();

		//UnityEngine.Transform UnityEngine.Transform::FindRelativeTransformWithPath(UnityEngine.Transform,System.String,System.Boolean)
		void Register_UnityEngine_Transform_FindRelativeTransformWithPath();
		Register_UnityEngine_Transform_FindRelativeTransformWithPath();

		//UnityEngine.Transform UnityEngine.Transform::GetChild(System.Int32)
		void Register_UnityEngine_Transform_GetChild();
		Register_UnityEngine_Transform_GetChild();

		//UnityEngine.Transform UnityEngine.Transform::GetParent()
		void Register_UnityEngine_Transform_GetParent();
		Register_UnityEngine_Transform_GetParent();

		//UnityEngine.Transform UnityEngine.Transform::GetRoot()
		void Register_UnityEngine_Transform_GetRoot();
		Register_UnityEngine_Transform_GetRoot();

	//End Registrations for type : UnityEngine.Transform

	//Start Registrations for type : UnityEngine.U2D.SpriteAtlas

		//System.Boolean UnityEngine.U2D.SpriteAtlas::CanBindTo(UnityEngine.Sprite)
		void Register_UnityEngine_U2D_SpriteAtlas_CanBindTo();
		Register_UnityEngine_U2D_SpriteAtlas_CanBindTo();

	//End Registrations for type : UnityEngine.U2D.SpriteAtlas

	//Start Registrations for type : UnityEngine.U2D.SpriteAtlasManager

		//System.Void UnityEngine.U2D.SpriteAtlasManager::Register(UnityEngine.U2D.SpriteAtlas)
		void Register_UnityEngine_U2D_SpriteAtlasManager_Register();
		Register_UnityEngine_U2D_SpriteAtlasManager_Register();

	//End Registrations for type : UnityEngine.U2D.SpriteAtlasManager

	//Start Registrations for type : UnityEngine.UISystemProfilerApi

		//System.Void UnityEngine.UISystemProfilerApi::AddMarker(System.String,UnityEngine.Object)
		void Register_UnityEngine_UISystemProfilerApi_AddMarker();
		Register_UnityEngine_UISystemProfilerApi_AddMarker();

		//System.Void UnityEngine.UISystemProfilerApi::BeginSample(UnityEngine.UISystemProfilerApi/SampleType)
		void Register_UnityEngine_UISystemProfilerApi_BeginSample();
		Register_UnityEngine_UISystemProfilerApi_BeginSample();

		//System.Void UnityEngine.UISystemProfilerApi::EndSample(UnityEngine.UISystemProfilerApi/SampleType)
		void Register_UnityEngine_UISystemProfilerApi_EndSample();
		Register_UnityEngine_UISystemProfilerApi_EndSample();

	//End Registrations for type : UnityEngine.UISystemProfilerApi

	//Start Registrations for type : UnityEngine.UnhandledExceptionHandler

		//System.Void UnityEngine.UnhandledExceptionHandler::iOSNativeUnhandledExceptionHandler(System.String,System.String,System.String)
		void Register_UnityEngine_UnhandledExceptionHandler_iOSNativeUnhandledExceptionHandler();
		Register_UnityEngine_UnhandledExceptionHandler_iOSNativeUnhandledExceptionHandler();

	//End Registrations for type : UnityEngine.UnhandledExceptionHandler

	//Start Registrations for type : UnityEngine.UnityLogWriter

		//System.Void UnityEngine.UnityLogWriter::WriteStringToUnityLogImpl(System.String)
		void Register_UnityEngine_UnityLogWriter_WriteStringToUnityLogImpl();
		Register_UnityEngine_UnityLogWriter_WriteStringToUnityLogImpl();

	//End Registrations for type : UnityEngine.UnityLogWriter

	//Start Registrations for type : UnityEngine.VFX.VFXEventAttribute

		//System.IntPtr UnityEngine.VFX.VFXEventAttribute::Internal_Create()
		void Register_UnityEngine_VFX_VFXEventAttribute_Internal_Create();
		Register_UnityEngine_VFX_VFXEventAttribute_Internal_Create();

		//System.Void UnityEngine.VFX.VFXEventAttribute::Internal_Destroy(System.IntPtr)
		void Register_UnityEngine_VFX_VFXEventAttribute_Internal_Destroy();
		Register_UnityEngine_VFX_VFXEventAttribute_Internal_Destroy();

		//System.Void UnityEngine.VFX.VFXEventAttribute::Internal_InitFromAsset(UnityEngine.VFX.VisualEffectAsset)
		void Register_UnityEngine_VFX_VFXEventAttribute_Internal_InitFromAsset();
		Register_UnityEngine_VFX_VFXEventAttribute_Internal_InitFromAsset();

	//End Registrations for type : UnityEngine.VFX.VFXEventAttribute

	//Start Registrations for type : UnityEngine.VFX.VFXSpawnerState

		//System.Void UnityEngine.VFX.VFXSpawnerState::Internal_Destroy(System.IntPtr)
		void Register_UnityEngine_VFX_VFXSpawnerState_Internal_Destroy();
		Register_UnityEngine_VFX_VFXSpawnerState_Internal_Destroy();

	//End Registrations for type : UnityEngine.VFX.VFXSpawnerState

	//Start Registrations for type : UnityEngine.VFX.VisualEffect

		//UnityEngine.VFX.VisualEffectAsset UnityEngine.VFX.VisualEffect::get_visualEffectAsset()
		void Register_UnityEngine_VFX_VisualEffect_get_visualEffectAsset();
		Register_UnityEngine_VFX_VisualEffect_get_visualEffectAsset();

	//End Registrations for type : UnityEngine.VFX.VisualEffect

	//Start Registrations for type : UnityEngine.Video.VideoPlayer

		//System.Int64 UnityEngine.Video.VideoPlayer::get_frame()
		void Register_UnityEngine_Video_VideoPlayer_get_frame();
		Register_UnityEngine_Video_VideoPlayer_get_frame();

		//System.UInt64 UnityEngine.Video.VideoPlayer::get_frameCount()
		void Register_UnityEngine_Video_VideoPlayer_get_frameCount();
		Register_UnityEngine_Video_VideoPlayer_get_frameCount();

	//End Registrations for type : UnityEngine.Video.VideoPlayer

	//Start Registrations for type : UnityEngineInternal.Input.NativeInputSystem

		//System.Void UnityEngineInternal.Input.NativeInputSystem::set_hasDeviceDiscoveredCallback(System.Boolean)
		void Register_UnityEngineInternal_Input_NativeInputSystem_set_hasDeviceDiscoveredCallback();
		Register_UnityEngineInternal_Input_NativeInputSystem_set_hasDeviceDiscoveredCallback();

	//End Registrations for type : UnityEngineInternal.Input.NativeInputSystem

}

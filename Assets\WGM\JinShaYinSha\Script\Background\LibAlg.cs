using UnityEngine;
using System;
using System.Collections;
 

namespace WGM.Bet.JinShaYinSha
{
    public class JsysDef
    {
        public const int PLAYER_MAX = 8;//玩家数目.
        public const int PLAYER_MAX2 = 106;//玩家数目.
        public const int NPC_SAME_TYPE = 29;
        public const int SAME_TYPE_MAX = 10;
        public const int DOU_TYPE = 39;
        public const int DOU_TYPE_MAX = 6;
    }

    public class AlgDef
    {
        //public const int ITEM_MAX = 8; //押分项目
        public const int ITEM_MAX = 8+4; //押分项目

        public const int HISTORY_MAX = 20;
        public const int HISTORY_ShowMAX = 14;
    }

    public enum WinType
    {
        Normal = 0,
        Light,
        Bonus,
        
    }

    public struct AlgConf
    {
        /// <summary>
        /// 用户分数
        /// </summary>
        public int score { get { return (int)LibAlg.player[seat].score; } set { LibAlg.player[seat].score = value; } }

        /// <summary>
        /// 一币多少分
        /// </summary>
        public int one_coin_score;
        /// <summary>
        /// 最大炮值 修改需报码 并需传递修改标志给报码函数 最大炮值与最小炮值在后台设定时差距不大于100倍(例:最大炮值设500 最小炮值最小设定到5).
        /// 范围(千炮100-1K 万炮1K-1W) 出厂值(千炮1K 万炮1W) 步进(千炮100 万炮1K).
        /// </summary>
        public uint bet_max;
        /// <summary>
        /// 最小炮值 范围(千炮1-100 万炮10-1000) 出厂值(千炮10 万炮100) 步进(千炮{<=10-1 <=100-10} 万炮{<=100-10 <=1000-100}).
        /// </summary>
        public uint bet_min;
        /// <summary>
        /// 押分时间
        /// </summary>
        public byte bet_time;
        /// <summary>
        /// 抢庄所需分数
        /// </summary>
        public int bankerScoreNeed;
        /// <summary>
        /// 单门限红
        /// </summary>
        public int itemSingleScoreLimit;
        /// <summary>
        /// 全台限红
        /// </summary>
        public int itemAllScoreLimit;

        /// <summary>
        /// 桌台数量
        /// </summary>
        public int table_max { get { return room_table[room_select]; } }

        public int room_amount;
        public int room_select;

        public int[] room_betTime;
        public int[] room_table;
        public int[] room_betMax;
        public int[] room_betMin;

        public int[] room_betMaxVice;
        public int[] room_betMinVice;
        public int[] room_betMaxDraw;
        public int[] room_betMinDraw;

        public int[] room_oneExchangeCoin;
        public int[] room_oneCoinScore;
        public int[] room_coinsNeed;
        public int[] room_BankerScoreNeed;

        public int[] room_ItemSingleScoreLimit;
        public int[] room_ItemAllScoreLimit;
        public int[] room_g_Money;


        public string[] room_table_name;
        public int[] room_table_time;
        /// <summary>
        /// 桌台是否有人玩
        /// </summary>
        public bool[] table_active;
        /// <summary>
        /// 玩家进入的桌台号
        /// </summary>
        public int table;

        /// <summary>
        /// 机台人数 范围(0-? 根据实际需要调整) 出厂值(0).
        /// </summary>
        public int player_num;


        /// <summary>
        /// 玩家座位号
        /// </summary>
        public int seat;

        public AlgConf(string name)
        {
            seat = 0;

            player_num = JsysDef.PLAYER_MAX;

            one_coin_score = 100;
            bet_min = 1;
            bet_max = 1000;
            bet_time = 30;
            bankerScoreNeed = 500000;
            itemSingleScoreLimit = 5000;
            itemAllScoreLimit = 50000;


            room_select = 0;
            room_amount = 3;

            room_betTime = new int[room_amount];
            room_table = new int[room_amount];
            room_betMax = new int[room_amount];
            room_betMin = new int[room_amount];

            room_betMaxVice = new int[room_amount];
            room_betMinVice = new int[room_amount];
            room_betMaxDraw = new int[room_amount];
            room_betMinDraw = new int[room_amount];

            room_oneExchangeCoin = new int[room_amount];
            room_oneCoinScore = new int[room_amount];
            room_coinsNeed = new int[room_amount];
            room_BankerScoreNeed = new int[room_amount];

            room_ItemSingleScoreLimit = new int[room_amount];
            room_ItemAllScoreLimit = new int[room_amount];
            room_g_Money = new int[room_amount];

            room_table_name = new string[1000];
            table_active = new bool[1000];
            room_table_time = new int[1000];
            table = 0;
        }

        public void InitRoom(int room)
        {
            room_amount = room;
            room_select = 0;


            room_betTime = new int[room_amount];
            room_table = new int[room_amount];
            room_betMax = new int[room_amount];
            room_betMin = new int[room_amount];

            room_betMaxVice = new int[room_amount];
            room_betMinVice = new int[room_amount];
            room_betMaxDraw = new int[room_amount];
            room_betMinDraw = new int[room_amount];

            room_oneExchangeCoin = new int[room_amount];
            room_oneCoinScore = new int[room_amount];
            room_coinsNeed = new int[room_amount];
            room_BankerScoreNeed = new int[room_amount];

            room_ItemSingleScoreLimit = new int[room_amount];
            room_ItemAllScoreLimit = new int[room_amount];
            room_g_Money = new int[room_amount];
        }
    };

    public struct PlayerRec
    {
        public long score;         //玩家台面分数
        public int win;
        public byte[] multiples;
        public int[] totalBet;
        public int[] bet;
        public byte winType;
        public byte winNum;
        public byte[] winItem;
        public int[] winScore;
        public byte winJp;
        public int playersBetAll;
        public int playersWinAll;
        public int playersPumpAll;
        public byte normalTime;
        public bool isBanker;
        public byte[] historyRecords;
        public bool success;
        public byte betId;
        public bool isRecResult;

        public bool active;
        public int portrait;


        public string nickname;
        public int userID;//玩家唯一标识id
        public int coin; //玩家金币
        internal ushort playerMax; //房间座位数

        public int body
        {
            get
            {
                if (portrait < 8) return 0;
                else if (portrait < 15) return 1;
                else if (portrait < 22) return 2;
                else return 3;
            }
        }

        public int betSum
        {
            get
            {
                int sum = 0;
                for (int i = 0; i < AlgDef.ITEM_MAX; i++)
                {
                    sum += bet[i];
                }
                return sum;
            }
        }

        public int winSum
        {
            get
            {
                int sum = 0;
                for (int i = 0; i < AlgDef.ITEM_MAX; i++)
                {
                    sum += winScore[i];
                }
                return sum;
            }
        }

        public PlayerRec(int id)
        {
            playerMax = JsysDef.PLAYER_MAX;
            nickname = "User" + id;
            portrait = 0;
            userID = 0;
            coin = 0;

            score = 0;
            win = 0;
            multiples = new byte[AlgDef.ITEM_MAX ];
            totalBet = new int[AlgDef.ITEM_MAX ];
            bet = new int[AlgDef.ITEM_MAX ];
            winType = 0;
            winNum = 0;
            winItem = new byte[AlgDef.ITEM_MAX];
            winScore = new int[AlgDef.ITEM_MAX];
            winJp = 0;
            playersBetAll = 0;
            playersWinAll = 0;
            playersPumpAll = 0;
            normalTime = 0;
            isBanker = false;
            historyRecords = new byte[AlgDef.HISTORY_MAX];
            success = false;
            betId = 0;
            isRecResult = false;
            active = false;
        }
    }

    public class LibAlg : GameData
    {
 
        public static AlgConf run = new AlgConf("");
        public static PlayerRec[] player = new PlayerRec[JsysDef.PLAYER_MAX2];

        public static Action onBetPrepare;
        public static Action onBetCountDown;
        public static Action onBetBet;
        public static Action onBetCancel;
        public static Action onBetContinue;
        public static Action onBetResult;
        public static Action<int> onTableStatus;
        public static Action<int> onSit;
        public static Action onHistoryCardUpdate;
        //在线人数更新
        public static Action onlinePeople;

        static LibAlg()
        {
            for (int i = 0; i < JsysDef.PLAYER_MAX; i++)
            {
                player[i] = new PlayerRec(i);
            }
        }


    
        public static CmdType GetCmdType()
        {
            CmdType type = (CmdType)network.GetCommand(ref mCmdBuf);

            int len = 0, table_id, player_id, tmp;

            ushort cnt = 4; 
            string msg;
            if (type != CmdType.Null && type != CmdType.HeartBeat)
            {
                len = (mCmdBuf[2] << 8) + mCmdBuf[3] + 4;

                Debug.Log("Receive " + type);
            }
  
            switch (type)
            {
                case CmdType.Login:

                    GameData.nickname = Network.UcToString(mCmdBuf, ref cnt);
                    GameData.portrait = mCmdBuf[cnt++];
                    GameData.coin = Network.UcToU4(mCmdBuf, ref cnt);
                    run.score = (Int32)Network.UcToU4(mCmdBuf, ref cnt);
                    run.room_amount = mCmdBuf[cnt++];
                    run.InitRoom(run.room_amount);

                    for (int i = 0; i < run.room_amount; i++)
                    {
                        run.room_betTime[i] = mCmdBuf[cnt++];
                        run.room_table[i] = Network.UcToU2(mCmdBuf, ref cnt);
                        run.room_betMax[i] = (Int32)Network.UcToU4(mCmdBuf, ref cnt);
                        run.room_betMin[i] = (Int32)Network.UcToU4(mCmdBuf, ref cnt);

                        run.room_betMaxVice[i] = (Int32)Network.UcToU4(mCmdBuf, ref cnt);
                        run.room_betMinVice[i] = (Int32)Network.UcToU4(mCmdBuf, ref cnt);
                        run.room_betMaxDraw[i] = (Int32)Network.UcToU4(mCmdBuf, ref cnt);
                        run.room_betMinDraw[i] = (Int32)Network.UcToU4(mCmdBuf, ref cnt);

                        run.room_oneExchangeCoin[i] = Network.UcToU2(mCmdBuf, ref cnt);
                        run.room_oneCoinScore[i] = Network.UcToU2(mCmdBuf, ref cnt);
                        run.room_coinsNeed[i] = (Int32)Network.UcToU4(mCmdBuf, ref cnt);
                        run.room_BankerScoreNeed[i] = (Int32)Network.UcToU4(mCmdBuf, ref cnt);

                        run.room_ItemSingleScoreLimit[i] = (Int32)Network.UcToU4(mCmdBuf, ref cnt);
                        run.room_ItemAllScoreLimit[i] = (Int32)Network.UcToU4(mCmdBuf, ref cnt);
                        run.room_g_Money[i] = (Int32)Network.UcToU4(mCmdBuf, ref cnt); 
                        
                    }

                    //run.bet_max = Network.UcToU2(mCmdBuf, ref cnt);
                    //run.bet_min = Network.UcToU2(mCmdBuf, ref cnt);
                    //run.bet_time = mCmdBuf[cnt++];
                    //run.bankerScoreNeed = Network.UcToS4(mCmdBuf, ref cnt);
                    //run.itemSingleScoreLimit = Network.UcToS4(mCmdBuf, ref cnt);
                    //run.itemAllScoreLimit = Network.UcToS4(mCmdBuf, ref cnt);

             
                    /*
                    Debug.Log("itemAllScoreLimit:" + run.itemAllScoreLimit);
                    Debug.Log("bet_max:" + run.bet_max); Debug.Log("nickname:" + GameData.nickname);
                    Debug.Log("coin:" + GameData.coin);
                    Debug.Log("score:" + run.score);
                    Debug.Log("bet_min:" + run.bet_min);
                    Debug.Log("bet_time:" + run.bet_time);
                    Debug.Log("bankerScoreNeed:" + run.bankerScoreNeed);
                    Debug.Log("itemSingleScoreLimit:" + run.itemSingleScoreLimit);
                    */




                    GameData.login_game = LoginStatus.Success;
                    if (onLogin != null) onLogin();
                    break;

                case CmdType.ReqTable:

                    table_id = Network.UcToU2(mCmdBuf, ref cnt);
                    GameData.onlinePeople = Network.UcToU2(mCmdBuf, ref cnt);


                    //Debug.Log(GameData.onlinePeople);

                    for (int i = 0; i < run.player_num; i++)
                    {
                        player[i].active = false;
                        player[i].nickname = "";
                        player[i].portrait = 0;
                        player[i].coin = 0;
                        player[i].score = 0;
                        //player[i].bet = 0;
                        player[i].userID = 0;
                    }
                    while (cnt < len)
                    {
                        player_id = mCmdBuf[cnt++]; 
                        player[player_id].nickname = Network.UcToString(mCmdBuf, ref cnt);

                        Debug.Log("昵称：" + player[player_id].nickname);

                        player[player_id].portrait = mCmdBuf[cnt++];
                        player[player_id].coin = Network.UcToS4(mCmdBuf, ref cnt);
                        player[player_id].score = Network.UcToS4(mCmdBuf, ref cnt);
                        //player[player_id].bet = Network.UcToU2(mCmdBuf, ref cnt);
                        player[player_id].userID = Network.UcToS4(mCmdBuf, ref cnt);


                        player[player_id].active = true;
                    }

                    if (onlinePeople != null) onlinePeople();
                    if (onTableStatus != null) onTableStatus(table_id);
                    break;

                case CmdType.SitDown:
                    player_id = mCmdBuf[cnt++];
                    if (player_id >= JsysDef.PLAYER_MAX)
                    {
                         Debug.Log("属于观战模式，座位号：" + player_id);
                    }
                    else
                    {
                        run.seat = player_id;

                        player[player_id].active = mCmdBuf[cnt++] > 0;
                        player[player_id].nickname = Network.UcToString(mCmdBuf, ref cnt);
                        player[player_id].portrait = mCmdBuf[cnt++];
                        player[player_id].coin = Network.UcToS4(mCmdBuf, ref cnt);
                        player[player_id].score = Network.UcToS4(mCmdBuf, ref cnt);
                        player[player_id].userID = Network.UcToS4(mCmdBuf, ref cnt);


                        //run.scene_index = mCmdBuf[cnt++];
                        //run.array_index = mCmdBuf[cnt++];
                        //run.scene_time = Network.UcToS4(mCmdBuf, ref cnt);

                        //断线续玩服务端下发的压分值和当前背景索引
                        //ushort curBetValue = Network.UcToU2(mCmdBuf, ref cnt);
                        //player[player_id].bet= curBetValue;                    
                        //LibAlg.run.scene_index = curBackgroundIndex;

                        if (player_id == run.seat)
                        {
                            GameData.coin = player[player_id].coin;
                            //  Debug.LogError("GameData.coin:" + GameData.coin);
                            //GameData.curBetValue = curBetValue;
                        }


                        //玩家数据全同步  xzhuan
                        player[player_id].playerMax = Network.UcToU2(mCmdBuf, ref cnt);
                        for (int i = 0; i < player[player_id].playerMax; i++)
                        {
                            player[i].nickname = Network.UcToString(mCmdBuf, ref cnt);
                            //player[i].bet = Network.UcToU2(mCmdBuf, ref cnt);
                            player[i].score = Network.UcToS4(mCmdBuf, ref cnt);
                            player[i].active = mCmdBuf[cnt++] > 0;
                            player[i].userID = Network.UcToS4(mCmdBuf, ref cnt);
                        }
 
                    }

           

                    if (onSit != null) onSit(player_id);
                    GameData.login_gameReconnect = ReconnectLoginStatus.Idle;

                    break;
                case CmdType.BetHistoryCard:
                    for (int i = 0; i < AlgDef.HISTORY_MAX; i++)
                    {
                        player[run.seat].historyRecords[i] = mCmdBuf[cnt++];
                        //Debug.Log("historyRecords" + i + ":" + player[run.seat].historyRecords[i]);
                    }

                    onHistoryCardUpdate?.Invoke(); 
                    break;
                case CmdType.CoinToScore:
                    GameData.coin = Network.UcToU4(mCmdBuf, ref cnt);
                    player[run.seat].score = Network.UcToU4(mCmdBuf, ref cnt) - (player[run.seat].isRecResult ? LibAlg.player[run.seat].winSum : LibAlg.player[run.seat].betSum);
                    if (onCoinToScore != null) onCoinToScore();
                    break;
                case CmdType.ScoreToCoin:
                    GameData.coin = Network.UcToU4(mCmdBuf, ref cnt);
                    player[run.seat].score = Network.UcToU4(mCmdBuf, ref cnt) - (player[run.seat].isRecResult ? LibAlg.player[run.seat].winSum : LibAlg.player[run.seat].betSum);
                    if (onScoreToCoin != null) onScoreToCoin();
                    break;
                case CmdType.BetPrepare:
                    for (int i = 0; i < AlgDef.ITEM_MAX; i++)
                    {
                        player[run.seat].multiples[i] = mCmdBuf[cnt++];
                        // Debug.Log("multiples" + i + ":" + player.multiples[i]);
                    }
                    player[run.seat].score = Network.UcToU4(mCmdBuf, ref cnt);
                    Debug.Log("score" + player[run.seat].score);
                    for (int i = 0; i < AlgDef.HISTORY_MAX; i++)
                    {
                        player[run.seat].historyRecords[i] = mCmdBuf[cnt++];
                     //   Debug.Log("historyRecords" + i + ":" + player.historyRecords[i]);
                    }
                    player[run.seat].isRecResult = false;
                    if (onBetPrepare != null) onBetPrepare();
                    break;
                case CmdType.BetCountDown:
                    player[run.seat].normalTime = mCmdBuf[cnt++];
                    for (int i = 0; i < AlgDef.ITEM_MAX; i++)
                    {
                        player[run.seat].totalBet[i] = Network.UcToS4(mCmdBuf, ref cnt);
                    }
                    if (onBetCountDown != null) onBetCountDown();
                    break;
                case CmdType.BetBet:
                    var ret = mCmdBuf[cnt++] ;
                    if (ret == 0)
                    {
                        player[run.seat].success = true;
                        player[run.seat].betId = mCmdBuf[cnt++];
                        player[run.seat].bet[player[run.seat].betId] = Network.UcToS4(mCmdBuf, ref cnt);
                        player[run.seat].totalBet[player[run.seat].betId] = Network.UcToS4(mCmdBuf, ref cnt);
                        player[run.seat].score = Network.UcToU4(mCmdBuf, ref cnt);

                        if (onBetBet != null)
                            onBetBet();
                    }
                    else
                    {
                        player[run.seat].success = false;
                    }

                  
                    break;
                case CmdType.BetCancel:
                    player[run.seat].success = true;
                    for (int i = 0; i < AlgDef.ITEM_MAX; i++)
                    {
                        player[run.seat].bet[i] = Network.UcToU2(mCmdBuf, ref cnt);
                        player[run.seat].totalBet[i] = Network.UcToS4(mCmdBuf, ref cnt);
                    }
                    player[run.seat].score = Network.UcToU4(mCmdBuf, ref cnt);
                    if (onBetCancel != null) onBetCancel();
                    break;
                case CmdType.BetContinue:
                    player[run.seat].success = mCmdBuf[cnt++] >= 0;
                    if (player[run.seat].success)
                    {
                        for (int i = 0; i < AlgDef.ITEM_MAX; i++)
                        {
                            player[run.seat].bet[i] = Network.UcToS4(mCmdBuf, ref cnt);
                            player[run.seat].totalBet[i] = Network.UcToS4(mCmdBuf, ref cnt);
                        }
                        player[run.seat].score = Network.UcToU4(mCmdBuf, ref cnt);
                    }
                    if (onBetContinue != null) onBetContinue();
                    break;
                case CmdType.BetResult:
                    Debug.Log("=== 收到BetResult消息 ===");
                    Debug.Log($"当前座位号: {run.seat}");
                    Debug.Log($"是否庄家: {player[run.seat].isBanker}");

                    player[run.seat].win = 0;

                    // 解析押注和总押注数据
                    Debug.Log("--- 押注数据 ---");
                    for (int i = 0; i < AlgDef.ITEM_MAX; i++)
                    {
                        player[run.seat].bet[i] = Network.UcToS4(mCmdBuf, ref cnt);
                        player[run.seat].totalBet[i] = Network.UcToS4(mCmdBuf, ref cnt);
                        Debug.Log($"项目{i}: bet={player[run.seat].bet[i]}, totalBet={player[run.seat].totalBet[i]}");
                    }

                    // 解析中奖类型和数量
                    player[run.seat].winType = mCmdBuf[cnt++];
                    player[run.seat].winNum = mCmdBuf[cnt++];
                    Debug.Log($"中奖类型: {player[run.seat].winType}");
                    Debug.Log($"中奖项目数量: {player[run.seat].winNum}");

                    // 解析中奖项目
                    Debug.Log("--- 中奖项目 ---");
                    for (int i = 0; i < player[run.seat].winNum; i++)
                    {
                        player[run.seat].winItem[i] = mCmdBuf[cnt++];
                        Debug.Log($"中奖项目{i}: {player[run.seat].winItem[i]}");
                    }

                    // 解析彩金
                    player[run.seat].winJp = mCmdBuf[cnt++];
                    Debug.Log($"彩金: {player[run.seat].winJp}");

                    if (player[run.seat].isBanker)
                    {
                        // 庄家数据
                        player[run.seat].playersBetAll = Network.UcToS4(mCmdBuf, ref cnt);
                        player[run.seat].playersWinAll = Network.UcToS4(mCmdBuf, ref cnt);
                        player[run.seat].playersPumpAll = Network.UcToS4(mCmdBuf, ref cnt);
                        Debug.Log("--- 庄家数据 ---");
                        Debug.Log($"玩家总押注: {player[run.seat].playersBetAll}");
                        Debug.Log($"玩家总赢分: {player[run.seat].playersWinAll}");
                        Debug.Log($"玩家总抽水: {player[run.seat].playersPumpAll}");
                    }
                    else
                    {
                        // 普通玩家中奖分数
                        Debug.Log("--- 中奖分数 ---");
                        for (int i = 0; i < AlgDef.ITEM_MAX; i++)
                        {
                            player[run.seat].winScore[i] = Network.UcToS4(mCmdBuf, ref cnt);
                            player[run.seat].win += player[run.seat].winScore[i];
                            Debug.Log($"项目{i}中奖分数: {player[run.seat].winScore[i]}");
                        }
                    }

                    player[run.seat].win += player[run.seat].winJp;
                    player[run.seat].isRecResult = true;

                    Debug.Log($"总中奖金额: {player[run.seat].win}");
                    Debug.Log("=== BetResult消息处理完成 ===");

                    if (onBetResult != null) onBetResult();
                    break;
                case CmdType.HeartBeat:
                    if (onHeartBeat != null) onHeartBeat();
                    break;
                case CmdType.ServerMsg:
                    msg = Network.UcToString(mCmdBuf, ref cnt);
                    msg = Network.ChuliZhongjiangxiaoxi(msg);
                    if (onServerMsg != null) onServerMsg(msg);
                    break;
                case CmdType.ServerHallMsg:
                    msg = Network.UcToString(mCmdBuf, ref cnt);
                    if (onServerHallMsg != null) onServerHallMsg(msg);
                    break;
                case CmdType.ForceLogout:
                    tmp = mCmdBuf[cnt++];
                    if (onForceLogout != null) onForceLogout(tmp);
                    break;
                default: break;
            }

            return type;
        }

        public static void SendBetPrepare()
        {
            byte[] buf = new byte[10];
            ushort cnt = 0;

            network.SendCommand(CmdType.BetPrepare, buf, cnt);
            Debug.Log("Send " + CmdType.BetPrepare);
        }

        public static void SendBetBet(int id, int bet)
        {

           Debug.Log("id:" + id + "      bet:" + bet);
            byte[] buf = new byte[100];
            ushort cnt = 0;

            buf[cnt++] = (byte)id;
            Network.S4ToUc(bet, buf, ref cnt);
            network.SendCommand(CmdType.BetBet, buf, cnt);
            Debug.Log("Send " + CmdType.BetBet);
        }


        public static void SendBetHistroy()
        {
            byte[] buf = new byte[10];
            ushort cnt = 0;

            network.SendCommand(CmdType.BetHistoryCard, buf, cnt);
            Debug.Log("Send " + CmdType.BetHistoryCard);
        }

        public static void SendBetCancel()
        {
            byte[] buf = new byte[10];
            ushort cnt = 0;

            network.SendCommand(CmdType.BetCancel, buf, cnt);
            Debug.Log("Send " + CmdType.BetCancel);
        }

        public static void SendBetContinue()
        {
            byte[] buf = new byte[10];
            ushort cnt = 0;

            network.SendCommand(CmdType.BetContinue, buf, cnt);
            Debug.Log("Send " + CmdType.BetContinue);
        }

        public static void SendBetEnd()
        {
            byte[] buf = new byte[10];
            ushort cnt = 0;

            network.SendCommand(CmdType.BetEnd, buf, cnt);
            Debug.Log("Send " + CmdType.BetEnd);
        }

        public static void Init()
        {
            for (int i = 0; i < AlgDef.ITEM_MAX; i++)
            {
                player[run.seat].bet[i] = 0;
                player[run.seat].totalBet[i] = 0;
                player[run.seat].winScore[i] = 0;
            }
            player[run.seat].winJp = 0;
        }

        public static void ApplicationQuit()
        {
            onBetPrepare = null;
            onBetCountDown = null;
            onBetBet = null;
            onBetCancel = null;
            onBetContinue = null;
            onBetResult = null;
            onServerMsg = null;

            ApplicationQuitBase();
        }

        public static void SendGetTableStatus()
        {
            byte[] buf = new byte[10];
            ushort cnt = 0;

            buf[cnt++] = (byte)run.room_select;
            network.SendCommand(CmdType.ReqTable, buf, cnt);
        }
    }
}
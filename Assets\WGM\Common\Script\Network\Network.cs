//#define NetworkDebug

/*
10004 The operation is canceled. 
10013 The requested address is a broadcast address, but flag is not set. 
10014 Invalid argument. 
10022 Socket not bound, invalid address or listen is not invoked prior to accept. 
10024 No more file descriptors are available, accept queue is empty. 
10035 Socket is non-blocking and the specified operation will block. 
10036 A blocking Winsock operation is in progress. 
10037 The operation is completed. No blocking operation is in progress. 
10038 The descriptor is not a socket. 
10039 Destination address is required. 
10040 The datagram is too large to fit into the buffer and is truncated. 
10041 The specified port is the wrong type for this socket. 
10042 Option unknown, or unsupported. 
10043 The specified port is not supported. 
10044 Socket type not supported in this address family. 
10045 Socket is not a type that supports connection oriented service. 
10047 Address Family is not supported. 
10048 Address in use. 
10049 Address is not available from the local machine. 
10050 Network subsystem failed. 
10051 The network cannot be reached from this host at this time. 
10052 Connection has timed out when SO_KEEPALIVE is set. 
10053 Connection is aborted due to timeout or other failure. 
10054 The connection is reset by remote side.
10055 No buffer space is available. 
10056 Socket is already connected. 
10057 Socket is not connected. 
10058 Socket has been shut down. 
10060 The attempt to connect timed out. 
10061 Connection is forcefully rejected. 
10201 Socket already created for this object. 
10202 Socket has not been created for this object. 
11001 Authoritative answer: Host not found. 
11002 Non-Authoritative answer: Host not found. 
11003 Non-recoverable errors. 
11004 Valid name, no data record of requested type.
 */

using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Net.Sockets;
using System.Threading;
using System.Net;
using System.Text;
using System.IO;
using UnityEngine.SceneManagement;
using WGM.YYT;
using Sockets;
using System.Linq;

public enum CmdType
{
    Null = 0,
    Login = 0xaa01,
    Coint = 0xaa19,
    Logout = 0xaa02,
    Regist = 0xaa03,
    SitDown = 0xaa04,
    ReqTable = 0xaa05,
    SitUp = 0xaa06,
    CoinToScore = 0xaa07,
    ScoreToCoin = 0xaa08,
    ExchangeEnd = 0xaa09,
    ServerMsg = 0xaa10, //中奖通告
    ServerHallMsg = 0xaa11,
    PlayerMsg = 0xaa12,
    Version = 0xaa0a,
    ChangePwd = 0xaa0b,
    HeartBeat = 0xaa0c,
    ChangePortrait = 0xaa0d,
    Notice = 0xaa0e,
    ForceLogout = 0xaa0f,

    BetPrepare = 0xaa30,
    BetCountDown = 0xaa31,
    BetRequire = 0xaa32,
    BetResult = 0xaa33,
    BetBet = 0xaa34,
    BetEnd = 0xaa35,
    BetCancel = 0x0aa36,
    BetContinue = 0xaa37,
    BetBankerCountDown = 0xaa3a,
    BetBankerRequire = 0xaa3b,
    BetBankerResult = 0xaa3c,
    BetHistoryCard = 0xaa3d,

    FishFire = 0xaa60,
    FishCollide = 0xaa61,
    FishAddFish = 0xaa62,
    FishPreChange = 0xaa63,
    FishChangeScene = 0xaa64,
    FishArrayEnd = 0xaa65,
    FishEnergyGun = 0xaa66,
    Fish30sStart = 0xaa67,
    Fish30sFire = 0xaa68,
    Fish30sEnd = 0xaa69,
    FishPreBoss = 0xaa6a,
    FishChangeBoss = 0xaa6b,

    OnceCard = 0xaa90,
    TwiceCard = 0xaa91,
    DouType = 0xaa92,
    DouOpen = 0xaa93,
    WinScore = 0xaa94,
    PokerCur = 0xaa95,
    LogoutKeep = 0xaa96,
    PrizeTop = 0xaa97,

    MSG_LOGIN_WECHAT = 0xAA12,
    MSG_LOGON_VIDEOGAME = 0xaa13,
    MSG_LOGOUT_VIDEOGAME = 0xaa14,
    OnlinePeople = 0xaa20,
    GAMEPORT = 0xAA15,
    CashOut = 0xAA17,
    MSG_Coin_ChANFE = 0xAA21,//充值到账 刷新

    MSG_UPDATE_TELEPHONE = 0xAA40,


    Reconnect = 0xBA01,         //小游戏重连
    UpdateCoint = 0xCA00,    //单独更新金币

    GET_KEY_VALUE = 0xCA01,    //获取键值对配置
    Update_Bind_Info = 0xCA02,    //更新绑定兑换信息
    Get_Bind_Info = 0xCA03,    //获取绑定兑换信息
    NEWCASH_OUT = 0xCA04,      //请求新的兑换

    Get_Duihuan_Jilu = 0xCA05,       //获取兑换记录
    Get_Chongzhi_Jilu = 0xCA06,		//获取充值记录
}

public enum LoginStatus
{
    Idle = 0,
    Logining,
    Timeout,
    Success,
    UserNameInvalid,
    UserPasswordError,
    VersionError,
    UserFreezed,
    YijingDenglu,
}

/// <summary>
/// 重连状态
/// </summary>
public enum ReconnectLoginStatus
{
    Idle = 0,//闲置
    Logining,//正在重连
    Timeout,//重连超时
    Success,//重连成功
}

public enum RegisterStatus
{
    Success,
    IdOccupied,
    NicknameOccupied,
    ClientError,
    ServerError,
}

public enum PlayerMsgType
{
    Text,
    Voice,
    TextVoice,
    Picture,
}

#if NWCT
public enum GameType
{
    CheBiao = 0,
    ShangHaiSaiChe,
    TouWang,
    DanTiao,
    HuanLeNiuNiu,
    FeiQinZouShou,
    BaiJiaLe,
    //ATT,
    YuLeQuan,

    /// <summary>
    /// 龙太子
    /// </summary>
    LongTaiZi = 9110,

    /// <summary>
    /// 捕鱼大亨
    /// </summary>
    BuYuDaHeng = 9301,
    /// <summary>
    /// 奔驰宝马  
    /// </summary>
    BenChiBaoMa = 9100,
    /// <summary>
    /// 极速飞车
    /// </summary>
    JiSuFeiChe = 9108,
    /// <summary>
    /// 骰宝
    /// </summary>
    TouBao = 9101,
    /// <summary>
    /// 单挑
    /// </summary>
    CaiJinDanTiao = 9102,
    /// <summary>
    /// 幸运六狮
    /// </summary>
    XingYunLiuShi = 9103,
    /// <summary>
    /// 百人牛牛
    /// </summary>
    BaiRenNiuNiu = 9104,
    /// <summary>
    /// 金鲨银鲨
    /// </summary>
    JinShaYinSha = 9109,
    /// <summary>
    /// 火凤凰
    /// </summary>
    HuoFengHuang = 9200,
    /// <summary>
    /// ATT连环炮
    /// </summary>
    ATT = 9201,
    /// <summary>
    /// 人鱼传说
    /// </summary>
    RenYuChuanShuo = 9202,
    /// <summary>
    /// 金皇冠
    /// </summary>
    JinHuangGuan = 9203,
    /// <summary>
    /// ATT至尊
    /// </summary>
    ATTZhiZun = 9205,
    /// <summary>
    /// ATT至尊2
    /// </summary>
    ChaoJiZhiZun2 = 9207,
    /// <summary>
    /// ATT3
    /// </summary>
    ATT3 = 20501,

    /// <summary>
    /// NBA
    /// </summary>
    NBA = 9209,
    /// <summary>
    /// ATT满花版
    /// </summary>
    ATTManHuaBan = 9206,
    /// <summary>
    /// 大字板
    /// </summary>
    DaZiBan = 9204,
    /// <summary>
    /// 金蟾捕鱼
    /// </summary>
    JinChanBuYu = 9300,
    /// <summary>
    /// 八爪鱼
    /// </summary>
    BaZhuaYu = 9319,
    /// <summary>
    /// 捕鱼达人
    /// </summary>
    DaRenBuYu = 9999,
    /// <summary>
    /// 史前巨鳄
    /// </summary>
    ShiQianJuE = 9316,
    /// <summary>
    /// 钻石大亨
    /// </summary>
    ZuanShiDaHeng = 9313,
    /// <summary>
    /// 牛魔王
    /// </summary>
    NiuMoWang = 9302,
    /// <summary>
    /// 三色鳄鱼
    /// </summary>
    SanSeEYu = 9303,
    /// <summary>
    /// 3D猎鸟
    /// </summary>
    SanDLieNiao = 9304,
    /// <summary>
    /// 李逵劈鱼
    /// </summary>
    LiKuiPiYu = 9305,
    /// <summary>
    /// 摇钱树
    /// </summary>
    YaoQianShu = 9307,
    /// <summary>
    /// 海王2
    /// </summary>
    HaiWang2 = 9306,
    /// <summary>
    /// 双响金龙
    /// </summary>
    ShuangXiangLong = 9309,
    /// <summary>
    /// 双响企鹅
    /// </summary>
    ShuangXiangQiE = 9317,
    /// <summary>
    /// 空战英豪
    /// </summary>
    KongZhanYingHao = 9310,
    /// <summary>
    /// 八鲨闹海
    /// </summary>
    BaShaNaoHai = 9314,
    /// <summary>
    /// 火麒麟
    /// </summary>
    HuoQiLin = 9311,
    /// <summary>
    /// 铁甲飞龙
    /// </summary>
    TieJiaFeiLong = 9312,
    /// <summary>
    /// 神龙宝藏
    /// </summary>
    ShenLongBaoZang = 9315,
    /// <summary>
    /// 娱乐红蟹
    /// </summary>
    YuLeHongXie = 9318,
    /// <summary>
    /// 西游争霸
    /// </summary>
    XiYouZhengBa = 9106,
    /// <summary>
    /// 三色龙
    /// </summary>
    SanSeLong = 9107,
    /// <summary>
    /// 大厅
    /// </summary>
    DaTingG178 = 20500,
    /// <summary>
    /// 3D捕鱼
    /// </summary>
    Fish3D = 9320,
    /// <summary>
    /// 糖果派对
    /// </summary>
    Candy = 9500,
    /// <summary>
    /// 水果拉霸
    /// </summary>
    SlotsFruit = 9400,
    /// <summary>
    /// 财神发发发
    /// </summary>
    Shz = 9401,
    /// <summary>
    /// 大兵小将
    /// </summary>
    DBXJ = 9210
}


#else
public enum GameType
{
    CheBiao = 0,
    ShangHaiSaiChe,
    TouWang,
    DanTiao,
    HuanLeNiuNiu,
    FeiQinZouShou,
    BaiJiaLe,
    //ATT,
    YuLeQuan,

    /// <summary>
    /// 龙太子
    /// </summary>
    LongTaiZi = 9110,

    /// <summary>
    /// 捕鱼大亨
    /// </summary>
    BuYuDaHeng = 9301,
    /// <summary>
    /// 奔驰宝马  
    /// </summary>
    BenChiBaoMa = 9100,
    /// <summary>
    /// 极速飞车
    /// </summary>
    JiSuFeiChe = 9108,
    /// <summary>
    /// 骰宝
    /// </summary>
    TouBao = 9101,
    /// <summary>
    /// 单挑
    /// </summary>
    CaiJinDanTiao = 9102,
    /// <summary>
    /// 幸运六狮
    /// </summary>
    XingYunLiuShi = 9103,
    /// <summary>
    /// 百人牛牛
    /// </summary>
    BaiRenNiuNiu = 9104,
    /// <summary>
    /// 金鲨银鲨
    /// </summary>
    JinShaYinSha = 9109,
    /// <summary>
    /// 火凤凰
    /// </summary>
    HuoFengHuang = 9200,
    /// <summary>
    /// ATT连环炮
    /// </summary>
    ATT = 9201,
    /// <summary>
    /// 人鱼传说
    /// </summary>
    RenYuChuanShuo = 9202,
    /// <summary>
    /// 金皇冠
    /// </summary>
    JinHuangGuan = 9203,
    /// <summary>
    /// ATT至尊
    /// </summary>
    ATTZhiZun = 9205,
    /// <summary>
    /// ATT至尊2
    /// </summary>
    ChaoJiZhiZun2 = 9207,
    /// <summary>
    /// ATT3
    /// </summary>
    ATT3 = 9208,

    /// <summary>
    /// NBA
    /// </summary>
    NBA = 9209,
    /// <summary>
    /// ATT满花版
    /// </summary>
    ATTManHuaBan = 9206,
    /// <summary>
    /// 大字板
    /// </summary>
    DaZiBan = 9204,
    /// <summary>
    /// 金蟾捕鱼
    /// </summary>
    JinChanBuYu = 9300,
    /// <summary>
    /// 八爪鱼
    /// </summary>
    BaZhuaYu = 9319,
    /// <summary>
    /// 捕鱼达人
    /// </summary>
    DaRenBuYu = 9999,
    /// <summary>
    /// 史前巨鳄
    /// </summary>
    ShiQianJuE = 9316,
    /// <summary>
    /// 钻石大亨
    /// </summary>
    ZuanShiDaHeng = 9313,
    /// <summary>
    /// 牛魔王
    /// </summary>
    NiuMoWang = 9302,
    /// <summary>
    /// 三色鳄鱼
    /// </summary>
    SanSeEYu = 9303,
    /// <summary>
    /// 3D猎鸟
    /// </summary>
    SanDLieNiao = 9304,
    /// <summary>
    /// 李逵劈鱼
    /// </summary>
    LiKuiPiYu = 9305,
    /// <summary>
    /// 摇钱树
    /// </summary>
    YaoQianShu = 9307,
    /// <summary>
    /// 海王2
    /// </summary>
    HaiWang2 = 9306,
    /// <summary>
    /// 双响金龙
    /// </summary>
    ShuangXiangLong = 9309,
    /// <summary>
    /// 双响企鹅
    /// </summary>
    ShuangXiangQiE = 9317,
    /// <summary>
    /// 空战英豪
    /// </summary>
    KongZhanYingHao = 9310,
    /// <summary>
    /// 八鲨闹海
    /// </summary>
    BaShaNaoHai = 9314,
    /// <summary>
    /// 火麒麟
    /// </summary>
    HuoQiLin = 9311,
    /// <summary>
    /// 铁甲飞龙
    /// </summary>
    TieJiaFeiLong = 9312,
    /// <summary>
    /// 神龙宝藏
    /// </summary>
    ShenLongBaoZang = 9315,
    /// <summary>
    /// 娱乐红蟹
    /// </summary>
    YuLeHongXie = 9318,
    /// <summary>
    /// 西游争霸
    /// </summary>
    XiYouZhengBa = 9106,
    /// <summary>
    /// 三色龙
    /// </summary>
    SanSeLong = 9107,
    /// <summary>
    /// 大厅
    /// </summary>
    DaTingG178 = 9000,
    /// <summary>
    /// 3D捕鱼
    /// </summary>
    Fish3D = 9320,
    /// <summary>
    /// 糖果派对
    /// </summary>
    Candy = 9500,
    /// <summary>
    /// 水果拉霸
    /// </summary>
    SlotsFruit = 9400,
    /// <summary>
    /// 财神发发发
    /// </summary>
    Shz = 9401,
    /// <summary>
    /// 大兵小将
    /// </summary>
    DBXJ = 9210
}


#endif

public static class GlobalDef
{
    public static Dictionary<string, string[]> hallName = new Dictionary<string, string[]>();
    public static bool hasDealCommandInstance = false;

    static GlobalDef()
    {
        hallName.Add(GameType.ZuanShiDaHeng.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.SanSeEYu.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.JinChanBuYu.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.YaoQianShu.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.NiuMoWang.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.LiKuiPiYu.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.SanDLieNiao.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.HuoFengHuang.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.RenYuChuanShuo.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.JinHuangGuan.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.BaZhuaYu.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.BuYuDaHeng.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.KongZhanYingHao.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.ShuangXiangLong.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.HuoQiLin.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.BaShaNaoHai.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.DaRenBuYu.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.ShiQianJuE.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.HaiWang2.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.ShuangXiangQiE.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.TieJiaFeiLong.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.YuLeQuan.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.ShenLongBaoZang.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.YuLeHongXie.ToString(), new string[] { "娱乐场", "普通场", "VIP场" });
        hallName.Add(GameType.ATT.ToString(), new string[] { "娱乐场", "普通场", "高级场", "VIP场" });

        hallName.Add(GameType.ATT3.ToString(), new string[] { "1分机", "2分机", "5分机", "1角机" });
        hallName.Add(GameType.ATT3.ToString() + "_JQ", new string[] { "1币100分", "1币50分", "1币20分", "1币10分" });


        hallName.Add(GameType.ATTZhiZun.ToString(), new string[] { "娱乐场", "普通场", "高级场", "VIP场" });
        //hallName.Add(GameType.YLM.ToString(), new string[] { "[房间1]", "[房间2]", "[房间3]", "[房间4]", "[房间5]", "[房间6]" });
        hallName.Add(GameType.NBA.ToString(), new string[] { "娱乐场", "普通场", "高级场", "VIP场" });
        hallName.Add(GameType.ChaoJiZhiZun2.ToString(), new string[] { "娱乐场", "普通场", "高级场", "VIP场" });
        hallName.Add(GameType.ATTManHuaBan.ToString(), new string[] { "娱乐场", "普通场", "高级场", "VIP场" });
        hallName.Add(GameType.DaZiBan.ToString(), new string[] { "娱乐场", "普通场", "高级场", "VIP场" });
        hallName.Add(GameType.DBXJ.ToString(), new string[] { "[房间1]", "[房间2]", "[房间3]", "[房间4]", "[房间5]", "[房间6]" });


        hallName.Add(GameType.JinShaYinSha.ToString(), new string[] { "1分机", "2分机", "5分机", "VIP场" });
    }
}

public class HallData
{
    public static Dictionary<string, int> version = new Dictionary<string, int>();
    public static Dictionary<string, string> url = new Dictionary<string, string>();
    public static Dictionary<string, bool> gamePremission = new Dictionary<string, bool>();
    public static Dictionary<string, int> gamePlayers = new Dictionary<string, int>();

    public static Dictionary<string, string> gameKeyvalueConfig = new Dictionary<string, string>();

    public static string notice = "";
    public static int login_times = 0;
    public static bool isError = false;

    public static ushort hallPort = Convert.ToUInt16(GameType.DaTingG178);
    public static Network network = new Network();
    protected static byte[] mCmdBuf = new byte[256];

    public static Action onLogin;
    public static Action onVersion;
    public static Action onNotice;
    public static Action<bool> onChangePwd;
    public static Action<bool> onChangePortrait;
    public static Action<RegisterStatus> onRegister;
    public static Action onHeartBeat;
    public static Action<string> onServerMsg;
    public static Action<string> onServerHallMsg;
    public static Action onlinePeople;
    public static Action cashCoin;
    public static Action act;
    public static Action onIphone;
    public static Action onGetBindInfo;
    public static Action<List<Duihuanxinxi>> onGetDuihuanJilu;
    public static Action<List<Chongzhixinxi>> onGetChongzhiJilu;
    static HallData()
    {
        network.connected += () => { Debug.Log("connected to " + network.ipAddress); };
        network.disconnected += () => { Debug.Log("disconnected " + network.ipAddress); };
        network.error += (Exception e) => { isError = true; network.Disconnect(0x00, e.Message); };

        GameType t = new GameType();
        string[] s = Enum.GetNames(t.GetType());
        for (int i = 0; i < s.Length; i++)
        {
            gamePremission.Add(s[i], true);
            gamePlayers.Add(s[i], 0);
        }
    }

    /// <summary>
    /// 普通账号登录
    /// </summary>
    /// <returns></returns>
    public static IEnumerator CLoginHall()
    {
        byte[] buf = new byte[100];
        ushort cnt = 0;

        if (GameData.login_hall != LoginStatus.Idle)
        {
            yield break;
        }
        GameData.login_hall = LoginStatus.Logining;

        if (network.status != Network.State.Connected)
        {
            network.Connect(hallPort);
        }

        while (network.status == Network.State.Connecting)
        {
            yield return null;
        }

        if (network.status == Network.State.Disconnect)
        {
            GameData.login_hall = LoginStatus.Timeout;
            yield break;
        }

        cnt = 0;

        Network.StringToUc(GameData.id, buf, ref cnt);
        Network.StringToUc("g178", buf, ref cnt);
        Network.StringToUc(GameData.password, buf, ref cnt);
        if (Application.platform == RuntimePlatform.WindowsPlayer || Application.platform == RuntimePlatform.WindowsEditor)
        {
            buf[cnt++] = 2;
        }
        else if (Application.platform == RuntimePlatform.Android)
        {
            buf[cnt++] = 0;
        }
        else if (Application.platform == RuntimePlatform.IPhonePlayer)
        {
            buf[cnt++] = 1;
        }
        //Network.S4ToUc(int.Parse(HallVersion.version), buf, ref cnt);
        network.SendCommand(CmdType.Login, buf, cnt);
        Debug.Log("Send = " + CmdType.Login);
    }

    /// <summary>
    /// 微信账号登录
    /// </summary>
    /// <returns></returns>
    public static IEnumerator WechatLoginHall(string code)
    {
        byte[] buf = new byte[100];
        ushort cnt = 0;

        if (GameData.login_hall != LoginStatus.Idle)
        {
            yield break;
        }
        GameData.login_hall = LoginStatus.Logining;

        if (network.status != Network.State.Connected)
        {
            network.Connect(hallPort);
        }

        while (network.status == Network.State.Connecting)
        {
            yield return null;
        }

        if (network.status == Network.State.Disconnect)
        {
            GameData.login_hall = LoginStatus.Timeout;
            yield break;
        }

        cnt = 0;
        Network.StringToUc(code, buf, ref cnt);
        if (Application.platform == RuntimePlatform.WindowsPlayer || Application.platform == RuntimePlatform.WindowsEditor)
        {
            buf[cnt++] = 2;
        }
        else if (Application.platform == RuntimePlatform.Android)
        {
            buf[cnt++] = 0;
        }
        else if (Application.platform == RuntimePlatform.IPhonePlayer)
        {
            buf[cnt++] = 1;
        }
        //Network.S4ToUc(int.Parse(HallVersion.version), buf, ref cnt);
        network.SendCommand(CmdType.MSG_LOGIN_WECHAT, buf, cnt);
        Debug.Log("Send = " + CmdType.MSG_LOGIN_WECHAT);
    }

    public static bool SendLoginHall()
    {
        byte[] buf = new byte[120];
        ushort cnt = 0;

        Socket socket = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
        socket.ReceiveTimeout = 5000;
        socket.Connect(Network.ip, hallPort);

        cnt = 0;

        Network.StringToUc(GameData.id, buf, ref cnt);
        Network.StringToUc(GameData.password, buf, ref cnt);
        if (Application.platform == RuntimePlatform.WindowsPlayer || Application.platform == RuntimePlatform.WindowsEditor)
        {
            buf[cnt++] = 2;
        }
        else if (Application.platform == RuntimePlatform.Android)
        {
            buf[cnt++] = 0;
        }
        else if (Application.platform == RuntimePlatform.IPhonePlayer)
        {
            buf[cnt++] = 1;
        }
        //Network.S4ToUc(int.Parse(HallVersion.version), buf, ref cnt);

        byte[] buffer = Network.CmdPack((ushort)CmdType.Login, buf, cnt);
        if (socket.Connected) socket.Send(buffer);
        Debug.LogWarning("Send = " + CmdType.Login);

        buffer = new byte[1500];
        bool ret = false;
        int recCnt = socket.Receive(buffer);
        if (recCnt > 4)
        {
            if (buffer[4] == 0)
            {
                ret = true;
            }
            else
            {
                ret = false;
                network.Disconnect();
                GameData.login_hall = LoginStatus.Idle;
                SceneManager.LoadScene(Network.daTing);
                //SceneManager.LoadScene("DaTingG_xingli");
            }
        }
        socket.Shutdown(SocketShutdown.Both);
        socket.Close();
        return ret;
    }

    //屏蔽 从服务器请求版本信息
    //public static void SendGetVersion()
    //{
    //    byte[] buf = new byte[10];
    //    ushort cnt = 0;

    //    if (Application.platform == RuntimePlatform.WindowsPlayer || Application.platform == RuntimePlatform.WindowsEditor)
    //    {
    //        buf[cnt++] = 2;
    //    }
    //    else if (Application.platform == RuntimePlatform.Android)
    //    {
    //        buf[cnt++] = 0;
    //    }
    //    else if (Application.platform == RuntimePlatform.IPhonePlayer)
    //    {
    //        buf[cnt++] = 1;
    //    }
    //    //network.SendCommand(CmdType.Version, buf, cnt);
    //    //Debug.LogWarning("Send = " + CmdType.Version);
    //}

    public static void SendGetNotice()
    {
        byte[] buf = new byte[100];
        ushort cnt = 0;

        network.SendCommand(CmdType.Notice, buf, cnt);
        Debug.LogWarning("Send = " + CmdType.Notice);
    }

    public static void SendGetKeyvalConfig()
    {
        byte[] buf = new byte[10];
        ushort cnt = 0;
        network.SendCommand(CmdType.GET_KEY_VALUE, buf, cnt);
    }


    public static void LoginVedioGame()
    {
        byte[] buf = new byte[10];
        ushort cnt = 0;
        network.SendCommand(CmdType.MSG_LOGON_VIDEOGAME, buf, cnt);
    }

    public static void VideoGameOut()
    {
        byte[] buf = new byte[10];
        ushort cnt = 0;
        network.SendCommand(CmdType.MSG_LOGOUT_VIDEOGAME, buf, cnt);
    }

    public static void SendGet_Bind_Info()
    {
        byte[] buf = new byte[10];
        ushort cnt = 0;
        network.SendCommand(CmdType.Get_Bind_Info, buf, cnt);
    }

    public static void SendGet_Duihuan_Jilu()
    {
        byte[] buf = new byte[10];
        ushort cnt = 0;
        network.SendCommand(CmdType.Get_Duihuan_Jilu, buf, cnt);
    }

    public static void SendGet_Chongzhi_Jilu()
    {
        byte[] buf = new byte[10];
        ushort cnt = 0;
        network.SendCommand(CmdType.Get_Chongzhi_Jilu, buf, cnt);
    }


    public static void SendChangePassword(string oldPwd, string newPwd)
    {
        byte[] buf = new byte[100];
        ushort cnt = 0;

        Network.StringToUc(GameData.id, buf, ref cnt);
        Network.StringToUc(oldPwd, buf, ref cnt);
        Network.StringToUc(newPwd, buf, ref cnt);
        network.SendCommand(CmdType.ChangePwd, buf, cnt);
        Debug.LogWarning("Send = " + CmdType.ChangePwd);
    }

    public static void SendChangePortrait(int portrait)
    {
        byte[] buf = new byte[100];
        ushort cnt = 0;

        GameData.portrait = portrait;
        Network.StringToUc(GameData.id, buf, ref cnt);
        Network.StringToUc(GameData.password, buf, ref cnt);
        buf[cnt++] = (byte)portrait;
        network.SendCommand(CmdType.ChangePortrait, buf, cnt);
        Debug.LogWarning("Send = " + CmdType.ChangePortrait);
        Debug.LogWarning("GameData.id:"+ GameData.id);
        Debug.LogWarning("GameData.password:" + GameData.password);
        Debug.LogWarning("portrait:" + portrait);

    }

    public static IEnumerator CSendRegister(string id, string nickname, string pwd, string client)
    {
        byte[] buf = new byte[100];
        ushort cnt = 0;

        if (network.status != Network.State.Connected)
        {
            network.Connect(hallPort);
        }

        while (network.status == Network.State.Connecting)
        {
            yield return null;
        }

        if (network.status == Network.State.Disconnect)
        {
            yield break;
        }

        Network.StringToUc(id, buf, ref cnt);
        Network.StringToUc(nickname, buf, ref cnt);
        Network.StringToUc(pwd, buf, ref cnt);
        Network.StringToUc(client, buf, ref cnt);
        network.SendCommand(CmdType.Regist, buf, cnt);
        Debug.LogWarning("Send = " + CmdType.Regist);
    }

    public static IEnumerator CSendTeleIphone(string iphone)
    {
        byte[] buf = new byte[100];
        ushort cnt = 0;
        if (network.status != Network.State.Connected)
        {
            network.Connect(hallPort);
        }

        while (network.status == Network.State.Connecting)
        {
            yield return null;
        }

        if (network.status == Network.State.Disconnect)
        {
            yield break;
        }
        Network.StringToUc(GameData.id, buf, ref cnt);
        Network.StringToUc(iphone, buf, ref cnt);
        network.SendCommand(CmdType.MSG_UPDATE_TELEPHONE, buf, cnt);
        Debug.Log("Send = " + CmdType.MSG_UPDATE_TELEPHONE);

    }

    public static IEnumerator CSendGetVersion()
    {
        byte[] buf = new byte[100];
        ushort cnt = 0;

        if (network.status != Network.State.Connected)
        {
            network.Connect(hallPort);
        }

        while (network.status == Network.State.Connecting)
        {
            yield return null;
        }

        if (network.status == Network.State.Disconnect)
        {
            yield break;
        }

        if (Application.platform == RuntimePlatform.WindowsPlayer || Application.platform == RuntimePlatform.WindowsEditor)
        {
            buf[cnt++] = 2;
        }
        else if (Application.platform == RuntimePlatform.Android)
        {
            buf[cnt++] = 0;
        }
        else if (Application.platform == RuntimePlatform.IPhonePlayer)
        {
            buf[cnt++] = 1;
        }
        //network.SendCommand(CmdType.Version, buf, cnt);
        //Debug.LogWarning("Send = " + CmdType.Version);
    }

    public static IEnumerator CSendChangePassword(string oldPwd, string newPwd)
    {
        byte[] buf = new byte[100];
        ushort cnt = 0;

        if (network.status != Network.State.Connected)
        {
            network.Connect(hallPort);
        }

        while (network.status == Network.State.Connecting)
        {
            yield return null;
        }

        if (network.status == Network.State.Disconnect)
        {
            yield break;
        }

        Network.StringToUc(GameData.id, buf, ref cnt);
        Network.StringToUc(oldPwd, buf, ref cnt);
        Network.StringToUc(newPwd, buf, ref cnt);
        network.SendCommand(CmdType.ChangePwd, buf, cnt);
        //Debug.LogWarning("Send = " + CmdType.ChangePwd);
    }

    public static void SendHeartBeat(int time)
    {
        byte[] buf = new byte[10];
        ushort cnt = 0;

        Network.S4ToUc(time, buf, ref cnt);
        network.SendCommand(CmdType.HeartBeat, buf, cnt);
    }

    /// <summary>
    /// 发送心跳消息
    /// </summary>
    public static void SendHeartBeat()
    {
        byte[] buf = new byte[10];
        ushort cnt = 0;

        network.SendCommand(CmdType.HeartBeat, buf, cnt);
    }

    /// <summary>
    /// 获得子游戏 端口port
    /// </summary>
    /// <param name="gameName">子游戏名字</param>
    public static void SendGamePort(string gameName, Action<bool> action)
    {
        //锁定触发 3s
        ViewMgr.Get.NetOut(3f, () =>
        {
            ViewMgr.Get.NetOut(0.1f, "暂未开启");
        });
        //获取子游戏端口
        GameData.port = GlobalCommon.GetPort(gameName);
        if (GameData.port == 0)
        {
            Debug.Log(string.Format("未找到{0}的端口", gameName));
            return;
        }
        action(true);
    }

    public static IEnumerator CDownloadPicture(string url, string savePath = null, Action<Texture2D> complete = null)
    {
        WWW www = new WWW(url);
        yield return www;

        Texture2D tex = www.texture;

        if (savePath != null)
        {
            byte[] bytes = tex.EncodeToPNG();
            File.WriteAllBytes(savePath, bytes);
        }

        if (complete != null) complete(tex);
    }

    public static void ClearDelegate()
    {
        onLogin = null;
        onVersion = null;
        onNotice = null;
        onChangePwd = null;
        onChangePortrait = null;
        onRegister = null;
        onHeartBeat = null;
        onServerMsg = null;
        onlinePeople = null;
        cashCoin = null;
        act = null;
        onIphone = null;
    }

    public static void ApplicationQuit()
    {
        ClearDelegate();
        network.Disconnect();
    }
}

public enum GameModel
{
    金币版,
    真钱版
}



public struct Duihuanxinxi
{
    public string shijian { get; set; }
    public int fangshi { get; set; }
    public int shuliang { get; set; }
    public string xiaoxi { get; set; }
    public int zhuangtai { get; set; }
}

public struct Chongzhixinxi
{
    public string shijian { get; set; }
    public int fangshi { get; set; }
    public int shuliang { get; set; }
    public string xiaoxi { get; set; }
    public int zhuangtai { get; set; }
}

public class GameData
{
    //add by yuanshijin 断线续玩服务端下发的压分值和当前背景索引
    public static ushort curBetValue = 0;
    //public static ushort curBackgroundIndex = 0;

    public static string id = "test";
    //public static string id = "cs001";
    public static string iphone = "";
    public static int iRet = -1;
    public static string password = "123456";
    public static int weixinDL = 1;//是否绑定了代理信息 0未绑定/1绑定了
    public static int userID = 88888888;
    public static string nickname = "";
    public static int portrait;
    public static int loginType = 0;//登录类型 0普通登录/1微信登录/2qq登录
    public static string headImage = string.Empty;//微信头像地址
    public static string headUrl;
    public static Sprite headImg;
    public static int Sex;//性别 1男/2女
    public static int roomNumber;
    public static string ip;
    public static ushort port;
    public static bool isRobot;
    public static long coin;
    public static int onlinePeople;
    public static int onlinePlayer = 0;
    public static Network network = new Network();
    public static GameType gameType;


    public static string WXZhanghao;
    public static string WXXingming;
    public static string ZFBZhanghao;
    public static string ZFBXingming;
    public static string YHKZhanghao;
    public static string YHKXingming;
    public static string YHKMingcheng;
    public static string YHKZhihang;
    public static string XNBDizhi;
    public static string XNBXingming;



    public static bool shouyeDengluguo { get; set; }



    public static LoginStatus login_hall { get; set; }
    static LoginStatus mLoginGame;


    public static LoginStatus login_game
    {
        get { return mLoginGame; }
        set
        {
            mLoginGame = value;
            Debug.Log("LoginGame = " + value);
        }
    }
    /// <summary>
    /// 重连状态
    /// </summary>
    public static ReconnectLoginStatus login_gameReconnect { get; set; }
    protected static byte[] mCmdBuf = new byte[256];
    public static GameModel gameModel = GameModel.金币版;

    static bool mInit = false;

    static bool mBgm;
    public static bool bgm
    {
        get { Init(); return mBgm; }
        set
        {
            mBgm = value;
            bgmVolume = mBgm ? 1 : 0;
            PlayerPrefs.SetInt("bgm", mBgm ? 1 : 0);
            if (onBGMChange != null)
            {
                onBGMChange(mBgm);
            }
        }
    }

    static bool mSe;
    public static bool se
    {
        get { Init(); return mSe; }
        set
        {
            mSe = value;
            seVolume = mSe ? 1 : 0;
            PlayerPrefs.SetInt("se", mSe ? 1 : 0);
        }
    }

    static float mBgmVolume;
    public static float bgmVolume
    {
        get { Init(); return mBgmVolume; }
        set
        {
            mBgmVolume = value;
            mBgm = mBgmVolume != 0;
            PlayerPrefs.SetFloat("bgmVolume", mBgmVolume);
        }
    }

    static float mSeVolume;
    public static float seVolume
    {
        get { Init(); return mSeVolume; }
        set
        {
            mSeVolume = value;
            mSe = mSeVolume != 0;
            PlayerPrefs.SetFloat("seVolume", mSeVolume);
        }
    }

    public static List<string> strList = new List<string>();
    public static List<int> countList = new List<int>();

    public static Dictionary<int, List<int>> historyPokerModeDic = new Dictionary<int, List<int>>();
    public static Dictionary<int, List<int>> historyPokerGuessDic = new Dictionary<int, List<int>>();
    public static Dictionary<int, List<int>> historyPokerCardDic = new Dictionary<int, List<int>>();
    public static Dictionary<int, List<int>> historyPokerBetDic = new Dictionary<int, List<int>>();
    static bool mScreenOn;
    public static bool screenOn { get { Init(); return mScreenOn; } set { mScreenOn = value; PlayerPrefs.SetInt("screenOn", mScreenOn ? 1 : 0); Screen.sleepTimeout = mScreenOn ? SleepTimeout.NeverSleep : SleepTimeout.SystemSetting; } }

    static void Init()
    {
        if (mInit) return;
        mInit = true;

        mBgm = PlayerPrefs.GetInt("bgm", 1) > 0;
        mSe = PlayerPrefs.GetInt("se", 1) > 0;
        mBgmVolume = PlayerPrefs.GetFloat("bgmVolume", 1.0f);
        mSeVolume = PlayerPrefs.GetFloat("seVolume", 1.0f);
        mScreenOn = PlayerPrefs.GetInt("screenOn", 1) > 0;
        Screen.sleepTimeout = mScreenOn ? SleepTimeout.NeverSleep : SleepTimeout.SystemSetting;
    }

    public static Action onLogin;
    public static Action onCoinToScore;
    public static Action onScoreToCoin;
    public static Action onExchangeEnd;
    public static Action onOffline;
    public static Action onHeartBeat;
    public static Action<string, PlayerMsgType, string> onPlayerMsg;
    public static Action<string> onServerMsg;
    public static Action<string> onServerHallMsg;
    public static Action<int> onForceLogout;
    public static Action<bool> onBGMChange;



    static GameData()
    {
        network.connected += () => { Debug.Log("connected to " + network.ipAddress); };
        network.disconnected += () => { Debug.Log("disconnected " + network.ipAddress); };
        network.error += (Exception e) => { };
    }


    public static IEnumerator CLoginGame(bool reconnect = false)
    {
        byte[] buf = new byte[100];
        ushort cnt = 0;

        gameType = (GameType)Enum.Parse(typeof(GameType), Application.loadedLevelName.Replace("Table", ""));
        string resName = Application.loadedLevelName.Replace("Table", "");

        while (GameData.login_hall != LoginStatus.Success)
        {
            yield return null;
        }

        if (GameData.login_game == LoginStatus.Logining) yield break;
        GameData.login_game = LoginStatus.Logining;

        //network.Connect((ushort)((int)gameType % 10000));
        //network.Connect(resName);

        //未连上 socket
        if (network.status != Network.State.Connected)
        {
            network.Connect(resName);
        }

        while (network.status == Network.State.Connecting)
        {
            yield return null;
        }

        if (network.status == Network.State.Disconnect)
        {
            Debug.Log("Disconnect break");
            yield break;
        }

        //yield return new WaitForSeconds(5f);

        Network.StringToUc(GameData.id, buf, ref cnt);
        Network.StringToUc(GameData.password, buf, ref cnt);
        buf[cnt++] = reconnect ? (byte)1 : (byte)0;
        Debug.Log("Send " + CmdType.Login);
        network.SendCommand(CmdType.Login, buf, cnt);

    }

    public static void SendGetTableStatus(int id)
    {
        byte[] buf = new byte[10];
        ushort cnt = 0;

        Network.U2ToUc((ushort)id, buf, ref cnt);
        network.SendCommand(CmdType.ReqTable, buf, cnt);
    }

    public static void SendSetSitDown(int table, int seat)
    {
        Debug.Log(" ====== SendSetSitDown ====== ");

        if (seat >= 6)
        {
            Network.isWatch = true;
        }
        else
        {
            Network.isWatch = false;
        }

        byte[] buf = new byte[10];
        ushort cnt = 0;

        Network.U2ToUc((ushort)table, buf, ref cnt);
        buf[cnt++] = (byte)seat;
        network.SendCommand(CmdType.SitDown, buf, cnt);
    }

    public static void SendSetSitDown(int room, int table, int seat)
    {
        byte[] buf = new byte[10];
        ushort cnt = 0;

        buf[cnt++] = (byte)room;
        Network.U2ToUc((ushort)table, buf, ref cnt);
        buf[cnt++] = (byte)seat;
        network.SendCommand(CmdType.SitDown, buf, cnt);
    }

    public static void SendSetSitUp()
    {
        byte[] buf = new byte[10];
        ushort cnt = 0;

        network.SendCommand(CmdType.SitUp, buf, cnt);
    }

    public static void SendCoinToScore()
    {
        byte[] buf = new byte[10];
        ushort cnt = 0;

        buf[cnt++] = 100;
        network.SendCommand(CmdType.CoinToScore, buf, cnt);
    }

    public static void SendScoreToCoin()
    {
        byte[] buf = new byte[10];
        ushort cnt = 0;

        buf[cnt++] = 100;
        network.SendCommand(CmdType.ScoreToCoin, buf, cnt);
    }

    public static void SendExchangeEnd()
    {
        byte[] buf = new byte[10];
        ushort cnt = 0;

        network.SendCommand(CmdType.ExchangeEnd, buf, cnt);
    }

    public static void SendHeartBeat(int time)
    {
        byte[] buf = new byte[10];
        ushort cnt = 0;

        Network.S4ToUc(time, buf, ref cnt);
        network.SendCommand(CmdType.HeartBeat, buf, cnt);
    }

    public static int FindMin(int a, int b, int c)
    {
        return ((a < b ? a : b) < c) ? (a < b ? a : b) : c;
    }

    public static int FindMax(int a, int b)
    {
        return (a > b) ? a : b;
    }

    public static int FindMin(int a, int b)
    {
        return (a < b) ? a : b;
    }

    public static float FindMin(float a, float b)
    {
        return (a < b) ? a : b;
    }

    public static void ApplicationQuitBase()
    {
        onLogin = null;
        onCoinToScore = null;
        onScoreToCoin = null;
        onExchangeEnd = null;
        onHeartBeat = null;
        onPlayerMsg = null;
        onServerMsg = null;
        onServerHallMsg = null;
        onOffline = null;
        onForceLogout = null;
        network.Disconnect();
    }

}

public class ServerData
{
    public static int[] goodsCost = new int[6];
    public static int[] goodsPoint = new int[6];

    static ServerData()
    {
        goodsCost[0] = 500;
        goodsPoint[0] = 10;

        goodsCost[1] = 1000;
        goodsPoint[1] = 20;

        goodsCost[2] = 3000;
        goodsPoint[2] = 60;

        goodsCost[3] = 5000;
        goodsPoint[3] = 100;

        goodsCost[4] = 10000;
        goodsPoint[4] = 200;

        goodsCost[5] = 30000;
        goodsPoint[5] = 600;
    }
}

public class Network
{
    public static string ip =
#if NWCT
        "frp-mix.top";
#else
#if UNITY_EDITOR
         "**************"; 
#else
         "**************";
#endif
#endif
    public static string daTing = "DaTingG178";


    //是否为观战模式
    public static bool isWatch = false;

    public int threadSpeed = 50;
    public long lastSendTime { get; private set; }
    public static long seconds { get { return DateTime.Now.Ticks / 10000000; } }

    public string host;
    public static ushort port = Convert.ToUInt16(GameType.DaTingG178);

    private NetworkStream netStream = null;
    private TcpClient client = null;

    private Thread connector;
    private Thread readWorker = null;

    const int MaxReceive = 102400;
    private byte[] mReceiveBuf = new byte[MaxReceive];
    private byte[] mDecodeBuf = new byte[MaxReceive];
    private int mDecodeCnt = 0;
    private Queue mQueueCommand = Queue.Synchronized(new Queue());
    public static bool loopBack = false;

    public enum State
    {
        Disconnect,
        Connecting,
        Connected,
    }
    public State status { get; private set; }

    public string ipAddress { get; private set; }

    public int receiveByteCount = 0;

    public int lastErrorCode { get; private set; }

    public string disconnectMsg { get; private set; }

    #region Event

    public delegate void BasicEvent();

    public delegate void NetworkErrorEvent(Exception exception);

    public delegate void DirectRawNetworkMessageEvent();

    public delegate void PlayerConnectionEvent();

    public event BasicEvent connected
    {
        add { connectedInvoker += value; }
        remove { connectedInvoker -= value; }
    }
    BasicEvent connectedInvoker;

    public event BasicEvent disconnected
    {
        add { disconnectedInvoker += value; }
        remove { disconnectedInvoker -= value; }
    }
    BasicEvent disconnectedInvoker;

    public event BasicEvent serverDisconnected
    {
        add { serverDisconnectedInvoker += value; }
        remove { serverDisconnectedInvoker -= value; }
    }
    BasicEvent serverDisconnectedInvoker;

    public event BasicEvent timeoutDisconnected
    {
        add { timeoutDisconnectedInvoker += value; }
        remove { timeoutDisconnectedInvoker -= value; }
    }
    BasicEvent timeoutDisconnectedInvoker;

    public event NetworkErrorEvent error
    {
        add { errorInvoker += value; }
        remove { errorInvoker -= value; }
    }
    NetworkErrorEvent errorInvoker;

    #endregion

    public void ResetTime()
    {
        lastSendTime = seconds;
    }

    public Network()
    {
        lastSendTime = seconds;
    }

    ~Network() { Disconnect(); }

    void Init()
    {
        lastSendTime = seconds;
        mQueueCommand.Clear();
    }

    /// <summary>
    /// add by yuanshijin
    /// </summary>
    /// <param name="resName"></param>
    public void Connect(string resName)
    {
        ushort port = GameData.port;
        if (port == 0)
            return;

        Connect(port);
    }

    public void Connect(ushort port)
    {
        IPAddress a;
        if (!IPAddress.TryParse(ip, out a))
        {
            ip = Dns.GetHostEntry(ip).AddressList[0].ToString();
        }
        Connect(ip, port);
    }

    public void Connect(string hostAddress, ushort port)
    {
        ipAddress = hostAddress + ":" + port;
        Debug.Log("Try to connect " + ipAddress);

        status = State.Connecting;
        connector = new Thread(new ParameterizedThreadStart(ThreadedConnect));
        connector.Start(new object[] { hostAddress, port });
    }

    private void ThreadedConnect(object hostAndPort)
    {
        string hostAddress = (string)((object[])hostAndPort)[0];
        ushort port = (ushort)((object[])hostAndPort)[1];

        try
        {
            client = new TcpClient(hostAddress, (int)port);
            client.NoDelay = true;

            netStream = client.GetStream();
            readWorker = new Thread(new ThreadStart(ReadAsync));
            readWorker.Start();
            if (status != State.Connected)
            {
                status = State.Connected;
                if (connectedInvoker != null) connectedInvoker();
            }
        }
        catch (Exception e)
        {
            Debug.LogException(e);
            disconnectMsg = e.Message;
            status = State.Disconnect;
            if (errorInvoker != null) errorInvoker(e);
        }
    }

    public void Disconnect()
    {
        if (status != State.Disconnect)
        {
            if (disconnectedInvoker != null) disconnectedInvoker();
            status = State.Disconnect;
        }

        if (netStream != null) netStream.Close();
        if (client != null && client.Connected) client.Close();
        //if(readWorker != null) readWorker.Abort();
    }

    public void Disconnect(int code, string msg = "")
    {
        lastErrorCode = code;
        disconnectMsg = "与服务器断开连接，代码0x" + code.ToString("X2") + msg;
        Debug.Log(disconnectMsg);
        Disconnect();
    }

    public void TimeoutDisconnect()
    {

    }

    public bool CanWrite()
    {
        if (client == null || netStream == null || !netStream.CanWrite)
        {
            return false;
        }
        else
        {
            return true;
        }
    }

    public void Write(byte[] buf)
    {
        // 输出 16 进制数据到控制台  
        Debug.Log("发送数据:" + BitConverter.ToString(buf).Replace("-", " "));

        Write(buf, buf.Length);
    }

    public void Write(byte[] buf, int length)
    {

        ushort cnt = 0;
        if (UcToU2(buf, ref cnt) != (int)CmdType.HeartBeat)
        {
            lastSendTime = seconds;
        }
        if (loopBack) mQueueCommand.Enqueue(buf);

        if (CanWrite()) netStream.Write(buf, 0, length);
        else Disconnect(0x01);
    }

    private void ReadAsync()
    {
        try
        {
            while (true)
            {
                if (!netStream.CanRead) break;
                while (netStream.DataAvailable)
                {
                    int length = netStream.Read(mReceiveBuf, 0, MaxReceive);
                    receiveByteCount += length;


                    for (int i = 0; i < length; i++)
                    {
                        DecodeByte(mReceiveBuf[i], length);
                    }

                    if (mDecodeCnt >= 4)
                    {
                        int len = (mDecodeBuf[2] << 8) + mDecodeBuf[3];
                        if (len > length && length < 1024)
                        {

                            Debug.Log("接收到的长度小于命令需要的长度" + "ActureLen " + length + "RequireLen " + len + " Cmd " + ((int)mDecodeBuf[0] << 8 + mDecodeBuf[1]).ToString("X4"));
                        }
                    }
                }
                if (client.Client.Poll(20, SelectMode.SelectRead) && (client.Client.Available == 0) || !client.Client.Connected)
                {
                    Disconnect(0x00);
                    Debug.Log("ReadAsync,网络断了，client.Available=" + client.Client.Available.ToString());
                    break;
                }

                Thread.Sleep(threadSpeed);
            }
        }
        catch (SocketException e)
        {
            Debug.Log(e.ErrorCode);
            Debug.LogException(e);
            Disconnect();
        }
    }

    private void DecodeByte(byte elem, int length)
    {
        mDecodeBuf[mDecodeCnt++] = elem;

        if (mDecodeCnt >= 4)
        {
            int len = (mDecodeBuf[2] << 8) + mDecodeBuf[3];
            if (len + 4 <= mDecodeCnt)
            {
                ReceiveProcess(mDecodeBuf, mDecodeCnt);
                mDecodeCnt = 0;
            }
        }
    }

    private void ReceiveProcess(byte[] buf, int len)
    {
        byte[] store_buf = new byte[len];
        Array.Copy(buf, store_buf, len);
        mQueueCommand.Enqueue(store_buf);
        if (mQueueCommand.Count > 50)
        {  //最多存储50条消息记录
            mQueueCommand.Dequeue();       //超过后就删除最先接收到的消息
        }
    }

    public ushort GetCommand(ref byte[] buf)
    {
        if (mQueueCommand.Count > 0)
        {
            buf = (byte[])mQueueCommand.Dequeue();
            return (ushort)((buf[0] << 8) + buf[1]);
        }
        else
        {
            return 0;
        }
    }


    public static byte[] CmdPack(ushort cmd, byte[] buf, ushort len)
    {
        byte[] send_buf = new byte[len + 4];
        send_buf[0] = (byte)(cmd >> 8);
        send_buf[1] = (byte)cmd;
        send_buf[2] = (byte)(len >> 8);
        send_buf[3] = (byte)len;
        Array.Copy(buf, 0, send_buf, 4, len);

        return send_buf;
    }

    public void SendCommand(ushort cmd, byte[] buf, ushort len)
    {
        byte[] s_buf = CmdPack(cmd, buf, len);
        Write(s_buf);
    }

    public void SendCommand(CmdType cmd, byte[] buf, ushort len)
    {
        byte[] s_buf = CmdPack((ushort)cmd, buf, len);
        Write(s_buf);
    }

    public static ushort UcToU2(byte[] buf, ref ushort cnt)
    {
        ushort ret;

        ret = (ushort)((buf[cnt] << 8) + (buf[cnt + 1]));
        cnt += 2;
        return ret;
    }

    public static int UcToS4(byte[] buf, ref ushort cnt)
    {
        int ret;

        ret = (int)((buf[cnt] << 24) + (buf[cnt + 1] << 16) + (buf[cnt + 2] << 8) + (buf[cnt + 3]));
        cnt += 4;
        return ret;
    }

    public static uint UcToU4(byte[] buf, ref ushort cnt)
    {
        uint ret;

        ret = (uint)((buf[cnt] << 24) + (buf[cnt + 1] << 16) + (buf[cnt + 2] << 8) + (buf[cnt + 3]));
        cnt += 4;
        return ret;
    }

    public static long UcToS8(byte[] buf, ref ushort cnt)
    {
        long ret;

        ret = (((long)buf[cnt] << 56) +
            ((long)buf[cnt + 1] << 48) +
            ((long)buf[cnt + 2] << 40) +
            ((long)buf[cnt + 3] << 32) +
            ((long)buf[cnt + 4] << 24) +
            ((long)buf[cnt + 5] << 16) +
            ((long)buf[cnt + 6] << 8) +
            ((long)buf[cnt + 7]));
        cnt += 8;
        return ret;
    }

    public static void U2ToUc(ushort num, byte[] buf, ref ushort cnt)
    {
        buf[cnt] = (byte)((num >> 8) & 0xff);
        buf[cnt + 1] = (byte)(num & 0xff);
        cnt += 2;
    }

    public static void S4ToUc(int num, byte[] buf, ref ushort cnt)
    {
        buf[cnt] = (byte)((num >> 24) & 0xff);
        buf[cnt + 1] = (byte)((num >> 16) & 0xff);
        buf[cnt + 2] = (byte)((num >> 8) & 0xff);
        buf[cnt + 3] = (byte)(num & 0xff);
        cnt += 4;
    }

    public static string UcToString(byte[] buf, ref ushort cnt)
    {
        uint len = 0;
        int s = 0;
        byte c;

        do
        {
            c = buf[cnt];
            cnt++;

            uint x = (uint)(c & 0x7F);
            x <<= s;
            len += x;
            s += 7;
        }
        while ((c & 0x80) > 0);

        if (len == 0)
        {
            return "";
        }

        byte[] byteArray = new byte[len];

        for (int i = 0; i < len; i++)
        {
            byteArray[i] = buf[cnt];
            cnt++;
        }

        return Encoding.UTF8.GetString(byteArray);
    }

    public static void StringToUc(string str, byte[] buf, ref ushort cnt)
    {
        byte[] byteArray = Encoding.UTF8.GetBytes(str);
        uint len = (uint)byteArray.Length;

        do
        {
            byte c = (byte)(len & 0x7F);
            len >>= 7;
            if (len > 0)
            {
                c |= 0x80;
            }
            buf[cnt] = c;
            cnt++;
        }
        while (len > 0);

        for (int i = 0; i < byteArray.Length; i++)
        {
            buf[cnt] = byteArray[i];
            cnt++;
        }
    }

    public static long GetNowMillisecond()
    {
        System.DateTime mOld1970 = new DateTime(1970, 1, 1, 0, 0, 0);
        return (System.DateTime.Now.Ticks - mOld1970.Ticks) / 10000 % 100000;
    }

    public static string ChuliZhongjiangxiaoxi(string msg)
    {
        if (msg.StartsWith("恭喜玩家"))
        {
            var tmp1 = msg.IndexOf("【");
            var tmp2 = msg.IndexOf("】");

            var str = msg.Substring(tmp1 + 1, tmp2 - tmp1 - 1);

            var nicheng = ChuliNicheng(str);

            msg = msg.Replace(str, nicheng);
        }

        return msg;
    }

    public static string ChuliNicheng(string str)
    {
        if (str.Length == 0 || str.Length == 1)
        {
            return "*";
        }
        else if (str.Length == 2)
        {
            var tou = str.FirstOrDefault();
            return tou + "*";
        }
        else
        {
            var tou = str.FirstOrDefault();
            var wei = str.LastOrDefault();

            var strbuild = new StringBuilder();
            strbuild.Append(tou);
            for (int i = 0; i < str.Length; i++)
            {
                if (i < 4)
                {
                    strbuild.Append("*");
                }
                else
                {
                    break;
                }

            }
            strbuild.Append(wei);

            return strbuild.ToString();
        }

    }



    public static string ChineseStringToHex(string input)
    {
        byte[] bytes = Encoding.UTF8.GetBytes(input);
        StringBuilder hex = new StringBuilder(bytes.Length * 2);
        foreach (byte b in bytes)
        {
            hex.AppendFormat("{0:X2}", b);
        }

        Debug.Log("转换前：" + input);
        Debug.Log("转换后：" + hex.ToString());
        return hex.ToString();
    }


    public static string HexToChineseString(string hexString)
    {
        // 将16进制字符串转换为字节数组
        byte[] bytes = new byte[hexString.Length / 2];
        for (int i = 0; i < bytes.Length; i++)
        {
            bytes[i] = Convert.ToByte(hexString.Substring(i * 2, 2), 16);
        }

        // 使用UTF-8编码将字节数组解码为字符串
        var ret = Encoding.UTF8.GetString(bytes);

        Debug.Log("转换前：" + hexString);
        Debug.Log("转换后：" + ret);

        return ret;
    }
}

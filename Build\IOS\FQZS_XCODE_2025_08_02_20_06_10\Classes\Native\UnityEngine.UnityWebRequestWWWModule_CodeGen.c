﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.String UnityEngine.WWW::EscapeURL(System.String)
extern void WWW_EscapeURL_mBE31E4E0CF0AA05B8C1580AB2EDBDF9C24061C63 (void);
// 0x00000002 System.String UnityEngine.WWW::EscapeURL(System.String,System.Text.Encoding)
extern void WWW_EscapeURL_mE69323EB64D6AD3AF6B69952BC8EAFF8D98F281F (void);
// 0x00000003 System.String UnityEngine.WWW::UnEscapeURL(System.String)
extern void WWW_UnEscapeURL_m5CC786E81C413BBCA2DBA8C2A04C0718C43BD45E (void);
// 0x00000004 System.String UnityEngine.WWW::UnEscapeURL(System.String,System.Text.Encoding)
extern void WWW_UnEscapeURL_m6747F1EFD419272E2C1E3E4F09DC101261FF1A7C (void);
// 0x00000005 UnityEngine.WWW UnityEngine.WWW::LoadFromCacheOrDownload(System.String,System.Int32)
extern void WWW_LoadFromCacheOrDownload_m2772A637BC4DC15E8EDD35B1612ABAD8E3AE5490 (void);
// 0x00000006 UnityEngine.WWW UnityEngine.WWW::LoadFromCacheOrDownload(System.String,System.Int32,System.UInt32)
extern void WWW_LoadFromCacheOrDownload_mCD2F8D4B41C321DC873CBC0C3CBEFC2260A49E15 (void);
// 0x00000007 UnityEngine.WWW UnityEngine.WWW::LoadFromCacheOrDownload(System.String,UnityEngine.Hash128)
extern void WWW_LoadFromCacheOrDownload_mA03E177C70F7E3D4FB9F7CC71A45D636D9DCCC05 (void);
// 0x00000008 UnityEngine.WWW UnityEngine.WWW::LoadFromCacheOrDownload(System.String,UnityEngine.Hash128,System.UInt32)
extern void WWW_LoadFromCacheOrDownload_m70A95398FA8D5927C25DEE4A28331AB3A45FE678 (void);
// 0x00000009 UnityEngine.WWW UnityEngine.WWW::LoadFromCacheOrDownload(System.String,UnityEngine.CachedAssetBundle,System.UInt32)
extern void WWW_LoadFromCacheOrDownload_m56CE5DB86EF3ED96BE9786BC0C112478A745A33D (void);
// 0x0000000A System.Void UnityEngine.WWW::.ctor(System.String)
extern void WWW__ctor_mE77AD6C372CC76F48A893C5E2F91A81193A9F8C5 (void);
// 0x0000000B System.Void UnityEngine.WWW::.ctor(System.String,UnityEngine.WWWForm)
extern void WWW__ctor_m2F58987EB716A6D1B9B2425464E5C42FB6CF7DE6 (void);
// 0x0000000C System.Void UnityEngine.WWW::.ctor(System.String,System.Byte[])
extern void WWW__ctor_mFB9E9E1DE62E5826F643B25B3AB6BE33B681B0F4 (void);
// 0x0000000D System.Void UnityEngine.WWW::.ctor(System.String,System.Byte[],System.Collections.Generic.Dictionary`2<System.String,System.String>)
extern void WWW__ctor_m6686CBC878BB9EB28F4C4171C203CA4E3DE3656C (void);
// 0x0000000E System.Void UnityEngine.WWW::.ctor(System.String,System.String,UnityEngine.Hash128,System.UInt32)
extern void WWW__ctor_m443FACCAB63C95EB81924E1FF3B5ADE2D7875186 (void);
// 0x0000000F UnityEngine.AssetBundle UnityEngine.WWW::get_assetBundle()
extern void WWW_get_assetBundle_mE523FA754E97AAECE72EA84CBC5D1888E445DE9F (void);
// 0x00000010 System.Byte[] UnityEngine.WWW::get_bytes()
extern void WWW_get_bytes_m378FCCD8E91FB7FE7FA22E05BA3FE528CD7EAF1A (void);
// 0x00000011 System.Int32 UnityEngine.WWW::get_bytesDownloaded()
extern void WWW_get_bytesDownloaded_mD4AF80DF37B74950C738539F6A69B12C7D94F1E5 (void);
// 0x00000012 System.String UnityEngine.WWW::get_error()
extern void WWW_get_error_mB278F5EC90EF99FEF70D80112940CFB49E79C9BC (void);
// 0x00000013 System.Boolean UnityEngine.WWW::get_isDone()
extern void WWW_get_isDone_m916B54D53395990DB59C64413798FBCAFB08E0E3 (void);
// 0x00000014 System.Single UnityEngine.WWW::get_progress()
extern void WWW_get_progress_m53C94C51D328A968EC132163D1F8B87654B5E073 (void);
// 0x00000015 System.Collections.Generic.Dictionary`2<System.String,System.String> UnityEngine.WWW::get_responseHeaders()
extern void WWW_get_responseHeaders_m41BDD7540714ED01ED6B0F63F3062AE1D4C8B598 (void);
// 0x00000016 System.String UnityEngine.WWW::get_text()
extern void WWW_get_text_m0D2EF7BBFB58E37FE30A665389355ACA65804138 (void);
// 0x00000017 UnityEngine.Texture2D UnityEngine.WWW::CreateTextureFromDownloadedData(System.Boolean)
extern void WWW_CreateTextureFromDownloadedData_m978F4A746B24EEA580E7AA8F1F05203196A299F9 (void);
// 0x00000018 UnityEngine.Texture2D UnityEngine.WWW::get_texture()
extern void WWW_get_texture_mC23FF88883698F3E6F7BED2733A2DB3B18F788E4 (void);
// 0x00000019 UnityEngine.Texture2D UnityEngine.WWW::get_textureNonReadable()
extern void WWW_get_textureNonReadable_m5656C1450CDA1CF1A55B8BC1EBBE21CCCE9FDD0C (void);
// 0x0000001A System.Void UnityEngine.WWW::LoadImageIntoTexture(UnityEngine.Texture2D)
extern void WWW_LoadImageIntoTexture_m20D481E8B0502D37528B5C648818D9983C1BDFEE (void);
// 0x0000001B UnityEngine.ThreadPriority UnityEngine.WWW::get_threadPriority()
extern void WWW_get_threadPriority_mF4153D3BC7816C8990BAB7C61A19A7B1CD26AC5D (void);
// 0x0000001C System.Void UnityEngine.WWW::set_threadPriority(UnityEngine.ThreadPriority)
extern void WWW_set_threadPriority_m4BC94A175B1556C516D4CB453F1C89BA4A72C141 (void);
// 0x0000001D System.Single UnityEngine.WWW::get_uploadProgress()
extern void WWW_get_uploadProgress_mC960CD6A757EDCC8C5149A437FE5DB9D32C88634 (void);
// 0x0000001E System.String UnityEngine.WWW::get_url()
extern void WWW_get_url_m1D75D492D78A7AA8F607C5D7700497B8FE5E9526 (void);
// 0x0000001F System.Boolean UnityEngine.WWW::get_keepWaiting()
extern void WWW_get_keepWaiting_m231A6A7A835610182D78FC414665CC75195ABD70 (void);
// 0x00000020 System.Void UnityEngine.WWW::Dispose()
extern void WWW_Dispose_mF5A8B944281564903043545BC1E7F1CAD941519F (void);
// 0x00000021 UnityEngine.Object UnityEngine.WWW::GetAudioClipInternal(System.Boolean,System.Boolean,System.Boolean,UnityEngine.AudioType)
extern void WWW_GetAudioClipInternal_mF8229045A1132E05315D8A45C99291D0A00EF940 (void);
// 0x00000022 UnityEngine.AudioClip UnityEngine.WWW::GetAudioClip()
extern void WWW_GetAudioClip_m7DEB77D23B10F28DF9E2047B0B3E3302E0CBB43A (void);
// 0x00000023 UnityEngine.AudioClip UnityEngine.WWW::GetAudioClip(System.Boolean)
extern void WWW_GetAudioClip_m8A681CDB374B874325CA3ADB0A2BE6DA39E6CB8C (void);
// 0x00000024 UnityEngine.AudioClip UnityEngine.WWW::GetAudioClip(System.Boolean,System.Boolean)
extern void WWW_GetAudioClip_m6BB7008EDBC79E3A8179C24CC1F69D9932CD71A2 (void);
// 0x00000025 UnityEngine.AudioClip UnityEngine.WWW::GetAudioClip(System.Boolean,System.Boolean,UnityEngine.AudioType)
extern void WWW_GetAudioClip_m8E8051E071CD814FDBBEF9857533C66456E18D38 (void);
// 0x00000026 UnityEngine.AudioClip UnityEngine.WWW::GetAudioClipCompressed()
extern void WWW_GetAudioClipCompressed_mA360E5A1AAE14DCEC767FB9626FFF23B3E0D49B4 (void);
// 0x00000027 UnityEngine.AudioClip UnityEngine.WWW::GetAudioClipCompressed(System.Boolean)
extern void WWW_GetAudioClipCompressed_mC562AD528C242AD143D66AEAB82091F9DCF1DCAB (void);
// 0x00000028 UnityEngine.AudioClip UnityEngine.WWW::GetAudioClipCompressed(System.Boolean,UnityEngine.AudioType)
extern void WWW_GetAudioClipCompressed_m2DCDFE6C5A32DC59784CD231FFC2414CFFE79103 (void);
// 0x00000029 System.Boolean UnityEngine.WWW::WaitUntilDoneIfPossible()
extern void WWW_WaitUntilDoneIfPossible_m8D6B638F661CBD13B442F392BF42F5C9BDF0E84D (void);
// 0x0000002A UnityEngine.AudioClip UnityEngine.Networking.WebRequestWWW::InternalCreateAudioClipUsingDH(UnityEngine.Networking.DownloadHandler,System.String,System.Boolean,System.Boolean,UnityEngine.AudioType)
extern void WebRequestWWW_InternalCreateAudioClipUsingDH_mE14CA92D0B8FD1152630AF4A076B49D8E1E74329 (void);
static Il2CppMethodPointer s_methodPointers[42] = 
{
	WWW_EscapeURL_mBE31E4E0CF0AA05B8C1580AB2EDBDF9C24061C63,
	WWW_EscapeURL_mE69323EB64D6AD3AF6B69952BC8EAFF8D98F281F,
	WWW_UnEscapeURL_m5CC786E81C413BBCA2DBA8C2A04C0718C43BD45E,
	WWW_UnEscapeURL_m6747F1EFD419272E2C1E3E4F09DC101261FF1A7C,
	WWW_LoadFromCacheOrDownload_m2772A637BC4DC15E8EDD35B1612ABAD8E3AE5490,
	WWW_LoadFromCacheOrDownload_mCD2F8D4B41C321DC873CBC0C3CBEFC2260A49E15,
	WWW_LoadFromCacheOrDownload_mA03E177C70F7E3D4FB9F7CC71A45D636D9DCCC05,
	WWW_LoadFromCacheOrDownload_m70A95398FA8D5927C25DEE4A28331AB3A45FE678,
	WWW_LoadFromCacheOrDownload_m56CE5DB86EF3ED96BE9786BC0C112478A745A33D,
	WWW__ctor_mE77AD6C372CC76F48A893C5E2F91A81193A9F8C5,
	WWW__ctor_m2F58987EB716A6D1B9B2425464E5C42FB6CF7DE6,
	WWW__ctor_mFB9E9E1DE62E5826F643B25B3AB6BE33B681B0F4,
	WWW__ctor_m6686CBC878BB9EB28F4C4171C203CA4E3DE3656C,
	WWW__ctor_m443FACCAB63C95EB81924E1FF3B5ADE2D7875186,
	WWW_get_assetBundle_mE523FA754E97AAECE72EA84CBC5D1888E445DE9F,
	WWW_get_bytes_m378FCCD8E91FB7FE7FA22E05BA3FE528CD7EAF1A,
	WWW_get_bytesDownloaded_mD4AF80DF37B74950C738539F6A69B12C7D94F1E5,
	WWW_get_error_mB278F5EC90EF99FEF70D80112940CFB49E79C9BC,
	WWW_get_isDone_m916B54D53395990DB59C64413798FBCAFB08E0E3,
	WWW_get_progress_m53C94C51D328A968EC132163D1F8B87654B5E073,
	WWW_get_responseHeaders_m41BDD7540714ED01ED6B0F63F3062AE1D4C8B598,
	WWW_get_text_m0D2EF7BBFB58E37FE30A665389355ACA65804138,
	WWW_CreateTextureFromDownloadedData_m978F4A746B24EEA580E7AA8F1F05203196A299F9,
	WWW_get_texture_mC23FF88883698F3E6F7BED2733A2DB3B18F788E4,
	WWW_get_textureNonReadable_m5656C1450CDA1CF1A55B8BC1EBBE21CCCE9FDD0C,
	WWW_LoadImageIntoTexture_m20D481E8B0502D37528B5C648818D9983C1BDFEE,
	WWW_get_threadPriority_mF4153D3BC7816C8990BAB7C61A19A7B1CD26AC5D,
	WWW_set_threadPriority_m4BC94A175B1556C516D4CB453F1C89BA4A72C141,
	WWW_get_uploadProgress_mC960CD6A757EDCC8C5149A437FE5DB9D32C88634,
	WWW_get_url_m1D75D492D78A7AA8F607C5D7700497B8FE5E9526,
	WWW_get_keepWaiting_m231A6A7A835610182D78FC414665CC75195ABD70,
	WWW_Dispose_mF5A8B944281564903043545BC1E7F1CAD941519F,
	WWW_GetAudioClipInternal_mF8229045A1132E05315D8A45C99291D0A00EF940,
	WWW_GetAudioClip_m7DEB77D23B10F28DF9E2047B0B3E3302E0CBB43A,
	WWW_GetAudioClip_m8A681CDB374B874325CA3ADB0A2BE6DA39E6CB8C,
	WWW_GetAudioClip_m6BB7008EDBC79E3A8179C24CC1F69D9932CD71A2,
	WWW_GetAudioClip_m8E8051E071CD814FDBBEF9857533C66456E18D38,
	WWW_GetAudioClipCompressed_mA360E5A1AAE14DCEC767FB9626FFF23B3E0D49B4,
	WWW_GetAudioClipCompressed_mC562AD528C242AD143D66AEAB82091F9DCF1DCAB,
	WWW_GetAudioClipCompressed_m2DCDFE6C5A32DC59784CD231FFC2414CFFE79103,
	WWW_WaitUntilDoneIfPossible_m8D6B638F661CBD13B442F392BF42F5C9BDF0E84D,
	WebRequestWWW_InternalCreateAudioClipUsingDH_mE14CA92D0B8FD1152630AF4A076B49D8E1E74329,
};
static const int32_t s_InvokerIndices[42] = 
{
	6091,
	5599,
	6091,
	5599,
	5595,
	5178,
	5593,
	5172,
	5166,
	3348,
	2171,
	2171,
	1337,
	890,
	3916,
	3916,
	3893,
	3916,
	3863,
	3951,
	3916,
	3916,
	3037,
	3916,
	3916,
	3348,
	3893,
	3313,
	3951,
	3916,
	3863,
	3967,
	595,
	3916,
	3037,
	1685,
	1063,
	3916,
	3037,
	1687,
	3863,
	4493,
};
extern const CustomAttributesCacheGenerator g_UnityEngine_UnityWebRequestWWWModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_UnityWebRequestWWWModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_UnityWebRequestWWWModule_CodeGenModule = 
{
	"UnityEngine.UnityWebRequestWWWModule.dll",
	42,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_UnityEngine_UnityWebRequestWWWModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

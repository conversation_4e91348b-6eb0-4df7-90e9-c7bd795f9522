﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.String UnityEngine.ISubsystemDescriptor::get_id()
// 0x00000002 System.Void UnityEngine.IntegratedSubsystem::SetHandle(UnityEngine.IntegratedSubsystem)
extern void IntegratedSubsystem_SetHandle_m951ABC336E4544AAD792A7118E2261AC8F2A297E (void);
// 0x00000003 System.Void UnityEngine.IntegratedSubsystem::.ctor()
extern void IntegratedSubsystem__ctor_mCD638F6C367F1B83B1CB62130E570CA64A757DCE (void);
// 0x00000004 System.Void UnityEngine.IntegratedSubsystem`1::.ctor()
// 0x00000005 System.String UnityEngine.IntegratedSubsystemDescriptor::get_id()
extern void IntegratedSubsystemDescriptor_get_id_m0347A7AE998F0210CC7DAD1DA974F788AB2CF879 (void);
// 0x00000006 System.Void UnityEngine.IntegratedSubsystemDescriptor::.ctor()
extern void IntegratedSubsystemDescriptor__ctor_m06A2C2DB3D3004DFAC7344C2B8FDEF89434CBF3A (void);
// 0x00000007 System.Void UnityEngine.IntegratedSubsystemDescriptor`1::.ctor()
// 0x00000008 System.String UnityEngine.SubsystemDescriptorBindings::GetId(System.IntPtr)
extern void SubsystemDescriptorBindings_GetId_m4EE049E0406900218C91037D565289AD91C58344 (void);
// 0x00000009 System.Void UnityEngine.Subsystem::.ctor()
extern void Subsystem__ctor_mC6AFF077E6D2BDFB272AFC1EA25D1375EC64EF77 (void);
// 0x0000000A System.String UnityEngine.SubsystemDescriptor::get_id()
extern void SubsystemDescriptor_get_id_mC3D7972588D4B57F906A06CEE54A61B55D1306DB (void);
// 0x0000000B System.Void UnityEngine.SubsystemDescriptor::.ctor()
extern void SubsystemDescriptor__ctor_m6BE761B6ADD71B136BCECB0F54B82801B7222114 (void);
// 0x0000000C System.Void UnityEngine.Internal_SubsystemDescriptors::Internal_AddDescriptor(UnityEngine.SubsystemDescriptor)
extern void Internal_SubsystemDescriptors_Internal_AddDescriptor_mE58F93C9BD9EB0B214A035BD2946502E7D6F7F28 (void);
// 0x0000000D System.Void UnityEngine.SubsystemManager::ReloadSubsystemsStarted()
extern void SubsystemManager_ReloadSubsystemsStarted_mE03EAF84668B7899152ABF20A772A8F730EBE4EC (void);
// 0x0000000E System.Void UnityEngine.SubsystemManager::ReloadSubsystemsCompleted()
extern void SubsystemManager_ReloadSubsystemsCompleted_m06A346A8997A31C8987097F1BBDCA8D2E50DEF15 (void);
// 0x0000000F System.Void UnityEngine.SubsystemManager::InitializeIntegratedSubsystem(System.IntPtr,UnityEngine.IntegratedSubsystem)
extern void SubsystemManager_InitializeIntegratedSubsystem_m7C561115016DBF4028D144CAE7EB0F7CD2FB414C (void);
// 0x00000010 System.Void UnityEngine.SubsystemManager::ClearSubsystems()
extern void SubsystemManager_ClearSubsystems_mAB4253ABD8AC74C8D7E2315901E6AAAFD052ADBE (void);
// 0x00000011 System.Void UnityEngine.SubsystemManager::StaticConstructScriptingClassMap()
extern void SubsystemManager_StaticConstructScriptingClassMap_m7BCC8951814431E9175831E56D21140102487FB8 (void);
// 0x00000012 System.Void UnityEngine.SubsystemManager::.cctor()
extern void SubsystemManager__cctor_m186104F78CF555D1FE2C6E93B0C9F58B5E7A940E (void);
// 0x00000013 UnityEngine.IntegratedSubsystem UnityEngine.SubsystemManager::GetIntegratedSubsystemByPtr(System.IntPtr)
extern void SubsystemManager_GetIntegratedSubsystemByPtr_mF7CCFB02956CB6FF9262BF47CF8F914AB78BF086 (void);
// 0x00000014 System.Void UnityEngine.SubsystemsImplementation.SubsystemDescriptorStore::InitializeManagedDescriptor(System.IntPtr,UnityEngine.IntegratedSubsystemDescriptor)
extern void SubsystemDescriptorStore_InitializeManagedDescriptor_m4B7C0A3C441DE725F0CA82B53D88B247A4AB9FEB (void);
// 0x00000015 System.Void UnityEngine.SubsystemsImplementation.SubsystemDescriptorStore::ClearManagedDescriptors()
extern void SubsystemDescriptorStore_ClearManagedDescriptors_mCA2345CD1E74F02AF1FEEB78F855D52D91953DA4 (void);
// 0x00000016 System.Void UnityEngine.SubsystemsImplementation.SubsystemDescriptorStore::ReportSingleSubsystemAnalytics(System.String)
extern void SubsystemDescriptorStore_ReportSingleSubsystemAnalytics_mC7D6C2CD8C3AD93F3DCC4CA3CE04B2B95C30DC15 (void);
// 0x00000017 System.Void UnityEngine.SubsystemsImplementation.SubsystemDescriptorStore::RegisterDescriptor(TDescriptor,System.Collections.Generic.List`1<TBaseTypeInList>)
// 0x00000018 System.Void UnityEngine.SubsystemsImplementation.SubsystemDescriptorStore::RegisterDeprecatedDescriptor(UnityEngine.SubsystemDescriptor)
extern void SubsystemDescriptorStore_RegisterDeprecatedDescriptor_m33D7186142719280B4AB1DAED92AE5D417531333 (void);
// 0x00000019 System.Void UnityEngine.SubsystemsImplementation.SubsystemDescriptorStore::.cctor()
extern void SubsystemDescriptorStore__cctor_m307A0E309C2618E022D55F55B8491A0180BA03AA (void);
// 0x0000001A System.String UnityEngine.SubsystemsImplementation.SubsystemDescriptorWithProvider::get_id()
extern void SubsystemDescriptorWithProvider_get_id_m0341F88D726C677F81277CD2C4A43919EA942861 (void);
// 0x0000001B System.Void UnityEngine.SubsystemsImplementation.SubsystemDescriptorWithProvider::.ctor()
extern void SubsystemDescriptorWithProvider__ctor_m6549AFB004D82BC1439CF25E69BC8BAB9C315604 (void);
// 0x0000001C System.Void UnityEngine.SubsystemsImplementation.SubsystemWithProvider::.ctor()
extern void SubsystemWithProvider__ctor_m7839AE90041C8237270AFC52FC96E1BEECCDC653 (void);
static Il2CppMethodPointer s_methodPointers[28] = 
{
	NULL,
	IntegratedSubsystem_SetHandle_m951ABC336E4544AAD792A7118E2261AC8F2A297E,
	IntegratedSubsystem__ctor_mCD638F6C367F1B83B1CB62130E570CA64A757DCE,
	NULL,
	IntegratedSubsystemDescriptor_get_id_m0347A7AE998F0210CC7DAD1DA974F788AB2CF879,
	IntegratedSubsystemDescriptor__ctor_m06A2C2DB3D3004DFAC7344C2B8FDEF89434CBF3A,
	NULL,
	SubsystemDescriptorBindings_GetId_m4EE049E0406900218C91037D565289AD91C58344,
	Subsystem__ctor_mC6AFF077E6D2BDFB272AFC1EA25D1375EC64EF77,
	SubsystemDescriptor_get_id_mC3D7972588D4B57F906A06CEE54A61B55D1306DB,
	SubsystemDescriptor__ctor_m6BE761B6ADD71B136BCECB0F54B82801B7222114,
	Internal_SubsystemDescriptors_Internal_AddDescriptor_mE58F93C9BD9EB0B214A035BD2946502E7D6F7F28,
	SubsystemManager_ReloadSubsystemsStarted_mE03EAF84668B7899152ABF20A772A8F730EBE4EC,
	SubsystemManager_ReloadSubsystemsCompleted_m06A346A8997A31C8987097F1BBDCA8D2E50DEF15,
	SubsystemManager_InitializeIntegratedSubsystem_m7C561115016DBF4028D144CAE7EB0F7CD2FB414C,
	SubsystemManager_ClearSubsystems_mAB4253ABD8AC74C8D7E2315901E6AAAFD052ADBE,
	SubsystemManager_StaticConstructScriptingClassMap_m7BCC8951814431E9175831E56D21140102487FB8,
	SubsystemManager__cctor_m186104F78CF555D1FE2C6E93B0C9F58B5E7A940E,
	SubsystemManager_GetIntegratedSubsystemByPtr_mF7CCFB02956CB6FF9262BF47CF8F914AB78BF086,
	SubsystemDescriptorStore_InitializeManagedDescriptor_m4B7C0A3C441DE725F0CA82B53D88B247A4AB9FEB,
	SubsystemDescriptorStore_ClearManagedDescriptors_mCA2345CD1E74F02AF1FEEB78F855D52D91953DA4,
	SubsystemDescriptorStore_ReportSingleSubsystemAnalytics_mC7D6C2CD8C3AD93F3DCC4CA3CE04B2B95C30DC15,
	NULL,
	SubsystemDescriptorStore_RegisterDeprecatedDescriptor_m33D7186142719280B4AB1DAED92AE5D417531333,
	SubsystemDescriptorStore__cctor_m307A0E309C2618E022D55F55B8491A0180BA03AA,
	SubsystemDescriptorWithProvider_get_id_m0341F88D726C677F81277CD2C4A43919EA942861,
	SubsystemDescriptorWithProvider__ctor_m6549AFB004D82BC1439CF25E69BC8BAB9C315604,
	SubsystemWithProvider__ctor_m7839AE90041C8237270AFC52FC96E1BEECCDC653,
};
static const int32_t s_InvokerIndices[28] = 
{
	3916,
	3348,
	3967,
	-1,
	3916,
	3967,
	-1,
	6090,
	3967,
	3916,
	3967,
	6218,
	6289,
	6289,
	5797,
	6289,
	6289,
	6289,
	6090,
	5797,
	6289,
	6218,
	-1,
	6218,
	6289,
	3916,
	3967,
	3967,
};
static const Il2CppTokenRangePair s_rgctxIndices[1] = 
{
	{ 0x06000017, { 0, 6 } },
};
static const Il2CppRGCTXDefinition s_rgctxValues[6] = 
{
	{ (Il2CppRGCTXDataType)3, 15325 },
	{ (Il2CppRGCTXDataType)2, 833 },
	{ (Il2CppRGCTXDataType)2, 426 },
	{ (Il2CppRGCTXDataType)3, 15326 },
	{ (Il2CppRGCTXDataType)3, 15324 },
	{ (Il2CppRGCTXDataType)3, 15323 },
};
extern const CustomAttributesCacheGenerator g_UnityEngine_SubsystemsModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_SubsystemsModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_SubsystemsModule_CodeGenModule = 
{
	"UnityEngine.SubsystemsModule.dll",
	28,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	1,
	s_rgctxIndices,
	6,
	s_rgctxValues,
	NULL,
	g_UnityEngine_SubsystemsModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

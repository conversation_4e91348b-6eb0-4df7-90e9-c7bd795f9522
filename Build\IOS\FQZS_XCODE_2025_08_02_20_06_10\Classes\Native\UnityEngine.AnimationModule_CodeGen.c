﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void UnityEngine.StateMachineBehaviour::OnStateEnter(UnityEngine.Animator,UnityEngine.AnimatorStateInfo,System.Int32)
extern void StateMachineBehaviour_OnStateEnter_m0B5055A01EEF9070E7611D3C3165AAA118D22953 (void);
// 0x00000002 System.Void UnityEngine.StateMachineBehaviour::OnStateUpdate(UnityEngine.Animator,UnityEngine.AnimatorStateInfo,System.Int32)
extern void StateMachineBehaviour_OnStateUpdate_m2FF9D5AD07DF99860C7B0033791FE08F2EF919F1 (void);
// 0x00000003 System.Void UnityEngine.StateMachineBehaviour::OnStateExit(UnityEngine.Animator,UnityEngine.AnimatorStateInfo,System.Int32)
extern void StateMachineBehaviour_OnStateExit_mE8EADFCEA482A101BF13AFB773A06C3C2C8B3208 (void);
// 0x00000004 System.Void UnityEngine.StateMachineBehaviour::OnStateMove(UnityEngine.Animator,UnityEngine.AnimatorStateInfo,System.Int32)
extern void StateMachineBehaviour_OnStateMove_mB51A6EA16DA5038BF7C4E46863C8ECA1338EFBDD (void);
// 0x00000005 System.Void UnityEngine.StateMachineBehaviour::OnStateIK(UnityEngine.Animator,UnityEngine.AnimatorStateInfo,System.Int32)
extern void StateMachineBehaviour_OnStateIK_m2BB5A0CD4B083CCDFAC7EE2F8233D2B11825197F (void);
// 0x00000006 System.Void UnityEngine.StateMachineBehaviour::OnStateMachineEnter(UnityEngine.Animator,System.Int32)
extern void StateMachineBehaviour_OnStateMachineEnter_m8696CC6EE9DC7577A07023F84DCF6E4F80E75ACC (void);
// 0x00000007 System.Void UnityEngine.StateMachineBehaviour::OnStateMachineExit(UnityEngine.Animator,System.Int32)
extern void StateMachineBehaviour_OnStateMachineExit_m7FD170C30229751A93F64C26AFFF9C9BA057BF3D (void);
// 0x00000008 System.Void UnityEngine.StateMachineBehaviour::OnStateEnter(UnityEngine.Animator,UnityEngine.AnimatorStateInfo,System.Int32,UnityEngine.Animations.AnimatorControllerPlayable)
extern void StateMachineBehaviour_OnStateEnter_m0027D5548D58C0E2777A4CF9420F015FD56CEC18 (void);
// 0x00000009 System.Void UnityEngine.StateMachineBehaviour::OnStateUpdate(UnityEngine.Animator,UnityEngine.AnimatorStateInfo,System.Int32,UnityEngine.Animations.AnimatorControllerPlayable)
extern void StateMachineBehaviour_OnStateUpdate_mF81F7D0AB02EB31012A7C50E75295C40301A5055 (void);
// 0x0000000A System.Void UnityEngine.StateMachineBehaviour::OnStateExit(UnityEngine.Animator,UnityEngine.AnimatorStateInfo,System.Int32,UnityEngine.Animations.AnimatorControllerPlayable)
extern void StateMachineBehaviour_OnStateExit_m795DAE1099CF045D5E61ABBBAD017455F48B0707 (void);
// 0x0000000B System.Void UnityEngine.StateMachineBehaviour::OnStateMove(UnityEngine.Animator,UnityEngine.AnimatorStateInfo,System.Int32,UnityEngine.Animations.AnimatorControllerPlayable)
extern void StateMachineBehaviour_OnStateMove_m29F850CE0258906408520515E4E157D43AFEB181 (void);
// 0x0000000C System.Void UnityEngine.StateMachineBehaviour::OnStateIK(UnityEngine.Animator,UnityEngine.AnimatorStateInfo,System.Int32,UnityEngine.Animations.AnimatorControllerPlayable)
extern void StateMachineBehaviour_OnStateIK_m96E18AE1A75046F85EB7FEB5C05CEC7377F72C1F (void);
// 0x0000000D System.Void UnityEngine.StateMachineBehaviour::OnStateMachineEnter(UnityEngine.Animator,System.Int32,UnityEngine.Animations.AnimatorControllerPlayable)
extern void StateMachineBehaviour_OnStateMachineEnter_mF2DF6E7A25D30F05E99984F3E8D4083D695F23CA (void);
// 0x0000000E System.Void UnityEngine.StateMachineBehaviour::OnStateMachineExit(UnityEngine.Animator,System.Int32,UnityEngine.Animations.AnimatorControllerPlayable)
extern void StateMachineBehaviour_OnStateMachineExit_m8AC70A1160FE329D0E1EC31F08B1E85B59DB516D (void);
// 0x0000000F System.Void UnityEngine.StateMachineBehaviour::.ctor()
extern void StateMachineBehaviour__ctor_mDB0650FD738799E5880150E656D4A88524D0EBE0 (void);
// 0x00000010 System.Void UnityEngine.Animation::Stop()
extern void Animation_Stop_m06283FD9F64A3B05A2A248AE2B86C7F88D479DE9 (void);
// 0x00000011 System.Void UnityEngine.Animation::Sample()
extern void Animation_Sample_mC2F1CF215D835404E205F52DD8096B74EEE3E9A1 (void);
// 0x00000012 System.Boolean UnityEngine.Animation::get_isPlaying()
extern void Animation_get_isPlaying_m71AF60BE632978C6449B57D0AFA59CBF9A3F0705 (void);
// 0x00000013 System.Boolean UnityEngine.Animation::IsPlaying(System.String)
extern void Animation_IsPlaying_mD156265199E5875D1DFABCF82819BC4735E34887 (void);
// 0x00000014 System.Boolean UnityEngine.Animation::Play()
extern void Animation_Play_m5588607899B9B866117A1477C696076F161BA3D4 (void);
// 0x00000015 System.Boolean UnityEngine.Animation::Play(UnityEngine.PlayMode)
extern void Animation_Play_mE768E71A80625EBCE4577FA5F82E2A6E6FE4F9F8 (void);
// 0x00000016 System.Boolean UnityEngine.Animation::PlayDefaultAnimation(UnityEngine.PlayMode)
extern void Animation_PlayDefaultAnimation_mF95A24563D8A4517F75A8149D0961C2024DE8DAB (void);
// 0x00000017 System.Boolean UnityEngine.Animation::Play(System.String)
extern void Animation_Play_m8EDFE80589A27DF1C34CCC0CF81DB5313CE35607 (void);
// 0x00000018 System.Boolean UnityEngine.Animation::Play(System.String,UnityEngine.PlayMode)
extern void Animation_Play_m8F742F537EA22D32B109BB0B57F54315BA2182A1 (void);
// 0x00000019 System.Void UnityEngine.Animation::CrossFade(System.String)
extern void Animation_CrossFade_mAA2C1F467BCFF4AFF6B06D041BA98A9CBD57352B (void);
// 0x0000001A System.Void UnityEngine.Animation::CrossFade(System.String,System.Single)
extern void Animation_CrossFade_m7D83F71B3DCF3D5F8995389669800718C988FA1A (void);
// 0x0000001B System.Void UnityEngine.Animation::CrossFade(System.String,System.Single,UnityEngine.PlayMode)
extern void Animation_CrossFade_m3FA2A5C413E36D5463FEDDFFD87582867F2ED65E (void);
// 0x0000001C System.Collections.IEnumerator UnityEngine.Animation::GetEnumerator()
extern void Animation_GetEnumerator_m7F0049CCE49D9F5963AFB5552CDEAD51793891F7 (void);
// 0x0000001D UnityEngine.AnimationState UnityEngine.Animation::GetStateAtIndex(System.Int32)
extern void Animation_GetStateAtIndex_m436707B278555E5B3AD4FB8F7B1A04F532CDC679 (void);
// 0x0000001E System.Int32 UnityEngine.Animation::GetStateCount()
extern void Animation_GetStateCount_mBFB004C6182E7243A2DFA09D7E378A94AC61AABA (void);
// 0x0000001F System.Void UnityEngine.Animation/Enumerator::.ctor(UnityEngine.Animation)
extern void Enumerator__ctor_mC97A2B142004D7B757AAF61F6F069FB102372DBA (void);
// 0x00000020 System.Object UnityEngine.Animation/Enumerator::get_Current()
extern void Enumerator_get_Current_mAB7913C42CA7EB7EFE4AE1F7B9BEAA6B5F44C1A9 (void);
// 0x00000021 System.Boolean UnityEngine.Animation/Enumerator::MoveNext()
extern void Enumerator_MoveNext_m46EE9A2B09C10531425A6CFA6FD305CCDB4B3101 (void);
// 0x00000022 System.Void UnityEngine.Animation/Enumerator::Reset()
extern void Enumerator_Reset_mFC24132D3D5F334C8892F799D3B8B92971CBE6C0 (void);
// 0x00000023 System.Single UnityEngine.AnimationState::get_time()
extern void AnimationState_get_time_m116DAEA9256A49DF4E507AD604B2E1975AD86FFC (void);
// 0x00000024 System.Void UnityEngine.AnimationState::set_time(System.Single)
extern void AnimationState_set_time_m5A1281D5FD5A5D344CC4FC7966F8E6867AD53B20 (void);
// 0x00000025 System.Single UnityEngine.AnimationState::get_speed()
extern void AnimationState_get_speed_mA4174B85772BD949608C6DBE9EC3FB8AED298B97 (void);
// 0x00000026 System.Void UnityEngine.AnimationState::set_speed(System.Single)
extern void AnimationState_set_speed_m54AF12C004F2253228DA25B9E21774184A77271D (void);
// 0x00000027 System.Single UnityEngine.AnimationState::get_length()
extern void AnimationState_get_length_m7CF9F4E73761BA6921103F5293AF031E8A6BAEB7 (void);
// 0x00000028 System.Void UnityEngine.AnimationState::set_layer(System.Int32)
extern void AnimationState_set_layer_mBDCC887D4763B0CA1B1C1A3C1147E01AA0CF7BD2 (void);
// 0x00000029 UnityEngine.AnimationClip UnityEngine.AnimationState::get_clip()
extern void AnimationState_get_clip_m84FD95AFB1FDC356E53AA6F44089F69B353B42BB (void);
// 0x0000002A System.String UnityEngine.AnimationState::get_name()
extern void AnimationState_get_name_m0967781DD92AB4E35E17DD45136E46BAB3C57E9E (void);
// 0x0000002B System.Void UnityEngine.AnimationState::.ctor()
extern void AnimationState__ctor_m5ECFF9F10ACFB838138F1E33E3D55EEAF891D217 (void);
// 0x0000002C System.Void UnityEngine.AnimationEvent::.ctor()
extern void AnimationEvent__ctor_mA2780A113EA8DD56C3C2EDD0D60BBA78047BACDE (void);
// 0x0000002D System.Void UnityEngine.AnimationClip::.ctor()
extern void AnimationClip__ctor_m1678D7A0D58F768C92690BABB01DBDE64F12D09E (void);
// 0x0000002E System.Void UnityEngine.AnimationClip::Internal_CreateAnimationClip(UnityEngine.AnimationClip)
extern void AnimationClip_Internal_CreateAnimationClip_m1086DC82A4506493D813B97851216A01F027EB75 (void);
// 0x0000002F System.Single UnityEngine.AnimationClip::get_length()
extern void AnimationClip_get_length_m7917C4C333DD8083A5395581652C227E0CECC82B (void);
// 0x00000030 UnityEngine.AnimationClip UnityEngine.AnimatorClipInfo::get_clip()
extern void AnimatorClipInfo_get_clip_m0822D4BB447803A294410A319C812D2D4B946A95 (void);
// 0x00000031 System.Single UnityEngine.AnimatorClipInfo::get_weight()
extern void AnimatorClipInfo_get_weight_mF22612DA966F5D6C8EC93E6AD2E05DFE10B36CCA (void);
// 0x00000032 UnityEngine.AnimationClip UnityEngine.AnimatorClipInfo::InstanceIDToAnimationClipPPtr(System.Int32)
extern void AnimatorClipInfo_InstanceIDToAnimationClipPPtr_m9EF0945EABA33C718853712B00E23EF2AAC2084A (void);
// 0x00000033 System.Int32 UnityEngine.AnimatorStateInfo::get_fullPathHash()
extern void AnimatorStateInfo_get_fullPathHash_m296D315AB1FBF6177A423298296CECC1DBA7221D (void);
// 0x00000034 System.Single UnityEngine.AnimatorStateInfo::get_normalizedTime()
extern void AnimatorStateInfo_get_normalizedTime_mC951C5D83749FC2AE37DCC75A022383C578F3B40 (void);
// 0x00000035 System.Single UnityEngine.AnimatorStateInfo::get_speed()
extern void AnimatorStateInfo_get_speed_m1B2CAB95244A0ECCE42F79CCFC22BA7CB8618843 (void);
// 0x00000036 System.Boolean UnityEngine.AnimatorStateInfo::get_loop()
extern void AnimatorStateInfo_get_loop_mC5EDC24973284F2976E57A6558DBD360820FEED0 (void);
// 0x00000037 System.Void UnityEngine.Animator::SetBool(System.String,System.Boolean)
extern void Animator_SetBool_m34E2E9785A47A3AE94E804004425C333C36CCD43 (void);
// 0x00000038 System.Void UnityEngine.Animator::SetInteger(System.String,System.Int32)
extern void Animator_SetInteger_mFB04A03AF6C24978BF2BDE9161243F8F6B9762C5 (void);
// 0x00000039 System.Void UnityEngine.Animator::SetTrigger(System.String)
extern void Animator_SetTrigger_m2D79D155CABD81B1CC75EFC35D90508B58D7211C (void);
// 0x0000003A System.Void UnityEngine.Animator::ResetTrigger(System.String)
extern void Animator_ResetTrigger_m02F8CF7EABE466CC3D008A8538171E14BFB907FA (void);
// 0x0000003B System.Int32 UnityEngine.Animator::get_layerCount()
extern void Animator_get_layerCount_m9F7ED8546CE9F7A507C88C577919A9C5E4DFE4F6 (void);
// 0x0000003C System.Single UnityEngine.Animator::GetLayerWeight(System.Int32)
extern void Animator_GetLayerWeight_m0A73AC322BA23FD164856B7FF67A3A0B748A1CF4 (void);
// 0x0000003D System.Void UnityEngine.Animator::GetAnimatorStateInfo(System.Int32,UnityEngine.StateInfoIndex,UnityEngine.AnimatorStateInfo&)
extern void Animator_GetAnimatorStateInfo_m623172399D38470B4C5EB7DE06A3106AB6958657 (void);
// 0x0000003E UnityEngine.AnimatorStateInfo UnityEngine.Animator::GetCurrentAnimatorStateInfo(System.Int32)
extern void Animator_GetCurrentAnimatorStateInfo_m562250C74BF8C626B5227FE840D6CB739B5F8314 (void);
// 0x0000003F UnityEngine.AnimatorStateInfo UnityEngine.Animator::GetNextAnimatorStateInfo(System.Int32)
extern void Animator_GetNextAnimatorStateInfo_m0CE4DD9BD652C6C8C691C308FF86ACF650A91C62 (void);
// 0x00000040 System.Int32 UnityEngine.Animator::GetAnimatorClipInfoCount(System.Int32,System.Boolean)
extern void Animator_GetAnimatorClipInfoCount_m80A50EDDEAFD721E18702F7F0706552A3C883D3B (void);
// 0x00000041 System.Int32 UnityEngine.Animator::GetCurrentAnimatorClipInfoCount(System.Int32)
extern void Animator_GetCurrentAnimatorClipInfoCount_m966D03992968F33EAA0EC9456AE60E18424621BE (void);
// 0x00000042 System.Int32 UnityEngine.Animator::GetNextAnimatorClipInfoCount(System.Int32)
extern void Animator_GetNextAnimatorClipInfoCount_mB69FE8373F6D7BD95162DB6A583818DD24C83A02 (void);
// 0x00000043 System.Void UnityEngine.Animator::GetCurrentAnimatorClipInfo(System.Int32,System.Collections.Generic.List`1<UnityEngine.AnimatorClipInfo>)
extern void Animator_GetCurrentAnimatorClipInfo_mA1061569ED175C2B54A3273EDB889E9D48DE5A2C (void);
// 0x00000044 System.Void UnityEngine.Animator::GetAnimatorClipInfoInternal(System.Int32,System.Boolean,System.Object)
extern void Animator_GetAnimatorClipInfoInternal_m7369503B85FAE6630B97209D4F3A7492BD31EA39 (void);
// 0x00000045 System.Void UnityEngine.Animator::GetNextAnimatorClipInfo(System.Int32,System.Collections.Generic.List`1<UnityEngine.AnimatorClipInfo>)
extern void Animator_GetNextAnimatorClipInfo_mB5F0367EAC2C788CD5DB486C511B4F783339C066 (void);
// 0x00000046 System.Void UnityEngine.Animator::set_speed(System.Single)
extern void Animator_set_speed_m632FF62E0D6B668C55190B3579B12684316C4041 (void);
// 0x00000047 System.Void UnityEngine.Animator::Play(System.String)
extern void Animator_Play_mE5E8B1753FFDF754EAD1ACEFF6C5B6ACA506363C (void);
// 0x00000048 System.Void UnityEngine.Animator::Play(System.String,System.Int32,System.Single)
extern void Animator_Play_m1438EDACA2804B50ED0D00D9986E30BCF903418B (void);
// 0x00000049 System.Void UnityEngine.Animator::Play(System.Int32,System.Int32,System.Single)
extern void Animator_Play_m392289975B858ED7D85DF6E7980E5BA47F606AEC (void);
// 0x0000004A UnityEngine.RuntimeAnimatorController UnityEngine.Animator::get_runtimeAnimatorController()
extern void Animator_get_runtimeAnimatorController_mA31EAB2AB6CA23B48FA70155CA399DFA2EE525CC (void);
// 0x0000004B System.Void UnityEngine.Animator::set_runtimeAnimatorController(UnityEngine.RuntimeAnimatorController)
extern void Animator_set_runtimeAnimatorController_mF50ED3D400626A3E3FC42BB6F0772A6CADEEC63B (void);
// 0x0000004C System.Boolean UnityEngine.Animator::get_hasBoundPlayables()
extern void Animator_get_hasBoundPlayables_m1ADEF28BC77A4C8DBC707DA02A1B72E00AC0C88A (void);
// 0x0000004D System.Int32 UnityEngine.Animator::StringToHash(System.String)
extern void Animator_StringToHash_mC9D8C2AB862E0C24670267F55A28DE94B23A7511 (void);
// 0x0000004E System.Void UnityEngine.Animator::SetBoolString(System.String,System.Boolean)
extern void Animator_SetBoolString_mB0D21540179FFB9E5F1B2C2EF008BD0595B78BA7 (void);
// 0x0000004F System.Void UnityEngine.Animator::SetIntegerString(System.String,System.Int32)
extern void Animator_SetIntegerString_m98F3F5AF11579CA8F0DD2C43625CBE6258999CB9 (void);
// 0x00000050 System.Void UnityEngine.Animator::SetTriggerString(System.String)
extern void Animator_SetTriggerString_m38F66A49276BCED56B89BB6AF8A36183BE4285F0 (void);
// 0x00000051 System.Void UnityEngine.Animator::ResetTriggerString(System.String)
extern void Animator_ResetTriggerString_m6FC21A6B7732A31338EE22E78F3D6220903EDBB2 (void);
// 0x00000052 System.Void UnityEngine.Animator::Update(System.Single)
extern void Animator_Update_mBA8EC912BA18CB8718B4A4CCB4F18A0639C36E0D (void);
// 0x00000053 System.Void UnityEngine.AnimatorOverrideController::OnInvalidateOverrideController(UnityEngine.AnimatorOverrideController)
extern void AnimatorOverrideController_OnInvalidateOverrideController_mB6D3256DA021EADE4D68F303C3C4146F55D45DAD (void);
// 0x00000054 System.Void UnityEngine.AnimatorOverrideController/OnOverrideControllerDirtyCallback::.ctor(System.Object,System.IntPtr)
extern void OnOverrideControllerDirtyCallback__ctor_mA35F55BEB8A4BD57D109684E857F85C1F0A6C1B5 (void);
// 0x00000055 System.Void UnityEngine.AnimatorOverrideController/OnOverrideControllerDirtyCallback::Invoke()
extern void OnOverrideControllerDirtyCallback_Invoke_m21DB79300E852ED93F2521FFC03EC4D858F6B330 (void);
// 0x00000056 System.IAsyncResult UnityEngine.AnimatorOverrideController/OnOverrideControllerDirtyCallback::BeginInvoke(System.AsyncCallback,System.Object)
extern void OnOverrideControllerDirtyCallback_BeginInvoke_m40D5810BF8C5066DB2C7987E7C76FD23D2AC47E3 (void);
// 0x00000057 System.Void UnityEngine.AnimatorOverrideController/OnOverrideControllerDirtyCallback::EndInvoke(System.IAsyncResult)
extern void OnOverrideControllerDirtyCallback_EndInvoke_mAF3C7805FADE63999DC2121032B11DF86668E9F4 (void);
// 0x00000058 System.Void UnityEngine.Motion::.ctor()
extern void Motion__ctor_mDC0BDF6F52E99C4A69EFDAC157CC29D0C1A4F9E6 (void);
// 0x00000059 System.Void UnityEngine.RuntimeAnimatorController::.ctor()
extern void RuntimeAnimatorController__ctor_m974DD22EB55FCAE168AE41718C2BD48B008C7CB3 (void);
// 0x0000005A UnityEngine.Playables.PlayableHandle UnityEngine.Animations.AnimationClipPlayable::GetHandle()
extern void AnimationClipPlayable_GetHandle_m93C27911A3C7107750C2A6BE529C58FB2FDB1122 (void);
// 0x0000005B System.Boolean UnityEngine.Animations.AnimationClipPlayable::Equals(UnityEngine.Animations.AnimationClipPlayable)
extern void AnimationClipPlayable_Equals_m73BDBE0839B6AA4782C37B21DD58D3388B5EC814 (void);
// 0x0000005C System.Void UnityEngine.Animations.AnimationLayerMixerPlayable::.ctor(UnityEngine.Playables.PlayableHandle)
extern void AnimationLayerMixerPlayable__ctor_m42F8E5BB37A175AF298324D3072932ED9946427B (void);
// 0x0000005D UnityEngine.Playables.PlayableHandle UnityEngine.Animations.AnimationLayerMixerPlayable::GetHandle()
extern void AnimationLayerMixerPlayable_GetHandle_mBFA950F140D76E10983B9AB946397F4C12ABC439 (void);
// 0x0000005E System.Boolean UnityEngine.Animations.AnimationLayerMixerPlayable::Equals(UnityEngine.Animations.AnimationLayerMixerPlayable)
extern void AnimationLayerMixerPlayable_Equals_m018BD27B24B3EDC5101A475A14F13F753F2323AA (void);
// 0x0000005F System.Void UnityEngine.Animations.AnimationLayerMixerPlayable::.cctor()
extern void AnimationLayerMixerPlayable__cctor_m098B488257BF0185BD8C01EFAB03D2C54A19D31B (void);
// 0x00000060 System.Void UnityEngine.Animations.AnimationMixerPlayable::.ctor(UnityEngine.Playables.PlayableHandle)
extern void AnimationMixerPlayable__ctor_mA03CF37709B7854227E25F91BE4F7559981058B0 (void);
// 0x00000061 UnityEngine.Playables.PlayableHandle UnityEngine.Animations.AnimationMixerPlayable::GetHandle()
extern void AnimationMixerPlayable_GetHandle_mE8F7D1A18F1BD1C00BA1EC6AA8036044E8907FC3 (void);
// 0x00000062 System.Boolean UnityEngine.Animations.AnimationMixerPlayable::Equals(UnityEngine.Animations.AnimationMixerPlayable)
extern void AnimationMixerPlayable_Equals_m8979D90ED92FF553B5D6AB0BDD616C544352816B (void);
// 0x00000063 System.Void UnityEngine.Animations.AnimationMixerPlayable::.cctor()
extern void AnimationMixerPlayable__cctor_m43D5D606D27FC8A55EFDBBC107B14C27BCC2C2D5 (void);
// 0x00000064 System.Void UnityEngine.Animations.AnimationMotionXToDeltaPlayable::.ctor(UnityEngine.Playables.PlayableHandle)
extern void AnimationMotionXToDeltaPlayable__ctor_m11668860161B62484EA095BD6360AFD26A86DE93 (void);
// 0x00000065 UnityEngine.Playables.PlayableHandle UnityEngine.Animations.AnimationMotionXToDeltaPlayable::GetHandle()
extern void AnimationMotionXToDeltaPlayable_GetHandle_m840D19A4E2DFB4BF2397061B833E63AD786587BA (void);
// 0x00000066 System.Boolean UnityEngine.Animations.AnimationMotionXToDeltaPlayable::Equals(UnityEngine.Animations.AnimationMotionXToDeltaPlayable)
extern void AnimationMotionXToDeltaPlayable_Equals_mB08A41C628755AF909489716A1D62AECC2BFDD9E (void);
// 0x00000067 System.Void UnityEngine.Animations.AnimationMotionXToDeltaPlayable::.cctor()
extern void AnimationMotionXToDeltaPlayable__cctor_m56F27AA05BD378D0320BFEEBC8A4AB53C686D7F5 (void);
// 0x00000068 System.Void UnityEngine.Animations.AnimationOffsetPlayable::.ctor(UnityEngine.Playables.PlayableHandle)
extern void AnimationOffsetPlayable__ctor_m9527E52AEA325EAE188AB9843497F2AB33CB742E (void);
// 0x00000069 UnityEngine.Playables.PlayableHandle UnityEngine.Animations.AnimationOffsetPlayable::GetHandle()
extern void AnimationOffsetPlayable_GetHandle_m8C3C08EC531127B002D3AFAB5AF259D8030B0049 (void);
// 0x0000006A System.Boolean UnityEngine.Animations.AnimationOffsetPlayable::Equals(UnityEngine.Animations.AnimationOffsetPlayable)
extern void AnimationOffsetPlayable_Equals_m9AFE60B035481569924E20C6953B4B21EF7734AA (void);
// 0x0000006B System.Void UnityEngine.Animations.AnimationOffsetPlayable::.cctor()
extern void AnimationOffsetPlayable__cctor_m8B28D6F560CE5715B9DCA320EEADD5220ABFAF84 (void);
// 0x0000006C System.Void UnityEngine.Animations.AnimationPosePlayable::.ctor(UnityEngine.Playables.PlayableHandle)
extern void AnimationPosePlayable__ctor_m318607F120F21EDE3D7C1ED07C8B2ED13A23BF57 (void);
// 0x0000006D UnityEngine.Playables.PlayableHandle UnityEngine.Animations.AnimationPosePlayable::GetHandle()
extern void AnimationPosePlayable_GetHandle_m0354C54EB680FC70D4B48D95F7FC4BA4700A0DCE (void);
// 0x0000006E System.Boolean UnityEngine.Animations.AnimationPosePlayable::Equals(UnityEngine.Animations.AnimationPosePlayable)
extern void AnimationPosePlayable_Equals_mECC5FA256AAA5334C38DBB6D00EE8AC1BDC015A1 (void);
// 0x0000006F System.Void UnityEngine.Animations.AnimationPosePlayable::.cctor()
extern void AnimationPosePlayable__cctor_mED38E3FFBD9F82C85C65826E83984D627B4C5058 (void);
// 0x00000070 System.Void UnityEngine.Animations.AnimationRemoveScalePlayable::.ctor(UnityEngine.Playables.PlayableHandle)
extern void AnimationRemoveScalePlayable__ctor_m08810C8ECE9A3A100087DD84B13204EC3AF73A8F (void);
// 0x00000071 UnityEngine.Playables.PlayableHandle UnityEngine.Animations.AnimationRemoveScalePlayable::GetHandle()
extern void AnimationRemoveScalePlayable_GetHandle_m1949202B58BDF17726A1ADC934EB5232E835CCA8 (void);
// 0x00000072 System.Boolean UnityEngine.Animations.AnimationRemoveScalePlayable::Equals(UnityEngine.Animations.AnimationRemoveScalePlayable)
extern void AnimationRemoveScalePlayable_Equals_m7FE9E55B027861A0B91347F18DAC7E11E2740397 (void);
// 0x00000073 System.Void UnityEngine.Animations.AnimationRemoveScalePlayable::.cctor()
extern void AnimationRemoveScalePlayable__cctor_m00BDA26A606F919C65B0070AC3CE5EEB9EF5CCB0 (void);
// 0x00000074 System.Void UnityEngine.Animations.AnimationScriptPlayable::.ctor(UnityEngine.Playables.PlayableHandle)
extern void AnimationScriptPlayable__ctor_m0B751F7A7D28F59AADACE7C13704D653E0879C56 (void);
// 0x00000075 UnityEngine.Playables.PlayableHandle UnityEngine.Animations.AnimationScriptPlayable::GetHandle()
extern void AnimationScriptPlayable_GetHandle_mCEA7899E7E43FC2C73B3331AE27C289327F03B18 (void);
// 0x00000076 System.Boolean UnityEngine.Animations.AnimationScriptPlayable::Equals(UnityEngine.Animations.AnimationScriptPlayable)
extern void AnimationScriptPlayable_Equals_m1705DCC80312E3D34E17B32BDBAF4BBB78D435D8 (void);
// 0x00000077 System.Void UnityEngine.Animations.AnimationScriptPlayable::.cctor()
extern void AnimationScriptPlayable__cctor_m5053EBB494C061715867F16ADB7C83B6E86335B5 (void);
// 0x00000078 System.Void UnityEngine.Animations.AnimatorControllerPlayable::.ctor(UnityEngine.Playables.PlayableHandle)
extern void AnimatorControllerPlayable__ctor_mBD4E1368EB671F6349C5740B1BF131F97BD12CC8 (void);
// 0x00000079 UnityEngine.Playables.PlayableHandle UnityEngine.Animations.AnimatorControllerPlayable::GetHandle()
extern void AnimatorControllerPlayable_GetHandle_mBB2911E1B1867ED9C9080BEF16838119A51E0C0C (void);
// 0x0000007A System.Void UnityEngine.Animations.AnimatorControllerPlayable::SetHandle(UnityEngine.Playables.PlayableHandle)
extern void AnimatorControllerPlayable_SetHandle_m19111E2A65EDAB3382AC9BE815503459A495FF38 (void);
// 0x0000007B System.Boolean UnityEngine.Animations.AnimatorControllerPlayable::Equals(UnityEngine.Animations.AnimatorControllerPlayable)
extern void AnimatorControllerPlayable_Equals_m9D2F918EE07AE657A11C13F285317C05BB257730 (void);
// 0x0000007C System.Void UnityEngine.Animations.AnimatorControllerPlayable::.cctor()
extern void AnimatorControllerPlayable__cctor_m471F26CF59746CF3B10821C2D1B444C0818A2332 (void);
static Il2CppMethodPointer s_methodPointers[124] = 
{
	StateMachineBehaviour_OnStateEnter_m0B5055A01EEF9070E7611D3C3165AAA118D22953,
	StateMachineBehaviour_OnStateUpdate_m2FF9D5AD07DF99860C7B0033791FE08F2EF919F1,
	StateMachineBehaviour_OnStateExit_mE8EADFCEA482A101BF13AFB773A06C3C2C8B3208,
	StateMachineBehaviour_OnStateMove_mB51A6EA16DA5038BF7C4E46863C8ECA1338EFBDD,
	StateMachineBehaviour_OnStateIK_m2BB5A0CD4B083CCDFAC7EE2F8233D2B11825197F,
	StateMachineBehaviour_OnStateMachineEnter_m8696CC6EE9DC7577A07023F84DCF6E4F80E75ACC,
	StateMachineBehaviour_OnStateMachineExit_m7FD170C30229751A93F64C26AFFF9C9BA057BF3D,
	StateMachineBehaviour_OnStateEnter_m0027D5548D58C0E2777A4CF9420F015FD56CEC18,
	StateMachineBehaviour_OnStateUpdate_mF81F7D0AB02EB31012A7C50E75295C40301A5055,
	StateMachineBehaviour_OnStateExit_m795DAE1099CF045D5E61ABBBAD017455F48B0707,
	StateMachineBehaviour_OnStateMove_m29F850CE0258906408520515E4E157D43AFEB181,
	StateMachineBehaviour_OnStateIK_m96E18AE1A75046F85EB7FEB5C05CEC7377F72C1F,
	StateMachineBehaviour_OnStateMachineEnter_mF2DF6E7A25D30F05E99984F3E8D4083D695F23CA,
	StateMachineBehaviour_OnStateMachineExit_m8AC70A1160FE329D0E1EC31F08B1E85B59DB516D,
	StateMachineBehaviour__ctor_mDB0650FD738799E5880150E656D4A88524D0EBE0,
	Animation_Stop_m06283FD9F64A3B05A2A248AE2B86C7F88D479DE9,
	Animation_Sample_mC2F1CF215D835404E205F52DD8096B74EEE3E9A1,
	Animation_get_isPlaying_m71AF60BE632978C6449B57D0AFA59CBF9A3F0705,
	Animation_IsPlaying_mD156265199E5875D1DFABCF82819BC4735E34887,
	Animation_Play_m5588607899B9B866117A1477C696076F161BA3D4,
	Animation_Play_mE768E71A80625EBCE4577FA5F82E2A6E6FE4F9F8,
	Animation_PlayDefaultAnimation_mF95A24563D8A4517F75A8149D0961C2024DE8DAB,
	Animation_Play_m8EDFE80589A27DF1C34CCC0CF81DB5313CE35607,
	Animation_Play_m8F742F537EA22D32B109BB0B57F54315BA2182A1,
	Animation_CrossFade_mAA2C1F467BCFF4AFF6B06D041BA98A9CBD57352B,
	Animation_CrossFade_m7D83F71B3DCF3D5F8995389669800718C988FA1A,
	Animation_CrossFade_m3FA2A5C413E36D5463FEDDFFD87582867F2ED65E,
	Animation_GetEnumerator_m7F0049CCE49D9F5963AFB5552CDEAD51793891F7,
	Animation_GetStateAtIndex_m436707B278555E5B3AD4FB8F7B1A04F532CDC679,
	Animation_GetStateCount_mBFB004C6182E7243A2DFA09D7E378A94AC61AABA,
	Enumerator__ctor_mC97A2B142004D7B757AAF61F6F069FB102372DBA,
	Enumerator_get_Current_mAB7913C42CA7EB7EFE4AE1F7B9BEAA6B5F44C1A9,
	Enumerator_MoveNext_m46EE9A2B09C10531425A6CFA6FD305CCDB4B3101,
	Enumerator_Reset_mFC24132D3D5F334C8892F799D3B8B92971CBE6C0,
	AnimationState_get_time_m116DAEA9256A49DF4E507AD604B2E1975AD86FFC,
	AnimationState_set_time_m5A1281D5FD5A5D344CC4FC7966F8E6867AD53B20,
	AnimationState_get_speed_mA4174B85772BD949608C6DBE9EC3FB8AED298B97,
	AnimationState_set_speed_m54AF12C004F2253228DA25B9E21774184A77271D,
	AnimationState_get_length_m7CF9F4E73761BA6921103F5293AF031E8A6BAEB7,
	AnimationState_set_layer_mBDCC887D4763B0CA1B1C1A3C1147E01AA0CF7BD2,
	AnimationState_get_clip_m84FD95AFB1FDC356E53AA6F44089F69B353B42BB,
	AnimationState_get_name_m0967781DD92AB4E35E17DD45136E46BAB3C57E9E,
	AnimationState__ctor_m5ECFF9F10ACFB838138F1E33E3D55EEAF891D217,
	AnimationEvent__ctor_mA2780A113EA8DD56C3C2EDD0D60BBA78047BACDE,
	AnimationClip__ctor_m1678D7A0D58F768C92690BABB01DBDE64F12D09E,
	AnimationClip_Internal_CreateAnimationClip_m1086DC82A4506493D813B97851216A01F027EB75,
	AnimationClip_get_length_m7917C4C333DD8083A5395581652C227E0CECC82B,
	AnimatorClipInfo_get_clip_m0822D4BB447803A294410A319C812D2D4B946A95,
	AnimatorClipInfo_get_weight_mF22612DA966F5D6C8EC93E6AD2E05DFE10B36CCA,
	AnimatorClipInfo_InstanceIDToAnimationClipPPtr_m9EF0945EABA33C718853712B00E23EF2AAC2084A,
	AnimatorStateInfo_get_fullPathHash_m296D315AB1FBF6177A423298296CECC1DBA7221D,
	AnimatorStateInfo_get_normalizedTime_mC951C5D83749FC2AE37DCC75A022383C578F3B40,
	AnimatorStateInfo_get_speed_m1B2CAB95244A0ECCE42F79CCFC22BA7CB8618843,
	AnimatorStateInfo_get_loop_mC5EDC24973284F2976E57A6558DBD360820FEED0,
	Animator_SetBool_m34E2E9785A47A3AE94E804004425C333C36CCD43,
	Animator_SetInteger_mFB04A03AF6C24978BF2BDE9161243F8F6B9762C5,
	Animator_SetTrigger_m2D79D155CABD81B1CC75EFC35D90508B58D7211C,
	Animator_ResetTrigger_m02F8CF7EABE466CC3D008A8538171E14BFB907FA,
	Animator_get_layerCount_m9F7ED8546CE9F7A507C88C577919A9C5E4DFE4F6,
	Animator_GetLayerWeight_m0A73AC322BA23FD164856B7FF67A3A0B748A1CF4,
	Animator_GetAnimatorStateInfo_m623172399D38470B4C5EB7DE06A3106AB6958657,
	Animator_GetCurrentAnimatorStateInfo_m562250C74BF8C626B5227FE840D6CB739B5F8314,
	Animator_GetNextAnimatorStateInfo_m0CE4DD9BD652C6C8C691C308FF86ACF650A91C62,
	Animator_GetAnimatorClipInfoCount_m80A50EDDEAFD721E18702F7F0706552A3C883D3B,
	Animator_GetCurrentAnimatorClipInfoCount_m966D03992968F33EAA0EC9456AE60E18424621BE,
	Animator_GetNextAnimatorClipInfoCount_mB69FE8373F6D7BD95162DB6A583818DD24C83A02,
	Animator_GetCurrentAnimatorClipInfo_mA1061569ED175C2B54A3273EDB889E9D48DE5A2C,
	Animator_GetAnimatorClipInfoInternal_m7369503B85FAE6630B97209D4F3A7492BD31EA39,
	Animator_GetNextAnimatorClipInfo_mB5F0367EAC2C788CD5DB486C511B4F783339C066,
	Animator_set_speed_m632FF62E0D6B668C55190B3579B12684316C4041,
	Animator_Play_mE5E8B1753FFDF754EAD1ACEFF6C5B6ACA506363C,
	Animator_Play_m1438EDACA2804B50ED0D00D9986E30BCF903418B,
	Animator_Play_m392289975B858ED7D85DF6E7980E5BA47F606AEC,
	Animator_get_runtimeAnimatorController_mA31EAB2AB6CA23B48FA70155CA399DFA2EE525CC,
	Animator_set_runtimeAnimatorController_mF50ED3D400626A3E3FC42BB6F0772A6CADEEC63B,
	Animator_get_hasBoundPlayables_m1ADEF28BC77A4C8DBC707DA02A1B72E00AC0C88A,
	Animator_StringToHash_mC9D8C2AB862E0C24670267F55A28DE94B23A7511,
	Animator_SetBoolString_mB0D21540179FFB9E5F1B2C2EF008BD0595B78BA7,
	Animator_SetIntegerString_m98F3F5AF11579CA8F0DD2C43625CBE6258999CB9,
	Animator_SetTriggerString_m38F66A49276BCED56B89BB6AF8A36183BE4285F0,
	Animator_ResetTriggerString_m6FC21A6B7732A31338EE22E78F3D6220903EDBB2,
	Animator_Update_mBA8EC912BA18CB8718B4A4CCB4F18A0639C36E0D,
	AnimatorOverrideController_OnInvalidateOverrideController_mB6D3256DA021EADE4D68F303C3C4146F55D45DAD,
	OnOverrideControllerDirtyCallback__ctor_mA35F55BEB8A4BD57D109684E857F85C1F0A6C1B5,
	OnOverrideControllerDirtyCallback_Invoke_m21DB79300E852ED93F2521FFC03EC4D858F6B330,
	OnOverrideControllerDirtyCallback_BeginInvoke_m40D5810BF8C5066DB2C7987E7C76FD23D2AC47E3,
	OnOverrideControllerDirtyCallback_EndInvoke_mAF3C7805FADE63999DC2121032B11DF86668E9F4,
	Motion__ctor_mDC0BDF6F52E99C4A69EFDAC157CC29D0C1A4F9E6,
	RuntimeAnimatorController__ctor_m974DD22EB55FCAE168AE41718C2BD48B008C7CB3,
	AnimationClipPlayable_GetHandle_m93C27911A3C7107750C2A6BE529C58FB2FDB1122,
	AnimationClipPlayable_Equals_m73BDBE0839B6AA4782C37B21DD58D3388B5EC814,
	AnimationLayerMixerPlayable__ctor_m42F8E5BB37A175AF298324D3072932ED9946427B,
	AnimationLayerMixerPlayable_GetHandle_mBFA950F140D76E10983B9AB946397F4C12ABC439,
	AnimationLayerMixerPlayable_Equals_m018BD27B24B3EDC5101A475A14F13F753F2323AA,
	AnimationLayerMixerPlayable__cctor_m098B488257BF0185BD8C01EFAB03D2C54A19D31B,
	AnimationMixerPlayable__ctor_mA03CF37709B7854227E25F91BE4F7559981058B0,
	AnimationMixerPlayable_GetHandle_mE8F7D1A18F1BD1C00BA1EC6AA8036044E8907FC3,
	AnimationMixerPlayable_Equals_m8979D90ED92FF553B5D6AB0BDD616C544352816B,
	AnimationMixerPlayable__cctor_m43D5D606D27FC8A55EFDBBC107B14C27BCC2C2D5,
	AnimationMotionXToDeltaPlayable__ctor_m11668860161B62484EA095BD6360AFD26A86DE93,
	AnimationMotionXToDeltaPlayable_GetHandle_m840D19A4E2DFB4BF2397061B833E63AD786587BA,
	AnimationMotionXToDeltaPlayable_Equals_mB08A41C628755AF909489716A1D62AECC2BFDD9E,
	AnimationMotionXToDeltaPlayable__cctor_m56F27AA05BD378D0320BFEEBC8A4AB53C686D7F5,
	AnimationOffsetPlayable__ctor_m9527E52AEA325EAE188AB9843497F2AB33CB742E,
	AnimationOffsetPlayable_GetHandle_m8C3C08EC531127B002D3AFAB5AF259D8030B0049,
	AnimationOffsetPlayable_Equals_m9AFE60B035481569924E20C6953B4B21EF7734AA,
	AnimationOffsetPlayable__cctor_m8B28D6F560CE5715B9DCA320EEADD5220ABFAF84,
	AnimationPosePlayable__ctor_m318607F120F21EDE3D7C1ED07C8B2ED13A23BF57,
	AnimationPosePlayable_GetHandle_m0354C54EB680FC70D4B48D95F7FC4BA4700A0DCE,
	AnimationPosePlayable_Equals_mECC5FA256AAA5334C38DBB6D00EE8AC1BDC015A1,
	AnimationPosePlayable__cctor_mED38E3FFBD9F82C85C65826E83984D627B4C5058,
	AnimationRemoveScalePlayable__ctor_m08810C8ECE9A3A100087DD84B13204EC3AF73A8F,
	AnimationRemoveScalePlayable_GetHandle_m1949202B58BDF17726A1ADC934EB5232E835CCA8,
	AnimationRemoveScalePlayable_Equals_m7FE9E55B027861A0B91347F18DAC7E11E2740397,
	AnimationRemoveScalePlayable__cctor_m00BDA26A606F919C65B0070AC3CE5EEB9EF5CCB0,
	AnimationScriptPlayable__ctor_m0B751F7A7D28F59AADACE7C13704D653E0879C56,
	AnimationScriptPlayable_GetHandle_mCEA7899E7E43FC2C73B3331AE27C289327F03B18,
	AnimationScriptPlayable_Equals_m1705DCC80312E3D34E17B32BDBAF4BBB78D435D8,
	AnimationScriptPlayable__cctor_m5053EBB494C061715867F16ADB7C83B6E86335B5,
	AnimatorControllerPlayable__ctor_mBD4E1368EB671F6349C5740B1BF131F97BD12CC8,
	AnimatorControllerPlayable_GetHandle_mBB2911E1B1867ED9C9080BEF16838119A51E0C0C,
	AnimatorControllerPlayable_SetHandle_m19111E2A65EDAB3382AC9BE815503459A495FF38,
	AnimatorControllerPlayable_Equals_m9D2F918EE07AE657A11C13F285317C05BB257730,
	AnimatorControllerPlayable__cctor_m471F26CF59746CF3B10821C2D1B444C0818A2332,
};
extern void AnimatorClipInfo_get_clip_m0822D4BB447803A294410A319C812D2D4B946A95_AdjustorThunk (void);
extern void AnimatorClipInfo_get_weight_mF22612DA966F5D6C8EC93E6AD2E05DFE10B36CCA_AdjustorThunk (void);
extern void AnimatorStateInfo_get_fullPathHash_m296D315AB1FBF6177A423298296CECC1DBA7221D_AdjustorThunk (void);
extern void AnimatorStateInfo_get_normalizedTime_mC951C5D83749FC2AE37DCC75A022383C578F3B40_AdjustorThunk (void);
extern void AnimatorStateInfo_get_speed_m1B2CAB95244A0ECCE42F79CCFC22BA7CB8618843_AdjustorThunk (void);
extern void AnimatorStateInfo_get_loop_mC5EDC24973284F2976E57A6558DBD360820FEED0_AdjustorThunk (void);
extern void AnimationClipPlayable_GetHandle_m93C27911A3C7107750C2A6BE529C58FB2FDB1122_AdjustorThunk (void);
extern void AnimationClipPlayable_Equals_m73BDBE0839B6AA4782C37B21DD58D3388B5EC814_AdjustorThunk (void);
extern void AnimationLayerMixerPlayable__ctor_m42F8E5BB37A175AF298324D3072932ED9946427B_AdjustorThunk (void);
extern void AnimationLayerMixerPlayable_GetHandle_mBFA950F140D76E10983B9AB946397F4C12ABC439_AdjustorThunk (void);
extern void AnimationLayerMixerPlayable_Equals_m018BD27B24B3EDC5101A475A14F13F753F2323AA_AdjustorThunk (void);
extern void AnimationMixerPlayable__ctor_mA03CF37709B7854227E25F91BE4F7559981058B0_AdjustorThunk (void);
extern void AnimationMixerPlayable_GetHandle_mE8F7D1A18F1BD1C00BA1EC6AA8036044E8907FC3_AdjustorThunk (void);
extern void AnimationMixerPlayable_Equals_m8979D90ED92FF553B5D6AB0BDD616C544352816B_AdjustorThunk (void);
extern void AnimationMotionXToDeltaPlayable__ctor_m11668860161B62484EA095BD6360AFD26A86DE93_AdjustorThunk (void);
extern void AnimationMotionXToDeltaPlayable_GetHandle_m840D19A4E2DFB4BF2397061B833E63AD786587BA_AdjustorThunk (void);
extern void AnimationMotionXToDeltaPlayable_Equals_mB08A41C628755AF909489716A1D62AECC2BFDD9E_AdjustorThunk (void);
extern void AnimationOffsetPlayable__ctor_m9527E52AEA325EAE188AB9843497F2AB33CB742E_AdjustorThunk (void);
extern void AnimationOffsetPlayable_GetHandle_m8C3C08EC531127B002D3AFAB5AF259D8030B0049_AdjustorThunk (void);
extern void AnimationOffsetPlayable_Equals_m9AFE60B035481569924E20C6953B4B21EF7734AA_AdjustorThunk (void);
extern void AnimationPosePlayable__ctor_m318607F120F21EDE3D7C1ED07C8B2ED13A23BF57_AdjustorThunk (void);
extern void AnimationPosePlayable_GetHandle_m0354C54EB680FC70D4B48D95F7FC4BA4700A0DCE_AdjustorThunk (void);
extern void AnimationPosePlayable_Equals_mECC5FA256AAA5334C38DBB6D00EE8AC1BDC015A1_AdjustorThunk (void);
extern void AnimationRemoveScalePlayable__ctor_m08810C8ECE9A3A100087DD84B13204EC3AF73A8F_AdjustorThunk (void);
extern void AnimationRemoveScalePlayable_GetHandle_m1949202B58BDF17726A1ADC934EB5232E835CCA8_AdjustorThunk (void);
extern void AnimationRemoveScalePlayable_Equals_m7FE9E55B027861A0B91347F18DAC7E11E2740397_AdjustorThunk (void);
extern void AnimationScriptPlayable__ctor_m0B751F7A7D28F59AADACE7C13704D653E0879C56_AdjustorThunk (void);
extern void AnimationScriptPlayable_GetHandle_mCEA7899E7E43FC2C73B3331AE27C289327F03B18_AdjustorThunk (void);
extern void AnimationScriptPlayable_Equals_m1705DCC80312E3D34E17B32BDBAF4BBB78D435D8_AdjustorThunk (void);
extern void AnimatorControllerPlayable__ctor_mBD4E1368EB671F6349C5740B1BF131F97BD12CC8_AdjustorThunk (void);
extern void AnimatorControllerPlayable_GetHandle_mBB2911E1B1867ED9C9080BEF16838119A51E0C0C_AdjustorThunk (void);
extern void AnimatorControllerPlayable_SetHandle_m19111E2A65EDAB3382AC9BE815503459A495FF38_AdjustorThunk (void);
extern void AnimatorControllerPlayable_Equals_m9D2F918EE07AE657A11C13F285317C05BB257730_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[33] = 
{
	{ 0x06000030, AnimatorClipInfo_get_clip_m0822D4BB447803A294410A319C812D2D4B946A95_AdjustorThunk },
	{ 0x06000031, AnimatorClipInfo_get_weight_mF22612DA966F5D6C8EC93E6AD2E05DFE10B36CCA_AdjustorThunk },
	{ 0x06000033, AnimatorStateInfo_get_fullPathHash_m296D315AB1FBF6177A423298296CECC1DBA7221D_AdjustorThunk },
	{ 0x06000034, AnimatorStateInfo_get_normalizedTime_mC951C5D83749FC2AE37DCC75A022383C578F3B40_AdjustorThunk },
	{ 0x06000035, AnimatorStateInfo_get_speed_m1B2CAB95244A0ECCE42F79CCFC22BA7CB8618843_AdjustorThunk },
	{ 0x06000036, AnimatorStateInfo_get_loop_mC5EDC24973284F2976E57A6558DBD360820FEED0_AdjustorThunk },
	{ 0x0600005A, AnimationClipPlayable_GetHandle_m93C27911A3C7107750C2A6BE529C58FB2FDB1122_AdjustorThunk },
	{ 0x0600005B, AnimationClipPlayable_Equals_m73BDBE0839B6AA4782C37B21DD58D3388B5EC814_AdjustorThunk },
	{ 0x0600005C, AnimationLayerMixerPlayable__ctor_m42F8E5BB37A175AF298324D3072932ED9946427B_AdjustorThunk },
	{ 0x0600005D, AnimationLayerMixerPlayable_GetHandle_mBFA950F140D76E10983B9AB946397F4C12ABC439_AdjustorThunk },
	{ 0x0600005E, AnimationLayerMixerPlayable_Equals_m018BD27B24B3EDC5101A475A14F13F753F2323AA_AdjustorThunk },
	{ 0x06000060, AnimationMixerPlayable__ctor_mA03CF37709B7854227E25F91BE4F7559981058B0_AdjustorThunk },
	{ 0x06000061, AnimationMixerPlayable_GetHandle_mE8F7D1A18F1BD1C00BA1EC6AA8036044E8907FC3_AdjustorThunk },
	{ 0x06000062, AnimationMixerPlayable_Equals_m8979D90ED92FF553B5D6AB0BDD616C544352816B_AdjustorThunk },
	{ 0x06000064, AnimationMotionXToDeltaPlayable__ctor_m11668860161B62484EA095BD6360AFD26A86DE93_AdjustorThunk },
	{ 0x06000065, AnimationMotionXToDeltaPlayable_GetHandle_m840D19A4E2DFB4BF2397061B833E63AD786587BA_AdjustorThunk },
	{ 0x06000066, AnimationMotionXToDeltaPlayable_Equals_mB08A41C628755AF909489716A1D62AECC2BFDD9E_AdjustorThunk },
	{ 0x06000068, AnimationOffsetPlayable__ctor_m9527E52AEA325EAE188AB9843497F2AB33CB742E_AdjustorThunk },
	{ 0x06000069, AnimationOffsetPlayable_GetHandle_m8C3C08EC531127B002D3AFAB5AF259D8030B0049_AdjustorThunk },
	{ 0x0600006A, AnimationOffsetPlayable_Equals_m9AFE60B035481569924E20C6953B4B21EF7734AA_AdjustorThunk },
	{ 0x0600006C, AnimationPosePlayable__ctor_m318607F120F21EDE3D7C1ED07C8B2ED13A23BF57_AdjustorThunk },
	{ 0x0600006D, AnimationPosePlayable_GetHandle_m0354C54EB680FC70D4B48D95F7FC4BA4700A0DCE_AdjustorThunk },
	{ 0x0600006E, AnimationPosePlayable_Equals_mECC5FA256AAA5334C38DBB6D00EE8AC1BDC015A1_AdjustorThunk },
	{ 0x06000070, AnimationRemoveScalePlayable__ctor_m08810C8ECE9A3A100087DD84B13204EC3AF73A8F_AdjustorThunk },
	{ 0x06000071, AnimationRemoveScalePlayable_GetHandle_m1949202B58BDF17726A1ADC934EB5232E835CCA8_AdjustorThunk },
	{ 0x06000072, AnimationRemoveScalePlayable_Equals_m7FE9E55B027861A0B91347F18DAC7E11E2740397_AdjustorThunk },
	{ 0x06000074, AnimationScriptPlayable__ctor_m0B751F7A7D28F59AADACE7C13704D653E0879C56_AdjustorThunk },
	{ 0x06000075, AnimationScriptPlayable_GetHandle_mCEA7899E7E43FC2C73B3331AE27C289327F03B18_AdjustorThunk },
	{ 0x06000076, AnimationScriptPlayable_Equals_m1705DCC80312E3D34E17B32BDBAF4BBB78D435D8_AdjustorThunk },
	{ 0x06000078, AnimatorControllerPlayable__ctor_mBD4E1368EB671F6349C5740B1BF131F97BD12CC8_AdjustorThunk },
	{ 0x06000079, AnimatorControllerPlayable_GetHandle_mBB2911E1B1867ED9C9080BEF16838119A51E0C0C_AdjustorThunk },
	{ 0x0600007A, AnimatorControllerPlayable_SetHandle_m19111E2A65EDAB3382AC9BE815503459A495FF38_AdjustorThunk },
	{ 0x0600007B, AnimatorControllerPlayable_Equals_m9D2F918EE07AE657A11C13F285317C05BB257730_AdjustorThunk },
};
static const int32_t s_InvokerIndices[124] = 
{
	1309,
	1309,
	1309,
	1309,
	1309,
	2167,
	2167,
	858,
	858,
	858,
	858,
	858,
	1322,
	1322,
	3967,
	3967,
	3967,
	3863,
	2591,
	3863,
	2570,
	2570,
	2591,
	1474,
	3348,
	2179,
	1346,
	3916,
	3044,
	3893,
	3348,
	3916,
	3863,
	3967,
	3951,
	3380,
	3951,
	3380,
	3951,
	3313,
	3916,
	3916,
	3967,
	3967,
	3967,
	6218,
	3951,
	3916,
	3951,
	6088,
	3893,
	3951,
	3951,
	3863,
	2158,
	2167,
	3348,
	3348,
	3893,
	3115,
	1276,
	2372,
	2372,
	1592,
	2884,
	2884,
	1981,
	1274,
	1981,
	3380,
	3348,
	1327,
	1282,
	3916,
	3348,
	3863,
	6005,
	2158,
	2167,
	3348,
	3348,
	3380,
	6218,
	2169,
	3967,
	1717,
	3348,
	3967,
	3967,
	3922,
	2512,
	3355,
	3922,
	2513,
	6289,
	3355,
	3922,
	2514,
	6289,
	3355,
	3922,
	2515,
	6289,
	3355,
	3922,
	2516,
	6289,
	3355,
	3922,
	2517,
	6289,
	3355,
	3922,
	2518,
	6289,
	3355,
	3922,
	2519,
	6289,
	3355,
	3922,
	3355,
	2521,
	6289,
};
extern const CustomAttributesCacheGenerator g_UnityEngine_AnimationModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_AnimationModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_AnimationModule_CodeGenModule = 
{
	"UnityEngine.AnimationModule.dll",
	124,
	s_methodPointers,
	33,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_UnityEngine_AnimationModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

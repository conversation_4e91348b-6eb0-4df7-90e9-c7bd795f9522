﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>
#include <stdint.h>



// System.Action`1<UnityEngine.VFX.VFXOutputEventArgs>
struct Action_1_t5C8C9298698F95A378E73C4584F33A97EF82A064;
// System.Delegate[]
struct DelegateU5BU5D_t677D8FE08A5F99E8EE49150B73966CD6E9BF7DB8;
// System.IntPtr[]
struct IntPtrU5BU5D_t27FC72B0409D75AAF33EC42498E8094E95FEE9A6;
// System.Diagnostics.StackTrace[]
struct StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971;
// System.AsyncCallback
struct AsyncCallback_tA7921BEF974919C46FF8F9D9867C567B200BB0EA;
// System.DelegateData
struct DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288;
// System.Exception
struct Exception_t;
// System.IAsyncResult
struct IAsyncResult_tC9F97BF36FCF122D29D3101D80642278297BF370;
// System.Collections.IDictionary
struct IDictionary_t99871C56B8EC2452AC5C4CF3831695E617B89D3A;
// System.Reflection.MethodInfo
struct MethodInfo_t;
// UnityEngine.Object
struct Object_tF2F3778131EFF286AF62B7B013A170F95A91571A;
// System.Runtime.Serialization.SafeSerializationManager
struct SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F;
// UnityEngine.ScriptableObject
struct ScriptableObject_t4361E08CEBF052C650D3666C7CEC37EB31DE116A;
// System.String
struct String_t;
// UnityEngine.VFX.VFXEventAttribute
struct VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF;
// UnityEngine.VFX.VFXExpressionValues
struct VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883;
// UnityEngine.VFX.VFXSpawnerCallbacks
struct VFXSpawnerCallbacks_t62128B7E3ADA64EBEA4705691DE0F045104801CA;
// UnityEngine.VFX.VFXSpawnerState
struct VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD;
// UnityEngine.VFX.VisualEffect
struct VisualEffect_t7C6E2AAA4DB4F47960AF2029EA96D4B579B3A4CA;
// UnityEngine.VFX.VisualEffectAsset
struct VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50;
// UnityEngine.VFX.VisualEffectObject
struct VisualEffectObject_tC7804AFDC2B4F2F0CE6833AC467ABC177A1617DB;
// System.Void
struct Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5;

IL2CPP_EXTERN_C RuntimeClass* Exception_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* GC_tD6F0377620BF01385965FD29272CF088A4309C0D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IntPtr_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral55CE32F73183782A9E356F9B9E36223057BA0BA3;
IL2CPP_EXTERN_C String_t* _stringLiteralC2149138D4858815F0CE2A684D1424F63B8B965D;
IL2CPP_EXTERN_C String_t* _stringLiteralDD6A1C41DBA2920AB3C87C187F0272E6380C0AAA;
IL2CPP_EXTERN_C const RuntimeMethod* Action_1_Invoke_m253872B5D1C80AFE87652D0B1DDBF95AC54E702D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* VFXSpawnerState_SetWrapValue_mA673646567D2B54072EEC275FB79BEBF82C7EE31_RuntimeMethod_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;
struct VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF;;
struct VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshaled_com;
struct VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshaled_com;;
struct VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshaled_pinvoke;
struct VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshaled_pinvoke;;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif

// <Module>
struct U3CModuleU3E_t9BC900AC115CCA160074141915B355D8F694FB1F 
{
public:

public:
};


// System.Object

struct Il2CppArrayBounds;

// System.Array


// System.String
struct String_t  : public RuntimeObject
{
public:
	// System.Int32 System.String::m_stringLength
	int32_t ___m_stringLength_0;
	// System.Char System.String::m_firstChar
	Il2CppChar ___m_firstChar_1;

public:
	inline static int32_t get_offset_of_m_stringLength_0() { return static_cast<int32_t>(offsetof(String_t, ___m_stringLength_0)); }
	inline int32_t get_m_stringLength_0() const { return ___m_stringLength_0; }
	inline int32_t* get_address_of_m_stringLength_0() { return &___m_stringLength_0; }
	inline void set_m_stringLength_0(int32_t value)
	{
		___m_stringLength_0 = value;
	}

	inline static int32_t get_offset_of_m_firstChar_1() { return static_cast<int32_t>(offsetof(String_t, ___m_firstChar_1)); }
	inline Il2CppChar get_m_firstChar_1() const { return ___m_firstChar_1; }
	inline Il2CppChar* get_address_of_m_firstChar_1() { return &___m_firstChar_1; }
	inline void set_m_firstChar_1(Il2CppChar value)
	{
		___m_firstChar_1 = value;
	}
};


// UnityEngine.VFX.VFXManager
struct VFXManager_tE525803FFE4F59D87D5B5E61AF8433037F226340  : public RuntimeObject
{
public:

public:
};


// System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52  : public RuntimeObject
{
public:

public:
};

// Native definition for P/Invoke marshalling of System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshaled_com
{
};

// System.Boolean
struct Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37 
{
public:
	// System.Boolean System.Boolean::m_value
	bool ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37, ___m_value_0)); }
	inline bool get_m_value_0() const { return ___m_value_0; }
	inline bool* get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(bool value)
	{
		___m_value_0 = value;
	}
};


// System.Int32
struct Int32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046 
{
public:
	// System.Int32 System.Int32::m_value
	int32_t ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(Int32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046, ___m_value_0)); }
	inline int32_t get_m_value_0() const { return ___m_value_0; }
	inline int32_t* get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(int32_t value)
	{
		___m_value_0 = value;
	}
};


// System.IntPtr
struct IntPtr_t 
{
public:
	// System.Void* System.IntPtr::m_value
	void* ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(IntPtr_t, ___m_value_0)); }
	inline void* get_m_value_0() const { return ___m_value_0; }
	inline void** get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(void* value)
	{
		___m_value_0 = value;
	}
};


// UnityEngine.VFX.VFXOutputEventArgs
struct VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5 
{
public:
	// System.Int32 UnityEngine.VFX.VFXOutputEventArgs::<nameId>k__BackingField
	int32_t ___U3CnameIdU3Ek__BackingField_0;
	// UnityEngine.VFX.VFXEventAttribute UnityEngine.VFX.VFXOutputEventArgs::<eventAttribute>k__BackingField
	VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * ___U3CeventAttributeU3Ek__BackingField_1;

public:
	inline static int32_t get_offset_of_U3CnameIdU3Ek__BackingField_0() { return static_cast<int32_t>(offsetof(VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5, ___U3CnameIdU3Ek__BackingField_0)); }
	inline int32_t get_U3CnameIdU3Ek__BackingField_0() const { return ___U3CnameIdU3Ek__BackingField_0; }
	inline int32_t* get_address_of_U3CnameIdU3Ek__BackingField_0() { return &___U3CnameIdU3Ek__BackingField_0; }
	inline void set_U3CnameIdU3Ek__BackingField_0(int32_t value)
	{
		___U3CnameIdU3Ek__BackingField_0 = value;
	}

	inline static int32_t get_offset_of_U3CeventAttributeU3Ek__BackingField_1() { return static_cast<int32_t>(offsetof(VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5, ___U3CeventAttributeU3Ek__BackingField_1)); }
	inline VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * get_U3CeventAttributeU3Ek__BackingField_1() const { return ___U3CeventAttributeU3Ek__BackingField_1; }
	inline VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF ** get_address_of_U3CeventAttributeU3Ek__BackingField_1() { return &___U3CeventAttributeU3Ek__BackingField_1; }
	inline void set_U3CeventAttributeU3Ek__BackingField_1(VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * value)
	{
		___U3CeventAttributeU3Ek__BackingField_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___U3CeventAttributeU3Ek__BackingField_1), (void*)value);
	}
};

// Native definition for P/Invoke marshalling of UnityEngine.VFX.VFXOutputEventArgs
struct VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5_marshaled_pinvoke
{
	int32_t ___U3CnameIdU3Ek__BackingField_0;
	VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshaled_pinvoke* ___U3CeventAttributeU3Ek__BackingField_1;
};
// Native definition for COM marshalling of UnityEngine.VFX.VFXOutputEventArgs
struct VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5_marshaled_com
{
	int32_t ___U3CnameIdU3Ek__BackingField_0;
	VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshaled_com* ___U3CeventAttributeU3Ek__BackingField_1;
};

// System.Void
struct Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5 
{
public:
	union
	{
		struct
		{
		};
		uint8_t Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5__padding[1];
	};

public:
};


// System.Delegate
struct Delegate_t  : public RuntimeObject
{
public:
	// System.IntPtr System.Delegate::method_ptr
	Il2CppMethodPointer ___method_ptr_0;
	// System.IntPtr System.Delegate::invoke_impl
	intptr_t ___invoke_impl_1;
	// System.Object System.Delegate::m_target
	RuntimeObject * ___m_target_2;
	// System.IntPtr System.Delegate::method
	intptr_t ___method_3;
	// System.IntPtr System.Delegate::delegate_trampoline
	intptr_t ___delegate_trampoline_4;
	// System.IntPtr System.Delegate::extra_arg
	intptr_t ___extra_arg_5;
	// System.IntPtr System.Delegate::method_code
	intptr_t ___method_code_6;
	// System.Reflection.MethodInfo System.Delegate::method_info
	MethodInfo_t * ___method_info_7;
	// System.Reflection.MethodInfo System.Delegate::original_method_info
	MethodInfo_t * ___original_method_info_8;
	// System.DelegateData System.Delegate::data
	DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 * ___data_9;
	// System.Boolean System.Delegate::method_is_virtual
	bool ___method_is_virtual_10;

public:
	inline static int32_t get_offset_of_method_ptr_0() { return static_cast<int32_t>(offsetof(Delegate_t, ___method_ptr_0)); }
	inline Il2CppMethodPointer get_method_ptr_0() const { return ___method_ptr_0; }
	inline Il2CppMethodPointer* get_address_of_method_ptr_0() { return &___method_ptr_0; }
	inline void set_method_ptr_0(Il2CppMethodPointer value)
	{
		___method_ptr_0 = value;
	}

	inline static int32_t get_offset_of_invoke_impl_1() { return static_cast<int32_t>(offsetof(Delegate_t, ___invoke_impl_1)); }
	inline intptr_t get_invoke_impl_1() const { return ___invoke_impl_1; }
	inline intptr_t* get_address_of_invoke_impl_1() { return &___invoke_impl_1; }
	inline void set_invoke_impl_1(intptr_t value)
	{
		___invoke_impl_1 = value;
	}

	inline static int32_t get_offset_of_m_target_2() { return static_cast<int32_t>(offsetof(Delegate_t, ___m_target_2)); }
	inline RuntimeObject * get_m_target_2() const { return ___m_target_2; }
	inline RuntimeObject ** get_address_of_m_target_2() { return &___m_target_2; }
	inline void set_m_target_2(RuntimeObject * value)
	{
		___m_target_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_target_2), (void*)value);
	}

	inline static int32_t get_offset_of_method_3() { return static_cast<int32_t>(offsetof(Delegate_t, ___method_3)); }
	inline intptr_t get_method_3() const { return ___method_3; }
	inline intptr_t* get_address_of_method_3() { return &___method_3; }
	inline void set_method_3(intptr_t value)
	{
		___method_3 = value;
	}

	inline static int32_t get_offset_of_delegate_trampoline_4() { return static_cast<int32_t>(offsetof(Delegate_t, ___delegate_trampoline_4)); }
	inline intptr_t get_delegate_trampoline_4() const { return ___delegate_trampoline_4; }
	inline intptr_t* get_address_of_delegate_trampoline_4() { return &___delegate_trampoline_4; }
	inline void set_delegate_trampoline_4(intptr_t value)
	{
		___delegate_trampoline_4 = value;
	}

	inline static int32_t get_offset_of_extra_arg_5() { return static_cast<int32_t>(offsetof(Delegate_t, ___extra_arg_5)); }
	inline intptr_t get_extra_arg_5() const { return ___extra_arg_5; }
	inline intptr_t* get_address_of_extra_arg_5() { return &___extra_arg_5; }
	inline void set_extra_arg_5(intptr_t value)
	{
		___extra_arg_5 = value;
	}

	inline static int32_t get_offset_of_method_code_6() { return static_cast<int32_t>(offsetof(Delegate_t, ___method_code_6)); }
	inline intptr_t get_method_code_6() const { return ___method_code_6; }
	inline intptr_t* get_address_of_method_code_6() { return &___method_code_6; }
	inline void set_method_code_6(intptr_t value)
	{
		___method_code_6 = value;
	}

	inline static int32_t get_offset_of_method_info_7() { return static_cast<int32_t>(offsetof(Delegate_t, ___method_info_7)); }
	inline MethodInfo_t * get_method_info_7() const { return ___method_info_7; }
	inline MethodInfo_t ** get_address_of_method_info_7() { return &___method_info_7; }
	inline void set_method_info_7(MethodInfo_t * value)
	{
		___method_info_7 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___method_info_7), (void*)value);
	}

	inline static int32_t get_offset_of_original_method_info_8() { return static_cast<int32_t>(offsetof(Delegate_t, ___original_method_info_8)); }
	inline MethodInfo_t * get_original_method_info_8() const { return ___original_method_info_8; }
	inline MethodInfo_t ** get_address_of_original_method_info_8() { return &___original_method_info_8; }
	inline void set_original_method_info_8(MethodInfo_t * value)
	{
		___original_method_info_8 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___original_method_info_8), (void*)value);
	}

	inline static int32_t get_offset_of_data_9() { return static_cast<int32_t>(offsetof(Delegate_t, ___data_9)); }
	inline DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 * get_data_9() const { return ___data_9; }
	inline DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 ** get_address_of_data_9() { return &___data_9; }
	inline void set_data_9(DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 * value)
	{
		___data_9 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___data_9), (void*)value);
	}

	inline static int32_t get_offset_of_method_is_virtual_10() { return static_cast<int32_t>(offsetof(Delegate_t, ___method_is_virtual_10)); }
	inline bool get_method_is_virtual_10() const { return ___method_is_virtual_10; }
	inline bool* get_address_of_method_is_virtual_10() { return &___method_is_virtual_10; }
	inline void set_method_is_virtual_10(bool value)
	{
		___method_is_virtual_10 = value;
	}
};

// Native definition for P/Invoke marshalling of System.Delegate
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr_0;
	intptr_t ___invoke_impl_1;
	Il2CppIUnknown* ___m_target_2;
	intptr_t ___method_3;
	intptr_t ___delegate_trampoline_4;
	intptr_t ___extra_arg_5;
	intptr_t ___method_code_6;
	MethodInfo_t * ___method_info_7;
	MethodInfo_t * ___original_method_info_8;
	DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 * ___data_9;
	int32_t ___method_is_virtual_10;
};
// Native definition for COM marshalling of System.Delegate
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr_0;
	intptr_t ___invoke_impl_1;
	Il2CppIUnknown* ___m_target_2;
	intptr_t ___method_3;
	intptr_t ___delegate_trampoline_4;
	intptr_t ___extra_arg_5;
	intptr_t ___method_code_6;
	MethodInfo_t * ___method_info_7;
	MethodInfo_t * ___original_method_info_8;
	DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 * ___data_9;
	int32_t ___method_is_virtual_10;
};

// System.Exception
struct Exception_t  : public RuntimeObject
{
public:
	// System.String System.Exception::_className
	String_t* ____className_1;
	// System.String System.Exception::_message
	String_t* ____message_2;
	// System.Collections.IDictionary System.Exception::_data
	RuntimeObject* ____data_3;
	// System.Exception System.Exception::_innerException
	Exception_t * ____innerException_4;
	// System.String System.Exception::_helpURL
	String_t* ____helpURL_5;
	// System.Object System.Exception::_stackTrace
	RuntimeObject * ____stackTrace_6;
	// System.String System.Exception::_stackTraceString
	String_t* ____stackTraceString_7;
	// System.String System.Exception::_remoteStackTraceString
	String_t* ____remoteStackTraceString_8;
	// System.Int32 System.Exception::_remoteStackIndex
	int32_t ____remoteStackIndex_9;
	// System.Object System.Exception::_dynamicMethods
	RuntimeObject * ____dynamicMethods_10;
	// System.Int32 System.Exception::_HResult
	int32_t ____HResult_11;
	// System.String System.Exception::_source
	String_t* ____source_12;
	// System.Runtime.Serialization.SafeSerializationManager System.Exception::_safeSerializationManager
	SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F * ____safeSerializationManager_13;
	// System.Diagnostics.StackTrace[] System.Exception::captured_traces
	StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971* ___captured_traces_14;
	// System.IntPtr[] System.Exception::native_trace_ips
	IntPtrU5BU5D_t27FC72B0409D75AAF33EC42498E8094E95FEE9A6* ___native_trace_ips_15;

public:
	inline static int32_t get_offset_of__className_1() { return static_cast<int32_t>(offsetof(Exception_t, ____className_1)); }
	inline String_t* get__className_1() const { return ____className_1; }
	inline String_t** get_address_of__className_1() { return &____className_1; }
	inline void set__className_1(String_t* value)
	{
		____className_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____className_1), (void*)value);
	}

	inline static int32_t get_offset_of__message_2() { return static_cast<int32_t>(offsetof(Exception_t, ____message_2)); }
	inline String_t* get__message_2() const { return ____message_2; }
	inline String_t** get_address_of__message_2() { return &____message_2; }
	inline void set__message_2(String_t* value)
	{
		____message_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____message_2), (void*)value);
	}

	inline static int32_t get_offset_of__data_3() { return static_cast<int32_t>(offsetof(Exception_t, ____data_3)); }
	inline RuntimeObject* get__data_3() const { return ____data_3; }
	inline RuntimeObject** get_address_of__data_3() { return &____data_3; }
	inline void set__data_3(RuntimeObject* value)
	{
		____data_3 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____data_3), (void*)value);
	}

	inline static int32_t get_offset_of__innerException_4() { return static_cast<int32_t>(offsetof(Exception_t, ____innerException_4)); }
	inline Exception_t * get__innerException_4() const { return ____innerException_4; }
	inline Exception_t ** get_address_of__innerException_4() { return &____innerException_4; }
	inline void set__innerException_4(Exception_t * value)
	{
		____innerException_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____innerException_4), (void*)value);
	}

	inline static int32_t get_offset_of__helpURL_5() { return static_cast<int32_t>(offsetof(Exception_t, ____helpURL_5)); }
	inline String_t* get__helpURL_5() const { return ____helpURL_5; }
	inline String_t** get_address_of__helpURL_5() { return &____helpURL_5; }
	inline void set__helpURL_5(String_t* value)
	{
		____helpURL_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____helpURL_5), (void*)value);
	}

	inline static int32_t get_offset_of__stackTrace_6() { return static_cast<int32_t>(offsetof(Exception_t, ____stackTrace_6)); }
	inline RuntimeObject * get__stackTrace_6() const { return ____stackTrace_6; }
	inline RuntimeObject ** get_address_of__stackTrace_6() { return &____stackTrace_6; }
	inline void set__stackTrace_6(RuntimeObject * value)
	{
		____stackTrace_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____stackTrace_6), (void*)value);
	}

	inline static int32_t get_offset_of__stackTraceString_7() { return static_cast<int32_t>(offsetof(Exception_t, ____stackTraceString_7)); }
	inline String_t* get__stackTraceString_7() const { return ____stackTraceString_7; }
	inline String_t** get_address_of__stackTraceString_7() { return &____stackTraceString_7; }
	inline void set__stackTraceString_7(String_t* value)
	{
		____stackTraceString_7 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____stackTraceString_7), (void*)value);
	}

	inline static int32_t get_offset_of__remoteStackTraceString_8() { return static_cast<int32_t>(offsetof(Exception_t, ____remoteStackTraceString_8)); }
	inline String_t* get__remoteStackTraceString_8() const { return ____remoteStackTraceString_8; }
	inline String_t** get_address_of__remoteStackTraceString_8() { return &____remoteStackTraceString_8; }
	inline void set__remoteStackTraceString_8(String_t* value)
	{
		____remoteStackTraceString_8 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____remoteStackTraceString_8), (void*)value);
	}

	inline static int32_t get_offset_of__remoteStackIndex_9() { return static_cast<int32_t>(offsetof(Exception_t, ____remoteStackIndex_9)); }
	inline int32_t get__remoteStackIndex_9() const { return ____remoteStackIndex_9; }
	inline int32_t* get_address_of__remoteStackIndex_9() { return &____remoteStackIndex_9; }
	inline void set__remoteStackIndex_9(int32_t value)
	{
		____remoteStackIndex_9 = value;
	}

	inline static int32_t get_offset_of__dynamicMethods_10() { return static_cast<int32_t>(offsetof(Exception_t, ____dynamicMethods_10)); }
	inline RuntimeObject * get__dynamicMethods_10() const { return ____dynamicMethods_10; }
	inline RuntimeObject ** get_address_of__dynamicMethods_10() { return &____dynamicMethods_10; }
	inline void set__dynamicMethods_10(RuntimeObject * value)
	{
		____dynamicMethods_10 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____dynamicMethods_10), (void*)value);
	}

	inline static int32_t get_offset_of__HResult_11() { return static_cast<int32_t>(offsetof(Exception_t, ____HResult_11)); }
	inline int32_t get__HResult_11() const { return ____HResult_11; }
	inline int32_t* get_address_of__HResult_11() { return &____HResult_11; }
	inline void set__HResult_11(int32_t value)
	{
		____HResult_11 = value;
	}

	inline static int32_t get_offset_of__source_12() { return static_cast<int32_t>(offsetof(Exception_t, ____source_12)); }
	inline String_t* get__source_12() const { return ____source_12; }
	inline String_t** get_address_of__source_12() { return &____source_12; }
	inline void set__source_12(String_t* value)
	{
		____source_12 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____source_12), (void*)value);
	}

	inline static int32_t get_offset_of__safeSerializationManager_13() { return static_cast<int32_t>(offsetof(Exception_t, ____safeSerializationManager_13)); }
	inline SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F * get__safeSerializationManager_13() const { return ____safeSerializationManager_13; }
	inline SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F ** get_address_of__safeSerializationManager_13() { return &____safeSerializationManager_13; }
	inline void set__safeSerializationManager_13(SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F * value)
	{
		____safeSerializationManager_13 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____safeSerializationManager_13), (void*)value);
	}

	inline static int32_t get_offset_of_captured_traces_14() { return static_cast<int32_t>(offsetof(Exception_t, ___captured_traces_14)); }
	inline StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971* get_captured_traces_14() const { return ___captured_traces_14; }
	inline StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971** get_address_of_captured_traces_14() { return &___captured_traces_14; }
	inline void set_captured_traces_14(StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971* value)
	{
		___captured_traces_14 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___captured_traces_14), (void*)value);
	}

	inline static int32_t get_offset_of_native_trace_ips_15() { return static_cast<int32_t>(offsetof(Exception_t, ___native_trace_ips_15)); }
	inline IntPtrU5BU5D_t27FC72B0409D75AAF33EC42498E8094E95FEE9A6* get_native_trace_ips_15() const { return ___native_trace_ips_15; }
	inline IntPtrU5BU5D_t27FC72B0409D75AAF33EC42498E8094E95FEE9A6** get_address_of_native_trace_ips_15() { return &___native_trace_ips_15; }
	inline void set_native_trace_ips_15(IntPtrU5BU5D_t27FC72B0409D75AAF33EC42498E8094E95FEE9A6* value)
	{
		___native_trace_ips_15 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___native_trace_ips_15), (void*)value);
	}
};

// Native definition for P/Invoke marshalling of System.Exception
struct Exception_t_marshaled_pinvoke
{
	char* ____className_1;
	char* ____message_2;
	RuntimeObject* ____data_3;
	Exception_t_marshaled_pinvoke* ____innerException_4;
	char* ____helpURL_5;
	Il2CppIUnknown* ____stackTrace_6;
	char* ____stackTraceString_7;
	char* ____remoteStackTraceString_8;
	int32_t ____remoteStackIndex_9;
	Il2CppIUnknown* ____dynamicMethods_10;
	int32_t ____HResult_11;
	char* ____source_12;
	SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F * ____safeSerializationManager_13;
	StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971* ___captured_traces_14;
	Il2CppSafeArray/*NONE*/* ___native_trace_ips_15;
};
// Native definition for COM marshalling of System.Exception
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className_1;
	Il2CppChar* ____message_2;
	RuntimeObject* ____data_3;
	Exception_t_marshaled_com* ____innerException_4;
	Il2CppChar* ____helpURL_5;
	Il2CppIUnknown* ____stackTrace_6;
	Il2CppChar* ____stackTraceString_7;
	Il2CppChar* ____remoteStackTraceString_8;
	int32_t ____remoteStackIndex_9;
	Il2CppIUnknown* ____dynamicMethods_10;
	int32_t ____HResult_11;
	Il2CppChar* ____source_12;
	SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F * ____safeSerializationManager_13;
	StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971* ___captured_traces_14;
	Il2CppSafeArray/*NONE*/* ___native_trace_ips_15;
};

// UnityEngine.Object
struct Object_tF2F3778131EFF286AF62B7B013A170F95A91571A  : public RuntimeObject
{
public:
	// System.IntPtr UnityEngine.Object::m_CachedPtr
	intptr_t ___m_CachedPtr_0;

public:
	inline static int32_t get_offset_of_m_CachedPtr_0() { return static_cast<int32_t>(offsetof(Object_tF2F3778131EFF286AF62B7B013A170F95A91571A, ___m_CachedPtr_0)); }
	inline intptr_t get_m_CachedPtr_0() const { return ___m_CachedPtr_0; }
	inline intptr_t* get_address_of_m_CachedPtr_0() { return &___m_CachedPtr_0; }
	inline void set_m_CachedPtr_0(intptr_t value)
	{
		___m_CachedPtr_0 = value;
	}
};

// Native definition for P/Invoke marshalling of UnityEngine.Object
struct Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr_0;
};
// Native definition for COM marshalling of UnityEngine.Object
struct Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_marshaled_com
{
	intptr_t ___m_CachedPtr_0;
};

// UnityEngine.VFX.VFXEventAttribute
struct VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF  : public RuntimeObject
{
public:
	// System.IntPtr UnityEngine.VFX.VFXEventAttribute::m_Ptr
	intptr_t ___m_Ptr_0;
	// System.Boolean UnityEngine.VFX.VFXEventAttribute::m_Owner
	bool ___m_Owner_1;
	// UnityEngine.VFX.VisualEffectAsset UnityEngine.VFX.VFXEventAttribute::m_VfxAsset
	VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 * ___m_VfxAsset_2;

public:
	inline static int32_t get_offset_of_m_Ptr_0() { return static_cast<int32_t>(offsetof(VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF, ___m_Ptr_0)); }
	inline intptr_t get_m_Ptr_0() const { return ___m_Ptr_0; }
	inline intptr_t* get_address_of_m_Ptr_0() { return &___m_Ptr_0; }
	inline void set_m_Ptr_0(intptr_t value)
	{
		___m_Ptr_0 = value;
	}

	inline static int32_t get_offset_of_m_Owner_1() { return static_cast<int32_t>(offsetof(VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF, ___m_Owner_1)); }
	inline bool get_m_Owner_1() const { return ___m_Owner_1; }
	inline bool* get_address_of_m_Owner_1() { return &___m_Owner_1; }
	inline void set_m_Owner_1(bool value)
	{
		___m_Owner_1 = value;
	}

	inline static int32_t get_offset_of_m_VfxAsset_2() { return static_cast<int32_t>(offsetof(VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF, ___m_VfxAsset_2)); }
	inline VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 * get_m_VfxAsset_2() const { return ___m_VfxAsset_2; }
	inline VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 ** get_address_of_m_VfxAsset_2() { return &___m_VfxAsset_2; }
	inline void set_m_VfxAsset_2(VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 * value)
	{
		___m_VfxAsset_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_VfxAsset_2), (void*)value);
	}
};

// Native definition for P/Invoke marshalling of UnityEngine.VFX.VFXEventAttribute
struct VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshaled_pinvoke
{
	intptr_t ___m_Ptr_0;
	int32_t ___m_Owner_1;
	VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 * ___m_VfxAsset_2;
};
// Native definition for COM marshalling of UnityEngine.VFX.VFXEventAttribute
struct VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshaled_com
{
	intptr_t ___m_Ptr_0;
	int32_t ___m_Owner_1;
	VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 * ___m_VfxAsset_2;
};

// UnityEngine.VFX.VFXExpressionValues
struct VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883  : public RuntimeObject
{
public:
	// System.IntPtr UnityEngine.VFX.VFXExpressionValues::m_Ptr
	intptr_t ___m_Ptr_0;

public:
	inline static int32_t get_offset_of_m_Ptr_0() { return static_cast<int32_t>(offsetof(VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883, ___m_Ptr_0)); }
	inline intptr_t get_m_Ptr_0() const { return ___m_Ptr_0; }
	inline intptr_t* get_address_of_m_Ptr_0() { return &___m_Ptr_0; }
	inline void set_m_Ptr_0(intptr_t value)
	{
		___m_Ptr_0 = value;
	}
};

// Native definition for P/Invoke marshalling of UnityEngine.VFX.VFXExpressionValues
struct VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883_marshaled_pinvoke
{
	intptr_t ___m_Ptr_0;
};
// Native definition for COM marshalling of UnityEngine.VFX.VFXExpressionValues
struct VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883_marshaled_com
{
	intptr_t ___m_Ptr_0;
};

// UnityEngine.VFX.VFXSpawnerState
struct VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD  : public RuntimeObject
{
public:
	// System.IntPtr UnityEngine.VFX.VFXSpawnerState::m_Ptr
	intptr_t ___m_Ptr_0;
	// System.Boolean UnityEngine.VFX.VFXSpawnerState::m_Owner
	bool ___m_Owner_1;

public:
	inline static int32_t get_offset_of_m_Ptr_0() { return static_cast<int32_t>(offsetof(VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD, ___m_Ptr_0)); }
	inline intptr_t get_m_Ptr_0() const { return ___m_Ptr_0; }
	inline intptr_t* get_address_of_m_Ptr_0() { return &___m_Ptr_0; }
	inline void set_m_Ptr_0(intptr_t value)
	{
		___m_Ptr_0 = value;
	}

	inline static int32_t get_offset_of_m_Owner_1() { return static_cast<int32_t>(offsetof(VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD, ___m_Owner_1)); }
	inline bool get_m_Owner_1() const { return ___m_Owner_1; }
	inline bool* get_address_of_m_Owner_1() { return &___m_Owner_1; }
	inline void set_m_Owner_1(bool value)
	{
		___m_Owner_1 = value;
	}
};

// Native definition for P/Invoke marshalling of UnityEngine.VFX.VFXSpawnerState
struct VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD_marshaled_pinvoke
{
	intptr_t ___m_Ptr_0;
	int32_t ___m_Owner_1;
};
// Native definition for COM marshalling of UnityEngine.VFX.VFXSpawnerState
struct VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD_marshaled_com
{
	intptr_t ___m_Ptr_0;
	int32_t ___m_Owner_1;
};

// UnityEngine.Component
struct Component_t62FBC8D2420DA4BE9037AFE430740F6B3EECA684  : public Object_tF2F3778131EFF286AF62B7B013A170F95A91571A
{
public:

public:
};


// System.MulticastDelegate
struct MulticastDelegate_t  : public Delegate_t
{
public:
	// System.Delegate[] System.MulticastDelegate::delegates
	DelegateU5BU5D_t677D8FE08A5F99E8EE49150B73966CD6E9BF7DB8* ___delegates_11;

public:
	inline static int32_t get_offset_of_delegates_11() { return static_cast<int32_t>(offsetof(MulticastDelegate_t, ___delegates_11)); }
	inline DelegateU5BU5D_t677D8FE08A5F99E8EE49150B73966CD6E9BF7DB8* get_delegates_11() const { return ___delegates_11; }
	inline DelegateU5BU5D_t677D8FE08A5F99E8EE49150B73966CD6E9BF7DB8** get_address_of_delegates_11() { return &___delegates_11; }
	inline void set_delegates_11(DelegateU5BU5D_t677D8FE08A5F99E8EE49150B73966CD6E9BF7DB8* value)
	{
		___delegates_11 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___delegates_11), (void*)value);
	}
};

// Native definition for P/Invoke marshalling of System.MulticastDelegate
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates_11;
};
// Native definition for COM marshalling of System.MulticastDelegate
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates_11;
};

// UnityEngine.ScriptableObject
struct ScriptableObject_t4361E08CEBF052C650D3666C7CEC37EB31DE116A  : public Object_tF2F3778131EFF286AF62B7B013A170F95A91571A
{
public:

public:
};

// Native definition for P/Invoke marshalling of UnityEngine.ScriptableObject
struct ScriptableObject_t4361E08CEBF052C650D3666C7CEC37EB31DE116A_marshaled_pinvoke : public Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_marshaled_pinvoke
{
};
// Native definition for COM marshalling of UnityEngine.ScriptableObject
struct ScriptableObject_t4361E08CEBF052C650D3666C7CEC37EB31DE116A_marshaled_com : public Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_marshaled_com
{
};

// UnityEngine.VFX.VisualEffectObject
struct VisualEffectObject_tC7804AFDC2B4F2F0CE6833AC467ABC177A1617DB  : public Object_tF2F3778131EFF286AF62B7B013A170F95A91571A
{
public:

public:
};


// System.Action`1<UnityEngine.VFX.VFXOutputEventArgs>
struct Action_1_t5C8C9298698F95A378E73C4584F33A97EF82A064  : public MulticastDelegate_t
{
public:

public:
};


// UnityEngine.Behaviour
struct Behaviour_t1A3DDDCF73B4627928FBFE02ED52B7251777DBD9  : public Component_t62FBC8D2420DA4BE9037AFE430740F6B3EECA684
{
public:

public:
};


// UnityEngine.VFX.VFXSpawnerCallbacks
struct VFXSpawnerCallbacks_t62128B7E3ADA64EBEA4705691DE0F045104801CA  : public ScriptableObject_t4361E08CEBF052C650D3666C7CEC37EB31DE116A
{
public:

public:
};


// UnityEngine.VFX.VisualEffectAsset
struct VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50  : public VisualEffectObject_tC7804AFDC2B4F2F0CE6833AC467ABC177A1617DB
{
public:

public:
};


// UnityEngine.VFX.VisualEffect
struct VisualEffect_t7C6E2AAA4DB4F47960AF2029EA96D4B579B3A4CA  : public Behaviour_t1A3DDDCF73B4627928FBFE02ED52B7251777DBD9
{
public:
	// UnityEngine.VFX.VFXEventAttribute UnityEngine.VFX.VisualEffect::m_cachedEventAttribute
	VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * ___m_cachedEventAttribute_4;
	// System.Action`1<UnityEngine.VFX.VFXOutputEventArgs> UnityEngine.VFX.VisualEffect::outputEventReceived
	Action_1_t5C8C9298698F95A378E73C4584F33A97EF82A064 * ___outputEventReceived_5;

public:
	inline static int32_t get_offset_of_m_cachedEventAttribute_4() { return static_cast<int32_t>(offsetof(VisualEffect_t7C6E2AAA4DB4F47960AF2029EA96D4B579B3A4CA, ___m_cachedEventAttribute_4)); }
	inline VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * get_m_cachedEventAttribute_4() const { return ___m_cachedEventAttribute_4; }
	inline VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF ** get_address_of_m_cachedEventAttribute_4() { return &___m_cachedEventAttribute_4; }
	inline void set_m_cachedEventAttribute_4(VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * value)
	{
		___m_cachedEventAttribute_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_cachedEventAttribute_4), (void*)value);
	}

	inline static int32_t get_offset_of_outputEventReceived_5() { return static_cast<int32_t>(offsetof(VisualEffect_t7C6E2AAA4DB4F47960AF2029EA96D4B579B3A4CA, ___outputEventReceived_5)); }
	inline Action_1_t5C8C9298698F95A378E73C4584F33A97EF82A064 * get_outputEventReceived_5() const { return ___outputEventReceived_5; }
	inline Action_1_t5C8C9298698F95A378E73C4584F33A97EF82A064 ** get_address_of_outputEventReceived_5() { return &___outputEventReceived_5; }
	inline void set_outputEventReceived_5(Action_1_t5C8C9298698F95A378E73C4584F33A97EF82A064 * value)
	{
		___outputEventReceived_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___outputEventReceived_5), (void*)value);
	}
};


// <Module>


// <Module>


// System.Object


// System.Object

struct Il2CppArrayBounds;

// System.Array

struct Il2CppArrayBounds;

// System.Array


// System.String

struct String_t_StaticFields
{
public:
	// System.String System.String::Empty
	String_t* ___Empty_5;

public:
	inline static int32_t get_offset_of_Empty_5() { return static_cast<int32_t>(offsetof(String_t_StaticFields, ___Empty_5)); }
	inline String_t* get_Empty_5() const { return ___Empty_5; }
	inline String_t** get_address_of_Empty_5() { return &___Empty_5; }
	inline void set_Empty_5(String_t* value)
	{
		___Empty_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Empty_5), (void*)value);
	}
};


// System.String


// UnityEngine.VFX.VFXManager


// UnityEngine.VFX.VFXManager


// System.ValueType


// System.ValueType


// System.Boolean

struct Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields
{
public:
	// System.String System.Boolean::TrueString
	String_t* ___TrueString_5;
	// System.String System.Boolean::FalseString
	String_t* ___FalseString_6;

public:
	inline static int32_t get_offset_of_TrueString_5() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields, ___TrueString_5)); }
	inline String_t* get_TrueString_5() const { return ___TrueString_5; }
	inline String_t** get_address_of_TrueString_5() { return &___TrueString_5; }
	inline void set_TrueString_5(String_t* value)
	{
		___TrueString_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___TrueString_5), (void*)value);
	}

	inline static int32_t get_offset_of_FalseString_6() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields, ___FalseString_6)); }
	inline String_t* get_FalseString_6() const { return ___FalseString_6; }
	inline String_t** get_address_of_FalseString_6() { return &___FalseString_6; }
	inline void set_FalseString_6(String_t* value)
	{
		___FalseString_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___FalseString_6), (void*)value);
	}
};


// System.Boolean


// System.Int32


// System.Int32


// System.IntPtr

struct IntPtr_t_StaticFields
{
public:
	// System.IntPtr System.IntPtr::Zero
	intptr_t ___Zero_1;

public:
	inline static int32_t get_offset_of_Zero_1() { return static_cast<int32_t>(offsetof(IntPtr_t_StaticFields, ___Zero_1)); }
	inline intptr_t get_Zero_1() const { return ___Zero_1; }
	inline intptr_t* get_address_of_Zero_1() { return &___Zero_1; }
	inline void set_Zero_1(intptr_t value)
	{
		___Zero_1 = value;
	}
};


// System.IntPtr


// UnityEngine.VFX.VFXOutputEventArgs


// UnityEngine.VFX.VFXOutputEventArgs


// System.Void


// System.Void


// System.Delegate


// System.Delegate


// System.Exception

struct Exception_t_StaticFields
{
public:
	// System.Object System.Exception::s_EDILock
	RuntimeObject * ___s_EDILock_0;

public:
	inline static int32_t get_offset_of_s_EDILock_0() { return static_cast<int32_t>(offsetof(Exception_t_StaticFields, ___s_EDILock_0)); }
	inline RuntimeObject * get_s_EDILock_0() const { return ___s_EDILock_0; }
	inline RuntimeObject ** get_address_of_s_EDILock_0() { return &___s_EDILock_0; }
	inline void set_s_EDILock_0(RuntimeObject * value)
	{
		___s_EDILock_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___s_EDILock_0), (void*)value);
	}
};


// System.Exception


// UnityEngine.Object

struct Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_StaticFields
{
public:
	// System.Int32 UnityEngine.Object::OffsetOfInstanceIDInCPlusPlusObject
	int32_t ___OffsetOfInstanceIDInCPlusPlusObject_1;

public:
	inline static int32_t get_offset_of_OffsetOfInstanceIDInCPlusPlusObject_1() { return static_cast<int32_t>(offsetof(Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_StaticFields, ___OffsetOfInstanceIDInCPlusPlusObject_1)); }
	inline int32_t get_OffsetOfInstanceIDInCPlusPlusObject_1() const { return ___OffsetOfInstanceIDInCPlusPlusObject_1; }
	inline int32_t* get_address_of_OffsetOfInstanceIDInCPlusPlusObject_1() { return &___OffsetOfInstanceIDInCPlusPlusObject_1; }
	inline void set_OffsetOfInstanceIDInCPlusPlusObject_1(int32_t value)
	{
		___OffsetOfInstanceIDInCPlusPlusObject_1 = value;
	}
};


// UnityEngine.Object


// UnityEngine.VFX.VFXEventAttribute


// UnityEngine.VFX.VFXEventAttribute


// UnityEngine.VFX.VFXExpressionValues


// UnityEngine.VFX.VFXExpressionValues


// UnityEngine.VFX.VFXSpawnerState


// UnityEngine.VFX.VFXSpawnerState


// UnityEngine.Component


// UnityEngine.Component


// System.MulticastDelegate


// System.MulticastDelegate


// UnityEngine.ScriptableObject


// UnityEngine.ScriptableObject


// UnityEngine.VFX.VisualEffectObject


// UnityEngine.VFX.VisualEffectObject


// System.Action`1<UnityEngine.VFX.VFXOutputEventArgs>


// System.Action`1<UnityEngine.VFX.VFXOutputEventArgs>


// UnityEngine.Behaviour


// UnityEngine.Behaviour


// UnityEngine.VFX.VFXSpawnerCallbacks


// UnityEngine.VFX.VFXSpawnerCallbacks


// UnityEngine.VFX.VisualEffectAsset

struct VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50_StaticFields
{
public:
	// System.Int32 UnityEngine.VFX.VisualEffectAsset::PlayEventID
	int32_t ___PlayEventID_4;
	// System.Int32 UnityEngine.VFX.VisualEffectAsset::StopEventID
	int32_t ___StopEventID_5;

public:
	inline static int32_t get_offset_of_PlayEventID_4() { return static_cast<int32_t>(offsetof(VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50_StaticFields, ___PlayEventID_4)); }
	inline int32_t get_PlayEventID_4() const { return ___PlayEventID_4; }
	inline int32_t* get_address_of_PlayEventID_4() { return &___PlayEventID_4; }
	inline void set_PlayEventID_4(int32_t value)
	{
		___PlayEventID_4 = value;
	}

	inline static int32_t get_offset_of_StopEventID_5() { return static_cast<int32_t>(offsetof(VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50_StaticFields, ___StopEventID_5)); }
	inline int32_t get_StopEventID_5() const { return ___StopEventID_5; }
	inline int32_t* get_address_of_StopEventID_5() { return &___StopEventID_5; }
	inline void set_StopEventID_5(int32_t value)
	{
		___StopEventID_5 = value;
	}
};


// UnityEngine.VFX.VisualEffectAsset


// UnityEngine.VFX.VisualEffect


// UnityEngine.VFX.VisualEffect

#ifdef __clang__
#pragma clang diagnostic pop
#endif

IL2CPP_EXTERN_C void VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshal_pinvoke(const VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF& unmarshaled, VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshaled_pinvoke& marshaled);
IL2CPP_EXTERN_C void VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshal_pinvoke_back(const VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshaled_pinvoke& marshaled, VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF& unmarshaled);
IL2CPP_EXTERN_C void VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshal_pinvoke_cleanup(VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshaled_pinvoke& marshaled);
IL2CPP_EXTERN_C void VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshal_com(const VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF& unmarshaled, VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshaled_com& marshaled);
IL2CPP_EXTERN_C void VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshal_com_back(const VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshaled_com& marshaled, VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF& unmarshaled);
IL2CPP_EXTERN_C void VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshal_com_cleanup(VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshaled_com& marshaled);

// System.Void System.Action`1<UnityEngine.VFX.VFXOutputEventArgs>::Invoke(!0)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1_Invoke_m253872B5D1C80AFE87652D0B1DDBF95AC54E702D_gshared (Action_1_t5C8C9298698F95A378E73C4584F33A97EF82A064 * __this, VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5  ___obj0, const RuntimeMethod* method);

// System.Void System.Object::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405 (RuntimeObject * __this, const RuntimeMethod* method);
// System.IntPtr UnityEngine.VFX.VFXEventAttribute::Internal_Create()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR intptr_t VFXEventAttribute_Internal_Create_mA5963ED38DB766B7AB2674A73CA7046D7470B794 (const RuntimeMethod* method);
// System.Void UnityEngine.VFX.VFXEventAttribute::.ctor(System.IntPtr,System.Boolean,UnityEngine.VFX.VisualEffectAsset)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXEventAttribute__ctor_mDE4833E90493250BDBF27A8A5AFAADE4E5A90964 (VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * __this, intptr_t ___ptr0, bool ___owner1, VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 * ___vfxAsset2, const RuntimeMethod* method);
// System.Void UnityEngine.VFX.VFXEventAttribute::Internal_InitFromAsset(UnityEngine.VFX.VisualEffectAsset)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXEventAttribute_Internal_InitFromAsset_m5DC121D03DAA7A8E6FF348D98CDAAE209F1F8AE1 (VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * __this, VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 * ___vfxAsset0, const RuntimeMethod* method);
// System.Boolean System.IntPtr::op_Inequality(System.IntPtr,System.IntPtr)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool IntPtr_op_Inequality_mB4DF9161F8D8F48708A2A7BBD7BF626F1EB8C3C8 (intptr_t ___value10, intptr_t ___value21, const RuntimeMethod* method);
// System.Void UnityEngine.VFX.VFXEventAttribute::Internal_Destroy(System.IntPtr)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXEventAttribute_Internal_Destroy_m4AE4B4E642D451D77F464E8D57985244940C6CAC (intptr_t ___ptr0, const RuntimeMethod* method);
// System.Void UnityEngine.VFX.VFXEventAttribute::Release()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXEventAttribute_Release_m9AEDE0F9E70188F9737D68638733D70DB4814983 (VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * __this, const RuntimeMethod* method);
// System.Void System.Object::Finalize()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object_Finalize_mC59C83CF4F7707E425FFA6362931C25D4C36676A (RuntimeObject * __this, const RuntimeMethod* method);
// System.Void System.GC::SuppressFinalize(System.Object)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GC_SuppressFinalize_mFF908DB65A7128F68FC8CAEA84AA753B439FE40E (RuntimeObject * ___obj0, const RuntimeMethod* method);
// System.Void UnityEngine.VFX.VFXExpressionValues::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXExpressionValues__ctor_m4B8043CAE5D33D062707D851DD4E62D9F7BB1320 (VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883 * __this, const RuntimeMethod* method);
// System.Void UnityEngine.VFX.VFXOutputEventArgs::.ctor(System.Int32,UnityEngine.VFX.VFXEventAttribute)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXOutputEventArgs__ctor_m7C49FC1B34F3582FE08D7A1CAA5024C09A86F7A2 (VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5 * __this, int32_t ___nameId0, VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * ___eventAttribute1, const RuntimeMethod* method);
// System.Void UnityEngine.ScriptableObject::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ScriptableObject__ctor_m8DAE6CDCFA34E16F2543B02CC3669669FF203063 (ScriptableObject_t4361E08CEBF052C650D3666C7CEC37EB31DE116A * __this, const RuntimeMethod* method);
// System.Void UnityEngine.VFX.VFXSpawnerState::.ctor(System.IntPtr,System.Boolean)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXSpawnerState__ctor_m2A95971A7A6D3701F39FD7A656451CD619C3010E (VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD * __this, intptr_t ___ptr0, bool ___owner1, const RuntimeMethod* method);
// System.Void System.Exception::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Exception__ctor_m8ECDE8ACA7F2E0EF1144BD1200FB5DB2870B5F11 (Exception_t * __this, String_t* ___message0, const RuntimeMethod* method);
// System.Void UnityEngine.VFX.VFXSpawnerState::Internal_Destroy(System.IntPtr)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXSpawnerState_Internal_Destroy_m847E1B71DBC4B8C289D718F62219EE94088FD0F9 (intptr_t ___ptr0, const RuntimeMethod* method);
// System.Void UnityEngine.VFX.VFXSpawnerState::Release()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXSpawnerState_Release_m9D420CAC0353FCBCBF0F2D59AA286ED3D0BC4649 (VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD * __this, const RuntimeMethod* method);
// UnityEngine.VFX.VisualEffectAsset UnityEngine.VFX.VisualEffect::get_visualEffectAsset()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 * VisualEffect_get_visualEffectAsset_m019D107588568B2C5F1FFA77C626DC986A63AD96 (VisualEffect_t7C6E2AAA4DB4F47960AF2029EA96D4B579B3A4CA * __this, const RuntimeMethod* method);
// System.Boolean UnityEngine.Object::op_Equality(UnityEngine.Object,UnityEngine.Object)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Object_op_Equality_m6D7EBC0E6D7E0CE1E9671D21DE14C9158AFB88B2 (Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * ___x0, Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * ___y1, const RuntimeMethod* method);
// UnityEngine.VFX.VFXEventAttribute UnityEngine.VFX.VFXEventAttribute::Internal_InstanciateVFXEventAttribute(UnityEngine.VFX.VisualEffectAsset)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * VFXEventAttribute_Internal_InstanciateVFXEventAttribute_m3AD160FECB74D898826FB11ECB937EA9BA0B47BA (VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 * ___vfxAsset0, const RuntimeMethod* method);
// UnityEngine.VFX.VFXEventAttribute UnityEngine.VFX.VisualEffect::CreateVFXEventAttribute()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * VisualEffect_CreateVFXEventAttribute_mE99FC6DA23D465D06A8547CC5B2502FB0B30D8D6 (VisualEffect_t7C6E2AAA4DB4F47960AF2029EA96D4B579B3A4CA * __this, const RuntimeMethod* method);
// System.Void System.Action`1<UnityEngine.VFX.VFXOutputEventArgs>::Invoke(!0)
inline void Action_1_Invoke_m253872B5D1C80AFE87652D0B1DDBF95AC54E702D (Action_1_t5C8C9298698F95A378E73C4584F33A97EF82A064 * __this, VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5  ___obj0, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t5C8C9298698F95A378E73C4584F33A97EF82A064 *, VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5 , const RuntimeMethod*))Action_1_Invoke_m253872B5D1C80AFE87652D0B1DDBF95AC54E702D_gshared)(__this, ___obj0, method);
}
// System.Void UnityEngine.VFX.VisualEffectObject::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VisualEffectObject__ctor_mC6F115A9348A51326C366ACB2724884050A00B2D (VisualEffectObject_tC7804AFDC2B4F2F0CE6833AC467ABC177A1617DB * __this, const RuntimeMethod* method);
// System.Int32 UnityEngine.Shader::PropertyToID(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Shader_PropertyToID_m73455E48C146AE645F830EC15177F24B788F7019 (String_t* ___name0, const RuntimeMethod* method);
// System.Void UnityEngine.Object::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_m4DCF5CDB32C2C69290894101A81F473865169279 (Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * __this, const RuntimeMethod* method);
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// Conversion methods for marshalling of: UnityEngine.VFX.VFXEventAttribute
IL2CPP_EXTERN_C void VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshal_pinvoke(const VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF& unmarshaled, VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshaled_pinvoke& marshaled)
{
	Exception_t* ___m_VfxAsset_2Exception = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field 'm_VfxAsset' of type 'VFXEventAttribute': Reference type field marshaling is not supported.");
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_VfxAsset_2Exception, NULL);
}
IL2CPP_EXTERN_C void VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshal_pinvoke_back(const VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshaled_pinvoke& marshaled, VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF& unmarshaled)
{
	Exception_t* ___m_VfxAsset_2Exception = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field 'm_VfxAsset' of type 'VFXEventAttribute': Reference type field marshaling is not supported.");
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_VfxAsset_2Exception, NULL);
}
// Conversion method for clean up from marshalling of: UnityEngine.VFX.VFXEventAttribute
IL2CPP_EXTERN_C void VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshal_pinvoke_cleanup(VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshaled_pinvoke& marshaled)
{
}
// Conversion methods for marshalling of: UnityEngine.VFX.VFXEventAttribute
IL2CPP_EXTERN_C void VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshal_com(const VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF& unmarshaled, VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshaled_com& marshaled)
{
	Exception_t* ___m_VfxAsset_2Exception = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field 'm_VfxAsset' of type 'VFXEventAttribute': Reference type field marshaling is not supported.");
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_VfxAsset_2Exception, NULL);
}
IL2CPP_EXTERN_C void VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshal_com_back(const VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshaled_com& marshaled, VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF& unmarshaled)
{
	Exception_t* ___m_VfxAsset_2Exception = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field 'm_VfxAsset' of type 'VFXEventAttribute': Reference type field marshaling is not supported.");
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_VfxAsset_2Exception, NULL);
}
// Conversion method for clean up from marshalling of: UnityEngine.VFX.VFXEventAttribute
IL2CPP_EXTERN_C void VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshal_com_cleanup(VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_marshaled_com& marshaled)
{
}
// System.Void UnityEngine.VFX.VFXEventAttribute::.ctor(System.IntPtr,System.Boolean,UnityEngine.VFX.VisualEffectAsset)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXEventAttribute__ctor_mDE4833E90493250BDBF27A8A5AFAADE4E5A90964 (VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * __this, intptr_t ___ptr0, bool ___owner1, VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 * ___vfxAsset2, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		intptr_t L_0 = ___ptr0;
		__this->set_m_Ptr_0((intptr_t)L_0);
		bool L_1 = ___owner1;
		__this->set_m_Owner_1(L_1);
		VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 * L_2 = ___vfxAsset2;
		__this->set_m_VfxAsset_2(L_2);
		return;
	}
}
// System.IntPtr UnityEngine.VFX.VFXEventAttribute::Internal_Create()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR intptr_t VFXEventAttribute_Internal_Create_mA5963ED38DB766B7AB2674A73CA7046D7470B794 (const RuntimeMethod* method)
{
	typedef intptr_t (*VFXEventAttribute_Internal_Create_mA5963ED38DB766B7AB2674A73CA7046D7470B794_ftn) ();
	static VFXEventAttribute_Internal_Create_mA5963ED38DB766B7AB2674A73CA7046D7470B794_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VFXEventAttribute_Internal_Create_mA5963ED38DB766B7AB2674A73CA7046D7470B794_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.VFX.VFXEventAttribute::Internal_Create()");
	intptr_t icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
// UnityEngine.VFX.VFXEventAttribute UnityEngine.VFX.VFXEventAttribute::Internal_InstanciateVFXEventAttribute(UnityEngine.VFX.VisualEffectAsset)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * VFXEventAttribute_Internal_InstanciateVFXEventAttribute_m3AD160FECB74D898826FB11ECB937EA9BA0B47BA (VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 * ___vfxAsset0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * V_0 = NULL;
	VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * V_1 = NULL;
	{
		intptr_t L_0;
		L_0 = VFXEventAttribute_Internal_Create_mA5963ED38DB766B7AB2674A73CA7046D7470B794(/*hidden argument*/NULL);
		VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 * L_1 = ___vfxAsset0;
		VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * L_2 = (VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF *)il2cpp_codegen_object_new(VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF_il2cpp_TypeInfo_var);
		VFXEventAttribute__ctor_mDE4833E90493250BDBF27A8A5AFAADE4E5A90964(L_2, (intptr_t)L_0, (bool)1, L_1, /*hidden argument*/NULL);
		V_0 = L_2;
		VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * L_3 = V_0;
		VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 * L_4 = ___vfxAsset0;
		NullCheck(L_3);
		VFXEventAttribute_Internal_InitFromAsset_m5DC121D03DAA7A8E6FF348D98CDAAE209F1F8AE1(L_3, L_4, /*hidden argument*/NULL);
		VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * L_5 = V_0;
		V_1 = L_5;
		goto IL_001a;
	}

IL_001a:
	{
		VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * L_6 = V_1;
		return L_6;
	}
}
// System.Void UnityEngine.VFX.VFXEventAttribute::Internal_InitFromAsset(UnityEngine.VFX.VisualEffectAsset)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXEventAttribute_Internal_InitFromAsset_m5DC121D03DAA7A8E6FF348D98CDAAE209F1F8AE1 (VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * __this, VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 * ___vfxAsset0, const RuntimeMethod* method)
{
	typedef void (*VFXEventAttribute_Internal_InitFromAsset_m5DC121D03DAA7A8E6FF348D98CDAAE209F1F8AE1_ftn) (VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF *, VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 *);
	static VFXEventAttribute_Internal_InitFromAsset_m5DC121D03DAA7A8E6FF348D98CDAAE209F1F8AE1_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VFXEventAttribute_Internal_InitFromAsset_m5DC121D03DAA7A8E6FF348D98CDAAE209F1F8AE1_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.VFX.VFXEventAttribute::Internal_InitFromAsset(UnityEngine.VFX.VisualEffectAsset)");
	_il2cpp_icall_func(__this, ___vfxAsset0);
}
// System.Void UnityEngine.VFX.VFXEventAttribute::Release()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXEventAttribute_Release_m9AEDE0F9E70188F9737D68638733D70DB4814983 (VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IntPtr_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	int32_t G_B3_0 = 0;
	{
		bool L_0 = __this->get_m_Owner_1();
		if (!L_0)
		{
			goto IL_001b;
		}
	}
	{
		intptr_t L_1 = __this->get_m_Ptr_0();
		bool L_2;
		L_2 = IntPtr_op_Inequality_mB4DF9161F8D8F48708A2A7BBD7BF626F1EB8C3C8((intptr_t)L_1, (intptr_t)(0), /*hidden argument*/NULL);
		G_B3_0 = ((int32_t)(L_2));
		goto IL_001c;
	}

IL_001b:
	{
		G_B3_0 = 0;
	}

IL_001c:
	{
		V_0 = (bool)G_B3_0;
		bool L_3 = V_0;
		if (!L_3)
		{
			goto IL_002e;
		}
	}
	{
		intptr_t L_4 = __this->get_m_Ptr_0();
		VFXEventAttribute_Internal_Destroy_m4AE4B4E642D451D77F464E8D57985244940C6CAC((intptr_t)L_4, /*hidden argument*/NULL);
	}

IL_002e:
	{
		__this->set_m_Ptr_0((intptr_t)(0));
		__this->set_m_VfxAsset_2((VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 *)NULL);
		return;
	}
}
// System.Void UnityEngine.VFX.VFXEventAttribute::Finalize()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXEventAttribute_Finalize_mC4B4672CB30A8925BD10CF82CFB59395A2C0A83A (VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * __this, const RuntimeMethod* method)
{
	Exception_t * __last_unhandled_exception = 0;
	il2cpp::utils::ExceptionSupportStack<int32_t, 1> __leave_targets;
	{
	}

IL_0001:
	try
	{ // begin try (depth: 1)
		VFXEventAttribute_Release_m9AEDE0F9E70188F9737D68638733D70DB4814983(__this, /*hidden argument*/NULL);
		IL2CPP_LEAVE(0x13, FINALLY_000b);
	} // end try (depth: 1)
	catch(Il2CppExceptionWrapper& e)
	{
		__last_unhandled_exception = (Exception_t *)e.ex;
		goto FINALLY_000b;
	}

FINALLY_000b:
	{ // begin finally (depth: 1)
		Object_Finalize_mC59C83CF4F7707E425FFA6362931C25D4C36676A(__this, /*hidden argument*/NULL);
		IL2CPP_END_FINALLY(11)
	} // end finally (depth: 1)
	IL2CPP_CLEANUP(11)
	{
		IL2CPP_RETHROW_IF_UNHANDLED(Exception_t *)
		IL2CPP_JUMP_TBL(0x13, IL_0013)
	}

IL_0013:
	{
		return;
	}
}
// System.Void UnityEngine.VFX.VFXEventAttribute::Dispose()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXEventAttribute_Dispose_m56663028C3FD054C89339050249052E8C350D0AF (VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GC_tD6F0377620BF01385965FD29272CF088A4309C0D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		VFXEventAttribute_Release_m9AEDE0F9E70188F9737D68638733D70DB4814983(__this, /*hidden argument*/NULL);
		IL2CPP_RUNTIME_CLASS_INIT(GC_tD6F0377620BF01385965FD29272CF088A4309C0D_il2cpp_TypeInfo_var);
		GC_SuppressFinalize_mFF908DB65A7128F68FC8CAEA84AA753B439FE40E(__this, /*hidden argument*/NULL);
		return;
	}
}
// System.Void UnityEngine.VFX.VFXEventAttribute::Internal_Destroy(System.IntPtr)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXEventAttribute_Internal_Destroy_m4AE4B4E642D451D77F464E8D57985244940C6CAC (intptr_t ___ptr0, const RuntimeMethod* method)
{
	typedef void (*VFXEventAttribute_Internal_Destroy_m4AE4B4E642D451D77F464E8D57985244940C6CAC_ftn) (intptr_t);
	static VFXEventAttribute_Internal_Destroy_m4AE4B4E642D451D77F464E8D57985244940C6CAC_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VFXEventAttribute_Internal_Destroy_m4AE4B4E642D451D77F464E8D57985244940C6CAC_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.VFX.VFXEventAttribute::Internal_Destroy(System.IntPtr)");
	_il2cpp_icall_func(___ptr0);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// Conversion methods for marshalling of: UnityEngine.VFX.VFXExpressionValues
IL2CPP_EXTERN_C void VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883_marshal_pinvoke(const VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883& unmarshaled, VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883_marshaled_pinvoke& marshaled)
{
	marshaled.___m_Ptr_0 = unmarshaled.get_m_Ptr_0();
}
IL2CPP_EXTERN_C void VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883_marshal_pinvoke_back(const VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883_marshaled_pinvoke& marshaled, VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883& unmarshaled)
{
	intptr_t unmarshaled_m_Ptr_temp_0;
	memset((&unmarshaled_m_Ptr_temp_0), 0, sizeof(unmarshaled_m_Ptr_temp_0));
	unmarshaled_m_Ptr_temp_0 = marshaled.___m_Ptr_0;
	unmarshaled.set_m_Ptr_0(unmarshaled_m_Ptr_temp_0);
}
// Conversion method for clean up from marshalling of: UnityEngine.VFX.VFXExpressionValues
IL2CPP_EXTERN_C void VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883_marshal_pinvoke_cleanup(VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883_marshaled_pinvoke& marshaled)
{
}
// Conversion methods for marshalling of: UnityEngine.VFX.VFXExpressionValues
IL2CPP_EXTERN_C void VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883_marshal_com(const VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883& unmarshaled, VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883_marshaled_com& marshaled)
{
	marshaled.___m_Ptr_0 = unmarshaled.get_m_Ptr_0();
}
IL2CPP_EXTERN_C void VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883_marshal_com_back(const VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883_marshaled_com& marshaled, VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883& unmarshaled)
{
	intptr_t unmarshaled_m_Ptr_temp_0;
	memset((&unmarshaled_m_Ptr_temp_0), 0, sizeof(unmarshaled_m_Ptr_temp_0));
	unmarshaled_m_Ptr_temp_0 = marshaled.___m_Ptr_0;
	unmarshaled.set_m_Ptr_0(unmarshaled_m_Ptr_temp_0);
}
// Conversion method for clean up from marshalling of: UnityEngine.VFX.VFXExpressionValues
IL2CPP_EXTERN_C void VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883_marshal_com_cleanup(VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883_marshaled_com& marshaled)
{
}
// System.Void UnityEngine.VFX.VFXExpressionValues::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXExpressionValues__ctor_m4B8043CAE5D33D062707D851DD4E62D9F7BB1320 (VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883 * __this, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		return;
	}
}
// UnityEngine.VFX.VFXExpressionValues UnityEngine.VFX.VFXExpressionValues::CreateExpressionValuesWrapper(System.IntPtr)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883 * VFXExpressionValues_CreateExpressionValuesWrapper_m0CCEFD12B45E894035502DA9B7CDAF716CFE2D2A (intptr_t ___ptr0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883 * V_0 = NULL;
	VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883 * V_1 = NULL;
	{
		VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883 * L_0 = (VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883 *)il2cpp_codegen_object_new(VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883_il2cpp_TypeInfo_var);
		VFXExpressionValues__ctor_m4B8043CAE5D33D062707D851DD4E62D9F7BB1320(L_0, /*hidden argument*/NULL);
		V_0 = L_0;
		VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883 * L_1 = V_0;
		intptr_t L_2 = ___ptr0;
		NullCheck(L_1);
		L_1->set_m_Ptr_0((intptr_t)L_2);
		VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883 * L_3 = V_0;
		V_1 = L_3;
		goto IL_0012;
	}

IL_0012:
	{
		VFXExpressionValues_tFB46D1CD053E9CD5BD04CBE4DB1B0ED24C9C0883 * L_4 = V_1;
		return L_4;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif


// Conversion methods for marshalling of: UnityEngine.VFX.VFXOutputEventArgs
IL2CPP_EXTERN_C void VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5_marshal_pinvoke(const VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5& unmarshaled, VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5_marshaled_pinvoke& marshaled)
{
	Exception_t* ___U3CeventAttributeU3Ek__BackingField_1Exception = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '<eventAttribute>k__BackingField' of type 'VFXOutputEventArgs': Reference type field marshaling is not supported.");
	IL2CPP_RAISE_MANAGED_EXCEPTION(___U3CeventAttributeU3Ek__BackingField_1Exception, NULL);
}
IL2CPP_EXTERN_C void VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5_marshal_pinvoke_back(const VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5_marshaled_pinvoke& marshaled, VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5& unmarshaled)
{
	Exception_t* ___U3CeventAttributeU3Ek__BackingField_1Exception = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '<eventAttribute>k__BackingField' of type 'VFXOutputEventArgs': Reference type field marshaling is not supported.");
	IL2CPP_RAISE_MANAGED_EXCEPTION(___U3CeventAttributeU3Ek__BackingField_1Exception, NULL);
}
// Conversion method for clean up from marshalling of: UnityEngine.VFX.VFXOutputEventArgs
IL2CPP_EXTERN_C void VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5_marshal_pinvoke_cleanup(VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5_marshaled_pinvoke& marshaled)
{
}


// Conversion methods for marshalling of: UnityEngine.VFX.VFXOutputEventArgs
IL2CPP_EXTERN_C void VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5_marshal_com(const VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5& unmarshaled, VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5_marshaled_com& marshaled)
{
	Exception_t* ___U3CeventAttributeU3Ek__BackingField_1Exception = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '<eventAttribute>k__BackingField' of type 'VFXOutputEventArgs': Reference type field marshaling is not supported.");
	IL2CPP_RAISE_MANAGED_EXCEPTION(___U3CeventAttributeU3Ek__BackingField_1Exception, NULL);
}
IL2CPP_EXTERN_C void VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5_marshal_com_back(const VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5_marshaled_com& marshaled, VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5& unmarshaled)
{
	Exception_t* ___U3CeventAttributeU3Ek__BackingField_1Exception = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '<eventAttribute>k__BackingField' of type 'VFXOutputEventArgs': Reference type field marshaling is not supported.");
	IL2CPP_RAISE_MANAGED_EXCEPTION(___U3CeventAttributeU3Ek__BackingField_1Exception, NULL);
}
// Conversion method for clean up from marshalling of: UnityEngine.VFX.VFXOutputEventArgs
IL2CPP_EXTERN_C void VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5_marshal_com_cleanup(VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5_marshaled_com& marshaled)
{
}
// System.Void UnityEngine.VFX.VFXOutputEventArgs::.ctor(System.Int32,UnityEngine.VFX.VFXEventAttribute)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXOutputEventArgs__ctor_m7C49FC1B34F3582FE08D7A1CAA5024C09A86F7A2 (VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5 * __this, int32_t ___nameId0, VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * ___eventAttribute1, const RuntimeMethod* method)
{
	{
		int32_t L_0 = ___nameId0;
		__this->set_U3CnameIdU3Ek__BackingField_0(L_0);
		VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * L_1 = ___eventAttribute1;
		__this->set_U3CeventAttributeU3Ek__BackingField_1(L_1);
		return;
	}
}
IL2CPP_EXTERN_C  void VFXOutputEventArgs__ctor_m7C49FC1B34F3582FE08D7A1CAA5024C09A86F7A2_AdjustorThunk (RuntimeObject * __this, int32_t ___nameId0, VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * ___eventAttribute1, const RuntimeMethod* method)
{
	int32_t _offset = 1;
	VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5 * _thisAdjusted = reinterpret_cast<VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5 *>(__this + _offset);
	VFXOutputEventArgs__ctor_m7C49FC1B34F3582FE08D7A1CAA5024C09A86F7A2(_thisAdjusted, ___nameId0, ___eventAttribute1, method);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void UnityEngine.VFX.VFXSpawnerCallbacks::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXSpawnerCallbacks__ctor_m4F7269BAF4E8085E8628FF6C0EF0BD42BF43B072 (VFXSpawnerCallbacks_t62128B7E3ADA64EBEA4705691DE0F045104801CA * __this, const RuntimeMethod* method)
{
	{
		ScriptableObject__ctor_m8DAE6CDCFA34E16F2543B02CC3669669FF203063(__this, /*hidden argument*/NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// Conversion methods for marshalling of: UnityEngine.VFX.VFXSpawnerState
IL2CPP_EXTERN_C void VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD_marshal_pinvoke(const VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD& unmarshaled, VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD_marshaled_pinvoke& marshaled)
{
	marshaled.___m_Ptr_0 = unmarshaled.get_m_Ptr_0();
	marshaled.___m_Owner_1 = static_cast<int32_t>(unmarshaled.get_m_Owner_1());
}
IL2CPP_EXTERN_C void VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD_marshal_pinvoke_back(const VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD_marshaled_pinvoke& marshaled, VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD& unmarshaled)
{
	intptr_t unmarshaled_m_Ptr_temp_0;
	memset((&unmarshaled_m_Ptr_temp_0), 0, sizeof(unmarshaled_m_Ptr_temp_0));
	unmarshaled_m_Ptr_temp_0 = marshaled.___m_Ptr_0;
	unmarshaled.set_m_Ptr_0(unmarshaled_m_Ptr_temp_0);
	bool unmarshaled_m_Owner_temp_1 = false;
	unmarshaled_m_Owner_temp_1 = static_cast<bool>(marshaled.___m_Owner_1);
	unmarshaled.set_m_Owner_1(unmarshaled_m_Owner_temp_1);
}
// Conversion method for clean up from marshalling of: UnityEngine.VFX.VFXSpawnerState
IL2CPP_EXTERN_C void VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD_marshal_pinvoke_cleanup(VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD_marshaled_pinvoke& marshaled)
{
}
// Conversion methods for marshalling of: UnityEngine.VFX.VFXSpawnerState
IL2CPP_EXTERN_C void VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD_marshal_com(const VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD& unmarshaled, VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD_marshaled_com& marshaled)
{
	marshaled.___m_Ptr_0 = unmarshaled.get_m_Ptr_0();
	marshaled.___m_Owner_1 = static_cast<int32_t>(unmarshaled.get_m_Owner_1());
}
IL2CPP_EXTERN_C void VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD_marshal_com_back(const VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD_marshaled_com& marshaled, VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD& unmarshaled)
{
	intptr_t unmarshaled_m_Ptr_temp_0;
	memset((&unmarshaled_m_Ptr_temp_0), 0, sizeof(unmarshaled_m_Ptr_temp_0));
	unmarshaled_m_Ptr_temp_0 = marshaled.___m_Ptr_0;
	unmarshaled.set_m_Ptr_0(unmarshaled_m_Ptr_temp_0);
	bool unmarshaled_m_Owner_temp_1 = false;
	unmarshaled_m_Owner_temp_1 = static_cast<bool>(marshaled.___m_Owner_1);
	unmarshaled.set_m_Owner_1(unmarshaled_m_Owner_temp_1);
}
// Conversion method for clean up from marshalling of: UnityEngine.VFX.VFXSpawnerState
IL2CPP_EXTERN_C void VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD_marshal_com_cleanup(VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD_marshaled_com& marshaled)
{
}
// System.Void UnityEngine.VFX.VFXSpawnerState::.ctor(System.IntPtr,System.Boolean)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXSpawnerState__ctor_m2A95971A7A6D3701F39FD7A656451CD619C3010E (VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD * __this, intptr_t ___ptr0, bool ___owner1, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		intptr_t L_0 = ___ptr0;
		__this->set_m_Ptr_0((intptr_t)L_0);
		bool L_1 = ___owner1;
		__this->set_m_Owner_1(L_1);
		return;
	}
}
// UnityEngine.VFX.VFXSpawnerState UnityEngine.VFX.VFXSpawnerState::CreateSpawnerStateWrapper()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD * VFXSpawnerState_CreateSpawnerStateWrapper_m350F2A13C5F5542667390CF7578E0CD9AC6A41E6 (const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IntPtr_t_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD * V_0 = NULL;
	VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD * V_1 = NULL;
	{
		VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD * L_0 = (VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD *)il2cpp_codegen_object_new(VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD_il2cpp_TypeInfo_var);
		VFXSpawnerState__ctor_m2A95971A7A6D3701F39FD7A656451CD619C3010E(L_0, (intptr_t)(0), (bool)0, /*hidden argument*/NULL);
		V_0 = L_0;
		VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD * L_1 = V_0;
		V_1 = L_1;
		goto IL_0011;
	}

IL_0011:
	{
		VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD * L_2 = V_1;
		return L_2;
	}
}
// System.Void UnityEngine.VFX.VFXSpawnerState::SetWrapValue(System.IntPtr)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXSpawnerState_SetWrapValue_mA673646567D2B54072EEC275FB79BEBF82C7EE31 (VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD * __this, intptr_t ___ptr0, const RuntimeMethod* method)
{
	bool V_0 = false;
	{
		bool L_0 = __this->get_m_Owner_1();
		V_0 = L_0;
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0017;
		}
	}
	{
		Exception_t * L_2 = (Exception_t *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m8ECDE8ACA7F2E0EF1144BD1200FB5DB2870B5F11(L_2, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralC2149138D4858815F0CE2A684D1424F63B8B965D)), /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_2, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&VFXSpawnerState_SetWrapValue_mA673646567D2B54072EEC275FB79BEBF82C7EE31_RuntimeMethod_var)));
	}

IL_0017:
	{
		intptr_t L_3 = ___ptr0;
		__this->set_m_Ptr_0((intptr_t)L_3);
		return;
	}
}
// System.Void UnityEngine.VFX.VFXSpawnerState::Release()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXSpawnerState_Release_m9D420CAC0353FCBCBF0F2D59AA286ED3D0BC4649 (VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IntPtr_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	int32_t G_B3_0 = 0;
	{
		intptr_t L_0 = __this->get_m_Ptr_0();
		bool L_1;
		L_1 = IntPtr_op_Inequality_mB4DF9161F8D8F48708A2A7BBD7BF626F1EB8C3C8((intptr_t)L_0, (intptr_t)(0), /*hidden argument*/NULL);
		if (!L_1)
		{
			goto IL_001b;
		}
	}
	{
		bool L_2 = __this->get_m_Owner_1();
		G_B3_0 = ((int32_t)(L_2));
		goto IL_001c;
	}

IL_001b:
	{
		G_B3_0 = 0;
	}

IL_001c:
	{
		V_0 = (bool)G_B3_0;
		bool L_3 = V_0;
		if (!L_3)
		{
			goto IL_002e;
		}
	}
	{
		intptr_t L_4 = __this->get_m_Ptr_0();
		VFXSpawnerState_Internal_Destroy_m847E1B71DBC4B8C289D718F62219EE94088FD0F9((intptr_t)L_4, /*hidden argument*/NULL);
	}

IL_002e:
	{
		__this->set_m_Ptr_0((intptr_t)(0));
		return;
	}
}
// System.Void UnityEngine.VFX.VFXSpawnerState::Finalize()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXSpawnerState_Finalize_mDD2AA40919A974E7CE616FE003162A762B806CA5 (VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD * __this, const RuntimeMethod* method)
{
	Exception_t * __last_unhandled_exception = 0;
	il2cpp::utils::ExceptionSupportStack<int32_t, 1> __leave_targets;
	{
	}

IL_0001:
	try
	{ // begin try (depth: 1)
		VFXSpawnerState_Release_m9D420CAC0353FCBCBF0F2D59AA286ED3D0BC4649(__this, /*hidden argument*/NULL);
		IL2CPP_LEAVE(0x13, FINALLY_000b);
	} // end try (depth: 1)
	catch(Il2CppExceptionWrapper& e)
	{
		__last_unhandled_exception = (Exception_t *)e.ex;
		goto FINALLY_000b;
	}

FINALLY_000b:
	{ // begin finally (depth: 1)
		Object_Finalize_mC59C83CF4F7707E425FFA6362931C25D4C36676A(__this, /*hidden argument*/NULL);
		IL2CPP_END_FINALLY(11)
	} // end finally (depth: 1)
	IL2CPP_CLEANUP(11)
	{
		IL2CPP_RETHROW_IF_UNHANDLED(Exception_t *)
		IL2CPP_JUMP_TBL(0x13, IL_0013)
	}

IL_0013:
	{
		return;
	}
}
// System.Void UnityEngine.VFX.VFXSpawnerState::Dispose()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXSpawnerState_Dispose_mE45940AB83108F8F02D81BE4CEDE8C738F1C47F5 (VFXSpawnerState_t5879CC401019E9C9D4F81128147AE52AAED167CD * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GC_tD6F0377620BF01385965FD29272CF088A4309C0D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		VFXSpawnerState_Release_m9D420CAC0353FCBCBF0F2D59AA286ED3D0BC4649(__this, /*hidden argument*/NULL);
		IL2CPP_RUNTIME_CLASS_INIT(GC_tD6F0377620BF01385965FD29272CF088A4309C0D_il2cpp_TypeInfo_var);
		GC_SuppressFinalize_mFF908DB65A7128F68FC8CAEA84AA753B439FE40E(__this, /*hidden argument*/NULL);
		return;
	}
}
// System.Void UnityEngine.VFX.VFXSpawnerState::Internal_Destroy(System.IntPtr)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VFXSpawnerState_Internal_Destroy_m847E1B71DBC4B8C289D718F62219EE94088FD0F9 (intptr_t ___ptr0, const RuntimeMethod* method)
{
	typedef void (*VFXSpawnerState_Internal_Destroy_m847E1B71DBC4B8C289D718F62219EE94088FD0F9_ftn) (intptr_t);
	static VFXSpawnerState_Internal_Destroy_m847E1B71DBC4B8C289D718F62219EE94088FD0F9_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VFXSpawnerState_Internal_Destroy_m847E1B71DBC4B8C289D718F62219EE94088FD0F9_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.VFX.VFXSpawnerState::Internal_Destroy(System.IntPtr)");
	_il2cpp_icall_func(___ptr0);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// UnityEngine.VFX.VisualEffectAsset UnityEngine.VFX.VisualEffect::get_visualEffectAsset()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 * VisualEffect_get_visualEffectAsset_m019D107588568B2C5F1FFA77C626DC986A63AD96 (VisualEffect_t7C6E2AAA4DB4F47960AF2029EA96D4B579B3A4CA * __this, const RuntimeMethod* method)
{
	typedef VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 * (*VisualEffect_get_visualEffectAsset_m019D107588568B2C5F1FFA77C626DC986A63AD96_ftn) (VisualEffect_t7C6E2AAA4DB4F47960AF2029EA96D4B579B3A4CA *);
	static VisualEffect_get_visualEffectAsset_m019D107588568B2C5F1FFA77C626DC986A63AD96_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (VisualEffect_get_visualEffectAsset_m019D107588568B2C5F1FFA77C626DC986A63AD96_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.VFX.VisualEffect::get_visualEffectAsset()");
	VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 * icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
// UnityEngine.VFX.VFXEventAttribute UnityEngine.VFX.VisualEffect::CreateVFXEventAttribute()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * VisualEffect_CreateVFXEventAttribute_mE99FC6DA23D465D06A8547CC5B2502FB0B30D8D6 (VisualEffect_t7C6E2AAA4DB4F47960AF2029EA96D4B579B3A4CA * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * V_0 = NULL;
	bool V_1 = false;
	VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * V_2 = NULL;
	{
		VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 * L_0;
		L_0 = VisualEffect_get_visualEffectAsset_m019D107588568B2C5F1FFA77C626DC986A63AD96(__this, /*hidden argument*/NULL);
		IL2CPP_RUNTIME_CLASS_INIT(Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_m6D7EBC0E6D7E0CE1E9671D21DE14C9158AFB88B2(L_0, (Object_tF2F3778131EFF286AF62B7B013A170F95A91571A *)NULL, /*hidden argument*/NULL);
		V_1 = L_1;
		bool L_2 = V_1;
		if (!L_2)
		{
			goto IL_0015;
		}
	}
	{
		V_2 = (VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF *)NULL;
		goto IL_0025;
	}

IL_0015:
	{
		VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 * L_3;
		L_3 = VisualEffect_get_visualEffectAsset_m019D107588568B2C5F1FFA77C626DC986A63AD96(__this, /*hidden argument*/NULL);
		VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * L_4;
		L_4 = VFXEventAttribute_Internal_InstanciateVFXEventAttribute_m3AD160FECB74D898826FB11ECB937EA9BA0B47BA(L_3, /*hidden argument*/NULL);
		V_0 = L_4;
		VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * L_5 = V_0;
		V_2 = L_5;
		goto IL_0025;
	}

IL_0025:
	{
		VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * L_6 = V_2;
		return L_6;
	}
}
// UnityEngine.VFX.VFXEventAttribute UnityEngine.VFX.VisualEffect::InvokeGetCachedEventAttributeForOutputEvent_Internal(UnityEngine.VFX.VisualEffect)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * VisualEffect_InvokeGetCachedEventAttributeForOutputEvent_Internal_m2535D29D566CCC8B69A8D276C4BF1FB74CDCF496 (VisualEffect_t7C6E2AAA4DB4F47960AF2029EA96D4B579B3A4CA * ___source0, const RuntimeMethod* method)
{
	bool V_0 = false;
	VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * V_1 = NULL;
	bool V_2 = false;
	{
		VisualEffect_t7C6E2AAA4DB4F47960AF2029EA96D4B579B3A4CA * L_0 = ___source0;
		NullCheck(L_0);
		Action_1_t5C8C9298698F95A378E73C4584F33A97EF82A064 * L_1 = L_0->get_outputEventReceived_5();
		V_0 = (bool)((((RuntimeObject*)(Action_1_t5C8C9298698F95A378E73C4584F33A97EF82A064 *)L_1) == ((RuntimeObject*)(RuntimeObject *)NULL))? 1 : 0);
		bool L_2 = V_0;
		if (!L_2)
		{
			goto IL_0012;
		}
	}
	{
		V_1 = (VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF *)NULL;
		goto IL_0034;
	}

IL_0012:
	{
		VisualEffect_t7C6E2AAA4DB4F47960AF2029EA96D4B579B3A4CA * L_3 = ___source0;
		NullCheck(L_3);
		VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * L_4 = L_3->get_m_cachedEventAttribute_4();
		V_2 = (bool)((((RuntimeObject*)(VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF *)L_4) == ((RuntimeObject*)(RuntimeObject *)NULL))? 1 : 0);
		bool L_5 = V_2;
		if (!L_5)
		{
			goto IL_002b;
		}
	}
	{
		VisualEffect_t7C6E2AAA4DB4F47960AF2029EA96D4B579B3A4CA * L_6 = ___source0;
		VisualEffect_t7C6E2AAA4DB4F47960AF2029EA96D4B579B3A4CA * L_7 = ___source0;
		NullCheck(L_7);
		VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * L_8;
		L_8 = VisualEffect_CreateVFXEventAttribute_mE99FC6DA23D465D06A8547CC5B2502FB0B30D8D6(L_7, /*hidden argument*/NULL);
		NullCheck(L_6);
		L_6->set_m_cachedEventAttribute_4(L_8);
	}

IL_002b:
	{
		VisualEffect_t7C6E2AAA4DB4F47960AF2029EA96D4B579B3A4CA * L_9 = ___source0;
		NullCheck(L_9);
		VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * L_10 = L_9->get_m_cachedEventAttribute_4();
		V_1 = L_10;
		goto IL_0034;
	}

IL_0034:
	{
		VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * L_11 = V_1;
		return L_11;
	}
}
// System.Void UnityEngine.VFX.VisualEffect::InvokeOutputEventReceived_Internal(UnityEngine.VFX.VisualEffect,System.Int32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VisualEffect_InvokeOutputEventReceived_Internal_mA75BD48DAF6822A61D161B5EBCB92EE386F8F403 (VisualEffect_t7C6E2AAA4DB4F47960AF2029EA96D4B579B3A4CA * ___source0, int32_t ___eventNameId1, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_Invoke_m253872B5D1C80AFE87652D0B1DDBF95AC54E702D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5  V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		int32_t L_0 = ___eventNameId1;
		VisualEffect_t7C6E2AAA4DB4F47960AF2029EA96D4B579B3A4CA * L_1 = ___source0;
		NullCheck(L_1);
		VFXEventAttribute_tC4E90458100D52776F591CE62B19FF6051F423EF * L_2 = L_1->get_m_cachedEventAttribute_4();
		VFXOutputEventArgs__ctor_m7C49FC1B34F3582FE08D7A1CAA5024C09A86F7A2((VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5 *)(&V_0), L_0, L_2, /*hidden argument*/NULL);
		VisualEffect_t7C6E2AAA4DB4F47960AF2029EA96D4B579B3A4CA * L_3 = ___source0;
		NullCheck(L_3);
		Action_1_t5C8C9298698F95A378E73C4584F33A97EF82A064 * L_4 = L_3->get_outputEventReceived_5();
		VFXOutputEventArgs_tE7E97EDFD67E4561E4412D2E4B1C999F95850BF5  L_5 = V_0;
		NullCheck(L_4);
		Action_1_Invoke_m253872B5D1C80AFE87652D0B1DDBF95AC54E702D(L_4, L_5, /*hidden argument*/Action_1_Invoke_m253872B5D1C80AFE87652D0B1DDBF95AC54E702D_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void UnityEngine.VFX.VisualEffectAsset::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VisualEffectAsset__ctor_m61A7A279415031194C654C892DFB4A1AD19CCC7C (VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50 * __this, const RuntimeMethod* method)
{
	{
		VisualEffectObject__ctor_mC6F115A9348A51326C366ACB2724884050A00B2D(__this, /*hidden argument*/NULL);
		return;
	}
}
// System.Void UnityEngine.VFX.VisualEffectAsset::.cctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VisualEffectAsset__cctor_m7363540DD8CE56375E38B8872AA7E6C23908DF66 (const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral55CE32F73183782A9E356F9B9E36223057BA0BA3);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDD6A1C41DBA2920AB3C87C187F0272E6380C0AAA);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0;
		L_0 = Shader_PropertyToID_m73455E48C146AE645F830EC15177F24B788F7019(_stringLiteralDD6A1C41DBA2920AB3C87C187F0272E6380C0AAA, /*hidden argument*/NULL);
		((VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50_StaticFields*)il2cpp_codegen_static_fields_for(VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50_il2cpp_TypeInfo_var))->set_PlayEventID_4(L_0);
		int32_t L_1;
		L_1 = Shader_PropertyToID_m73455E48C146AE645F830EC15177F24B788F7019(_stringLiteral55CE32F73183782A9E356F9B9E36223057BA0BA3, /*hidden argument*/NULL);
		((VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50_StaticFields*)il2cpp_codegen_static_fields_for(VisualEffectAsset_tEFF95BDCD904AF7D5DEA8CF020C62E23A978EC50_il2cpp_TypeInfo_var))->set_StopEventID_5(L_1);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void UnityEngine.VFX.VisualEffectObject::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VisualEffectObject__ctor_mC6F115A9348A51326C366ACB2724884050A00B2D (VisualEffectObject_tC7804AFDC2B4F2F0CE6833AC467ABC177A1617DB * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_il2cpp_TypeInfo_var);
		Object__ctor_m4DCF5CDB32C2C69290894101A81F473865169279(__this, /*hidden argument*/NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif

﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 UnityEngine.TerrainData UnityEngine.Terrain::get_terrainData()
extern void Terrain_get_terrainData_mDB60C324B3424339C3C9FA6CDF6DC1C9B47D6E41 (void);
// 0x00000002 System.Boolean UnityEngine.Terrain::get_allowAutoConnect()
extern void Terrain_get_allowAutoConnect_mC1B0AC480E9AA5E33EDF412E8F9AA3EB4832BA67 (void);
// 0x00000003 System.Int32 UnityEngine.Terrain::get_groupingID()
extern void Terrain_get_groupingID_m8390315914A192A424C890605D780E638F5E1CC9 (void);
// 0x00000004 System.Void UnityEngine.Terrain::SetNeighbors(UnityEngine.Terrain,UnityEngine.Terrain,UnityEngine.Terrain,UnityEngine.Terrain)
extern void Terrain_SetNeighbors_m8D84FD4852DE0F39C99BF04E6D4363C1869BF59F (void);
// 0x00000005 UnityEngine.Terrain[] UnityEngine.Terrain::get_activeTerrains()
extern void Terrain_get_activeTerrains_mBBD7DAE95FDCB0CD70B6FF83A74ACE69906A6F97 (void);
// 0x00000006 System.Void UnityEngine.Terrain::.ctor()
extern void Terrain__ctor_m3E411CBA0F2F20E56475F1755B7AEDF0C9F57464 (void);
// 0x00000007 System.Int32 UnityEngine.TerrainData::GetBoundaryValue(UnityEngine.TerrainData/BoundaryValueType)
extern void TerrainData_GetBoundaryValue_mFC44C930303B1062C92AB049357BF526E1F58A98 (void);
// 0x00000008 System.Void UnityEngine.TerrainData::.ctor()
extern void TerrainData__ctor_m09DE788EE93388ACD3E80CB586FC2ED551B66ED7 (void);
// 0x00000009 System.Void UnityEngine.TerrainData::Internal_Create(UnityEngine.TerrainData)
extern void TerrainData_Internal_Create_m4360F0526F3A270D162AA9B2B31C1ACDB2E7400E (void);
// 0x0000000A UnityEngine.Vector3 UnityEngine.TerrainData::get_size()
extern void TerrainData_get_size_mF68B76A49498AE26C506D77483EA81E4F816EB15 (void);
// 0x0000000B System.Single UnityEngine.TerrainData::GetAlphamapResolutionInternal()
extern void TerrainData_GetAlphamapResolutionInternal_mB3D8631E512C887B38CE96496428B803C3837CCB (void);
// 0x0000000C UnityEngine.Terrain[] UnityEngine.TerrainData::get_users()
extern void TerrainData_get_users_m4BBC80BD0296525664EB84FE7DD6F1ABAE1CAF0F (void);
// 0x0000000D System.Void UnityEngine.TerrainData::.cctor()
extern void TerrainData__cctor_m723BAB5B9E379C9D0E0E55FEF9CABD2AD16855DF (void);
// 0x0000000E System.Void UnityEngine.TerrainData::get_size_Injected(UnityEngine.Vector3&)
extern void TerrainData_get_size_Injected_m181495692C7B755ACD1D7F7F115A2CE8DC6A9E64 (void);
// 0x0000000F System.Void UnityEngine.Experimental.TerrainAPI.TerrainCallbacks::InvokeHeightmapChangedCallback(UnityEngine.TerrainData,UnityEngine.RectInt,System.Boolean)
extern void TerrainCallbacks_InvokeHeightmapChangedCallback_mA5D5B5508FE82CB9E224C0D46376683F4A57D44F (void);
// 0x00000010 System.Void UnityEngine.Experimental.TerrainAPI.TerrainCallbacks::InvokeTextureChangedCallback(UnityEngine.TerrainData,System.String,UnityEngine.RectInt,System.Boolean)
extern void TerrainCallbacks_InvokeTextureChangedCallback_m74640A64BDE3AB8112DA3BD232EAA5B84BFC12B5 (void);
// 0x00000011 System.Void UnityEngine.Experimental.TerrainAPI.TerrainCallbacks/HeightmapChangedCallback::.ctor(System.Object,System.IntPtr)
extern void HeightmapChangedCallback__ctor_mB63473491843FCAFE4EC51977A276DF20F11B1D0 (void);
// 0x00000012 System.Void UnityEngine.Experimental.TerrainAPI.TerrainCallbacks/HeightmapChangedCallback::Invoke(UnityEngine.Terrain,UnityEngine.RectInt,System.Boolean)
extern void HeightmapChangedCallback_Invoke_m24BDB8F85D5AC1B4B183E8C698905E3281CB4489 (void);
// 0x00000013 System.IAsyncResult UnityEngine.Experimental.TerrainAPI.TerrainCallbacks/HeightmapChangedCallback::BeginInvoke(UnityEngine.Terrain,UnityEngine.RectInt,System.Boolean,System.AsyncCallback,System.Object)
extern void HeightmapChangedCallback_BeginInvoke_m590DAC8F14CB6AC982D6FE89C27ACF10CFA17E05 (void);
// 0x00000014 System.Void UnityEngine.Experimental.TerrainAPI.TerrainCallbacks/HeightmapChangedCallback::EndInvoke(System.IAsyncResult)
extern void HeightmapChangedCallback_EndInvoke_m015EB6B241A8FC17A0870FC57A1048520DCEB1E0 (void);
// 0x00000015 System.Void UnityEngine.Experimental.TerrainAPI.TerrainCallbacks/TextureChangedCallback::.ctor(System.Object,System.IntPtr)
extern void TextureChangedCallback__ctor_m7086172D805BDFEFEF9901EAC1C78904DBB63D29 (void);
// 0x00000016 System.Void UnityEngine.Experimental.TerrainAPI.TerrainCallbacks/TextureChangedCallback::Invoke(UnityEngine.Terrain,System.String,UnityEngine.RectInt,System.Boolean)
extern void TextureChangedCallback_Invoke_mC92D41CF0240EA1783C1A1816696EA19895F5569 (void);
// 0x00000017 System.IAsyncResult UnityEngine.Experimental.TerrainAPI.TerrainCallbacks/TextureChangedCallback::BeginInvoke(UnityEngine.Terrain,System.String,UnityEngine.RectInt,System.Boolean,System.AsyncCallback,System.Object)
extern void TextureChangedCallback_BeginInvoke_mE19FD540CF24CED1C990B54DE4A84A270C5BA37C (void);
// 0x00000018 System.Void UnityEngine.Experimental.TerrainAPI.TerrainCallbacks/TextureChangedCallback::EndInvoke(System.IAsyncResult)
extern void TextureChangedCallback_EndInvoke_mE86E8C09F0F8EB087F90979A86270204AB551B84 (void);
// 0x00000019 System.Boolean UnityEngine.Experimental.TerrainAPI.TerrainUtility::HasValidTerrains()
extern void TerrainUtility_HasValidTerrains_mA4DAA7367522DD6ACFC5D6624DC2E0FAB3F40B4B (void);
// 0x0000001A System.Void UnityEngine.Experimental.TerrainAPI.TerrainUtility::ClearConnectivity()
extern void TerrainUtility_ClearConnectivity_m09D1277D3FA3236F1D9AC9811F34E2012B58D4FC (void);
// 0x0000001B UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainGroups UnityEngine.Experimental.TerrainAPI.TerrainUtility::CollectTerrains(System.Boolean)
extern void TerrainUtility_CollectTerrains_mF927BAF0EB1876EC5352A27D90644D682DF69930 (void);
// 0x0000001C System.Void UnityEngine.Experimental.TerrainAPI.TerrainUtility::AutoConnect()
extern void TerrainUtility_AutoConnect_m16A73EC80625CA673352BA7743FF9330FAEABBB8 (void);
// 0x0000001D UnityEngine.Terrain UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainMap::GetTerrain(System.Int32,System.Int32)
extern void TerrainMap_GetTerrain_mF027E4E4677131A19CA44E9A22CCB89101145006 (void);
// 0x0000001E UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainMap UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainMap::CreateFromPlacement(UnityEngine.Terrain,UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainMap/TerrainFilter,System.Boolean)
extern void TerrainMap_CreateFromPlacement_m27545DA78FF571C828EE1C17125698971DC75B01 (void);
// 0x0000001F UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainMap UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainMap::CreateFromPlacement(UnityEngine.Vector2,UnityEngine.Vector2,UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainMap/TerrainFilter,System.Boolean)
extern void TerrainMap_CreateFromPlacement_m36D0BF083030EDF5D7A0391089797CF7B95F5F3F (void);
// 0x00000020 System.Void UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainMap::.ctor()
extern void TerrainMap__ctor_m0A16A2E6ED5C4EFB2F87D72A5665EF7C4E62F761 (void);
// 0x00000021 System.Void UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainMap::AddTerrainInternal(System.Int32,System.Int32,UnityEngine.Terrain)
extern void TerrainMap_AddTerrainInternal_m82F62E3018D1D2A6E48FB7361DB6531F0E9BEB79 (void);
// 0x00000022 System.Boolean UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainMap::TryToAddTerrain(System.Int32,System.Int32,UnityEngine.Terrain)
extern void TerrainMap_TryToAddTerrain_m49A7085766F102EADE7E4A29259232F399735C61 (void);
// 0x00000023 System.Void UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainMap::ValidateTerrain(System.Int32,System.Int32)
extern void TerrainMap_ValidateTerrain_mFE264FDE78C3D68285943250BC9FABAC89D85764 (void);
// 0x00000024 UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainMap/ErrorCode UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainMap::Validate()
extern void TerrainMap_Validate_m9CD6FAF70E4F90C896BF25F083BC0A7F21C8FA56 (void);
// 0x00000025 System.Void UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainMap/TerrainFilter::.ctor(System.Object,System.IntPtr)
extern void TerrainFilter__ctor_m6A1F2AE7CF7A3B502AFBCB351B615EBBE942B838 (void);
// 0x00000026 System.Boolean UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainMap/TerrainFilter::Invoke(UnityEngine.Terrain)
extern void TerrainFilter_Invoke_m48E69E662BC21917E57559702D1F9D94E4F762F7 (void);
// 0x00000027 System.IAsyncResult UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainMap/TerrainFilter::BeginInvoke(UnityEngine.Terrain,System.AsyncCallback,System.Object)
extern void TerrainFilter_BeginInvoke_m4C17FEFE5AE0498C9A88E63A7ABEA673CD31C949 (void);
// 0x00000028 System.Boolean UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainMap/TerrainFilter::EndInvoke(System.IAsyncResult)
extern void TerrainFilter_EndInvoke_m483F3A9363FE8FDF3B5022AE2C284ACE661857B0 (void);
// 0x00000029 System.Void UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainMap/TileCoord::.ctor(System.Int32,System.Int32)
extern void TileCoord__ctor_m9EED41FD3E08320CDA102E34DC65236E5137F155 (void);
// 0x0000002A System.Void UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainMap/<>c__DisplayClass4_0::.ctor()
extern void U3CU3Ec__DisplayClass4_0__ctor_mF6CE52C3D202B71510907E3EDCA198C369468888 (void);
// 0x0000002B System.Boolean UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainMap/<>c__DisplayClass4_0::<CreateFromPlacement>b__0(UnityEngine.Terrain)
extern void U3CU3Ec__DisplayClass4_0_U3CCreateFromPlacementU3Eb__0_mA0E2295171D220FA7ABA12660D2CB357BC721653 (void);
// 0x0000002C System.Void UnityEngine.Experimental.TerrainAPI.TerrainUtility/TerrainGroups::.ctor()
extern void TerrainGroups__ctor_mA9F11D4BE52D80563D0D31788BA80C8F5381FFB1 (void);
// 0x0000002D System.Void UnityEngine.Experimental.TerrainAPI.TerrainUtility/<>c__DisplayClass4_0::.ctor()
extern void U3CU3Ec__DisplayClass4_0__ctor_m857F329AF653D7F052DCF0BE6511BFE40CD13653 (void);
// 0x0000002E System.Void UnityEngine.Experimental.TerrainAPI.TerrainUtility/<>c__DisplayClass4_1::.ctor()
extern void U3CU3Ec__DisplayClass4_1__ctor_m2B5F521527B39BE091B856058F67DC7E3DE4B345 (void);
// 0x0000002F System.Boolean UnityEngine.Experimental.TerrainAPI.TerrainUtility/<>c__DisplayClass4_1::<CollectTerrains>b__0(UnityEngine.Terrain)
extern void U3CU3Ec__DisplayClass4_1_U3CCollectTerrainsU3Eb__0_m539C07F9B8F371A9E9C09A8AFD003DD4163C7810 (void);
static Il2CppMethodPointer s_methodPointers[47] = 
{
	Terrain_get_terrainData_mDB60C324B3424339C3C9FA6CDF6DC1C9B47D6E41,
	Terrain_get_allowAutoConnect_mC1B0AC480E9AA5E33EDF412E8F9AA3EB4832BA67,
	Terrain_get_groupingID_m8390315914A192A424C890605D780E638F5E1CC9,
	Terrain_SetNeighbors_m8D84FD4852DE0F39C99BF04E6D4363C1869BF59F,
	Terrain_get_activeTerrains_mBBD7DAE95FDCB0CD70B6FF83A74ACE69906A6F97,
	Terrain__ctor_m3E411CBA0F2F20E56475F1755B7AEDF0C9F57464,
	TerrainData_GetBoundaryValue_mFC44C930303B1062C92AB049357BF526E1F58A98,
	TerrainData__ctor_m09DE788EE93388ACD3E80CB586FC2ED551B66ED7,
	TerrainData_Internal_Create_m4360F0526F3A270D162AA9B2B31C1ACDB2E7400E,
	TerrainData_get_size_mF68B76A49498AE26C506D77483EA81E4F816EB15,
	TerrainData_GetAlphamapResolutionInternal_mB3D8631E512C887B38CE96496428B803C3837CCB,
	TerrainData_get_users_m4BBC80BD0296525664EB84FE7DD6F1ABAE1CAF0F,
	TerrainData__cctor_m723BAB5B9E379C9D0E0E55FEF9CABD2AD16855DF,
	TerrainData_get_size_Injected_m181495692C7B755ACD1D7F7F115A2CE8DC6A9E64,
	TerrainCallbacks_InvokeHeightmapChangedCallback_mA5D5B5508FE82CB9E224C0D46376683F4A57D44F,
	TerrainCallbacks_InvokeTextureChangedCallback_m74640A64BDE3AB8112DA3BD232EAA5B84BFC12B5,
	HeightmapChangedCallback__ctor_mB63473491843FCAFE4EC51977A276DF20F11B1D0,
	HeightmapChangedCallback_Invoke_m24BDB8F85D5AC1B4B183E8C698905E3281CB4489,
	HeightmapChangedCallback_BeginInvoke_m590DAC8F14CB6AC982D6FE89C27ACF10CFA17E05,
	HeightmapChangedCallback_EndInvoke_m015EB6B241A8FC17A0870FC57A1048520DCEB1E0,
	TextureChangedCallback__ctor_m7086172D805BDFEFEF9901EAC1C78904DBB63D29,
	TextureChangedCallback_Invoke_mC92D41CF0240EA1783C1A1816696EA19895F5569,
	TextureChangedCallback_BeginInvoke_mE19FD540CF24CED1C990B54DE4A84A270C5BA37C,
	TextureChangedCallback_EndInvoke_mE86E8C09F0F8EB087F90979A86270204AB551B84,
	TerrainUtility_HasValidTerrains_mA4DAA7367522DD6ACFC5D6624DC2E0FAB3F40B4B,
	TerrainUtility_ClearConnectivity_m09D1277D3FA3236F1D9AC9811F34E2012B58D4FC,
	TerrainUtility_CollectTerrains_mF927BAF0EB1876EC5352A27D90644D682DF69930,
	TerrainUtility_AutoConnect_m16A73EC80625CA673352BA7743FF9330FAEABBB8,
	TerrainMap_GetTerrain_mF027E4E4677131A19CA44E9A22CCB89101145006,
	TerrainMap_CreateFromPlacement_m27545DA78FF571C828EE1C17125698971DC75B01,
	TerrainMap_CreateFromPlacement_m36D0BF083030EDF5D7A0391089797CF7B95F5F3F,
	TerrainMap__ctor_m0A16A2E6ED5C4EFB2F87D72A5665EF7C4E62F761,
	TerrainMap_AddTerrainInternal_m82F62E3018D1D2A6E48FB7361DB6531F0E9BEB79,
	TerrainMap_TryToAddTerrain_m49A7085766F102EADE7E4A29259232F399735C61,
	TerrainMap_ValidateTerrain_mFE264FDE78C3D68285943250BC9FABAC89D85764,
	TerrainMap_Validate_m9CD6FAF70E4F90C896BF25F083BC0A7F21C8FA56,
	TerrainFilter__ctor_m6A1F2AE7CF7A3B502AFBCB351B615EBBE942B838,
	TerrainFilter_Invoke_m48E69E662BC21917E57559702D1F9D94E4F762F7,
	TerrainFilter_BeginInvoke_m4C17FEFE5AE0498C9A88E63A7ABEA673CD31C949,
	TerrainFilter_EndInvoke_m483F3A9363FE8FDF3B5022AE2C284ACE661857B0,
	TileCoord__ctor_m9EED41FD3E08320CDA102E34DC65236E5137F155,
	U3CU3Ec__DisplayClass4_0__ctor_mF6CE52C3D202B71510907E3EDCA198C369468888,
	U3CU3Ec__DisplayClass4_0_U3CCreateFromPlacementU3Eb__0_mA0E2295171D220FA7ABA12660D2CB357BC721653,
	TerrainGroups__ctor_mA9F11D4BE52D80563D0D31788BA80C8F5381FFB1,
	U3CU3Ec__DisplayClass4_0__ctor_m857F329AF653D7F052DCF0BE6511BFE40CD13653,
	U3CU3Ec__DisplayClass4_1__ctor_m2B5F521527B39BE091B856058F67DC7E3DE4B345,
	U3CU3Ec__DisplayClass4_1_U3CCollectTerrainsU3Eb__0_m539C07F9B8F371A9E9C09A8AFD003DD4163C7810,
};
extern void TileCoord__ctor_m9EED41FD3E08320CDA102E34DC65236E5137F155_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[1] = 
{
	{ 0x06000029, TileCoord__ctor_m9EED41FD3E08320CDA102E34DC65236E5137F155_AdjustorThunk },
};
static const int32_t s_InvokerIndices[47] = 
{
	3916,
	3863,
	3893,
	898,
	6270,
	3967,
	6000,
	3967,
	6218,
	3965,
	3951,
	3916,
	6289,
	3255,
	5343,
	4987,
	2169,
	1344,
	343,
	3348,
	2169,
	899,
	206,
	3348,
	6256,
	6289,
	6076,
	6289,
	1703,
	5183,
	4904,
	3967,
	1281,
	954,
	1961,
	3893,
	2169,
	2591,
	1129,
	2591,
	1961,
	3967,
	2591,
	3967,
	3967,
	3967,
	2591,
};
extern const CustomAttributesCacheGenerator g_UnityEngine_TerrainModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_TerrainModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_TerrainModule_CodeGenModule = 
{
	"UnityEngine.TerrainModule.dll",
	47,
	s_methodPointers,
	1,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_UnityEngine_TerrainModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

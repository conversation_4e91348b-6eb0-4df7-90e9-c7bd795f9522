﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>
#include <stdint.h>


template <typename R>
struct VirtFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename R, typename T1, typename T2>
struct VirtFuncInvoker2
{
	typedef R (*Func)(void*, T1, T2, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1, T2 p2)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, p1, p2, invokeData.method);
	}
};
template <typename T1, typename T2>
struct VirtActionInvoker2
{
	typedef void (*Action)(void*, T1, T2, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1, T2 p2)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, p1, p2, invokeData.method);
	}
};

// System.Int64[][]
struct Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC;
// System.Byte[]
struct ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726;
// System.Char[]
struct CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34;
// System.Int64[]
struct Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6;
// System.IntPtr[]
struct IntPtrU5BU5D_t27FC72B0409D75AAF33EC42498E8094E95FEE9A6;
// System.Diagnostics.StackTrace[]
struct StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971;
// ZXing.Datamatrix.Encoder.SymbolInfo[]
struct SymbolInfoU5BU5D_tAF72E0E52AD56C26B4E26C9B3C1543D452691080;
// ZXing.QrCode.Internal.Version/ECB[]
struct ECBU5BU5D_tF56F61F7AFE8A79E0DBCA8A4EFE62F6E9D40CEF6;
// ZXing.Datamatrix.Encoder.C40Encoder
struct C40Encoder_tB94DE00FD435E90B141B49317406C62C8B84A82C;
// ZXing.Dimension
struct Dimension_t5CF236AE53396A1D441F5CCC0D7BE55600EF4B83;
// ZXing.Datamatrix.Encoder.EncoderContext
struct EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173;
// System.Text.Encoding
struct Encoding_tE901442411E2E70039D2A4AE77FB81C3D6064827;
// System.Exception
struct Exception_t;
// System.Collections.IDictionary
struct IDictionary_t99871C56B8EC2452AC5C4CF3831695E617B89D3A;
// System.Runtime.Serialization.SafeSerializationManager
struct SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F;
// System.String
struct String_t;
// System.Text.StringBuilder
struct StringBuilder_t;
// ZXing.Datamatrix.Encoder.SymbolInfo
struct SymbolInfo_t5655B64F5E51A9C1D956AEAF957811C751148C03;
// System.Void
struct Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5;
// ZXing.WriterException
struct WriterException_t9577E55BBCF46262001961CFABA3A2B388767DFA;
// ZXing.Datamatrix.Encoder.X12Encoder
struct X12Encoder_t86C3847B2F53AAAAA2A43173DFB2B910A5182DA8;
// BigIntegerLibrary.Base10BigInteger/DigitContainer
struct DigitContainer_tFDA46C9477308140571F6CF1B540B4D067CADC3C;
// BigIntegerLibrary.BigInteger/DigitContainer
struct DigitContainer_t54CF59B9FAD2F0D27764576030066E76496BCF3A;
// ZXing.QrCode.Internal.Version/ECB
struct ECB_t189F63BF68B45D79DADDD123C64695D51E993AE0;
// ZXing.QrCode.Internal.Version/ECBlocks
struct ECBlocks_tD16A91D6A2E9CF6323A3A1E4A7A1DEB501CA02F4;

IL2CPP_EXTERN_C RuntimeClass* Exception_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* StringBuilder_t_il2cpp_TypeInfo_var;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC;
struct Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6;
struct ECBU5BU5D_tF56F61F7AFE8A79E0DBCA8A4EFE62F6E9D40CEF6;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif

// System.Object

struct Il2CppArrayBounds;

// System.Array


// ZXing.Datamatrix.Encoder.C40Encoder
struct C40Encoder_tB94DE00FD435E90B141B49317406C62C8B84A82C  : public RuntimeObject
{
public:

public:
};


// System.String
struct String_t  : public RuntimeObject
{
public:
	// System.Int32 System.String::m_stringLength
	int32_t ___m_stringLength_0;
	// System.Char System.String::m_firstChar
	Il2CppChar ___m_firstChar_1;

public:
	inline static int32_t get_offset_of_m_stringLength_0() { return static_cast<int32_t>(offsetof(String_t, ___m_stringLength_0)); }
	inline int32_t get_m_stringLength_0() const { return ___m_stringLength_0; }
	inline int32_t* get_address_of_m_stringLength_0() { return &___m_stringLength_0; }
	inline void set_m_stringLength_0(int32_t value)
	{
		___m_stringLength_0 = value;
	}

	inline static int32_t get_offset_of_m_firstChar_1() { return static_cast<int32_t>(offsetof(String_t, ___m_firstChar_1)); }
	inline Il2CppChar get_m_firstChar_1() const { return ___m_firstChar_1; }
	inline Il2CppChar* get_address_of_m_firstChar_1() { return &___m_firstChar_1; }
	inline void set_m_firstChar_1(Il2CppChar value)
	{
		___m_firstChar_1 = value;
	}
};


// System.Text.StringBuilder
struct StringBuilder_t  : public RuntimeObject
{
public:
	// System.Char[] System.Text.StringBuilder::m_ChunkChars
	CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* ___m_ChunkChars_0;
	// System.Text.StringBuilder System.Text.StringBuilder::m_ChunkPrevious
	StringBuilder_t * ___m_ChunkPrevious_1;
	// System.Int32 System.Text.StringBuilder::m_ChunkLength
	int32_t ___m_ChunkLength_2;
	// System.Int32 System.Text.StringBuilder::m_ChunkOffset
	int32_t ___m_ChunkOffset_3;
	// System.Int32 System.Text.StringBuilder::m_MaxCapacity
	int32_t ___m_MaxCapacity_4;

public:
	inline static int32_t get_offset_of_m_ChunkChars_0() { return static_cast<int32_t>(offsetof(StringBuilder_t, ___m_ChunkChars_0)); }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* get_m_ChunkChars_0() const { return ___m_ChunkChars_0; }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34** get_address_of_m_ChunkChars_0() { return &___m_ChunkChars_0; }
	inline void set_m_ChunkChars_0(CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* value)
	{
		___m_ChunkChars_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_ChunkChars_0), (void*)value);
	}

	inline static int32_t get_offset_of_m_ChunkPrevious_1() { return static_cast<int32_t>(offsetof(StringBuilder_t, ___m_ChunkPrevious_1)); }
	inline StringBuilder_t * get_m_ChunkPrevious_1() const { return ___m_ChunkPrevious_1; }
	inline StringBuilder_t ** get_address_of_m_ChunkPrevious_1() { return &___m_ChunkPrevious_1; }
	inline void set_m_ChunkPrevious_1(StringBuilder_t * value)
	{
		___m_ChunkPrevious_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_ChunkPrevious_1), (void*)value);
	}

	inline static int32_t get_offset_of_m_ChunkLength_2() { return static_cast<int32_t>(offsetof(StringBuilder_t, ___m_ChunkLength_2)); }
	inline int32_t get_m_ChunkLength_2() const { return ___m_ChunkLength_2; }
	inline int32_t* get_address_of_m_ChunkLength_2() { return &___m_ChunkLength_2; }
	inline void set_m_ChunkLength_2(int32_t value)
	{
		___m_ChunkLength_2 = value;
	}

	inline static int32_t get_offset_of_m_ChunkOffset_3() { return static_cast<int32_t>(offsetof(StringBuilder_t, ___m_ChunkOffset_3)); }
	inline int32_t get_m_ChunkOffset_3() const { return ___m_ChunkOffset_3; }
	inline int32_t* get_address_of_m_ChunkOffset_3() { return &___m_ChunkOffset_3; }
	inline void set_m_ChunkOffset_3(int32_t value)
	{
		___m_ChunkOffset_3 = value;
	}

	inline static int32_t get_offset_of_m_MaxCapacity_4() { return static_cast<int32_t>(offsetof(StringBuilder_t, ___m_MaxCapacity_4)); }
	inline int32_t get_m_MaxCapacity_4() const { return ___m_MaxCapacity_4; }
	inline int32_t* get_address_of_m_MaxCapacity_4() { return &___m_MaxCapacity_4; }
	inline void set_m_MaxCapacity_4(int32_t value)
	{
		___m_MaxCapacity_4 = value;
	}
};


// ZXing.Datamatrix.Encoder.SymbolInfo
struct SymbolInfo_t5655B64F5E51A9C1D956AEAF957811C751148C03  : public RuntimeObject
{
public:
	// System.Boolean ZXing.Datamatrix.Encoder.SymbolInfo::rectangular
	bool ___rectangular_2;
	// System.Int32 ZXing.Datamatrix.Encoder.SymbolInfo::dataCapacity
	int32_t ___dataCapacity_3;
	// System.Int32 ZXing.Datamatrix.Encoder.SymbolInfo::errorCodewords
	int32_t ___errorCodewords_4;
	// System.Int32 ZXing.Datamatrix.Encoder.SymbolInfo::matrixWidth
	int32_t ___matrixWidth_5;
	// System.Int32 ZXing.Datamatrix.Encoder.SymbolInfo::matrixHeight
	int32_t ___matrixHeight_6;
	// System.Int32 ZXing.Datamatrix.Encoder.SymbolInfo::dataRegions
	int32_t ___dataRegions_7;
	// System.Int32 ZXing.Datamatrix.Encoder.SymbolInfo::rsBlockData
	int32_t ___rsBlockData_8;
	// System.Int32 ZXing.Datamatrix.Encoder.SymbolInfo::rsBlockError
	int32_t ___rsBlockError_9;

public:
	inline static int32_t get_offset_of_rectangular_2() { return static_cast<int32_t>(offsetof(SymbolInfo_t5655B64F5E51A9C1D956AEAF957811C751148C03, ___rectangular_2)); }
	inline bool get_rectangular_2() const { return ___rectangular_2; }
	inline bool* get_address_of_rectangular_2() { return &___rectangular_2; }
	inline void set_rectangular_2(bool value)
	{
		___rectangular_2 = value;
	}

	inline static int32_t get_offset_of_dataCapacity_3() { return static_cast<int32_t>(offsetof(SymbolInfo_t5655B64F5E51A9C1D956AEAF957811C751148C03, ___dataCapacity_3)); }
	inline int32_t get_dataCapacity_3() const { return ___dataCapacity_3; }
	inline int32_t* get_address_of_dataCapacity_3() { return &___dataCapacity_3; }
	inline void set_dataCapacity_3(int32_t value)
	{
		___dataCapacity_3 = value;
	}

	inline static int32_t get_offset_of_errorCodewords_4() { return static_cast<int32_t>(offsetof(SymbolInfo_t5655B64F5E51A9C1D956AEAF957811C751148C03, ___errorCodewords_4)); }
	inline int32_t get_errorCodewords_4() const { return ___errorCodewords_4; }
	inline int32_t* get_address_of_errorCodewords_4() { return &___errorCodewords_4; }
	inline void set_errorCodewords_4(int32_t value)
	{
		___errorCodewords_4 = value;
	}

	inline static int32_t get_offset_of_matrixWidth_5() { return static_cast<int32_t>(offsetof(SymbolInfo_t5655B64F5E51A9C1D956AEAF957811C751148C03, ___matrixWidth_5)); }
	inline int32_t get_matrixWidth_5() const { return ___matrixWidth_5; }
	inline int32_t* get_address_of_matrixWidth_5() { return &___matrixWidth_5; }
	inline void set_matrixWidth_5(int32_t value)
	{
		___matrixWidth_5 = value;
	}

	inline static int32_t get_offset_of_matrixHeight_6() { return static_cast<int32_t>(offsetof(SymbolInfo_t5655B64F5E51A9C1D956AEAF957811C751148C03, ___matrixHeight_6)); }
	inline int32_t get_matrixHeight_6() const { return ___matrixHeight_6; }
	inline int32_t* get_address_of_matrixHeight_6() { return &___matrixHeight_6; }
	inline void set_matrixHeight_6(int32_t value)
	{
		___matrixHeight_6 = value;
	}

	inline static int32_t get_offset_of_dataRegions_7() { return static_cast<int32_t>(offsetof(SymbolInfo_t5655B64F5E51A9C1D956AEAF957811C751148C03, ___dataRegions_7)); }
	inline int32_t get_dataRegions_7() const { return ___dataRegions_7; }
	inline int32_t* get_address_of_dataRegions_7() { return &___dataRegions_7; }
	inline void set_dataRegions_7(int32_t value)
	{
		___dataRegions_7 = value;
	}

	inline static int32_t get_offset_of_rsBlockData_8() { return static_cast<int32_t>(offsetof(SymbolInfo_t5655B64F5E51A9C1D956AEAF957811C751148C03, ___rsBlockData_8)); }
	inline int32_t get_rsBlockData_8() const { return ___rsBlockData_8; }
	inline int32_t* get_address_of_rsBlockData_8() { return &___rsBlockData_8; }
	inline void set_rsBlockData_8(int32_t value)
	{
		___rsBlockData_8 = value;
	}

	inline static int32_t get_offset_of_rsBlockError_9() { return static_cast<int32_t>(offsetof(SymbolInfo_t5655B64F5E51A9C1D956AEAF957811C751148C03, ___rsBlockError_9)); }
	inline int32_t get_rsBlockError_9() const { return ___rsBlockError_9; }
	inline int32_t* get_address_of_rsBlockError_9() { return &___rsBlockError_9; }
	inline void set_rsBlockError_9(int32_t value)
	{
		___rsBlockError_9 = value;
	}
};


// System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52  : public RuntimeObject
{
public:

public:
};

// Native definition for P/Invoke marshalling of System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshaled_com
{
};

// BigIntegerLibrary.Base10BigInteger/DigitContainer
struct DigitContainer_tFDA46C9477308140571F6CF1B540B4D067CADC3C  : public RuntimeObject
{
public:
	// System.Int64[][] BigIntegerLibrary.Base10BigInteger/DigitContainer::digits
	Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC* ___digits_0;

public:
	inline static int32_t get_offset_of_digits_0() { return static_cast<int32_t>(offsetof(DigitContainer_tFDA46C9477308140571F6CF1B540B4D067CADC3C, ___digits_0)); }
	inline Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC* get_digits_0() const { return ___digits_0; }
	inline Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC** get_address_of_digits_0() { return &___digits_0; }
	inline void set_digits_0(Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC* value)
	{
		___digits_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___digits_0), (void*)value);
	}
};


// BigIntegerLibrary.BigInteger/DigitContainer
struct DigitContainer_t54CF59B9FAD2F0D27764576030066E76496BCF3A  : public RuntimeObject
{
public:
	// System.Int64[][] BigIntegerLibrary.BigInteger/DigitContainer::digits
	Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC* ___digits_0;

public:
	inline static int32_t get_offset_of_digits_0() { return static_cast<int32_t>(offsetof(DigitContainer_t54CF59B9FAD2F0D27764576030066E76496BCF3A, ___digits_0)); }
	inline Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC* get_digits_0() const { return ___digits_0; }
	inline Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC** get_address_of_digits_0() { return &___digits_0; }
	inline void set_digits_0(Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC* value)
	{
		___digits_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___digits_0), (void*)value);
	}
};


// ZXing.QrCode.Internal.Version/ECB
struct ECB_t189F63BF68B45D79DADDD123C64695D51E993AE0  : public RuntimeObject
{
public:
	// System.Int32 ZXing.QrCode.Internal.Version/ECB::count
	int32_t ___count_0;
	// System.Int32 ZXing.QrCode.Internal.Version/ECB::dataCodewords
	int32_t ___dataCodewords_1;

public:
	inline static int32_t get_offset_of_count_0() { return static_cast<int32_t>(offsetof(ECB_t189F63BF68B45D79DADDD123C64695D51E993AE0, ___count_0)); }
	inline int32_t get_count_0() const { return ___count_0; }
	inline int32_t* get_address_of_count_0() { return &___count_0; }
	inline void set_count_0(int32_t value)
	{
		___count_0 = value;
	}

	inline static int32_t get_offset_of_dataCodewords_1() { return static_cast<int32_t>(offsetof(ECB_t189F63BF68B45D79DADDD123C64695D51E993AE0, ___dataCodewords_1)); }
	inline int32_t get_dataCodewords_1() const { return ___dataCodewords_1; }
	inline int32_t* get_address_of_dataCodewords_1() { return &___dataCodewords_1; }
	inline void set_dataCodewords_1(int32_t value)
	{
		___dataCodewords_1 = value;
	}
};


// ZXing.QrCode.Internal.Version/ECBlocks
struct ECBlocks_tD16A91D6A2E9CF6323A3A1E4A7A1DEB501CA02F4  : public RuntimeObject
{
public:
	// System.Int32 ZXing.QrCode.Internal.Version/ECBlocks::ecCodewordsPerBlock
	int32_t ___ecCodewordsPerBlock_0;
	// ZXing.QrCode.Internal.Version/ECB[] ZXing.QrCode.Internal.Version/ECBlocks::ecBlocks
	ECBU5BU5D_tF56F61F7AFE8A79E0DBCA8A4EFE62F6E9D40CEF6* ___ecBlocks_1;

public:
	inline static int32_t get_offset_of_ecCodewordsPerBlock_0() { return static_cast<int32_t>(offsetof(ECBlocks_tD16A91D6A2E9CF6323A3A1E4A7A1DEB501CA02F4, ___ecCodewordsPerBlock_0)); }
	inline int32_t get_ecCodewordsPerBlock_0() const { return ___ecCodewordsPerBlock_0; }
	inline int32_t* get_address_of_ecCodewordsPerBlock_0() { return &___ecCodewordsPerBlock_0; }
	inline void set_ecCodewordsPerBlock_0(int32_t value)
	{
		___ecCodewordsPerBlock_0 = value;
	}

	inline static int32_t get_offset_of_ecBlocks_1() { return static_cast<int32_t>(offsetof(ECBlocks_tD16A91D6A2E9CF6323A3A1E4A7A1DEB501CA02F4, ___ecBlocks_1)); }
	inline ECBU5BU5D_tF56F61F7AFE8A79E0DBCA8A4EFE62F6E9D40CEF6* get_ecBlocks_1() const { return ___ecBlocks_1; }
	inline ECBU5BU5D_tF56F61F7AFE8A79E0DBCA8A4EFE62F6E9D40CEF6** get_address_of_ecBlocks_1() { return &___ecBlocks_1; }
	inline void set_ecBlocks_1(ECBU5BU5D_tF56F61F7AFE8A79E0DBCA8A4EFE62F6E9D40CEF6* value)
	{
		___ecBlocks_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___ecBlocks_1), (void*)value);
	}
};


// System.Boolean
struct Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37 
{
public:
	// System.Boolean System.Boolean::m_value
	bool ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37, ___m_value_0)); }
	inline bool get_m_value_0() const { return ___m_value_0; }
	inline bool* get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(bool value)
	{
		___m_value_0 = value;
	}
};


// System.Char
struct Char_tFF60D8E7E89A20BE2294A003734341BD1DF43E14 
{
public:
	// System.Char System.Char::m_value
	Il2CppChar ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(Char_tFF60D8E7E89A20BE2294A003734341BD1DF43E14, ___m_value_0)); }
	inline Il2CppChar get_m_value_0() const { return ___m_value_0; }
	inline Il2CppChar* get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(Il2CppChar value)
	{
		___m_value_0 = value;
	}
};


// System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA  : public ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52
{
public:

public:
};

// Native definition for P/Invoke marshalling of System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshaled_com
{
};

// System.Int32
struct Int32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046 
{
public:
	// System.Int32 System.Int32::m_value
	int32_t ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(Int32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046, ___m_value_0)); }
	inline int32_t get_m_value_0() const { return ___m_value_0; }
	inline int32_t* get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(int32_t value)
	{
		___m_value_0 = value;
	}
};


// System.Int64
struct Int64_t378EE0D608BD3107E77238E85F30D2BBD46981F3 
{
public:
	// System.Int64 System.Int64::m_value
	int64_t ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(Int64_t378EE0D608BD3107E77238E85F30D2BBD46981F3, ___m_value_0)); }
	inline int64_t get_m_value_0() const { return ___m_value_0; }
	inline int64_t* get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(int64_t value)
	{
		___m_value_0 = value;
	}
};


// System.IntPtr
struct IntPtr_t 
{
public:
	// System.Void* System.IntPtr::m_value
	void* ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(IntPtr_t, ___m_value_0)); }
	inline void* get_m_value_0() const { return ___m_value_0; }
	inline void** get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(void* value)
	{
		___m_value_0 = value;
	}
};


// System.Void
struct Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5 
{
public:
	union
	{
		struct
		{
		};
		uint8_t Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5__padding[1];
	};

public:
};


// ZXing.Datamatrix.Encoder.X12Encoder
struct X12Encoder_t86C3847B2F53AAAAA2A43173DFB2B910A5182DA8  : public C40Encoder_tB94DE00FD435E90B141B49317406C62C8B84A82C
{
public:

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=1024
struct __StaticArrayInitTypeSizeU3D1024_tD88D03BD18357FED8D67919FE62E21A902020F06 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D1024_tD88D03BD18357FED8D67919FE62E21A902020F06__padding[1024];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=112
struct __StaticArrayInitTypeSizeU3D112_t5E88D1114DB9FC9E4AEAB4A93B514826506D69D1 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D112_t5E88D1114DB9FC9E4AEAB4A93B514826506D69D1__padding[112];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=12
struct __StaticArrayInitTypeSizeU3D12_t5E8C5132C1D4E414AF7C88FE445708DEBCC2030B 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D12_t5E8C5132C1D4E414AF7C88FE445708DEBCC2030B__padding[12];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=124
struct __StaticArrayInitTypeSizeU3D124_t18B287CFD7F667506F553F40D84F27A7D2BC1E80 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D124_t18B287CFD7F667506F553F40D84F27A7D2BC1E80__padding[124];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=128
struct __StaticArrayInitTypeSizeU3D128_t55B152DBCAA1485D090DBE047A980D753A4EDD32 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D128_t55B152DBCAA1485D090DBE047A980D753A4EDD32__padding[128];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=132
struct __StaticArrayInitTypeSizeU3D132_tF2A2F4C4EAA8BCED10AE9DCAE127C57FD4A356CE 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D132_tF2A2F4C4EAA8BCED10AE9DCAE127C57FD4A356CE__padding[132];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=136
struct __StaticArrayInitTypeSizeU3D136_t2AE02B5EA681B6B69ECFE10AE9B85B29BAAAEF27 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D136_t2AE02B5EA681B6B69ECFE10AE9B85B29BAAAEF27__padding[136];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=144
struct __StaticArrayInitTypeSizeU3D144_tECBBBF399306BF943D71B17E449A8B37ACE7C3C8 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D144_tECBBBF399306BF943D71B17E449A8B37ACE7C3C8__padding[144];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=148
struct __StaticArrayInitTypeSizeU3D148_t3ADF8920BECF60099D05CD97116EB058D2D65C8B 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D148_t3ADF8920BECF60099D05CD97116EB058D2D65C8B__padding[148];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=16
struct __StaticArrayInitTypeSizeU3D16_tEB8366B944A7B178B44BC352D301C9EE332FDA9A 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D16_tEB8366B944A7B178B44BC352D301C9EE332FDA9A__padding[16];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=168
struct __StaticArrayInitTypeSizeU3D168_t160878836BD0192DB362CD56863FD4FCBBC60C2A 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D168_t160878836BD0192DB362CD56863FD4FCBBC60C2A__padding[168];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=176
struct __StaticArrayInitTypeSizeU3D176_t3A84247F08496BA68BE96C4147E056F8449442D2 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D176_t3A84247F08496BA68BE96C4147E056F8449442D2__padding[176];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=192
struct __StaticArrayInitTypeSizeU3D192_tF06EF2AC15125841C72809D7417A820FDEA8128C 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D192_tF06EF2AC15125841C72809D7417A820FDEA8128C__padding[192];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=20
struct __StaticArrayInitTypeSizeU3D20_t5FDD2C56454D00B21FF8D90CF5939AB958040C71 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D20_t5FDD2C56454D00B21FF8D90CF5939AB958040C71__padding[20];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=2048
struct __StaticArrayInitTypeSizeU3D2048_t94A664AE5693BC9327031536DF75AD40A2F9A486 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D2048_t94A664AE5693BC9327031536DF75AD40A2F9A486__padding[2048];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=224
struct __StaticArrayInitTypeSizeU3D224_t5449249D9FF879A6A87D640B5A73FB07B2F93622 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D224_t5449249D9FF879A6A87D640B5A73FB07B2F93622__padding[224];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=24
struct __StaticArrayInitTypeSizeU3D24_t657A487F8B280178F98FE8CB4EAC2505E63FB6AE 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D24_t657A487F8B280178F98FE8CB4EAC2505E63FB6AE__padding[24];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=248
struct __StaticArrayInitTypeSizeU3D248_tD9D0173E6F49B9E03936D1641FFDEB7EBD28F19E 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D248_tD9D0173E6F49B9E03936D1641FFDEB7EBD28F19E__padding[248];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=256
struct __StaticArrayInitTypeSizeU3D256_t87E7403B0C408B0DFF6019F89532409F3A41C3E7 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D256_t87E7403B0C408B0DFF6019F89532409F3A41C3E7__padding[256];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=272
struct __StaticArrayInitTypeSizeU3D272_tD18AD4497847098124B45F021766DADD1908F97D 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D272_tD18AD4497847098124B45F021766DADD1908F97D__padding[272];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=28
struct __StaticArrayInitTypeSizeU3D28_tD0893D06763828AB7439156856184AA9094B7A47 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D28_tD0893D06763828AB7439156856184AA9094B7A47__padding[28];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=30
struct __StaticArrayInitTypeSizeU3D30_tA2443CDFCDCD3A176F2D36CBA5FE3EF41C272A7B 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D30_tA2443CDFCDCD3A176F2D36CBA5FE3EF41C272A7B__padding[30];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=32
struct __StaticArrayInitTypeSizeU3D32_tEDF85C46F61FBAB5C99ADF2EECE3EF9BD4A281E8 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D32_tEDF85C46F61FBAB5C99ADF2EECE3EF9BD4A281E8__padding[32];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=3716
struct __StaticArrayInitTypeSizeU3D3716_t3877E01FB1D271FCCE8BA6791CB0008B33380045 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D3716_t3877E01FB1D271FCCE8BA6791CB0008B33380045__padding[3716];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=384
struct __StaticArrayInitTypeSizeU3D384_tDA75C8EE026A789C88D83266801424B9B55318FF 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D384_tDA75C8EE026A789C88D83266801424B9B55318FF__padding[384];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=40
struct __StaticArrayInitTypeSizeU3D40_t853F85ED91EA1CE5FB80704ACC1BC7D5D7DEEBD1 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D40_t853F85ED91EA1CE5FB80704ACC1BC7D5D7DEEBD1__padding[40];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=44
struct __StaticArrayInitTypeSizeU3D44_tE7053F88646550484AC98AEEE723BD7757B3DD3F 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D44_tE7053F88646550484AC98AEEE723BD7757B3DD3F__padding[44];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=48
struct __StaticArrayInitTypeSizeU3D48_t6937524EE8A04B3E657A8911B642E61B81AEC738 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D48_t6937524EE8A04B3E657A8911B642E61B81AEC738__padding[48];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=512
struct __StaticArrayInitTypeSizeU3D512_t75E9C5096AF865640BA6F850385C606F78583091 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D512_t75E9C5096AF865640BA6F850385C606F78583091__padding[512];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=56
struct __StaticArrayInitTypeSizeU3D56_t58DDF62A75716B3ECEF6154BC808C5E458B9CF89 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D56_t58DDF62A75716B3ECEF6154BC808C5E458B9CF89__padding[56];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=64
struct __StaticArrayInitTypeSizeU3D64_tCFAD213F33C170260F20E2171FAE0BFF2E16C7B9 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D64_tCFAD213F33C170260F20E2171FAE0BFF2E16C7B9__padding[64];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=72
struct __StaticArrayInitTypeSizeU3D72_tE241607872E7E771865DEBC10E95AD50DCB9159E 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D72_tE241607872E7E771865DEBC10E95AD50DCB9159E__padding[72];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=80
struct __StaticArrayInitTypeSizeU3D80_tF306AD44144E8AF1C08542990F5512351DC3C5DE 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D80_tF306AD44144E8AF1C08542990F5512351DC3C5DE__padding[80];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=9
struct __StaticArrayInitTypeSizeU3D9_t5BA4587C6739BB04DA346702FF19977C8AAEAF10 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D9_t5BA4587C6739BB04DA346702FF19977C8AAEAF10__padding[9];
	};

public:
};


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=96
struct __StaticArrayInitTypeSizeU3D96_tD1AB0733AC8661E93EFBE62CDBA9B9D8BBC49A77 
{
public:
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D96_tD1AB0733AC8661E93EFBE62CDBA9B9D8BBC49A77__padding[96];
	};

public:
};


// System.Exception
struct Exception_t  : public RuntimeObject
{
public:
	// System.String System.Exception::_className
	String_t* ____className_1;
	// System.String System.Exception::_message
	String_t* ____message_2;
	// System.Collections.IDictionary System.Exception::_data
	RuntimeObject* ____data_3;
	// System.Exception System.Exception::_innerException
	Exception_t * ____innerException_4;
	// System.String System.Exception::_helpURL
	String_t* ____helpURL_5;
	// System.Object System.Exception::_stackTrace
	RuntimeObject * ____stackTrace_6;
	// System.String System.Exception::_stackTraceString
	String_t* ____stackTraceString_7;
	// System.String System.Exception::_remoteStackTraceString
	String_t* ____remoteStackTraceString_8;
	// System.Int32 System.Exception::_remoteStackIndex
	int32_t ____remoteStackIndex_9;
	// System.Object System.Exception::_dynamicMethods
	RuntimeObject * ____dynamicMethods_10;
	// System.Int32 System.Exception::_HResult
	int32_t ____HResult_11;
	// System.String System.Exception::_source
	String_t* ____source_12;
	// System.Runtime.Serialization.SafeSerializationManager System.Exception::_safeSerializationManager
	SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F * ____safeSerializationManager_13;
	// System.Diagnostics.StackTrace[] System.Exception::captured_traces
	StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971* ___captured_traces_14;
	// System.IntPtr[] System.Exception::native_trace_ips
	IntPtrU5BU5D_t27FC72B0409D75AAF33EC42498E8094E95FEE9A6* ___native_trace_ips_15;

public:
	inline static int32_t get_offset_of__className_1() { return static_cast<int32_t>(offsetof(Exception_t, ____className_1)); }
	inline String_t* get__className_1() const { return ____className_1; }
	inline String_t** get_address_of__className_1() { return &____className_1; }
	inline void set__className_1(String_t* value)
	{
		____className_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____className_1), (void*)value);
	}

	inline static int32_t get_offset_of__message_2() { return static_cast<int32_t>(offsetof(Exception_t, ____message_2)); }
	inline String_t* get__message_2() const { return ____message_2; }
	inline String_t** get_address_of__message_2() { return &____message_2; }
	inline void set__message_2(String_t* value)
	{
		____message_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____message_2), (void*)value);
	}

	inline static int32_t get_offset_of__data_3() { return static_cast<int32_t>(offsetof(Exception_t, ____data_3)); }
	inline RuntimeObject* get__data_3() const { return ____data_3; }
	inline RuntimeObject** get_address_of__data_3() { return &____data_3; }
	inline void set__data_3(RuntimeObject* value)
	{
		____data_3 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____data_3), (void*)value);
	}

	inline static int32_t get_offset_of__innerException_4() { return static_cast<int32_t>(offsetof(Exception_t, ____innerException_4)); }
	inline Exception_t * get__innerException_4() const { return ____innerException_4; }
	inline Exception_t ** get_address_of__innerException_4() { return &____innerException_4; }
	inline void set__innerException_4(Exception_t * value)
	{
		____innerException_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____innerException_4), (void*)value);
	}

	inline static int32_t get_offset_of__helpURL_5() { return static_cast<int32_t>(offsetof(Exception_t, ____helpURL_5)); }
	inline String_t* get__helpURL_5() const { return ____helpURL_5; }
	inline String_t** get_address_of__helpURL_5() { return &____helpURL_5; }
	inline void set__helpURL_5(String_t* value)
	{
		____helpURL_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____helpURL_5), (void*)value);
	}

	inline static int32_t get_offset_of__stackTrace_6() { return static_cast<int32_t>(offsetof(Exception_t, ____stackTrace_6)); }
	inline RuntimeObject * get__stackTrace_6() const { return ____stackTrace_6; }
	inline RuntimeObject ** get_address_of__stackTrace_6() { return &____stackTrace_6; }
	inline void set__stackTrace_6(RuntimeObject * value)
	{
		____stackTrace_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____stackTrace_6), (void*)value);
	}

	inline static int32_t get_offset_of__stackTraceString_7() { return static_cast<int32_t>(offsetof(Exception_t, ____stackTraceString_7)); }
	inline String_t* get__stackTraceString_7() const { return ____stackTraceString_7; }
	inline String_t** get_address_of__stackTraceString_7() { return &____stackTraceString_7; }
	inline void set__stackTraceString_7(String_t* value)
	{
		____stackTraceString_7 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____stackTraceString_7), (void*)value);
	}

	inline static int32_t get_offset_of__remoteStackTraceString_8() { return static_cast<int32_t>(offsetof(Exception_t, ____remoteStackTraceString_8)); }
	inline String_t* get__remoteStackTraceString_8() const { return ____remoteStackTraceString_8; }
	inline String_t** get_address_of__remoteStackTraceString_8() { return &____remoteStackTraceString_8; }
	inline void set__remoteStackTraceString_8(String_t* value)
	{
		____remoteStackTraceString_8 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____remoteStackTraceString_8), (void*)value);
	}

	inline static int32_t get_offset_of__remoteStackIndex_9() { return static_cast<int32_t>(offsetof(Exception_t, ____remoteStackIndex_9)); }
	inline int32_t get__remoteStackIndex_9() const { return ____remoteStackIndex_9; }
	inline int32_t* get_address_of__remoteStackIndex_9() { return &____remoteStackIndex_9; }
	inline void set__remoteStackIndex_9(int32_t value)
	{
		____remoteStackIndex_9 = value;
	}

	inline static int32_t get_offset_of__dynamicMethods_10() { return static_cast<int32_t>(offsetof(Exception_t, ____dynamicMethods_10)); }
	inline RuntimeObject * get__dynamicMethods_10() const { return ____dynamicMethods_10; }
	inline RuntimeObject ** get_address_of__dynamicMethods_10() { return &____dynamicMethods_10; }
	inline void set__dynamicMethods_10(RuntimeObject * value)
	{
		____dynamicMethods_10 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____dynamicMethods_10), (void*)value);
	}

	inline static int32_t get_offset_of__HResult_11() { return static_cast<int32_t>(offsetof(Exception_t, ____HResult_11)); }
	inline int32_t get__HResult_11() const { return ____HResult_11; }
	inline int32_t* get_address_of__HResult_11() { return &____HResult_11; }
	inline void set__HResult_11(int32_t value)
	{
		____HResult_11 = value;
	}

	inline static int32_t get_offset_of__source_12() { return static_cast<int32_t>(offsetof(Exception_t, ____source_12)); }
	inline String_t* get__source_12() const { return ____source_12; }
	inline String_t** get_address_of__source_12() { return &____source_12; }
	inline void set__source_12(String_t* value)
	{
		____source_12 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____source_12), (void*)value);
	}

	inline static int32_t get_offset_of__safeSerializationManager_13() { return static_cast<int32_t>(offsetof(Exception_t, ____safeSerializationManager_13)); }
	inline SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F * get__safeSerializationManager_13() const { return ____safeSerializationManager_13; }
	inline SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F ** get_address_of__safeSerializationManager_13() { return &____safeSerializationManager_13; }
	inline void set__safeSerializationManager_13(SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F * value)
	{
		____safeSerializationManager_13 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____safeSerializationManager_13), (void*)value);
	}

	inline static int32_t get_offset_of_captured_traces_14() { return static_cast<int32_t>(offsetof(Exception_t, ___captured_traces_14)); }
	inline StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971* get_captured_traces_14() const { return ___captured_traces_14; }
	inline StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971** get_address_of_captured_traces_14() { return &___captured_traces_14; }
	inline void set_captured_traces_14(StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971* value)
	{
		___captured_traces_14 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___captured_traces_14), (void*)value);
	}

	inline static int32_t get_offset_of_native_trace_ips_15() { return static_cast<int32_t>(offsetof(Exception_t, ___native_trace_ips_15)); }
	inline IntPtrU5BU5D_t27FC72B0409D75AAF33EC42498E8094E95FEE9A6* get_native_trace_ips_15() const { return ___native_trace_ips_15; }
	inline IntPtrU5BU5D_t27FC72B0409D75AAF33EC42498E8094E95FEE9A6** get_address_of_native_trace_ips_15() { return &___native_trace_ips_15; }
	inline void set_native_trace_ips_15(IntPtrU5BU5D_t27FC72B0409D75AAF33EC42498E8094E95FEE9A6* value)
	{
		___native_trace_ips_15 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___native_trace_ips_15), (void*)value);
	}
};

// Native definition for P/Invoke marshalling of System.Exception
struct Exception_t_marshaled_pinvoke
{
	char* ____className_1;
	char* ____message_2;
	RuntimeObject* ____data_3;
	Exception_t_marshaled_pinvoke* ____innerException_4;
	char* ____helpURL_5;
	Il2CppIUnknown* ____stackTrace_6;
	char* ____stackTraceString_7;
	char* ____remoteStackTraceString_8;
	int32_t ____remoteStackIndex_9;
	Il2CppIUnknown* ____dynamicMethods_10;
	int32_t ____HResult_11;
	char* ____source_12;
	SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F * ____safeSerializationManager_13;
	StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971* ___captured_traces_14;
	Il2CppSafeArray/*NONE*/* ___native_trace_ips_15;
};
// Native definition for COM marshalling of System.Exception
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className_1;
	Il2CppChar* ____message_2;
	RuntimeObject* ____data_3;
	Exception_t_marshaled_com* ____innerException_4;
	Il2CppChar* ____helpURL_5;
	Il2CppIUnknown* ____stackTrace_6;
	Il2CppChar* ____stackTraceString_7;
	Il2CppChar* ____remoteStackTraceString_8;
	int32_t ____remoteStackIndex_9;
	Il2CppIUnknown* ____dynamicMethods_10;
	int32_t ____HResult_11;
	Il2CppChar* ____source_12;
	SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F * ____safeSerializationManager_13;
	StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971* ___captured_traces_14;
	Il2CppSafeArray/*NONE*/* ___native_trace_ips_15;
};

// ZXing.Datamatrix.Encoder.SymbolShapeHint
struct SymbolShapeHint_tA7150AB7C2D134B0A1429725E2CF2B356270367D 
{
public:
	// System.Int32 ZXing.Datamatrix.Encoder.SymbolShapeHint::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(SymbolShapeHint_tA7150AB7C2D134B0A1429725E2CF2B356270367D, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// ZXing.OneD.Code128Writer/CType
struct CType_tC124C4BF236F84A456DBE222836D66F492A7D106 
{
public:
	// System.Int32 ZXing.OneD.Code128Writer/CType::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(CType_tC124C4BF236F84A456DBE222836D66F492A7D106, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// ZXing.Datamatrix.Encoder.EncoderContext
struct EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173  : public RuntimeObject
{
public:
	// System.String ZXing.Datamatrix.Encoder.EncoderContext::msg
	String_t* ___msg_0;
	// ZXing.Datamatrix.Encoder.SymbolShapeHint ZXing.Datamatrix.Encoder.EncoderContext::shape
	int32_t ___shape_1;
	// ZXing.Dimension ZXing.Datamatrix.Encoder.EncoderContext::minSize
	Dimension_t5CF236AE53396A1D441F5CCC0D7BE55600EF4B83 * ___minSize_2;
	// ZXing.Dimension ZXing.Datamatrix.Encoder.EncoderContext::maxSize
	Dimension_t5CF236AE53396A1D441F5CCC0D7BE55600EF4B83 * ___maxSize_3;
	// System.Text.StringBuilder ZXing.Datamatrix.Encoder.EncoderContext::codewords
	StringBuilder_t * ___codewords_4;
	// System.Int32 ZXing.Datamatrix.Encoder.EncoderContext::pos
	int32_t ___pos_5;
	// System.Int32 ZXing.Datamatrix.Encoder.EncoderContext::newEncoding
	int32_t ___newEncoding_6;
	// ZXing.Datamatrix.Encoder.SymbolInfo ZXing.Datamatrix.Encoder.EncoderContext::symbolInfo
	SymbolInfo_t5655B64F5E51A9C1D956AEAF957811C751148C03 * ___symbolInfo_7;
	// System.Int32 ZXing.Datamatrix.Encoder.EncoderContext::skipAtEnd
	int32_t ___skipAtEnd_8;

public:
	inline static int32_t get_offset_of_msg_0() { return static_cast<int32_t>(offsetof(EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173, ___msg_0)); }
	inline String_t* get_msg_0() const { return ___msg_0; }
	inline String_t** get_address_of_msg_0() { return &___msg_0; }
	inline void set_msg_0(String_t* value)
	{
		___msg_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___msg_0), (void*)value);
	}

	inline static int32_t get_offset_of_shape_1() { return static_cast<int32_t>(offsetof(EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173, ___shape_1)); }
	inline int32_t get_shape_1() const { return ___shape_1; }
	inline int32_t* get_address_of_shape_1() { return &___shape_1; }
	inline void set_shape_1(int32_t value)
	{
		___shape_1 = value;
	}

	inline static int32_t get_offset_of_minSize_2() { return static_cast<int32_t>(offsetof(EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173, ___minSize_2)); }
	inline Dimension_t5CF236AE53396A1D441F5CCC0D7BE55600EF4B83 * get_minSize_2() const { return ___minSize_2; }
	inline Dimension_t5CF236AE53396A1D441F5CCC0D7BE55600EF4B83 ** get_address_of_minSize_2() { return &___minSize_2; }
	inline void set_minSize_2(Dimension_t5CF236AE53396A1D441F5CCC0D7BE55600EF4B83 * value)
	{
		___minSize_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___minSize_2), (void*)value);
	}

	inline static int32_t get_offset_of_maxSize_3() { return static_cast<int32_t>(offsetof(EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173, ___maxSize_3)); }
	inline Dimension_t5CF236AE53396A1D441F5CCC0D7BE55600EF4B83 * get_maxSize_3() const { return ___maxSize_3; }
	inline Dimension_t5CF236AE53396A1D441F5CCC0D7BE55600EF4B83 ** get_address_of_maxSize_3() { return &___maxSize_3; }
	inline void set_maxSize_3(Dimension_t5CF236AE53396A1D441F5CCC0D7BE55600EF4B83 * value)
	{
		___maxSize_3 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___maxSize_3), (void*)value);
	}

	inline static int32_t get_offset_of_codewords_4() { return static_cast<int32_t>(offsetof(EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173, ___codewords_4)); }
	inline StringBuilder_t * get_codewords_4() const { return ___codewords_4; }
	inline StringBuilder_t ** get_address_of_codewords_4() { return &___codewords_4; }
	inline void set_codewords_4(StringBuilder_t * value)
	{
		___codewords_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___codewords_4), (void*)value);
	}

	inline static int32_t get_offset_of_pos_5() { return static_cast<int32_t>(offsetof(EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173, ___pos_5)); }
	inline int32_t get_pos_5() const { return ___pos_5; }
	inline int32_t* get_address_of_pos_5() { return &___pos_5; }
	inline void set_pos_5(int32_t value)
	{
		___pos_5 = value;
	}

	inline static int32_t get_offset_of_newEncoding_6() { return static_cast<int32_t>(offsetof(EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173, ___newEncoding_6)); }
	inline int32_t get_newEncoding_6() const { return ___newEncoding_6; }
	inline int32_t* get_address_of_newEncoding_6() { return &___newEncoding_6; }
	inline void set_newEncoding_6(int32_t value)
	{
		___newEncoding_6 = value;
	}

	inline static int32_t get_offset_of_symbolInfo_7() { return static_cast<int32_t>(offsetof(EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173, ___symbolInfo_7)); }
	inline SymbolInfo_t5655B64F5E51A9C1D956AEAF957811C751148C03 * get_symbolInfo_7() const { return ___symbolInfo_7; }
	inline SymbolInfo_t5655B64F5E51A9C1D956AEAF957811C751148C03 ** get_address_of_symbolInfo_7() { return &___symbolInfo_7; }
	inline void set_symbolInfo_7(SymbolInfo_t5655B64F5E51A9C1D956AEAF957811C751148C03 * value)
	{
		___symbolInfo_7 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___symbolInfo_7), (void*)value);
	}

	inline static int32_t get_offset_of_skipAtEnd_8() { return static_cast<int32_t>(offsetof(EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173, ___skipAtEnd_8)); }
	inline int32_t get_skipAtEnd_8() const { return ___skipAtEnd_8; }
	inline int32_t* get_address_of_skipAtEnd_8() { return &___skipAtEnd_8; }
	inline void set_skipAtEnd_8(int32_t value)
	{
		___skipAtEnd_8 = value;
	}
};


// ZXing.WriterException
struct WriterException_t9577E55BBCF46262001961CFABA3A2B388767DFA  : public Exception_t
{
public:

public:
};


// System.Object


// System.Object

struct Il2CppArrayBounds;

// System.Array

struct Il2CppArrayBounds;

// System.Array


// ZXing.Datamatrix.Encoder.C40Encoder


// ZXing.Datamatrix.Encoder.C40Encoder


// System.String

struct String_t_StaticFields
{
public:
	// System.String System.String::Empty
	String_t* ___Empty_5;

public:
	inline static int32_t get_offset_of_Empty_5() { return static_cast<int32_t>(offsetof(String_t_StaticFields, ___Empty_5)); }
	inline String_t* get_Empty_5() const { return ___Empty_5; }
	inline String_t** get_address_of_Empty_5() { return &___Empty_5; }
	inline void set_Empty_5(String_t* value)
	{
		___Empty_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Empty_5), (void*)value);
	}
};


// System.String


// System.Text.StringBuilder


// System.Text.StringBuilder


// ZXing.Datamatrix.Encoder.SymbolInfo

struct SymbolInfo_t5655B64F5E51A9C1D956AEAF957811C751148C03_StaticFields
{
public:
	// ZXing.Datamatrix.Encoder.SymbolInfo[] ZXing.Datamatrix.Encoder.SymbolInfo::PROD_SYMBOLS
	SymbolInfoU5BU5D_tAF72E0E52AD56C26B4E26C9B3C1543D452691080* ___PROD_SYMBOLS_0;
	// ZXing.Datamatrix.Encoder.SymbolInfo[] ZXing.Datamatrix.Encoder.SymbolInfo::symbols
	SymbolInfoU5BU5D_tAF72E0E52AD56C26B4E26C9B3C1543D452691080* ___symbols_1;

public:
	inline static int32_t get_offset_of_PROD_SYMBOLS_0() { return static_cast<int32_t>(offsetof(SymbolInfo_t5655B64F5E51A9C1D956AEAF957811C751148C03_StaticFields, ___PROD_SYMBOLS_0)); }
	inline SymbolInfoU5BU5D_tAF72E0E52AD56C26B4E26C9B3C1543D452691080* get_PROD_SYMBOLS_0() const { return ___PROD_SYMBOLS_0; }
	inline SymbolInfoU5BU5D_tAF72E0E52AD56C26B4E26C9B3C1543D452691080** get_address_of_PROD_SYMBOLS_0() { return &___PROD_SYMBOLS_0; }
	inline void set_PROD_SYMBOLS_0(SymbolInfoU5BU5D_tAF72E0E52AD56C26B4E26C9B3C1543D452691080* value)
	{
		___PROD_SYMBOLS_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___PROD_SYMBOLS_0), (void*)value);
	}

	inline static int32_t get_offset_of_symbols_1() { return static_cast<int32_t>(offsetof(SymbolInfo_t5655B64F5E51A9C1D956AEAF957811C751148C03_StaticFields, ___symbols_1)); }
	inline SymbolInfoU5BU5D_tAF72E0E52AD56C26B4E26C9B3C1543D452691080* get_symbols_1() const { return ___symbols_1; }
	inline SymbolInfoU5BU5D_tAF72E0E52AD56C26B4E26C9B3C1543D452691080** get_address_of_symbols_1() { return &___symbols_1; }
	inline void set_symbols_1(SymbolInfoU5BU5D_tAF72E0E52AD56C26B4E26C9B3C1543D452691080* value)
	{
		___symbols_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___symbols_1), (void*)value);
	}
};


// ZXing.Datamatrix.Encoder.SymbolInfo


// System.ValueType


// System.ValueType


// BigIntegerLibrary.Base10BigInteger/DigitContainer


// BigIntegerLibrary.Base10BigInteger/DigitContainer


// BigIntegerLibrary.BigInteger/DigitContainer


// BigIntegerLibrary.BigInteger/DigitContainer


// ZXing.QrCode.Internal.Version/ECB


// ZXing.QrCode.Internal.Version/ECB


// ZXing.QrCode.Internal.Version/ECBlocks


// ZXing.QrCode.Internal.Version/ECBlocks


// System.Boolean

struct Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields
{
public:
	// System.String System.Boolean::TrueString
	String_t* ___TrueString_5;
	// System.String System.Boolean::FalseString
	String_t* ___FalseString_6;

public:
	inline static int32_t get_offset_of_TrueString_5() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields, ___TrueString_5)); }
	inline String_t* get_TrueString_5() const { return ___TrueString_5; }
	inline String_t** get_address_of_TrueString_5() { return &___TrueString_5; }
	inline void set_TrueString_5(String_t* value)
	{
		___TrueString_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___TrueString_5), (void*)value);
	}

	inline static int32_t get_offset_of_FalseString_6() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields, ___FalseString_6)); }
	inline String_t* get_FalseString_6() const { return ___FalseString_6; }
	inline String_t** get_address_of_FalseString_6() { return &___FalseString_6; }
	inline void set_FalseString_6(String_t* value)
	{
		___FalseString_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___FalseString_6), (void*)value);
	}
};


// System.Boolean


// System.Char

struct Char_tFF60D8E7E89A20BE2294A003734341BD1DF43E14_StaticFields
{
public:
	// System.Byte[] System.Char::categoryForLatin1
	ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726* ___categoryForLatin1_3;

public:
	inline static int32_t get_offset_of_categoryForLatin1_3() { return static_cast<int32_t>(offsetof(Char_tFF60D8E7E89A20BE2294A003734341BD1DF43E14_StaticFields, ___categoryForLatin1_3)); }
	inline ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726* get_categoryForLatin1_3() const { return ___categoryForLatin1_3; }
	inline ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726** get_address_of_categoryForLatin1_3() { return &___categoryForLatin1_3; }
	inline void set_categoryForLatin1_3(ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726* value)
	{
		___categoryForLatin1_3 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___categoryForLatin1_3), (void*)value);
	}
};


// System.Char


// System.Enum

struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_StaticFields
{
public:
	// System.Char[] System.Enum::enumSeperatorCharArray
	CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* ___enumSeperatorCharArray_0;

public:
	inline static int32_t get_offset_of_enumSeperatorCharArray_0() { return static_cast<int32_t>(offsetof(Enum_t23B90B40F60E677A8025267341651C94AE079CDA_StaticFields, ___enumSeperatorCharArray_0)); }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* get_enumSeperatorCharArray_0() const { return ___enumSeperatorCharArray_0; }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34** get_address_of_enumSeperatorCharArray_0() { return &___enumSeperatorCharArray_0; }
	inline void set_enumSeperatorCharArray_0(CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* value)
	{
		___enumSeperatorCharArray_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___enumSeperatorCharArray_0), (void*)value);
	}
};


// System.Enum


// System.Int32


// System.Int32


// System.Int64


// System.Int64


// System.IntPtr

struct IntPtr_t_StaticFields
{
public:
	// System.IntPtr System.IntPtr::Zero
	intptr_t ___Zero_1;

public:
	inline static int32_t get_offset_of_Zero_1() { return static_cast<int32_t>(offsetof(IntPtr_t_StaticFields, ___Zero_1)); }
	inline intptr_t get_Zero_1() const { return ___Zero_1; }
	inline intptr_t* get_address_of_Zero_1() { return &___Zero_1; }
	inline void set_Zero_1(intptr_t value)
	{
		___Zero_1 = value;
	}
};


// System.IntPtr


// System.Void


// System.Void


// ZXing.Datamatrix.Encoder.X12Encoder


// ZXing.Datamatrix.Encoder.X12Encoder


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=1024


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=1024


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=112


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=112


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=12


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=12


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=124


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=124


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=128


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=128


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=132


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=132


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=136


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=136


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=144


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=144


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=148


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=148


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=16


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=16


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=168


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=168


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=176


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=176


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=192


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=192


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=20


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=20


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=2048


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=2048


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=224


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=224


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=24


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=24


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=248


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=248


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=256


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=256


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=272


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=272


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=28


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=28


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=30


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=30


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=32


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=32


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=3716


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=3716


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=384


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=384


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=40


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=40


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=44


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=44


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=48


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=48


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=512


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=512


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=56


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=56


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=64


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=64


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=72


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=72


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=80


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=80


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=9


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=9


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=96


// <PrivateImplementationDetails>{C85D4D1A-4642-4199-B42D-05D1B1D6E6C8}/__StaticArrayInitTypeSize=96


// System.Exception

struct Exception_t_StaticFields
{
public:
	// System.Object System.Exception::s_EDILock
	RuntimeObject * ___s_EDILock_0;

public:
	inline static int32_t get_offset_of_s_EDILock_0() { return static_cast<int32_t>(offsetof(Exception_t_StaticFields, ___s_EDILock_0)); }
	inline RuntimeObject * get_s_EDILock_0() const { return ___s_EDILock_0; }
	inline RuntimeObject ** get_address_of_s_EDILock_0() { return &___s_EDILock_0; }
	inline void set_s_EDILock_0(RuntimeObject * value)
	{
		___s_EDILock_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___s_EDILock_0), (void*)value);
	}
};


// System.Exception


// ZXing.Datamatrix.Encoder.SymbolShapeHint


// ZXing.Datamatrix.Encoder.SymbolShapeHint


// ZXing.OneD.Code128Writer/CType


// ZXing.OneD.Code128Writer/CType


// ZXing.Datamatrix.Encoder.EncoderContext

struct EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173_StaticFields
{
public:
	// System.Text.Encoding ZXing.Datamatrix.Encoder.EncoderContext::encoding
	Encoding_tE901442411E2E70039D2A4AE77FB81C3D6064827 * ___encoding_9;

public:
	inline static int32_t get_offset_of_encoding_9() { return static_cast<int32_t>(offsetof(EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173_StaticFields, ___encoding_9)); }
	inline Encoding_tE901442411E2E70039D2A4AE77FB81C3D6064827 * get_encoding_9() const { return ___encoding_9; }
	inline Encoding_tE901442411E2E70039D2A4AE77FB81C3D6064827 ** get_address_of_encoding_9() { return &___encoding_9; }
	inline void set_encoding_9(Encoding_tE901442411E2E70039D2A4AE77FB81C3D6064827 * value)
	{
		___encoding_9 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___encoding_9), (void*)value);
	}
};


// ZXing.Datamatrix.Encoder.EncoderContext


// ZXing.WriterException


// ZXing.WriterException

#ifdef __clang__
#pragma clang diagnostic pop
#endif
// System.Int64[]
struct Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6  : public RuntimeArray
{
public:
	ALIGN_FIELD (8) int64_t m_Items[1];

public:
	inline int64_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline int64_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, int64_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline int64_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline int64_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, int64_t value)
	{
		m_Items[index] = value;
	}
};
// System.Int64[][]
struct Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC  : public RuntimeArray
{
public:
	ALIGN_FIELD (8) Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* m_Items[1];

public:
	inline Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
// ZXing.QrCode.Internal.Version/ECB[]
struct ECBU5BU5D_tF56F61F7AFE8A79E0DBCA8A4EFE62F6E9D40CEF6  : public RuntimeArray
{
public:
	ALIGN_FIELD (8) ECB_t189F63BF68B45D79DADDD123C64695D51E993AE0 * m_Items[1];

public:
	inline ECB_t189F63BF68B45D79DADDD123C64695D51E993AE0 * GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline ECB_t189F63BF68B45D79DADDD123C64695D51E993AE0 ** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, ECB_t189F63BF68B45D79DADDD123C64695D51E993AE0 * value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline ECB_t189F63BF68B45D79DADDD123C64695D51E993AE0 * GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline ECB_t189F63BF68B45D79DADDD123C64695D51E993AE0 ** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, ECB_t189F63BF68B45D79DADDD123C64695D51E993AE0 * value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};



// System.Void System.Exception::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Exception__ctor_m0E9BEC861F6DBED197960E5BA23149543B1D7F5B (Exception_t * __this, const RuntimeMethod* method);
// System.Void System.Exception::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Exception__ctor_m8ECDE8ACA7F2E0EF1144BD1200FB5DB2870B5F11 (Exception_t * __this, String_t* ___message0, const RuntimeMethod* method);
// System.Void System.Exception::.ctor(System.String,System.Exception)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Exception__ctor_mB842BA6E644CDB9DB058F5628BB90DF5EF22C080 (Exception_t * __this, String_t* ___message0, Exception_t * ___innerException1, const RuntimeMethod* method);
// System.Void System.Text.StringBuilder::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void StringBuilder__ctor_m5A81DE19E748F748E19FF13FB6FFD2547F9212D9 (StringBuilder_t * __this, const RuntimeMethod* method);
// System.Char ZXing.Datamatrix.Encoder.EncoderContext::get_CurrentChar()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Il2CppChar EncoderContext_get_CurrentChar_m17CC24F702F8019C3EBC108ABFA352D2F7DAB9A3 (EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * __this, const RuntimeMethod* method);
// System.Int32 ZXing.Datamatrix.Encoder.EncoderContext::get_Pos()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t EncoderContext_get_Pos_mB6FC37697D743FBE341043898E7B659559A5DECE_inline (EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * __this, const RuntimeMethod* method);
// System.Void ZXing.Datamatrix.Encoder.EncoderContext::set_Pos(System.Int32)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void EncoderContext_set_Pos_m11E60F0C66BD176B5030FCEB7A51452F28DCD522_inline (EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * __this, int32_t ___value0, const RuntimeMethod* method);
// System.Int32 System.Text.StringBuilder::get_Length()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t StringBuilder_get_Length_m680500263C59ACFD9582BF2AEEED8E92C87FF5C0 (StringBuilder_t * __this, const RuntimeMethod* method);
// System.Void ZXing.Datamatrix.Encoder.C40Encoder::writeNextTriplet(ZXing.Datamatrix.Encoder.EncoderContext,System.Text.StringBuilder)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void C40Encoder_writeNextTriplet_mC28D9A4AE98BDBF51DFCCDBA97D0C1BCF215D16A (EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * ___context0, StringBuilder_t * ___buffer1, const RuntimeMethod* method);
// System.String ZXing.Datamatrix.Encoder.EncoderContext::get_Message()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* EncoderContext_get_Message_m0CF5394F9C84697DBAD2DC5722C8EADC38E2074F_inline (EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * __this, const RuntimeMethod* method);
// System.Int32 ZXing.Datamatrix.Encoder.HighLevelEncoder::lookAheadTest(System.String,System.Int32,System.Int32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t HighLevelEncoder_lookAheadTest_mE924E6B480D3F76CEF50176348C81A383AADADFA (String_t* ___msg0, int32_t ___startpos1, int32_t ___currentMode2, const RuntimeMethod* method);
// System.Void ZXing.Datamatrix.Encoder.EncoderContext::signalEncoderChange(System.Int32)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void EncoderContext_signalEncoderChange_mADF1DAD66E8381B308016EEB037F640723B8E35D_inline (EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * __this, int32_t ___encoding0, const RuntimeMethod* method);
// System.Boolean ZXing.Datamatrix.Encoder.EncoderContext::get_HasMoreCharacters()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool EncoderContext_get_HasMoreCharacters_m91BCE0DF2F4D8EE92502837726242CE1AB84EC1A (EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * __this, const RuntimeMethod* method);
// System.Text.StringBuilder System.Text.StringBuilder::Append(System.Char)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR StringBuilder_t * StringBuilder_Append_m1ADA3C16E40BF253BCDB5F9579B4DBA9C3E5B22E (StringBuilder_t * __this, Il2CppChar ___value0, const RuntimeMethod* method);
// System.Void ZXing.Datamatrix.Encoder.HighLevelEncoder::illegalCharacter(System.Char)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void HighLevelEncoder_illegalCharacter_m246DB50947B6A720135A793EA1D12E2318B816EE (Il2CppChar ___c0, const RuntimeMethod* method);
// System.Void ZXing.Datamatrix.Encoder.EncoderContext::updateSymbolInfo()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EncoderContext_updateSymbolInfo_mD58B4A0EA2A8ED121E31293CF2464A8A01FB4C7F (EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * __this, const RuntimeMethod* method);
// ZXing.Datamatrix.Encoder.SymbolInfo ZXing.Datamatrix.Encoder.EncoderContext::get_SymbolInfo()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR SymbolInfo_t5655B64F5E51A9C1D956AEAF957811C751148C03 * EncoderContext_get_SymbolInfo_m94C5BE03529789BE8897F207EAC85431F4D2A316_inline (EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * __this, const RuntimeMethod* method);
// System.Int32 ZXing.Datamatrix.Encoder.EncoderContext::get_CodewordCount()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t EncoderContext_get_CodewordCount_m86A2A4383E2C210DA7DAE7D1C180D1BC90238798 (EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * __this, const RuntimeMethod* method);
// System.Int32 ZXing.Datamatrix.Encoder.EncoderContext::get_RemainingCharacters()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t EncoderContext_get_RemainingCharacters_m0D4877E225AB71AF61E4049F92FB38B4340A8FEB (EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * __this, const RuntimeMethod* method);
// System.Void ZXing.Datamatrix.Encoder.EncoderContext::writeCodeword(System.Char)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EncoderContext_writeCodeword_m05A8D6AAF3A9918D18A2F7F9F3A36E4E911F38E8 (EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * __this, Il2CppChar ___codeword0, const RuntimeMethod* method);
// System.Int32 ZXing.Datamatrix.Encoder.EncoderContext::get_NewEncoding()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t EncoderContext_get_NewEncoding_m7B6E49E8BCF0FC93642D2F58B3490B71F1E8AD57_inline (EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * __this, const RuntimeMethod* method);
// System.Void ZXing.Datamatrix.Encoder.C40Encoder::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void C40Encoder__ctor_m2BF4DEE8F84BF12EDB431C857C475D317A381EE9 (C40Encoder_tB94DE00FD435E90B141B49317406C62C8B84A82C * __this, const RuntimeMethod* method);
// System.Void System.Object::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405 (RuntimeObject * __this, const RuntimeMethod* method);
// System.Int32 ZXing.QrCode.Internal.Version/ECB::get_Count()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t ECB_get_Count_mB561812ADB3C72573B7448A4C15F77A8D98FD0E2_inline (ECB_t189F63BF68B45D79DADDD123C64695D51E993AE0 * __this, const RuntimeMethod* method);
// System.Int32 ZXing.QrCode.Internal.Version/ECBlocks::get_NumBlocks()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ECBlocks_get_NumBlocks_mAB97A37082ECA5AF5428F29A61A02873DCC4310A (ECBlocks_tD16A91D6A2E9CF6323A3A1E4A7A1DEB501CA02F4 * __this, const RuntimeMethod* method);
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void ZXing.WriterException::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WriterException__ctor_m676F8A883738D7980A5C802B95D3880BD42E3AC8 (WriterException_t9577E55BBCF46262001961CFABA3A2B388767DFA * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(Exception_t_il2cpp_TypeInfo_var);
		Exception__ctor_m0E9BEC861F6DBED197960E5BA23149543B1D7F5B(__this, /*hidden argument*/NULL);
		return;
	}
}
// System.Void ZXing.WriterException::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WriterException__ctor_mEEBB5A4BEA5F74994195D23D0A4A0AA6420D62DC (WriterException_t9577E55BBCF46262001961CFABA3A2B388767DFA * __this, String_t* ___message0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		String_t* L_0 = ___message0;
		IL2CPP_RUNTIME_CLASS_INIT(Exception_t_il2cpp_TypeInfo_var);
		Exception__ctor_m8ECDE8ACA7F2E0EF1144BD1200FB5DB2870B5F11(__this, L_0, /*hidden argument*/NULL);
		return;
	}
}
// System.Void ZXing.WriterException::.ctor(System.String,System.Exception)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void WriterException__ctor_m7E3C1EE31425FB8CE2B47483EF69EC794696AF4C (WriterException_t9577E55BBCF46262001961CFABA3A2B388767DFA * __this, String_t* ___message0, Exception_t * ___innerExc1, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		String_t* L_0 = ___message0;
		Exception_t * L_1 = ___innerExc1;
		IL2CPP_RUNTIME_CLASS_INIT(Exception_t_il2cpp_TypeInfo_var);
		Exception__ctor_mB842BA6E644CDB9DB058F5628BB90DF5EF22C080(__this, L_0, L_1, /*hidden argument*/NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Int32 ZXing.Datamatrix.Encoder.X12Encoder::get_EncodingMode()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t X12Encoder_get_EncodingMode_m114182FD5F4A0EFCAC7D3ECE20D6D29C4A4A8BB5 (X12Encoder_t86C3847B2F53AAAAA2A43173DFB2B910A5182DA8 * __this, const RuntimeMethod* method)
{
	{
		return 3;
	}
}
// System.Void ZXing.Datamatrix.Encoder.X12Encoder::encode(ZXing.Datamatrix.Encoder.EncoderContext)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void X12Encoder_encode_m1E403628B395E933C2DC4542F9A688F181388549 (X12Encoder_t86C3847B2F53AAAAA2A43173DFB2B910A5182DA8 * __this, EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * ___context0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&StringBuilder_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	StringBuilder_t * V_0 = NULL;
	int32_t V_1 = 0;
	Il2CppChar V_2 = 0x0;
	int32_t V_3 = 0;
	int32_t V_4 = 0;
	{
		StringBuilder_t * L_0 = (StringBuilder_t *)il2cpp_codegen_object_new(StringBuilder_t_il2cpp_TypeInfo_var);
		StringBuilder__ctor_m5A81DE19E748F748E19FF13FB6FFD2547F9212D9(L_0, /*hidden argument*/NULL);
		V_0 = L_0;
		int32_t L_1;
		L_1 = VirtFuncInvoker0< int32_t >::Invoke(5 /* System.Int32 ZXing.Datamatrix.Encoder.C40Encoder::get_EncodingMode() */, __this);
		V_1 = L_1;
		goto IL_0063;
	}

IL_000f:
	{
		EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * L_2 = ___context0;
		NullCheck(L_2);
		Il2CppChar L_3;
		L_3 = EncoderContext_get_CurrentChar_m17CC24F702F8019C3EBC108ABFA352D2F7DAB9A3(L_2, /*hidden argument*/NULL);
		V_2 = L_3;
		EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * L_4 = ___context0;
		EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * L_5 = L_4;
		NullCheck(L_5);
		int32_t L_6;
		L_6 = EncoderContext_get_Pos_mB6FC37697D743FBE341043898E7B659559A5DECE_inline(L_5, /*hidden argument*/NULL);
		NullCheck(L_5);
		EncoderContext_set_Pos_m11E60F0C66BD176B5030FCEB7A51452F28DCD522_inline(L_5, ((int32_t)il2cpp_codegen_add((int32_t)L_6, (int32_t)1)), /*hidden argument*/NULL);
		Il2CppChar L_7 = V_2;
		StringBuilder_t * L_8 = V_0;
		int32_t L_9;
		L_9 = VirtFuncInvoker2< int32_t, Il2CppChar, StringBuilder_t * >::Invoke(8 /* System.Int32 ZXing.Datamatrix.Encoder.C40Encoder::encodeChar(System.Char,System.Text.StringBuilder) */, __this, L_7, L_8);
		StringBuilder_t * L_10 = V_0;
		NullCheck(L_10);
		int32_t L_11;
		L_11 = StringBuilder_get_Length_m680500263C59ACFD9582BF2AEEED8E92C87FF5C0(L_10, /*hidden argument*/NULL);
		V_3 = L_11;
		int32_t L_12 = V_3;
		if (((int32_t)((int32_t)L_12%(int32_t)3)))
		{
			goto IL_0063;
		}
	}
	{
		EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * L_13 = ___context0;
		StringBuilder_t * L_14 = V_0;
		C40Encoder_writeNextTriplet_mC28D9A4AE98BDBF51DFCCDBA97D0C1BCF215D16A(L_13, L_14, /*hidden argument*/NULL);
		EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * L_15 = ___context0;
		NullCheck(L_15);
		String_t* L_16;
		L_16 = EncoderContext_get_Message_m0CF5394F9C84697DBAD2DC5722C8EADC38E2074F_inline(L_15, /*hidden argument*/NULL);
		EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * L_17 = ___context0;
		NullCheck(L_17);
		int32_t L_18;
		L_18 = EncoderContext_get_Pos_mB6FC37697D743FBE341043898E7B659559A5DECE_inline(L_17, /*hidden argument*/NULL);
		int32_t L_19 = V_1;
		int32_t L_20;
		L_20 = HighLevelEncoder_lookAheadTest_mE924E6B480D3F76CEF50176348C81A383AADADFA(L_16, L_18, L_19, /*hidden argument*/NULL);
		V_4 = L_20;
		int32_t L_21 = V_4;
		int32_t L_22 = V_1;
		if ((((int32_t)L_21) == ((int32_t)L_22)))
		{
			goto IL_0063;
		}
	}
	{
		EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * L_23 = ___context0;
		int32_t L_24 = V_4;
		NullCheck(L_23);
		EncoderContext_signalEncoderChange_mADF1DAD66E8381B308016EEB037F640723B8E35D_inline(L_23, L_24, /*hidden argument*/NULL);
		goto IL_006b;
	}

IL_0063:
	{
		EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * L_25 = ___context0;
		NullCheck(L_25);
		bool L_26;
		L_26 = EncoderContext_get_HasMoreCharacters_m91BCE0DF2F4D8EE92502837726242CE1AB84EC1A(L_25, /*hidden argument*/NULL);
		if (L_26)
		{
			goto IL_000f;
		}
	}

IL_006b:
	{
		EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * L_27 = ___context0;
		StringBuilder_t * L_28 = V_0;
		VirtActionInvoker2< EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 *, StringBuilder_t * >::Invoke(7 /* System.Void ZXing.Datamatrix.Encoder.C40Encoder::handleEOD(ZXing.Datamatrix.Encoder.EncoderContext,System.Text.StringBuilder) */, __this, L_27, L_28);
		return;
	}
}
// System.Int32 ZXing.Datamatrix.Encoder.X12Encoder::encodeChar(System.Char,System.Text.StringBuilder)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t X12Encoder_encodeChar_m40D66B95BE9446537712801AF5A0C86D27213757 (X12Encoder_t86C3847B2F53AAAAA2A43173DFB2B910A5182DA8 * __this, Il2CppChar ___c0, StringBuilder_t * ___sb1, const RuntimeMethod* method)
{
	Il2CppChar V_0 = 0x0;
	{
		Il2CppChar L_0 = ___c0;
		V_0 = L_0;
		Il2CppChar L_1 = V_0;
		if ((((int32_t)L_1) > ((int32_t)((int32_t)32))))
		{
			goto IL_0013;
		}
	}
	{
		Il2CppChar L_2 = V_0;
		if ((((int32_t)L_2) == ((int32_t)((int32_t)13))))
		{
			goto IL_001f;
		}
	}
	{
		Il2CppChar L_3 = V_0;
		if ((((int32_t)L_3) == ((int32_t)((int32_t)32))))
		{
			goto IL_003d;
		}
	}
	{
		goto IL_0047;
	}

IL_0013:
	{
		Il2CppChar L_4 = V_0;
		if ((((int32_t)L_4) == ((int32_t)((int32_t)42))))
		{
			goto IL_0029;
		}
	}
	{
		Il2CppChar L_5 = V_0;
		if ((((int32_t)L_5) == ((int32_t)((int32_t)62))))
		{
			goto IL_0033;
		}
	}
	{
		goto IL_0047;
	}

IL_001f:
	{
		StringBuilder_t * L_6 = ___sb1;
		NullCheck(L_6);
		StringBuilder_t * L_7;
		L_7 = StringBuilder_Append_m1ADA3C16E40BF253BCDB5F9579B4DBA9C3E5B22E(L_6, 0, /*hidden argument*/NULL);
		goto IL_0082;
	}

IL_0029:
	{
		StringBuilder_t * L_8 = ___sb1;
		NullCheck(L_8);
		StringBuilder_t * L_9;
		L_9 = StringBuilder_Append_m1ADA3C16E40BF253BCDB5F9579B4DBA9C3E5B22E(L_8, 1, /*hidden argument*/NULL);
		goto IL_0082;
	}

IL_0033:
	{
		StringBuilder_t * L_10 = ___sb1;
		NullCheck(L_10);
		StringBuilder_t * L_11;
		L_11 = StringBuilder_Append_m1ADA3C16E40BF253BCDB5F9579B4DBA9C3E5B22E(L_10, 2, /*hidden argument*/NULL);
		goto IL_0082;
	}

IL_003d:
	{
		StringBuilder_t * L_12 = ___sb1;
		NullCheck(L_12);
		StringBuilder_t * L_13;
		L_13 = StringBuilder_Append_m1ADA3C16E40BF253BCDB5F9579B4DBA9C3E5B22E(L_12, 3, /*hidden argument*/NULL);
		goto IL_0082;
	}

IL_0047:
	{
		Il2CppChar L_14 = ___c0;
		if ((((int32_t)L_14) < ((int32_t)((int32_t)48))))
		{
			goto IL_0061;
		}
	}
	{
		Il2CppChar L_15 = ___c0;
		if ((((int32_t)L_15) > ((int32_t)((int32_t)57))))
		{
			goto IL_0061;
		}
	}
	{
		StringBuilder_t * L_16 = ___sb1;
		Il2CppChar L_17 = ___c0;
		NullCheck(L_16);
		StringBuilder_t * L_18;
		L_18 = StringBuilder_Append_m1ADA3C16E40BF253BCDB5F9579B4DBA9C3E5B22E(L_16, ((int32_t)((uint16_t)((int32_t)il2cpp_codegen_add((int32_t)((int32_t)il2cpp_codegen_subtract((int32_t)L_17, (int32_t)((int32_t)48))), (int32_t)4)))), /*hidden argument*/NULL);
		goto IL_0082;
	}

IL_0061:
	{
		Il2CppChar L_19 = ___c0;
		if ((((int32_t)L_19) < ((int32_t)((int32_t)65))))
		{
			goto IL_007c;
		}
	}
	{
		Il2CppChar L_20 = ___c0;
		if ((((int32_t)L_20) > ((int32_t)((int32_t)90))))
		{
			goto IL_007c;
		}
	}
	{
		StringBuilder_t * L_21 = ___sb1;
		Il2CppChar L_22 = ___c0;
		NullCheck(L_21);
		StringBuilder_t * L_23;
		L_23 = StringBuilder_Append_m1ADA3C16E40BF253BCDB5F9579B4DBA9C3E5B22E(L_21, ((int32_t)((uint16_t)((int32_t)il2cpp_codegen_add((int32_t)((int32_t)il2cpp_codegen_subtract((int32_t)L_22, (int32_t)((int32_t)65))), (int32_t)((int32_t)14))))), /*hidden argument*/NULL);
		goto IL_0082;
	}

IL_007c:
	{
		Il2CppChar L_24 = ___c0;
		HighLevelEncoder_illegalCharacter_m246DB50947B6A720135A793EA1D12E2318B816EE(L_24, /*hidden argument*/NULL);
	}

IL_0082:
	{
		return 1;
	}
}
// System.Void ZXing.Datamatrix.Encoder.X12Encoder::handleEOD(ZXing.Datamatrix.Encoder.EncoderContext,System.Text.StringBuilder)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void X12Encoder_handleEOD_mF027757C6C3F9CAE4C593FD4F45C3EA824D02795 (X12Encoder_t86C3847B2F53AAAAA2A43173DFB2B910A5182DA8 * __this, EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * ___context0, StringBuilder_t * ___buffer1, const RuntimeMethod* method)
{
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	{
		EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * L_0 = ___context0;
		NullCheck(L_0);
		EncoderContext_updateSymbolInfo_mD58B4A0EA2A8ED121E31293CF2464A8A01FB4C7F(L_0, /*hidden argument*/NULL);
		EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * L_1 = ___context0;
		NullCheck(L_1);
		SymbolInfo_t5655B64F5E51A9C1D956AEAF957811C751148C03 * L_2;
		L_2 = EncoderContext_get_SymbolInfo_m94C5BE03529789BE8897F207EAC85431F4D2A316_inline(L_1, /*hidden argument*/NULL);
		NullCheck(L_2);
		int32_t L_3 = L_2->get_dataCapacity_3();
		EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * L_4 = ___context0;
		NullCheck(L_4);
		int32_t L_5;
		L_5 = EncoderContext_get_CodewordCount_m86A2A4383E2C210DA7DAE7D1C180D1BC90238798(L_4, /*hidden argument*/NULL);
		V_0 = ((int32_t)il2cpp_codegen_subtract((int32_t)L_3, (int32_t)L_5));
		StringBuilder_t * L_6 = ___buffer1;
		NullCheck(L_6);
		int32_t L_7;
		L_7 = StringBuilder_get_Length_m680500263C59ACFD9582BF2AEEED8E92C87FF5C0(L_6, /*hidden argument*/NULL);
		V_1 = L_7;
		EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * L_8 = ___context0;
		EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * L_9 = L_8;
		NullCheck(L_9);
		int32_t L_10;
		L_10 = EncoderContext_get_Pos_mB6FC37697D743FBE341043898E7B659559A5DECE_inline(L_9, /*hidden argument*/NULL);
		int32_t L_11 = V_1;
		NullCheck(L_9);
		EncoderContext_set_Pos_m11E60F0C66BD176B5030FCEB7A51452F28DCD522_inline(L_9, ((int32_t)il2cpp_codegen_subtract((int32_t)L_10, (int32_t)L_11)), /*hidden argument*/NULL);
		EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * L_12 = ___context0;
		NullCheck(L_12);
		int32_t L_13;
		L_13 = EncoderContext_get_RemainingCharacters_m0D4877E225AB71AF61E4049F92FB38B4340A8FEB(L_12, /*hidden argument*/NULL);
		if ((((int32_t)L_13) > ((int32_t)1)))
		{
			goto IL_0044;
		}
	}
	{
		int32_t L_14 = V_0;
		if ((((int32_t)L_14) > ((int32_t)1)))
		{
			goto IL_0044;
		}
	}
	{
		EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * L_15 = ___context0;
		NullCheck(L_15);
		int32_t L_16;
		L_16 = EncoderContext_get_RemainingCharacters_m0D4877E225AB71AF61E4049F92FB38B4340A8FEB(L_15, /*hidden argument*/NULL);
		int32_t L_17 = V_0;
		if ((((int32_t)L_16) == ((int32_t)L_17)))
		{
			goto IL_004f;
		}
	}

IL_0044:
	{
		EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * L_18 = ___context0;
		NullCheck(L_18);
		EncoderContext_writeCodeword_m05A8D6AAF3A9918D18A2F7F9F3A36E4E911F38E8(L_18, ((int32_t)254), /*hidden argument*/NULL);
	}

IL_004f:
	{
		EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * L_19 = ___context0;
		NullCheck(L_19);
		int32_t L_20;
		L_20 = EncoderContext_get_NewEncoding_m7B6E49E8BCF0FC93642D2F58B3490B71F1E8AD57_inline(L_19, /*hidden argument*/NULL);
		if ((((int32_t)L_20) >= ((int32_t)0)))
		{
			goto IL_005f;
		}
	}
	{
		EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * L_21 = ___context0;
		NullCheck(L_21);
		EncoderContext_signalEncoderChange_mADF1DAD66E8381B308016EEB037F640723B8E35D_inline(L_21, 0, /*hidden argument*/NULL);
	}

IL_005f:
	{
		return;
	}
}
// System.Void ZXing.Datamatrix.Encoder.X12Encoder::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void X12Encoder__ctor_m3209B93C13C02E5B0064DF0EBAAF4A52904118C6 (X12Encoder_t86C3847B2F53AAAAA2A43173DFB2B910A5182DA8 * __this, const RuntimeMethod* method)
{
	{
		C40Encoder__ctor_m2BF4DEE8F84BF12EDB431C857C475D317A381EE9(__this, /*hidden argument*/NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void BigIntegerLibrary.Base10BigInteger/DigitContainer::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DigitContainer__ctor_m23D682353CA47F33FC605259DF26B0AFB35993E0 (DigitContainer_tFDA46C9477308140571F6CF1B540B4D067CADC3C * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC* L_0 = (Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC*)(Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC*)SZArrayNew(Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC_il2cpp_TypeInfo_var, (uint32_t)((int32_t)200));
		__this->set_digits_0(L_0);
		return;
	}
}
// System.Int64 BigIntegerLibrary.Base10BigInteger/DigitContainer::get_Item(System.Int32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int64_t DigitContainer_get_Item_m5685F599915CD0D680E53ADFF38D30DD99620D71 (DigitContainer_tFDA46C9477308140571F6CF1B540B4D067CADC3C * __this, int32_t ___index0, const RuntimeMethod* method)
{
	int32_t V_0 = 0;
	Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* V_1 = NULL;
	{
		int32_t L_0 = ___index0;
		V_0 = ((int32_t)((int32_t)L_0>>(int32_t)5));
		Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC* L_1 = __this->get_digits_0();
		int32_t L_2 = V_0;
		NullCheck(L_1);
		int32_t L_3 = L_2;
		Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* L_4 = (Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6*)(L_1)->GetAt(static_cast<il2cpp_array_size_t>(L_3));
		V_1 = L_4;
		Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* L_5 = V_1;
		if (!L_5)
		{
			goto IL_0017;
		}
	}
	{
		Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* L_6 = V_1;
		int32_t L_7 = ___index0;
		NullCheck(L_6);
		int32_t L_8 = ((int32_t)((int32_t)L_7%(int32_t)((int32_t)32)));
		int64_t L_9 = (L_6)->GetAt(static_cast<il2cpp_array_size_t>(L_8));
		return L_9;
	}

IL_0017:
	{
		return ((int64_t)((int64_t)0));
	}
}
// System.Void BigIntegerLibrary.Base10BigInteger/DigitContainer::set_Item(System.Int32,System.Int64)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DigitContainer_set_Item_m2D41AE85F3CEEBE7C0969C76ACF0F1F2153F899F (DigitContainer_tFDA46C9477308140571F6CF1B540B4D067CADC3C * __this, int32_t ___index0, int64_t ___value1, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* V_1 = NULL;
	Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* V_2 = NULL;
	Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* G_B2_0 = NULL;
	Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* G_B1_0 = NULL;
	{
		int32_t L_0 = ___index0;
		V_0 = ((int32_t)((int32_t)L_0>>(int32_t)5));
		Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC* L_1 = __this->get_digits_0();
		int32_t L_2 = V_0;
		NullCheck(L_1);
		int32_t L_3 = L_2;
		Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* L_4 = (Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6*)(L_1)->GetAt(static_cast<il2cpp_array_size_t>(L_3));
		Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* L_5 = L_4;
		G_B1_0 = L_5;
		if (L_5)
		{
			G_B2_0 = L_5;
			goto IL_0022;
		}
	}
	{
		Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC* L_6 = __this->get_digits_0();
		int32_t L_7 = V_0;
		Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* L_8 = (Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6*)(Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6*)SZArrayNew(Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6_il2cpp_TypeInfo_var, (uint32_t)((int32_t)32));
		Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* L_9 = L_8;
		V_2 = L_9;
		NullCheck(L_6);
		ArrayElementTypeCheck (L_6, L_9);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(L_7), (Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6*)L_9);
		Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* L_10 = V_2;
		G_B2_0 = L_10;
	}

IL_0022:
	{
		V_1 = G_B2_0;
		Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* L_11 = V_1;
		int32_t L_12 = ___index0;
		int64_t L_13 = ___value1;
		NullCheck(L_11);
		(L_11)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)((int32_t)L_12%(int32_t)((int32_t)32)))), (int64_t)L_13);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void BigIntegerLibrary.BigInteger/DigitContainer::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DigitContainer__ctor_mB6729FC739F1D8DE01C67483AB306596BCDCB527 (DigitContainer_t54CF59B9FAD2F0D27764576030066E76496BCF3A * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC* L_0 = (Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC*)(Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC*)SZArrayNew(Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC_il2cpp_TypeInfo_var, (uint32_t)((int32_t)80));
		__this->set_digits_0(L_0);
		return;
	}
}
// System.Int64 BigIntegerLibrary.BigInteger/DigitContainer::get_Item(System.Int32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int64_t DigitContainer_get_Item_m13CCE57F6FC21EFEC29182C89C0246460BD8C3A6 (DigitContainer_t54CF59B9FAD2F0D27764576030066E76496BCF3A * __this, int32_t ___index0, const RuntimeMethod* method)
{
	int32_t V_0 = 0;
	Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* V_1 = NULL;
	{
		int32_t L_0 = ___index0;
		V_0 = ((int32_t)((int32_t)L_0>>(int32_t)4));
		Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC* L_1 = __this->get_digits_0();
		int32_t L_2 = V_0;
		NullCheck(L_1);
		int32_t L_3 = L_2;
		Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* L_4 = (Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6*)(L_1)->GetAt(static_cast<il2cpp_array_size_t>(L_3));
		V_1 = L_4;
		Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* L_5 = V_1;
		if (!L_5)
		{
			goto IL_0017;
		}
	}
	{
		Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* L_6 = V_1;
		int32_t L_7 = ___index0;
		NullCheck(L_6);
		int32_t L_8 = ((int32_t)((int32_t)L_7%(int32_t)((int32_t)16)));
		int64_t L_9 = (L_6)->GetAt(static_cast<il2cpp_array_size_t>(L_8));
		return L_9;
	}

IL_0017:
	{
		return ((int64_t)((int64_t)0));
	}
}
// System.Void BigIntegerLibrary.BigInteger/DigitContainer::set_Item(System.Int32,System.Int64)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DigitContainer_set_Item_m8842DE3C1EF724D10D6BEBA617E57DFF8C492D65 (DigitContainer_t54CF59B9FAD2F0D27764576030066E76496BCF3A * __this, int32_t ___index0, int64_t ___value1, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* V_1 = NULL;
	Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* V_2 = NULL;
	Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* G_B2_0 = NULL;
	Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* G_B1_0 = NULL;
	{
		int32_t L_0 = ___index0;
		V_0 = ((int32_t)((int32_t)L_0>>(int32_t)4));
		Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC* L_1 = __this->get_digits_0();
		int32_t L_2 = V_0;
		NullCheck(L_1);
		int32_t L_3 = L_2;
		Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* L_4 = (Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6*)(L_1)->GetAt(static_cast<il2cpp_array_size_t>(L_3));
		Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* L_5 = L_4;
		G_B1_0 = L_5;
		if (L_5)
		{
			G_B2_0 = L_5;
			goto IL_0022;
		}
	}
	{
		Int64U5BU5DU5BU5D_t5237BA0F53E06948ADC63C3B2D68D7EEC8CBD2AC* L_6 = __this->get_digits_0();
		int32_t L_7 = V_0;
		Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* L_8 = (Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6*)(Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6*)SZArrayNew(Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6_il2cpp_TypeInfo_var, (uint32_t)((int32_t)16));
		Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* L_9 = L_8;
		V_2 = L_9;
		NullCheck(L_6);
		ArrayElementTypeCheck (L_6, L_9);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(L_7), (Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6*)L_9);
		Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* L_10 = V_2;
		G_B2_0 = L_10;
	}

IL_0022:
	{
		V_1 = G_B2_0;
		Int64U5BU5D_tCA61E42872C63A4286B24EEE6E0650143B43DCE6* L_11 = V_1;
		int32_t L_12 = ___index0;
		int64_t L_13 = ___value1;
		NullCheck(L_11);
		(L_11)->SetAt(static_cast<il2cpp_array_size_t>(((int32_t)((int32_t)L_12%(int32_t)((int32_t)16)))), (int64_t)L_13);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void ZXing.QrCode.Internal.Version/ECB::.ctor(System.Int32,System.Int32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ECB__ctor_mE5D63C7AE05AB1B6EC8237C68219F66AA1D05AFB (ECB_t189F63BF68B45D79DADDD123C64695D51E993AE0 * __this, int32_t ___count0, int32_t ___dataCodewords1, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		int32_t L_0 = ___count0;
		__this->set_count_0(L_0);
		int32_t L_1 = ___dataCodewords1;
		__this->set_dataCodewords_1(L_1);
		return;
	}
}
// System.Int32 ZXing.QrCode.Internal.Version/ECB::get_Count()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ECB_get_Count_mB561812ADB3C72573B7448A4C15F77A8D98FD0E2 (ECB_t189F63BF68B45D79DADDD123C64695D51E993AE0 * __this, const RuntimeMethod* method)
{
	{
		int32_t L_0 = __this->get_count_0();
		return L_0;
	}
}
// System.Int32 ZXing.QrCode.Internal.Version/ECB::get_DataCodewords()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ECB_get_DataCodewords_m21EDF743302FBB953BD5E30111197CAABF8FDE5B (ECB_t189F63BF68B45D79DADDD123C64695D51E993AE0 * __this, const RuntimeMethod* method)
{
	{
		int32_t L_0 = __this->get_dataCodewords_1();
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void ZXing.QrCode.Internal.Version/ECBlocks::.ctor(System.Int32,ZXing.QrCode.Internal.Version/ECB[])
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ECBlocks__ctor_m9D0FEA2A62AD87300386326630D71B2FE43D9011 (ECBlocks_tD16A91D6A2E9CF6323A3A1E4A7A1DEB501CA02F4 * __this, int32_t ___ecCodewordsPerBlock0, ECBU5BU5D_tF56F61F7AFE8A79E0DBCA8A4EFE62F6E9D40CEF6* ___ecBlocks1, const RuntimeMethod* method)
{
	{
		Object__ctor_m88880E0413421D13FD95325EDCE231707CE1F405(__this, /*hidden argument*/NULL);
		int32_t L_0 = ___ecCodewordsPerBlock0;
		__this->set_ecCodewordsPerBlock_0(L_0);
		ECBU5BU5D_tF56F61F7AFE8A79E0DBCA8A4EFE62F6E9D40CEF6* L_1 = ___ecBlocks1;
		__this->set_ecBlocks_1(L_1);
		return;
	}
}
// System.Int32 ZXing.QrCode.Internal.Version/ECBlocks::get_ECCodewordsPerBlock()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ECBlocks_get_ECCodewordsPerBlock_mF5F7E0CB687399B198D52DD6069845FF62BFFFA9 (ECBlocks_tD16A91D6A2E9CF6323A3A1E4A7A1DEB501CA02F4 * __this, const RuntimeMethod* method)
{
	{
		int32_t L_0 = __this->get_ecCodewordsPerBlock_0();
		return L_0;
	}
}
// System.Int32 ZXing.QrCode.Internal.Version/ECBlocks::get_NumBlocks()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ECBlocks_get_NumBlocks_mAB97A37082ECA5AF5428F29A61A02873DCC4310A (ECBlocks_tD16A91D6A2E9CF6323A3A1E4A7A1DEB501CA02F4 * __this, const RuntimeMethod* method)
{
	int32_t V_0 = 0;
	ECB_t189F63BF68B45D79DADDD123C64695D51E993AE0 * V_1 = NULL;
	ECBU5BU5D_tF56F61F7AFE8A79E0DBCA8A4EFE62F6E9D40CEF6* V_2 = NULL;
	int32_t V_3 = 0;
	{
		V_0 = 0;
		ECBU5BU5D_tF56F61F7AFE8A79E0DBCA8A4EFE62F6E9D40CEF6* L_0 = __this->get_ecBlocks_1();
		V_2 = L_0;
		V_3 = 0;
		goto IL_001e;
	}

IL_000d:
	{
		ECBU5BU5D_tF56F61F7AFE8A79E0DBCA8A4EFE62F6E9D40CEF6* L_1 = V_2;
		int32_t L_2 = V_3;
		NullCheck(L_1);
		int32_t L_3 = L_2;
		ECB_t189F63BF68B45D79DADDD123C64695D51E993AE0 * L_4 = (L_1)->GetAt(static_cast<il2cpp_array_size_t>(L_3));
		V_1 = L_4;
		int32_t L_5 = V_0;
		ECB_t189F63BF68B45D79DADDD123C64695D51E993AE0 * L_6 = V_1;
		NullCheck(L_6);
		int32_t L_7;
		L_7 = ECB_get_Count_mB561812ADB3C72573B7448A4C15F77A8D98FD0E2_inline(L_6, /*hidden argument*/NULL);
		V_0 = ((int32_t)il2cpp_codegen_add((int32_t)L_5, (int32_t)L_7));
		int32_t L_8 = V_3;
		V_3 = ((int32_t)il2cpp_codegen_add((int32_t)L_8, (int32_t)1));
	}

IL_001e:
	{
		int32_t L_9 = V_3;
		ECBU5BU5D_tF56F61F7AFE8A79E0DBCA8A4EFE62F6E9D40CEF6* L_10 = V_2;
		NullCheck(L_10);
		if ((((int32_t)L_9) < ((int32_t)((int32_t)((int32_t)(((RuntimeArray*)L_10)->max_length))))))
		{
			goto IL_000d;
		}
	}
	{
		int32_t L_11 = V_0;
		return L_11;
	}
}
// System.Int32 ZXing.QrCode.Internal.Version/ECBlocks::get_TotalECCodewords()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ECBlocks_get_TotalECCodewords_m93CAC7D317E46A5596F928124939ADA688134E4B (ECBlocks_tD16A91D6A2E9CF6323A3A1E4A7A1DEB501CA02F4 * __this, const RuntimeMethod* method)
{
	{
		int32_t L_0 = __this->get_ecCodewordsPerBlock_0();
		int32_t L_1;
		L_1 = ECBlocks_get_NumBlocks_mAB97A37082ECA5AF5428F29A61A02873DCC4310A(__this, /*hidden argument*/NULL);
		return ((int32_t)il2cpp_codegen_multiply((int32_t)L_0, (int32_t)L_1));
	}
}
// ZXing.QrCode.Internal.Version/ECB[] ZXing.QrCode.Internal.Version/ECBlocks::getECBlocks()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ECBU5BU5D_tF56F61F7AFE8A79E0DBCA8A4EFE62F6E9D40CEF6* ECBlocks_getECBlocks_m289C8698C02897868B44F8F02AD0A09713474112 (ECBlocks_tD16A91D6A2E9CF6323A3A1E4A7A1DEB501CA02F4 * __this, const RuntimeMethod* method)
{
	{
		ECBU5BU5D_tF56F61F7AFE8A79E0DBCA8A4EFE62F6E9D40CEF6* L_0 = __this->get_ecBlocks_1();
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t EncoderContext_get_Pos_mB6FC37697D743FBE341043898E7B659559A5DECE_inline (EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * __this, const RuntimeMethod* method)
{
	{
		int32_t L_0 = __this->get_pos_5();
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void EncoderContext_set_Pos_m11E60F0C66BD176B5030FCEB7A51452F28DCD522_inline (EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * __this, int32_t ___value0, const RuntimeMethod* method)
{
	{
		int32_t L_0 = ___value0;
		__this->set_pos_5(L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* EncoderContext_get_Message_m0CF5394F9C84697DBAD2DC5722C8EADC38E2074F_inline (EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * __this, const RuntimeMethod* method)
{
	{
		String_t* L_0 = __this->get_msg_0();
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void EncoderContext_signalEncoderChange_mADF1DAD66E8381B308016EEB037F640723B8E35D_inline (EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * __this, int32_t ___encoding0, const RuntimeMethod* method)
{
	{
		int32_t L_0 = ___encoding0;
		__this->set_newEncoding_6(L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR SymbolInfo_t5655B64F5E51A9C1D956AEAF957811C751148C03 * EncoderContext_get_SymbolInfo_m94C5BE03529789BE8897F207EAC85431F4D2A316_inline (EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * __this, const RuntimeMethod* method)
{
	{
		SymbolInfo_t5655B64F5E51A9C1D956AEAF957811C751148C03 * L_0 = __this->get_symbolInfo_7();
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t EncoderContext_get_NewEncoding_m7B6E49E8BCF0FC93642D2F58B3490B71F1E8AD57_inline (EncoderContext_t1EDAF9C583CE5F60E4C56CCB825B41C5A92D2173 * __this, const RuntimeMethod* method)
{
	{
		int32_t L_0 = __this->get_newEncoding_6();
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t ECB_get_Count_mB561812ADB3C72573B7448A4C15F77A8D98FD0E2_inline (ECB_t189F63BF68B45D79DADDD123C64695D51E993AE0 * __this, const RuntimeMethod* method)
{
	{
		int32_t L_0 = __this->get_count_0();
		return L_0;
	}
}

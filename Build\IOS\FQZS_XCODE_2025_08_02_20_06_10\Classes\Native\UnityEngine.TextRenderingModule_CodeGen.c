﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Boolean UnityEngine.TextGenerationSettings::CompareColors(UnityEngine.Color,UnityEngine.Color)
extern void TextGenerationSettings_CompareColors_m2D96C913B3B3004B54E2DF50B1D080E2756D6F83 (void);
// 0x00000002 System.Boolean UnityEngine.TextGenerationSettings::CompareVector2(UnityEngine.Vector2,UnityEngine.Vector2)
extern void TextGenerationSettings_CompareVector2_mE4B7BA9402E9183E2F373151C64BEDF29D3F9530 (void);
// 0x00000003 System.Boolean UnityEngine.TextGenerationSettings::Equals(UnityEngine.TextGenerationSettings)
extern void TextGenerationSettings_Equals_m39EB4E712D3C4A3DEE0A3A09EB2CD80EED5B6E70 (void);
// 0x00000004 System.Void UnityEngine.TextGenerator::.ctor()
extern void TextGenerator__ctor_m2018893FBFC055D3BBB11F0BEF120799E670E90D (void);
// 0x00000005 System.Void UnityEngine.TextGenerator::.ctor(System.Int32)
extern void TextGenerator__ctor_m1476375B22A72960883563CFB9590528F2439EE0 (void);
// 0x00000006 System.Void UnityEngine.TextGenerator::Finalize()
extern void TextGenerator_Finalize_m75F6D3C34EDF6AD36EFAC32F70C24361605E2D0A (void);
// 0x00000007 System.Void UnityEngine.TextGenerator::System.IDisposable.Dispose()
extern void TextGenerator_System_IDisposable_Dispose_m068B4B5E7B8E1BDAE6C849561DE80BEE6440814C (void);
// 0x00000008 System.Int32 UnityEngine.TextGenerator::get_characterCountVisible()
extern void TextGenerator_get_characterCountVisible_mA4D21FA8F79BCED441624DF5655B7DF079881076 (void);
// 0x00000009 UnityEngine.TextGenerationSettings UnityEngine.TextGenerator::ValidatedSettings(UnityEngine.TextGenerationSettings)
extern void TextGenerator_ValidatedSettings_mA1F21390BC9F8A8C257E43B9A3E8D1BF3C7B5EFB (void);
// 0x0000000A System.Void UnityEngine.TextGenerator::Invalidate()
extern void TextGenerator_Invalidate_m5A27D34A969A8607A2115999DE68530949DAB591 (void);
// 0x0000000B System.Void UnityEngine.TextGenerator::GetCharacters(System.Collections.Generic.List`1<UnityEngine.UICharInfo>)
extern void TextGenerator_GetCharacters_m4A831783606F122779F0C63A5B97FAB196DDC09A (void);
// 0x0000000C System.Void UnityEngine.TextGenerator::GetLines(System.Collections.Generic.List`1<UnityEngine.UILineInfo>)
extern void TextGenerator_GetLines_m2CC35881EE195C8F8F0575ACBF4A91612274A017 (void);
// 0x0000000D System.Void UnityEngine.TextGenerator::GetVertices(System.Collections.Generic.List`1<UnityEngine.UIVertex>)
extern void TextGenerator_GetVertices_mC834A1A97D58828B41CEF4ED34C6DD6F6AF331CA (void);
// 0x0000000E System.Single UnityEngine.TextGenerator::GetPreferredWidth(System.String,UnityEngine.TextGenerationSettings)
extern void TextGenerator_GetPreferredWidth_mF951E0E3DDE4CD9688C698AB81CE96699DE53206 (void);
// 0x0000000F System.Single UnityEngine.TextGenerator::GetPreferredHeight(System.String,UnityEngine.TextGenerationSettings)
extern void TextGenerator_GetPreferredHeight_mE685E293F9A571A49FDCCD3D7B45F8D732F5E195 (void);
// 0x00000010 System.Boolean UnityEngine.TextGenerator::PopulateWithErrors(System.String,UnityEngine.TextGenerationSettings,UnityEngine.GameObject)
extern void TextGenerator_PopulateWithErrors_mE5FA5DB6EBB1EBA92C3A09DC213EB8607396F265 (void);
// 0x00000011 System.Boolean UnityEngine.TextGenerator::Populate(System.String,UnityEngine.TextGenerationSettings)
extern void TextGenerator_Populate_m90DBFEA8769DCDD3F62949A677230E041358FD26 (void);
// 0x00000012 UnityEngine.TextGenerationError UnityEngine.TextGenerator::PopulateWithError(System.String,UnityEngine.TextGenerationSettings)
extern void TextGenerator_PopulateWithError_mCE3D60B71CB4B77197EDE7E1EC11831CBDD5C2C5 (void);
// 0x00000013 UnityEngine.TextGenerationError UnityEngine.TextGenerator::PopulateAlways(System.String,UnityEngine.TextGenerationSettings)
extern void TextGenerator_PopulateAlways_mA85E7B11C3267E35D10842423DA7874CBF5A77CF (void);
// 0x00000014 System.Collections.Generic.IList`1<UnityEngine.UIVertex> UnityEngine.TextGenerator::get_verts()
extern void TextGenerator_get_verts_m24E5F72EF4BB465321EA39A7B87285B48B423131 (void);
// 0x00000015 System.Collections.Generic.IList`1<UnityEngine.UICharInfo> UnityEngine.TextGenerator::get_characters()
extern void TextGenerator_get_characters_m129C7FB7C6CEDAD42FA3D7BE3052FC74D9D0A167 (void);
// 0x00000016 System.Collections.Generic.IList`1<UnityEngine.UILineInfo> UnityEngine.TextGenerator::get_lines()
extern void TextGenerator_get_lines_m3277C5BEA1392607B39B8CA03E902CE5AD7CEFC1 (void);
// 0x00000017 UnityEngine.Rect UnityEngine.TextGenerator::get_rectExtents()
extern void TextGenerator_get_rectExtents_m2573486A6322644F0EA3FB2AE05C5D5C52BC285B (void);
// 0x00000018 System.Int32 UnityEngine.TextGenerator::get_characterCount()
extern void TextGenerator_get_characterCount_mD4396E0258CB4E23B8ADA5F869504A69CA34580B (void);
// 0x00000019 System.Int32 UnityEngine.TextGenerator::get_lineCount()
extern void TextGenerator_get_lineCount_m20F5297212E59393F574E277D9A5E5843E72FD6A (void);
// 0x0000001A System.IntPtr UnityEngine.TextGenerator::Internal_Create()
extern void TextGenerator_Internal_Create_mDFFB825EDD2B5F9A1CF144B045506986A0AFDB1D (void);
// 0x0000001B System.Void UnityEngine.TextGenerator::Internal_Destroy(System.IntPtr)
extern void TextGenerator_Internal_Destroy_m734F82102AFE9CF0C016FE4BCDCECE068E825C94 (void);
// 0x0000001C System.Boolean UnityEngine.TextGenerator::Populate_Internal(System.String,UnityEngine.Font,UnityEngine.Color,System.Int32,System.Single,System.Single,UnityEngine.FontStyle,System.Boolean,System.Boolean,System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean,UnityEngine.TextAnchor,System.Single,System.Single,System.Single,System.Single,System.Boolean,System.Boolean,System.UInt32&)
extern void TextGenerator_Populate_Internal_m4DFCDDEC2D327C33EFEBD4FC71264A91A10D1F37 (void);
// 0x0000001D System.Boolean UnityEngine.TextGenerator::Populate_Internal(System.String,UnityEngine.Font,UnityEngine.Color,System.Int32,System.Single,System.Single,UnityEngine.FontStyle,System.Boolean,System.Boolean,System.Int32,System.Int32,UnityEngine.VerticalWrapMode,UnityEngine.HorizontalWrapMode,System.Boolean,UnityEngine.TextAnchor,UnityEngine.Vector2,UnityEngine.Vector2,System.Boolean,System.Boolean,UnityEngine.TextGenerationError&)
extern void TextGenerator_Populate_Internal_m1E27E45E04E4709DD18502776659E882E2252554 (void);
// 0x0000001E System.Void UnityEngine.TextGenerator::GetVerticesInternal(System.Object)
extern void TextGenerator_GetVerticesInternal_m1090ABB82FD178A8D32D6D37907E9D04B940E1D8 (void);
// 0x0000001F System.Void UnityEngine.TextGenerator::GetCharactersInternal(System.Object)
extern void TextGenerator_GetCharactersInternal_m1B1206C5358B2E7ABE274DE03C10371887DA7C1D (void);
// 0x00000020 System.Void UnityEngine.TextGenerator::GetLinesInternal(System.Object)
extern void TextGenerator_GetLinesInternal_mCC147D2DAC648B936C54A5662C5FE43844B4FFDE (void);
// 0x00000021 System.Void UnityEngine.TextGenerator::get_rectExtents_Injected(UnityEngine.Rect&)
extern void TextGenerator_get_rectExtents_Injected_m8D58D2455AC5997ADE354CFA9466782EB6B31AB6 (void);
// 0x00000022 System.Boolean UnityEngine.TextGenerator::Populate_Internal_Injected(System.String,UnityEngine.Font,UnityEngine.Color&,System.Int32,System.Single,System.Single,UnityEngine.FontStyle,System.Boolean,System.Boolean,System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean,UnityEngine.TextAnchor,System.Single,System.Single,System.Single,System.Single,System.Boolean,System.Boolean,System.UInt32&)
extern void TextGenerator_Populate_Internal_Injected_m521AC47E26C872BEDDC23AF447EEA2E45EE088F3 (void);
// 0x00000023 System.Void UnityEngine.TextMesh::set_text(System.String)
extern void TextMesh_set_text_m5879B13F5C9E4A1D05155839B89CCDB85BE28A04 (void);
// 0x00000024 System.Void UnityEngine.TextMesh::set_font(UnityEngine.Font)
extern void TextMesh_set_font_mF87EE065DC748F4B29F6FE3E809A6CD1EA3AB711 (void);
// 0x00000025 System.Int32 UnityEngine.CharacterInfo::get_advance()
extern void CharacterInfo_get_advance_m53238FF95DEE0C98AC35F9AE1472F0108E8DF559 (void);
// 0x00000026 System.Int32 UnityEngine.CharacterInfo::get_minY()
extern void CharacterInfo_get_minY_m330438FF72219ABA0B5944BDFAC2EA39485EAE9E (void);
// 0x00000027 System.Int32 UnityEngine.CharacterInfo::get_maxY()
extern void CharacterInfo_get_maxY_m614B293EA428D93231060B29BAD6D7EAD847BFA0 (void);
// 0x00000028 System.Int32 UnityEngine.CharacterInfo::get_minX()
extern void CharacterInfo_get_minX_m812AFDF63A73BD28E0390D68931EF9D94A2A0002 (void);
// 0x00000029 System.Int32 UnityEngine.CharacterInfo::get_maxX()
extern void CharacterInfo_get_maxX_m395F0004E7E3070D99954B9C4628512AEC621F01 (void);
// 0x0000002A UnityEngine.Vector2 UnityEngine.CharacterInfo::get_uvBottomLeftUnFlipped()
extern void CharacterInfo_get_uvBottomLeftUnFlipped_m48DBC48D5C4DE63756B010B162DBB91144A6A29C (void);
// 0x0000002B UnityEngine.Vector2 UnityEngine.CharacterInfo::get_uvBottomRightUnFlipped()
extern void CharacterInfo_get_uvBottomRightUnFlipped_mF7CEBF52D193A78D0D64DD9CC0571AF7F900C60F (void);
// 0x0000002C UnityEngine.Vector2 UnityEngine.CharacterInfo::get_uvTopRightUnFlipped()
extern void CharacterInfo_get_uvTopRightUnFlipped_mE76F7DAF2AB38A667A29B386288DEF706B3BE5CA (void);
// 0x0000002D UnityEngine.Vector2 UnityEngine.CharacterInfo::get_uvTopLeftUnFlipped()
extern void CharacterInfo_get_uvTopLeftUnFlipped_m010320E18DA0758E5F78261AA0FFBF2A2998DB1E (void);
// 0x0000002E UnityEngine.Vector2 UnityEngine.CharacterInfo::get_uvBottomLeft()
extern void CharacterInfo_get_uvBottomLeft_m47284B7C13A9B16E4F471D516ECF79A00CC4D36B (void);
// 0x0000002F UnityEngine.Vector2 UnityEngine.CharacterInfo::get_uvBottomRight()
extern void CharacterInfo_get_uvBottomRight_mBC6D4C1139A6F1C9E61B9D7A9CCA0E8DACAB4F04 (void);
// 0x00000030 UnityEngine.Vector2 UnityEngine.CharacterInfo::get_uvTopRight()
extern void CharacterInfo_get_uvTopRight_m6E55AC2EA576AB56915BDF8FFC7F118A7978C11B (void);
// 0x00000031 UnityEngine.Vector2 UnityEngine.CharacterInfo::get_uvTopLeft()
extern void CharacterInfo_get_uvTopLeft_mFFFF6BE355A02808B8483A62DF0667B3F50A07D3 (void);
// 0x00000032 System.Void UnityEngine.UIVertex::.cctor()
extern void UIVertex__cctor_mF942E5CDA7A6356E0E92D8D0363FF628E69A7822 (void);
// 0x00000033 System.Void UnityEngine.Font::add_textureRebuilt(System.Action`1<UnityEngine.Font>)
extern void Font_add_textureRebuilt_m5AA790DF583AFD9EC3F569D23DC6A6694F95F887 (void);
// 0x00000034 System.Void UnityEngine.Font::remove_textureRebuilt(System.Action`1<UnityEngine.Font>)
extern void Font_remove_textureRebuilt_m8E903D18B6DD7A43BDABC5DE852BF10A8B2F1FC2 (void);
// 0x00000035 UnityEngine.Material UnityEngine.Font::get_material()
extern void Font_get_material_m799A85F3FF161469D8AF8CC0CCA6D550A6491565 (void);
// 0x00000036 System.String[] UnityEngine.Font::get_fontNames()
extern void Font_get_fontNames_m40BC277B2E176F31D50AC91A9568E20B227C561B (void);
// 0x00000037 System.Boolean UnityEngine.Font::get_dynamic()
extern void Font_get_dynamic_m2CA1DFFB862B41EAE100830F654880CD668F23AD (void);
// 0x00000038 System.Int32 UnityEngine.Font::get_fontSize()
extern void Font_get_fontSize_m284493C6ABD87266D2DC3D32619D9972F6711261 (void);
// 0x00000039 System.Void UnityEngine.Font::.ctor()
extern void Font__ctor_m7E2532960BF01745ADB54BBCC43DBBE650A83E4C (void);
// 0x0000003A System.Void UnityEngine.Font::InvokeTextureRebuilt_Internal(UnityEngine.Font)
extern void Font_InvokeTextureRebuilt_Internal_mBA27EC1526245BFAB6C6F77DB125D19F5700FA2D (void);
// 0x0000003B System.Boolean UnityEngine.Font::HasCharacter(System.Char)
extern void Font_HasCharacter_m91E1618BC3221EBB83518E5B85D5CA3996FAD10C (void);
// 0x0000003C System.Boolean UnityEngine.Font::HasCharacter(System.Int32)
extern void Font_HasCharacter_mEC8A3BB085240EC6B44999C19588319DB644B2AC (void);
// 0x0000003D System.Void UnityEngine.Font::Internal_CreateFont(UnityEngine.Font,System.String)
extern void Font_Internal_CreateFont_mDD91EFD7C7CD8C5F437DF939FAAE9C8BBFB241BE (void);
// 0x0000003E System.Boolean UnityEngine.Font::GetCharacterInfo(System.Char,UnityEngine.CharacterInfo&,System.Int32,UnityEngine.FontStyle)
extern void Font_GetCharacterInfo_m24ED802426FFDB6BA12ED7A5D46AE32B95FB3160 (void);
// 0x0000003F System.Boolean UnityEngine.Font::GetCharacterInfo(System.Char,UnityEngine.CharacterInfo&,System.Int32)
extern void Font_GetCharacterInfo_mCAA0F6C1E05AC11D37515C810C2CA1571F480FEC (void);
// 0x00000040 System.Void UnityEngine.Font::RequestCharactersInTexture(System.String,System.Int32,UnityEngine.FontStyle)
extern void Font_RequestCharactersInTexture_mC997BCA7C84F89D53D4D8DF3BEC898E7B23BD2D7 (void);
// 0x00000041 System.Void UnityEngine.Font/FontTextureRebuildCallback::.ctor(System.Object,System.IntPtr)
extern void FontTextureRebuildCallback__ctor_m58D67535ED1CC9895AB016CBB713A730A73480E0 (void);
// 0x00000042 System.Void UnityEngine.Font/FontTextureRebuildCallback::Invoke()
extern void FontTextureRebuildCallback_Invoke_m7F5D9CAA51DC8C9779104ACF46F668654B35EA1F (void);
// 0x00000043 System.IAsyncResult UnityEngine.Font/FontTextureRebuildCallback::BeginInvoke(System.AsyncCallback,System.Object)
extern void FontTextureRebuildCallback_BeginInvoke_m3F36739573BA8EE2C6C3BF90DC37E9D78AD7365F (void);
// 0x00000044 System.Void UnityEngine.Font/FontTextureRebuildCallback::EndInvoke(System.IAsyncResult)
extern void FontTextureRebuildCallback_EndInvoke_m3FDA95B07122814A8E63A8B7FFB5BCA7EC157B2C (void);
static Il2CppMethodPointer s_methodPointers[68] = 
{
	TextGenerationSettings_CompareColors_m2D96C913B3B3004B54E2DF50B1D080E2756D6F83,
	TextGenerationSettings_CompareVector2_mE4B7BA9402E9183E2F373151C64BEDF29D3F9530,
	TextGenerationSettings_Equals_m39EB4E712D3C4A3DEE0A3A09EB2CD80EED5B6E70,
	TextGenerator__ctor_m2018893FBFC055D3BBB11F0BEF120799E670E90D,
	TextGenerator__ctor_m1476375B22A72960883563CFB9590528F2439EE0,
	TextGenerator_Finalize_m75F6D3C34EDF6AD36EFAC32F70C24361605E2D0A,
	TextGenerator_System_IDisposable_Dispose_m068B4B5E7B8E1BDAE6C849561DE80BEE6440814C,
	TextGenerator_get_characterCountVisible_mA4D21FA8F79BCED441624DF5655B7DF079881076,
	TextGenerator_ValidatedSettings_mA1F21390BC9F8A8C257E43B9A3E8D1BF3C7B5EFB,
	TextGenerator_Invalidate_m5A27D34A969A8607A2115999DE68530949DAB591,
	TextGenerator_GetCharacters_m4A831783606F122779F0C63A5B97FAB196DDC09A,
	TextGenerator_GetLines_m2CC35881EE195C8F8F0575ACBF4A91612274A017,
	TextGenerator_GetVertices_mC834A1A97D58828B41CEF4ED34C6DD6F6AF331CA,
	TextGenerator_GetPreferredWidth_mF951E0E3DDE4CD9688C698AB81CE96699DE53206,
	TextGenerator_GetPreferredHeight_mE685E293F9A571A49FDCCD3D7B45F8D732F5E195,
	TextGenerator_PopulateWithErrors_mE5FA5DB6EBB1EBA92C3A09DC213EB8607396F265,
	TextGenerator_Populate_m90DBFEA8769DCDD3F62949A677230E041358FD26,
	TextGenerator_PopulateWithError_mCE3D60B71CB4B77197EDE7E1EC11831CBDD5C2C5,
	TextGenerator_PopulateAlways_mA85E7B11C3267E35D10842423DA7874CBF5A77CF,
	TextGenerator_get_verts_m24E5F72EF4BB465321EA39A7B87285B48B423131,
	TextGenerator_get_characters_m129C7FB7C6CEDAD42FA3D7BE3052FC74D9D0A167,
	TextGenerator_get_lines_m3277C5BEA1392607B39B8CA03E902CE5AD7CEFC1,
	TextGenerator_get_rectExtents_m2573486A6322644F0EA3FB2AE05C5D5C52BC285B,
	TextGenerator_get_characterCount_mD4396E0258CB4E23B8ADA5F869504A69CA34580B,
	TextGenerator_get_lineCount_m20F5297212E59393F574E277D9A5E5843E72FD6A,
	TextGenerator_Internal_Create_mDFFB825EDD2B5F9A1CF144B045506986A0AFDB1D,
	TextGenerator_Internal_Destroy_m734F82102AFE9CF0C016FE4BCDCECE068E825C94,
	TextGenerator_Populate_Internal_m4DFCDDEC2D327C33EFEBD4FC71264A91A10D1F37,
	TextGenerator_Populate_Internal_m1E27E45E04E4709DD18502776659E882E2252554,
	TextGenerator_GetVerticesInternal_m1090ABB82FD178A8D32D6D37907E9D04B940E1D8,
	TextGenerator_GetCharactersInternal_m1B1206C5358B2E7ABE274DE03C10371887DA7C1D,
	TextGenerator_GetLinesInternal_mCC147D2DAC648B936C54A5662C5FE43844B4FFDE,
	TextGenerator_get_rectExtents_Injected_m8D58D2455AC5997ADE354CFA9466782EB6B31AB6,
	TextGenerator_Populate_Internal_Injected_m521AC47E26C872BEDDC23AF447EEA2E45EE088F3,
	TextMesh_set_text_m5879B13F5C9E4A1D05155839B89CCDB85BE28A04,
	TextMesh_set_font_mF87EE065DC748F4B29F6FE3E809A6CD1EA3AB711,
	CharacterInfo_get_advance_m53238FF95DEE0C98AC35F9AE1472F0108E8DF559,
	CharacterInfo_get_minY_m330438FF72219ABA0B5944BDFAC2EA39485EAE9E,
	CharacterInfo_get_maxY_m614B293EA428D93231060B29BAD6D7EAD847BFA0,
	CharacterInfo_get_minX_m812AFDF63A73BD28E0390D68931EF9D94A2A0002,
	CharacterInfo_get_maxX_m395F0004E7E3070D99954B9C4628512AEC621F01,
	CharacterInfo_get_uvBottomLeftUnFlipped_m48DBC48D5C4DE63756B010B162DBB91144A6A29C,
	CharacterInfo_get_uvBottomRightUnFlipped_mF7CEBF52D193A78D0D64DD9CC0571AF7F900C60F,
	CharacterInfo_get_uvTopRightUnFlipped_mE76F7DAF2AB38A667A29B386288DEF706B3BE5CA,
	CharacterInfo_get_uvTopLeftUnFlipped_m010320E18DA0758E5F78261AA0FFBF2A2998DB1E,
	CharacterInfo_get_uvBottomLeft_m47284B7C13A9B16E4F471D516ECF79A00CC4D36B,
	CharacterInfo_get_uvBottomRight_mBC6D4C1139A6F1C9E61B9D7A9CCA0E8DACAB4F04,
	CharacterInfo_get_uvTopRight_m6E55AC2EA576AB56915BDF8FFC7F118A7978C11B,
	CharacterInfo_get_uvTopLeft_mFFFF6BE355A02808B8483A62DF0667B3F50A07D3,
	UIVertex__cctor_mF942E5CDA7A6356E0E92D8D0363FF628E69A7822,
	Font_add_textureRebuilt_m5AA790DF583AFD9EC3F569D23DC6A6694F95F887,
	Font_remove_textureRebuilt_m8E903D18B6DD7A43BDABC5DE852BF10A8B2F1FC2,
	Font_get_material_m799A85F3FF161469D8AF8CC0CCA6D550A6491565,
	Font_get_fontNames_m40BC277B2E176F31D50AC91A9568E20B227C561B,
	Font_get_dynamic_m2CA1DFFB862B41EAE100830F654880CD668F23AD,
	Font_get_fontSize_m284493C6ABD87266D2DC3D32619D9972F6711261,
	Font__ctor_m7E2532960BF01745ADB54BBCC43DBBE650A83E4C,
	Font_InvokeTextureRebuilt_Internal_mBA27EC1526245BFAB6C6F77DB125D19F5700FA2D,
	Font_HasCharacter_m91E1618BC3221EBB83518E5B85D5CA3996FAD10C,
	Font_HasCharacter_mEC8A3BB085240EC6B44999C19588319DB644B2AC,
	Font_Internal_CreateFont_mDD91EFD7C7CD8C5F437DF939FAAE9C8BBFB241BE,
	Font_GetCharacterInfo_m24ED802426FFDB6BA12ED7A5D46AE32B95FB3160,
	Font_GetCharacterInfo_mCAA0F6C1E05AC11D37515C810C2CA1571F480FEC,
	Font_RequestCharactersInTexture_mC997BCA7C84F89D53D4D8DF3BEC898E7B23BD2D7,
	FontTextureRebuildCallback__ctor_m58D67535ED1CC9895AB016CBB713A730A73480E0,
	FontTextureRebuildCallback_Invoke_m7F5D9CAA51DC8C9779104ACF46F668654B35EA1F,
	FontTextureRebuildCallback_BeginInvoke_m3F36739573BA8EE2C6C3BF90DC37E9D78AD7365F,
	FontTextureRebuildCallback_EndInvoke_m3FDA95B07122814A8E63A8B7FFB5BCA7EC157B2C,
};
extern void TextGenerationSettings_CompareColors_m2D96C913B3B3004B54E2DF50B1D080E2756D6F83_AdjustorThunk (void);
extern void TextGenerationSettings_CompareVector2_mE4B7BA9402E9183E2F373151C64BEDF29D3F9530_AdjustorThunk (void);
extern void TextGenerationSettings_Equals_m39EB4E712D3C4A3DEE0A3A09EB2CD80EED5B6E70_AdjustorThunk (void);
extern void CharacterInfo_get_advance_m53238FF95DEE0C98AC35F9AE1472F0108E8DF559_AdjustorThunk (void);
extern void CharacterInfo_get_minY_m330438FF72219ABA0B5944BDFAC2EA39485EAE9E_AdjustorThunk (void);
extern void CharacterInfo_get_maxY_m614B293EA428D93231060B29BAD6D7EAD847BFA0_AdjustorThunk (void);
extern void CharacterInfo_get_minX_m812AFDF63A73BD28E0390D68931EF9D94A2A0002_AdjustorThunk (void);
extern void CharacterInfo_get_maxX_m395F0004E7E3070D99954B9C4628512AEC621F01_AdjustorThunk (void);
extern void CharacterInfo_get_uvBottomLeftUnFlipped_m48DBC48D5C4DE63756B010B162DBB91144A6A29C_AdjustorThunk (void);
extern void CharacterInfo_get_uvBottomRightUnFlipped_mF7CEBF52D193A78D0D64DD9CC0571AF7F900C60F_AdjustorThunk (void);
extern void CharacterInfo_get_uvTopRightUnFlipped_mE76F7DAF2AB38A667A29B386288DEF706B3BE5CA_AdjustorThunk (void);
extern void CharacterInfo_get_uvTopLeftUnFlipped_m010320E18DA0758E5F78261AA0FFBF2A2998DB1E_AdjustorThunk (void);
extern void CharacterInfo_get_uvBottomLeft_m47284B7C13A9B16E4F471D516ECF79A00CC4D36B_AdjustorThunk (void);
extern void CharacterInfo_get_uvBottomRight_mBC6D4C1139A6F1C9E61B9D7A9CCA0E8DACAB4F04_AdjustorThunk (void);
extern void CharacterInfo_get_uvTopRight_m6E55AC2EA576AB56915BDF8FFC7F118A7978C11B_AdjustorThunk (void);
extern void CharacterInfo_get_uvTopLeft_mFFFF6BE355A02808B8483A62DF0667B3F50A07D3_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[16] = 
{
	{ 0x06000001, TextGenerationSettings_CompareColors_m2D96C913B3B3004B54E2DF50B1D080E2756D6F83_AdjustorThunk },
	{ 0x06000002, TextGenerationSettings_CompareVector2_mE4B7BA9402E9183E2F373151C64BEDF29D3F9530_AdjustorThunk },
	{ 0x06000003, TextGenerationSettings_Equals_m39EB4E712D3C4A3DEE0A3A09EB2CD80EED5B6E70_AdjustorThunk },
	{ 0x06000025, CharacterInfo_get_advance_m53238FF95DEE0C98AC35F9AE1472F0108E8DF559_AdjustorThunk },
	{ 0x06000026, CharacterInfo_get_minY_m330438FF72219ABA0B5944BDFAC2EA39485EAE9E_AdjustorThunk },
	{ 0x06000027, CharacterInfo_get_maxY_m614B293EA428D93231060B29BAD6D7EAD847BFA0_AdjustorThunk },
	{ 0x06000028, CharacterInfo_get_minX_m812AFDF63A73BD28E0390D68931EF9D94A2A0002_AdjustorThunk },
	{ 0x06000029, CharacterInfo_get_maxX_m395F0004E7E3070D99954B9C4628512AEC621F01_AdjustorThunk },
	{ 0x0600002A, CharacterInfo_get_uvBottomLeftUnFlipped_m48DBC48D5C4DE63756B010B162DBB91144A6A29C_AdjustorThunk },
	{ 0x0600002B, CharacterInfo_get_uvBottomRightUnFlipped_mF7CEBF52D193A78D0D64DD9CC0571AF7F900C60F_AdjustorThunk },
	{ 0x0600002C, CharacterInfo_get_uvTopRightUnFlipped_mE76F7DAF2AB38A667A29B386288DEF706B3BE5CA_AdjustorThunk },
	{ 0x0600002D, CharacterInfo_get_uvTopLeftUnFlipped_m010320E18DA0758E5F78261AA0FFBF2A2998DB1E_AdjustorThunk },
	{ 0x0600002E, CharacterInfo_get_uvBottomLeft_m47284B7C13A9B16E4F471D516ECF79A00CC4D36B_AdjustorThunk },
	{ 0x0600002F, CharacterInfo_get_uvBottomRight_mBC6D4C1139A6F1C9E61B9D7A9CCA0E8DACAB4F04_AdjustorThunk },
	{ 0x06000030, CharacterInfo_get_uvTopRight_m6E55AC2EA576AB56915BDF8FFC7F118A7978C11B_AdjustorThunk },
	{ 0x06000031, CharacterInfo_get_uvTopLeft_mFFFF6BE355A02808B8483A62DF0667B3F50A07D3_AdjustorThunk },
};
static const int32_t s_InvokerIndices[68] = 
{
	1434,
	1513,
	2628,
	3967,
	3313,
	3967,
	3967,
	3893,
	3125,
	3967,
	3348,
	3348,
	3348,
	1775,
	1775,
	988,
	1477,
	1608,
	1608,
	3916,
	3916,
	3916,
	3938,
	3893,
	3893,
	6265,
	6215,
	1,
	2,
	3348,
	3348,
	3348,
	3255,
	0,
	3348,
	3348,
	3893,
	3893,
	3893,
	3893,
	3893,
	3963,
	3963,
	3963,
	3963,
	3963,
	3963,
	3963,
	3963,
	6289,
	6218,
	6218,
	3916,
	3916,
	3863,
	3893,
	3967,
	6218,
	2635,
	2570,
	5834,
	470,
	996,
	1324,
	2169,
	3967,
	1717,
	3348,
};
extern const CustomAttributesCacheGenerator g_UnityEngine_TextRenderingModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_TextRenderingModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_TextRenderingModule_CodeGenModule = 
{
	"UnityEngine.TextRenderingModule.dll",
	68,
	s_methodPointers,
	16,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_UnityEngine_TextRenderingModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

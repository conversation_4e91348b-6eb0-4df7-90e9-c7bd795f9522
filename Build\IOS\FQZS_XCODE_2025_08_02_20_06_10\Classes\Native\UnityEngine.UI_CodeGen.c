﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.String UnityEngine.UI.AnimationTriggers::get_normalTrigger()
extern void AnimationTriggers_get_normalTrigger_m32A0AABB530B9DB501EEEAD4CD2B464A8D3AD44A (void);
// 0x00000002 System.Void UnityEngine.UI.AnimationTriggers::set_normalTrigger(System.String)
extern void AnimationTriggers_set_normalTrigger_mC80FD5EF3A5E3C1DDBFB8D4E0ED537804D628989 (void);
// 0x00000003 System.String UnityEngine.UI.AnimationTriggers::get_highlightedTrigger()
extern void AnimationTriggers_get_highlightedTrigger_m3EBBC2E67EAAB6F886A6405114FF1C00880938FD (void);
// 0x00000004 System.Void UnityEngine.UI.AnimationTriggers::set_highlightedTrigger(System.String)
extern void AnimationTriggers_set_highlightedTrigger_m2B28CFEFFCDF7576FB796B00F1C218A1AB2D874E (void);
// 0x00000005 System.String UnityEngine.UI.AnimationTriggers::get_pressedTrigger()
extern void AnimationTriggers_get_pressedTrigger_mEA76FE9A10B78E152D6392FF5311F9C92E3E15EC (void);
// 0x00000006 System.Void UnityEngine.UI.AnimationTriggers::set_pressedTrigger(System.String)
extern void AnimationTriggers_set_pressedTrigger_m80C1E8257889A25700B942282014DDB99F1B1156 (void);
// 0x00000007 System.String UnityEngine.UI.AnimationTriggers::get_selectedTrigger()
extern void AnimationTriggers_get_selectedTrigger_m9CD5D37F9D5883819546DEB24959B2DD408C2465 (void);
// 0x00000008 System.Void UnityEngine.UI.AnimationTriggers::set_selectedTrigger(System.String)
extern void AnimationTriggers_set_selectedTrigger_m74C434D7BD62A7502646B7A1C9EE1FF28618CB70 (void);
// 0x00000009 System.String UnityEngine.UI.AnimationTriggers::get_disabledTrigger()
extern void AnimationTriggers_get_disabledTrigger_mA66CB9FDC15B8154F4AB4C71384E8E725C9FF4F8 (void);
// 0x0000000A System.Void UnityEngine.UI.AnimationTriggers::set_disabledTrigger(System.String)
extern void AnimationTriggers_set_disabledTrigger_m7B29FA0B3C3E9E30BE8CD2689337D4853C789DB4 (void);
// 0x0000000B System.Void UnityEngine.UI.AnimationTriggers::.ctor()
extern void AnimationTriggers__ctor_m73EF30313E61AD0FB9E687F270ECAEB30CF9DB89 (void);
// 0x0000000C System.Void UnityEngine.UI.Button::.ctor()
extern void Button__ctor_m49611E4C5C9CCDACC1A95EACA4673A88DF1638CE (void);
// 0x0000000D UnityEngine.UI.Button/ButtonClickedEvent UnityEngine.UI.Button::get_onClick()
extern void Button_get_onClick_m28BD8C670676D4E2B292B5A7F59387D4BF61F8F4 (void);
// 0x0000000E System.Void UnityEngine.UI.Button::set_onClick(UnityEngine.UI.Button/ButtonClickedEvent)
extern void Button_set_onClick_m896AFE34EFF24C941B39AC00E07EEDE470D1D113 (void);
// 0x0000000F System.Void UnityEngine.UI.Button::Press()
extern void Button_Press_m24D98A1967E4A82133B5B730EE966513CCFAC124 (void);
// 0x00000010 System.Void UnityEngine.UI.Button::OnPointerClick(UnityEngine.EventSystems.PointerEventData)
extern void Button_OnPointerClick_m0BB4BB85529C02F25F85D2D1BC82210608767B0E (void);
// 0x00000011 System.Void UnityEngine.UI.Button::OnSubmit(UnityEngine.EventSystems.BaseEventData)
extern void Button_OnSubmit_m25CCF45D13DF76A7F294FBCA4C4D6F89BA90F416 (void);
// 0x00000012 System.Collections.IEnumerator UnityEngine.UI.Button::OnFinishSubmit()
extern void Button_OnFinishSubmit_mA6C59616C7BFBFED1887E6C8D1F46D4A0C8C9108 (void);
// 0x00000013 System.Void UnityEngine.UI.Button/ButtonClickedEvent::.ctor()
extern void ButtonClickedEvent__ctor_m83123C7524AB4DC1FFB95F86455F859572F1B6A8 (void);
// 0x00000014 System.Void UnityEngine.UI.Button/<OnFinishSubmit>d__9::.ctor(System.Int32)
extern void U3COnFinishSubmitU3Ed__9__ctor_m4663BB761D062DC802EABBD3FDBD8FDFA03551EC (void);
// 0x00000015 System.Void UnityEngine.UI.Button/<OnFinishSubmit>d__9::System.IDisposable.Dispose()
extern void U3COnFinishSubmitU3Ed__9_System_IDisposable_Dispose_m1EB4BF5C53168DA8C48EFAC7FFE209B4E4090FB6 (void);
// 0x00000016 System.Boolean UnityEngine.UI.Button/<OnFinishSubmit>d__9::MoveNext()
extern void U3COnFinishSubmitU3Ed__9_MoveNext_m23D48ECBECED3420D3B57B062BA5D612B44907B2 (void);
// 0x00000017 System.Object UnityEngine.UI.Button/<OnFinishSubmit>d__9::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3COnFinishSubmitU3Ed__9_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mCE5F932F82D5E0662EB7EC1F04EA53AA6E9E3A62 (void);
// 0x00000018 System.Void UnityEngine.UI.Button/<OnFinishSubmit>d__9::System.Collections.IEnumerator.Reset()
extern void U3COnFinishSubmitU3Ed__9_System_Collections_IEnumerator_Reset_m8C34DF18B3EE52EC98CAECD371811E60CCA7BC1D (void);
// 0x00000019 System.Object UnityEngine.UI.Button/<OnFinishSubmit>d__9::System.Collections.IEnumerator.get_Current()
extern void U3COnFinishSubmitU3Ed__9_System_Collections_IEnumerator_get_Current_m1336764E1957C2B5EBE654AB0C8834996F183534 (void);
// 0x0000001A System.Void UnityEngine.UI.ICanvasElement::Rebuild(UnityEngine.UI.CanvasUpdate)
// 0x0000001B UnityEngine.Transform UnityEngine.UI.ICanvasElement::get_transform()
// 0x0000001C System.Void UnityEngine.UI.ICanvasElement::LayoutComplete()
// 0x0000001D System.Void UnityEngine.UI.ICanvasElement::GraphicUpdateComplete()
// 0x0000001E System.Boolean UnityEngine.UI.ICanvasElement::IsDestroyed()
// 0x0000001F System.Void UnityEngine.UI.CanvasUpdateRegistry::.ctor()
extern void CanvasUpdateRegistry__ctor_m6D0581E2F1AE62935B2AFA50A8D669044AEDDD29 (void);
// 0x00000020 UnityEngine.UI.CanvasUpdateRegistry UnityEngine.UI.CanvasUpdateRegistry::get_instance()
extern void CanvasUpdateRegistry_get_instance_m81A024E19B9A7F49208A336886918DE3D232EC3D (void);
// 0x00000021 System.Boolean UnityEngine.UI.CanvasUpdateRegistry::ObjectValidForUpdate(UnityEngine.UI.ICanvasElement)
extern void CanvasUpdateRegistry_ObjectValidForUpdate_m8121661F105B8FB50AAF73674224CF5C647ECE31 (void);
// 0x00000022 System.Void UnityEngine.UI.CanvasUpdateRegistry::CleanInvalidItems()
extern void CanvasUpdateRegistry_CleanInvalidItems_m945A3E5AFF8D2ABE719CC86C960BA7A19D530B1C (void);
// 0x00000023 System.Void UnityEngine.UI.CanvasUpdateRegistry::PerformUpdate()
extern void CanvasUpdateRegistry_PerformUpdate_mC0E85CD761EA6D619AB636E6671CB91DF6605A12 (void);
// 0x00000024 System.Int32 UnityEngine.UI.CanvasUpdateRegistry::ParentCount(UnityEngine.Transform)
extern void CanvasUpdateRegistry_ParentCount_mC41F1B074121B659B34DF850FA9CAC84067482AD (void);
// 0x00000025 System.Int32 UnityEngine.UI.CanvasUpdateRegistry::SortLayoutList(UnityEngine.UI.ICanvasElement,UnityEngine.UI.ICanvasElement)
extern void CanvasUpdateRegistry_SortLayoutList_m8FEA1C70AA15AF804FC8E2BF0F45824FF36315DF (void);
// 0x00000026 System.Void UnityEngine.UI.CanvasUpdateRegistry::RegisterCanvasElementForLayoutRebuild(UnityEngine.UI.ICanvasElement)
extern void CanvasUpdateRegistry_RegisterCanvasElementForLayoutRebuild_m9542EF33E111AF4F551348563148D791D90D0BAF (void);
// 0x00000027 System.Boolean UnityEngine.UI.CanvasUpdateRegistry::TryRegisterCanvasElementForLayoutRebuild(UnityEngine.UI.ICanvasElement)
extern void CanvasUpdateRegistry_TryRegisterCanvasElementForLayoutRebuild_mE82AD265E12721522B359CAD98134B02C3A086C1 (void);
// 0x00000028 System.Boolean UnityEngine.UI.CanvasUpdateRegistry::InternalRegisterCanvasElementForLayoutRebuild(UnityEngine.UI.ICanvasElement)
extern void CanvasUpdateRegistry_InternalRegisterCanvasElementForLayoutRebuild_mFDCDAD147183A8DAB00B3AEDDBB9963C319D7CF3 (void);
// 0x00000029 System.Void UnityEngine.UI.CanvasUpdateRegistry::RegisterCanvasElementForGraphicRebuild(UnityEngine.UI.ICanvasElement)
extern void CanvasUpdateRegistry_RegisterCanvasElementForGraphicRebuild_m274722849A82887D5FD1EBC8F2880BF4A1E6D26C (void);
// 0x0000002A System.Boolean UnityEngine.UI.CanvasUpdateRegistry::TryRegisterCanvasElementForGraphicRebuild(UnityEngine.UI.ICanvasElement)
extern void CanvasUpdateRegistry_TryRegisterCanvasElementForGraphicRebuild_m3320ED3C5EEBF2F81573EBA0B1E7629136C57E9E (void);
// 0x0000002B System.Boolean UnityEngine.UI.CanvasUpdateRegistry::InternalRegisterCanvasElementForGraphicRebuild(UnityEngine.UI.ICanvasElement)
extern void CanvasUpdateRegistry_InternalRegisterCanvasElementForGraphicRebuild_m6EA791E10DE1BC0F84CD30C47EAFA03DAF270C28 (void);
// 0x0000002C System.Void UnityEngine.UI.CanvasUpdateRegistry::UnRegisterCanvasElementForRebuild(UnityEngine.UI.ICanvasElement)
extern void CanvasUpdateRegistry_UnRegisterCanvasElementForRebuild_m540CAC50C52279F2DC58809B4AC2798DBA104952 (void);
// 0x0000002D System.Void UnityEngine.UI.CanvasUpdateRegistry::DisableCanvasElementForRebuild(UnityEngine.UI.ICanvasElement)
extern void CanvasUpdateRegistry_DisableCanvasElementForRebuild_m401D4002D9B8A0FE78BB74F422DCB1E650EC1104 (void);
// 0x0000002E System.Void UnityEngine.UI.CanvasUpdateRegistry::InternalUnRegisterCanvasElementForLayoutRebuild(UnityEngine.UI.ICanvasElement)
extern void CanvasUpdateRegistry_InternalUnRegisterCanvasElementForLayoutRebuild_mA212240B063E15BD6E7BABBE97C495CCC0E0E977 (void);
// 0x0000002F System.Void UnityEngine.UI.CanvasUpdateRegistry::InternalUnRegisterCanvasElementForGraphicRebuild(UnityEngine.UI.ICanvasElement)
extern void CanvasUpdateRegistry_InternalUnRegisterCanvasElementForGraphicRebuild_m9D8F7A91F2DC520F674697BEF74F6A045B4C239E (void);
// 0x00000030 System.Void UnityEngine.UI.CanvasUpdateRegistry::InternalDisableCanvasElementForLayoutRebuild(UnityEngine.UI.ICanvasElement)
extern void CanvasUpdateRegistry_InternalDisableCanvasElementForLayoutRebuild_mB2D3B0FFC17A6779DFA0BDC9F3E2CD38D24B8CDB (void);
// 0x00000031 System.Void UnityEngine.UI.CanvasUpdateRegistry::InternalDisableCanvasElementForGraphicRebuild(UnityEngine.UI.ICanvasElement)
extern void CanvasUpdateRegistry_InternalDisableCanvasElementForGraphicRebuild_m55EF93896A58CFB2E1DF0080142E3CAC9299E1A4 (void);
// 0x00000032 System.Boolean UnityEngine.UI.CanvasUpdateRegistry::IsRebuildingLayout()
extern void CanvasUpdateRegistry_IsRebuildingLayout_mEF1D75977915C7B7775D0ACDC35E76F569C931EA (void);
// 0x00000033 System.Boolean UnityEngine.UI.CanvasUpdateRegistry::IsRebuildingGraphics()
extern void CanvasUpdateRegistry_IsRebuildingGraphics_m32B6EF4628CCF85397BC6F37D057BF5B955291AB (void);
// 0x00000034 System.Void UnityEngine.UI.CanvasUpdateRegistry::.cctor()
extern void CanvasUpdateRegistry__cctor_m2ACC51DA4905EDE64620BABB78F33E4DE43EE388 (void);
// 0x00000035 UnityEngine.Color UnityEngine.UI.ColorBlock::get_normalColor()
extern void ColorBlock_get_normalColor_m7C5A73BBE739FFA030EE1FE10CE78BECB8417126 (void);
// 0x00000036 System.Void UnityEngine.UI.ColorBlock::set_normalColor(UnityEngine.Color)
extern void ColorBlock_set_normalColor_m32EB40A0BB6DD89B8816945EF43CFED8A1ED78B9 (void);
// 0x00000037 UnityEngine.Color UnityEngine.UI.ColorBlock::get_highlightedColor()
extern void ColorBlock_get_highlightedColor_mDA12AC25C740DF2AFFF086B62795533B4D2ED336 (void);
// 0x00000038 System.Void UnityEngine.UI.ColorBlock::set_highlightedColor(UnityEngine.Color)
extern void ColorBlock_set_highlightedColor_m176DF68369EC1536D089EABD1357AB8EAC33BC88 (void);
// 0x00000039 UnityEngine.Color UnityEngine.UI.ColorBlock::get_pressedColor()
extern void ColorBlock_get_pressedColor_mCEBCE4184CBAE7D33FE3D8AD3FDFA3931FD87C70 (void);
// 0x0000003A System.Void UnityEngine.UI.ColorBlock::set_pressedColor(UnityEngine.Color)
extern void ColorBlock_set_pressedColor_m7F357DD0E16A18A84D2DCF039238ADE6695EB5C0 (void);
// 0x0000003B UnityEngine.Color UnityEngine.UI.ColorBlock::get_selectedColor()
extern void ColorBlock_get_selectedColor_m7C592B6652C94BACF43F0C007AC0093A2543F725 (void);
// 0x0000003C System.Void UnityEngine.UI.ColorBlock::set_selectedColor(UnityEngine.Color)
extern void ColorBlock_set_selectedColor_mA1A0ADD43DBE600443C5A1D217922614D77A6C3C (void);
// 0x0000003D UnityEngine.Color UnityEngine.UI.ColorBlock::get_disabledColor()
extern void ColorBlock_get_disabledColor_mF470FB3FA48E777F5C70F1C18EF218DB673970E9 (void);
// 0x0000003E System.Void UnityEngine.UI.ColorBlock::set_disabledColor(UnityEngine.Color)
extern void ColorBlock_set_disabledColor_m0E9C6689B962550D1C3F15275DF2409FD3C28841 (void);
// 0x0000003F System.Single UnityEngine.UI.ColorBlock::get_colorMultiplier()
extern void ColorBlock_get_colorMultiplier_m38A25E26F0AB48B6CB04A7A18EF1EAC2AFD2BA28 (void);
// 0x00000040 System.Void UnityEngine.UI.ColorBlock::set_colorMultiplier(System.Single)
extern void ColorBlock_set_colorMultiplier_m8A321BF2E07D4D184214014C32F5389C8BE06198 (void);
// 0x00000041 System.Single UnityEngine.UI.ColorBlock::get_fadeDuration()
extern void ColorBlock_get_fadeDuration_m37083141F2C18A45CC211E4683D1903E3A614B1C (void);
// 0x00000042 System.Void UnityEngine.UI.ColorBlock::set_fadeDuration(System.Single)
extern void ColorBlock_set_fadeDuration_m51827B5F57CFEF2FF8DFB03687A301E2F1B3CDCE (void);
// 0x00000043 System.Void UnityEngine.UI.ColorBlock::.cctor()
extern void ColorBlock__cctor_m12A94594EB642025030047DAB52597B01B65804F (void);
// 0x00000044 System.Boolean UnityEngine.UI.ColorBlock::Equals(System.Object)
extern void ColorBlock_Equals_m4A08484E3305C07A0CBE38C0205E21CDF05C88D7 (void);
// 0x00000045 System.Boolean UnityEngine.UI.ColorBlock::Equals(UnityEngine.UI.ColorBlock)
extern void ColorBlock_Equals_m4C4BE778C6BEFA266E497540E97CB3201B13FB6A (void);
// 0x00000046 System.Boolean UnityEngine.UI.ColorBlock::op_Equality(UnityEngine.UI.ColorBlock,UnityEngine.UI.ColorBlock)
extern void ColorBlock_op_Equality_mAE2E9F06E81EEC62E10EA85C90DAC9F7739A3C9C (void);
// 0x00000047 System.Boolean UnityEngine.UI.ColorBlock::op_Inequality(UnityEngine.UI.ColorBlock,UnityEngine.UI.ColorBlock)
extern void ColorBlock_op_Inequality_mDF9B0DC55C4C2AAE2B38BC2C0367DCE14AA64FCB (void);
// 0x00000048 System.Int32 UnityEngine.UI.ColorBlock::GetHashCode()
extern void ColorBlock_GetHashCode_m26AE64D514B61123616F54D3FC820A3FE11AA40E (void);
// 0x00000049 System.Void UnityEngine.UI.ClipperRegistry::.ctor()
extern void ClipperRegistry__ctor_m767DBC8E8436902A800FF29E5C32C54A947AB455 (void);
// 0x0000004A UnityEngine.UI.ClipperRegistry UnityEngine.UI.ClipperRegistry::get_instance()
extern void ClipperRegistry_get_instance_m2463BECEF27C0014F7A741C34F21022C400B1FE8 (void);
// 0x0000004B System.Void UnityEngine.UI.ClipperRegistry::Cull()
extern void ClipperRegistry_Cull_mA8642E827D39EF74E76C30E2DDFBB22332E77D2B (void);
// 0x0000004C System.Void UnityEngine.UI.ClipperRegistry::Register(UnityEngine.UI.IClipper)
extern void ClipperRegistry_Register_m6C2DD936413F7AF54EBCB0B5E05B89E8F0345370 (void);
// 0x0000004D System.Void UnityEngine.UI.ClipperRegistry::Unregister(UnityEngine.UI.IClipper)
extern void ClipperRegistry_Unregister_m215BC68BFB16A39E496DC9D18343C9B42A40705B (void);
// 0x0000004E System.Void UnityEngine.UI.ClipperRegistry::Disable(UnityEngine.UI.IClipper)
extern void ClipperRegistry_Disable_mD528316650DAF2622B044E960F93201230524F5D (void);
// 0x0000004F UnityEngine.Rect UnityEngine.UI.Clipping::FindCullAndClipWorldRect(System.Collections.Generic.List`1<UnityEngine.UI.RectMask2D>,System.Boolean&)
extern void Clipping_FindCullAndClipWorldRect_mCD9700613DC315DE81B430153D35A8A5439F453C (void);
// 0x00000050 System.Void UnityEngine.UI.IClipper::PerformClipping()
// 0x00000051 UnityEngine.GameObject UnityEngine.UI.IClippable::get_gameObject()
// 0x00000052 System.Void UnityEngine.UI.IClippable::RecalculateClipping()
// 0x00000053 UnityEngine.RectTransform UnityEngine.UI.IClippable::get_rectTransform()
// 0x00000054 System.Void UnityEngine.UI.IClippable::Cull(UnityEngine.Rect,System.Boolean)
// 0x00000055 System.Void UnityEngine.UI.IClippable::SetClipRect(UnityEngine.Rect,System.Boolean)
// 0x00000056 System.Void UnityEngine.UI.IClippable::SetClipSoftness(UnityEngine.Vector2)
// 0x00000057 UnityEngine.Rect UnityEngine.UI.RectangularVertexClipper::GetCanvasRect(UnityEngine.RectTransform,UnityEngine.Canvas)
extern void RectangularVertexClipper_GetCanvasRect_m57C6607045EDB62BEA830718C7731988681A415C (void);
// 0x00000058 System.Void UnityEngine.UI.RectangularVertexClipper::.ctor()
extern void RectangularVertexClipper__ctor_m357C226F7734C4E4B4939BFD94382A10E5348541 (void);
// 0x00000059 UnityEngine.UI.DefaultControls/IFactoryControls UnityEngine.UI.DefaultControls::get_factory()
extern void DefaultControls_get_factory_m650E164FAFD5C88F5D265C579E91A6D5AB0C37FD (void);
// 0x0000005A UnityEngine.GameObject UnityEngine.UI.DefaultControls::CreateUIElementRoot(System.String,UnityEngine.Vector2,System.Type[])
extern void DefaultControls_CreateUIElementRoot_m263036E6FBF57322DD1CE45CB2B1500AA687C04A (void);
// 0x0000005B UnityEngine.GameObject UnityEngine.UI.DefaultControls::CreateUIObject(System.String,UnityEngine.GameObject,System.Type[])
extern void DefaultControls_CreateUIObject_mD2182752F7F59067B563D01717CDEC050EBED6AE (void);
// 0x0000005C System.Void UnityEngine.UI.DefaultControls::SetDefaultTextValues(UnityEngine.UI.Text)
extern void DefaultControls_SetDefaultTextValues_m4AC6C3A0CD8763AD0DD3CA38FEB54D9057667664 (void);
// 0x0000005D System.Void UnityEngine.UI.DefaultControls::SetDefaultColorTransitionValues(UnityEngine.UI.Selectable)
extern void DefaultControls_SetDefaultColorTransitionValues_m4A83B274EC1B37EEDE3D3A4873E918950E5CC89E (void);
// 0x0000005E System.Void UnityEngine.UI.DefaultControls::SetParentAndAlign(UnityEngine.GameObject,UnityEngine.GameObject)
extern void DefaultControls_SetParentAndAlign_m39F7E5C514E5F413148A64D79541568258E1D648 (void);
// 0x0000005F System.Void UnityEngine.UI.DefaultControls::SetLayerRecursively(UnityEngine.GameObject,System.Int32)
extern void DefaultControls_SetLayerRecursively_m0ACD54B770C4BD8AFF5FCCE3B7683BD578D85EAC (void);
// 0x00000060 UnityEngine.GameObject UnityEngine.UI.DefaultControls::CreatePanel(UnityEngine.UI.DefaultControls/Resources)
extern void DefaultControls_CreatePanel_m7BC6386B7779EAB89AED42C9034E3B6E6E525B63 (void);
// 0x00000061 UnityEngine.GameObject UnityEngine.UI.DefaultControls::CreateButton(UnityEngine.UI.DefaultControls/Resources)
extern void DefaultControls_CreateButton_mD0D3662039D6A691FFEAF698A82D306A1A39AAB0 (void);
// 0x00000062 UnityEngine.GameObject UnityEngine.UI.DefaultControls::CreateText(UnityEngine.UI.DefaultControls/Resources)
extern void DefaultControls_CreateText_mE31FF96169C99E5F35C3771503D88D25E71607C4 (void);
// 0x00000063 UnityEngine.GameObject UnityEngine.UI.DefaultControls::CreateImage(UnityEngine.UI.DefaultControls/Resources)
extern void DefaultControls_CreateImage_mA04F918B1FD3F336028770981B203466A5D4BC72 (void);
// 0x00000064 UnityEngine.GameObject UnityEngine.UI.DefaultControls::CreateRawImage(UnityEngine.UI.DefaultControls/Resources)
extern void DefaultControls_CreateRawImage_mC824917CB780430B67442B223FC3B478F396C848 (void);
// 0x00000065 UnityEngine.GameObject UnityEngine.UI.DefaultControls::CreateSlider(UnityEngine.UI.DefaultControls/Resources)
extern void DefaultControls_CreateSlider_m6C83A0D5C3E4C7872442073E253E9F9035E008F0 (void);
// 0x00000066 UnityEngine.GameObject UnityEngine.UI.DefaultControls::CreateScrollbar(UnityEngine.UI.DefaultControls/Resources)
extern void DefaultControls_CreateScrollbar_m2C457F088FFC2ABCE11C213045D8A21EC26D7CA2 (void);
// 0x00000067 UnityEngine.GameObject UnityEngine.UI.DefaultControls::CreateToggle(UnityEngine.UI.DefaultControls/Resources)
extern void DefaultControls_CreateToggle_mDAFB086187684752D784F681CD5992CAE084F925 (void);
// 0x00000068 UnityEngine.GameObject UnityEngine.UI.DefaultControls::CreateInputField(UnityEngine.UI.DefaultControls/Resources)
extern void DefaultControls_CreateInputField_mEDCE8ECC28DE5430DA0F24D9B947E0D0B53A4FC3 (void);
// 0x00000069 UnityEngine.GameObject UnityEngine.UI.DefaultControls::CreateDropdown(UnityEngine.UI.DefaultControls/Resources)
extern void DefaultControls_CreateDropdown_mDCBE27065F24B1F37AA993C559F764B57E668616 (void);
// 0x0000006A UnityEngine.GameObject UnityEngine.UI.DefaultControls::CreateScrollView(UnityEngine.UI.DefaultControls/Resources)
extern void DefaultControls_CreateScrollView_mDF4D794DE8DE1994721455D5CC9BAAB9413D9A48 (void);
// 0x0000006B System.Void UnityEngine.UI.DefaultControls::.cctor()
extern void DefaultControls__cctor_mA82CEB5C54B5784FB65E15BC97FF938F68826252 (void);
// 0x0000006C UnityEngine.GameObject UnityEngine.UI.DefaultControls/IFactoryControls::CreateGameObject(System.String,System.Type[])
// 0x0000006D UnityEngine.GameObject UnityEngine.UI.DefaultControls/DefaultRuntimeFactory::CreateGameObject(System.String,System.Type[])
extern void DefaultRuntimeFactory_CreateGameObject_mC8B02B41466EBE09F2EF2501E6A060AA1796EF15 (void);
// 0x0000006E System.Void UnityEngine.UI.DefaultControls/DefaultRuntimeFactory::.ctor()
extern void DefaultRuntimeFactory__ctor_m491525093C771A05048F78F1B5936D8B8F914F25 (void);
// 0x0000006F System.Void UnityEngine.UI.DefaultControls/DefaultRuntimeFactory::.cctor()
extern void DefaultRuntimeFactory__cctor_m91FE60CB8690DB9AE103930E4DAB2F4D9993EF7C (void);
// 0x00000070 UnityEngine.RectTransform UnityEngine.UI.Dropdown::get_template()
extern void Dropdown_get_template_mFDEACFB41DEB580848BEA60BFE844EA029DD407D (void);
// 0x00000071 System.Void UnityEngine.UI.Dropdown::set_template(UnityEngine.RectTransform)
extern void Dropdown_set_template_m3E706A431125DCD78183DB9DAA68C164AF90D430 (void);
// 0x00000072 UnityEngine.UI.Text UnityEngine.UI.Dropdown::get_captionText()
extern void Dropdown_get_captionText_mB90FCAF70EDDAD19CFD8EBCDF2EB88E4D2C55376 (void);
// 0x00000073 System.Void UnityEngine.UI.Dropdown::set_captionText(UnityEngine.UI.Text)
extern void Dropdown_set_captionText_m4688634DB4D91214DD1BA38C57684F79E9F80B84 (void);
// 0x00000074 UnityEngine.UI.Image UnityEngine.UI.Dropdown::get_captionImage()
extern void Dropdown_get_captionImage_mC31FFCF352F035CB9FC198A35D600E77ECCEE885 (void);
// 0x00000075 System.Void UnityEngine.UI.Dropdown::set_captionImage(UnityEngine.UI.Image)
extern void Dropdown_set_captionImage_m18F032892158167D1CD739F63999FA000CF63F68 (void);
// 0x00000076 UnityEngine.UI.Text UnityEngine.UI.Dropdown::get_itemText()
extern void Dropdown_get_itemText_m89586E53171746D3BD16F8EBA0C5A814006435C1 (void);
// 0x00000077 System.Void UnityEngine.UI.Dropdown::set_itemText(UnityEngine.UI.Text)
extern void Dropdown_set_itemText_mEDA356F0B628755CE5584C0870993AB84C2EEBF7 (void);
// 0x00000078 UnityEngine.UI.Image UnityEngine.UI.Dropdown::get_itemImage()
extern void Dropdown_get_itemImage_m0189F9EF2CD249331922CFCEBEC6A0594E78CE80 (void);
// 0x00000079 System.Void UnityEngine.UI.Dropdown::set_itemImage(UnityEngine.UI.Image)
extern void Dropdown_set_itemImage_m1E0E1615193CFD73E5540DC9FB1B10F432955D02 (void);
// 0x0000007A System.Collections.Generic.List`1<UnityEngine.UI.Dropdown/OptionData> UnityEngine.UI.Dropdown::get_options()
extern void Dropdown_get_options_mF427A2157CDD901C12F1B160C4D1F8207D7111D0 (void);
// 0x0000007B System.Void UnityEngine.UI.Dropdown::set_options(System.Collections.Generic.List`1<UnityEngine.UI.Dropdown/OptionData>)
extern void Dropdown_set_options_m64FE183D8C988AC643870012A98F3095E2C2C14B (void);
// 0x0000007C UnityEngine.UI.Dropdown/DropdownEvent UnityEngine.UI.Dropdown::get_onValueChanged()
extern void Dropdown_get_onValueChanged_m05479714AEB528CF5CE93ED09870E98E6EC94CFA (void);
// 0x0000007D System.Void UnityEngine.UI.Dropdown::set_onValueChanged(UnityEngine.UI.Dropdown/DropdownEvent)
extern void Dropdown_set_onValueChanged_mC153B430EEF7453F46811C081DEB648A90EC6CB5 (void);
// 0x0000007E System.Single UnityEngine.UI.Dropdown::get_alphaFadeSpeed()
extern void Dropdown_get_alphaFadeSpeed_mB780F0BD78DE41F2EE162FB7D565CCB376E963F9 (void);
// 0x0000007F System.Void UnityEngine.UI.Dropdown::set_alphaFadeSpeed(System.Single)
extern void Dropdown_set_alphaFadeSpeed_m3AE1F3364445C8C77CE60D295CCDA95A2E99A0AD (void);
// 0x00000080 System.Int32 UnityEngine.UI.Dropdown::get_value()
extern void Dropdown_get_value_mFBF47E0C72050C5CB96B8B6D33F41BA2D1368F26 (void);
// 0x00000081 System.Void UnityEngine.UI.Dropdown::set_value(System.Int32)
extern void Dropdown_set_value_mE4418D205D53F8A3AD23B957D1A8CD71489CB3B9 (void);
// 0x00000082 System.Void UnityEngine.UI.Dropdown::SetValueWithoutNotify(System.Int32)
extern void Dropdown_SetValueWithoutNotify_mC81063284C78D7458A0F33B8AAF9FB506B1585E9 (void);
// 0x00000083 System.Void UnityEngine.UI.Dropdown::Set(System.Int32,System.Boolean)
extern void Dropdown_Set_mC71A8A900EFA989FD5C216B2B4A91E3567DC0B6A (void);
// 0x00000084 System.Void UnityEngine.UI.Dropdown::.ctor()
extern void Dropdown__ctor_mF86953AE89E7391E626E8D3F4E1590DC51B0FB15 (void);
// 0x00000085 System.Void UnityEngine.UI.Dropdown::Awake()
extern void Dropdown_Awake_mBEBE3A27F3379095CF5F296C1E8AD391EA6EE687 (void);
// 0x00000086 System.Void UnityEngine.UI.Dropdown::Start()
extern void Dropdown_Start_mFD1169E003C18BB5F93D17E6CD06BD4684FA21D4 (void);
// 0x00000087 System.Void UnityEngine.UI.Dropdown::OnDisable()
extern void Dropdown_OnDisable_mE8D6EB5D3A4FE63A1D3D2578441BD9676B216B2C (void);
// 0x00000088 System.Void UnityEngine.UI.Dropdown::RefreshShownValue()
extern void Dropdown_RefreshShownValue_m1D58FF9265C31D85B65F52410EA8A8234F974FBA (void);
// 0x00000089 System.Void UnityEngine.UI.Dropdown::AddOptions(System.Collections.Generic.List`1<UnityEngine.UI.Dropdown/OptionData>)
extern void Dropdown_AddOptions_m9DEF8F740A7A1E8F5582849C2043967BFEFC834D (void);
// 0x0000008A System.Void UnityEngine.UI.Dropdown::AddOptions(System.Collections.Generic.List`1<System.String>)
extern void Dropdown_AddOptions_m2375327887D29ACE452F157863186FC8D0AA4AF9 (void);
// 0x0000008B System.Void UnityEngine.UI.Dropdown::AddOptions(System.Collections.Generic.List`1<UnityEngine.Sprite>)
extern void Dropdown_AddOptions_mD622304344FA058D8CC1DD6B6C0DED4E933A9641 (void);
// 0x0000008C System.Void UnityEngine.UI.Dropdown::ClearOptions()
extern void Dropdown_ClearOptions_m7F59A8B054698715921D2B0E37EB1808BE53C23C (void);
// 0x0000008D System.Void UnityEngine.UI.Dropdown::SetupTemplate(UnityEngine.Canvas)
extern void Dropdown_SetupTemplate_m15EFE26E6D244C98E9A8AB5C3BB923A49DEB4EBD (void);
// 0x0000008E T UnityEngine.UI.Dropdown::GetOrAddComponent(UnityEngine.GameObject)
// 0x0000008F System.Void UnityEngine.UI.Dropdown::OnPointerClick(UnityEngine.EventSystems.PointerEventData)
extern void Dropdown_OnPointerClick_m69E4562059144BC2D414B5E984E7FB0A91801512 (void);
// 0x00000090 System.Void UnityEngine.UI.Dropdown::OnSubmit(UnityEngine.EventSystems.BaseEventData)
extern void Dropdown_OnSubmit_m5A0C1CDE9E75E21E60E0AB5AAE23647F6390B4C2 (void);
// 0x00000091 System.Void UnityEngine.UI.Dropdown::OnCancel(UnityEngine.EventSystems.BaseEventData)
extern void Dropdown_OnCancel_mEBEDBE7AC4AC120520B5BC138B67D21484BDF3C2 (void);
// 0x00000092 System.Void UnityEngine.UI.Dropdown::Show()
extern void Dropdown_Show_m83EB3F87E6A7F0E02EF65F2763C4677FD546FCFB (void);
// 0x00000093 UnityEngine.GameObject UnityEngine.UI.Dropdown::CreateBlocker(UnityEngine.Canvas)
extern void Dropdown_CreateBlocker_mF6872BF2D24F5E4DF51F2F2B72AE208F98967A7B (void);
// 0x00000094 System.Void UnityEngine.UI.Dropdown::DestroyBlocker(UnityEngine.GameObject)
extern void Dropdown_DestroyBlocker_mA6E237A7B69FD7F952B5D959C01E163A44C17F2D (void);
// 0x00000095 UnityEngine.GameObject UnityEngine.UI.Dropdown::CreateDropdownList(UnityEngine.GameObject)
extern void Dropdown_CreateDropdownList_m8126CEBE20918EC1EC807E0C2D1F2D1DD3265C18 (void);
// 0x00000096 System.Void UnityEngine.UI.Dropdown::DestroyDropdownList(UnityEngine.GameObject)
extern void Dropdown_DestroyDropdownList_m749F3F9AF5D410E4E8EED3EBA10D5FA5CD4C1FA0 (void);
// 0x00000097 UnityEngine.UI.Dropdown/DropdownItem UnityEngine.UI.Dropdown::CreateItem(UnityEngine.UI.Dropdown/DropdownItem)
extern void Dropdown_CreateItem_m31638231195DD1DF15B8FB320AEB6782222764BC (void);
// 0x00000098 System.Void UnityEngine.UI.Dropdown::DestroyItem(UnityEngine.UI.Dropdown/DropdownItem)
extern void Dropdown_DestroyItem_mDD3CF9E02F179A2A29DBC3010DEC110CE4DC7270 (void);
// 0x00000099 UnityEngine.UI.Dropdown/DropdownItem UnityEngine.UI.Dropdown::AddItem(UnityEngine.UI.Dropdown/OptionData,System.Boolean,UnityEngine.UI.Dropdown/DropdownItem,System.Collections.Generic.List`1<UnityEngine.UI.Dropdown/DropdownItem>)
extern void Dropdown_AddItem_m9708CFF7E67F17CCDD0071C68F97978863D81467 (void);
// 0x0000009A System.Void UnityEngine.UI.Dropdown::AlphaFadeList(System.Single,System.Single)
extern void Dropdown_AlphaFadeList_m88C9E2306FC0DEA8C6737C561512A535CF7255AB (void);
// 0x0000009B System.Void UnityEngine.UI.Dropdown::AlphaFadeList(System.Single,System.Single,System.Single)
extern void Dropdown_AlphaFadeList_m8EC421B16EA2A7D606AF6B0005F7F6DF5A75C5A0 (void);
// 0x0000009C System.Void UnityEngine.UI.Dropdown::SetAlpha(System.Single)
extern void Dropdown_SetAlpha_mA297B2AAE01EB7055193D514AF149C2AB21CEE36 (void);
// 0x0000009D System.Void UnityEngine.UI.Dropdown::Hide()
extern void Dropdown_Hide_m730F238F76DFA575F75C31AFADA880004B324544 (void);
// 0x0000009E System.Collections.IEnumerator UnityEngine.UI.Dropdown::DelayedDestroyDropdownList(System.Single)
extern void Dropdown_DelayedDestroyDropdownList_m78CF275EA12EAB4537161863C7E2F12000A45387 (void);
// 0x0000009F System.Void UnityEngine.UI.Dropdown::ImmediateDestroyDropdownList()
extern void Dropdown_ImmediateDestroyDropdownList_mA6162FD9DB206E8593ED2878AB2D3B8C95DA760E (void);
// 0x000000A0 System.Void UnityEngine.UI.Dropdown::OnSelectItem(UnityEngine.UI.Toggle)
extern void Dropdown_OnSelectItem_m51485B5AF5732C5C7A63A7C0984267D00534E31C (void);
// 0x000000A1 System.Void UnityEngine.UI.Dropdown::.cctor()
extern void Dropdown__cctor_m19D2E5F48D1BB0F07E7E5B328B1D4AF199E99118 (void);
// 0x000000A2 UnityEngine.UI.Text UnityEngine.UI.Dropdown/DropdownItem::get_text()
extern void DropdownItem_get_text_mFFE89EDD35FAA758C0793DC0743D2C7265150904 (void);
// 0x000000A3 System.Void UnityEngine.UI.Dropdown/DropdownItem::set_text(UnityEngine.UI.Text)
extern void DropdownItem_set_text_mC11250A9655C633527F6D09FD3774BE37740B8D6 (void);
// 0x000000A4 UnityEngine.UI.Image UnityEngine.UI.Dropdown/DropdownItem::get_image()
extern void DropdownItem_get_image_m2CCA7CA013F2EBB8C75827D616370928023827D2 (void);
// 0x000000A5 System.Void UnityEngine.UI.Dropdown/DropdownItem::set_image(UnityEngine.UI.Image)
extern void DropdownItem_set_image_mFE9F20CF013BAFC91ACE79C9FD199282D3038CE8 (void);
// 0x000000A6 UnityEngine.RectTransform UnityEngine.UI.Dropdown/DropdownItem::get_rectTransform()
extern void DropdownItem_get_rectTransform_m848D2741413CF956597F825EDCAA547655EAB7E4 (void);
// 0x000000A7 System.Void UnityEngine.UI.Dropdown/DropdownItem::set_rectTransform(UnityEngine.RectTransform)
extern void DropdownItem_set_rectTransform_mFE5A54410202CA0E151BEECFAE71CC9D4B81E50F (void);
// 0x000000A8 UnityEngine.UI.Toggle UnityEngine.UI.Dropdown/DropdownItem::get_toggle()
extern void DropdownItem_get_toggle_m696C6516BE86A6014F90D07B549868A999E2B247 (void);
// 0x000000A9 System.Void UnityEngine.UI.Dropdown/DropdownItem::set_toggle(UnityEngine.UI.Toggle)
extern void DropdownItem_set_toggle_mACFADA4ED419E2959BE14A979DDF7195562A441A (void);
// 0x000000AA System.Void UnityEngine.UI.Dropdown/DropdownItem::OnPointerEnter(UnityEngine.EventSystems.PointerEventData)
extern void DropdownItem_OnPointerEnter_m27B152E33C585BB46FA85CA0B155290CF7465618 (void);
// 0x000000AB System.Void UnityEngine.UI.Dropdown/DropdownItem::OnCancel(UnityEngine.EventSystems.BaseEventData)
extern void DropdownItem_OnCancel_m2A6C5ACB0FDD94E53B364C56F39A70A891C9CB8F (void);
// 0x000000AC System.Void UnityEngine.UI.Dropdown/DropdownItem::.ctor()
extern void DropdownItem__ctor_mC4429F24D5D1E49FA86D33B61DADC33464728B97 (void);
// 0x000000AD System.String UnityEngine.UI.Dropdown/OptionData::get_text()
extern void OptionData_get_text_m8652FE3866405C4C7C3782659009EF2C7E54D232 (void);
// 0x000000AE System.Void UnityEngine.UI.Dropdown/OptionData::set_text(System.String)
extern void OptionData_set_text_m23C74889CF93559CD64F90EC8DA69C20C13FC549 (void);
// 0x000000AF UnityEngine.Sprite UnityEngine.UI.Dropdown/OptionData::get_image()
extern void OptionData_get_image_m3A9639CF8946C3DF2D8A2364B62E69D52FD6C1BC (void);
// 0x000000B0 System.Void UnityEngine.UI.Dropdown/OptionData::set_image(UnityEngine.Sprite)
extern void OptionData_set_image_m575DC2D9B5CF0727CBEB9F32B51B9B1E219C5A0C (void);
// 0x000000B1 System.Void UnityEngine.UI.Dropdown/OptionData::.ctor()
extern void OptionData__ctor_mA1D3FE8359A7237C62D802A3E94221D451364056 (void);
// 0x000000B2 System.Void UnityEngine.UI.Dropdown/OptionData::.ctor(System.String)
extern void OptionData__ctor_m5AF14BD8BBF6118AC51A7A9A38AE3AB2DE3C2675 (void);
// 0x000000B3 System.Void UnityEngine.UI.Dropdown/OptionData::.ctor(UnityEngine.Sprite)
extern void OptionData__ctor_m8C980A8F61978E1BD5091A7476453792CD07FDF0 (void);
// 0x000000B4 System.Void UnityEngine.UI.Dropdown/OptionData::.ctor(System.String,UnityEngine.Sprite)
extern void OptionData__ctor_mD0A7C0F3F57C0259BF4307389CE24E2B33C7FD8B (void);
// 0x000000B5 System.Collections.Generic.List`1<UnityEngine.UI.Dropdown/OptionData> UnityEngine.UI.Dropdown/OptionDataList::get_options()
extern void OptionDataList_get_options_mDC1404A7BE88BEF7957F1E5C2D66EEA7F0B1B712 (void);
// 0x000000B6 System.Void UnityEngine.UI.Dropdown/OptionDataList::set_options(System.Collections.Generic.List`1<UnityEngine.UI.Dropdown/OptionData>)
extern void OptionDataList_set_options_mC0550A0E7A192C60A93B5A6DF56D86BDC6609A8E (void);
// 0x000000B7 System.Void UnityEngine.UI.Dropdown/OptionDataList::.ctor()
extern void OptionDataList__ctor_mAEA703E51D910C8FA6BE11A012B9C9F932E187C7 (void);
// 0x000000B8 System.Void UnityEngine.UI.Dropdown/DropdownEvent::.ctor()
extern void DropdownEvent__ctor_mE2A2ECC494E83733FD196E30F74CB19B05B940B9 (void);
// 0x000000B9 System.Void UnityEngine.UI.Dropdown/<>c__DisplayClass63_0::.ctor()
extern void U3CU3Ec__DisplayClass63_0__ctor_mE228A7EF3A8402C0C54E10741528BA4C12FA19CD (void);
// 0x000000BA System.Void UnityEngine.UI.Dropdown/<>c__DisplayClass63_0::<Show>b__0(System.Boolean)
extern void U3CU3Ec__DisplayClass63_0_U3CShowU3Eb__0_m42E8F0816930DEB826D2091D0E5429947623F0FB (void);
// 0x000000BB System.Void UnityEngine.UI.Dropdown/<DelayedDestroyDropdownList>d__75::.ctor(System.Int32)
extern void U3CDelayedDestroyDropdownListU3Ed__75__ctor_m47D084871CB3AF3216C6DC8D342881D7460E14C4 (void);
// 0x000000BC System.Void UnityEngine.UI.Dropdown/<DelayedDestroyDropdownList>d__75::System.IDisposable.Dispose()
extern void U3CDelayedDestroyDropdownListU3Ed__75_System_IDisposable_Dispose_m9746BE4CFDE332E7BB2B42BB56F16E4383F759EF (void);
// 0x000000BD System.Boolean UnityEngine.UI.Dropdown/<DelayedDestroyDropdownList>d__75::MoveNext()
extern void U3CDelayedDestroyDropdownListU3Ed__75_MoveNext_m2166A5CAE1107928834C346B4D52ACB983AC8845 (void);
// 0x000000BE System.Object UnityEngine.UI.Dropdown/<DelayedDestroyDropdownList>d__75::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CDelayedDestroyDropdownListU3Ed__75_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m5EA4D91EB06B4E048941BBC0AD8D92B7829B4D39 (void);
// 0x000000BF System.Void UnityEngine.UI.Dropdown/<DelayedDestroyDropdownList>d__75::System.Collections.IEnumerator.Reset()
extern void U3CDelayedDestroyDropdownListU3Ed__75_System_Collections_IEnumerator_Reset_mE42D3D74A267F697DA8EB36F64E317621923C98D (void);
// 0x000000C0 System.Object UnityEngine.UI.Dropdown/<DelayedDestroyDropdownList>d__75::System.Collections.IEnumerator.get_Current()
extern void U3CDelayedDestroyDropdownListU3Ed__75_System_Collections_IEnumerator_get_Current_m5ED616A0AA9D4E548F5B8F39FAADB1F016155CBF (void);
// 0x000000C1 UnityEngine.UI.FontData UnityEngine.UI.FontData::get_defaultFontData()
extern void FontData_get_defaultFontData_m2690B96BA777BC52F286840D37C3C74371E60AC1 (void);
// 0x000000C2 UnityEngine.Font UnityEngine.UI.FontData::get_font()
extern void FontData_get_font_mF59D5C9E97B46D8F298E83AD5A91B59740ACB8AF (void);
// 0x000000C3 System.Void UnityEngine.UI.FontData::set_font(UnityEngine.Font)
extern void FontData_set_font_m026F16527DCD0CD4F25361B4DED1756553D0FAE8 (void);
// 0x000000C4 System.Int32 UnityEngine.UI.FontData::get_fontSize()
extern void FontData_get_fontSize_mE13F5F1B45827C6011C8A31B05E618B60832331B (void);
// 0x000000C5 System.Void UnityEngine.UI.FontData::set_fontSize(System.Int32)
extern void FontData_set_fontSize_mE9B82951CCF0D998F6F115E6C9D8E5E907781D76 (void);
// 0x000000C6 UnityEngine.FontStyle UnityEngine.UI.FontData::get_fontStyle()
extern void FontData_get_fontStyle_mBDCA14034A03D890A46B8BC82CFDE821352D1CB1 (void);
// 0x000000C7 System.Void UnityEngine.UI.FontData::set_fontStyle(UnityEngine.FontStyle)
extern void FontData_set_fontStyle_m7E34F839351D0096FA9B81CE87E5A22B995765D1 (void);
// 0x000000C8 System.Boolean UnityEngine.UI.FontData::get_bestFit()
extern void FontData_get_bestFit_mF1603689DD76EEBD462794B6F16E571AA84642DE (void);
// 0x000000C9 System.Void UnityEngine.UI.FontData::set_bestFit(System.Boolean)
extern void FontData_set_bestFit_m88B35F336FB48E710623DE8DCBF4809F257A76E4 (void);
// 0x000000CA System.Int32 UnityEngine.UI.FontData::get_minSize()
extern void FontData_get_minSize_m5EF405821A9665106B19F0B1C72ECD0FE27DE727 (void);
// 0x000000CB System.Void UnityEngine.UI.FontData::set_minSize(System.Int32)
extern void FontData_set_minSize_m882073EF72432C453CF5EE554F1C40EB369B1267 (void);
// 0x000000CC System.Int32 UnityEngine.UI.FontData::get_maxSize()
extern void FontData_get_maxSize_m53ECFA4C6AD93DD56EA3D42414EF29BC83882A56 (void);
// 0x000000CD System.Void UnityEngine.UI.FontData::set_maxSize(System.Int32)
extern void FontData_set_maxSize_m19265E3D2E977671F9AA2F5FA6B67893FC8B6D4D (void);
// 0x000000CE UnityEngine.TextAnchor UnityEngine.UI.FontData::get_alignment()
extern void FontData_get_alignment_m432230C0F14D50D39C51713158D703898B7B37A5 (void);
// 0x000000CF System.Void UnityEngine.UI.FontData::set_alignment(UnityEngine.TextAnchor)
extern void FontData_set_alignment_m37A3B04BD3E107BA0ED5790C113325979BE96B80 (void);
// 0x000000D0 System.Boolean UnityEngine.UI.FontData::get_alignByGeometry()
extern void FontData_get_alignByGeometry_m0445778A81F8A695935D1DD8AF02E11CB054B753 (void);
// 0x000000D1 System.Void UnityEngine.UI.FontData::set_alignByGeometry(System.Boolean)
extern void FontData_set_alignByGeometry_m37B399E7776DD78B91DD17BA99521012A0AA9DB3 (void);
// 0x000000D2 System.Boolean UnityEngine.UI.FontData::get_richText()
extern void FontData_get_richText_mA3A81900C3BA0C464AD07736326CF5E01D1DE6A5 (void);
// 0x000000D3 System.Void UnityEngine.UI.FontData::set_richText(System.Boolean)
extern void FontData_set_richText_mD08E389ADCE118C9B2043555896565070F4A61B3 (void);
// 0x000000D4 UnityEngine.HorizontalWrapMode UnityEngine.UI.FontData::get_horizontalOverflow()
extern void FontData_get_horizontalOverflow_m4753C85F6030408730D122DA0EAD7266903A9958 (void);
// 0x000000D5 System.Void UnityEngine.UI.FontData::set_horizontalOverflow(UnityEngine.HorizontalWrapMode)
extern void FontData_set_horizontalOverflow_mFE27939FF5E996F996B9FFA277243D2F50566E03 (void);
// 0x000000D6 UnityEngine.VerticalWrapMode UnityEngine.UI.FontData::get_verticalOverflow()
extern void FontData_get_verticalOverflow_m2F782F21A1721A387126B5968DD8C5616C8EA2BD (void);
// 0x000000D7 System.Void UnityEngine.UI.FontData::set_verticalOverflow(UnityEngine.VerticalWrapMode)
extern void FontData_set_verticalOverflow_m1EBDF75A9D5F98CB815612FA35249CE177DC1E5C (void);
// 0x000000D8 System.Single UnityEngine.UI.FontData::get_lineSpacing()
extern void FontData_get_lineSpacing_m5868C02CEDB7C34057BB5AE97ACE7721BD3B5110 (void);
// 0x000000D9 System.Void UnityEngine.UI.FontData::set_lineSpacing(System.Single)
extern void FontData_set_lineSpacing_mEBE69BC6FF339D085BE81D829861627240F64EDD (void);
// 0x000000DA System.Void UnityEngine.UI.FontData::UnityEngine.ISerializationCallbackReceiver.OnBeforeSerialize()
extern void FontData_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m332904D0F17795FFFBE6419EF65B67B92AB845F5 (void);
// 0x000000DB System.Void UnityEngine.UI.FontData::UnityEngine.ISerializationCallbackReceiver.OnAfterDeserialize()
extern void FontData_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m2769988B5DDE40E09030DCD62422E10336755A2E (void);
// 0x000000DC System.Void UnityEngine.UI.FontData::.ctor()
extern void FontData__ctor_m0DDBDF050D9690404B848157702BC3A52146B9FE (void);
// 0x000000DD System.Void UnityEngine.UI.FontUpdateTracker::TrackText(UnityEngine.UI.Text)
extern void FontUpdateTracker_TrackText_m628C9A1120A76C180C11E3BBEDE99CEFF2953DCB (void);
// 0x000000DE System.Void UnityEngine.UI.FontUpdateTracker::RebuildForFont(UnityEngine.Font)
extern void FontUpdateTracker_RebuildForFont_m053A36FD90A110F0E819E3B283ECA94256A3CC77 (void);
// 0x000000DF System.Void UnityEngine.UI.FontUpdateTracker::UntrackText(UnityEngine.UI.Text)
extern void FontUpdateTracker_UntrackText_m73CFCABDCFB909E87B252ABA24D7A46C6AFF52C5 (void);
// 0x000000E0 System.Void UnityEngine.UI.FontUpdateTracker::.cctor()
extern void FontUpdateTracker__cctor_mD67474C1A4E60CE3C68663C81DD2CE57861E923C (void);
// 0x000000E1 UnityEngine.Material UnityEngine.UI.Graphic::get_defaultGraphicMaterial()
extern void Graphic_get_defaultGraphicMaterial_m11CD8ED7625283F7EF14EEC63D5CC08CEA3BE37E (void);
// 0x000000E2 UnityEngine.Color UnityEngine.UI.Graphic::get_color()
extern void Graphic_get_color_m922C082FFA75F2B2A893EF625589B1BE398DA9A8 (void);
// 0x000000E3 System.Void UnityEngine.UI.Graphic::set_color(UnityEngine.Color)
extern void Graphic_set_color_m69635A441D681D3D665214F4B856018FE9B287FC (void);
// 0x000000E4 System.Boolean UnityEngine.UI.Graphic::get_raycastTarget()
extern void Graphic_get_raycastTarget_m82EFC64ED0CCB4D377B5BF7B267BAFB82A93101C (void);
// 0x000000E5 System.Void UnityEngine.UI.Graphic::set_raycastTarget(System.Boolean)
extern void Graphic_set_raycastTarget_m6CEEC04CD0ED1231CF359AAA8EBDC0D714119ECF (void);
// 0x000000E6 UnityEngine.Vector4 UnityEngine.UI.Graphic::get_raycastPadding()
extern void Graphic_get_raycastPadding_m4D6F2F7733620B7683F73995A9E9ADD042AB94E1 (void);
// 0x000000E7 System.Void UnityEngine.UI.Graphic::set_raycastPadding(UnityEngine.Vector4)
extern void Graphic_set_raycastPadding_m8F7E6E55649D6EA562839909E4A2D9685E897E9F (void);
// 0x000000E8 System.Boolean UnityEngine.UI.Graphic::get_useLegacyMeshGeneration()
extern void Graphic_get_useLegacyMeshGeneration_mC0B873372429B274A516B1E2C002F76C7504018E (void);
// 0x000000E9 System.Void UnityEngine.UI.Graphic::set_useLegacyMeshGeneration(System.Boolean)
extern void Graphic_set_useLegacyMeshGeneration_m115AE8DE204ADAC46F457D2E973B29FC122623DD (void);
// 0x000000EA System.Void UnityEngine.UI.Graphic::.ctor()
extern void Graphic__ctor_m41CDFE33452C8382425A864410FB01D516C55D8F (void);
// 0x000000EB System.Void UnityEngine.UI.Graphic::SetAllDirty()
extern void Graphic_SetAllDirty_m026845332A5ABD19A89BCD2AFE311274937D89D4 (void);
// 0x000000EC System.Void UnityEngine.UI.Graphic::SetLayoutDirty()
extern void Graphic_SetLayoutDirty_m21F1B38F8271F887CAE7B7966AAC7487B7C2EEBD (void);
// 0x000000ED System.Void UnityEngine.UI.Graphic::SetVerticesDirty()
extern void Graphic_SetVerticesDirty_m544965E052ADD5A8517BC69B0A04BC9324D6E19F (void);
// 0x000000EE System.Void UnityEngine.UI.Graphic::SetMaterialDirty()
extern void Graphic_SetMaterialDirty_m45496137F4A0BE6B86C8C23E4A849DA96DF17CFD (void);
// 0x000000EF System.Void UnityEngine.UI.Graphic::SetRaycastDirty()
extern void Graphic_SetRaycastDirty_mCAA09EEF8484643AE160246C682EE61C7BDF912A (void);
// 0x000000F0 System.Void UnityEngine.UI.Graphic::OnRectTransformDimensionsChange()
extern void Graphic_OnRectTransformDimensionsChange_m582C9953019AEAD41756B6218D16A132F78F18DD (void);
// 0x000000F1 System.Void UnityEngine.UI.Graphic::OnBeforeTransformParentChanged()
extern void Graphic_OnBeforeTransformParentChanged_m2BA694129A195D64D4AB4E79B3375964725142C2 (void);
// 0x000000F2 System.Void UnityEngine.UI.Graphic::OnTransformParentChanged()
extern void Graphic_OnTransformParentChanged_m4E7241AAF724D464E6FB2159E3A5940AD3C938FD (void);
// 0x000000F3 System.Int32 UnityEngine.UI.Graphic::get_depth()
extern void Graphic_get_depth_m8AF43A1523D90A3A42A812835D516940E320CD17 (void);
// 0x000000F4 UnityEngine.RectTransform UnityEngine.UI.Graphic::get_rectTransform()
extern void Graphic_get_rectTransform_m87D5A808474C6B71649CBB153DEBF5F268189EFF (void);
// 0x000000F5 UnityEngine.Canvas UnityEngine.UI.Graphic::get_canvas()
extern void Graphic_get_canvas_mDB17EC66AF3FD40E8D368FC11C8F07319BB9D1B0 (void);
// 0x000000F6 System.Void UnityEngine.UI.Graphic::CacheCanvas()
extern void Graphic_CacheCanvas_mE48DF7983F345B68B89E27630DEB22C03B370249 (void);
// 0x000000F7 UnityEngine.CanvasRenderer UnityEngine.UI.Graphic::get_canvasRenderer()
extern void Graphic_get_canvasRenderer_m33EC3A53310593E87C540654486C7A73A66FCF4A (void);
// 0x000000F8 UnityEngine.Material UnityEngine.UI.Graphic::get_defaultMaterial()
extern void Graphic_get_defaultMaterial_mAEF1BC02CA3DAB0494CF1DB58BA5EC44023196F6 (void);
// 0x000000F9 UnityEngine.Material UnityEngine.UI.Graphic::get_material()
extern void Graphic_get_material_m58E72EBA935CBA9F9669B3A86B11DDB59BE7EF64 (void);
// 0x000000FA System.Void UnityEngine.UI.Graphic::set_material(UnityEngine.Material)
extern void Graphic_set_material_m3D93A3A7E51D661643E9467E3C943CEB905CF125 (void);
// 0x000000FB UnityEngine.Material UnityEngine.UI.Graphic::get_materialForRendering()
extern void Graphic_get_materialForRendering_mEFA79FBE242FDF474868D2F202E36B36AD7907A9 (void);
// 0x000000FC UnityEngine.Texture UnityEngine.UI.Graphic::get_mainTexture()
extern void Graphic_get_mainTexture_m92495D19AF1E318C85255FCD82605A6FDD0C6E56 (void);
// 0x000000FD System.Void UnityEngine.UI.Graphic::OnEnable()
extern void Graphic_OnEnable_mED9484501E8C3DFAB17F390568AE65C42D682F30 (void);
// 0x000000FE System.Void UnityEngine.UI.Graphic::OnDisable()
extern void Graphic_OnDisable_m8E7D00E9C361D62A0A596B55CB1633E14C07A2B0 (void);
// 0x000000FF System.Void UnityEngine.UI.Graphic::OnDestroy()
extern void Graphic_OnDestroy_m21BA6F3D54572B9EBE18FC1FE685DA944F610849 (void);
// 0x00000100 System.Void UnityEngine.UI.Graphic::OnCanvasHierarchyChanged()
extern void Graphic_OnCanvasHierarchyChanged_mF23B9CAAE227B9322E32D41FBF753DEF9D1BBDB2 (void);
// 0x00000101 System.Void UnityEngine.UI.Graphic::OnCullingChanged()
extern void Graphic_OnCullingChanged_m50EBEDDA655F828722AB0C6EC0CC27BC84BC4EA3 (void);
// 0x00000102 System.Void UnityEngine.UI.Graphic::Rebuild(UnityEngine.UI.CanvasUpdate)
extern void Graphic_Rebuild_m581524D9BA8EB6BE80539919811F2A062017A0B7 (void);
// 0x00000103 System.Void UnityEngine.UI.Graphic::LayoutComplete()
extern void Graphic_LayoutComplete_mA591050412BD81D7F9EF3D265B978F0CCFA33E0E (void);
// 0x00000104 System.Void UnityEngine.UI.Graphic::GraphicUpdateComplete()
extern void Graphic_GraphicUpdateComplete_m563B5685218C32201DF82180B653F09EC3BE669F (void);
// 0x00000105 System.Void UnityEngine.UI.Graphic::UpdateMaterial()
extern void Graphic_UpdateMaterial_m5A5D02BFE3677C9EC1A30054B6CC486BEE3F45A9 (void);
// 0x00000106 System.Void UnityEngine.UI.Graphic::UpdateGeometry()
extern void Graphic_UpdateGeometry_m28D710BB5ABA1340DB4350B6CBC65DC687655EC5 (void);
// 0x00000107 System.Void UnityEngine.UI.Graphic::DoMeshGeneration()
extern void Graphic_DoMeshGeneration_m753A422F3F1892128C98D9F73001DD22F108C06E (void);
// 0x00000108 System.Void UnityEngine.UI.Graphic::DoLegacyMeshGeneration()
extern void Graphic_DoLegacyMeshGeneration_mB0749616E597835253857FC89E4DD92F575BFD5A (void);
// 0x00000109 UnityEngine.Mesh UnityEngine.UI.Graphic::get_workerMesh()
extern void Graphic_get_workerMesh_m5AEF73D150916905A623B1D4A64E85719805C56B (void);
// 0x0000010A System.Void UnityEngine.UI.Graphic::OnFillVBO(System.Collections.Generic.List`1<UnityEngine.UIVertex>)
extern void Graphic_OnFillVBO_mE640EA379BCBC021D296597D2131372D7CF29E38 (void);
// 0x0000010B System.Void UnityEngine.UI.Graphic::OnPopulateMesh(UnityEngine.Mesh)
extern void Graphic_OnPopulateMesh_m7B2611B869E2E60F1E083D716026E7182DC48BF2 (void);
// 0x0000010C System.Void UnityEngine.UI.Graphic::OnPopulateMesh(UnityEngine.UI.VertexHelper)
extern void Graphic_OnPopulateMesh_m60171AAA2F2AF25E9304C8334FCE766711828DA5 (void);
// 0x0000010D System.Void UnityEngine.UI.Graphic::OnDidApplyAnimationProperties()
extern void Graphic_OnDidApplyAnimationProperties_m4D06551930C4D0D091C2D2A4499FCA2322362A1D (void);
// 0x0000010E System.Void UnityEngine.UI.Graphic::SetNativeSize()
extern void Graphic_SetNativeSize_m5A2E1E6D5AF7BB2B4490E6DBEC8CC99855400415 (void);
// 0x0000010F System.Boolean UnityEngine.UI.Graphic::Raycast(UnityEngine.Vector2,UnityEngine.Camera)
extern void Graphic_Raycast_m203BB6CA95719A31F4462A98F61AA97C24EA40FD (void);
// 0x00000110 UnityEngine.Vector2 UnityEngine.UI.Graphic::PixelAdjustPoint(UnityEngine.Vector2)
extern void Graphic_PixelAdjustPoint_m97EB91CCF7ED5D9892043E53DC0574FED3EF89AA (void);
// 0x00000111 UnityEngine.Rect UnityEngine.UI.Graphic::GetPixelAdjustedRect()
extern void Graphic_GetPixelAdjustedRect_m97D803029E437D6E20057C7FBAF420532184D16C (void);
// 0x00000112 System.Void UnityEngine.UI.Graphic::CrossFadeColor(UnityEngine.Color,System.Single,System.Boolean,System.Boolean)
extern void Graphic_CrossFadeColor_mB6115D791ABCE6CF1ADC94158D7AA135E006A566 (void);
// 0x00000113 System.Void UnityEngine.UI.Graphic::CrossFadeColor(UnityEngine.Color,System.Single,System.Boolean,System.Boolean,System.Boolean)
extern void Graphic_CrossFadeColor_m01C2BA7648677A835C6E1D7234E7028206B0AB63 (void);
// 0x00000114 UnityEngine.Color UnityEngine.UI.Graphic::CreateColorFromAlpha(System.Single)
extern void Graphic_CreateColorFromAlpha_m19B5529929135A5CF1687A9671E3958448C1B5D2 (void);
// 0x00000115 System.Void UnityEngine.UI.Graphic::CrossFadeAlpha(System.Single,System.Single,System.Boolean)
extern void Graphic_CrossFadeAlpha_m15C6267D463C1531AC1E1F6EBE489FC6A41C3404 (void);
// 0x00000116 System.Void UnityEngine.UI.Graphic::RegisterDirtyLayoutCallback(UnityEngine.Events.UnityAction)
extern void Graphic_RegisterDirtyLayoutCallback_m6F0C7B8E9712332EAC21ED730F2B8DA2DEF42165 (void);
// 0x00000117 System.Void UnityEngine.UI.Graphic::UnregisterDirtyLayoutCallback(UnityEngine.Events.UnityAction)
extern void Graphic_UnregisterDirtyLayoutCallback_m948B7781200B4F0AF75EB73B132F97C21CFED15A (void);
// 0x00000118 System.Void UnityEngine.UI.Graphic::RegisterDirtyVerticesCallback(UnityEngine.Events.UnityAction)
extern void Graphic_RegisterDirtyVerticesCallback_m445D5D558197F444F3D8EBED25DFB75901BF314D (void);
// 0x00000119 System.Void UnityEngine.UI.Graphic::UnregisterDirtyVerticesCallback(UnityEngine.Events.UnityAction)
extern void Graphic_UnregisterDirtyVerticesCallback_mCEFBFF280C6F432096A373512DF62EAF9469E8C7 (void);
// 0x0000011A System.Void UnityEngine.UI.Graphic::RegisterDirtyMaterialCallback(UnityEngine.Events.UnityAction)
extern void Graphic_RegisterDirtyMaterialCallback_m11C1085CED157B875005B6BAACD428664810AF8E (void);
// 0x0000011B System.Void UnityEngine.UI.Graphic::UnregisterDirtyMaterialCallback(UnityEngine.Events.UnityAction)
extern void Graphic_UnregisterDirtyMaterialCallback_m993432BB86AB6CEB0260DB7552A1044C5065A475 (void);
// 0x0000011C System.Void UnityEngine.UI.Graphic::.cctor()
extern void Graphic__cctor_mF4E4372347905D073802DC52ADDD76D646A9EF6F (void);
// 0x0000011D UnityEngine.Transform UnityEngine.UI.Graphic::UnityEngine.UI.ICanvasElement.get_transform()
extern void Graphic_UnityEngine_UI_ICanvasElement_get_transform_mC41FB4648C476A193FBC1F7E1847E051C642B402 (void);
// 0x0000011E System.Int32 UnityEngine.UI.GraphicRaycaster::get_sortOrderPriority()
extern void GraphicRaycaster_get_sortOrderPriority_mE193C6656CB9FB78AEFE5FA4FCFA7E3FEBE893F6 (void);
// 0x0000011F System.Int32 UnityEngine.UI.GraphicRaycaster::get_renderOrderPriority()
extern void GraphicRaycaster_get_renderOrderPriority_mF735C4553ECA17B8428200423AC4B17F0B89DF6A (void);
// 0x00000120 System.Boolean UnityEngine.UI.GraphicRaycaster::get_ignoreReversedGraphics()
extern void GraphicRaycaster_get_ignoreReversedGraphics_m404FCBC6EC70E5193E56A1DB77605AF448EF4235 (void);
// 0x00000121 System.Void UnityEngine.UI.GraphicRaycaster::set_ignoreReversedGraphics(System.Boolean)
extern void GraphicRaycaster_set_ignoreReversedGraphics_m3C6936127ECCDC00F192D18F3D5ED64B21E18897 (void);
// 0x00000122 UnityEngine.UI.GraphicRaycaster/BlockingObjects UnityEngine.UI.GraphicRaycaster::get_blockingObjects()
extern void GraphicRaycaster_get_blockingObjects_m89AF984ED21882BF086AC9DB069B8AB6D1B7636C (void);
// 0x00000123 System.Void UnityEngine.UI.GraphicRaycaster::set_blockingObjects(UnityEngine.UI.GraphicRaycaster/BlockingObjects)
extern void GraphicRaycaster_set_blockingObjects_m50F4BC7E0506311637A9108F7976BF2E3C2F743F (void);
// 0x00000124 UnityEngine.LayerMask UnityEngine.UI.GraphicRaycaster::get_blockingMask()
extern void GraphicRaycaster_get_blockingMask_m220545FB5151EE500B7155D17226E37D9349BE12 (void);
// 0x00000125 System.Void UnityEngine.UI.GraphicRaycaster::set_blockingMask(UnityEngine.LayerMask)
extern void GraphicRaycaster_set_blockingMask_mB328FB4F26453B7F72F4465FAD6E9C9ABD0BAE3E (void);
// 0x00000126 System.Void UnityEngine.UI.GraphicRaycaster::.ctor()
extern void GraphicRaycaster__ctor_mD77E88D530ECCB4CB237078BC57E5E541B87987F (void);
// 0x00000127 UnityEngine.Canvas UnityEngine.UI.GraphicRaycaster::get_canvas()
extern void GraphicRaycaster_get_canvas_m3EFC279CCA66AE0F492EF985F91EA43A587C6BF2 (void);
// 0x00000128 System.Void UnityEngine.UI.GraphicRaycaster::Raycast(UnityEngine.EventSystems.PointerEventData,System.Collections.Generic.List`1<UnityEngine.EventSystems.RaycastResult>)
extern void GraphicRaycaster_Raycast_m8EEDA350767E551AE3747D5B1AD420ADC02AA8F1 (void);
// 0x00000129 UnityEngine.Camera UnityEngine.UI.GraphicRaycaster::get_eventCamera()
extern void GraphicRaycaster_get_eventCamera_m955394C9A656E4D381195504005692BE15335BA7 (void);
// 0x0000012A System.Void UnityEngine.UI.GraphicRaycaster::Raycast(UnityEngine.Canvas,UnityEngine.Camera,UnityEngine.Vector2,System.Collections.Generic.IList`1<UnityEngine.UI.Graphic>,System.Collections.Generic.List`1<UnityEngine.UI.Graphic>)
extern void GraphicRaycaster_Raycast_mA94B1F53ABEFCBBB136D90F2890704316624BDF3 (void);
// 0x0000012B System.Void UnityEngine.UI.GraphicRaycaster::.cctor()
extern void GraphicRaycaster__cctor_m8B071B710A8344527D441A06E04957777C47E01D (void);
// 0x0000012C System.Void UnityEngine.UI.GraphicRaycaster/<>c::.cctor()
extern void U3CU3Ec__cctor_m3491A1CEC139E0F077D119BBFB4B223E0FF66206 (void);
// 0x0000012D System.Void UnityEngine.UI.GraphicRaycaster/<>c::.ctor()
extern void U3CU3Ec__ctor_mD924EE81C8A13ED0718AF48F253124F294ABB2DB (void);
// 0x0000012E System.Int32 UnityEngine.UI.GraphicRaycaster/<>c::<Raycast>b__27_0(UnityEngine.UI.Graphic,UnityEngine.UI.Graphic)
extern void U3CU3Ec_U3CRaycastU3Eb__27_0_mE359E2E78D7A20EDFC2AE14298F21CCD3245506E (void);
// 0x0000012F System.Void UnityEngine.UI.GraphicRegistry::.ctor()
extern void GraphicRegistry__ctor_m5BC68A12EAC3B9A5AFF3FF57CF2C876C45B1C111 (void);
// 0x00000130 UnityEngine.UI.GraphicRegistry UnityEngine.UI.GraphicRegistry::get_instance()
extern void GraphicRegistry_get_instance_mCFC645966A874560156295A13E74A2D148C2FA62 (void);
// 0x00000131 System.Void UnityEngine.UI.GraphicRegistry::RegisterGraphicForCanvas(UnityEngine.Canvas,UnityEngine.UI.Graphic)
extern void GraphicRegistry_RegisterGraphicForCanvas_m9A8778558EC3015B04A4EEEFC7E643608E266497 (void);
// 0x00000132 System.Void UnityEngine.UI.GraphicRegistry::RegisterRaycastGraphicForCanvas(UnityEngine.Canvas,UnityEngine.UI.Graphic)
extern void GraphicRegistry_RegisterRaycastGraphicForCanvas_m15EC248B93C2BBB65F18B290535561683142AAC9 (void);
// 0x00000133 System.Void UnityEngine.UI.GraphicRegistry::UnregisterGraphicForCanvas(UnityEngine.Canvas,UnityEngine.UI.Graphic)
extern void GraphicRegistry_UnregisterGraphicForCanvas_mA6C356F8DAE43E589A4533528F4D68900D4B5ED8 (void);
// 0x00000134 System.Void UnityEngine.UI.GraphicRegistry::UnregisterRaycastGraphicForCanvas(UnityEngine.Canvas,UnityEngine.UI.Graphic)
extern void GraphicRegistry_UnregisterRaycastGraphicForCanvas_mB1F0F7263AEF9C47F640E40F7DD246AF8FB82651 (void);
// 0x00000135 System.Void UnityEngine.UI.GraphicRegistry::DisableGraphicForCanvas(UnityEngine.Canvas,UnityEngine.UI.Graphic)
extern void GraphicRegistry_DisableGraphicForCanvas_mD22A5FE9364E7A692C85FF48C0F928A00BD11E70 (void);
// 0x00000136 System.Void UnityEngine.UI.GraphicRegistry::DisableRaycastGraphicForCanvas(UnityEngine.Canvas,UnityEngine.UI.Graphic)
extern void GraphicRegistry_DisableRaycastGraphicForCanvas_mE334F07F6E26C51A71B8E1367804D4FACB3EAA6C (void);
// 0x00000137 System.Collections.Generic.IList`1<UnityEngine.UI.Graphic> UnityEngine.UI.GraphicRegistry::GetGraphicsForCanvas(UnityEngine.Canvas)
extern void GraphicRegistry_GetGraphicsForCanvas_m6977E4571F576E30523E77D079169DEE3870AB94 (void);
// 0x00000138 System.Collections.Generic.IList`1<UnityEngine.UI.Graphic> UnityEngine.UI.GraphicRegistry::GetRaycastableGraphicsForCanvas(UnityEngine.Canvas)
extern void GraphicRegistry_GetRaycastableGraphicsForCanvas_m68B2F2A1FB4AC54EBF7AECE253BF83F0815A09F9 (void);
// 0x00000139 System.Void UnityEngine.UI.GraphicRegistry::.cctor()
extern void GraphicRegistry__cctor_m4FCE9C4CCDDE1AC16F90AE8682FC5D1FB3BA917D (void);
// 0x0000013A System.Void UnityEngine.UI.IGraphicEnabledDisabled::OnSiblingGraphicEnabledDisabled()
// 0x0000013B System.Boolean UnityEngine.UI.IMask::Enabled()
// 0x0000013C UnityEngine.RectTransform UnityEngine.UI.IMask::get_rectTransform()
// 0x0000013D System.Void UnityEngine.UI.IMaskable::RecalculateMasking()
// 0x0000013E UnityEngine.Sprite UnityEngine.UI.Image::get_sprite()
extern void Image_get_sprite_mA6FB016B4E3FE5EFFAE4B3AEE2D2DF89C61E0AF3 (void);
// 0x0000013F System.Void UnityEngine.UI.Image::set_sprite(UnityEngine.Sprite)
extern void Image_set_sprite_m55C50F18ABA0A98E926FC777F7D07FA18A7D04E4 (void);
// 0x00000140 System.Void UnityEngine.UI.Image::DisableSpriteOptimizations()
extern void Image_DisableSpriteOptimizations_mF314F3B31973284E92A8ED1508FED8BE38DEE80C (void);
// 0x00000141 UnityEngine.Sprite UnityEngine.UI.Image::get_overrideSprite()
extern void Image_get_overrideSprite_m8F1963AF2ED4861839C0F90F758050DD18CA96E0 (void);
// 0x00000142 System.Void UnityEngine.UI.Image::set_overrideSprite(UnityEngine.Sprite)
extern void Image_set_overrideSprite_m8BC8EE1CAD36C5F8C4310DBB2C0A7485106F8D2D (void);
// 0x00000143 UnityEngine.Sprite UnityEngine.UI.Image::get_activeSprite()
extern void Image_get_activeSprite_m0B2FA144D32A1F4EEB6773BC470E4A632DB4497A (void);
// 0x00000144 UnityEngine.UI.Image/Type UnityEngine.UI.Image::get_type()
extern void Image_get_type_m730305AA6DAA0AF5C57A8AD2C1B8A97E6B0B8229 (void);
// 0x00000145 System.Void UnityEngine.UI.Image::set_type(UnityEngine.UI.Image/Type)
extern void Image_set_type_m2D19AE16A5C44F054CC9F9B6F423D95ACA0C960E (void);
// 0x00000146 System.Boolean UnityEngine.UI.Image::get_preserveAspect()
extern void Image_get_preserveAspect_m912AF1F90CFCFE32FDAAB18C02DEA67E2DAC7A8B (void);
// 0x00000147 System.Void UnityEngine.UI.Image::set_preserveAspect(System.Boolean)
extern void Image_set_preserveAspect_m3D15FD6A69BE656FFC014C2162A0FE8636F478B8 (void);
// 0x00000148 System.Boolean UnityEngine.UI.Image::get_fillCenter()
extern void Image_get_fillCenter_mD1F9DB6D03C955027D9C59D0A659752741C4AD86 (void);
// 0x00000149 System.Void UnityEngine.UI.Image::set_fillCenter(System.Boolean)
extern void Image_set_fillCenter_m6F53797CF5A74CDB9259E68D845EE8C318E856CC (void);
// 0x0000014A UnityEngine.UI.Image/FillMethod UnityEngine.UI.Image::get_fillMethod()
extern void Image_get_fillMethod_mA24F59E82D3A9307852DC572753EB24675BC9B1C (void);
// 0x0000014B System.Void UnityEngine.UI.Image::set_fillMethod(UnityEngine.UI.Image/FillMethod)
extern void Image_set_fillMethod_m36C3359A19B0DE2F4B8F0454113D55450960027D (void);
// 0x0000014C System.Single UnityEngine.UI.Image::get_fillAmount()
extern void Image_get_fillAmount_mA6F275C1167931E2F166EA85058EF181D8008B09 (void);
// 0x0000014D System.Void UnityEngine.UI.Image::set_fillAmount(System.Single)
extern void Image_set_fillAmount_m1D28CFC9B15A45AB6C561AA42BD8F305605E9E3C (void);
// 0x0000014E System.Boolean UnityEngine.UI.Image::get_fillClockwise()
extern void Image_get_fillClockwise_m0B214F4423A4B9C66549C28005A181F3197270C3 (void);
// 0x0000014F System.Void UnityEngine.UI.Image::set_fillClockwise(System.Boolean)
extern void Image_set_fillClockwise_mD2296AD106CD954A5564EE0305D99248821C0CF9 (void);
// 0x00000150 System.Int32 UnityEngine.UI.Image::get_fillOrigin()
extern void Image_get_fillOrigin_m903CD74EBAC5F765ED046264BF66D3BE3A5B67AB (void);
// 0x00000151 System.Void UnityEngine.UI.Image::set_fillOrigin(System.Int32)
extern void Image_set_fillOrigin_mFAA94C29AD228999205725E6183F6A572FDDC37C (void);
// 0x00000152 System.Single UnityEngine.UI.Image::get_eventAlphaThreshold()
extern void Image_get_eventAlphaThreshold_m2FE3EFF6A13505702A4FF4CEA5AA238D035FABA4 (void);
// 0x00000153 System.Void UnityEngine.UI.Image::set_eventAlphaThreshold(System.Single)
extern void Image_set_eventAlphaThreshold_m8ECE006ABAD57DC04E35E55BA618CE9C88B6DE55 (void);
// 0x00000154 System.Single UnityEngine.UI.Image::get_alphaHitTestMinimumThreshold()
extern void Image_get_alphaHitTestMinimumThreshold_m4271487851417EC8581659AA2611705182F2EB1B (void);
// 0x00000155 System.Void UnityEngine.UI.Image::set_alphaHitTestMinimumThreshold(System.Single)
extern void Image_set_alphaHitTestMinimumThreshold_m95763334366C9A33E68C0BBC6695D62E0DFE462B (void);
// 0x00000156 System.Boolean UnityEngine.UI.Image::get_useSpriteMesh()
extern void Image_get_useSpriteMesh_mA2A1DEB7AC296C364B911D72B4A25828963BD8BE (void);
// 0x00000157 System.Void UnityEngine.UI.Image::set_useSpriteMesh(System.Boolean)
extern void Image_set_useSpriteMesh_m3BFF46CFA7C14E4646BC2E50D041C704B2A6B549 (void);
// 0x00000158 System.Void UnityEngine.UI.Image::.ctor()
extern void Image__ctor_m11AB7390F778F2BA5F3B110654400201123F1924 (void);
// 0x00000159 UnityEngine.Material UnityEngine.UI.Image::get_defaultETC1GraphicMaterial()
extern void Image_get_defaultETC1GraphicMaterial_mFC59846AF686F1BC371F1722D16991F1F50A91AE (void);
// 0x0000015A UnityEngine.Texture UnityEngine.UI.Image::get_mainTexture()
extern void Image_get_mainTexture_m7650B5327838CEE79723AAE344CC05041E660B9F (void);
// 0x0000015B System.Boolean UnityEngine.UI.Image::get_hasBorder()
extern void Image_get_hasBorder_m444A40E69275ED3748079EBF6D6FD489FD17CA51 (void);
// 0x0000015C System.Single UnityEngine.UI.Image::get_pixelsPerUnitMultiplier()
extern void Image_get_pixelsPerUnitMultiplier_m1F9B9896852C675664DE06FB74A1C5105626A41D (void);
// 0x0000015D System.Void UnityEngine.UI.Image::set_pixelsPerUnitMultiplier(System.Single)
extern void Image_set_pixelsPerUnitMultiplier_m5F08D766BE960A22D846506E173B2E11D00A1E64 (void);
// 0x0000015E System.Single UnityEngine.UI.Image::get_pixelsPerUnit()
extern void Image_get_pixelsPerUnit_m98A6403E0798B752A00DEF044DDF6B199B35593F (void);
// 0x0000015F System.Single UnityEngine.UI.Image::get_multipliedPixelsPerUnit()
extern void Image_get_multipliedPixelsPerUnit_m1C97AC7EFDE809F8A763CE7C4064678BB1E14270 (void);
// 0x00000160 UnityEngine.Material UnityEngine.UI.Image::get_material()
extern void Image_get_material_m64BB551A86DA68E6BB4824D80B87D1222B2A719A (void);
// 0x00000161 System.Void UnityEngine.UI.Image::set_material(UnityEngine.Material)
extern void Image_set_material_m666B9F6F4224CC1ACC26179CD32E491C6B2258A8 (void);
// 0x00000162 System.Void UnityEngine.UI.Image::OnBeforeSerialize()
extern void Image_OnBeforeSerialize_mC5AC9EFBACF39D25F135CDA97DBB233E9486936D (void);
// 0x00000163 System.Void UnityEngine.UI.Image::OnAfterDeserialize()
extern void Image_OnAfterDeserialize_mAFE846C60D003A128F7945A8EE0887C677DD6CFC (void);
// 0x00000164 System.Void UnityEngine.UI.Image::PreserveSpriteAspectRatio(UnityEngine.Rect&,UnityEngine.Vector2)
extern void Image_PreserveSpriteAspectRatio_mEA1EA46C681D08468D3EB86E7E0185E89FDEC667 (void);
// 0x00000165 UnityEngine.Vector4 UnityEngine.UI.Image::GetDrawingDimensions(System.Boolean)
extern void Image_GetDrawingDimensions_mC40C4D11A97A7EEB627C9F1369D296199CAD5CD4 (void);
// 0x00000166 System.Void UnityEngine.UI.Image::SetNativeSize()
extern void Image_SetNativeSize_m3431DB7456FD40DD2CBEDF3FD82EBF55B46E7D8E (void);
// 0x00000167 System.Void UnityEngine.UI.Image::OnPopulateMesh(UnityEngine.UI.VertexHelper)
extern void Image_OnPopulateMesh_mA43AC802D098FB93CC941275F90AEBFCC81F4EB5 (void);
// 0x00000168 System.Void UnityEngine.UI.Image::TrackSprite()
extern void Image_TrackSprite_m807EE9E74775D2F4AF1D54B52F2A230C9B5C1143 (void);
// 0x00000169 System.Void UnityEngine.UI.Image::OnEnable()
extern void Image_OnEnable_mF77EA3DC594DAE50115D24C3CA09C3E120F32A4F (void);
// 0x0000016A System.Void UnityEngine.UI.Image::OnDisable()
extern void Image_OnDisable_m9A63211DBBB352D9FEDB27B12E129FE42F86FF12 (void);
// 0x0000016B System.Void UnityEngine.UI.Image::UpdateMaterial()
extern void Image_UpdateMaterial_m6B56C4E5F44712288A92D4A0CED493C2803E218F (void);
// 0x0000016C System.Void UnityEngine.UI.Image::OnCanvasHierarchyChanged()
extern void Image_OnCanvasHierarchyChanged_m8ACAB18B22A3B6D65F3D580886C5AA3C86C79687 (void);
// 0x0000016D System.Void UnityEngine.UI.Image::GenerateSimpleSprite(UnityEngine.UI.VertexHelper,System.Boolean)
extern void Image_GenerateSimpleSprite_m76CEEC3CC99171B4BA9E26DC2E46B0BED05FD355 (void);
// 0x0000016E System.Void UnityEngine.UI.Image::GenerateSprite(UnityEngine.UI.VertexHelper,System.Boolean)
extern void Image_GenerateSprite_mABC4D004A77A4203E4271EC29EC12A8E22A65858 (void);
// 0x0000016F System.Void UnityEngine.UI.Image::GenerateSlicedSprite(UnityEngine.UI.VertexHelper)
extern void Image_GenerateSlicedSprite_mA22D83C3A325339734B90ADA61204BC1F4ABE117 (void);
// 0x00000170 System.Void UnityEngine.UI.Image::GenerateTiledSprite(UnityEngine.UI.VertexHelper)
extern void Image_GenerateTiledSprite_mD85AC44D6C08B374EF7EA03A4BF7943CC830DE37 (void);
// 0x00000171 System.Void UnityEngine.UI.Image::AddQuad(UnityEngine.UI.VertexHelper,UnityEngine.Vector3[],UnityEngine.Color32,UnityEngine.Vector3[])
extern void Image_AddQuad_m0AE23278BA55C31B5716D4D9E86D869F1E4C6803 (void);
// 0x00000172 System.Void UnityEngine.UI.Image::AddQuad(UnityEngine.UI.VertexHelper,UnityEngine.Vector2,UnityEngine.Vector2,UnityEngine.Color32,UnityEngine.Vector2,UnityEngine.Vector2)
extern void Image_AddQuad_m99E948C86E6BC6A57C6260FCF34A4D4ABB7E521C (void);
// 0x00000173 UnityEngine.Vector4 UnityEngine.UI.Image::GetAdjustedBorders(UnityEngine.Vector4,UnityEngine.Rect)
extern void Image_GetAdjustedBorders_mEE19FD1E4AD9939B9565C4A0B3937433F6A73596 (void);
// 0x00000174 System.Void UnityEngine.UI.Image::GenerateFilledSprite(UnityEngine.UI.VertexHelper,System.Boolean)
extern void Image_GenerateFilledSprite_mB32C81541BE95D5144A522E1487497A4139883BF (void);
// 0x00000175 System.Boolean UnityEngine.UI.Image::RadialCut(UnityEngine.Vector3[],UnityEngine.Vector3[],System.Single,System.Boolean,System.Int32)
extern void Image_RadialCut_m7A4BB3C664FF6469FC7579B2CCC300516C88C229 (void);
// 0x00000176 System.Void UnityEngine.UI.Image::RadialCut(UnityEngine.Vector3[],System.Single,System.Single,System.Boolean,System.Int32)
extern void Image_RadialCut_m1785A0403C98D4E8FED402DC24A4370875D5848E (void);
// 0x00000177 System.Void UnityEngine.UI.Image::CalculateLayoutInputHorizontal()
extern void Image_CalculateLayoutInputHorizontal_m04FE856B2079DAA841634A767CE764DE5AFC7C94 (void);
// 0x00000178 System.Void UnityEngine.UI.Image::CalculateLayoutInputVertical()
extern void Image_CalculateLayoutInputVertical_mFB4812D7E95B71CDA2A78981FA8E57C952AE16CA (void);
// 0x00000179 System.Single UnityEngine.UI.Image::get_minWidth()
extern void Image_get_minWidth_m80F0CCAD55244FB5B0EDBE47A969AE727404A711 (void);
// 0x0000017A System.Single UnityEngine.UI.Image::get_preferredWidth()
extern void Image_get_preferredWidth_m632101B442589E11959948982B13E2A237645F4E (void);
// 0x0000017B System.Single UnityEngine.UI.Image::get_flexibleWidth()
extern void Image_get_flexibleWidth_m0334DEE5F5BB3D87FA70375C3C9F7C839DDE7AA4 (void);
// 0x0000017C System.Single UnityEngine.UI.Image::get_minHeight()
extern void Image_get_minHeight_m41442CB39DC88BF08A33408C65C3392D2752709A (void);
// 0x0000017D System.Single UnityEngine.UI.Image::get_preferredHeight()
extern void Image_get_preferredHeight_mE2A282AF37B5669BA18B7FE022C50F6603E7946F (void);
// 0x0000017E System.Single UnityEngine.UI.Image::get_flexibleHeight()
extern void Image_get_flexibleHeight_m49E73811DD8518AC06CB59E240E6FA09A1FABFEA (void);
// 0x0000017F System.Int32 UnityEngine.UI.Image::get_layoutPriority()
extern void Image_get_layoutPriority_m62067AC13C013976E5BB8E752A2896201A2BC8B6 (void);
// 0x00000180 System.Boolean UnityEngine.UI.Image::IsRaycastLocationValid(UnityEngine.Vector2,UnityEngine.Camera)
extern void Image_IsRaycastLocationValid_m216026118F2C011AA5026CB849F28C669D449CF6 (void);
// 0x00000181 UnityEngine.Vector2 UnityEngine.UI.Image::MapCoordinate(UnityEngine.Vector2,UnityEngine.Rect)
extern void Image_MapCoordinate_m4539737D09F1D8FD0296E59E8AB2A4C906D6E096 (void);
// 0x00000182 System.Void UnityEngine.UI.Image::RebuildImage(UnityEngine.U2D.SpriteAtlas)
extern void Image_RebuildImage_m19F129C68BAD55C32AB963738669D81E29C81352 (void);
// 0x00000183 System.Void UnityEngine.UI.Image::TrackImage(UnityEngine.UI.Image)
extern void Image_TrackImage_m18837F3C0DD115BD62F9DCC532626FC8DE3988F6 (void);
// 0x00000184 System.Void UnityEngine.UI.Image::UnTrackImage(UnityEngine.UI.Image)
extern void Image_UnTrackImage_mE1307DDE71EF1116A030342E664C38661F46C461 (void);
// 0x00000185 System.Void UnityEngine.UI.Image::OnDidApplyAnimationProperties()
extern void Image_OnDidApplyAnimationProperties_mAD738CA9AA44E112C09FDCA2CA4DD5B27CF4CC17 (void);
// 0x00000186 System.Void UnityEngine.UI.Image::.cctor()
extern void Image__cctor_m0C262032BB7F9AFFCC95D8D30C7625C4DB44B785 (void);
// 0x00000187 UnityEngine.EventSystems.BaseInput UnityEngine.UI.InputField::get_input()
extern void InputField_get_input_m2BF279A6233AFFC48AC5744CA27ECC7AFBC45320 (void);
// 0x00000188 System.String UnityEngine.UI.InputField::get_compositionString()
extern void InputField_get_compositionString_mE86C18CEB54EF0C00415F1B1E04586046BCDBB36 (void);
// 0x00000189 System.Void UnityEngine.UI.InputField::.ctor()
extern void InputField__ctor_mC18C13BA47FF7230CD94BCC3E829E522E800CA3B (void);
// 0x0000018A UnityEngine.Mesh UnityEngine.UI.InputField::get_mesh()
extern void InputField_get_mesh_mC8B8AD9D1A849BE210E376C18193A662F8F015FA (void);
// 0x0000018B UnityEngine.TextGenerator UnityEngine.UI.InputField::get_cachedInputTextGenerator()
extern void InputField_get_cachedInputTextGenerator_m791D35AEA97BE7F89C42F5A0513E5408F0E6BF3C (void);
// 0x0000018C System.Void UnityEngine.UI.InputField::set_shouldHideMobileInput(System.Boolean)
extern void InputField_set_shouldHideMobileInput_m682DA851218A025AADCBCC1A208149C8ECD9AC52 (void);
// 0x0000018D System.Boolean UnityEngine.UI.InputField::get_shouldHideMobileInput()
extern void InputField_get_shouldHideMobileInput_m15A0741882D419292EACEC96DA38B7691F24263F (void);
// 0x0000018E System.Void UnityEngine.UI.InputField::set_shouldActivateOnSelect(System.Boolean)
extern void InputField_set_shouldActivateOnSelect_m346C4DAD42D68592BE5ABA56DB0D0EFB08311700 (void);
// 0x0000018F System.Boolean UnityEngine.UI.InputField::get_shouldActivateOnSelect()
extern void InputField_get_shouldActivateOnSelect_mC219F26075E9EF829F9ED0363EAB0C5D261FD82D (void);
// 0x00000190 System.String UnityEngine.UI.InputField::get_text()
extern void InputField_get_text_m15D0C784A4A104390610325B02216FC2A6F1077C (void);
// 0x00000191 System.Void UnityEngine.UI.InputField::set_text(System.String)
extern void InputField_set_text_m30EE95E064474DFA2842CA2DD6E831FF3F3500BF (void);
// 0x00000192 System.Void UnityEngine.UI.InputField::SetTextWithoutNotify(System.String)
extern void InputField_SetTextWithoutNotify_mD200F65D8CBD6B825CCF437FBB30BACD97365893 (void);
// 0x00000193 System.Void UnityEngine.UI.InputField::SetText(System.String,System.Boolean)
extern void InputField_SetText_mA2D467E75DBB4E11719D893B45DB6EC1092AE956 (void);
// 0x00000194 System.Boolean UnityEngine.UI.InputField::get_isFocused()
extern void InputField_get_isFocused_m60B873B25A63045E65D55BDC90268C8623D7C418 (void);
// 0x00000195 System.Single UnityEngine.UI.InputField::get_caretBlinkRate()
extern void InputField_get_caretBlinkRate_mAA39E62C211CAFE475B1CC9B43E2B5E01D8B861F (void);
// 0x00000196 System.Void UnityEngine.UI.InputField::set_caretBlinkRate(System.Single)
extern void InputField_set_caretBlinkRate_m3D311EA33F9D274CC09B83BC09B7B0784BFAFE30 (void);
// 0x00000197 System.Int32 UnityEngine.UI.InputField::get_caretWidth()
extern void InputField_get_caretWidth_m70D0EBCAD43971C20B8FD4E031A86587CD75ED83 (void);
// 0x00000198 System.Void UnityEngine.UI.InputField::set_caretWidth(System.Int32)
extern void InputField_set_caretWidth_m7F11F6C71B41CBC9A98E078B880D093F0901F25B (void);
// 0x00000199 UnityEngine.UI.Text UnityEngine.UI.InputField::get_textComponent()
extern void InputField_get_textComponent_mF2F6C6AB96152BA577A1364A663906315AD01D4F (void);
// 0x0000019A System.Void UnityEngine.UI.InputField::set_textComponent(UnityEngine.UI.Text)
extern void InputField_set_textComponent_m4F14433F7DCF4FD7C427177BF1EAE71719A3CE7F (void);
// 0x0000019B UnityEngine.UI.Graphic UnityEngine.UI.InputField::get_placeholder()
extern void InputField_get_placeholder_mFA1B993866F8CD89D76AA238D8CFF2D48AC3970B (void);
// 0x0000019C System.Void UnityEngine.UI.InputField::set_placeholder(UnityEngine.UI.Graphic)
extern void InputField_set_placeholder_m55B3E498EFFC547D3B4AEFFE51934D49641D97D3 (void);
// 0x0000019D UnityEngine.Color UnityEngine.UI.InputField::get_caretColor()
extern void InputField_get_caretColor_m766D1DB9AF41796F23E0A0DD8C565EE50829731C (void);
// 0x0000019E System.Void UnityEngine.UI.InputField::set_caretColor(UnityEngine.Color)
extern void InputField_set_caretColor_mD8C38138581CCC8ED15030569E4B96E65BDBDDE4 (void);
// 0x0000019F System.Boolean UnityEngine.UI.InputField::get_customCaretColor()
extern void InputField_get_customCaretColor_mBF58C9552A845540FE36960BC3F29E3BF7DF7D15 (void);
// 0x000001A0 System.Void UnityEngine.UI.InputField::set_customCaretColor(System.Boolean)
extern void InputField_set_customCaretColor_mAF774E66BB80B5E42586B1F2AB74765A0796FC29 (void);
// 0x000001A1 UnityEngine.Color UnityEngine.UI.InputField::get_selectionColor()
extern void InputField_get_selectionColor_m205696954151CB58BB8DF822563B39E78EE9F7BB (void);
// 0x000001A2 System.Void UnityEngine.UI.InputField::set_selectionColor(UnityEngine.Color)
extern void InputField_set_selectionColor_m7F0CA63B660916CCB1AC6C00F3772771497C3636 (void);
// 0x000001A3 UnityEngine.UI.InputField/SubmitEvent UnityEngine.UI.InputField::get_onEndEdit()
extern void InputField_get_onEndEdit_m0C979736255DF520629D269CCDD6452EF4F52EB5 (void);
// 0x000001A4 System.Void UnityEngine.UI.InputField::set_onEndEdit(UnityEngine.UI.InputField/SubmitEvent)
extern void InputField_set_onEndEdit_m4123895F70108223E6A0BDC9FE72FD650E7D5F18 (void);
// 0x000001A5 UnityEngine.UI.InputField/OnChangeEvent UnityEngine.UI.InputField::get_onValueChange()
extern void InputField_get_onValueChange_m140D8CE21B587746CB1AD299B6B0C8DA6173D60F (void);
// 0x000001A6 System.Void UnityEngine.UI.InputField::set_onValueChange(UnityEngine.UI.InputField/OnChangeEvent)
extern void InputField_set_onValueChange_m060DFA02771168B0C5B1877D9483F60D4D8AB5E0 (void);
// 0x000001A7 UnityEngine.UI.InputField/OnChangeEvent UnityEngine.UI.InputField::get_onValueChanged()
extern void InputField_get_onValueChanged_mAF7D77CCBED34CF4A46A9E4EA77405D790C58449 (void);
// 0x000001A8 System.Void UnityEngine.UI.InputField::set_onValueChanged(UnityEngine.UI.InputField/OnChangeEvent)
extern void InputField_set_onValueChanged_m5F3D099C06E6C12A1394D3F915533C83BA9A00DD (void);
// 0x000001A9 UnityEngine.UI.InputField/OnValidateInput UnityEngine.UI.InputField::get_onValidateInput()
extern void InputField_get_onValidateInput_mCBE2BB6D465B095BDA7F75091ED88BAFCC7EC9EE (void);
// 0x000001AA System.Void UnityEngine.UI.InputField::set_onValidateInput(UnityEngine.UI.InputField/OnValidateInput)
extern void InputField_set_onValidateInput_mDADA0C659939DF17C43501F6D09C7B12243FFB5C (void);
// 0x000001AB System.Int32 UnityEngine.UI.InputField::get_characterLimit()
extern void InputField_get_characterLimit_mE37C423F3248D89801FD33A223878062DE951C51 (void);
// 0x000001AC System.Void UnityEngine.UI.InputField::set_characterLimit(System.Int32)
extern void InputField_set_characterLimit_m3CCF4D3F14B341DB38E5CDDB2BDF9F550A559613 (void);
// 0x000001AD UnityEngine.UI.InputField/ContentType UnityEngine.UI.InputField::get_contentType()
extern void InputField_get_contentType_m3717B7CB4EB125E353B4B7F48DFDE21F195AE9E9 (void);
// 0x000001AE System.Void UnityEngine.UI.InputField::set_contentType(UnityEngine.UI.InputField/ContentType)
extern void InputField_set_contentType_mEB66E08EF42632A15C4A5A30BD0A19E20560C7B1 (void);
// 0x000001AF UnityEngine.UI.InputField/LineType UnityEngine.UI.InputField::get_lineType()
extern void InputField_get_lineType_m02C63C6C52D3FDE31AA67AB9C77B315B7AEA231B (void);
// 0x000001B0 System.Void UnityEngine.UI.InputField::set_lineType(UnityEngine.UI.InputField/LineType)
extern void InputField_set_lineType_mE9D5E11A1A7F16648E082590395D21742CA53E5B (void);
// 0x000001B1 UnityEngine.UI.InputField/InputType UnityEngine.UI.InputField::get_inputType()
extern void InputField_get_inputType_mB69EBB88308E8797FF13A77E5540BACE1277F065 (void);
// 0x000001B2 System.Void UnityEngine.UI.InputField::set_inputType(UnityEngine.UI.InputField/InputType)
extern void InputField_set_inputType_mAA484C54A6D3A6DCBA6B12CC939186928BE031A4 (void);
// 0x000001B3 UnityEngine.TouchScreenKeyboard UnityEngine.UI.InputField::get_touchScreenKeyboard()
extern void InputField_get_touchScreenKeyboard_m99C235BD411F5970741599B1A704A5089B112086 (void);
// 0x000001B4 UnityEngine.TouchScreenKeyboardType UnityEngine.UI.InputField::get_keyboardType()
extern void InputField_get_keyboardType_mB9BE8A9EDAD0C10413436CC933EE187CC4A3CBEE (void);
// 0x000001B5 System.Void UnityEngine.UI.InputField::set_keyboardType(UnityEngine.TouchScreenKeyboardType)
extern void InputField_set_keyboardType_m4D7479C6DF0C3AEC74A1309D4A3528D7EE972F21 (void);
// 0x000001B6 UnityEngine.UI.InputField/CharacterValidation UnityEngine.UI.InputField::get_characterValidation()
extern void InputField_get_characterValidation_m9446E5CDD7DF9B41799344119032DAB48DDE9CFC (void);
// 0x000001B7 System.Void UnityEngine.UI.InputField::set_characterValidation(UnityEngine.UI.InputField/CharacterValidation)
extern void InputField_set_characterValidation_m3049D3C8558B473E505A8A0A6E41270202A07AE4 (void);
// 0x000001B8 System.Boolean UnityEngine.UI.InputField::get_readOnly()
extern void InputField_get_readOnly_m7E74B9F7F030D889835F94922325C9AFB3388CA0 (void);
// 0x000001B9 System.Void UnityEngine.UI.InputField::set_readOnly(System.Boolean)
extern void InputField_set_readOnly_mFE554E845DEE5D76223D618E3E553169F7EF8AB2 (void);
// 0x000001BA System.Boolean UnityEngine.UI.InputField::get_multiLine()
extern void InputField_get_multiLine_mA9BE5B7BFEE95E9764958FB83F61D1E69B2EA8B2 (void);
// 0x000001BB System.Char UnityEngine.UI.InputField::get_asteriskChar()
extern void InputField_get_asteriskChar_m51B289120D5EF43D619DAF0C287938A629399E44 (void);
// 0x000001BC System.Void UnityEngine.UI.InputField::set_asteriskChar(System.Char)
extern void InputField_set_asteriskChar_m526E2E04223E307C4EB083E3377E907FEE9BC2A9 (void);
// 0x000001BD System.Boolean UnityEngine.UI.InputField::get_wasCanceled()
extern void InputField_get_wasCanceled_mF527091ED7C92731E83F921F7ADDAF2D5E77086E (void);
// 0x000001BE System.Void UnityEngine.UI.InputField::ClampPos(System.Int32&)
extern void InputField_ClampPos_m4DA5D04357FF57128B5A03EBBD015AFCFA1F1A6A (void);
// 0x000001BF System.Int32 UnityEngine.UI.InputField::get_caretPositionInternal()
extern void InputField_get_caretPositionInternal_m9208E7E6A4ED51AB8E47C9E35538A431806BC488 (void);
// 0x000001C0 System.Void UnityEngine.UI.InputField::set_caretPositionInternal(System.Int32)
extern void InputField_set_caretPositionInternal_mB399A8BC983C2010A5CB6FD0BE19ECDB7CA49104 (void);
// 0x000001C1 System.Int32 UnityEngine.UI.InputField::get_caretSelectPositionInternal()
extern void InputField_get_caretSelectPositionInternal_mE282EC08EADCAEC8741AE308178F4C82813B2E3D (void);
// 0x000001C2 System.Void UnityEngine.UI.InputField::set_caretSelectPositionInternal(System.Int32)
extern void InputField_set_caretSelectPositionInternal_mFF674F8B5A0BA7697E4FDA13E917BC5ECB83E137 (void);
// 0x000001C3 System.Boolean UnityEngine.UI.InputField::get_hasSelection()
extern void InputField_get_hasSelection_m2CF3B8E665092331229BE635B40A6A32AEB47E92 (void);
// 0x000001C4 System.Int32 UnityEngine.UI.InputField::get_caretPosition()
extern void InputField_get_caretPosition_mEC68FA5C979CA4282A59511BCE0701569AA69C7B (void);
// 0x000001C5 System.Void UnityEngine.UI.InputField::set_caretPosition(System.Int32)
extern void InputField_set_caretPosition_m7B6934DD91905D0DBF721EB09B1B7960ABA5BCA9 (void);
// 0x000001C6 System.Int32 UnityEngine.UI.InputField::get_selectionAnchorPosition()
extern void InputField_get_selectionAnchorPosition_mD6A77D08DB66FA68241D052D7D98570DBF3C6995 (void);
// 0x000001C7 System.Void UnityEngine.UI.InputField::set_selectionAnchorPosition(System.Int32)
extern void InputField_set_selectionAnchorPosition_mE03A165781A4BE3825A1246B829969F7E144A171 (void);
// 0x000001C8 System.Int32 UnityEngine.UI.InputField::get_selectionFocusPosition()
extern void InputField_get_selectionFocusPosition_m477373F0852FDC320EC8F76E21948642B9D1B79B (void);
// 0x000001C9 System.Void UnityEngine.UI.InputField::set_selectionFocusPosition(System.Int32)
extern void InputField_set_selectionFocusPosition_mBA1CA496C3396A287AA866243DFA6F7EFAECF54D (void);
// 0x000001CA System.Void UnityEngine.UI.InputField::OnEnable()
extern void InputField_OnEnable_mB72732E0908E821D56B9758D1A9533274A2D9D01 (void);
// 0x000001CB System.Void UnityEngine.UI.InputField::OnDisable()
extern void InputField_OnDisable_m31BB366D7FC75A02D98163A685DD0F5522084AB3 (void);
// 0x000001CC System.Void UnityEngine.UI.InputField::OnDestroy()
extern void InputField_OnDestroy_m6559B5D023DE3AE9C70A3CAA528047B57D86EB77 (void);
// 0x000001CD System.Collections.IEnumerator UnityEngine.UI.InputField::CaretBlink()
extern void InputField_CaretBlink_m04B107717E10F3C3D28E5D3C9ED2EC4645F75BE3 (void);
// 0x000001CE System.Void UnityEngine.UI.InputField::SetCaretVisible()
extern void InputField_SetCaretVisible_m0FA23A6B2D29C01831B929417911C028FF76F787 (void);
// 0x000001CF System.Void UnityEngine.UI.InputField::SetCaretActive()
extern void InputField_SetCaretActive_m7F895A414EB388CCB62D99F342D96B4FF816DE48 (void);
// 0x000001D0 System.Void UnityEngine.UI.InputField::UpdateCaretMaterial()
extern void InputField_UpdateCaretMaterial_m4CD8DA75BF5C8041413326AAD8E7CE00EC2D4F30 (void);
// 0x000001D1 System.Void UnityEngine.UI.InputField::OnFocus()
extern void InputField_OnFocus_m12136DA7D04901A0D657FA260D7B6D29B4B777EA (void);
// 0x000001D2 System.Void UnityEngine.UI.InputField::SelectAll()
extern void InputField_SelectAll_mE0FC6C49370561EC070ADF1CEED5159DC4573176 (void);
// 0x000001D3 System.Void UnityEngine.UI.InputField::MoveTextEnd(System.Boolean)
extern void InputField_MoveTextEnd_m72571BFED9CD0DFCA3E28561DE7A81FA435586F1 (void);
// 0x000001D4 System.Void UnityEngine.UI.InputField::MoveTextStart(System.Boolean)
extern void InputField_MoveTextStart_mB2AA4132A375A5DEAC2116D638DCC0C4F30810B7 (void);
// 0x000001D5 System.String UnityEngine.UI.InputField::get_clipboard()
extern void InputField_get_clipboard_mA422380D8FDBD384589651DEC5EC994BEAD8B3F8 (void);
// 0x000001D6 System.Void UnityEngine.UI.InputField::set_clipboard(System.String)
extern void InputField_set_clipboard_mDB385A8B02A1DA01F8A8525642AD7B90A962E0D2 (void);
// 0x000001D7 System.Boolean UnityEngine.UI.InputField::TouchScreenKeyboardShouldBeUsed()
extern void InputField_TouchScreenKeyboardShouldBeUsed_m2B607F8829742049E8D17348C30480DAC10F6272 (void);
// 0x000001D8 System.Boolean UnityEngine.UI.InputField::InPlaceEditing()
extern void InputField_InPlaceEditing_mEE428E0610B32F56F4707C1DAD9BF2644EEE759D (void);
// 0x000001D9 System.Boolean UnityEngine.UI.InputField::InPlaceEditingChanged()
extern void InputField_InPlaceEditingChanged_m4CF273C1B5FCEA9B39397C4248A13B9486F6554B (void);
// 0x000001DA System.Void UnityEngine.UI.InputField::UpdateCaretFromKeyboard()
extern void InputField_UpdateCaretFromKeyboard_m664E5EA17EAE912369D41DAA6D000EE43F797A18 (void);
// 0x000001DB System.Void UnityEngine.UI.InputField::LateUpdate()
extern void InputField_LateUpdate_m795829E897B395D1CD675CE422E6EC9ADDDCD3EE (void);
// 0x000001DC UnityEngine.Vector2 UnityEngine.UI.InputField::ScreenToLocal(UnityEngine.Vector2)
extern void InputField_ScreenToLocal_mE3824345E77E14F8F3D96E67BC2AF8D133350DF8 (void);
// 0x000001DD System.Int32 UnityEngine.UI.InputField::GetUnclampedCharacterLineFromPosition(UnityEngine.Vector2,UnityEngine.TextGenerator)
extern void InputField_GetUnclampedCharacterLineFromPosition_m0FFAA4A076B7A6A673FF90B7CF32C5BCF811CF09 (void);
// 0x000001DE System.Int32 UnityEngine.UI.InputField::GetCharacterIndexFromPosition(UnityEngine.Vector2)
extern void InputField_GetCharacterIndexFromPosition_m4680EC4C185A5B1BF978A6928013CA02F2F4E04B (void);
// 0x000001DF System.Boolean UnityEngine.UI.InputField::MayDrag(UnityEngine.EventSystems.PointerEventData)
extern void InputField_MayDrag_m646952F57A66908739FF2DB47553AAD0CC803BC6 (void);
// 0x000001E0 System.Void UnityEngine.UI.InputField::OnBeginDrag(UnityEngine.EventSystems.PointerEventData)
extern void InputField_OnBeginDrag_m496E54FBD1ADF8395DB80C51CC28DEDB5EA18B85 (void);
// 0x000001E1 System.Void UnityEngine.UI.InputField::OnDrag(UnityEngine.EventSystems.PointerEventData)
extern void InputField_OnDrag_m36347819AF7E4D0CFF3317C1DE45E884FD11029C (void);
// 0x000001E2 System.Collections.IEnumerator UnityEngine.UI.InputField::MouseDragOutsideRect(UnityEngine.EventSystems.PointerEventData)
extern void InputField_MouseDragOutsideRect_mAFFDDB6F3FA3722599CF2CDA7E0EEB57DAA9A257 (void);
// 0x000001E3 System.Void UnityEngine.UI.InputField::OnEndDrag(UnityEngine.EventSystems.PointerEventData)
extern void InputField_OnEndDrag_m5B9E0AE98C4B531C002E002EA64D6AB3536D8082 (void);
// 0x000001E4 System.Void UnityEngine.UI.InputField::OnPointerDown(UnityEngine.EventSystems.PointerEventData)
extern void InputField_OnPointerDown_m5639510FC7BCE98EFA490A85B198E7D4F18B4DCA (void);
// 0x000001E5 UnityEngine.UI.InputField/EditState UnityEngine.UI.InputField::KeyPressed(UnityEngine.Event)
extern void InputField_KeyPressed_m0F50321EEE506CDA75CCA02F9747FD65E5DF896C (void);
// 0x000001E6 System.Boolean UnityEngine.UI.InputField::IsValidChar(System.Char)
extern void InputField_IsValidChar_m05A915535E3D8DF3B7123F0BAE9BE3365106CE81 (void);
// 0x000001E7 System.Void UnityEngine.UI.InputField::ProcessEvent(UnityEngine.Event)
extern void InputField_ProcessEvent_m5F0E049EFBB30935E1A289C32FE2AF6D6856239D (void);
// 0x000001E8 System.Void UnityEngine.UI.InputField::OnUpdateSelected(UnityEngine.EventSystems.BaseEventData)
extern void InputField_OnUpdateSelected_mA7E87E9453287D7632A060B8BF4400657EB80092 (void);
// 0x000001E9 System.String UnityEngine.UI.InputField::GetSelectedString()
extern void InputField_GetSelectedString_m0BC31CC065E2258323259C646F0D33448914FCB2 (void);
// 0x000001EA System.Int32 UnityEngine.UI.InputField::FindtNextWordBegin()
extern void InputField_FindtNextWordBegin_m8FC97151F226C4CCCEC6B8BA72E47B16278D9D32 (void);
// 0x000001EB System.Void UnityEngine.UI.InputField::MoveRight(System.Boolean,System.Boolean)
extern void InputField_MoveRight_m02C718260771AED239B61770F1DB38E7AE266D7A (void);
// 0x000001EC System.Int32 UnityEngine.UI.InputField::FindtPrevWordBegin()
extern void InputField_FindtPrevWordBegin_m143AB3F7AE317D9F62A475A88E20E3A3F62D4A6D (void);
// 0x000001ED System.Void UnityEngine.UI.InputField::MoveLeft(System.Boolean,System.Boolean)
extern void InputField_MoveLeft_m0671A9AC1D833070233E3F966F8B00680D1E1FB3 (void);
// 0x000001EE System.Int32 UnityEngine.UI.InputField::DetermineCharacterLine(System.Int32,UnityEngine.TextGenerator)
extern void InputField_DetermineCharacterLine_m083FE6AA16263F9E407FDFB445DA35FB2DA86915 (void);
// 0x000001EF System.Int32 UnityEngine.UI.InputField::LineUpCharacterPosition(System.Int32,System.Boolean)
extern void InputField_LineUpCharacterPosition_mA0C6A2867A30798648A9833DB8F4B1C24E93FBFA (void);
// 0x000001F0 System.Int32 UnityEngine.UI.InputField::LineDownCharacterPosition(System.Int32,System.Boolean)
extern void InputField_LineDownCharacterPosition_mBCC9AA30EC94EA0EF724BDB6C78D2E9952830BFA (void);
// 0x000001F1 System.Void UnityEngine.UI.InputField::MoveDown(System.Boolean)
extern void InputField_MoveDown_m99F896D1A8F784A17BDA364D01D87A2F3CF313E8 (void);
// 0x000001F2 System.Void UnityEngine.UI.InputField::MoveDown(System.Boolean,System.Boolean)
extern void InputField_MoveDown_m791D171F5C4611A775AF835297E5CB4505FC3E9B (void);
// 0x000001F3 System.Void UnityEngine.UI.InputField::MoveUp(System.Boolean)
extern void InputField_MoveUp_mC87D4402849F1E530735D6D885CEE855C63FC372 (void);
// 0x000001F4 System.Void UnityEngine.UI.InputField::MoveUp(System.Boolean,System.Boolean)
extern void InputField_MoveUp_mAC099D941C00DF9BE47A1C55D43C9CF7B9CD4304 (void);
// 0x000001F5 System.Void UnityEngine.UI.InputField::Delete()
extern void InputField_Delete_mF2790369AD8584F70D359D229B596A909ACDDA97 (void);
// 0x000001F6 System.Void UnityEngine.UI.InputField::ForwardSpace()
extern void InputField_ForwardSpace_m3063562008D859408F60DA255DE7C2AA324DA0E0 (void);
// 0x000001F7 System.Void UnityEngine.UI.InputField::Backspace()
extern void InputField_Backspace_m4DF759C80B208BCD552F1570E00FE8771D5C9D0C (void);
// 0x000001F8 System.Void UnityEngine.UI.InputField::Insert(System.Char)
extern void InputField_Insert_mF32CF6706C7BE2D9E8B9510A17C1FA3936BE50BA (void);
// 0x000001F9 System.Void UnityEngine.UI.InputField::UpdateTouchKeyboardFromEditChanges()
extern void InputField_UpdateTouchKeyboardFromEditChanges_m2975A5DA3CEBE635175C45CB13FCAA38DC24BC45 (void);
// 0x000001FA System.Void UnityEngine.UI.InputField::SendOnValueChangedAndUpdateLabel()
extern void InputField_SendOnValueChangedAndUpdateLabel_m830267D28C97DA267F51DDE82E36C446E2AB1A8C (void);
// 0x000001FB System.Void UnityEngine.UI.InputField::SendOnValueChanged()
extern void InputField_SendOnValueChanged_m6E3270644F5990D04EC0F07E6E0314F9B5085DA4 (void);
// 0x000001FC System.Void UnityEngine.UI.InputField::SendOnSubmit()
extern void InputField_SendOnSubmit_m61B6B9C63D10D04EB57F6E7EC4305B5E9470F929 (void);
// 0x000001FD System.Void UnityEngine.UI.InputField::Append(System.String)
extern void InputField_Append_m636E533087317BDD9C392E4BCD27C2F735DC87A6 (void);
// 0x000001FE System.Void UnityEngine.UI.InputField::Append(System.Char)
extern void InputField_Append_m0405A57105B52A2BBC5CA6B06E804975B3343CE2 (void);
// 0x000001FF System.Void UnityEngine.UI.InputField::UpdateLabel()
extern void InputField_UpdateLabel_mF570FC1863A271F7B69C1701711F57BEC7E1633A (void);
// 0x00000200 System.Boolean UnityEngine.UI.InputField::IsSelectionVisible()
extern void InputField_IsSelectionVisible_m448CAEA19B6E9048F1FB23EB4092F9362A996960 (void);
// 0x00000201 System.Int32 UnityEngine.UI.InputField::GetLineStartPosition(UnityEngine.TextGenerator,System.Int32)
extern void InputField_GetLineStartPosition_mD6F2D28455348713D7674FB7115F03C006FAE126 (void);
// 0x00000202 System.Int32 UnityEngine.UI.InputField::GetLineEndPosition(UnityEngine.TextGenerator,System.Int32)
extern void InputField_GetLineEndPosition_mE5634E70FE765F25C7DB1F9D20393B667358CF27 (void);
// 0x00000203 System.Void UnityEngine.UI.InputField::SetDrawRangeToContainCaretPosition(System.Int32)
extern void InputField_SetDrawRangeToContainCaretPosition_mC1D83691E0439C1629302420F00F390D6FFF72EE (void);
// 0x00000204 System.Void UnityEngine.UI.InputField::ForceLabelUpdate()
extern void InputField_ForceLabelUpdate_mCD4242F19DAA068A57D8A49FB45DBF20578C729C (void);
// 0x00000205 System.Void UnityEngine.UI.InputField::MarkGeometryAsDirty()
extern void InputField_MarkGeometryAsDirty_mE510B52A8F4814750C7F0FAF012E2735507DD5ED (void);
// 0x00000206 System.Void UnityEngine.UI.InputField::Rebuild(UnityEngine.UI.CanvasUpdate)
extern void InputField_Rebuild_mF5EA85AF39F59C0C614D91461FA6F580CD429C73 (void);
// 0x00000207 System.Void UnityEngine.UI.InputField::LayoutComplete()
extern void InputField_LayoutComplete_mC597409B0AAC5B2801BA7C59073955C58B21CF71 (void);
// 0x00000208 System.Void UnityEngine.UI.InputField::GraphicUpdateComplete()
extern void InputField_GraphicUpdateComplete_m03176EFF6E8BB4AE382CA905E497D237FAAF1409 (void);
// 0x00000209 System.Void UnityEngine.UI.InputField::UpdateGeometry()
extern void InputField_UpdateGeometry_mE0A082526B600442DF0BB1AD683BA18253E4BD17 (void);
// 0x0000020A System.Void UnityEngine.UI.InputField::AssignPositioningIfNeeded()
extern void InputField_AssignPositioningIfNeeded_mA3B81E438571DD2FF198C6D94D5337410FE4E2DD (void);
// 0x0000020B System.Void UnityEngine.UI.InputField::OnFillVBO(UnityEngine.Mesh)
extern void InputField_OnFillVBO_mF25F719237FB66905CD30D83A171E0C1715D5BBD (void);
// 0x0000020C System.Void UnityEngine.UI.InputField::GenerateCaret(UnityEngine.UI.VertexHelper,UnityEngine.Vector2)
extern void InputField_GenerateCaret_m87A1C4E932C7BC4873FE30FB7D5D82B4CC1920DB (void);
// 0x0000020D System.Void UnityEngine.UI.InputField::CreateCursorVerts()
extern void InputField_CreateCursorVerts_m8569AF37F2846E937A8D6250B83BB17A1BE4D533 (void);
// 0x0000020E System.Void UnityEngine.UI.InputField::GenerateHighlight(UnityEngine.UI.VertexHelper,UnityEngine.Vector2)
extern void InputField_GenerateHighlight_m024AC6454AAC108CEF5FE3D13DE10796AC402212 (void);
// 0x0000020F System.Char UnityEngine.UI.InputField::Validate(System.String,System.Int32,System.Char)
extern void InputField_Validate_mF6336A3F55070D1DB07C078DF6895EE99FC5A675 (void);
// 0x00000210 System.Void UnityEngine.UI.InputField::ActivateInputField()
extern void InputField_ActivateInputField_m97B9F9C150E4463DE299D1F8BC046CCA826064D1 (void);
// 0x00000211 System.Void UnityEngine.UI.InputField::ActivateInputFieldInternal()
extern void InputField_ActivateInputFieldInternal_m04421DBA5D63B5516A25CA01E22F649638EF1BD2 (void);
// 0x00000212 System.Void UnityEngine.UI.InputField::OnSelect(UnityEngine.EventSystems.BaseEventData)
extern void InputField_OnSelect_mC01B78E1C2EDAAD2A3573BD6437DD128BBD24E8B (void);
// 0x00000213 System.Void UnityEngine.UI.InputField::OnPointerClick(UnityEngine.EventSystems.PointerEventData)
extern void InputField_OnPointerClick_m8036C2B156FF0374258973655BD1F58DC2F10B6F (void);
// 0x00000214 System.Void UnityEngine.UI.InputField::DeactivateInputField()
extern void InputField_DeactivateInputField_m5560C45B3B91280675019F297463FBCED7A90C6C (void);
// 0x00000215 System.Void UnityEngine.UI.InputField::OnDeselect(UnityEngine.EventSystems.BaseEventData)
extern void InputField_OnDeselect_mAFD183BB78EEA3BB585304DA1AC42A671100F97C (void);
// 0x00000216 System.Void UnityEngine.UI.InputField::OnSubmit(UnityEngine.EventSystems.BaseEventData)
extern void InputField_OnSubmit_mF999728A0343ACD4DB69B23BB2A349A79EC70B73 (void);
// 0x00000217 System.Void UnityEngine.UI.InputField::EnforceContentType()
extern void InputField_EnforceContentType_mC4C55F7A66FC993DB252931E0E15FB9F44880027 (void);
// 0x00000218 System.Void UnityEngine.UI.InputField::EnforceTextHOverflow()
extern void InputField_EnforceTextHOverflow_m051A4A818627D6D39E78247D7C90F06E993FEC98 (void);
// 0x00000219 System.Void UnityEngine.UI.InputField::SetToCustomIfContentTypeIsNot(UnityEngine.UI.InputField/ContentType[])
extern void InputField_SetToCustomIfContentTypeIsNot_m33A8EA45715BE8A35F72C635323CF83F1C6F50DF (void);
// 0x0000021A System.Void UnityEngine.UI.InputField::SetToCustom()
extern void InputField_SetToCustom_mC31AC959C1A0D93521A79BB1E8B4FC165DF5A8F8 (void);
// 0x0000021B System.Void UnityEngine.UI.InputField::DoStateTransition(UnityEngine.UI.Selectable/SelectionState,System.Boolean)
extern void InputField_DoStateTransition_m2A4EC0AFC46330002ED61B4DDD3FC18F7436AD99 (void);
// 0x0000021C System.Void UnityEngine.UI.InputField::CalculateLayoutInputHorizontal()
extern void InputField_CalculateLayoutInputHorizontal_m12DE84494E7FDB173D18085440A531882B52B66E (void);
// 0x0000021D System.Void UnityEngine.UI.InputField::CalculateLayoutInputVertical()
extern void InputField_CalculateLayoutInputVertical_m26E12F39F97EA7422A8EE82F137B9EE60C47F3D7 (void);
// 0x0000021E System.Single UnityEngine.UI.InputField::get_minWidth()
extern void InputField_get_minWidth_m81FE96651BA4719B847DE7225830E7EEB3651639 (void);
// 0x0000021F System.Single UnityEngine.UI.InputField::get_preferredWidth()
extern void InputField_get_preferredWidth_m3898E4B9FD50DCC8E0E81621FC23E4550CEA93EC (void);
// 0x00000220 System.Single UnityEngine.UI.InputField::get_flexibleWidth()
extern void InputField_get_flexibleWidth_mF5D2ACD28D789C2ACD9EE69FF45EF21A2A91C878 (void);
// 0x00000221 System.Single UnityEngine.UI.InputField::get_minHeight()
extern void InputField_get_minHeight_m8A4D5C88D7CEE000CB00E49A057D09349EBEFC5C (void);
// 0x00000222 System.Single UnityEngine.UI.InputField::get_preferredHeight()
extern void InputField_get_preferredHeight_mC4813F206B87CC7C97715341979DA9766CB7D083 (void);
// 0x00000223 System.Single UnityEngine.UI.InputField::get_flexibleHeight()
extern void InputField_get_flexibleHeight_mB510F9D956944F8894C7E487FD9C60B54402A7CD (void);
// 0x00000224 System.Int32 UnityEngine.UI.InputField::get_layoutPriority()
extern void InputField_get_layoutPriority_mB177909CAC82C3B460D4807A24D2277D0B7A8242 (void);
// 0x00000225 System.Void UnityEngine.UI.InputField::.cctor()
extern void InputField__cctor_m135EE98A98FE68E18B474E15E09D83291C97173B (void);
// 0x00000226 UnityEngine.Transform UnityEngine.UI.InputField::UnityEngine.UI.ICanvasElement.get_transform()
extern void InputField_UnityEngine_UI_ICanvasElement_get_transform_mE65406876D4DB002FAE227E3772D9FF805964361 (void);
// 0x00000227 System.Void UnityEngine.UI.InputField/OnValidateInput::.ctor(System.Object,System.IntPtr)
extern void OnValidateInput__ctor_m79176985D76F3F168B4682FDE46B33C400806149 (void);
// 0x00000228 System.Char UnityEngine.UI.InputField/OnValidateInput::Invoke(System.String,System.Int32,System.Char)
extern void OnValidateInput_Invoke_mFD5B2C2FE9905B863CB61FC6FC6B1D20ED50FDBF (void);
// 0x00000229 System.IAsyncResult UnityEngine.UI.InputField/OnValidateInput::BeginInvoke(System.String,System.Int32,System.Char,System.AsyncCallback,System.Object)
extern void OnValidateInput_BeginInvoke_m3B34E2413C3D091F4A38582A8F786705F1CD4FED (void);
// 0x0000022A System.Char UnityEngine.UI.InputField/OnValidateInput::EndInvoke(System.IAsyncResult)
extern void OnValidateInput_EndInvoke_m12D40F5D1A80B60996DC349AD5B3CC3711DAA517 (void);
// 0x0000022B System.Void UnityEngine.UI.InputField/SubmitEvent::.ctor()
extern void SubmitEvent__ctor_m32C23EA2D0183736A8039A9B638734819D760CE4 (void);
// 0x0000022C System.Void UnityEngine.UI.InputField/OnChangeEvent::.ctor()
extern void OnChangeEvent__ctor_mB146DCA915176957A9B5CF48F08FF1EF64E44F5F (void);
// 0x0000022D System.Void UnityEngine.UI.InputField/<CaretBlink>d__165::.ctor(System.Int32)
extern void U3CCaretBlinkU3Ed__165__ctor_mD52F27AA6D0FCEFB97DCD816060515DAD011332C (void);
// 0x0000022E System.Void UnityEngine.UI.InputField/<CaretBlink>d__165::System.IDisposable.Dispose()
extern void U3CCaretBlinkU3Ed__165_System_IDisposable_Dispose_m286379FC217519BE0837F50C7D73AA060EAE6765 (void);
// 0x0000022F System.Boolean UnityEngine.UI.InputField/<CaretBlink>d__165::MoveNext()
extern void U3CCaretBlinkU3Ed__165_MoveNext_mF45CF25F768884A0454D685E5E98DE51A47B5F1A (void);
// 0x00000230 System.Object UnityEngine.UI.InputField/<CaretBlink>d__165::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CCaretBlinkU3Ed__165_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC74CAEF32DC2E5A2171A03E704E0F5FBF7E6844C (void);
// 0x00000231 System.Void UnityEngine.UI.InputField/<CaretBlink>d__165::System.Collections.IEnumerator.Reset()
extern void U3CCaretBlinkU3Ed__165_System_Collections_IEnumerator_Reset_mAB5C4A532AFA5EBF4932537C737DD7805D41DA31 (void);
// 0x00000232 System.Object UnityEngine.UI.InputField/<CaretBlink>d__165::System.Collections.IEnumerator.get_Current()
extern void U3CCaretBlinkU3Ed__165_System_Collections_IEnumerator_get_Current_m298756EDEFB19F4E0AB9764AA97D561542FBB931 (void);
// 0x00000233 System.Void UnityEngine.UI.InputField/<MouseDragOutsideRect>d__187::.ctor(System.Int32)
extern void U3CMouseDragOutsideRectU3Ed__187__ctor_m4F54B77E13F77986DE053D7C835ECB3C1AD2EAD9 (void);
// 0x00000234 System.Void UnityEngine.UI.InputField/<MouseDragOutsideRect>d__187::System.IDisposable.Dispose()
extern void U3CMouseDragOutsideRectU3Ed__187_System_IDisposable_Dispose_m304F008C76437BC5CB56EEE617BD6657F326E532 (void);
// 0x00000235 System.Boolean UnityEngine.UI.InputField/<MouseDragOutsideRect>d__187::MoveNext()
extern void U3CMouseDragOutsideRectU3Ed__187_MoveNext_m5289AF98EFB9C29E6E07398CB22E1706DD900170 (void);
// 0x00000236 System.Object UnityEngine.UI.InputField/<MouseDragOutsideRect>d__187::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMouseDragOutsideRectU3Ed__187_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9FCCB5B7180351583F40846D4E09DE98B98FE66B (void);
// 0x00000237 System.Void UnityEngine.UI.InputField/<MouseDragOutsideRect>d__187::System.Collections.IEnumerator.Reset()
extern void U3CMouseDragOutsideRectU3Ed__187_System_Collections_IEnumerator_Reset_m455B41A0D9F03DD2DC28021D10E73E4AB1FE0F72 (void);
// 0x00000238 System.Object UnityEngine.UI.InputField/<MouseDragOutsideRect>d__187::System.Collections.IEnumerator.get_Current()
extern void U3CMouseDragOutsideRectU3Ed__187_System_Collections_IEnumerator_get_Current_mF099543528C346A5D23B19CC6EE2D9CE73D9BBFB (void);
// 0x00000239 UnityEngine.UI.AspectRatioFitter/AspectMode UnityEngine.UI.AspectRatioFitter::get_aspectMode()
extern void AspectRatioFitter_get_aspectMode_m66C318F8E4ECBABB190F32F9FE9A360DCEF8306F (void);
// 0x0000023A System.Void UnityEngine.UI.AspectRatioFitter::set_aspectMode(UnityEngine.UI.AspectRatioFitter/AspectMode)
extern void AspectRatioFitter_set_aspectMode_m810BF480F4C07C609339AB7546445A29881103A0 (void);
// 0x0000023B System.Single UnityEngine.UI.AspectRatioFitter::get_aspectRatio()
extern void AspectRatioFitter_get_aspectRatio_m44B5119268774856091D7711325C2AB81F1D67B8 (void);
// 0x0000023C System.Void UnityEngine.UI.AspectRatioFitter::set_aspectRatio(System.Single)
extern void AspectRatioFitter_set_aspectRatio_mA12F8575EB30A7B2E734F188169FA7F91192BB67 (void);
// 0x0000023D UnityEngine.RectTransform UnityEngine.UI.AspectRatioFitter::get_rectTransform()
extern void AspectRatioFitter_get_rectTransform_m8764D78A30C6496122E08EE1BAD137B8B6B14B57 (void);
// 0x0000023E System.Void UnityEngine.UI.AspectRatioFitter::.ctor()
extern void AspectRatioFitter__ctor_mA790862A4DCA8878F2E7E8AAF82AF8BB7529FFAD (void);
// 0x0000023F System.Void UnityEngine.UI.AspectRatioFitter::OnEnable()
extern void AspectRatioFitter_OnEnable_m38A96D4F9AC1143D68CA9E5E71E738392CCB8922 (void);
// 0x00000240 System.Void UnityEngine.UI.AspectRatioFitter::Start()
extern void AspectRatioFitter_Start_m18B1282D4D9AFD417CF46B9232AEB2979C27F27D (void);
// 0x00000241 System.Void UnityEngine.UI.AspectRatioFitter::OnDisable()
extern void AspectRatioFitter_OnDisable_m2C7C486ECD1DCE0A75498620CC597995619F5545 (void);
// 0x00000242 System.Void UnityEngine.UI.AspectRatioFitter::OnTransformParentChanged()
extern void AspectRatioFitter_OnTransformParentChanged_m37F22AA99DEDC02E14217AFCB33A821A7C4E1AA2 (void);
// 0x00000243 System.Void UnityEngine.UI.AspectRatioFitter::Update()
extern void AspectRatioFitter_Update_m3E413128D33AB67A7D08F12D9E8FD3B2F67AAF14 (void);
// 0x00000244 System.Void UnityEngine.UI.AspectRatioFitter::OnRectTransformDimensionsChange()
extern void AspectRatioFitter_OnRectTransformDimensionsChange_mA05F29955CFC331A079CE71C19B6D0D14A0080E1 (void);
// 0x00000245 System.Void UnityEngine.UI.AspectRatioFitter::UpdateRect()
extern void AspectRatioFitter_UpdateRect_m139CA7294F60E8CF54B8CCCA846059D56BCE972D (void);
// 0x00000246 System.Single UnityEngine.UI.AspectRatioFitter::GetSizeDeltaToProduceSize(System.Single,System.Int32)
extern void AspectRatioFitter_GetSizeDeltaToProduceSize_m5262FB8F1C6DBB9F3E7C4D52790420EA5267497C (void);
// 0x00000247 UnityEngine.Vector2 UnityEngine.UI.AspectRatioFitter::GetParentSize()
extern void AspectRatioFitter_GetParentSize_mB88924D7ED70E1DA5C59263D15D680C545EC17A9 (void);
// 0x00000248 System.Void UnityEngine.UI.AspectRatioFitter::SetLayoutHorizontal()
extern void AspectRatioFitter_SetLayoutHorizontal_m54B187C0FD7273A1606BA3041BCDD7B56BAEB764 (void);
// 0x00000249 System.Void UnityEngine.UI.AspectRatioFitter::SetLayoutVertical()
extern void AspectRatioFitter_SetLayoutVertical_mAFEA45A75696F6C078C2486E2ED1480A7D88B744 (void);
// 0x0000024A System.Void UnityEngine.UI.AspectRatioFitter::SetDirty()
extern void AspectRatioFitter_SetDirty_mB456E2343490B4488A973F06D2F978E1724B301F (void);
// 0x0000024B System.Boolean UnityEngine.UI.AspectRatioFitter::IsComponentValidOnObject()
extern void AspectRatioFitter_IsComponentValidOnObject_m3A52897DC8A8ED421D0E8258901D39838A151F4E (void);
// 0x0000024C System.Boolean UnityEngine.UI.AspectRatioFitter::IsAspectModeValid()
extern void AspectRatioFitter_IsAspectModeValid_mE2E56DEE17B7C09F539A24B46CEFBFDF352AE25E (void);
// 0x0000024D System.Boolean UnityEngine.UI.AspectRatioFitter::DoesParentExists()
extern void AspectRatioFitter_DoesParentExists_m5F215E68AA01868AEF6BD74C7976ADB424AB7D21 (void);
// 0x0000024E UnityEngine.UI.CanvasScaler/ScaleMode UnityEngine.UI.CanvasScaler::get_uiScaleMode()
extern void CanvasScaler_get_uiScaleMode_m6B2801FE9E3816E01F2B85F46BB554695B6FC7A5 (void);
// 0x0000024F System.Void UnityEngine.UI.CanvasScaler::set_uiScaleMode(UnityEngine.UI.CanvasScaler/ScaleMode)
extern void CanvasScaler_set_uiScaleMode_m75D0DFC352DB3934976F081732AB03CFB7565568 (void);
// 0x00000250 System.Single UnityEngine.UI.CanvasScaler::get_referencePixelsPerUnit()
extern void CanvasScaler_get_referencePixelsPerUnit_m750F6B1AA54243A8FF73E3F94894F76FFF82999C (void);
// 0x00000251 System.Void UnityEngine.UI.CanvasScaler::set_referencePixelsPerUnit(System.Single)
extern void CanvasScaler_set_referencePixelsPerUnit_m69FD2D7C21EA8FAF9E639F8D6D4FC3858AF9F926 (void);
// 0x00000252 System.Single UnityEngine.UI.CanvasScaler::get_scaleFactor()
extern void CanvasScaler_get_scaleFactor_mC9E5B58CB6C5B97934900BE2AD20BB662A14829D (void);
// 0x00000253 System.Void UnityEngine.UI.CanvasScaler::set_scaleFactor(System.Single)
extern void CanvasScaler_set_scaleFactor_mB0976DB810D4AB2410D7F50CD39BC75E1A143197 (void);
// 0x00000254 UnityEngine.Vector2 UnityEngine.UI.CanvasScaler::get_referenceResolution()
extern void CanvasScaler_get_referenceResolution_mC88C9BB9FAB47FEDAFB14ECC57620870E965F542 (void);
// 0x00000255 System.Void UnityEngine.UI.CanvasScaler::set_referenceResolution(UnityEngine.Vector2)
extern void CanvasScaler_set_referenceResolution_mD3200E76103B16C8064B6E48595EFC9C5BDCB719 (void);
// 0x00000256 UnityEngine.UI.CanvasScaler/ScreenMatchMode UnityEngine.UI.CanvasScaler::get_screenMatchMode()
extern void CanvasScaler_get_screenMatchMode_m3AF1E5875BA22777EC08A70E308EFF74FE5F8C36 (void);
// 0x00000257 System.Void UnityEngine.UI.CanvasScaler::set_screenMatchMode(UnityEngine.UI.CanvasScaler/ScreenMatchMode)
extern void CanvasScaler_set_screenMatchMode_m2A140B7ABD7510CFA4130EFF84E1AC085C25351E (void);
// 0x00000258 System.Single UnityEngine.UI.CanvasScaler::get_matchWidthOrHeight()
extern void CanvasScaler_get_matchWidthOrHeight_mB0DF4FAA9AFC23C53DF212CC4092775162D59B42 (void);
// 0x00000259 System.Void UnityEngine.UI.CanvasScaler::set_matchWidthOrHeight(System.Single)
extern void CanvasScaler_set_matchWidthOrHeight_mE55542B64CA909A849FC57EEE1DD5E4C7447C8C8 (void);
// 0x0000025A UnityEngine.UI.CanvasScaler/Unit UnityEngine.UI.CanvasScaler::get_physicalUnit()
extern void CanvasScaler_get_physicalUnit_mAC6375D4B4AD8F24ED9CB9F5AD630A6903D4FEDA (void);
// 0x0000025B System.Void UnityEngine.UI.CanvasScaler::set_physicalUnit(UnityEngine.UI.CanvasScaler/Unit)
extern void CanvasScaler_set_physicalUnit_mA17EFA8B9034AC52A5F28B7A974412BB11078D4B (void);
// 0x0000025C System.Single UnityEngine.UI.CanvasScaler::get_fallbackScreenDPI()
extern void CanvasScaler_get_fallbackScreenDPI_mF5310101362D88C5E23A7CB83987279682C8BDE7 (void);
// 0x0000025D System.Void UnityEngine.UI.CanvasScaler::set_fallbackScreenDPI(System.Single)
extern void CanvasScaler_set_fallbackScreenDPI_m6E9B2C54C3E3498E2EE38791241C4859875D43F5 (void);
// 0x0000025E System.Single UnityEngine.UI.CanvasScaler::get_defaultSpriteDPI()
extern void CanvasScaler_get_defaultSpriteDPI_m0919438D2993679F002EC4B723E4E4BA4880B2B6 (void);
// 0x0000025F System.Void UnityEngine.UI.CanvasScaler::set_defaultSpriteDPI(System.Single)
extern void CanvasScaler_set_defaultSpriteDPI_mD9CF4AF444F2CCAD4C377A05FA6324DC1EB4676F (void);
// 0x00000260 System.Single UnityEngine.UI.CanvasScaler::get_dynamicPixelsPerUnit()
extern void CanvasScaler_get_dynamicPixelsPerUnit_m5CD7C22018C45A1283DE516361671E30FB2AAE6B (void);
// 0x00000261 System.Void UnityEngine.UI.CanvasScaler::set_dynamicPixelsPerUnit(System.Single)
extern void CanvasScaler_set_dynamicPixelsPerUnit_m9C1A6BAAB42388B770D93E9DF28C01CB76F8DE99 (void);
// 0x00000262 System.Void UnityEngine.UI.CanvasScaler::.ctor()
extern void CanvasScaler__ctor_m163FF7D442D68D88E593709707770F3D52FC61B0 (void);
// 0x00000263 System.Void UnityEngine.UI.CanvasScaler::OnEnable()
extern void CanvasScaler_OnEnable_m8CA19CA8AAEAA0A7B9E045C8C327487C1DB2FBAA (void);
// 0x00000264 System.Void UnityEngine.UI.CanvasScaler::Canvas_preWillRenderCanvases()
extern void CanvasScaler_Canvas_preWillRenderCanvases_mFF22B98C4E3D799799F88F36F8853C6782AE2526 (void);
// 0x00000265 System.Void UnityEngine.UI.CanvasScaler::OnDisable()
extern void CanvasScaler_OnDisable_m025E1C4B1629A29EA8C1C0A101C76973E8DCA8FF (void);
// 0x00000266 System.Void UnityEngine.UI.CanvasScaler::Handle()
extern void CanvasScaler_Handle_m72CD0853B60F4AEF304B81A91173BA08B0F0C555 (void);
// 0x00000267 System.Void UnityEngine.UI.CanvasScaler::HandleWorldCanvas()
extern void CanvasScaler_HandleWorldCanvas_m92E81FE6FA13E6A3D500F79FD1E9645CEDF91E41 (void);
// 0x00000268 System.Void UnityEngine.UI.CanvasScaler::HandleConstantPixelSize()
extern void CanvasScaler_HandleConstantPixelSize_mBCBF7101336FEAFE8DD14FE9EFFEB86D96CD70E6 (void);
// 0x00000269 System.Void UnityEngine.UI.CanvasScaler::HandleScaleWithScreenSize()
extern void CanvasScaler_HandleScaleWithScreenSize_m0182771F235A98D2733E568384ECCF77EA4A0973 (void);
// 0x0000026A System.Void UnityEngine.UI.CanvasScaler::HandleConstantPhysicalSize()
extern void CanvasScaler_HandleConstantPhysicalSize_m042683E0BFCE47F0AB6457A07FDC74435350D492 (void);
// 0x0000026B System.Void UnityEngine.UI.CanvasScaler::SetScaleFactor(System.Single)
extern void CanvasScaler_SetScaleFactor_mE8817AA99B5DB531BFB3304704B53DA2D848D7E4 (void);
// 0x0000026C System.Void UnityEngine.UI.CanvasScaler::SetReferencePixelsPerUnit(System.Single)
extern void CanvasScaler_SetReferencePixelsPerUnit_m34EE2290639A159BE66A23C7B7BC7AE6FA7B83C7 (void);
// 0x0000026D UnityEngine.UI.ContentSizeFitter/FitMode UnityEngine.UI.ContentSizeFitter::get_horizontalFit()
extern void ContentSizeFitter_get_horizontalFit_mCC5F1FB35E99093EDF9E2A8031D6D40485654DED (void);
// 0x0000026E System.Void UnityEngine.UI.ContentSizeFitter::set_horizontalFit(UnityEngine.UI.ContentSizeFitter/FitMode)
extern void ContentSizeFitter_set_horizontalFit_m2614655A0C802BD8FCE5B5C68642DC30EF48D678 (void);
// 0x0000026F UnityEngine.UI.ContentSizeFitter/FitMode UnityEngine.UI.ContentSizeFitter::get_verticalFit()
extern void ContentSizeFitter_get_verticalFit_mC1AA72ACD85DBFB0A90DE3B4DB636A12DFB27AC3 (void);
// 0x00000270 System.Void UnityEngine.UI.ContentSizeFitter::set_verticalFit(UnityEngine.UI.ContentSizeFitter/FitMode)
extern void ContentSizeFitter_set_verticalFit_m03290C9732B5B9E014C01DC21DAE634C903B30B7 (void);
// 0x00000271 UnityEngine.RectTransform UnityEngine.UI.ContentSizeFitter::get_rectTransform()
extern void ContentSizeFitter_get_rectTransform_mF90F07D2624D24638CD10040BAFFB2F0DE04CB9A (void);
// 0x00000272 System.Void UnityEngine.UI.ContentSizeFitter::.ctor()
extern void ContentSizeFitter__ctor_m29659B73718BD0188F77D029C0D934672B3176ED (void);
// 0x00000273 System.Void UnityEngine.UI.ContentSizeFitter::OnEnable()
extern void ContentSizeFitter_OnEnable_mF6ED50D7212B491EA29464AC4C6F652CB3D063F6 (void);
// 0x00000274 System.Void UnityEngine.UI.ContentSizeFitter::OnDisable()
extern void ContentSizeFitter_OnDisable_m3CAA304211378CA2113775A6211EAC36656DFCEB (void);
// 0x00000275 System.Void UnityEngine.UI.ContentSizeFitter::OnRectTransformDimensionsChange()
extern void ContentSizeFitter_OnRectTransformDimensionsChange_m6C5066B0EBC0316EF2FFE3B7E265D15D028E3B38 (void);
// 0x00000276 System.Void UnityEngine.UI.ContentSizeFitter::HandleSelfFittingAlongAxis(System.Int32)
extern void ContentSizeFitter_HandleSelfFittingAlongAxis_mBDAA1B648DCF90F864E232A99D1039D58F7F6BA9 (void);
// 0x00000277 System.Void UnityEngine.UI.ContentSizeFitter::SetLayoutHorizontal()
extern void ContentSizeFitter_SetLayoutHorizontal_mDF1737C0F62A8810857BA399E216BAC530D8611A (void);
// 0x00000278 System.Void UnityEngine.UI.ContentSizeFitter::SetLayoutVertical()
extern void ContentSizeFitter_SetLayoutVertical_m2BE1A92C2CC913BC359F2861A77A9E167AF0FD34 (void);
// 0x00000279 System.Void UnityEngine.UI.ContentSizeFitter::SetDirty()
extern void ContentSizeFitter_SetDirty_mCEE7380AA0EDF47B90FF1D0962ED6069718C65AC (void);
// 0x0000027A UnityEngine.UI.GridLayoutGroup/Corner UnityEngine.UI.GridLayoutGroup::get_startCorner()
extern void GridLayoutGroup_get_startCorner_m7B8BCE61E4A7CA9895A69610A9660EB4FF9B01EF (void);
// 0x0000027B System.Void UnityEngine.UI.GridLayoutGroup::set_startCorner(UnityEngine.UI.GridLayoutGroup/Corner)
extern void GridLayoutGroup_set_startCorner_m8CC0C6A40ED0BDD71B90B9FA1EF0536699AF6939 (void);
// 0x0000027C UnityEngine.UI.GridLayoutGroup/Axis UnityEngine.UI.GridLayoutGroup::get_startAxis()
extern void GridLayoutGroup_get_startAxis_m1CCE88FBF37EA3DA85F16679DBB39FF01ED675F9 (void);
// 0x0000027D System.Void UnityEngine.UI.GridLayoutGroup::set_startAxis(UnityEngine.UI.GridLayoutGroup/Axis)
extern void GridLayoutGroup_set_startAxis_m70A706B56A1A5AC2B513DDC7DA6A8633901819E7 (void);
// 0x0000027E UnityEngine.Vector2 UnityEngine.UI.GridLayoutGroup::get_cellSize()
extern void GridLayoutGroup_get_cellSize_m8F6B1D095560E6DAF3620280D00458A333C2C9DE (void);
// 0x0000027F System.Void UnityEngine.UI.GridLayoutGroup::set_cellSize(UnityEngine.Vector2)
extern void GridLayoutGroup_set_cellSize_m61514081AED88038DC0271BD20DCAB3D75E6A839 (void);
// 0x00000280 UnityEngine.Vector2 UnityEngine.UI.GridLayoutGroup::get_spacing()
extern void GridLayoutGroup_get_spacing_m6FBE62A17EB67001AECC9321F3BFC116749F55F0 (void);
// 0x00000281 System.Void UnityEngine.UI.GridLayoutGroup::set_spacing(UnityEngine.Vector2)
extern void GridLayoutGroup_set_spacing_m386B56A802B6EBE7EC5B43CEFB8D1355322939B3 (void);
// 0x00000282 UnityEngine.UI.GridLayoutGroup/Constraint UnityEngine.UI.GridLayoutGroup::get_constraint()
extern void GridLayoutGroup_get_constraint_mFA42B1E79F0E43DE5BBEC9D2FDA67AF680002CA3 (void);
// 0x00000283 System.Void UnityEngine.UI.GridLayoutGroup::set_constraint(UnityEngine.UI.GridLayoutGroup/Constraint)
extern void GridLayoutGroup_set_constraint_m4363B759F362D1A718743CF430A9A1D1604056AC (void);
// 0x00000284 System.Int32 UnityEngine.UI.GridLayoutGroup::get_constraintCount()
extern void GridLayoutGroup_get_constraintCount_m71979F3F937B006FC91512ED3C36345A66CE8195 (void);
// 0x00000285 System.Void UnityEngine.UI.GridLayoutGroup::set_constraintCount(System.Int32)
extern void GridLayoutGroup_set_constraintCount_mDA9246D502A94EAADE8554BEE7535649BC6995A6 (void);
// 0x00000286 System.Void UnityEngine.UI.GridLayoutGroup::.ctor()
extern void GridLayoutGroup__ctor_mF7D4849C233DD54D341F49C69F54BCAB76A53967 (void);
// 0x00000287 System.Void UnityEngine.UI.GridLayoutGroup::CalculateLayoutInputHorizontal()
extern void GridLayoutGroup_CalculateLayoutInputHorizontal_mDEBF6BB5ABC44910FB07523C7E990AB50DA14B41 (void);
// 0x00000288 System.Void UnityEngine.UI.GridLayoutGroup::CalculateLayoutInputVertical()
extern void GridLayoutGroup_CalculateLayoutInputVertical_m810E7C656E03603A84AB66EA860C0B8AF8263638 (void);
// 0x00000289 System.Void UnityEngine.UI.GridLayoutGroup::SetLayoutHorizontal()
extern void GridLayoutGroup_SetLayoutHorizontal_m6171F3C7990EC2506EDBC465124716A0F7CD3EA4 (void);
// 0x0000028A System.Void UnityEngine.UI.GridLayoutGroup::SetLayoutVertical()
extern void GridLayoutGroup_SetLayoutVertical_m8A1A0B2B81D83339119D8E1219FF92CA4036D6C6 (void);
// 0x0000028B System.Void UnityEngine.UI.GridLayoutGroup::SetCellsAlongAxis(System.Int32)
extern void GridLayoutGroup_SetCellsAlongAxis_mEE61F59F97D16F5F692C79E7CFB7B85BC0380209 (void);
// 0x0000028C System.Void UnityEngine.UI.HorizontalLayoutGroup::.ctor()
extern void HorizontalLayoutGroup__ctor_m5BD32654793C99610B165A9A926B495952B03853 (void);
// 0x0000028D System.Void UnityEngine.UI.HorizontalLayoutGroup::CalculateLayoutInputHorizontal()
extern void HorizontalLayoutGroup_CalculateLayoutInputHorizontal_m58F17FFCD8BAEEA48D962F4118F4E59D9F2B7D31 (void);
// 0x0000028E System.Void UnityEngine.UI.HorizontalLayoutGroup::CalculateLayoutInputVertical()
extern void HorizontalLayoutGroup_CalculateLayoutInputVertical_mE7BA088589FD23BDF517FBB19B83AD0027A75685 (void);
// 0x0000028F System.Void UnityEngine.UI.HorizontalLayoutGroup::SetLayoutHorizontal()
extern void HorizontalLayoutGroup_SetLayoutHorizontal_mBF6CA4AD5D92305DC79F350C62E6B5807DFBA238 (void);
// 0x00000290 System.Void UnityEngine.UI.HorizontalLayoutGroup::SetLayoutVertical()
extern void HorizontalLayoutGroup_SetLayoutVertical_mAA537085E5664BE721EA09F25F4E0F0F74DB3FC6 (void);
// 0x00000291 System.Single UnityEngine.UI.HorizontalOrVerticalLayoutGroup::get_spacing()
extern void HorizontalOrVerticalLayoutGroup_get_spacing_m52E2EAC47D467F71010583C5AAB31F4D523AC278 (void);
// 0x00000292 System.Void UnityEngine.UI.HorizontalOrVerticalLayoutGroup::set_spacing(System.Single)
extern void HorizontalOrVerticalLayoutGroup_set_spacing_mE5769AC8EBF0C7C9076D71509CC19DBEFF5CEE04 (void);
// 0x00000293 System.Boolean UnityEngine.UI.HorizontalOrVerticalLayoutGroup::get_childForceExpandWidth()
extern void HorizontalOrVerticalLayoutGroup_get_childForceExpandWidth_m493C24DC7BCBAEBB4D0F2C3CA7041EEA32E543F7 (void);
// 0x00000294 System.Void UnityEngine.UI.HorizontalOrVerticalLayoutGroup::set_childForceExpandWidth(System.Boolean)
extern void HorizontalOrVerticalLayoutGroup_set_childForceExpandWidth_m6D4FAB24FD5620FA409E7102779E67894A05D116 (void);
// 0x00000295 System.Boolean UnityEngine.UI.HorizontalOrVerticalLayoutGroup::get_childForceExpandHeight()
extern void HorizontalOrVerticalLayoutGroup_get_childForceExpandHeight_mD9D677E0E1D519AC1E046C52B23967A5B4416223 (void);
// 0x00000296 System.Void UnityEngine.UI.HorizontalOrVerticalLayoutGroup::set_childForceExpandHeight(System.Boolean)
extern void HorizontalOrVerticalLayoutGroup_set_childForceExpandHeight_m4C90F31DD9AB2098FA27C9E826F66C3CCF843E7B (void);
// 0x00000297 System.Boolean UnityEngine.UI.HorizontalOrVerticalLayoutGroup::get_childControlWidth()
extern void HorizontalOrVerticalLayoutGroup_get_childControlWidth_m2C410BBD452AF6E2A74D97041D10B02DE93C17A2 (void);
// 0x00000298 System.Void UnityEngine.UI.HorizontalOrVerticalLayoutGroup::set_childControlWidth(System.Boolean)
extern void HorizontalOrVerticalLayoutGroup_set_childControlWidth_m7B290C97F81F3F0519F8E0E4B4C532B9D0FB8A50 (void);
// 0x00000299 System.Boolean UnityEngine.UI.HorizontalOrVerticalLayoutGroup::get_childControlHeight()
extern void HorizontalOrVerticalLayoutGroup_get_childControlHeight_mFCCDB9F070C4FBD4B34C856931123F42287F4784 (void);
// 0x0000029A System.Void UnityEngine.UI.HorizontalOrVerticalLayoutGroup::set_childControlHeight(System.Boolean)
extern void HorizontalOrVerticalLayoutGroup_set_childControlHeight_mB6FC9B6A66716FBA81C9D50526A00C14DACDD289 (void);
// 0x0000029B System.Boolean UnityEngine.UI.HorizontalOrVerticalLayoutGroup::get_childScaleWidth()
extern void HorizontalOrVerticalLayoutGroup_get_childScaleWidth_mB5B84A9068062297EA5445B423B2BCBB379C9BDD (void);
// 0x0000029C System.Void UnityEngine.UI.HorizontalOrVerticalLayoutGroup::set_childScaleWidth(System.Boolean)
extern void HorizontalOrVerticalLayoutGroup_set_childScaleWidth_mBFD464F83FAA3ADAB539E59A5A677B95768A2E27 (void);
// 0x0000029D System.Boolean UnityEngine.UI.HorizontalOrVerticalLayoutGroup::get_childScaleHeight()
extern void HorizontalOrVerticalLayoutGroup_get_childScaleHeight_mB8F63923C43CCE678A7B3B2234C684C0D76867D9 (void);
// 0x0000029E System.Void UnityEngine.UI.HorizontalOrVerticalLayoutGroup::set_childScaleHeight(System.Boolean)
extern void HorizontalOrVerticalLayoutGroup_set_childScaleHeight_mC928D80D266D4A57E01CCE604821200E01D54FFE (void);
// 0x0000029F System.Boolean UnityEngine.UI.HorizontalOrVerticalLayoutGroup::get_reverseArrangement()
extern void HorizontalOrVerticalLayoutGroup_get_reverseArrangement_m8C11FEF137F0E3A9BFE8B6AE7FEDF8D1E38126C8 (void);
// 0x000002A0 System.Void UnityEngine.UI.HorizontalOrVerticalLayoutGroup::set_reverseArrangement(System.Boolean)
extern void HorizontalOrVerticalLayoutGroup_set_reverseArrangement_m467D77B8C2C113AACF694DDF4561ED35C2290F66 (void);
// 0x000002A1 System.Void UnityEngine.UI.HorizontalOrVerticalLayoutGroup::CalcAlongAxis(System.Int32,System.Boolean)
extern void HorizontalOrVerticalLayoutGroup_CalcAlongAxis_m88F784D17AA542ED1CD28A4541F422A7E90CBE14 (void);
// 0x000002A2 System.Void UnityEngine.UI.HorizontalOrVerticalLayoutGroup::SetChildrenAlongAxis(System.Int32,System.Boolean)
extern void HorizontalOrVerticalLayoutGroup_SetChildrenAlongAxis_m478E2367383D18BF103AD4C58360BDB002F7A88C (void);
// 0x000002A3 System.Void UnityEngine.UI.HorizontalOrVerticalLayoutGroup::GetChildSizes(UnityEngine.RectTransform,System.Int32,System.Boolean,System.Boolean,System.Single&,System.Single&,System.Single&)
extern void HorizontalOrVerticalLayoutGroup_GetChildSizes_m7FE67759B97A7270B9714ACD3D1DA62E0A81FE04 (void);
// 0x000002A4 System.Void UnityEngine.UI.HorizontalOrVerticalLayoutGroup::.ctor()
extern void HorizontalOrVerticalLayoutGroup__ctor_m3FC0FB5106A29D484A1D08F92547715FBBB39337 (void);
// 0x000002A5 System.Void UnityEngine.UI.ILayoutElement::CalculateLayoutInputHorizontal()
// 0x000002A6 System.Void UnityEngine.UI.ILayoutElement::CalculateLayoutInputVertical()
// 0x000002A7 System.Single UnityEngine.UI.ILayoutElement::get_minWidth()
// 0x000002A8 System.Single UnityEngine.UI.ILayoutElement::get_preferredWidth()
// 0x000002A9 System.Single UnityEngine.UI.ILayoutElement::get_flexibleWidth()
// 0x000002AA System.Single UnityEngine.UI.ILayoutElement::get_minHeight()
// 0x000002AB System.Single UnityEngine.UI.ILayoutElement::get_preferredHeight()
// 0x000002AC System.Single UnityEngine.UI.ILayoutElement::get_flexibleHeight()
// 0x000002AD System.Int32 UnityEngine.UI.ILayoutElement::get_layoutPriority()
// 0x000002AE System.Void UnityEngine.UI.ILayoutController::SetLayoutHorizontal()
// 0x000002AF System.Void UnityEngine.UI.ILayoutController::SetLayoutVertical()
// 0x000002B0 System.Boolean UnityEngine.UI.ILayoutIgnorer::get_ignoreLayout()
// 0x000002B1 System.Boolean UnityEngine.UI.LayoutElement::get_ignoreLayout()
extern void LayoutElement_get_ignoreLayout_m90FB86E9510B28B452FF7CC2C318E4D84412391F (void);
// 0x000002B2 System.Void UnityEngine.UI.LayoutElement::set_ignoreLayout(System.Boolean)
extern void LayoutElement_set_ignoreLayout_m44FD1F001ECF85ED7E9A8CE6D3835111FF32623F (void);
// 0x000002B3 System.Void UnityEngine.UI.LayoutElement::CalculateLayoutInputHorizontal()
extern void LayoutElement_CalculateLayoutInputHorizontal_m2AA33718284ADA0ED37CB7D2A1FB44FCCB56BF30 (void);
// 0x000002B4 System.Void UnityEngine.UI.LayoutElement::CalculateLayoutInputVertical()
extern void LayoutElement_CalculateLayoutInputVertical_m47451BFB2053F6C8384D1CA005B2D59247534528 (void);
// 0x000002B5 System.Single UnityEngine.UI.LayoutElement::get_minWidth()
extern void LayoutElement_get_minWidth_mD835C4483246B5FC4D75762EBE915C53C79D1F14 (void);
// 0x000002B6 System.Void UnityEngine.UI.LayoutElement::set_minWidth(System.Single)
extern void LayoutElement_set_minWidth_m28700CB74FF7859E10F7FA479E7C46ABCCABE1A7 (void);
// 0x000002B7 System.Single UnityEngine.UI.LayoutElement::get_minHeight()
extern void LayoutElement_get_minHeight_m5DC25C52E5E7A09848B09F720960D35CF9EB3A8D (void);
// 0x000002B8 System.Void UnityEngine.UI.LayoutElement::set_minHeight(System.Single)
extern void LayoutElement_set_minHeight_m5C71632B3DC3222A9C876D56CAC70688CB84FF51 (void);
// 0x000002B9 System.Single UnityEngine.UI.LayoutElement::get_preferredWidth()
extern void LayoutElement_get_preferredWidth_m73D4E83DCD8902E9335F99BB328F11CBE8F6356A (void);
// 0x000002BA System.Void UnityEngine.UI.LayoutElement::set_preferredWidth(System.Single)
extern void LayoutElement_set_preferredWidth_m0954FDE76D9B80A1E18394E038D703ED10EF426F (void);
// 0x000002BB System.Single UnityEngine.UI.LayoutElement::get_preferredHeight()
extern void LayoutElement_get_preferredHeight_m1E8012BD187CBC867AB4EB71972F650EA1795CC8 (void);
// 0x000002BC System.Void UnityEngine.UI.LayoutElement::set_preferredHeight(System.Single)
extern void LayoutElement_set_preferredHeight_mE369B9F754F79CDA63F01E2CBD2415AE5C7BDB46 (void);
// 0x000002BD System.Single UnityEngine.UI.LayoutElement::get_flexibleWidth()
extern void LayoutElement_get_flexibleWidth_mEE15E7963CBC510FEE099F642066B0DF8EC60605 (void);
// 0x000002BE System.Void UnityEngine.UI.LayoutElement::set_flexibleWidth(System.Single)
extern void LayoutElement_set_flexibleWidth_mAC6031498B3C17D4DD9F7793ACE68B53A1A8E2FD (void);
// 0x000002BF System.Single UnityEngine.UI.LayoutElement::get_flexibleHeight()
extern void LayoutElement_get_flexibleHeight_m6C7B9FE7CDE05C968B84E7067765592CEE74A39C (void);
// 0x000002C0 System.Void UnityEngine.UI.LayoutElement::set_flexibleHeight(System.Single)
extern void LayoutElement_set_flexibleHeight_m5851510F4E7D4D21210053A429FF136E079F0E29 (void);
// 0x000002C1 System.Int32 UnityEngine.UI.LayoutElement::get_layoutPriority()
extern void LayoutElement_get_layoutPriority_m88EDDAE40F620749222D39951F63FFFD0335A238 (void);
// 0x000002C2 System.Void UnityEngine.UI.LayoutElement::set_layoutPriority(System.Int32)
extern void LayoutElement_set_layoutPriority_m7400A83A4C8BB6AD2E278B7971AF2524CF0C997C (void);
// 0x000002C3 System.Void UnityEngine.UI.LayoutElement::.ctor()
extern void LayoutElement__ctor_mEE483CE245BEE525E0C1198D888A6E63F11BDC42 (void);
// 0x000002C4 System.Void UnityEngine.UI.LayoutElement::OnEnable()
extern void LayoutElement_OnEnable_mECD267BF0D6E730570A9D856A15D83DCB673C60C (void);
// 0x000002C5 System.Void UnityEngine.UI.LayoutElement::OnTransformParentChanged()
extern void LayoutElement_OnTransformParentChanged_m60B50562ACAF5289FBEDC949EE137C0A2AD66AA5 (void);
// 0x000002C6 System.Void UnityEngine.UI.LayoutElement::OnDisable()
extern void LayoutElement_OnDisable_m8062A074769E24789C500041F75E5E93EAB25098 (void);
// 0x000002C7 System.Void UnityEngine.UI.LayoutElement::OnDidApplyAnimationProperties()
extern void LayoutElement_OnDidApplyAnimationProperties_m18430F40F3F6090341BE86AA5D9E0E672D128FBA (void);
// 0x000002C8 System.Void UnityEngine.UI.LayoutElement::OnBeforeTransformParentChanged()
extern void LayoutElement_OnBeforeTransformParentChanged_mB3A9008D088FCFEAF7E4CADF7534645BB450703C (void);
// 0x000002C9 System.Void UnityEngine.UI.LayoutElement::SetDirty()
extern void LayoutElement_SetDirty_mFCCF9B49870E36FCEDB5508C181FA4CB15C2E2D5 (void);
// 0x000002CA UnityEngine.RectOffset UnityEngine.UI.LayoutGroup::get_padding()
extern void LayoutGroup_get_padding_m14312E35C2C558577068963736278D801BA9BFCB (void);
// 0x000002CB System.Void UnityEngine.UI.LayoutGroup::set_padding(UnityEngine.RectOffset)
extern void LayoutGroup_set_padding_mBABB15FB92C0766BA1FA688FE7D7C252B9D6560A (void);
// 0x000002CC UnityEngine.TextAnchor UnityEngine.UI.LayoutGroup::get_childAlignment()
extern void LayoutGroup_get_childAlignment_m670498A8C95B7E523D0E819BB6FB4A52B5464B12 (void);
// 0x000002CD System.Void UnityEngine.UI.LayoutGroup::set_childAlignment(UnityEngine.TextAnchor)
extern void LayoutGroup_set_childAlignment_m574A13B9726C01BC30AB0C164C925D3B09C68748 (void);
// 0x000002CE UnityEngine.RectTransform UnityEngine.UI.LayoutGroup::get_rectTransform()
extern void LayoutGroup_get_rectTransform_mF81F1612DF0C56799E91552048508B5A94896CD9 (void);
// 0x000002CF System.Collections.Generic.List`1<UnityEngine.RectTransform> UnityEngine.UI.LayoutGroup::get_rectChildren()
extern void LayoutGroup_get_rectChildren_mB8A4C237BEAE27F143A2F7571192BF6736B5E52E (void);
// 0x000002D0 System.Void UnityEngine.UI.LayoutGroup::CalculateLayoutInputHorizontal()
extern void LayoutGroup_CalculateLayoutInputHorizontal_m5E1D66D491C159A1F45014E6115A56719B3B9933 (void);
// 0x000002D1 System.Void UnityEngine.UI.LayoutGroup::CalculateLayoutInputVertical()
// 0x000002D2 System.Single UnityEngine.UI.LayoutGroup::get_minWidth()
extern void LayoutGroup_get_minWidth_mEF0E63E9F35D0085FB39887A2D104A9427E4D996 (void);
// 0x000002D3 System.Single UnityEngine.UI.LayoutGroup::get_preferredWidth()
extern void LayoutGroup_get_preferredWidth_mE4CBB2309F30250BE73BFA7913A52C09138EF801 (void);
// 0x000002D4 System.Single UnityEngine.UI.LayoutGroup::get_flexibleWidth()
extern void LayoutGroup_get_flexibleWidth_mA2F83444A705FF8A20AE91EAC036D9015BFD46E2 (void);
// 0x000002D5 System.Single UnityEngine.UI.LayoutGroup::get_minHeight()
extern void LayoutGroup_get_minHeight_mB1AE8171B557C9A14744305181D2FDA300FD5C6F (void);
// 0x000002D6 System.Single UnityEngine.UI.LayoutGroup::get_preferredHeight()
extern void LayoutGroup_get_preferredHeight_m0649F34C601D214D5CB51246FED7D79F40ABBFBB (void);
// 0x000002D7 System.Single UnityEngine.UI.LayoutGroup::get_flexibleHeight()
extern void LayoutGroup_get_flexibleHeight_m4943BC00D95A92703B653243EBCC1A6CA56D4B25 (void);
// 0x000002D8 System.Int32 UnityEngine.UI.LayoutGroup::get_layoutPriority()
extern void LayoutGroup_get_layoutPriority_mD389BE409DEB26E85405C0BA7477E21789EC10F3 (void);
// 0x000002D9 System.Void UnityEngine.UI.LayoutGroup::SetLayoutHorizontal()
// 0x000002DA System.Void UnityEngine.UI.LayoutGroup::SetLayoutVertical()
// 0x000002DB System.Void UnityEngine.UI.LayoutGroup::.ctor()
extern void LayoutGroup__ctor_mF6B995B1A6B39EAB7999D5A41260651463FDFE37 (void);
// 0x000002DC System.Void UnityEngine.UI.LayoutGroup::OnEnable()
extern void LayoutGroup_OnEnable_mD22B04F97D7FB19DD4E9F0E1F179B00DA305B42F (void);
// 0x000002DD System.Void UnityEngine.UI.LayoutGroup::OnDisable()
extern void LayoutGroup_OnDisable_mF9EC32C322F3B4B15C1FEE77CA5F04CD4035D8F5 (void);
// 0x000002DE System.Void UnityEngine.UI.LayoutGroup::OnDidApplyAnimationProperties()
extern void LayoutGroup_OnDidApplyAnimationProperties_m447CFDEA42F252105BCE07A58F7F26D1C93994D4 (void);
// 0x000002DF System.Single UnityEngine.UI.LayoutGroup::GetTotalMinSize(System.Int32)
extern void LayoutGroup_GetTotalMinSize_m0D5047B7CCD8A168F3DE7F6CF3CA68D62D50C32C (void);
// 0x000002E0 System.Single UnityEngine.UI.LayoutGroup::GetTotalPreferredSize(System.Int32)
extern void LayoutGroup_GetTotalPreferredSize_mD57F59092D1A3CD7ED35A13672FE3E9D706410B9 (void);
// 0x000002E1 System.Single UnityEngine.UI.LayoutGroup::GetTotalFlexibleSize(System.Int32)
extern void LayoutGroup_GetTotalFlexibleSize_m5C74F3CD2D5D236CD7A4B49B83E85A216B1530A4 (void);
// 0x000002E2 System.Single UnityEngine.UI.LayoutGroup::GetStartOffset(System.Int32,System.Single)
extern void LayoutGroup_GetStartOffset_m071CE94DE28E370E83633BBC8D5A699D91A45344 (void);
// 0x000002E3 System.Single UnityEngine.UI.LayoutGroup::GetAlignmentOnAxis(System.Int32)
extern void LayoutGroup_GetAlignmentOnAxis_m84AE6B5EF7769C6154E5B36FC04D66F8CBC93790 (void);
// 0x000002E4 System.Void UnityEngine.UI.LayoutGroup::SetLayoutInputForAxis(System.Single,System.Single,System.Single,System.Int32)
extern void LayoutGroup_SetLayoutInputForAxis_mDAA241F8AF27C222C01B89CB659BB6446D87A299 (void);
// 0x000002E5 System.Void UnityEngine.UI.LayoutGroup::SetChildAlongAxis(UnityEngine.RectTransform,System.Int32,System.Single)
extern void LayoutGroup_SetChildAlongAxis_m8FDB65849BEDFD74A3E08D986122AE6B6807E739 (void);
// 0x000002E6 System.Void UnityEngine.UI.LayoutGroup::SetChildAlongAxisWithScale(UnityEngine.RectTransform,System.Int32,System.Single,System.Single)
extern void LayoutGroup_SetChildAlongAxisWithScale_mD655B16F2D4CBF6F504A85349868F9668FA04623 (void);
// 0x000002E7 System.Void UnityEngine.UI.LayoutGroup::SetChildAlongAxis(UnityEngine.RectTransform,System.Int32,System.Single,System.Single)
extern void LayoutGroup_SetChildAlongAxis_m5D13D8EF43A7C6126A33BD8E21F260956700C52C (void);
// 0x000002E8 System.Void UnityEngine.UI.LayoutGroup::SetChildAlongAxisWithScale(UnityEngine.RectTransform,System.Int32,System.Single,System.Single,System.Single)
extern void LayoutGroup_SetChildAlongAxisWithScale_m65623CB84FBEF746D08E0003F261468AF67075C5 (void);
// 0x000002E9 System.Boolean UnityEngine.UI.LayoutGroup::get_isRootLayoutGroup()
extern void LayoutGroup_get_isRootLayoutGroup_mAED51B1FF22135348E2088D8746E5F51E0FFEC2B (void);
// 0x000002EA System.Void UnityEngine.UI.LayoutGroup::OnRectTransformDimensionsChange()
extern void LayoutGroup_OnRectTransformDimensionsChange_m85246BC71AA506DCC65AC2CEBE1AEE2DB28D287B (void);
// 0x000002EB System.Void UnityEngine.UI.LayoutGroup::OnTransformChildrenChanged()
extern void LayoutGroup_OnTransformChildrenChanged_mFDF8D16A0C6A4C50CAB1997ABB33E17677733E3C (void);
// 0x000002EC System.Void UnityEngine.UI.LayoutGroup::SetProperty(T&,T)
// 0x000002ED System.Void UnityEngine.UI.LayoutGroup::SetDirty()
extern void LayoutGroup_SetDirty_mCD44774BE69A93938EE1622CC13FFC2C16DDB021 (void);
// 0x000002EE System.Collections.IEnumerator UnityEngine.UI.LayoutGroup::DelayedSetDirty(UnityEngine.RectTransform)
extern void LayoutGroup_DelayedSetDirty_mD732CCC3419B94BF80F00D4E1126C3AAFA249C8B (void);
// 0x000002EF System.Void UnityEngine.UI.LayoutGroup/<DelayedSetDirty>d__56::.ctor(System.Int32)
extern void U3CDelayedSetDirtyU3Ed__56__ctor_mA52F10924596857781781E2D32305ED395CCFC40 (void);
// 0x000002F0 System.Void UnityEngine.UI.LayoutGroup/<DelayedSetDirty>d__56::System.IDisposable.Dispose()
extern void U3CDelayedSetDirtyU3Ed__56_System_IDisposable_Dispose_mB2EEBBFE1EDA19D7668469D89C5DC20135F92CA3 (void);
// 0x000002F1 System.Boolean UnityEngine.UI.LayoutGroup/<DelayedSetDirty>d__56::MoveNext()
extern void U3CDelayedSetDirtyU3Ed__56_MoveNext_m2600A9E03E1423A54B1E91926F6AD71E180719BC (void);
// 0x000002F2 System.Object UnityEngine.UI.LayoutGroup/<DelayedSetDirty>d__56::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CDelayedSetDirtyU3Ed__56_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC0766D0840885F5FD82287052C846A487A3C954D (void);
// 0x000002F3 System.Void UnityEngine.UI.LayoutGroup/<DelayedSetDirty>d__56::System.Collections.IEnumerator.Reset()
extern void U3CDelayedSetDirtyU3Ed__56_System_Collections_IEnumerator_Reset_mD3851B6C7275A35789E989AAFB19866B0240BEAC (void);
// 0x000002F4 System.Object UnityEngine.UI.LayoutGroup/<DelayedSetDirty>d__56::System.Collections.IEnumerator.get_Current()
extern void U3CDelayedSetDirtyU3Ed__56_System_Collections_IEnumerator_get_Current_mFA346E997DCBDC2A7E20F57FD48C4A012EC0ED4E (void);
// 0x000002F5 System.Void UnityEngine.UI.LayoutRebuilder::Initialize(UnityEngine.RectTransform)
extern void LayoutRebuilder_Initialize_mA64FB3CEB4C28A10A77DE188CD36837A9C5F601F (void);
// 0x000002F6 System.Void UnityEngine.UI.LayoutRebuilder::Clear()
extern void LayoutRebuilder_Clear_m28BB858DDB42479A0E2C9FA91467381CF8755669 (void);
// 0x000002F7 System.Void UnityEngine.UI.LayoutRebuilder::.cctor()
extern void LayoutRebuilder__cctor_m93AF2F44B9B7411276E2374CCD10F4F70456E380 (void);
// 0x000002F8 System.Void UnityEngine.UI.LayoutRebuilder::ReapplyDrivenProperties(UnityEngine.RectTransform)
extern void LayoutRebuilder_ReapplyDrivenProperties_m92F035C865057BF1D34CC02662DABB29EB543B0F (void);
// 0x000002F9 UnityEngine.Transform UnityEngine.UI.LayoutRebuilder::get_transform()
extern void LayoutRebuilder_get_transform_m16D6996C3E2A1F975BA5F280E8F530E0EBA86B8C (void);
// 0x000002FA System.Boolean UnityEngine.UI.LayoutRebuilder::IsDestroyed()
extern void LayoutRebuilder_IsDestroyed_m7C24E5FF56D4600C3368CC88511D92DA4B03BE57 (void);
// 0x000002FB System.Void UnityEngine.UI.LayoutRebuilder::StripDisabledBehavioursFromList(System.Collections.Generic.List`1<UnityEngine.Component>)
extern void LayoutRebuilder_StripDisabledBehavioursFromList_m1B50BD38F9EE421716F6E763AF6EF8D43B241227 (void);
// 0x000002FC System.Void UnityEngine.UI.LayoutRebuilder::ForceRebuildLayoutImmediate(UnityEngine.RectTransform)
extern void LayoutRebuilder_ForceRebuildLayoutImmediate_mA56A6E02EFB1DD50371E603EDD49297707F146CA (void);
// 0x000002FD System.Void UnityEngine.UI.LayoutRebuilder::Rebuild(UnityEngine.UI.CanvasUpdate)
extern void LayoutRebuilder_Rebuild_mB7370F51240B9FE738FED8593FB729A953783EAB (void);
// 0x000002FE System.Void UnityEngine.UI.LayoutRebuilder::PerformLayoutControl(UnityEngine.RectTransform,UnityEngine.Events.UnityAction`1<UnityEngine.Component>)
extern void LayoutRebuilder_PerformLayoutControl_m02DBD164DD8EDCC8D5573E86C26A65C59A933ADA (void);
// 0x000002FF System.Void UnityEngine.UI.LayoutRebuilder::PerformLayoutCalculation(UnityEngine.RectTransform,UnityEngine.Events.UnityAction`1<UnityEngine.Component>)
extern void LayoutRebuilder_PerformLayoutCalculation_m7F8DF3422B31393955167737593C3AFF6B607531 (void);
// 0x00000300 System.Void UnityEngine.UI.LayoutRebuilder::MarkLayoutForRebuild(UnityEngine.RectTransform)
extern void LayoutRebuilder_MarkLayoutForRebuild_m025A52454C18402ADDE59FD652A6A93262DD4374 (void);
// 0x00000301 System.Boolean UnityEngine.UI.LayoutRebuilder::ValidController(UnityEngine.RectTransform,System.Collections.Generic.List`1<UnityEngine.Component>)
extern void LayoutRebuilder_ValidController_m759455420379586151C15036400E7B8B1DCA6B38 (void);
// 0x00000302 System.Void UnityEngine.UI.LayoutRebuilder::MarkLayoutRootForRebuild(UnityEngine.RectTransform)
extern void LayoutRebuilder_MarkLayoutRootForRebuild_mD2CA04C7F9E68839AFBD3054FFE3948C036A9725 (void);
// 0x00000303 System.Void UnityEngine.UI.LayoutRebuilder::LayoutComplete()
extern void LayoutRebuilder_LayoutComplete_mE4F8C9D77EAF91B18B0C76979E0B3891986C22FE (void);
// 0x00000304 System.Void UnityEngine.UI.LayoutRebuilder::GraphicUpdateComplete()
extern void LayoutRebuilder_GraphicUpdateComplete_mEE92F38BE479A86089EC72D083A14ABE1E5B3F1E (void);
// 0x00000305 System.Int32 UnityEngine.UI.LayoutRebuilder::GetHashCode()
extern void LayoutRebuilder_GetHashCode_m76283AE71310FB5F1D685EB98F62DA09C929AC0B (void);
// 0x00000306 System.Boolean UnityEngine.UI.LayoutRebuilder::Equals(System.Object)
extern void LayoutRebuilder_Equals_mC3E1CF820F9BC34537A9E5465DA0CBC795CC276C (void);
// 0x00000307 System.String UnityEngine.UI.LayoutRebuilder::ToString()
extern void LayoutRebuilder_ToString_mC07B5519A043C74D0600F98FD019214349ACA974 (void);
// 0x00000308 System.Void UnityEngine.UI.LayoutRebuilder::.ctor()
extern void LayoutRebuilder__ctor_m463A9E152E52208B053AB1AB2188FD2D8A0ACB28 (void);
// 0x00000309 System.Void UnityEngine.UI.LayoutRebuilder/<>c::.cctor()
extern void U3CU3Ec__cctor_m3F5D7F404B03B80C5F061CAABDC75ECA40BA1159 (void);
// 0x0000030A System.Void UnityEngine.UI.LayoutRebuilder/<>c::.ctor()
extern void U3CU3Ec__ctor_m08AEBA4CF31106A157D26C8F80A5BAC82544DA2A (void);
// 0x0000030B System.Void UnityEngine.UI.LayoutRebuilder/<>c::<.cctor>b__5_0(UnityEngine.UI.LayoutRebuilder)
extern void U3CU3Ec_U3C_cctorU3Eb__5_0_m8E25902030C5EC6F9AF84E1FACF3135A511EE87E (void);
// 0x0000030C System.Boolean UnityEngine.UI.LayoutRebuilder/<>c::<StripDisabledBehavioursFromList>b__10_0(UnityEngine.Component)
extern void U3CU3Ec_U3CStripDisabledBehavioursFromListU3Eb__10_0_m701215783D65B7E21FBEDF6DD2D14AB22719C28D (void);
// 0x0000030D System.Void UnityEngine.UI.LayoutRebuilder/<>c::<Rebuild>b__12_0(UnityEngine.Component)
extern void U3CU3Ec_U3CRebuildU3Eb__12_0_mA041A1DCC1746B1827D000661B5CD0D3AABE0161 (void);
// 0x0000030E System.Void UnityEngine.UI.LayoutRebuilder/<>c::<Rebuild>b__12_1(UnityEngine.Component)
extern void U3CU3Ec_U3CRebuildU3Eb__12_1_mB33AEEFC02E5A2BADD0EC1020E2975B7ABCA9107 (void);
// 0x0000030F System.Void UnityEngine.UI.LayoutRebuilder/<>c::<Rebuild>b__12_2(UnityEngine.Component)
extern void U3CU3Ec_U3CRebuildU3Eb__12_2_mA37813EF3059FEDE8365C5F941899CD429089586 (void);
// 0x00000310 System.Void UnityEngine.UI.LayoutRebuilder/<>c::<Rebuild>b__12_3(UnityEngine.Component)
extern void U3CU3Ec_U3CRebuildU3Eb__12_3_m50289A90C028D9B108DB0F8551E1237AD636337F (void);
// 0x00000311 System.Single UnityEngine.UI.LayoutUtility::GetMinSize(UnityEngine.RectTransform,System.Int32)
extern void LayoutUtility_GetMinSize_m8D3AAF478A0A261BC05548DEB8ED61B795B5A554 (void);
// 0x00000312 System.Single UnityEngine.UI.LayoutUtility::GetPreferredSize(UnityEngine.RectTransform,System.Int32)
extern void LayoutUtility_GetPreferredSize_m3A02690B68FE61A89524BBB93F52D262061A838B (void);
// 0x00000313 System.Single UnityEngine.UI.LayoutUtility::GetFlexibleSize(UnityEngine.RectTransform,System.Int32)
extern void LayoutUtility_GetFlexibleSize_mC7D226041C128F868058F137299E20626F9F87AD (void);
// 0x00000314 System.Single UnityEngine.UI.LayoutUtility::GetMinWidth(UnityEngine.RectTransform)
extern void LayoutUtility_GetMinWidth_m5730683A1CF1F091BD18E17C897345EB35093315 (void);
// 0x00000315 System.Single UnityEngine.UI.LayoutUtility::GetPreferredWidth(UnityEngine.RectTransform)
extern void LayoutUtility_GetPreferredWidth_m62BA20D2ECF0A0502BA8478F5D63658FA844947F (void);
// 0x00000316 System.Single UnityEngine.UI.LayoutUtility::GetFlexibleWidth(UnityEngine.RectTransform)
extern void LayoutUtility_GetFlexibleWidth_m68EF94A84D1037BA28D99F15D3CDC20781712A2C (void);
// 0x00000317 System.Single UnityEngine.UI.LayoutUtility::GetMinHeight(UnityEngine.RectTransform)
extern void LayoutUtility_GetMinHeight_m527516709B779A3B459A54A9AD6F892828CD706F (void);
// 0x00000318 System.Single UnityEngine.UI.LayoutUtility::GetPreferredHeight(UnityEngine.RectTransform)
extern void LayoutUtility_GetPreferredHeight_m0D9083AAD4CAA0656A6EFB0449B64537FD2C4547 (void);
// 0x00000319 System.Single UnityEngine.UI.LayoutUtility::GetFlexibleHeight(UnityEngine.RectTransform)
extern void LayoutUtility_GetFlexibleHeight_m18B694D154FD9DB74320D96C51F2919CB0405920 (void);
// 0x0000031A System.Single UnityEngine.UI.LayoutUtility::GetLayoutProperty(UnityEngine.RectTransform,System.Func`2<UnityEngine.UI.ILayoutElement,System.Single>,System.Single)
extern void LayoutUtility_GetLayoutProperty_mDD68B3F4FBB2D4AF14CD1434A5CC21381C964577 (void);
// 0x0000031B System.Single UnityEngine.UI.LayoutUtility::GetLayoutProperty(UnityEngine.RectTransform,System.Func`2<UnityEngine.UI.ILayoutElement,System.Single>,System.Single,UnityEngine.UI.ILayoutElement&)
extern void LayoutUtility_GetLayoutProperty_m5D0585B7B2F1E9DA5EAEE60FBCA3B387ADB9BFCD (void);
// 0x0000031C System.Void UnityEngine.UI.LayoutUtility/<>c::.cctor()
extern void U3CU3Ec__cctor_m4B1FD62EA1AD030EA991BE423C491A1E8F64CD7E (void);
// 0x0000031D System.Void UnityEngine.UI.LayoutUtility/<>c::.ctor()
extern void U3CU3Ec__ctor_m1B199444775398EC661E1E5A54F902DDEC91E77F (void);
// 0x0000031E System.Single UnityEngine.UI.LayoutUtility/<>c::<GetMinWidth>b__3_0(UnityEngine.UI.ILayoutElement)
extern void U3CU3Ec_U3CGetMinWidthU3Eb__3_0_mCBA85C4A1F5E5868352281A6D02ECB2DB3CA543A (void);
// 0x0000031F System.Single UnityEngine.UI.LayoutUtility/<>c::<GetPreferredWidth>b__4_0(UnityEngine.UI.ILayoutElement)
extern void U3CU3Ec_U3CGetPreferredWidthU3Eb__4_0_m98B9DCF76E6BF84270C3DA0B8E3A98868E056390 (void);
// 0x00000320 System.Single UnityEngine.UI.LayoutUtility/<>c::<GetPreferredWidth>b__4_1(UnityEngine.UI.ILayoutElement)
extern void U3CU3Ec_U3CGetPreferredWidthU3Eb__4_1_m4460F5F291EA091DC96FA574787BFBE261FFD659 (void);
// 0x00000321 System.Single UnityEngine.UI.LayoutUtility/<>c::<GetFlexibleWidth>b__5_0(UnityEngine.UI.ILayoutElement)
extern void U3CU3Ec_U3CGetFlexibleWidthU3Eb__5_0_m168AAD0730CBC71061EF59ACE641DBAA2F73EEF5 (void);
// 0x00000322 System.Single UnityEngine.UI.LayoutUtility/<>c::<GetMinHeight>b__6_0(UnityEngine.UI.ILayoutElement)
extern void U3CU3Ec_U3CGetMinHeightU3Eb__6_0_mE547AB359DF68B6250AC20EAE08BF7A6B370039B (void);
// 0x00000323 System.Single UnityEngine.UI.LayoutUtility/<>c::<GetPreferredHeight>b__7_0(UnityEngine.UI.ILayoutElement)
extern void U3CU3Ec_U3CGetPreferredHeightU3Eb__7_0_mD751E70588985A919D25922900C1C3CBE2CC7A9B (void);
// 0x00000324 System.Single UnityEngine.UI.LayoutUtility/<>c::<GetPreferredHeight>b__7_1(UnityEngine.UI.ILayoutElement)
extern void U3CU3Ec_U3CGetPreferredHeightU3Eb__7_1_m490DBFE3A1AFF5E10F751C55998C2E951C542594 (void);
// 0x00000325 System.Single UnityEngine.UI.LayoutUtility/<>c::<GetFlexibleHeight>b__8_0(UnityEngine.UI.ILayoutElement)
extern void U3CU3Ec_U3CGetFlexibleHeightU3Eb__8_0_m500C6DA403AFD260B45ABFCE746ED86872EECC2F (void);
// 0x00000326 System.Void UnityEngine.UI.VerticalLayoutGroup::.ctor()
extern void VerticalLayoutGroup__ctor_m50FC410FAF5C69492B239DA9A18189563054E4D0 (void);
// 0x00000327 System.Void UnityEngine.UI.VerticalLayoutGroup::CalculateLayoutInputHorizontal()
extern void VerticalLayoutGroup_CalculateLayoutInputHorizontal_m6C97EB48D36329CEDA4D94DC825DA2DD8D49AF99 (void);
// 0x00000328 System.Void UnityEngine.UI.VerticalLayoutGroup::CalculateLayoutInputVertical()
extern void VerticalLayoutGroup_CalculateLayoutInputVertical_mD376BB61F211CE987B424108EA749D9BA17D1F52 (void);
// 0x00000329 System.Void UnityEngine.UI.VerticalLayoutGroup::SetLayoutHorizontal()
extern void VerticalLayoutGroup_SetLayoutHorizontal_m6CE79D24AC376A32573E305024D9ED763BED8CDB (void);
// 0x0000032A System.Void UnityEngine.UI.VerticalLayoutGroup::SetLayoutVertical()
extern void VerticalLayoutGroup_SetLayoutVertical_m41201990B993E828922B56DA65C656505F0FA2E3 (void);
// 0x0000032B UnityEngine.RectTransform UnityEngine.UI.Mask::get_rectTransform()
extern void Mask_get_rectTransform_mF1081D590012320A4D694F33A7965B7A8DFE4A28 (void);
// 0x0000032C System.Boolean UnityEngine.UI.Mask::get_showMaskGraphic()
extern void Mask_get_showMaskGraphic_mA87FB0DBA07B8761DA2C923EA50CD60BD3EB7271 (void);
// 0x0000032D System.Void UnityEngine.UI.Mask::set_showMaskGraphic(System.Boolean)
extern void Mask_set_showMaskGraphic_m7AF49BD8866B82C70293D2C666D979FB320168D4 (void);
// 0x0000032E UnityEngine.UI.Graphic UnityEngine.UI.Mask::get_graphic()
extern void Mask_get_graphic_m3C7C03706C188F60DB2068C62459E1BF0277FED4 (void);
// 0x0000032F System.Void UnityEngine.UI.Mask::.ctor()
extern void Mask__ctor_mC3A6C226EDD04AC7058424F80D413DB9A85570AE (void);
// 0x00000330 System.Boolean UnityEngine.UI.Mask::MaskEnabled()
extern void Mask_MaskEnabled_m87EF5C7FCC4DA5A28BEDA3FE7986DC78BD3BCA04 (void);
// 0x00000331 System.Void UnityEngine.UI.Mask::OnSiblingGraphicEnabledDisabled()
extern void Mask_OnSiblingGraphicEnabledDisabled_m7710190D8CD6A9D2F4C2C1DB55C1226637B52D1B (void);
// 0x00000332 System.Void UnityEngine.UI.Mask::OnEnable()
extern void Mask_OnEnable_mD321373E240B3013212217903A2902D12C904A21 (void);
// 0x00000333 System.Void UnityEngine.UI.Mask::OnDisable()
extern void Mask_OnDisable_m24823C343812F3A0CE28B2B0347139B61B07B289 (void);
// 0x00000334 System.Boolean UnityEngine.UI.Mask::IsRaycastLocationValid(UnityEngine.Vector2,UnityEngine.Camera)
extern void Mask_IsRaycastLocationValid_mD5F1B486F5816382572A751AF9D7A5329E9C4469 (void);
// 0x00000335 UnityEngine.Material UnityEngine.UI.Mask::GetModifiedMaterial(UnityEngine.Material)
extern void Mask_GetModifiedMaterial_mB0819C9D0018D8DC89510BBD5BD8DB35EF50837D (void);
// 0x00000336 System.Void UnityEngine.UI.MaskUtilities::Notify2DMaskStateChanged(UnityEngine.Component)
extern void MaskUtilities_Notify2DMaskStateChanged_m78C7E6535470CF1345ED9471C3878CE24324A08B (void);
// 0x00000337 System.Void UnityEngine.UI.MaskUtilities::NotifyStencilStateChanged(UnityEngine.Component)
extern void MaskUtilities_NotifyStencilStateChanged_mF0A329778C7F1FD73762B86A0B37A02312D2BC87 (void);
// 0x00000338 UnityEngine.Transform UnityEngine.UI.MaskUtilities::FindRootSortOverrideCanvas(UnityEngine.Transform)
extern void MaskUtilities_FindRootSortOverrideCanvas_m8011C7BE2E9AA5F60134D65013FD5F5BE25CED6D (void);
// 0x00000339 System.Int32 UnityEngine.UI.MaskUtilities::GetStencilDepth(UnityEngine.Transform,UnityEngine.Transform)
extern void MaskUtilities_GetStencilDepth_m849019B4446DB533DEB78006CCC46C99620D92C6 (void);
// 0x0000033A System.Boolean UnityEngine.UI.MaskUtilities::IsDescendantOrSelf(UnityEngine.Transform,UnityEngine.Transform)
extern void MaskUtilities_IsDescendantOrSelf_mC354024F9052BA6D06229258CB686082C3D747FA (void);
// 0x0000033B UnityEngine.UI.RectMask2D UnityEngine.UI.MaskUtilities::GetRectMaskForClippable(UnityEngine.UI.IClippable)
extern void MaskUtilities_GetRectMaskForClippable_mDF3C0050FBD1491A248D70B9ED136F73688D2BB8 (void);
// 0x0000033C System.Void UnityEngine.UI.MaskUtilities::GetRectMasksForClip(UnityEngine.UI.RectMask2D,System.Collections.Generic.List`1<UnityEngine.UI.RectMask2D>)
extern void MaskUtilities_GetRectMasksForClip_m4DFEE101F2703345EB46AD9B217D4760822B0F94 (void);
// 0x0000033D System.Void UnityEngine.UI.MaskUtilities::.ctor()
extern void MaskUtilities__ctor_mED82BFC80F6E0DDF6687DFCD89139F3F0B376B3E (void);
// 0x0000033E UnityEngine.UI.MaskableGraphic/CullStateChangedEvent UnityEngine.UI.MaskableGraphic::get_onCullStateChanged()
extern void MaskableGraphic_get_onCullStateChanged_mCF39F6FB561F9F9866E23662066E26718AB07CE0 (void);
// 0x0000033F System.Void UnityEngine.UI.MaskableGraphic::set_onCullStateChanged(UnityEngine.UI.MaskableGraphic/CullStateChangedEvent)
extern void MaskableGraphic_set_onCullStateChanged_m5239A4147A72D6113180B1A2C444C5F76D388A39 (void);
// 0x00000340 System.Boolean UnityEngine.UI.MaskableGraphic::get_maskable()
extern void MaskableGraphic_get_maskable_mAB089F676929CD6D1CA9FC204EE04A10EF86B6E8 (void);
// 0x00000341 System.Void UnityEngine.UI.MaskableGraphic::set_maskable(System.Boolean)
extern void MaskableGraphic_set_maskable_m59171D018C5424DC8CA4E46D005A28F71AC2B1CB (void);
// 0x00000342 System.Boolean UnityEngine.UI.MaskableGraphic::get_isMaskingGraphic()
extern void MaskableGraphic_get_isMaskingGraphic_m147E8FC2072567862BAC9AFDFE265B76C7708202 (void);
// 0x00000343 System.Void UnityEngine.UI.MaskableGraphic::set_isMaskingGraphic(System.Boolean)
extern void MaskableGraphic_set_isMaskingGraphic_m274342FF4513DEB896214303AA74206944C13601 (void);
// 0x00000344 UnityEngine.Material UnityEngine.UI.MaskableGraphic::GetModifiedMaterial(UnityEngine.Material)
extern void MaskableGraphic_GetModifiedMaterial_m58800A1D036E9FD9040E871F5CA2573D59EC211B (void);
// 0x00000345 System.Void UnityEngine.UI.MaskableGraphic::Cull(UnityEngine.Rect,System.Boolean)
extern void MaskableGraphic_Cull_mF3E7CA1619211B6695E398275AC8EB62F13D6CBF (void);
// 0x00000346 System.Void UnityEngine.UI.MaskableGraphic::UpdateCull(System.Boolean)
extern void MaskableGraphic_UpdateCull_m5E32F0412FD48EBAE03F539655B05A55220C2BD3 (void);
// 0x00000347 System.Void UnityEngine.UI.MaskableGraphic::SetClipRect(UnityEngine.Rect,System.Boolean)
extern void MaskableGraphic_SetClipRect_m8BBAB7D151E7F87910ED49FFC4145464CDC8D71C (void);
// 0x00000348 System.Void UnityEngine.UI.MaskableGraphic::SetClipSoftness(UnityEngine.Vector2)
extern void MaskableGraphic_SetClipSoftness_mA1280583A758409DD41DADD9C0EE2178BBAB74AE (void);
// 0x00000349 System.Void UnityEngine.UI.MaskableGraphic::OnEnable()
extern void MaskableGraphic_OnEnable_m61F2B68A4560CAB2A40C3C6F6AF74C3C10D80AE8 (void);
// 0x0000034A System.Void UnityEngine.UI.MaskableGraphic::OnDisable()
extern void MaskableGraphic_OnDisable_m85189B68E2DBE5ECCFBC9B2A1385F38050FE2686 (void);
// 0x0000034B System.Void UnityEngine.UI.MaskableGraphic::OnTransformParentChanged()
extern void MaskableGraphic_OnTransformParentChanged_mF98185DF6C98FEA60C6EF77969D2F23638C03846 (void);
// 0x0000034C System.Void UnityEngine.UI.MaskableGraphic::ParentMaskStateChanged()
extern void MaskableGraphic_ParentMaskStateChanged_m3E76F4824392C343448165502617A717297457E4 (void);
// 0x0000034D System.Void UnityEngine.UI.MaskableGraphic::OnCanvasHierarchyChanged()
extern void MaskableGraphic_OnCanvasHierarchyChanged_mAB3381225CEB6CC2803046EC7B9C7CBBA8887EC6 (void);
// 0x0000034E UnityEngine.Rect UnityEngine.UI.MaskableGraphic::get_rootCanvasRect()
extern void MaskableGraphic_get_rootCanvasRect_mE2C5C1C3237308CFEED55BB501AE001174A75AA9 (void);
// 0x0000034F System.Void UnityEngine.UI.MaskableGraphic::UpdateClipParent()
extern void MaskableGraphic_UpdateClipParent_mF0C05D442F080B5902D081512371DE9E32F739B0 (void);
// 0x00000350 System.Void UnityEngine.UI.MaskableGraphic::RecalculateClipping()
extern void MaskableGraphic_RecalculateClipping_m0402575B6C04BC3A36CAE548FC29B380B923CC09 (void);
// 0x00000351 System.Void UnityEngine.UI.MaskableGraphic::RecalculateMasking()
extern void MaskableGraphic_RecalculateMasking_mC8BE4CA3AD0428C8C0F520864E47AC9BA64B4A2B (void);
// 0x00000352 System.Void UnityEngine.UI.MaskableGraphic::.ctor()
extern void MaskableGraphic__ctor_m89126DB114322D1D18F67BA3B8D0695FF1371A4D (void);
// 0x00000353 UnityEngine.GameObject UnityEngine.UI.MaskableGraphic::UnityEngine.UI.IClippable.get_gameObject()
extern void MaskableGraphic_UnityEngine_UI_IClippable_get_gameObject_mA760B34D8EFAD579D0277436706534CFE46FA238 (void);
// 0x00000354 System.Void UnityEngine.UI.MaskableGraphic/CullStateChangedEvent::.ctor()
extern void CullStateChangedEvent__ctor_m733D6A4DFDEED4F4812B6EFD581007DB79C1FB57 (void);
// 0x00000355 UnityEngine.Material UnityEngine.UI.IMaterialModifier::GetModifiedMaterial(UnityEngine.Material)
// 0x00000356 System.Void UnityEngine.UI.Misc::Destroy(UnityEngine.Object)
extern void Misc_Destroy_m66309EEE8B043D11C853ED50B66DDA0A830FA7F0 (void);
// 0x00000357 System.Void UnityEngine.UI.Misc::DestroyImmediate(UnityEngine.Object)
extern void Misc_DestroyImmediate_mB67B6DF39213A22333EB9ADC8DC52C1D7B6B7F74 (void);
// 0x00000358 System.Boolean UnityEngine.UI.MultipleDisplayUtilities::GetRelativeMousePositionForDrag(UnityEngine.EventSystems.PointerEventData,UnityEngine.Vector2&)
extern void MultipleDisplayUtilities_GetRelativeMousePositionForDrag_mD23D7090ECC680ADC154DA4930EADE29B6D092B8 (void);
// 0x00000359 UnityEngine.Vector3 UnityEngine.UI.MultipleDisplayUtilities::RelativeMouseAtScaled(UnityEngine.Vector2)
extern void MultipleDisplayUtilities_RelativeMouseAtScaled_mE54C997DC566E0E97F97D5A5A4F109688A023C1B (void);
// 0x0000035A UnityEngine.UI.Navigation/Mode UnityEngine.UI.Navigation::get_mode()
extern void Navigation_get_mode_mB995DE758F5FE0E01F6D54EC5FAC27E85D51E9D9 (void);
// 0x0000035B System.Void UnityEngine.UI.Navigation::set_mode(UnityEngine.UI.Navigation/Mode)
extern void Navigation_set_mode_m06986291C80ABAB009A7968A3A5C557197B7CAD0 (void);
// 0x0000035C System.Boolean UnityEngine.UI.Navigation::get_wrapAround()
extern void Navigation_get_wrapAround_mD84154C6A5DF2D999F9C68694660BF4C75E42E09 (void);
// 0x0000035D System.Void UnityEngine.UI.Navigation::set_wrapAround(System.Boolean)
extern void Navigation_set_wrapAround_m88C6E2CE8D45F409B10220931C7574A925478EEF (void);
// 0x0000035E UnityEngine.UI.Selectable UnityEngine.UI.Navigation::get_selectOnUp()
extern void Navigation_get_selectOnUp_mE2CE90A504C34AE46D8BC9930C7E309F3BD61D05 (void);
// 0x0000035F System.Void UnityEngine.UI.Navigation::set_selectOnUp(UnityEngine.UI.Selectable)
extern void Navigation_set_selectOnUp_m4FB3B1A6FD96E0F5AF5A029C49150E05520EAEB4 (void);
// 0x00000360 UnityEngine.UI.Selectable UnityEngine.UI.Navigation::get_selectOnDown()
extern void Navigation_get_selectOnDown_mDEAD9711CD57EC13A9622AA29E1D95E7FE49B26C (void);
// 0x00000361 System.Void UnityEngine.UI.Navigation::set_selectOnDown(UnityEngine.UI.Selectable)
extern void Navigation_set_selectOnDown_mFAAAE5BDCD7A658119C96474C108C9A59E62F2D5 (void);
// 0x00000362 UnityEngine.UI.Selectable UnityEngine.UI.Navigation::get_selectOnLeft()
extern void Navigation_get_selectOnLeft_m7E124CA8B6205001892CA5F8FBCC3CE2A451ACF8 (void);
// 0x00000363 System.Void UnityEngine.UI.Navigation::set_selectOnLeft(UnityEngine.UI.Selectable)
extern void Navigation_set_selectOnLeft_mA33AA78D5F7F6BE93490C9EABF182E1D77C0E580 (void);
// 0x00000364 UnityEngine.UI.Selectable UnityEngine.UI.Navigation::get_selectOnRight()
extern void Navigation_get_selectOnRight_mA7E6599E6A54419AE4C532F76F4199EFF2188114 (void);
// 0x00000365 System.Void UnityEngine.UI.Navigation::set_selectOnRight(UnityEngine.UI.Selectable)
extern void Navigation_set_selectOnRight_m6B60DBA287E342806BF4CF1AD332DA4EF875BD4A (void);
// 0x00000366 UnityEngine.UI.Navigation UnityEngine.UI.Navigation::get_defaultNavigation()
extern void Navigation_get_defaultNavigation_mEF98E8BBEC8608B4332A9417042B3E1D1EDC1955 (void);
// 0x00000367 System.Boolean UnityEngine.UI.Navigation::Equals(UnityEngine.UI.Navigation)
extern void Navigation_Equals_m1462D8660E507B770231FF9CBDAC296C1D751C7A (void);
// 0x00000368 System.Void UnityEngine.UI.RawImage::.ctor()
extern void RawImage__ctor_mF745F95A5854034191E5787E9A22F2267943FDFE (void);
// 0x00000369 UnityEngine.Texture UnityEngine.UI.RawImage::get_mainTexture()
extern void RawImage_get_mainTexture_m6607D1D2672EF3ADA7BA65E5116516AE994B998E (void);
// 0x0000036A UnityEngine.Texture UnityEngine.UI.RawImage::get_texture()
extern void RawImage_get_texture_m12060F8F44CCBFC6753E455FAF61A075E89022A1 (void);
// 0x0000036B System.Void UnityEngine.UI.RawImage::set_texture(UnityEngine.Texture)
extern void RawImage_set_texture_m1D7BAE6CB629C36894B664D9F5D68CACA88B8D99 (void);
// 0x0000036C UnityEngine.Rect UnityEngine.UI.RawImage::get_uvRect()
extern void RawImage_get_uvRect_m5C5CB285CD0DBF30B9DE8A62C90011B22FBF523B (void);
// 0x0000036D System.Void UnityEngine.UI.RawImage::set_uvRect(UnityEngine.Rect)
extern void RawImage_set_uvRect_m529CC2006BBD8AB373C567F80F4FCABEBFF1242E (void);
// 0x0000036E System.Void UnityEngine.UI.RawImage::SetNativeSize()
extern void RawImage_SetNativeSize_m558874FAB55362D5ED28C5D2551A23E374CDF960 (void);
// 0x0000036F System.Void UnityEngine.UI.RawImage::OnPopulateMesh(UnityEngine.UI.VertexHelper)
extern void RawImage_OnPopulateMesh_m6EA21D82C0D9C4327339440CB4292CBAFAA3B87C (void);
// 0x00000370 System.Void UnityEngine.UI.RawImage::OnDidApplyAnimationProperties()
extern void RawImage_OnDidApplyAnimationProperties_m7E77F85DF55B8F807033B1A69226A5C0DEF0B322 (void);
// 0x00000371 UnityEngine.Vector4 UnityEngine.UI.RectMask2D::get_padding()
extern void RectMask2D_get_padding_mDE45F1235EBC500D855A14D8A191C553864F5B6C (void);
// 0x00000372 System.Void UnityEngine.UI.RectMask2D::set_padding(UnityEngine.Vector4)
extern void RectMask2D_set_padding_mC6682C99C43EE4EAA308EEF0734E31F2E9509BDE (void);
// 0x00000373 UnityEngine.Vector2Int UnityEngine.UI.RectMask2D::get_softness()
extern void RectMask2D_get_softness_m2759F63DF0C023E1B7C442EEEC79B2EFED2F0766 (void);
// 0x00000374 System.Void UnityEngine.UI.RectMask2D::set_softness(UnityEngine.Vector2Int)
extern void RectMask2D_set_softness_mBAA0308DDEF6B338515E73917DCDFBB5BBB69577 (void);
// 0x00000375 UnityEngine.Canvas UnityEngine.UI.RectMask2D::get_Canvas()
extern void RectMask2D_get_Canvas_m2109052587563A271881729044E0896C8330EBCA (void);
// 0x00000376 UnityEngine.Rect UnityEngine.UI.RectMask2D::get_canvasRect()
extern void RectMask2D_get_canvasRect_mCF9908196FEF28E2493F401755BC48D49F55D8F2 (void);
// 0x00000377 UnityEngine.RectTransform UnityEngine.UI.RectMask2D::get_rectTransform()
extern void RectMask2D_get_rectTransform_mF0BAAE5A88C9390C41E5CEB4A2D944A4F69DC33D (void);
// 0x00000378 System.Void UnityEngine.UI.RectMask2D::.ctor()
extern void RectMask2D__ctor_mE0AA40ADEDAD189C28C17B038732C249C54F2C43 (void);
// 0x00000379 System.Void UnityEngine.UI.RectMask2D::OnEnable()
extern void RectMask2D_OnEnable_mFD84098C65DC9A4C58A3A9127079B6ED17F79A98 (void);
// 0x0000037A System.Void UnityEngine.UI.RectMask2D::OnDisable()
extern void RectMask2D_OnDisable_m995393F2508BCF5688DABE9F19F1CABF49A4709E (void);
// 0x0000037B System.Void UnityEngine.UI.RectMask2D::OnDestroy()
extern void RectMask2D_OnDestroy_m2B7DFD2F15BAA18CF021C0E58740666989AD5170 (void);
// 0x0000037C System.Boolean UnityEngine.UI.RectMask2D::IsRaycastLocationValid(UnityEngine.Vector2,UnityEngine.Camera)
extern void RectMask2D_IsRaycastLocationValid_m45BB3B371ED9DAA908E18633FB1EA9B1005B127B (void);
// 0x0000037D UnityEngine.Rect UnityEngine.UI.RectMask2D::get_rootCanvasRect()
extern void RectMask2D_get_rootCanvasRect_m215A31029C47BDE07D332433F58A77CF34A8572D (void);
// 0x0000037E System.Void UnityEngine.UI.RectMask2D::PerformClipping()
extern void RectMask2D_PerformClipping_m2D6A62F7600B3897AA061A4D4760E4B532C6F768 (void);
// 0x0000037F System.Void UnityEngine.UI.RectMask2D::UpdateClipSoftness()
extern void RectMask2D_UpdateClipSoftness_mEF617C8CFF0C3284D06ACFD7B61F48CE2B6B7585 (void);
// 0x00000380 System.Void UnityEngine.UI.RectMask2D::AddClippable(UnityEngine.UI.IClippable)
extern void RectMask2D_AddClippable_mF1C7DF9391787AF278C408D179E1AD7C1798A6A0 (void);
// 0x00000381 System.Void UnityEngine.UI.RectMask2D::RemoveClippable(UnityEngine.UI.IClippable)
extern void RectMask2D_RemoveClippable_mF30C17434A5F117300B26C7D2D0C06E9E011B454 (void);
// 0x00000382 System.Void UnityEngine.UI.RectMask2D::OnTransformParentChanged()
extern void RectMask2D_OnTransformParentChanged_m6B083DD4655623AA054A02CFF269C0702A2605FC (void);
// 0x00000383 System.Void UnityEngine.UI.RectMask2D::OnCanvasHierarchyChanged()
extern void RectMask2D_OnCanvasHierarchyChanged_mA96030132A4CA47A6070562B402261C1CF27F98B (void);
// 0x00000384 UnityEngine.RectTransform UnityEngine.UI.ScrollRect::get_content()
extern void ScrollRect_get_content_m80081CD9D9D066BB138099E6495390B37071C3B0 (void);
// 0x00000385 System.Void UnityEngine.UI.ScrollRect::set_content(UnityEngine.RectTransform)
extern void ScrollRect_set_content_mAE777214875D848F20F1F6795E8848E8E7EF6D65 (void);
// 0x00000386 System.Boolean UnityEngine.UI.ScrollRect::get_horizontal()
extern void ScrollRect_get_horizontal_m3C85C37B4136F69E47ACFAF287D05FC4104E5C8A (void);
// 0x00000387 System.Void UnityEngine.UI.ScrollRect::set_horizontal(System.Boolean)
extern void ScrollRect_set_horizontal_m45564FC7DD17DE132CCC1A843C6D83A9635C3BB4 (void);
// 0x00000388 System.Boolean UnityEngine.UI.ScrollRect::get_vertical()
extern void ScrollRect_get_vertical_mC44745C3E8AC6BF41C3C2D3FA0AEDED3DE96B4EA (void);
// 0x00000389 System.Void UnityEngine.UI.ScrollRect::set_vertical(System.Boolean)
extern void ScrollRect_set_vertical_m25BA9647871AD3CADF2A26CB938BE5418A13C22E (void);
// 0x0000038A UnityEngine.UI.ScrollRect/MovementType UnityEngine.UI.ScrollRect::get_movementType()
extern void ScrollRect_get_movementType_m12C1D0EB101216FC3C545AECBF883AE23AD82101 (void);
// 0x0000038B System.Void UnityEngine.UI.ScrollRect::set_movementType(UnityEngine.UI.ScrollRect/MovementType)
extern void ScrollRect_set_movementType_m63E3C04ECBDA708AF2341564DB987227D32075CC (void);
// 0x0000038C System.Single UnityEngine.UI.ScrollRect::get_elasticity()
extern void ScrollRect_get_elasticity_m45FBA92BD4EFFAC555B8ACC4B0D385CE9E775F89 (void);
// 0x0000038D System.Void UnityEngine.UI.ScrollRect::set_elasticity(System.Single)
extern void ScrollRect_set_elasticity_m6D3FBF1D599AE50DCC8C908E3E4ECE1098F15F9E (void);
// 0x0000038E System.Boolean UnityEngine.UI.ScrollRect::get_inertia()
extern void ScrollRect_get_inertia_mB2D4CBFDDF073DE6BA62E2A248E9BAAFE2886462 (void);
// 0x0000038F System.Void UnityEngine.UI.ScrollRect::set_inertia(System.Boolean)
extern void ScrollRect_set_inertia_m9CE7BE08CEB308DC8B9335436B901D557B4E6718 (void);
// 0x00000390 System.Single UnityEngine.UI.ScrollRect::get_decelerationRate()
extern void ScrollRect_get_decelerationRate_m318236CD5B50F314C311D61528785E703FDA8292 (void);
// 0x00000391 System.Void UnityEngine.UI.ScrollRect::set_decelerationRate(System.Single)
extern void ScrollRect_set_decelerationRate_m1981EED127C9DEE4E02027B53F7779FF39AE2BCB (void);
// 0x00000392 System.Single UnityEngine.UI.ScrollRect::get_scrollSensitivity()
extern void ScrollRect_get_scrollSensitivity_mD3FA21FAA3F5F0B73C93D00FF3F18E786B04D890 (void);
// 0x00000393 System.Void UnityEngine.UI.ScrollRect::set_scrollSensitivity(System.Single)
extern void ScrollRect_set_scrollSensitivity_m9C2DFE6AF0977A4E9B9D9F208D83548EE7A95550 (void);
// 0x00000394 UnityEngine.RectTransform UnityEngine.UI.ScrollRect::get_viewport()
extern void ScrollRect_get_viewport_m5F19B84F9A457DB423884801E0D58F53FE371382 (void);
// 0x00000395 System.Void UnityEngine.UI.ScrollRect::set_viewport(UnityEngine.RectTransform)
extern void ScrollRect_set_viewport_mAD1CEECC3A6C8B77CC96C48522D47EA7CB15F846 (void);
// 0x00000396 UnityEngine.UI.Scrollbar UnityEngine.UI.ScrollRect::get_horizontalScrollbar()
extern void ScrollRect_get_horizontalScrollbar_m0E70E75626442D98852270657FB8535F63789C59 (void);
// 0x00000397 System.Void UnityEngine.UI.ScrollRect::set_horizontalScrollbar(UnityEngine.UI.Scrollbar)
extern void ScrollRect_set_horizontalScrollbar_mDEE90E50682241478EA86561DF2426F84C9BD5F0 (void);
// 0x00000398 UnityEngine.UI.Scrollbar UnityEngine.UI.ScrollRect::get_verticalScrollbar()
extern void ScrollRect_get_verticalScrollbar_m5A5637770B83D5542ECC2F72C4653FF6B3F6860B (void);
// 0x00000399 System.Void UnityEngine.UI.ScrollRect::set_verticalScrollbar(UnityEngine.UI.Scrollbar)
extern void ScrollRect_set_verticalScrollbar_m1733A8BFBDA05FC60882409B832C44FE14AED399 (void);
// 0x0000039A UnityEngine.UI.ScrollRect/ScrollbarVisibility UnityEngine.UI.ScrollRect::get_horizontalScrollbarVisibility()
extern void ScrollRect_get_horizontalScrollbarVisibility_m3DFDDB45471A160F3CDBBEF8C482CA44C6D8DD5F (void);
// 0x0000039B System.Void UnityEngine.UI.ScrollRect::set_horizontalScrollbarVisibility(UnityEngine.UI.ScrollRect/ScrollbarVisibility)
extern void ScrollRect_set_horizontalScrollbarVisibility_m496B18BB2183C7E44AE46D388A1780B0E8E07FC4 (void);
// 0x0000039C UnityEngine.UI.ScrollRect/ScrollbarVisibility UnityEngine.UI.ScrollRect::get_verticalScrollbarVisibility()
extern void ScrollRect_get_verticalScrollbarVisibility_mE2CBD69FBB2F9B242D1CCCDDBCB1B7DCCB8657B8 (void);
// 0x0000039D System.Void UnityEngine.UI.ScrollRect::set_verticalScrollbarVisibility(UnityEngine.UI.ScrollRect/ScrollbarVisibility)
extern void ScrollRect_set_verticalScrollbarVisibility_m1D20BA7616E7642DC8E7054A06469D4C8B9D9FA9 (void);
// 0x0000039E System.Single UnityEngine.UI.ScrollRect::get_horizontalScrollbarSpacing()
extern void ScrollRect_get_horizontalScrollbarSpacing_mAFDBCD05E05BF104C4465F6D8BE48ED38A57B525 (void);
// 0x0000039F System.Void UnityEngine.UI.ScrollRect::set_horizontalScrollbarSpacing(System.Single)
extern void ScrollRect_set_horizontalScrollbarSpacing_mCFDF9FF111EAC5CFA76489DB4F7FE1EF507A4D23 (void);
// 0x000003A0 System.Single UnityEngine.UI.ScrollRect::get_verticalScrollbarSpacing()
extern void ScrollRect_get_verticalScrollbarSpacing_m63D70F75E9C1ED9AF8DE1362967E28F304E6A8AD (void);
// 0x000003A1 System.Void UnityEngine.UI.ScrollRect::set_verticalScrollbarSpacing(System.Single)
extern void ScrollRect_set_verticalScrollbarSpacing_m4BA8BD3FFB47F67FA2654086A2D45B94E87B2885 (void);
// 0x000003A2 UnityEngine.UI.ScrollRect/ScrollRectEvent UnityEngine.UI.ScrollRect::get_onValueChanged()
extern void ScrollRect_get_onValueChanged_m6CB9D48C2BFBDF7896EB011B2FA7977EEFC05B14 (void);
// 0x000003A3 System.Void UnityEngine.UI.ScrollRect::set_onValueChanged(UnityEngine.UI.ScrollRect/ScrollRectEvent)
extern void ScrollRect_set_onValueChanged_m8BF93395195F8F7C3F55127AC75E2F396B554E50 (void);
// 0x000003A4 UnityEngine.RectTransform UnityEngine.UI.ScrollRect::get_viewRect()
extern void ScrollRect_get_viewRect_m0AA50C04284AB56F79258AA0CBD30A1FFDAE9299 (void);
// 0x000003A5 UnityEngine.Vector2 UnityEngine.UI.ScrollRect::get_velocity()
extern void ScrollRect_get_velocity_mF5519EB702F5A1BCBDA6EE50CE92EEFA3398BDC6 (void);
// 0x000003A6 System.Void UnityEngine.UI.ScrollRect::set_velocity(UnityEngine.Vector2)
extern void ScrollRect_set_velocity_m6CBDADD5F61163917B37C922FA6F42170D68E1D9 (void);
// 0x000003A7 UnityEngine.RectTransform UnityEngine.UI.ScrollRect::get_rectTransform()
extern void ScrollRect_get_rectTransform_mCA3A848543FE3C0BB1A594A11F4726DCF5C4CF91 (void);
// 0x000003A8 System.Void UnityEngine.UI.ScrollRect::.ctor()
extern void ScrollRect__ctor_mB4B056D158F001158736D43FF95A79C4749F81EB (void);
// 0x000003A9 System.Void UnityEngine.UI.ScrollRect::Rebuild(UnityEngine.UI.CanvasUpdate)
extern void ScrollRect_Rebuild_m8779D60FEAF422554E9FDE675E8A1FF78DBB7B9C (void);
// 0x000003AA System.Void UnityEngine.UI.ScrollRect::LayoutComplete()
extern void ScrollRect_LayoutComplete_m85522038286BE8858947C3E9D1D4C7E36B25D840 (void);
// 0x000003AB System.Void UnityEngine.UI.ScrollRect::GraphicUpdateComplete()
extern void ScrollRect_GraphicUpdateComplete_mC82C019BFB983269063A158D5BB5B0AD6A27373E (void);
// 0x000003AC System.Void UnityEngine.UI.ScrollRect::UpdateCachedData()
extern void ScrollRect_UpdateCachedData_mE1AB66554F5D5B9F2AEA6D1369E678B19B34BEA0 (void);
// 0x000003AD System.Void UnityEngine.UI.ScrollRect::OnEnable()
extern void ScrollRect_OnEnable_m71DB5029CAF2AA0C6AE3C5265195FA2BD8F3BF14 (void);
// 0x000003AE System.Void UnityEngine.UI.ScrollRect::OnDisable()
extern void ScrollRect_OnDisable_mB88CAFFED9F9E8C3C1CF730A4C88FDB1C39EBB4F (void);
// 0x000003AF System.Boolean UnityEngine.UI.ScrollRect::IsActive()
extern void ScrollRect_IsActive_m48C5CB5DA8DA18455B523E5039BD38CF0B6ADD21 (void);
// 0x000003B0 System.Void UnityEngine.UI.ScrollRect::EnsureLayoutHasRebuilt()
extern void ScrollRect_EnsureLayoutHasRebuilt_m6F55D294F93B8F0EA375C4A3479588FA1C836756 (void);
// 0x000003B1 System.Void UnityEngine.UI.ScrollRect::StopMovement()
extern void ScrollRect_StopMovement_m34AED9A8CF4EBD7422B90575C87B4A839FB83DDA (void);
// 0x000003B2 System.Void UnityEngine.UI.ScrollRect::OnScroll(UnityEngine.EventSystems.PointerEventData)
extern void ScrollRect_OnScroll_m0D80BCED3E165E19CA4A9F30306E3B92A7D70406 (void);
// 0x000003B3 System.Void UnityEngine.UI.ScrollRect::OnInitializePotentialDrag(UnityEngine.EventSystems.PointerEventData)
extern void ScrollRect_OnInitializePotentialDrag_m442FF00867FECD77D57B3ADD27ABB4736C4A783F (void);
// 0x000003B4 System.Void UnityEngine.UI.ScrollRect::OnBeginDrag(UnityEngine.EventSystems.PointerEventData)
extern void ScrollRect_OnBeginDrag_mFD8547604ADE7E19C1CB247BE3C3D30EDBF6F170 (void);
// 0x000003B5 System.Void UnityEngine.UI.ScrollRect::OnEndDrag(UnityEngine.EventSystems.PointerEventData)
extern void ScrollRect_OnEndDrag_m655F48C5CD01CFC9802144038A635B6F663FE828 (void);
// 0x000003B6 System.Void UnityEngine.UI.ScrollRect::OnDrag(UnityEngine.EventSystems.PointerEventData)
extern void ScrollRect_OnDrag_mAE4C9A8E16CE9E40F1F50F6CC9DBAC5107327C24 (void);
// 0x000003B7 System.Void UnityEngine.UI.ScrollRect::SetContentAnchoredPosition(UnityEngine.Vector2)
extern void ScrollRect_SetContentAnchoredPosition_mF134A239555D56A9EFB5D7B0BBE7337608D9B8F5 (void);
// 0x000003B8 System.Void UnityEngine.UI.ScrollRect::LateUpdate()
extern void ScrollRect_LateUpdate_mAFD5AD44F910FFB77D8969DF1C0C52966F79BE94 (void);
// 0x000003B9 System.Void UnityEngine.UI.ScrollRect::UpdatePrevData()
extern void ScrollRect_UpdatePrevData_m5BA02E98ABA18860856725E547949B64C4C88AC2 (void);
// 0x000003BA System.Void UnityEngine.UI.ScrollRect::UpdateScrollbars(UnityEngine.Vector2)
extern void ScrollRect_UpdateScrollbars_m95DCA6709A3A5E1DD0DA5BE024B694F22B7706C0 (void);
// 0x000003BB UnityEngine.Vector2 UnityEngine.UI.ScrollRect::get_normalizedPosition()
extern void ScrollRect_get_normalizedPosition_m337A8A3C84D96C866B4851D654E69362CC3ABD6C (void);
// 0x000003BC System.Void UnityEngine.UI.ScrollRect::set_normalizedPosition(UnityEngine.Vector2)
extern void ScrollRect_set_normalizedPosition_mCC99F394BFA5FF1D1F52CF75BC41C0A761F41551 (void);
// 0x000003BD System.Single UnityEngine.UI.ScrollRect::get_horizontalNormalizedPosition()
extern void ScrollRect_get_horizontalNormalizedPosition_m2447569D682F6F02D64671B82B64FA9C80EE8FE2 (void);
// 0x000003BE System.Void UnityEngine.UI.ScrollRect::set_horizontalNormalizedPosition(System.Single)
extern void ScrollRect_set_horizontalNormalizedPosition_m3552851D166277D4DEDFCB6B81BF6726666D3AF5 (void);
// 0x000003BF System.Single UnityEngine.UI.ScrollRect::get_verticalNormalizedPosition()
extern void ScrollRect_get_verticalNormalizedPosition_mB5E338D332643DF0B7464F45CC9C04DD9868FBB4 (void);
// 0x000003C0 System.Void UnityEngine.UI.ScrollRect::set_verticalNormalizedPosition(System.Single)
extern void ScrollRect_set_verticalNormalizedPosition_m18393A7B91359B013466A984325F233535A6941D (void);
// 0x000003C1 System.Void UnityEngine.UI.ScrollRect::SetHorizontalNormalizedPosition(System.Single)
extern void ScrollRect_SetHorizontalNormalizedPosition_m0831302489172DCA40C1AB185D551AFEB5B0087C (void);
// 0x000003C2 System.Void UnityEngine.UI.ScrollRect::SetVerticalNormalizedPosition(System.Single)
extern void ScrollRect_SetVerticalNormalizedPosition_m5FD7F53D655877D67117439068280E9FB247634E (void);
// 0x000003C3 System.Void UnityEngine.UI.ScrollRect::SetNormalizedPosition(System.Single,System.Int32)
extern void ScrollRect_SetNormalizedPosition_m1443593E3619BBF532744E7567AE36DE2A8C4196 (void);
// 0x000003C4 System.Single UnityEngine.UI.ScrollRect::RubberDelta(System.Single,System.Single)
extern void ScrollRect_RubberDelta_mAEEA0DF9F667A8BB7B463A30CFF9B7862C4A6418 (void);
// 0x000003C5 System.Void UnityEngine.UI.ScrollRect::OnRectTransformDimensionsChange()
extern void ScrollRect_OnRectTransformDimensionsChange_m98D63D7045CF46910BD761853193CBC1A56317EB (void);
// 0x000003C6 System.Boolean UnityEngine.UI.ScrollRect::get_hScrollingNeeded()
extern void ScrollRect_get_hScrollingNeeded_m89C5EB3D4E25F46D317354DA0988B7340DC4AFCB (void);
// 0x000003C7 System.Boolean UnityEngine.UI.ScrollRect::get_vScrollingNeeded()
extern void ScrollRect_get_vScrollingNeeded_mE8234F874E268A323926A3A13ED6C2F126805890 (void);
// 0x000003C8 System.Void UnityEngine.UI.ScrollRect::CalculateLayoutInputHorizontal()
extern void ScrollRect_CalculateLayoutInputHorizontal_m97CEF11B8DEE2EB2CF2FB5AE3E65B88ECD864042 (void);
// 0x000003C9 System.Void UnityEngine.UI.ScrollRect::CalculateLayoutInputVertical()
extern void ScrollRect_CalculateLayoutInputVertical_mCD39E6A18E8E9D08AEECC69F5ED4BCDEEFC60066 (void);
// 0x000003CA System.Single UnityEngine.UI.ScrollRect::get_minWidth()
extern void ScrollRect_get_minWidth_m4BFD7F0DC39B06D4744EAFBF7BEA0FA31304360D (void);
// 0x000003CB System.Single UnityEngine.UI.ScrollRect::get_preferredWidth()
extern void ScrollRect_get_preferredWidth_m79A19083EAEA565D0FDDE830DC1592E3CCD1E9E2 (void);
// 0x000003CC System.Single UnityEngine.UI.ScrollRect::get_flexibleWidth()
extern void ScrollRect_get_flexibleWidth_m49AC658BFB2CB9200C647FF7C2480F3D58541E73 (void);
// 0x000003CD System.Single UnityEngine.UI.ScrollRect::get_minHeight()
extern void ScrollRect_get_minHeight_mE504CAE24344F7FF00C6612CB075AA05DBCC1078 (void);
// 0x000003CE System.Single UnityEngine.UI.ScrollRect::get_preferredHeight()
extern void ScrollRect_get_preferredHeight_m02F31C2CEC4CDDD4E822CB8D919A4F40B936C995 (void);
// 0x000003CF System.Single UnityEngine.UI.ScrollRect::get_flexibleHeight()
extern void ScrollRect_get_flexibleHeight_m83AF3CDF404A57FBC672646E252C0781D158407B (void);
// 0x000003D0 System.Int32 UnityEngine.UI.ScrollRect::get_layoutPriority()
extern void ScrollRect_get_layoutPriority_m1276F59CCD936DB745749DE12CFA1386958A347E (void);
// 0x000003D1 System.Void UnityEngine.UI.ScrollRect::SetLayoutHorizontal()
extern void ScrollRect_SetLayoutHorizontal_m34E183E669122BE54199783ED1F15A38929CFCBC (void);
// 0x000003D2 System.Void UnityEngine.UI.ScrollRect::SetLayoutVertical()
extern void ScrollRect_SetLayoutVertical_mC9CD0A1437205234CE2FB2704100A4A7A28CD7E4 (void);
// 0x000003D3 System.Void UnityEngine.UI.ScrollRect::UpdateScrollbarVisibility()
extern void ScrollRect_UpdateScrollbarVisibility_m1EB5A90580B6EA5A6855E6CBB784B8EA2929DC56 (void);
// 0x000003D4 System.Void UnityEngine.UI.ScrollRect::UpdateOneScrollbarVisibility(System.Boolean,System.Boolean,UnityEngine.UI.ScrollRect/ScrollbarVisibility,UnityEngine.UI.Scrollbar)
extern void ScrollRect_UpdateOneScrollbarVisibility_m03BFF7787125947A60922EA18512BCF1DC0839D9 (void);
// 0x000003D5 System.Void UnityEngine.UI.ScrollRect::UpdateScrollbarLayout()
extern void ScrollRect_UpdateScrollbarLayout_mEAB065DB24B0CE20EC5EE3AB9B2FBCF5CAF4E0B7 (void);
// 0x000003D6 System.Void UnityEngine.UI.ScrollRect::UpdateBounds()
extern void ScrollRect_UpdateBounds_m5D98C2EABD039BDFFC9A3362BE3F2D2DD33BDD7A (void);
// 0x000003D7 System.Void UnityEngine.UI.ScrollRect::AdjustBounds(UnityEngine.Bounds&,UnityEngine.Vector2&,UnityEngine.Vector3&,UnityEngine.Vector3&)
extern void ScrollRect_AdjustBounds_m63C18824143696C5FAAAD706D24BA76393BA6D36 (void);
// 0x000003D8 UnityEngine.Bounds UnityEngine.UI.ScrollRect::GetBounds()
extern void ScrollRect_GetBounds_m84B078D23326F6A5287D35A5FC4978A020FDA33A (void);
// 0x000003D9 UnityEngine.Bounds UnityEngine.UI.ScrollRect::InternalGetBounds(UnityEngine.Vector3[],UnityEngine.Matrix4x4&)
extern void ScrollRect_InternalGetBounds_mD8104E1E0DDFF1DB7C949F15B445E71FA25737FD (void);
// 0x000003DA UnityEngine.Vector2 UnityEngine.UI.ScrollRect::CalculateOffset(UnityEngine.Vector2)
extern void ScrollRect_CalculateOffset_mEDC05AE7605F106C328D03D1AC18E0EB8B2C2972 (void);
// 0x000003DB UnityEngine.Vector2 UnityEngine.UI.ScrollRect::InternalCalculateOffset(UnityEngine.Bounds&,UnityEngine.Bounds&,System.Boolean,System.Boolean,UnityEngine.UI.ScrollRect/MovementType,UnityEngine.Vector2&)
extern void ScrollRect_InternalCalculateOffset_m7A3CCD6C9A7AC67A91250F5FA4E91628F66AC4FE (void);
// 0x000003DC System.Void UnityEngine.UI.ScrollRect::SetDirty()
extern void ScrollRect_SetDirty_m5007A787609DA72D331A2A42B0D52D88EB5A01AD (void);
// 0x000003DD System.Void UnityEngine.UI.ScrollRect::SetDirtyCaching()
extern void ScrollRect_SetDirtyCaching_mBA4C1977ED1E5AFECA3CB21823DB915C981C3BC9 (void);
// 0x000003DE UnityEngine.Transform UnityEngine.UI.ScrollRect::UnityEngine.UI.ICanvasElement.get_transform()
extern void ScrollRect_UnityEngine_UI_ICanvasElement_get_transform_m3C7EE3376326BEC74AE45A6C091753B4149BFCF7 (void);
// 0x000003DF System.Void UnityEngine.UI.ScrollRect/ScrollRectEvent::.ctor()
extern void ScrollRectEvent__ctor_mF8413852D2A70FE11491D494D0909CA5BD0FD303 (void);
// 0x000003E0 UnityEngine.RectTransform UnityEngine.UI.Scrollbar::get_handleRect()
extern void Scrollbar_get_handleRect_m45CB651CA840761D5D3DE137D419FE5A86A6C21D (void);
// 0x000003E1 System.Void UnityEngine.UI.Scrollbar::set_handleRect(UnityEngine.RectTransform)
extern void Scrollbar_set_handleRect_m718E47F2C0F307303898AC0B0CF60F3F176688B6 (void);
// 0x000003E2 UnityEngine.UI.Scrollbar/Direction UnityEngine.UI.Scrollbar::get_direction()
extern void Scrollbar_get_direction_m9EF74B3DDD6C4DD7265952A4D315F2BA0FDE312C (void);
// 0x000003E3 System.Void UnityEngine.UI.Scrollbar::set_direction(UnityEngine.UI.Scrollbar/Direction)
extern void Scrollbar_set_direction_mA23D0E4790E46B12B892488D6D13503D031EF02B (void);
// 0x000003E4 System.Void UnityEngine.UI.Scrollbar::.ctor()
extern void Scrollbar__ctor_m80CCB2881577F6AE404B53F35969419ECE24010E (void);
// 0x000003E5 System.Single UnityEngine.UI.Scrollbar::get_value()
extern void Scrollbar_get_value_mC925448739BB4DC891D49F600D370D808296BD07 (void);
// 0x000003E6 System.Void UnityEngine.UI.Scrollbar::set_value(System.Single)
extern void Scrollbar_set_value_mEDFFDDF8153EA01B897198648DCFB1D1EA539197 (void);
// 0x000003E7 System.Void UnityEngine.UI.Scrollbar::SetValueWithoutNotify(System.Single)
extern void Scrollbar_SetValueWithoutNotify_m288FB57076EAF069E6E231BAA85EC6347693D1E1 (void);
// 0x000003E8 System.Single UnityEngine.UI.Scrollbar::get_size()
extern void Scrollbar_get_size_m5D2EDDF92A6BA31ED642067D57E8B7174778E69B (void);
// 0x000003E9 System.Void UnityEngine.UI.Scrollbar::set_size(System.Single)
extern void Scrollbar_set_size_m93B8718BD965E7188796F588928D240730CE22E6 (void);
// 0x000003EA System.Int32 UnityEngine.UI.Scrollbar::get_numberOfSteps()
extern void Scrollbar_get_numberOfSteps_mF257DEFB3A88F3ECDEE9402D88BFE60EB7232730 (void);
// 0x000003EB System.Void UnityEngine.UI.Scrollbar::set_numberOfSteps(System.Int32)
extern void Scrollbar_set_numberOfSteps_mB5A632F3D5CCA75A392675F925A6C806C09A9386 (void);
// 0x000003EC UnityEngine.UI.Scrollbar/ScrollEvent UnityEngine.UI.Scrollbar::get_onValueChanged()
extern void Scrollbar_get_onValueChanged_m40245AB9ADE9B6EC0B03A77874E9CAE51090FB34 (void);
// 0x000003ED System.Void UnityEngine.UI.Scrollbar::set_onValueChanged(UnityEngine.UI.Scrollbar/ScrollEvent)
extern void Scrollbar_set_onValueChanged_mB77AC16CFBCF4DC9C775FB884C09C5AC6740FAA4 (void);
// 0x000003EE System.Single UnityEngine.UI.Scrollbar::get_stepSize()
extern void Scrollbar_get_stepSize_m5278BCD7DC4B4B1ABBEE2E307E8E9E23DDADEF76 (void);
// 0x000003EF System.Void UnityEngine.UI.Scrollbar::Rebuild(UnityEngine.UI.CanvasUpdate)
extern void Scrollbar_Rebuild_mE717E5E4A86B86FCFC060B25D1BA3FBD9854564C (void);
// 0x000003F0 System.Void UnityEngine.UI.Scrollbar::LayoutComplete()
extern void Scrollbar_LayoutComplete_m8C11EDDC3142224049EB6FBE3A5D20B5D211CB7C (void);
// 0x000003F1 System.Void UnityEngine.UI.Scrollbar::GraphicUpdateComplete()
extern void Scrollbar_GraphicUpdateComplete_m750E9739FB98B6CE947367E81174DD0B69B7B70A (void);
// 0x000003F2 System.Void UnityEngine.UI.Scrollbar::OnEnable()
extern void Scrollbar_OnEnable_m118E712E4424022A2D3445A0143EFF90E3E47A5C (void);
// 0x000003F3 System.Void UnityEngine.UI.Scrollbar::OnDisable()
extern void Scrollbar_OnDisable_m4C27D3F7D3309333CCDE609BC4AA8C7390B4ED6A (void);
// 0x000003F4 System.Void UnityEngine.UI.Scrollbar::Update()
extern void Scrollbar_Update_m9CDEA1F7A37F03D54DFA56304A4320493ED772DE (void);
// 0x000003F5 System.Void UnityEngine.UI.Scrollbar::UpdateCachedReferences()
extern void Scrollbar_UpdateCachedReferences_m665A5D25A9563092CBB93AE789C21D5D3A02FDB9 (void);
// 0x000003F6 System.Void UnityEngine.UI.Scrollbar::Set(System.Single,System.Boolean)
extern void Scrollbar_Set_m7648F8AF6180528CB0F764E8336619A3F5BDD6DD (void);
// 0x000003F7 System.Void UnityEngine.UI.Scrollbar::OnRectTransformDimensionsChange()
extern void Scrollbar_OnRectTransformDimensionsChange_m279735A03AD38298ADFB35679214B7694B11B60D (void);
// 0x000003F8 UnityEngine.UI.Scrollbar/Axis UnityEngine.UI.Scrollbar::get_axis()
extern void Scrollbar_get_axis_m98559873075F3EC100F759F77D85F125DF3EAD5F (void);
// 0x000003F9 System.Boolean UnityEngine.UI.Scrollbar::get_reverseValue()
extern void Scrollbar_get_reverseValue_mE21B1C18892A9E7A7B977092CA78D190DE9D2038 (void);
// 0x000003FA System.Void UnityEngine.UI.Scrollbar::UpdateVisuals()
extern void Scrollbar_UpdateVisuals_mF8DB48A1E85987DB7527BDF32DB6D8875CD87109 (void);
// 0x000003FB System.Void UnityEngine.UI.Scrollbar::UpdateDrag(UnityEngine.EventSystems.PointerEventData)
extern void Scrollbar_UpdateDrag_mB92938245162616F94C421E5AD88FA20E545170C (void);
// 0x000003FC System.Void UnityEngine.UI.Scrollbar::DoUpdateDrag(UnityEngine.Vector2,System.Single)
extern void Scrollbar_DoUpdateDrag_mE9E8F4734D1019CA486C2C42625A993929A85589 (void);
// 0x000003FD System.Boolean UnityEngine.UI.Scrollbar::MayDrag(UnityEngine.EventSystems.PointerEventData)
extern void Scrollbar_MayDrag_mFE3DD24CC14FF172474172E0B0C04FD08A812D17 (void);
// 0x000003FE System.Void UnityEngine.UI.Scrollbar::OnBeginDrag(UnityEngine.EventSystems.PointerEventData)
extern void Scrollbar_OnBeginDrag_m7E55F54116ED0E93C44F217F9605F7261CBA4201 (void);
// 0x000003FF System.Void UnityEngine.UI.Scrollbar::OnDrag(UnityEngine.EventSystems.PointerEventData)
extern void Scrollbar_OnDrag_mC731CB61B1B2DED2D282D5796340906A8385167E (void);
// 0x00000400 System.Void UnityEngine.UI.Scrollbar::OnPointerDown(UnityEngine.EventSystems.PointerEventData)
extern void Scrollbar_OnPointerDown_m35D22C28672555C90395DE39CAECF90D8920CAB7 (void);
// 0x00000401 System.Collections.IEnumerator UnityEngine.UI.Scrollbar::ClickRepeat(UnityEngine.EventSystems.PointerEventData)
extern void Scrollbar_ClickRepeat_mA194AE50BAE28AC44A0F5AD82C33B459C3BA8498 (void);
// 0x00000402 System.Collections.IEnumerator UnityEngine.UI.Scrollbar::ClickRepeat(UnityEngine.Vector2,UnityEngine.Camera)
extern void Scrollbar_ClickRepeat_m7F4E106BDED75AFD5041ED1EDBC71D0FCD00B1A9 (void);
// 0x00000403 System.Void UnityEngine.UI.Scrollbar::OnPointerUp(UnityEngine.EventSystems.PointerEventData)
extern void Scrollbar_OnPointerUp_m8487F735AF1B04F04B1C5A5AF0093825B34806F1 (void);
// 0x00000404 System.Void UnityEngine.UI.Scrollbar::OnMove(UnityEngine.EventSystems.AxisEventData)
extern void Scrollbar_OnMove_mB38F3B76B3176D3C68862C4A89B8FC602CBDEF15 (void);
// 0x00000405 UnityEngine.UI.Selectable UnityEngine.UI.Scrollbar::FindSelectableOnLeft()
extern void Scrollbar_FindSelectableOnLeft_m3C5EC37268434F881E19E5672EA618FFD003E542 (void);
// 0x00000406 UnityEngine.UI.Selectable UnityEngine.UI.Scrollbar::FindSelectableOnRight()
extern void Scrollbar_FindSelectableOnRight_m92A7E0729E7A8D574D7A4CAB6B5DC1AB877DB650 (void);
// 0x00000407 UnityEngine.UI.Selectable UnityEngine.UI.Scrollbar::FindSelectableOnUp()
extern void Scrollbar_FindSelectableOnUp_mC04DED87BF27E25DDCFBE2E856756306029610D7 (void);
// 0x00000408 UnityEngine.UI.Selectable UnityEngine.UI.Scrollbar::FindSelectableOnDown()
extern void Scrollbar_FindSelectableOnDown_m8A3AC1A349CDF57806A6052BFDA50F3E4923394E (void);
// 0x00000409 System.Void UnityEngine.UI.Scrollbar::OnInitializePotentialDrag(UnityEngine.EventSystems.PointerEventData)
extern void Scrollbar_OnInitializePotentialDrag_m3F193C72AFED340A173112D9FEBCE657612E1E17 (void);
// 0x0000040A System.Void UnityEngine.UI.Scrollbar::SetDirection(UnityEngine.UI.Scrollbar/Direction,System.Boolean)
extern void Scrollbar_SetDirection_mA2EC79BD55CF96E8FA6EF13C0507EDDBD3DB5AD1 (void);
// 0x0000040B UnityEngine.Transform UnityEngine.UI.Scrollbar::UnityEngine.UI.ICanvasElement.get_transform()
extern void Scrollbar_UnityEngine_UI_ICanvasElement_get_transform_mAA5CFBC4FA92695E11E09688C9A356E3F35D26C2 (void);
// 0x0000040C System.Void UnityEngine.UI.Scrollbar/ScrollEvent::.ctor()
extern void ScrollEvent__ctor_m44DC6D36587E09ACF8AC837A3251B4345559ACE8 (void);
// 0x0000040D System.Void UnityEngine.UI.Scrollbar/<ClickRepeat>d__58::.ctor(System.Int32)
extern void U3CClickRepeatU3Ed__58__ctor_m8873755FA35DD3F2FD77BC0BBF4790DEDAD63E34 (void);
// 0x0000040E System.Void UnityEngine.UI.Scrollbar/<ClickRepeat>d__58::System.IDisposable.Dispose()
extern void U3CClickRepeatU3Ed__58_System_IDisposable_Dispose_m53CB1C56045E24999148EA191B041CB6815839BA (void);
// 0x0000040F System.Boolean UnityEngine.UI.Scrollbar/<ClickRepeat>d__58::MoveNext()
extern void U3CClickRepeatU3Ed__58_MoveNext_mB0F8287D6C01B3C1F27761E3736E533046D2C813 (void);
// 0x00000410 System.Object UnityEngine.UI.Scrollbar/<ClickRepeat>d__58::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CClickRepeatU3Ed__58_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2D1D5B3A420C829CDCF5FF31E77ACBE2CF12C033 (void);
// 0x00000411 System.Void UnityEngine.UI.Scrollbar/<ClickRepeat>d__58::System.Collections.IEnumerator.Reset()
extern void U3CClickRepeatU3Ed__58_System_Collections_IEnumerator_Reset_m91332A8DFD75C0F222997DD4D8573EA9AAC27F36 (void);
// 0x00000412 System.Object UnityEngine.UI.Scrollbar/<ClickRepeat>d__58::System.Collections.IEnumerator.get_Current()
extern void U3CClickRepeatU3Ed__58_System_Collections_IEnumerator_get_Current_m52060FC222F29953E4BC040BC88CCC64EF3C8492 (void);
// 0x00000413 UnityEngine.UI.Selectable[] UnityEngine.UI.Selectable::get_allSelectablesArray()
extern void Selectable_get_allSelectablesArray_m82DC9617ED58417072BA0E125DBDFCBFAA0E6626 (void);
// 0x00000414 System.Int32 UnityEngine.UI.Selectable::get_allSelectableCount()
extern void Selectable_get_allSelectableCount_mC344C7D2980DDEC1F25FB2D38899936FFA82E431 (void);
// 0x00000415 System.Collections.Generic.List`1<UnityEngine.UI.Selectable> UnityEngine.UI.Selectable::get_allSelectables()
extern void Selectable_get_allSelectables_mD4895881690BC278354FA3EC9B10B4EE256C449A (void);
// 0x00000416 System.Int32 UnityEngine.UI.Selectable::AllSelectablesNoAlloc(UnityEngine.UI.Selectable[])
extern void Selectable_AllSelectablesNoAlloc_m48336092D38D0D80EC2EDBFD77AE43D86317911D (void);
// 0x00000417 UnityEngine.UI.Navigation UnityEngine.UI.Selectable::get_navigation()
extern void Selectable_get_navigation_m5E66BC477203E3245F9FCBE3EABE51A8003980C1 (void);
// 0x00000418 System.Void UnityEngine.UI.Selectable::set_navigation(UnityEngine.UI.Navigation)
extern void Selectable_set_navigation_mCFB3BDFA535CA2F42299049ACDF02F91E15B4542 (void);
// 0x00000419 UnityEngine.UI.Selectable/Transition UnityEngine.UI.Selectable::get_transition()
extern void Selectable_get_transition_m9F5ADA0325416768CDAEEF85478C9BDE4AB24ACC (void);
// 0x0000041A System.Void UnityEngine.UI.Selectable::set_transition(UnityEngine.UI.Selectable/Transition)
extern void Selectable_set_transition_m394926F279C7B5054C3D75AAA99788C48BB50343 (void);
// 0x0000041B UnityEngine.UI.ColorBlock UnityEngine.UI.Selectable::get_colors()
extern void Selectable_get_colors_m47C712DD0CFA000DAACD750853E81E981C90B7D9 (void);
// 0x0000041C System.Void UnityEngine.UI.Selectable::set_colors(UnityEngine.UI.ColorBlock)
extern void Selectable_set_colors_m2610F85E7DC191E0AA2D71E2447BA5B58B7C4621 (void);
// 0x0000041D UnityEngine.UI.SpriteState UnityEngine.UI.Selectable::get_spriteState()
extern void Selectable_get_spriteState_m3599C4C35FCA7427F27D7C6CAD8DCF31476718E6 (void);
// 0x0000041E System.Void UnityEngine.UI.Selectable::set_spriteState(UnityEngine.UI.SpriteState)
extern void Selectable_set_spriteState_mD0DB578B3D6404A8D5B38865CB5FCF812D881C87 (void);
// 0x0000041F UnityEngine.UI.AnimationTriggers UnityEngine.UI.Selectable::get_animationTriggers()
extern void Selectable_get_animationTriggers_m26FB70A2F465548564190F6991B6F427628C2573 (void);
// 0x00000420 System.Void UnityEngine.UI.Selectable::set_animationTriggers(UnityEngine.UI.AnimationTriggers)
extern void Selectable_set_animationTriggers_m6306E54670720009966BCFA605F86236DCAF06CE (void);
// 0x00000421 UnityEngine.UI.Graphic UnityEngine.UI.Selectable::get_targetGraphic()
extern void Selectable_get_targetGraphic_mF2D1BFB9DE33BAB553933533CC4A9135A37F4A72 (void);
// 0x00000422 System.Void UnityEngine.UI.Selectable::set_targetGraphic(UnityEngine.UI.Graphic)
extern void Selectable_set_targetGraphic_m668325D43E18A35560B4176477D004ECDB9C9604 (void);
// 0x00000423 System.Boolean UnityEngine.UI.Selectable::get_interactable()
extern void Selectable_get_interactable_m4231515CC3C861B431AA22FE3345F831389B3840 (void);
// 0x00000424 System.Void UnityEngine.UI.Selectable::set_interactable(System.Boolean)
extern void Selectable_set_interactable_mE6F57D33A9E0484377174D0F490C4372BF7F0D40 (void);
// 0x00000425 System.Boolean UnityEngine.UI.Selectable::get_isPointerInside()
extern void Selectable_get_isPointerInside_m16EF4963CD1916567154D40FA6CDFB3C05A18785 (void);
// 0x00000426 System.Void UnityEngine.UI.Selectable::set_isPointerInside(System.Boolean)
extern void Selectable_set_isPointerInside_m235566B1D1AC3989033566483BA78B3B6D65A080 (void);
// 0x00000427 System.Boolean UnityEngine.UI.Selectable::get_isPointerDown()
extern void Selectable_get_isPointerDown_m15EE53CB56493F9C65339EF06DBBFE35CF923526 (void);
// 0x00000428 System.Void UnityEngine.UI.Selectable::set_isPointerDown(System.Boolean)
extern void Selectable_set_isPointerDown_m8B64367E49F20925A0D7755CE456CA5E91E84BB5 (void);
// 0x00000429 System.Boolean UnityEngine.UI.Selectable::get_hasSelection()
extern void Selectable_get_hasSelection_m6D96C84C22E2B7B2E5044FFE7ED7F96B3C871235 (void);
// 0x0000042A System.Void UnityEngine.UI.Selectable::set_hasSelection(System.Boolean)
extern void Selectable_set_hasSelection_mCDC1198B44CED5A1B1A02881BC5E53FF574A19F3 (void);
// 0x0000042B System.Void UnityEngine.UI.Selectable::.ctor()
extern void Selectable__ctor_m71A423A365D0031DECFDAA82E5AC47BA4746834D (void);
// 0x0000042C UnityEngine.UI.Image UnityEngine.UI.Selectable::get_image()
extern void Selectable_get_image_mAB45C107C7C858ECBEFFFF540B8C69746BB6C6FE (void);
// 0x0000042D System.Void UnityEngine.UI.Selectable::set_image(UnityEngine.UI.Image)
extern void Selectable_set_image_m3D9DB0594D31F38D71AE41A9CA9FD494607E0E6B (void);
// 0x0000042E UnityEngine.Animator UnityEngine.UI.Selectable::get_animator()
extern void Selectable_get_animator_m64B7187172AE08620AFB3C616450AA63DA1C8AC3 (void);
// 0x0000042F System.Void UnityEngine.UI.Selectable::Awake()
extern void Selectable_Awake_mDC49DAA01529547D77A25CD8ABC91D0E38D2E881 (void);
// 0x00000430 System.Void UnityEngine.UI.Selectable::OnCanvasGroupChanged()
extern void Selectable_OnCanvasGroupChanged_m412845BE2E955ECC1B34C657EF72A654FD1C7AD2 (void);
// 0x00000431 System.Boolean UnityEngine.UI.Selectable::ParentGroupAllowsInteraction()
extern void Selectable_ParentGroupAllowsInteraction_m5C2EEEF999B684CE9C12430AB0B0152294D0C082 (void);
// 0x00000432 System.Boolean UnityEngine.UI.Selectable::IsInteractable()
extern void Selectable_IsInteractable_m166B7B80827A2AF092DFAE6C95DC7B45ADB57D9F (void);
// 0x00000433 System.Void UnityEngine.UI.Selectable::OnDidApplyAnimationProperties()
extern void Selectable_OnDidApplyAnimationProperties_mF971F5679B02796A1626742C7D4D66DFE6A9A122 (void);
// 0x00000434 System.Void UnityEngine.UI.Selectable::OnEnable()
extern void Selectable_OnEnable_m16A76B731BE2E80E08B910F30F060608659B11B6 (void);
// 0x00000435 System.Void UnityEngine.UI.Selectable::OnTransformParentChanged()
extern void Selectable_OnTransformParentChanged_m6FFE375C64847CC5182DAD896EFB600F325F8104 (void);
// 0x00000436 System.Void UnityEngine.UI.Selectable::OnSetProperty()
extern void Selectable_OnSetProperty_m0913D82035D767DDAF37F34AF4F82F395AC8663C (void);
// 0x00000437 System.Void UnityEngine.UI.Selectable::OnDisable()
extern void Selectable_OnDisable_m490A86E00A2060B312E8168C29BD26E9BED3F9D5 (void);
// 0x00000438 UnityEngine.UI.Selectable/SelectionState UnityEngine.UI.Selectable::get_currentSelectionState()
extern void Selectable_get_currentSelectionState_m2F4651DC6AA8CD09F3395F178523D937DFDFCD2E (void);
// 0x00000439 System.Void UnityEngine.UI.Selectable::InstantClearState()
extern void Selectable_InstantClearState_mDCA630365FB86AD677F7A6E34ADD3B46A7E83D8C (void);
// 0x0000043A System.Void UnityEngine.UI.Selectable::DoStateTransition(UnityEngine.UI.Selectable/SelectionState,System.Boolean)
extern void Selectable_DoStateTransition_mEFD5B374017C75F4FE9F9CC983ADC73E5A13B41E (void);
// 0x0000043B UnityEngine.UI.Selectable UnityEngine.UI.Selectable::FindSelectable(UnityEngine.Vector3)
extern void Selectable_FindSelectable_mA6D4C4B4AB595BB51BEAB0DF7A4308FF71D40FD5 (void);
// 0x0000043C UnityEngine.Vector3 UnityEngine.UI.Selectable::GetPointOnRectEdge(UnityEngine.RectTransform,UnityEngine.Vector2)
extern void Selectable_GetPointOnRectEdge_mF3EF4B71604DB85D1093F2F0B7F1588AECFF8B80 (void);
// 0x0000043D System.Void UnityEngine.UI.Selectable::Navigate(UnityEngine.EventSystems.AxisEventData,UnityEngine.UI.Selectable)
extern void Selectable_Navigate_mD1DC810617EEF6412FB9DF1E1AA1A9CBA659FF73 (void);
// 0x0000043E UnityEngine.UI.Selectable UnityEngine.UI.Selectable::FindSelectableOnLeft()
extern void Selectable_FindSelectableOnLeft_mB42E50642047189B486186AC74F0D8FCC4E06240 (void);
// 0x0000043F UnityEngine.UI.Selectable UnityEngine.UI.Selectable::FindSelectableOnRight()
extern void Selectable_FindSelectableOnRight_mD2EC5BC567595EDDD7316609F2C23FF5FF8F22C0 (void);
// 0x00000440 UnityEngine.UI.Selectable UnityEngine.UI.Selectable::FindSelectableOnUp()
extern void Selectable_FindSelectableOnUp_mA6F1D56B00532781BA9FB379E8E33F7B249C029B (void);
// 0x00000441 UnityEngine.UI.Selectable UnityEngine.UI.Selectable::FindSelectableOnDown()
extern void Selectable_FindSelectableOnDown_m548BF9DB6F72D1B836691C681232E9E4BAFD67AA (void);
// 0x00000442 System.Void UnityEngine.UI.Selectable::OnMove(UnityEngine.EventSystems.AxisEventData)
extern void Selectable_OnMove_m309528EE263D12F664BF2572A0FFD2AB2A12BD24 (void);
// 0x00000443 System.Void UnityEngine.UI.Selectable::StartColorTween(UnityEngine.Color,System.Boolean)
extern void Selectable_StartColorTween_mC0E53DA70ACFED66DA8E049922F22C4B54C2A4D1 (void);
// 0x00000444 System.Void UnityEngine.UI.Selectable::DoSpriteSwap(UnityEngine.Sprite)
extern void Selectable_DoSpriteSwap_mF4A2E7DEECC2A0E68A86AE207E0A8679566341B6 (void);
// 0x00000445 System.Void UnityEngine.UI.Selectable::TriggerAnimation(System.String)
extern void Selectable_TriggerAnimation_m7DA58CA3E8C1A946C0926B461F9FB668498A5624 (void);
// 0x00000446 System.Boolean UnityEngine.UI.Selectable::IsHighlighted()
extern void Selectable_IsHighlighted_m5E76C4938BF5CCF299CF7F2A4D67FFDB7C5303A9 (void);
// 0x00000447 System.Boolean UnityEngine.UI.Selectable::IsPressed()
extern void Selectable_IsPressed_mCEF0D241284AE90C08B093CA0ADFA2A485007746 (void);
// 0x00000448 System.Void UnityEngine.UI.Selectable::EvaluateAndTransitionToSelectionState()
extern void Selectable_EvaluateAndTransitionToSelectionState_mEA0BA58511B6D5755BADB7E9CEAEF42491AEB920 (void);
// 0x00000449 System.Void UnityEngine.UI.Selectable::OnPointerDown(UnityEngine.EventSystems.PointerEventData)
extern void Selectable_OnPointerDown_mECD8313A4900B647F476CCF596DCF9C92B32F2AA (void);
// 0x0000044A System.Void UnityEngine.UI.Selectable::OnPointerUp(UnityEngine.EventSystems.PointerEventData)
extern void Selectable_OnPointerUp_mCC5A82EF3E566BE6720348BA5913A8461D7B3ECD (void);
// 0x0000044B System.Void UnityEngine.UI.Selectable::OnPointerEnter(UnityEngine.EventSystems.PointerEventData)
extern void Selectable_OnPointerEnter_mECC5B6BB8CCE06CF95FA837E73F0C83F46160542 (void);
// 0x0000044C System.Void UnityEngine.UI.Selectable::OnPointerExit(UnityEngine.EventSystems.PointerEventData)
extern void Selectable_OnPointerExit_m5A0E6FE0E9BAC0DA39F812B7A390FBEC1CACBFE7 (void);
// 0x0000044D System.Void UnityEngine.UI.Selectable::OnSelect(UnityEngine.EventSystems.BaseEventData)
extern void Selectable_OnSelect_m6FD18DBF52BD234A676B3BF43DA2A3CD7FC63230 (void);
// 0x0000044E System.Void UnityEngine.UI.Selectable::OnDeselect(UnityEngine.EventSystems.BaseEventData)
extern void Selectable_OnDeselect_m2E0FC2E14667F2348242BB9FB1611BE9A79C2FDA (void);
// 0x0000044F System.Void UnityEngine.UI.Selectable::Select()
extern void Selectable_Select_m8C877D0983976C5CE6A70B2219679B1F42B0971E (void);
// 0x00000450 System.Void UnityEngine.UI.Selectable::.cctor()
extern void Selectable__cctor_m0A91753D744D8E5A286F83E8383D58F169ACA3A2 (void);
// 0x00000451 System.Boolean UnityEngine.UI.SetPropertyUtility::SetColor(UnityEngine.Color&,UnityEngine.Color)
extern void SetPropertyUtility_SetColor_mAB8BF8D69240EE5931FB5A53C2434867C48A6616 (void);
// 0x00000452 System.Boolean UnityEngine.UI.SetPropertyUtility::SetStruct(T&,T)
// 0x00000453 System.Boolean UnityEngine.UI.SetPropertyUtility::SetClass(T&,T)
// 0x00000454 UnityEngine.RectTransform UnityEngine.UI.Slider::get_fillRect()
extern void Slider_get_fillRect_m4D02B70BFAA5C003B34E8132C10CB80A0F022CAA (void);
// 0x00000455 System.Void UnityEngine.UI.Slider::set_fillRect(UnityEngine.RectTransform)
extern void Slider_set_fillRect_mDD7F4FD79D92C9ED5D005612E4BF083D56560DCD (void);
// 0x00000456 UnityEngine.RectTransform UnityEngine.UI.Slider::get_handleRect()
extern void Slider_get_handleRect_mE1B614E89D5DFE5CED40D3F0BF4BE30871BC81D0 (void);
// 0x00000457 System.Void UnityEngine.UI.Slider::set_handleRect(UnityEngine.RectTransform)
extern void Slider_set_handleRect_m51044F33CE9CD375EC7FB1D41B420FA7B44D6086 (void);
// 0x00000458 UnityEngine.UI.Slider/Direction UnityEngine.UI.Slider::get_direction()
extern void Slider_get_direction_m599FD5897AEAD92856C45A6905F9AF62705746D5 (void);
// 0x00000459 System.Void UnityEngine.UI.Slider::set_direction(UnityEngine.UI.Slider/Direction)
extern void Slider_set_direction_m1D8BE0408B11A471327AD2CC5B1DB0169315DC7F (void);
// 0x0000045A System.Single UnityEngine.UI.Slider::get_minValue()
extern void Slider_get_minValue_m7B5A89FDE9916A4A111BDB91648750E23C034B08 (void);
// 0x0000045B System.Void UnityEngine.UI.Slider::set_minValue(System.Single)
extern void Slider_set_minValue_m253C0E27C8B0275EDAAFD9E97F6DC1E426460F93 (void);
// 0x0000045C System.Single UnityEngine.UI.Slider::get_maxValue()
extern void Slider_get_maxValue_m369FF59A4AEC91348D79BF1906F4012A2A850959 (void);
// 0x0000045D System.Void UnityEngine.UI.Slider::set_maxValue(System.Single)
extern void Slider_set_maxValue_m5CDA3D451B68CF2D3FCFF43D1738D1DCC1C6425B (void);
// 0x0000045E System.Boolean UnityEngine.UI.Slider::get_wholeNumbers()
extern void Slider_get_wholeNumbers_m1D891AB6E780B340CA0EA364C7DF7425186930F6 (void);
// 0x0000045F System.Void UnityEngine.UI.Slider::set_wholeNumbers(System.Boolean)
extern void Slider_set_wholeNumbers_mF278D1746B80BB0CAEC84362D060D12CB2DB0134 (void);
// 0x00000460 System.Single UnityEngine.UI.Slider::get_value()
extern void Slider_get_value_m787B367CC0F4BD00041FD80F297014DDA4C2D846 (void);
// 0x00000461 System.Void UnityEngine.UI.Slider::set_value(System.Single)
extern void Slider_set_value_mC53042FE25A4FD98D41FDB23D9DDC0BFE935BB80 (void);
// 0x00000462 System.Void UnityEngine.UI.Slider::SetValueWithoutNotify(System.Single)
extern void Slider_SetValueWithoutNotify_mDA8676600809F9207AC51FAADA2D757BA5FA35BE (void);
// 0x00000463 System.Single UnityEngine.UI.Slider::get_normalizedValue()
extern void Slider_get_normalizedValue_m09A06767F3E8064200CA1C954AF5C362C5138EC3 (void);
// 0x00000464 System.Void UnityEngine.UI.Slider::set_normalizedValue(System.Single)
extern void Slider_set_normalizedValue_m33A334123C4869919B6CF52711B4938F82AE2D41 (void);
// 0x00000465 UnityEngine.UI.Slider/SliderEvent UnityEngine.UI.Slider::get_onValueChanged()
extern void Slider_get_onValueChanged_m7F480C569A6D668952BE1436691850D13825E129 (void);
// 0x00000466 System.Void UnityEngine.UI.Slider::set_onValueChanged(UnityEngine.UI.Slider/SliderEvent)
extern void Slider_set_onValueChanged_mBB24A666AC4F2DA637732CD77A41C014040782EE (void);
// 0x00000467 System.Single UnityEngine.UI.Slider::get_stepSize()
extern void Slider_get_stepSize_m4C4B9C8E3DD4989847E9770B0EAD66069DFB5885 (void);
// 0x00000468 System.Void UnityEngine.UI.Slider::.ctor()
extern void Slider__ctor_mFC18E800E96F11533382DCECDE8D2F160DA19947 (void);
// 0x00000469 System.Void UnityEngine.UI.Slider::Rebuild(UnityEngine.UI.CanvasUpdate)
extern void Slider_Rebuild_m35C747EFB7CC6D45BE2BB7DA8416FAE449BC4271 (void);
// 0x0000046A System.Void UnityEngine.UI.Slider::LayoutComplete()
extern void Slider_LayoutComplete_m6E92D0C7DF2A6B1F2F63BCEE44472071ABC78651 (void);
// 0x0000046B System.Void UnityEngine.UI.Slider::GraphicUpdateComplete()
extern void Slider_GraphicUpdateComplete_m7103B31E0991F4883DEF776797B3B839F3DDA827 (void);
// 0x0000046C System.Void UnityEngine.UI.Slider::OnEnable()
extern void Slider_OnEnable_m11539744EC2B374C6B1AABE7F2909457DD956022 (void);
// 0x0000046D System.Void UnityEngine.UI.Slider::OnDisable()
extern void Slider_OnDisable_mAEBBE8222ABEE88BFEF4A2209CA61CCD39BDCFFA (void);
// 0x0000046E System.Void UnityEngine.UI.Slider::Update()
extern void Slider_Update_m149A2FFECFAE8AA377777BCA848B0CAB0CB06694 (void);
// 0x0000046F System.Void UnityEngine.UI.Slider::OnDidApplyAnimationProperties()
extern void Slider_OnDidApplyAnimationProperties_m8AA149F0F3E09E93F124971B5A1C94F8DB9970FC (void);
// 0x00000470 System.Void UnityEngine.UI.Slider::UpdateCachedReferences()
extern void Slider_UpdateCachedReferences_m07895017E8F07A1F6129E769D1F6A43FEF453BDC (void);
// 0x00000471 System.Single UnityEngine.UI.Slider::ClampValue(System.Single)
extern void Slider_ClampValue_m21176A1E21326F10E784782303DDBEE004FB6435 (void);
// 0x00000472 System.Void UnityEngine.UI.Slider::Set(System.Single,System.Boolean)
extern void Slider_Set_m706D4F83CA9A006F1AC1ABB1AE8E165DACB8FA5F (void);
// 0x00000473 System.Void UnityEngine.UI.Slider::OnRectTransformDimensionsChange()
extern void Slider_OnRectTransformDimensionsChange_mB7578390513E3FB21BA08079225CE4CF751D640F (void);
// 0x00000474 UnityEngine.UI.Slider/Axis UnityEngine.UI.Slider::get_axis()
extern void Slider_get_axis_mE52AD1AC11A9A6B0930E2A1C055F94234AD4BEFA (void);
// 0x00000475 System.Boolean UnityEngine.UI.Slider::get_reverseValue()
extern void Slider_get_reverseValue_mD1BF64576A93FEC0E1394B5DAFAD3F05F7D29205 (void);
// 0x00000476 System.Void UnityEngine.UI.Slider::UpdateVisuals()
extern void Slider_UpdateVisuals_m6985F921D2E0C14800D51257ABEA5784736243E7 (void);
// 0x00000477 System.Void UnityEngine.UI.Slider::UpdateDrag(UnityEngine.EventSystems.PointerEventData,UnityEngine.Camera)
extern void Slider_UpdateDrag_m7E812610D0F98C7CC8CD45A7C2774B93010C9143 (void);
// 0x00000478 System.Boolean UnityEngine.UI.Slider::MayDrag(UnityEngine.EventSystems.PointerEventData)
extern void Slider_MayDrag_m81F9CDAF63CC4CB6661BE3C6D669F222C3DC105E (void);
// 0x00000479 System.Void UnityEngine.UI.Slider::OnPointerDown(UnityEngine.EventSystems.PointerEventData)
extern void Slider_OnPointerDown_mD311547B8584197150D3B19317E8FD4CF74C5C69 (void);
// 0x0000047A System.Void UnityEngine.UI.Slider::OnDrag(UnityEngine.EventSystems.PointerEventData)
extern void Slider_OnDrag_mE7E7AD8A03992B99DAF76EF3A5F3DC114A917F3F (void);
// 0x0000047B System.Void UnityEngine.UI.Slider::OnMove(UnityEngine.EventSystems.AxisEventData)
extern void Slider_OnMove_mAD1581FCC361E0A2BB300C001B45015FCE53F911 (void);
// 0x0000047C UnityEngine.UI.Selectable UnityEngine.UI.Slider::FindSelectableOnLeft()
extern void Slider_FindSelectableOnLeft_mF26C5F8012D02EF3418D5B6078A40D611C331828 (void);
// 0x0000047D UnityEngine.UI.Selectable UnityEngine.UI.Slider::FindSelectableOnRight()
extern void Slider_FindSelectableOnRight_m5F2861D5679DA3CD1CCF6263DF8FAEC1368325AD (void);
// 0x0000047E UnityEngine.UI.Selectable UnityEngine.UI.Slider::FindSelectableOnUp()
extern void Slider_FindSelectableOnUp_mFD9FF36CD4AADF0062D3C8DE37DDC3A95BB896FA (void);
// 0x0000047F UnityEngine.UI.Selectable UnityEngine.UI.Slider::FindSelectableOnDown()
extern void Slider_FindSelectableOnDown_mFF49AB2F8816150590F280859E07A78D85E1ADBF (void);
// 0x00000480 System.Void UnityEngine.UI.Slider::OnInitializePotentialDrag(UnityEngine.EventSystems.PointerEventData)
extern void Slider_OnInitializePotentialDrag_m13553E8E4A0FDD955FE69E888CDAE144EEA16F09 (void);
// 0x00000481 System.Void UnityEngine.UI.Slider::SetDirection(UnityEngine.UI.Slider/Direction,System.Boolean)
extern void Slider_SetDirection_m7A4187E11D4F9F3701253C2EDCA3B58769DDC006 (void);
// 0x00000482 UnityEngine.Transform UnityEngine.UI.Slider::UnityEngine.UI.ICanvasElement.get_transform()
extern void Slider_UnityEngine_UI_ICanvasElement_get_transform_m10BC38EEE32C2A84D963626863CD81C6DC011C37 (void);
// 0x00000483 System.Void UnityEngine.UI.Slider/SliderEvent::.ctor()
extern void SliderEvent__ctor_m9D53B3806FC27FCFEB6B8EE6CF86FD7257DC0E6F (void);
// 0x00000484 UnityEngine.Sprite UnityEngine.UI.SpriteState::get_highlightedSprite()
extern void SpriteState_get_highlightedSprite_m695FD2C0827908CBAFFF5D5033FEED380D4219FA (void);
// 0x00000485 System.Void UnityEngine.UI.SpriteState::set_highlightedSprite(UnityEngine.Sprite)
extern void SpriteState_set_highlightedSprite_m3B5F7EF5AF584C6917BA3FB7155701F697B6070D (void);
// 0x00000486 UnityEngine.Sprite UnityEngine.UI.SpriteState::get_pressedSprite()
extern void SpriteState_get_pressedSprite_mDCEB9F07BDD7C2CFCDC7F7680D05B47EA71965D6 (void);
// 0x00000487 System.Void UnityEngine.UI.SpriteState::set_pressedSprite(UnityEngine.Sprite)
extern void SpriteState_set_pressedSprite_m21C5C37D35A794F750D6D4A95F794633B9027602 (void);
// 0x00000488 UnityEngine.Sprite UnityEngine.UI.SpriteState::get_selectedSprite()
extern void SpriteState_get_selectedSprite_mA85714CC6BF3801A63CC42B026E66CEDFD36949E (void);
// 0x00000489 System.Void UnityEngine.UI.SpriteState::set_selectedSprite(UnityEngine.Sprite)
extern void SpriteState_set_selectedSprite_m00EC0C38B3ADBA12D9524CAE982BE8B21F608A54 (void);
// 0x0000048A UnityEngine.Sprite UnityEngine.UI.SpriteState::get_disabledSprite()
extern void SpriteState_get_disabledSprite_m7AF976C63DA03ED035B031D5A98413C39894F50C (void);
// 0x0000048B System.Void UnityEngine.UI.SpriteState::set_disabledSprite(UnityEngine.Sprite)
extern void SpriteState_set_disabledSprite_mB368418E0E6ED9F220570BC9F066C6B6BF227B13 (void);
// 0x0000048C System.Boolean UnityEngine.UI.SpriteState::Equals(UnityEngine.UI.SpriteState)
extern void SpriteState_Equals_m2190A8BFFC45EC86766FC68C808F3DFE18E35827 (void);
// 0x0000048D UnityEngine.Material UnityEngine.UI.StencilMaterial::Add(UnityEngine.Material,System.Int32)
extern void StencilMaterial_Add_m6C73C30371938A7BF27E5D1585FF2CBFF8110D94 (void);
// 0x0000048E UnityEngine.Material UnityEngine.UI.StencilMaterial::Add(UnityEngine.Material,System.Int32,UnityEngine.Rendering.StencilOp,UnityEngine.Rendering.CompareFunction,UnityEngine.Rendering.ColorWriteMask)
extern void StencilMaterial_Add_mD22425AE22272F987211266F6A96AB28F4B96CE3 (void);
// 0x0000048F System.Void UnityEngine.UI.StencilMaterial::LogWarningWhenNotInBatchmode(System.String,UnityEngine.Object)
extern void StencilMaterial_LogWarningWhenNotInBatchmode_m4FEF061F9A2DD6EC8535E354E891C2C7D7B3C0EB (void);
// 0x00000490 UnityEngine.Material UnityEngine.UI.StencilMaterial::Add(UnityEngine.Material,System.Int32,UnityEngine.Rendering.StencilOp,UnityEngine.Rendering.CompareFunction,UnityEngine.Rendering.ColorWriteMask,System.Int32,System.Int32)
extern void StencilMaterial_Add_mC403BBB08D41E5B06FC6BAAA925D1EB944E8FA88 (void);
// 0x00000491 System.Void UnityEngine.UI.StencilMaterial::Remove(UnityEngine.Material)
extern void StencilMaterial_Remove_m74F6B3C15F50B2873959CF6B9D58801AEF4EA964 (void);
// 0x00000492 System.Void UnityEngine.UI.StencilMaterial::ClearAll()
extern void StencilMaterial_ClearAll_m9F8AA57D8DFB4F1B24D7AF781CC1C9645A37A8C8 (void);
// 0x00000493 System.Void UnityEngine.UI.StencilMaterial::.cctor()
extern void StencilMaterial__cctor_mA9660B29DCE5AABDD4F2DF6119672AB7593895F9 (void);
// 0x00000494 System.Void UnityEngine.UI.StencilMaterial/MatEntry::.ctor()
extern void MatEntry__ctor_mE5E902719906D17EAC17E5861CD3A6BB91B913A0 (void);
// 0x00000495 System.Void UnityEngine.UI.Text::.ctor()
extern void Text__ctor_mB8DEA3FF32B10CF70732F6F91D4B144506689FE7 (void);
// 0x00000496 UnityEngine.TextGenerator UnityEngine.UI.Text::get_cachedTextGenerator()
extern void Text_get_cachedTextGenerator_mC1CA3F78904E1B2E5759DEA6EFDB1C13AB3BBB65 (void);
// 0x00000497 UnityEngine.TextGenerator UnityEngine.UI.Text::get_cachedTextGeneratorForLayout()
extern void Text_get_cachedTextGeneratorForLayout_m464140899A674C970F9BBAD836EDDC1AD74DFF66 (void);
// 0x00000498 UnityEngine.Texture UnityEngine.UI.Text::get_mainTexture()
extern void Text_get_mainTexture_m3B1A372943D77082ED6C76201D5EB90AFC100991 (void);
// 0x00000499 System.Void UnityEngine.UI.Text::FontTextureChanged()
extern void Text_FontTextureChanged_mF4E2911B04AD15B2C482113179EB78653D6C33DF (void);
// 0x0000049A UnityEngine.Font UnityEngine.UI.Text::get_font()
extern void Text_get_font_m8D2D6709C3C35D54331B6DB56F2CBBC929FFA86C (void);
// 0x0000049B System.Void UnityEngine.UI.Text::set_font(UnityEngine.Font)
extern void Text_set_font_m10F529719C942343F7B963D28480A20940CD0B52 (void);
// 0x0000049C System.String UnityEngine.UI.Text::get_text()
extern void Text_get_text_mA9A5B551F443FE797B101B12A8F109CBFE82251A (void);
// 0x0000049D System.Void UnityEngine.UI.Text::set_text(System.String)
extern void Text_set_text_mD2EA82E603CB39FCAE5BDFAE21E5D747307E4239 (void);
// 0x0000049E System.Boolean UnityEngine.UI.Text::get_supportRichText()
extern void Text_get_supportRichText_mE259102B63D4404BA6C997BF184546FEADBECE89 (void);
// 0x0000049F System.Void UnityEngine.UI.Text::set_supportRichText(System.Boolean)
extern void Text_set_supportRichText_m62254112F808CB0F15355E81FEA2C8174FC6D66D (void);
// 0x000004A0 System.Boolean UnityEngine.UI.Text::get_resizeTextForBestFit()
extern void Text_get_resizeTextForBestFit_m3569E556E4152E54CF17021CCE84F31911EBCCD1 (void);
// 0x000004A1 System.Void UnityEngine.UI.Text::set_resizeTextForBestFit(System.Boolean)
extern void Text_set_resizeTextForBestFit_m0FFB3B0A6531A963B2A55122CF2903AE87CAEBE7 (void);
// 0x000004A2 System.Int32 UnityEngine.UI.Text::get_resizeTextMinSize()
extern void Text_get_resizeTextMinSize_mBCEDC46AF1B0F0BF2F282B6EB1A35AA86DEC5D39 (void);
// 0x000004A3 System.Void UnityEngine.UI.Text::set_resizeTextMinSize(System.Int32)
extern void Text_set_resizeTextMinSize_m78D4AF5080EFFB22D06943643D1F6771FAE617D6 (void);
// 0x000004A4 System.Int32 UnityEngine.UI.Text::get_resizeTextMaxSize()
extern void Text_get_resizeTextMaxSize_m7F3E89688C621994454CD6507FBD711EFF630C8B (void);
// 0x000004A5 System.Void UnityEngine.UI.Text::set_resizeTextMaxSize(System.Int32)
extern void Text_set_resizeTextMaxSize_m94CD82876E80E3391B1FD5FF5140ACAACA2C5CF2 (void);
// 0x000004A6 UnityEngine.TextAnchor UnityEngine.UI.Text::get_alignment()
extern void Text_get_alignment_m815A072002DEFDA14F8D523DE96403F3D70B2BBA (void);
// 0x000004A7 System.Void UnityEngine.UI.Text::set_alignment(UnityEngine.TextAnchor)
extern void Text_set_alignment_mBA9424D5CCC6FB11861B67A40E0C0F6DDBFDAB2C (void);
// 0x000004A8 System.Boolean UnityEngine.UI.Text::get_alignByGeometry()
extern void Text_get_alignByGeometry_m822AE54A8A9E711E634E9EE0DD5D23B410AFC6D6 (void);
// 0x000004A9 System.Void UnityEngine.UI.Text::set_alignByGeometry(System.Boolean)
extern void Text_set_alignByGeometry_m6AF8EDE5E32FC1126F3C7C8D3D794C0EDDBC52E7 (void);
// 0x000004AA System.Int32 UnityEngine.UI.Text::get_fontSize()
extern void Text_get_fontSize_m63951F82E2028B2AAFCB4FEF0C4E6464370AE72A (void);
// 0x000004AB System.Void UnityEngine.UI.Text::set_fontSize(System.Int32)
extern void Text_set_fontSize_m0D32489043916BCE64E51E0BDFCC12AC1B829411 (void);
// 0x000004AC UnityEngine.HorizontalWrapMode UnityEngine.UI.Text::get_horizontalOverflow()
extern void Text_get_horizontalOverflow_m8613B65EAC12945ADC0CA56C742198DB59FB5BBE (void);
// 0x000004AD System.Void UnityEngine.UI.Text::set_horizontalOverflow(UnityEngine.HorizontalWrapMode)
extern void Text_set_horizontalOverflow_m2D8B7DD9E784AE082C388FE483CFDB296950F60B (void);
// 0x000004AE UnityEngine.VerticalWrapMode UnityEngine.UI.Text::get_verticalOverflow()
extern void Text_get_verticalOverflow_mC80112C002317C58616F6CBC2489A113A2E3ECA8 (void);
// 0x000004AF System.Void UnityEngine.UI.Text::set_verticalOverflow(UnityEngine.VerticalWrapMode)
extern void Text_set_verticalOverflow_mBB9FA4C8CA6236354168B93AB6666BEB4D82D0BD (void);
// 0x000004B0 System.Single UnityEngine.UI.Text::get_lineSpacing()
extern void Text_get_lineSpacing_mA977B63BADFC5E82528B7B010E450B57F3BBCD09 (void);
// 0x000004B1 System.Void UnityEngine.UI.Text::set_lineSpacing(System.Single)
extern void Text_set_lineSpacing_mBEFCEE561D8E0827A7E0CD65BBAF9CD6A1C944F1 (void);
// 0x000004B2 UnityEngine.FontStyle UnityEngine.UI.Text::get_fontStyle()
extern void Text_get_fontStyle_m5068017317D8AE127A308676FD1AD98E6CCFE4A7 (void);
// 0x000004B3 System.Void UnityEngine.UI.Text::set_fontStyle(UnityEngine.FontStyle)
extern void Text_set_fontStyle_mA7869224CE4806D14EC614E68DB3873C6C2D54DD (void);
// 0x000004B4 System.Single UnityEngine.UI.Text::get_pixelsPerUnit()
extern void Text_get_pixelsPerUnit_mE181D725EA8DB4E273C725DFC9C9AA9712C8804A (void);
// 0x000004B5 System.Void UnityEngine.UI.Text::OnEnable()
extern void Text_OnEnable_m868042773CA7FE4508671FB715140B0FCAAD79F3 (void);
// 0x000004B6 System.Void UnityEngine.UI.Text::OnDisable()
extern void Text_OnDisable_m678E9C3DB6A7DBDA6F55D454627C91F99B173A4E (void);
// 0x000004B7 System.Void UnityEngine.UI.Text::UpdateGeometry()
extern void Text_UpdateGeometry_m8CC00C6CE511C1CBD96E138025788A6DFDBFC9BC (void);
// 0x000004B8 System.Void UnityEngine.UI.Text::AssignDefaultFont()
extern void Text_AssignDefaultFont_m480C1C4CC4F14095138E0E82EDBDD434D1545B0F (void);
// 0x000004B9 UnityEngine.TextGenerationSettings UnityEngine.UI.Text::GetGenerationSettings(UnityEngine.Vector2)
extern void Text_GetGenerationSettings_m7ADF67C21E79A53624FCF42CE828C9BF57FA98CE (void);
// 0x000004BA UnityEngine.Vector2 UnityEngine.UI.Text::GetTextAnchorPivot(UnityEngine.TextAnchor)
extern void Text_GetTextAnchorPivot_mF42A2C864A8909F356D4C51819F12FEB9C57ED20 (void);
// 0x000004BB System.Void UnityEngine.UI.Text::OnPopulateMesh(UnityEngine.UI.VertexHelper)
extern void Text_OnPopulateMesh_m4E8D3BD94E7F21D6D0887B0A0BC8F40389A1C778 (void);
// 0x000004BC System.Void UnityEngine.UI.Text::CalculateLayoutInputHorizontal()
extern void Text_CalculateLayoutInputHorizontal_mB2B8BAA95A0D8A825CB20C7A919EE9D857580139 (void);
// 0x000004BD System.Void UnityEngine.UI.Text::CalculateLayoutInputVertical()
extern void Text_CalculateLayoutInputVertical_mEF4CCC05582EC841C0CB0C0F786213E78C64B13B (void);
// 0x000004BE System.Single UnityEngine.UI.Text::get_minWidth()
extern void Text_get_minWidth_m31CB8C5C847BF46105FE2E6186AB801446D9FB9D (void);
// 0x000004BF System.Single UnityEngine.UI.Text::get_preferredWidth()
extern void Text_get_preferredWidth_m7FEDB1F56EC6BC313DE2F8CBB443CEA29CCB3E8C (void);
// 0x000004C0 System.Single UnityEngine.UI.Text::get_flexibleWidth()
extern void Text_get_flexibleWidth_mB59646E08036BC4316208E3911F29A46A8BD2322 (void);
// 0x000004C1 System.Single UnityEngine.UI.Text::get_minHeight()
extern void Text_get_minHeight_m092B0806C09C26E338CCD04670E3CD1356789016 (void);
// 0x000004C2 System.Single UnityEngine.UI.Text::get_preferredHeight()
extern void Text_get_preferredHeight_mD0CDFAE12ADBF007F24A5B895CB2ADE526219AC8 (void);
// 0x000004C3 System.Single UnityEngine.UI.Text::get_flexibleHeight()
extern void Text_get_flexibleHeight_mFF13C6F1C12057AE3757E99A2449E5F13EE6966A (void);
// 0x000004C4 System.Int32 UnityEngine.UI.Text::get_layoutPriority()
extern void Text_get_layoutPriority_mBB3F6A8BB6C56D9EEFA85D69F84A5F52867FE158 (void);
// 0x000004C5 System.Void UnityEngine.UI.Text::.cctor()
extern void Text__cctor_m5F39DABD01825657CD9AF1D6D6BDE5C9C06F9D56 (void);
// 0x000004C6 UnityEngine.UI.ToggleGroup UnityEngine.UI.Toggle::get_group()
extern void Toggle_get_group_m32DE73FB5899A95C8BC31B377F31F8D5167D2BE2 (void);
// 0x000004C7 System.Void UnityEngine.UI.Toggle::set_group(UnityEngine.UI.ToggleGroup)
extern void Toggle_set_group_mDE3F57C5F225B7A7856F40A7AB6CA1A22C0C2B23 (void);
// 0x000004C8 System.Void UnityEngine.UI.Toggle::.ctor()
extern void Toggle__ctor_m73EB2B8A5201BDE4789E0317CF7F8D66A22F392E (void);
// 0x000004C9 System.Void UnityEngine.UI.Toggle::Rebuild(UnityEngine.UI.CanvasUpdate)
extern void Toggle_Rebuild_m03534F97F2ED9B61AF2E01F07B13A59B425DDA11 (void);
// 0x000004CA System.Void UnityEngine.UI.Toggle::LayoutComplete()
extern void Toggle_LayoutComplete_m1E1D7A8F53C7AE28B65D6F7CFF406D093D875A66 (void);
// 0x000004CB System.Void UnityEngine.UI.Toggle::GraphicUpdateComplete()
extern void Toggle_GraphicUpdateComplete_m68CA8BA30F7C56559E8CBEAFA28EB2B25F9E9EB6 (void);
// 0x000004CC System.Void UnityEngine.UI.Toggle::OnDestroy()
extern void Toggle_OnDestroy_m2E15215B509E798734CDFFECA1146A749ADC9A0F (void);
// 0x000004CD System.Void UnityEngine.UI.Toggle::OnEnable()
extern void Toggle_OnEnable_m88F408A8D38B70537BAEF7919CAF8AC33F32BB53 (void);
// 0x000004CE System.Void UnityEngine.UI.Toggle::OnDisable()
extern void Toggle_OnDisable_m01F709F5D7780EB81C27B55DE74674DF3B4322DC (void);
// 0x000004CF System.Void UnityEngine.UI.Toggle::OnDidApplyAnimationProperties()
extern void Toggle_OnDidApplyAnimationProperties_m0662478457D843C95CC4689CF0C46E1FF2052B3D (void);
// 0x000004D0 System.Void UnityEngine.UI.Toggle::SetToggleGroup(UnityEngine.UI.ToggleGroup,System.Boolean)
extern void Toggle_SetToggleGroup_m50058F84A8AD3CF060D50147D7DF0FD9DA8FDD12 (void);
// 0x000004D1 System.Boolean UnityEngine.UI.Toggle::get_isOn()
extern void Toggle_get_isOn_m2B1F3640101A6FCDA6B5AF27924FFD10E3A89A6C (void);
// 0x000004D2 System.Void UnityEngine.UI.Toggle::set_isOn(System.Boolean)
extern void Toggle_set_isOn_mB018B9F410D7236AAB71D6D1A5BACC64C891F507 (void);
// 0x000004D3 System.Void UnityEngine.UI.Toggle::SetIsOnWithoutNotify(System.Boolean)
extern void Toggle_SetIsOnWithoutNotify_mD07469424A970A7894F38F2AE3A84CC465AE7952 (void);
// 0x000004D4 System.Void UnityEngine.UI.Toggle::Set(System.Boolean,System.Boolean)
extern void Toggle_Set_mDFEF33CCBD142D223B80FEBA43C75DD3A0ECA312 (void);
// 0x000004D5 System.Void UnityEngine.UI.Toggle::PlayEffect(System.Boolean)
extern void Toggle_PlayEffect_m60130B573D4FA4821127FFAFB1D1822315D5ACAA (void);
// 0x000004D6 System.Void UnityEngine.UI.Toggle::Start()
extern void Toggle_Start_mA6FA457EBD527A089B6B195C134C971F94918813 (void);
// 0x000004D7 System.Void UnityEngine.UI.Toggle::InternalToggle()
extern void Toggle_InternalToggle_m3C04FA487B0F311CD814F7C6796D1F8EEBF9A594 (void);
// 0x000004D8 System.Void UnityEngine.UI.Toggle::OnPointerClick(UnityEngine.EventSystems.PointerEventData)
extern void Toggle_OnPointerClick_m917A59AE7AE323514F157EB7FF38BE346D1EC0EA (void);
// 0x000004D9 System.Void UnityEngine.UI.Toggle::OnSubmit(UnityEngine.EventSystems.BaseEventData)
extern void Toggle_OnSubmit_mD0E022F5E0799162461A46EF25BE058B47C14EDC (void);
// 0x000004DA UnityEngine.Transform UnityEngine.UI.Toggle::UnityEngine.UI.ICanvasElement.get_transform()
extern void Toggle_UnityEngine_UI_ICanvasElement_get_transform_m824C6DB82B23058726C8ACE7F39AF72DE9125FBA (void);
// 0x000004DB System.Void UnityEngine.UI.Toggle/ToggleEvent::.ctor()
extern void ToggleEvent__ctor_m8B27AC4348B70FDEF171E184CE39A0B40CD07022 (void);
// 0x000004DC System.Boolean UnityEngine.UI.ToggleGroup::get_allowSwitchOff()
extern void ToggleGroup_get_allowSwitchOff_m970C9B6CFCC408D8146B2D4100780E6BECC080F0 (void);
// 0x000004DD System.Void UnityEngine.UI.ToggleGroup::set_allowSwitchOff(System.Boolean)
extern void ToggleGroup_set_allowSwitchOff_mFA7B1BA141BA27AF0B25FDACA84DCE31544828FC (void);
// 0x000004DE System.Void UnityEngine.UI.ToggleGroup::.ctor()
extern void ToggleGroup__ctor_mEB26AD500D667CC983BEB0E7F34B13145480A395 (void);
// 0x000004DF System.Void UnityEngine.UI.ToggleGroup::Start()
extern void ToggleGroup_Start_mD457A294157374B7A5F913F0904D40E9C4A1819D (void);
// 0x000004E0 System.Void UnityEngine.UI.ToggleGroup::OnEnable()
extern void ToggleGroup_OnEnable_m8B12EB3F11AE1A3600B0C663486D005DABB1233F (void);
// 0x000004E1 System.Void UnityEngine.UI.ToggleGroup::ValidateToggleIsInGroup(UnityEngine.UI.Toggle)
extern void ToggleGroup_ValidateToggleIsInGroup_mE666CF7D1CF799910B808A81855D087F9E44E93D (void);
// 0x000004E2 System.Void UnityEngine.UI.ToggleGroup::NotifyToggleOn(UnityEngine.UI.Toggle,System.Boolean)
extern void ToggleGroup_NotifyToggleOn_m4B1E6B18DFFFB672B2227C4DCAB68A26440FA33F (void);
// 0x000004E3 System.Void UnityEngine.UI.ToggleGroup::UnregisterToggle(UnityEngine.UI.Toggle)
extern void ToggleGroup_UnregisterToggle_m1903602F193762B2E5264642D7C09B2A91B52685 (void);
// 0x000004E4 System.Void UnityEngine.UI.ToggleGroup::RegisterToggle(UnityEngine.UI.Toggle)
extern void ToggleGroup_RegisterToggle_m7E87D7943C6D2CCBE0B792326F69AA18A726848C (void);
// 0x000004E5 System.Void UnityEngine.UI.ToggleGroup::EnsureValidState()
extern void ToggleGroup_EnsureValidState_m8995EE9A121B4ED71723E21A317B6264C08E03FE (void);
// 0x000004E6 System.Boolean UnityEngine.UI.ToggleGroup::AnyTogglesOn()
extern void ToggleGroup_AnyTogglesOn_mA6EB9869F012D763BF7150EC335DFF548A02837D (void);
// 0x000004E7 System.Collections.Generic.IEnumerable`1<UnityEngine.UI.Toggle> UnityEngine.UI.ToggleGroup::ActiveToggles()
extern void ToggleGroup_ActiveToggles_m4CF8A6DBB4637A10A5CDB852B42C4C4FBCFC3C00 (void);
// 0x000004E8 UnityEngine.UI.Toggle UnityEngine.UI.ToggleGroup::GetFirstActiveToggle()
extern void ToggleGroup_GetFirstActiveToggle_mB4938A5F6C3AB10118C16C4F09B02E0EE1AD223A (void);
// 0x000004E9 System.Void UnityEngine.UI.ToggleGroup::SetAllTogglesOff(System.Boolean)
extern void ToggleGroup_SetAllTogglesOff_mCE1A2D61E940E3AE772367D181CD3221F4529090 (void);
// 0x000004EA System.Void UnityEngine.UI.ToggleGroup/<>c::.cctor()
extern void U3CU3Ec__cctor_mF08B3226ECAD2C55446D4A1276EA0B33B8D27DB2 (void);
// 0x000004EB System.Void UnityEngine.UI.ToggleGroup/<>c::.ctor()
extern void U3CU3Ec__ctor_mE529EC087B06A93509276E7E9CA68D3E3E6CC257 (void);
// 0x000004EC System.Boolean UnityEngine.UI.ToggleGroup/<>c::<AnyTogglesOn>b__13_0(UnityEngine.UI.Toggle)
extern void U3CU3Ec_U3CAnyTogglesOnU3Eb__13_0_m6B58E5D7E10F6C3A857BF297744D758E5D81B6B4 (void);
// 0x000004ED System.Boolean UnityEngine.UI.ToggleGroup/<>c::<ActiveToggles>b__14_0(UnityEngine.UI.Toggle)
extern void U3CU3Ec_U3CActiveTogglesU3Eb__14_0_m8A396237A2696D3A2068BE32BCB869F70904C9AD (void);
// 0x000004EE System.Void UnityEngine.UI.ListPool`1::Clear(System.Collections.Generic.List`1<T>)
// 0x000004EF System.Collections.Generic.List`1<T> UnityEngine.UI.ListPool`1::Get()
// 0x000004F0 System.Void UnityEngine.UI.ListPool`1::Release(System.Collections.Generic.List`1<T>)
// 0x000004F1 System.Void UnityEngine.UI.ListPool`1::.cctor()
// 0x000004F2 System.Int32 UnityEngine.UI.ObjectPool`1::get_countAll()
// 0x000004F3 System.Void UnityEngine.UI.ObjectPool`1::set_countAll(System.Int32)
// 0x000004F4 System.Int32 UnityEngine.UI.ObjectPool`1::get_countActive()
// 0x000004F5 System.Int32 UnityEngine.UI.ObjectPool`1::get_countInactive()
// 0x000004F6 System.Void UnityEngine.UI.ObjectPool`1::.ctor(UnityEngine.Events.UnityAction`1<T>,UnityEngine.Events.UnityAction`1<T>)
// 0x000004F7 T UnityEngine.UI.ObjectPool`1::Get()
// 0x000004F8 System.Void UnityEngine.UI.ObjectPool`1::Release(T)
// 0x000004F9 System.Void UnityEngine.UI.ReflectionMethodsCache::.ctor()
extern void ReflectionMethodsCache__ctor_m9B1FD42D9BA69B5E82C794F29E0D76CD8B50A407 (void);
// 0x000004FA UnityEngine.UI.ReflectionMethodsCache UnityEngine.UI.ReflectionMethodsCache::get_Singleton()
extern void ReflectionMethodsCache_get_Singleton_m979239967F039C94A7E7ABABEBEDC43D461E9AD7 (void);
// 0x000004FB System.Void UnityEngine.UI.ReflectionMethodsCache::.cctor()
extern void ReflectionMethodsCache__cctor_m1A8A28C0A1386C1B9B64179B0BB6727C2A209347 (void);
// 0x000004FC System.Void UnityEngine.UI.ReflectionMethodsCache/Raycast3DCallback::.ctor(System.Object,System.IntPtr)
extern void Raycast3DCallback__ctor_mAAD87D0BBDD341D9798A282988F3E29209F367EC (void);
// 0x000004FD System.Boolean UnityEngine.UI.ReflectionMethodsCache/Raycast3DCallback::Invoke(UnityEngine.Ray,UnityEngine.RaycastHit&,System.Single,System.Int32)
extern void Raycast3DCallback_Invoke_mC1E4D81B6EF4118CAB6ED4ECEA3312AFB1E81744 (void);
// 0x000004FE System.IAsyncResult UnityEngine.UI.ReflectionMethodsCache/Raycast3DCallback::BeginInvoke(UnityEngine.Ray,UnityEngine.RaycastHit&,System.Single,System.Int32,System.AsyncCallback,System.Object)
extern void Raycast3DCallback_BeginInvoke_mD96B941801A9E258E0E5F32AD7E37E99532A7DC7 (void);
// 0x000004FF System.Boolean UnityEngine.UI.ReflectionMethodsCache/Raycast3DCallback::EndInvoke(UnityEngine.RaycastHit&,System.IAsyncResult)
extern void Raycast3DCallback_EndInvoke_m45E04D8E5C1288FA778A3364599532EFB4834AC5 (void);
// 0x00000500 System.Void UnityEngine.UI.ReflectionMethodsCache/RaycastAllCallback::.ctor(System.Object,System.IntPtr)
extern void RaycastAllCallback__ctor_m219974A3E0AE674C4843A1638B9B97E967D942B6 (void);
// 0x00000501 UnityEngine.RaycastHit[] UnityEngine.UI.ReflectionMethodsCache/RaycastAllCallback::Invoke(UnityEngine.Ray,System.Single,System.Int32)
extern void RaycastAllCallback_Invoke_m4A107AB96A1A28BD319A50AFBFD401A791E8DD26 (void);
// 0x00000502 System.IAsyncResult UnityEngine.UI.ReflectionMethodsCache/RaycastAllCallback::BeginInvoke(UnityEngine.Ray,System.Single,System.Int32,System.AsyncCallback,System.Object)
extern void RaycastAllCallback_BeginInvoke_m4343AD48520C9F524B3206CE11A9D7F1BF7265C6 (void);
// 0x00000503 UnityEngine.RaycastHit[] UnityEngine.UI.ReflectionMethodsCache/RaycastAllCallback::EndInvoke(System.IAsyncResult)
extern void RaycastAllCallback_EndInvoke_mCD8F79E5C1A4870FAEEED315823042FD990D30D5 (void);
// 0x00000504 System.Void UnityEngine.UI.ReflectionMethodsCache/GetRaycastNonAllocCallback::.ctor(System.Object,System.IntPtr)
extern void GetRaycastNonAllocCallback__ctor_m5EF88C8923C7A175F825682E242A0A21F7F9CDC1 (void);
// 0x00000505 System.Int32 UnityEngine.UI.ReflectionMethodsCache/GetRaycastNonAllocCallback::Invoke(UnityEngine.Ray,UnityEngine.RaycastHit[],System.Single,System.Int32)
extern void GetRaycastNonAllocCallback_Invoke_m9F06CE1FAAC409FACB2138A85B6E69E6A38099E5 (void);
// 0x00000506 System.IAsyncResult UnityEngine.UI.ReflectionMethodsCache/GetRaycastNonAllocCallback::BeginInvoke(UnityEngine.Ray,UnityEngine.RaycastHit[],System.Single,System.Int32,System.AsyncCallback,System.Object)
extern void GetRaycastNonAllocCallback_BeginInvoke_m7AC50FE945DA1CE964D3C6BB7AD97DEB4440B32C (void);
// 0x00000507 System.Int32 UnityEngine.UI.ReflectionMethodsCache/GetRaycastNonAllocCallback::EndInvoke(System.IAsyncResult)
extern void GetRaycastNonAllocCallback_EndInvoke_m8F4495A70A254C556EFC0730A30AC71E4BD4FB0B (void);
// 0x00000508 System.Void UnityEngine.UI.ReflectionMethodsCache/Raycast2DCallback::.ctor(System.Object,System.IntPtr)
extern void Raycast2DCallback__ctor_mBE2DD6E8DBBE5E92D144CF9E21D3F7B446A84DF0 (void);
// 0x00000509 UnityEngine.RaycastHit2D UnityEngine.UI.ReflectionMethodsCache/Raycast2DCallback::Invoke(UnityEngine.Vector2,UnityEngine.Vector2,System.Single,System.Int32)
extern void Raycast2DCallback_Invoke_mD30E994123A65522A82EF29EBAA5A75ED1A25097 (void);
// 0x0000050A System.IAsyncResult UnityEngine.UI.ReflectionMethodsCache/Raycast2DCallback::BeginInvoke(UnityEngine.Vector2,UnityEngine.Vector2,System.Single,System.Int32,System.AsyncCallback,System.Object)
extern void Raycast2DCallback_BeginInvoke_m5A041AB9DB48B5D5C0CA2169D51BCC12200C319C (void);
// 0x0000050B UnityEngine.RaycastHit2D UnityEngine.UI.ReflectionMethodsCache/Raycast2DCallback::EndInvoke(System.IAsyncResult)
extern void Raycast2DCallback_EndInvoke_m93B7E9EAD357455F829161F77D937B90361F648A (void);
// 0x0000050C System.Void UnityEngine.UI.ReflectionMethodsCache/GetRayIntersectionAllCallback::.ctor(System.Object,System.IntPtr)
extern void GetRayIntersectionAllCallback__ctor_m3FB3501090DF1536469BE66442205BC29D4D2725 (void);
// 0x0000050D UnityEngine.RaycastHit2D[] UnityEngine.UI.ReflectionMethodsCache/GetRayIntersectionAllCallback::Invoke(UnityEngine.Ray,System.Single,System.Int32)
extern void GetRayIntersectionAllCallback_Invoke_mC413E2F7F934A21FCF43D6FC99DB7A16A85427FC (void);
// 0x0000050E System.IAsyncResult UnityEngine.UI.ReflectionMethodsCache/GetRayIntersectionAllCallback::BeginInvoke(UnityEngine.Ray,System.Single,System.Int32,System.AsyncCallback,System.Object)
extern void GetRayIntersectionAllCallback_BeginInvoke_m28BC711B1ADD07E7E1CFE655540570F1CB7CA8A5 (void);
// 0x0000050F UnityEngine.RaycastHit2D[] UnityEngine.UI.ReflectionMethodsCache/GetRayIntersectionAllCallback::EndInvoke(System.IAsyncResult)
extern void GetRayIntersectionAllCallback_EndInvoke_m2CD8143576A1A90F130FB217CAE90D8E3701E2EF (void);
// 0x00000510 System.Void UnityEngine.UI.ReflectionMethodsCache/GetRayIntersectionAllNonAllocCallback::.ctor(System.Object,System.IntPtr)
extern void GetRayIntersectionAllNonAllocCallback__ctor_mBC32BC06655600F7870640A99A1D0A4970FD0403 (void);
// 0x00000511 System.Int32 UnityEngine.UI.ReflectionMethodsCache/GetRayIntersectionAllNonAllocCallback::Invoke(UnityEngine.Ray,UnityEngine.RaycastHit2D[],System.Single,System.Int32)
extern void GetRayIntersectionAllNonAllocCallback_Invoke_m3A03F6D1E31D55B967B4688FE35EA70C618B1BC9 (void);
// 0x00000512 System.IAsyncResult UnityEngine.UI.ReflectionMethodsCache/GetRayIntersectionAllNonAllocCallback::BeginInvoke(UnityEngine.Ray,UnityEngine.RaycastHit2D[],System.Single,System.Int32,System.AsyncCallback,System.Object)
extern void GetRayIntersectionAllNonAllocCallback_BeginInvoke_m11D1A4ADD9C26978DE03AB28C0A419E11D2A0569 (void);
// 0x00000513 System.Int32 UnityEngine.UI.ReflectionMethodsCache/GetRayIntersectionAllNonAllocCallback::EndInvoke(System.IAsyncResult)
extern void GetRayIntersectionAllNonAllocCallback_EndInvoke_mD095B5BFC09387E0AE997CF77DE71D138E97C8C3 (void);
// 0x00000514 System.Void UnityEngine.UI.VertexHelper::.ctor()
extern void VertexHelper__ctor_m66DE6882DBEBE377C3E672DD0E9DEB88694069B6 (void);
// 0x00000515 System.Void UnityEngine.UI.VertexHelper::.ctor(UnityEngine.Mesh)
extern void VertexHelper__ctor_m393544BA4187E8E1FF628CC7FD3755AEB5EA76EE (void);
// 0x00000516 System.Void UnityEngine.UI.VertexHelper::InitializeListIfRequired()
extern void VertexHelper_InitializeListIfRequired_m6CCC5B58B5B1EC87F651B36220440A58B38728CF (void);
// 0x00000517 System.Void UnityEngine.UI.VertexHelper::Dispose()
extern void VertexHelper_Dispose_m1F4448E484FD377DDA18AE871DE116EEBE39A5FB (void);
// 0x00000518 System.Void UnityEngine.UI.VertexHelper::Clear()
extern void VertexHelper_Clear_mBF3FB3CEA5153F8F72C74FFD6006A7AFF62C18BA (void);
// 0x00000519 System.Int32 UnityEngine.UI.VertexHelper::get_currentVertCount()
extern void VertexHelper_get_currentVertCount_m4E9932F9BBCC9CB9636B3415A03454D6B7A92807 (void);
// 0x0000051A System.Int32 UnityEngine.UI.VertexHelper::get_currentIndexCount()
extern void VertexHelper_get_currentIndexCount_mBE8966E80B9260A6A8FF56FA7881E027E25702D8 (void);
// 0x0000051B System.Void UnityEngine.UI.VertexHelper::PopulateUIVertex(UnityEngine.UIVertex&,System.Int32)
extern void VertexHelper_PopulateUIVertex_m540F0A80C1A55C7444259CEE118CAC61F198B555 (void);
// 0x0000051C System.Void UnityEngine.UI.VertexHelper::SetUIVertex(UnityEngine.UIVertex,System.Int32)
extern void VertexHelper_SetUIVertex_mE6E1BF09DA31C90FA922B6F96123D7C363A71D7E (void);
// 0x0000051D System.Void UnityEngine.UI.VertexHelper::FillMesh(UnityEngine.Mesh)
extern void VertexHelper_FillMesh_m69ADAB814A243F7F5578BC07086F373B85A34269 (void);
// 0x0000051E System.Void UnityEngine.UI.VertexHelper::AddVert(UnityEngine.Vector3,UnityEngine.Color32,UnityEngine.Vector4,UnityEngine.Vector4,UnityEngine.Vector4,UnityEngine.Vector4,UnityEngine.Vector3,UnityEngine.Vector4)
extern void VertexHelper_AddVert_m0988345B2D2BCC66B875E9F07B99E12C68C4590C (void);
// 0x0000051F System.Void UnityEngine.UI.VertexHelper::AddVert(UnityEngine.Vector3,UnityEngine.Color32,UnityEngine.Vector4,UnityEngine.Vector4,UnityEngine.Vector3,UnityEngine.Vector4)
extern void VertexHelper_AddVert_m3428A0D5A377CBF2191350B793299EF1EC3503B1 (void);
// 0x00000520 System.Void UnityEngine.UI.VertexHelper::AddVert(UnityEngine.Vector3,UnityEngine.Color32,UnityEngine.Vector4)
extern void VertexHelper_AddVert_m5CD02FDA1B6ADBD0E276037F948B68E08497D1F2 (void);
// 0x00000521 System.Void UnityEngine.UI.VertexHelper::AddVert(UnityEngine.UIVertex)
extern void VertexHelper_AddVert_m7A43A65F746413AF697EBD1D0A8EA87A0A7ED032 (void);
// 0x00000522 System.Void UnityEngine.UI.VertexHelper::AddTriangle(System.Int32,System.Int32,System.Int32)
extern void VertexHelper_AddTriangle_m1EE93E4BF27E3BCCE69A348358FAF605105B63C6 (void);
// 0x00000523 System.Void UnityEngine.UI.VertexHelper::AddUIVertexQuad(UnityEngine.UIVertex[])
extern void VertexHelper_AddUIVertexQuad_m16C46AF7CE9A2D9E1AE47A4B9799081A707C47B5 (void);
// 0x00000524 System.Void UnityEngine.UI.VertexHelper::AddUIVertexStream(System.Collections.Generic.List`1<UnityEngine.UIVertex>,System.Collections.Generic.List`1<System.Int32>)
extern void VertexHelper_AddUIVertexStream_m979FD37B1176E5B5A217065C04B64EDB568DC85B (void);
// 0x00000525 System.Void UnityEngine.UI.VertexHelper::AddUIVertexTriangleStream(System.Collections.Generic.List`1<UnityEngine.UIVertex>)
extern void VertexHelper_AddUIVertexTriangleStream_m3FC7DF3D1DA3F0D40025258E3B8FF5830EE7CE55 (void);
// 0x00000526 System.Void UnityEngine.UI.VertexHelper::GetUIVertexStream(System.Collections.Generic.List`1<UnityEngine.UIVertex>)
extern void VertexHelper_GetUIVertexStream_mA3E62A7B45BFFFC73D72BC7B8BFAD5388F8578BA (void);
// 0x00000527 System.Void UnityEngine.UI.VertexHelper::.cctor()
extern void VertexHelper__cctor_m3DDE70E5F715BA724FEAFD8BD9A93385D2E44878 (void);
// 0x00000528 System.Void UnityEngine.UI.BaseVertexEffect::ModifyVertices(System.Collections.Generic.List`1<UnityEngine.UIVertex>)
// 0x00000529 System.Void UnityEngine.UI.BaseVertexEffect::.ctor()
extern void BaseVertexEffect__ctor_mD0598FFC9739945F3DE04BF263F33934BF9F38AC (void);
// 0x0000052A UnityEngine.UI.Graphic UnityEngine.UI.BaseMeshEffect::get_graphic()
extern void BaseMeshEffect_get_graphic_m4FAFDA7300251A13F7DDE689145C54E8B971688D (void);
// 0x0000052B System.Void UnityEngine.UI.BaseMeshEffect::OnEnable()
extern void BaseMeshEffect_OnEnable_mB23ADD5744D6132E260E3E8D40EC9FC8A1CB69E7 (void);
// 0x0000052C System.Void UnityEngine.UI.BaseMeshEffect::OnDisable()
extern void BaseMeshEffect_OnDisable_mD74FC12529C7975B8214235CB629BAD3702C7A1F (void);
// 0x0000052D System.Void UnityEngine.UI.BaseMeshEffect::OnDidApplyAnimationProperties()
extern void BaseMeshEffect_OnDidApplyAnimationProperties_m8624B9A46D7F47EE2DA080F4624C5CCAAED6A450 (void);
// 0x0000052E System.Void UnityEngine.UI.BaseMeshEffect::ModifyMesh(UnityEngine.Mesh)
extern void BaseMeshEffect_ModifyMesh_mDC045F4A3A571E76731F7773E7164E7D0E1143F3 (void);
// 0x0000052F System.Void UnityEngine.UI.BaseMeshEffect::ModifyMesh(UnityEngine.UI.VertexHelper)
// 0x00000530 System.Void UnityEngine.UI.BaseMeshEffect::.ctor()
extern void BaseMeshEffect__ctor_m7D21D47A3B87CB9B715FCEEE1B955E417FEEF01B (void);
// 0x00000531 System.Void UnityEngine.UI.IVertexModifier::ModifyVertices(System.Collections.Generic.List`1<UnityEngine.UIVertex>)
// 0x00000532 System.Void UnityEngine.UI.IMeshModifier::ModifyMesh(UnityEngine.Mesh)
// 0x00000533 System.Void UnityEngine.UI.IMeshModifier::ModifyMesh(UnityEngine.UI.VertexHelper)
// 0x00000534 System.Void UnityEngine.UI.Outline::.ctor()
extern void Outline__ctor_m6143991EB8225AB600E3D212CD057A43DAC8A7DB (void);
// 0x00000535 System.Void UnityEngine.UI.Outline::ModifyMesh(UnityEngine.UI.VertexHelper)
extern void Outline_ModifyMesh_m21784D49980211B1AA3E6A6E6A2E2EEF03DBDC54 (void);
// 0x00000536 System.Void UnityEngine.UI.PositionAsUV1::.ctor()
extern void PositionAsUV1__ctor_mA7764A6F1AC48964112A831DCE71C75D36864E0A (void);
// 0x00000537 System.Void UnityEngine.UI.PositionAsUV1::ModifyMesh(UnityEngine.UI.VertexHelper)
extern void PositionAsUV1_ModifyMesh_m23B103D2A5CA62A4FEB450B6BE15A6DAFEA939A3 (void);
// 0x00000538 System.Void UnityEngine.UI.Shadow::.ctor()
extern void Shadow__ctor_mC023CEF78072A0E21FCBB5EDE94582DE042A2C54 (void);
// 0x00000539 UnityEngine.Color UnityEngine.UI.Shadow::get_effectColor()
extern void Shadow_get_effectColor_m00C1776542129598C244BB469E7128D60F6BCAC2 (void);
// 0x0000053A System.Void UnityEngine.UI.Shadow::set_effectColor(UnityEngine.Color)
extern void Shadow_set_effectColor_mFB6601937B8DCBB52A6095435A380C4AE0A807DF (void);
// 0x0000053B UnityEngine.Vector2 UnityEngine.UI.Shadow::get_effectDistance()
extern void Shadow_get_effectDistance_mD0C417FD305D3F674FB111F38B41C9B94808E7C0 (void);
// 0x0000053C System.Void UnityEngine.UI.Shadow::set_effectDistance(UnityEngine.Vector2)
extern void Shadow_set_effectDistance_m5C9FAC6D8D46E952FF29D00852E790E6A3BF2E09 (void);
// 0x0000053D System.Boolean UnityEngine.UI.Shadow::get_useGraphicAlpha()
extern void Shadow_get_useGraphicAlpha_mF5EAD2754C90C2C0BAA50786C514E6A1D834B6F0 (void);
// 0x0000053E System.Void UnityEngine.UI.Shadow::set_useGraphicAlpha(System.Boolean)
extern void Shadow_set_useGraphicAlpha_m819149C2D4E0B64D112B8BA8FF4D4E1383C5CF6A (void);
// 0x0000053F System.Void UnityEngine.UI.Shadow::ApplyShadowZeroAlloc(System.Collections.Generic.List`1<UnityEngine.UIVertex>,UnityEngine.Color32,System.Int32,System.Int32,System.Single,System.Single)
extern void Shadow_ApplyShadowZeroAlloc_m31E0AC08A226594BF2CB47E9B19CF5C816C1499F (void);
// 0x00000540 System.Void UnityEngine.UI.Shadow::ApplyShadow(System.Collections.Generic.List`1<UnityEngine.UIVertex>,UnityEngine.Color32,System.Int32,System.Int32,System.Single,System.Single)
extern void Shadow_ApplyShadow_mB51E2C37515B2DB9D0242AE30FD16EB1AE36EF86 (void);
// 0x00000541 System.Void UnityEngine.UI.Shadow::ModifyMesh(UnityEngine.UI.VertexHelper)
extern void Shadow_ModifyMesh_mF44456F48248AF8EBA900E0E96C2A69F8DE655DD (void);
// 0x00000542 System.Void UnityEngine.UI.Collections.IndexedSet`1::Add(T)
// 0x00000543 System.Void UnityEngine.UI.Collections.IndexedSet`1::Add(T,System.Boolean)
// 0x00000544 System.Boolean UnityEngine.UI.Collections.IndexedSet`1::AddUnique(T,System.Boolean)
// 0x00000545 System.Boolean UnityEngine.UI.Collections.IndexedSet`1::EnableItem(T)
// 0x00000546 System.Boolean UnityEngine.UI.Collections.IndexedSet`1::DisableItem(T)
// 0x00000547 System.Boolean UnityEngine.UI.Collections.IndexedSet`1::Remove(T)
// 0x00000548 System.Collections.Generic.IEnumerator`1<T> UnityEngine.UI.Collections.IndexedSet`1::GetEnumerator()
// 0x00000549 System.Collections.IEnumerator UnityEngine.UI.Collections.IndexedSet`1::System.Collections.IEnumerable.GetEnumerator()
// 0x0000054A System.Void UnityEngine.UI.Collections.IndexedSet`1::Clear()
// 0x0000054B System.Boolean UnityEngine.UI.Collections.IndexedSet`1::Contains(T)
// 0x0000054C System.Void UnityEngine.UI.Collections.IndexedSet`1::CopyTo(T[],System.Int32)
// 0x0000054D System.Int32 UnityEngine.UI.Collections.IndexedSet`1::get_Count()
// 0x0000054E System.Int32 UnityEngine.UI.Collections.IndexedSet`1::get_Capacity()
// 0x0000054F System.Boolean UnityEngine.UI.Collections.IndexedSet`1::get_IsReadOnly()
// 0x00000550 System.Int32 UnityEngine.UI.Collections.IndexedSet`1::IndexOf(T)
// 0x00000551 System.Void UnityEngine.UI.Collections.IndexedSet`1::Insert(System.Int32,T)
// 0x00000552 System.Void UnityEngine.UI.Collections.IndexedSet`1::RemoveAt(System.Int32)
// 0x00000553 System.Void UnityEngine.UI.Collections.IndexedSet`1::Swap(System.Int32,System.Int32)
// 0x00000554 T UnityEngine.UI.Collections.IndexedSet`1::get_Item(System.Int32)
// 0x00000555 System.Void UnityEngine.UI.Collections.IndexedSet`1::set_Item(System.Int32,T)
// 0x00000556 System.Void UnityEngine.UI.Collections.IndexedSet`1::RemoveAll(System.Predicate`1<T>)
// 0x00000557 System.Void UnityEngine.UI.Collections.IndexedSet`1::Sort(System.Comparison`1<T>)
// 0x00000558 System.Void UnityEngine.UI.Collections.IndexedSet`1::.ctor()
// 0x00000559 System.Void UnityEngine.UI.CoroutineTween.ITweenValue::TweenValue(System.Single)
// 0x0000055A System.Boolean UnityEngine.UI.CoroutineTween.ITweenValue::get_ignoreTimeScale()
// 0x0000055B System.Single UnityEngine.UI.CoroutineTween.ITweenValue::get_duration()
// 0x0000055C System.Boolean UnityEngine.UI.CoroutineTween.ITweenValue::ValidTarget()
// 0x0000055D UnityEngine.Color UnityEngine.UI.CoroutineTween.ColorTween::get_startColor()
extern void ColorTween_get_startColor_m17A0327111EF4CEB0C15009D70607065D7B2DD0C (void);
// 0x0000055E System.Void UnityEngine.UI.CoroutineTween.ColorTween::set_startColor(UnityEngine.Color)
extern void ColorTween_set_startColor_m3FDDCF9C585451522E3DC19AC452B6020931418D (void);
// 0x0000055F UnityEngine.Color UnityEngine.UI.CoroutineTween.ColorTween::get_targetColor()
extern void ColorTween_get_targetColor_m7FBFE43D0C87958C7F82CE25627ED2A6C1E04EBC (void);
// 0x00000560 System.Void UnityEngine.UI.CoroutineTween.ColorTween::set_targetColor(UnityEngine.Color)
extern void ColorTween_set_targetColor_m0172FC5EED7BEDA58CC0BF38D15484A9E621C9B2 (void);
// 0x00000561 UnityEngine.UI.CoroutineTween.ColorTween/ColorTweenMode UnityEngine.UI.CoroutineTween.ColorTween::get_tweenMode()
extern void ColorTween_get_tweenMode_mE64B5F00617D95B356E317C0DD27F4E047872840 (void);
// 0x00000562 System.Void UnityEngine.UI.CoroutineTween.ColorTween::set_tweenMode(UnityEngine.UI.CoroutineTween.ColorTween/ColorTweenMode)
extern void ColorTween_set_tweenMode_m3EB2248A06FAC8B31730D2C0A7019E83FF3BFA54 (void);
// 0x00000563 System.Single UnityEngine.UI.CoroutineTween.ColorTween::get_duration()
extern void ColorTween_get_duration_m0D54F8FF864F645287570B01A7263FDA284A3091 (void);
// 0x00000564 System.Void UnityEngine.UI.CoroutineTween.ColorTween::set_duration(System.Single)
extern void ColorTween_set_duration_mB1EF6D6212E52CE120C294C354188F55A3AAAE56 (void);
// 0x00000565 System.Boolean UnityEngine.UI.CoroutineTween.ColorTween::get_ignoreTimeScale()
extern void ColorTween_get_ignoreTimeScale_mB626FF1519AD4745DA9C6F1ACFB06665F7775740 (void);
// 0x00000566 System.Void UnityEngine.UI.CoroutineTween.ColorTween::set_ignoreTimeScale(System.Boolean)
extern void ColorTween_set_ignoreTimeScale_mB859C6558AAA257A7C25E98E91A4C1E1B1F3FADC (void);
// 0x00000567 System.Void UnityEngine.UI.CoroutineTween.ColorTween::TweenValue(System.Single)
extern void ColorTween_TweenValue_m5F8B59F75D4CE627BC5F6E34A1345D41941FDCC6 (void);
// 0x00000568 System.Void UnityEngine.UI.CoroutineTween.ColorTween::AddOnChangedCallback(UnityEngine.Events.UnityAction`1<UnityEngine.Color>)
extern void ColorTween_AddOnChangedCallback_mABFB32C3DFECB9271E2CECA5FE79567F82D56DCF (void);
// 0x00000569 System.Boolean UnityEngine.UI.CoroutineTween.ColorTween::GetIgnoreTimescale()
extern void ColorTween_GetIgnoreTimescale_mF956E0A7BCE90295B6341EB17641A36A9777B8FB (void);
// 0x0000056A System.Single UnityEngine.UI.CoroutineTween.ColorTween::GetDuration()
extern void ColorTween_GetDuration_mDF63029E52D00CCFE62AD905D392D54523D3CA76 (void);
// 0x0000056B System.Boolean UnityEngine.UI.CoroutineTween.ColorTween::ValidTarget()
extern void ColorTween_ValidTarget_m1A0F15E461C7359650F9B26BD2CE50EFCA2EA80A (void);
// 0x0000056C System.Void UnityEngine.UI.CoroutineTween.ColorTween/ColorTweenCallback::.ctor()
extern void ColorTweenCallback__ctor_m657B9693A9F289FE37673F95D76A89BCF250FAA9 (void);
// 0x0000056D System.Single UnityEngine.UI.CoroutineTween.FloatTween::get_startValue()
extern void FloatTween_get_startValue_m50E23F438012DBCDB77F79083F8B3E7C747ECEB3 (void);
// 0x0000056E System.Void UnityEngine.UI.CoroutineTween.FloatTween::set_startValue(System.Single)
extern void FloatTween_set_startValue_mFB659473A8FB57C4D0F951DC53D9364ABF5435A5 (void);
// 0x0000056F System.Single UnityEngine.UI.CoroutineTween.FloatTween::get_targetValue()
extern void FloatTween_get_targetValue_mAA60AB9B567184EB6DB1C960F2AED3B934FF5DFE (void);
// 0x00000570 System.Void UnityEngine.UI.CoroutineTween.FloatTween::set_targetValue(System.Single)
extern void FloatTween_set_targetValue_m4DD565573FA7B4110EEDA4D524D369858571A0A2 (void);
// 0x00000571 System.Single UnityEngine.UI.CoroutineTween.FloatTween::get_duration()
extern void FloatTween_get_duration_mD6CD9915EB7F798B9F07F20E096066B201CCFF73 (void);
// 0x00000572 System.Void UnityEngine.UI.CoroutineTween.FloatTween::set_duration(System.Single)
extern void FloatTween_set_duration_mB561C369977BAE6807268EAA9633B31F530151E9 (void);
// 0x00000573 System.Boolean UnityEngine.UI.CoroutineTween.FloatTween::get_ignoreTimeScale()
extern void FloatTween_get_ignoreTimeScale_m40168239ACB9A32B1A87EAF382CBA87FB641743D (void);
// 0x00000574 System.Void UnityEngine.UI.CoroutineTween.FloatTween::set_ignoreTimeScale(System.Boolean)
extern void FloatTween_set_ignoreTimeScale_mACD465EA2E70BAFBA3FD3FC4FECC4393E17E0786 (void);
// 0x00000575 System.Void UnityEngine.UI.CoroutineTween.FloatTween::TweenValue(System.Single)
extern void FloatTween_TweenValue_mF21AE3A616B020B1D351E237D1F3145B508ACB11 (void);
// 0x00000576 System.Void UnityEngine.UI.CoroutineTween.FloatTween::AddOnChangedCallback(UnityEngine.Events.UnityAction`1<System.Single>)
extern void FloatTween_AddOnChangedCallback_m07B7D701CF26857FE4E33DE9467716065381EBEE (void);
// 0x00000577 System.Boolean UnityEngine.UI.CoroutineTween.FloatTween::GetIgnoreTimescale()
extern void FloatTween_GetIgnoreTimescale_m39FBA712B845E9E33B886502D2DB92A499D2F6D8 (void);
// 0x00000578 System.Single UnityEngine.UI.CoroutineTween.FloatTween::GetDuration()
extern void FloatTween_GetDuration_m1D2C252520C4713F105A75162AFAA1A7785C28BE (void);
// 0x00000579 System.Boolean UnityEngine.UI.CoroutineTween.FloatTween::ValidTarget()
extern void FloatTween_ValidTarget_mE1A5EA20038733B467C8B810E63BE6617F7DAAFF (void);
// 0x0000057A System.Void UnityEngine.UI.CoroutineTween.FloatTween/FloatTweenCallback::.ctor()
extern void FloatTweenCallback__ctor_m5BD825A7E915816E2F70423A9567C4D82959C309 (void);
// 0x0000057B System.Collections.IEnumerator UnityEngine.UI.CoroutineTween.TweenRunner`1::Start(T)
// 0x0000057C System.Void UnityEngine.UI.CoroutineTween.TweenRunner`1::Init(UnityEngine.MonoBehaviour)
// 0x0000057D System.Void UnityEngine.UI.CoroutineTween.TweenRunner`1::StartTween(T)
// 0x0000057E System.Void UnityEngine.UI.CoroutineTween.TweenRunner`1::StopTween()
// 0x0000057F System.Void UnityEngine.UI.CoroutineTween.TweenRunner`1::.ctor()
// 0x00000580 System.Void UnityEngine.UI.CoroutineTween.TweenRunner`1/<Start>d__2::.ctor(System.Int32)
// 0x00000581 System.Void UnityEngine.UI.CoroutineTween.TweenRunner`1/<Start>d__2::System.IDisposable.Dispose()
// 0x00000582 System.Boolean UnityEngine.UI.CoroutineTween.TweenRunner`1/<Start>d__2::MoveNext()
// 0x00000583 System.Object UnityEngine.UI.CoroutineTween.TweenRunner`1/<Start>d__2::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
// 0x00000584 System.Void UnityEngine.UI.CoroutineTween.TweenRunner`1/<Start>d__2::System.Collections.IEnumerator.Reset()
// 0x00000585 System.Object UnityEngine.UI.CoroutineTween.TweenRunner`1/<Start>d__2::System.Collections.IEnumerator.get_Current()
// 0x00000586 UnityEngine.Vector2 UnityEngine.EventSystems.AxisEventData::get_moveVector()
extern void AxisEventData_get_moveVector_mC99249AB7977DA35D4BDCEA4B13BAF08986F6BDA (void);
// 0x00000587 System.Void UnityEngine.EventSystems.AxisEventData::set_moveVector(UnityEngine.Vector2)
extern void AxisEventData_set_moveVector_mE7D38A878DCC73C1E829C30A4AA6A1B2DE6DC0A7 (void);
// 0x00000588 UnityEngine.EventSystems.MoveDirection UnityEngine.EventSystems.AxisEventData::get_moveDir()
extern void AxisEventData_get_moveDir_mEE3B3409B871B022C83343228C554D4CBA4FDB7C (void);
// 0x00000589 System.Void UnityEngine.EventSystems.AxisEventData::set_moveDir(UnityEngine.EventSystems.MoveDirection)
extern void AxisEventData_set_moveDir_m2232A7B0D06742A92628CFD04478D42FFABCB3FA (void);
// 0x0000058A System.Void UnityEngine.EventSystems.AxisEventData::.ctor(UnityEngine.EventSystems.EventSystem)
extern void AxisEventData__ctor_mC155A7CC3943E35A99D1F011DCEE37914835052B (void);
// 0x0000058B System.Void UnityEngine.EventSystems.AbstractEventData::Reset()
extern void AbstractEventData_Reset_m3E1463A5F5CBDC06D22019C342CF8EB560C18245 (void);
// 0x0000058C System.Void UnityEngine.EventSystems.AbstractEventData::Use()
extern void AbstractEventData_Use_m44D8C11EDB60410F54840BF720E04FDB95B0F5E3 (void);
// 0x0000058D System.Boolean UnityEngine.EventSystems.AbstractEventData::get_used()
extern void AbstractEventData_get_used_m9A7B684ED21FE129F0A72AA87CCE980F0C18122A (void);
// 0x0000058E System.Void UnityEngine.EventSystems.AbstractEventData::.ctor()
extern void AbstractEventData__ctor_m99EC216AC354D3644883C2FFC95A54217C628A3F (void);
// 0x0000058F System.Void UnityEngine.EventSystems.BaseEventData::.ctor(UnityEngine.EventSystems.EventSystem)
extern void BaseEventData__ctor_m51BEBFA3BB83410335A06787E273E954CAFD4B84 (void);
// 0x00000590 UnityEngine.EventSystems.BaseInputModule UnityEngine.EventSystems.BaseEventData::get_currentInputModule()
extern void BaseEventData_get_currentInputModule_m76CF69CF6C3042B20D3255C73CAEA013D992B7FF (void);
// 0x00000591 UnityEngine.GameObject UnityEngine.EventSystems.BaseEventData::get_selectedObject()
extern void BaseEventData_get_selectedObject_mFBA205D5EE9CCC6DAD5D606EC6DE23704669B093 (void);
// 0x00000592 System.Void UnityEngine.EventSystems.BaseEventData::set_selectedObject(UnityEngine.GameObject)
extern void BaseEventData_set_selectedObject_m828F8FC80B817712F0B77C82D5DFC8066A23B064 (void);
// 0x00000593 UnityEngine.GameObject UnityEngine.EventSystems.PointerEventData::get_pointerEnter()
extern void PointerEventData_get_pointerEnter_m6F16C8962F195BB6ED58150986AEF584E4B979CB (void);
// 0x00000594 System.Void UnityEngine.EventSystems.PointerEventData::set_pointerEnter(UnityEngine.GameObject)
extern void PointerEventData_set_pointerEnter_mA547F8B280EA1AE5DE27EB5FF14AC3CF156A86D1 (void);
// 0x00000595 UnityEngine.GameObject UnityEngine.EventSystems.PointerEventData::get_lastPress()
extern void PointerEventData_get_lastPress_m362C5876B8C9F50BACC27D9026DB3709D6950C0B (void);
// 0x00000596 System.Void UnityEngine.EventSystems.PointerEventData::set_lastPress(UnityEngine.GameObject)
extern void PointerEventData_set_lastPress_m6324AB7BF9620A37B34A2D0DF62ED2CCC97DDC22 (void);
// 0x00000597 UnityEngine.GameObject UnityEngine.EventSystems.PointerEventData::get_rawPointerPress()
extern void PointerEventData_get_rawPointerPress_m0C23DB50BCE28ECC43609CC01E727CCA77FC6473 (void);
// 0x00000598 System.Void UnityEngine.EventSystems.PointerEventData::set_rawPointerPress(UnityEngine.GameObject)
extern void PointerEventData_set_rawPointerPress_m0BEEB9CA5E44F570C2C0803553BA9736F4DF58F0 (void);
// 0x00000599 UnityEngine.GameObject UnityEngine.EventSystems.PointerEventData::get_pointerDrag()
extern void PointerEventData_get_pointerDrag_m5FD1D758CA629D9EBB8BDA3207132BC9BAB91ACE (void);
// 0x0000059A System.Void UnityEngine.EventSystems.PointerEventData::set_pointerDrag(UnityEngine.GameObject)
extern void PointerEventData_set_pointerDrag_m2E9F059EC1CDF71E0A097A0D3CCBA564E0C463C2 (void);
// 0x0000059B UnityEngine.GameObject UnityEngine.EventSystems.PointerEventData::get_pointerClick()
extern void PointerEventData_get_pointerClick_mBB8D52B230FF80A2ABCEA6B7C8E04AF5D6330F3F (void);
// 0x0000059C System.Void UnityEngine.EventSystems.PointerEventData::set_pointerClick(UnityEngine.GameObject)
extern void PointerEventData_set_pointerClick_mDF51451241642D1771C8C6CF8598CD76CFF43A4E (void);
// 0x0000059D UnityEngine.EventSystems.RaycastResult UnityEngine.EventSystems.PointerEventData::get_pointerCurrentRaycast()
extern void PointerEventData_get_pointerCurrentRaycast_m8F200C53C20879FC2A2EECFDDFA9B453E63964B3 (void);
// 0x0000059E System.Void UnityEngine.EventSystems.PointerEventData::set_pointerCurrentRaycast(UnityEngine.EventSystems.RaycastResult)
extern void PointerEventData_set_pointerCurrentRaycast_m16C21C39E7763E52ABE4485B4171FC4DF2A2A6BC (void);
// 0x0000059F UnityEngine.EventSystems.RaycastResult UnityEngine.EventSystems.PointerEventData::get_pointerPressRaycast()
extern void PointerEventData_get_pointerPressRaycast_m3C5785CD2C31F91C91D6F1084D2EAC31BED56ACB (void);
// 0x000005A0 System.Void UnityEngine.EventSystems.PointerEventData::set_pointerPressRaycast(UnityEngine.EventSystems.RaycastResult)
extern void PointerEventData_set_pointerPressRaycast_mAF28B12216468A02DACA9900B0A57FA1BF3B94F4 (void);
// 0x000005A1 System.Boolean UnityEngine.EventSystems.PointerEventData::get_eligibleForClick()
extern void PointerEventData_get_eligibleForClick_mEE3ADEFAD3CF5BCBBAC695A1974870E9F3781AA7 (void);
// 0x000005A2 System.Void UnityEngine.EventSystems.PointerEventData::set_eligibleForClick(System.Boolean)
extern void PointerEventData_set_eligibleForClick_m5CFAF671C2B33AF8E9153FA4826D93B9308C4C07 (void);
// 0x000005A3 System.Int32 UnityEngine.EventSystems.PointerEventData::get_pointerId()
extern void PointerEventData_get_pointerId_m50BE6AA34EE21DA6BE7AF07AAC9115CAB6B0636A (void);
// 0x000005A4 System.Void UnityEngine.EventSystems.PointerEventData::set_pointerId(System.Int32)
extern void PointerEventData_set_pointerId_m7CD30C18A42AD6A99B435816B58F6CFDF42777D4 (void);
// 0x000005A5 UnityEngine.Vector2 UnityEngine.EventSystems.PointerEventData::get_position()
extern void PointerEventData_get_position_mE65C1CF448C935678F7C2A6265B4F3906FD9D651 (void);
// 0x000005A6 System.Void UnityEngine.EventSystems.PointerEventData::set_position(UnityEngine.Vector2)
extern void PointerEventData_set_position_m65960EBCA54317C91CEFFC4893466F87FB168BBF (void);
// 0x000005A7 UnityEngine.Vector2 UnityEngine.EventSystems.PointerEventData::get_delta()
extern void PointerEventData_get_delta_mCEECFB10CBB95E1C5FFD8A24B54A3989D926CA34 (void);
// 0x000005A8 System.Void UnityEngine.EventSystems.PointerEventData::set_delta(UnityEngine.Vector2)
extern void PointerEventData_set_delta_m30E0BE702A57A13FEA52CA55D4B29DDE66931261 (void);
// 0x000005A9 UnityEngine.Vector2 UnityEngine.EventSystems.PointerEventData::get_pressPosition()
extern void PointerEventData_get_pressPosition_mB8F60EB21F6E6892EC731382614BAB85E29ED642 (void);
// 0x000005AA System.Void UnityEngine.EventSystems.PointerEventData::set_pressPosition(UnityEngine.Vector2)
extern void PointerEventData_set_pressPosition_mE644EE1603DFF2087224FF6364EA0204D04D7939 (void);
// 0x000005AB UnityEngine.Vector3 UnityEngine.EventSystems.PointerEventData::get_worldPosition()
extern void PointerEventData_get_worldPosition_mFA3E9DD557E07F51C7D7D75F505DB93BEB2A9426 (void);
// 0x000005AC System.Void UnityEngine.EventSystems.PointerEventData::set_worldPosition(UnityEngine.Vector3)
extern void PointerEventData_set_worldPosition_m87A8206A970075895344E014F49FD2D58FE3B366 (void);
// 0x000005AD UnityEngine.Vector3 UnityEngine.EventSystems.PointerEventData::get_worldNormal()
extern void PointerEventData_get_worldNormal_m75ADD877237B3E030EEA436A28943BB2B8704066 (void);
// 0x000005AE System.Void UnityEngine.EventSystems.PointerEventData::set_worldNormal(UnityEngine.Vector3)
extern void PointerEventData_set_worldNormal_m2B698E666B463EAB0D60DABAB68DCC60A2B8EC9F (void);
// 0x000005AF System.Single UnityEngine.EventSystems.PointerEventData::get_clickTime()
extern void PointerEventData_get_clickTime_m08F7FD164EFE2AE7B47A15C70BC418632B9E5950 (void);
// 0x000005B0 System.Void UnityEngine.EventSystems.PointerEventData::set_clickTime(System.Single)
extern void PointerEventData_set_clickTime_m215E254F8585FFC518E3161FAF9137388F64AC58 (void);
// 0x000005B1 System.Int32 UnityEngine.EventSystems.PointerEventData::get_clickCount()
extern void PointerEventData_get_clickCount_mB44AAB99335BD7D2BD93E40DAC282A56202E44F2 (void);
// 0x000005B2 System.Void UnityEngine.EventSystems.PointerEventData::set_clickCount(System.Int32)
extern void PointerEventData_set_clickCount_m2EAAB7F43CE26BF505B7FCF7D509C988DCFD7F28 (void);
// 0x000005B3 UnityEngine.Vector2 UnityEngine.EventSystems.PointerEventData::get_scrollDelta()
extern void PointerEventData_get_scrollDelta_m4E15304EBE0928F78F7178A5497C1533FC33E7A8 (void);
// 0x000005B4 System.Void UnityEngine.EventSystems.PointerEventData::set_scrollDelta(UnityEngine.Vector2)
extern void PointerEventData_set_scrollDelta_m3ECB852D4D953D59C617DF0FE9E44B0EEF8AEA13 (void);
// 0x000005B5 System.Boolean UnityEngine.EventSystems.PointerEventData::get_useDragThreshold()
extern void PointerEventData_get_useDragThreshold_m683F708E7E5F8351D8DD354A3B2CA9B290683BEE (void);
// 0x000005B6 System.Void UnityEngine.EventSystems.PointerEventData::set_useDragThreshold(System.Boolean)
extern void PointerEventData_set_useDragThreshold_m146893D383B122225651D7882A6998FFB4274C85 (void);
// 0x000005B7 System.Boolean UnityEngine.EventSystems.PointerEventData::get_dragging()
extern void PointerEventData_get_dragging_m7FD3F5D4D8DAC559A57EDB88F2B2B5DEA4B48266 (void);
// 0x000005B8 System.Void UnityEngine.EventSystems.PointerEventData::set_dragging(System.Boolean)
extern void PointerEventData_set_dragging_mEB739C44F1B1848B4B3F4E7FBB9B376587C2C7E1 (void);
// 0x000005B9 UnityEngine.EventSystems.PointerEventData/InputButton UnityEngine.EventSystems.PointerEventData::get_button()
extern void PointerEventData_get_button_m180AAB76815A20002896B6B3AAC5B27D9598CDC1 (void);
// 0x000005BA System.Void UnityEngine.EventSystems.PointerEventData::set_button(UnityEngine.EventSystems.PointerEventData/InputButton)
extern void PointerEventData_set_button_m207898937082827843819AB2DBDA46F90EEDA9B7 (void);
// 0x000005BB System.Void UnityEngine.EventSystems.PointerEventData::.ctor(UnityEngine.EventSystems.EventSystem)
extern void PointerEventData__ctor_m3A877590C20995B4F549C6923BBE2B0901A684F2 (void);
// 0x000005BC System.Boolean UnityEngine.EventSystems.PointerEventData::IsPointerMoving()
extern void PointerEventData_IsPointerMoving_m4D3F38B1E1DA05DF39C98D39F1355A0456C84923 (void);
// 0x000005BD System.Boolean UnityEngine.EventSystems.PointerEventData::IsScrolling()
extern void PointerEventData_IsScrolling_mC0A9A01895E5028145FA87A0B633F76A5DEE6DA8 (void);
// 0x000005BE UnityEngine.Camera UnityEngine.EventSystems.PointerEventData::get_enterEventCamera()
extern void PointerEventData_get_enterEventCamera_m5C21DFBFE45E241DD29EA035D51146859DE03774 (void);
// 0x000005BF UnityEngine.Camera UnityEngine.EventSystems.PointerEventData::get_pressEventCamera()
extern void PointerEventData_get_pressEventCamera_m514C040A3C32E269345D0FC8B72BB2FE553FA448 (void);
// 0x000005C0 UnityEngine.GameObject UnityEngine.EventSystems.PointerEventData::get_pointerPress()
extern void PointerEventData_get_pointerPress_mB55C5528AF445DB7B912086E43F0BCD9CDFF409C (void);
// 0x000005C1 System.Void UnityEngine.EventSystems.PointerEventData::set_pointerPress(UnityEngine.GameObject)
extern void PointerEventData_set_pointerPress_mF37D23566DDB326EB2CFE59592F8538F23BA0EC0 (void);
// 0x000005C2 System.String UnityEngine.EventSystems.PointerEventData::ToString()
extern void PointerEventData_ToString_m9AE3F27AE062B31B4CC9B015B1E17152CF705E0C (void);
// 0x000005C3 System.Void UnityEngine.EventSystems.IPointerEnterHandler::OnPointerEnter(UnityEngine.EventSystems.PointerEventData)
// 0x000005C4 System.Void UnityEngine.EventSystems.IPointerExitHandler::OnPointerExit(UnityEngine.EventSystems.PointerEventData)
// 0x000005C5 System.Void UnityEngine.EventSystems.IPointerDownHandler::OnPointerDown(UnityEngine.EventSystems.PointerEventData)
// 0x000005C6 System.Void UnityEngine.EventSystems.IPointerUpHandler::OnPointerUp(UnityEngine.EventSystems.PointerEventData)
// 0x000005C7 System.Void UnityEngine.EventSystems.IPointerClickHandler::OnPointerClick(UnityEngine.EventSystems.PointerEventData)
// 0x000005C8 System.Void UnityEngine.EventSystems.IBeginDragHandler::OnBeginDrag(UnityEngine.EventSystems.PointerEventData)
// 0x000005C9 System.Void UnityEngine.EventSystems.IInitializePotentialDragHandler::OnInitializePotentialDrag(UnityEngine.EventSystems.PointerEventData)
// 0x000005CA System.Void UnityEngine.EventSystems.IDragHandler::OnDrag(UnityEngine.EventSystems.PointerEventData)
// 0x000005CB System.Void UnityEngine.EventSystems.IEndDragHandler::OnEndDrag(UnityEngine.EventSystems.PointerEventData)
// 0x000005CC System.Void UnityEngine.EventSystems.IDropHandler::OnDrop(UnityEngine.EventSystems.PointerEventData)
// 0x000005CD System.Void UnityEngine.EventSystems.IScrollHandler::OnScroll(UnityEngine.EventSystems.PointerEventData)
// 0x000005CE System.Void UnityEngine.EventSystems.IUpdateSelectedHandler::OnUpdateSelected(UnityEngine.EventSystems.BaseEventData)
// 0x000005CF System.Void UnityEngine.EventSystems.ISelectHandler::OnSelect(UnityEngine.EventSystems.BaseEventData)
// 0x000005D0 System.Void UnityEngine.EventSystems.IDeselectHandler::OnDeselect(UnityEngine.EventSystems.BaseEventData)
// 0x000005D1 System.Void UnityEngine.EventSystems.IMoveHandler::OnMove(UnityEngine.EventSystems.AxisEventData)
// 0x000005D2 System.Void UnityEngine.EventSystems.ISubmitHandler::OnSubmit(UnityEngine.EventSystems.BaseEventData)
// 0x000005D3 System.Void UnityEngine.EventSystems.ICancelHandler::OnCancel(UnityEngine.EventSystems.BaseEventData)
// 0x000005D4 UnityEngine.EventSystems.EventSystem UnityEngine.EventSystems.EventSystem::get_current()
extern void EventSystem_get_current_m6DCD04BC8A3C673A2961DB3BC373EDC0BEAD8251 (void);
// 0x000005D5 System.Void UnityEngine.EventSystems.EventSystem::set_current(UnityEngine.EventSystems.EventSystem)
extern void EventSystem_set_current_mAE3E20E127734F7FBF0481911903203FA07EE3C1 (void);
// 0x000005D6 System.Boolean UnityEngine.EventSystems.EventSystem::get_sendNavigationEvents()
extern void EventSystem_get_sendNavigationEvents_m6577B15136A3AAE95673BBE20109F12C4BB2D023 (void);
// 0x000005D7 System.Void UnityEngine.EventSystems.EventSystem::set_sendNavigationEvents(System.Boolean)
extern void EventSystem_set_sendNavigationEvents_mC4AF68C06C2A8E1017142D7C9C9AA29018F56F96 (void);
// 0x000005D8 System.Int32 UnityEngine.EventSystems.EventSystem::get_pixelDragThreshold()
extern void EventSystem_get_pixelDragThreshold_mE6BC245DF53C04B06DCDF7663BF40087696D793A (void);
// 0x000005D9 System.Void UnityEngine.EventSystems.EventSystem::set_pixelDragThreshold(System.Int32)
extern void EventSystem_set_pixelDragThreshold_m2EC2BBAAD11C1DDFC3D8AF1A6CBB827F47FDA951 (void);
// 0x000005DA UnityEngine.EventSystems.BaseInputModule UnityEngine.EventSystems.EventSystem::get_currentInputModule()
extern void EventSystem_get_currentInputModule_mA369862FF1DB0C9CD447DE69F1E77DF0C0AE37E3 (void);
// 0x000005DB UnityEngine.GameObject UnityEngine.EventSystems.EventSystem::get_firstSelectedGameObject()
extern void EventSystem_get_firstSelectedGameObject_mE8CE4C529A7849B4A0C0EC51E61037A0F7227EF0 (void);
// 0x000005DC System.Void UnityEngine.EventSystems.EventSystem::set_firstSelectedGameObject(UnityEngine.GameObject)
extern void EventSystem_set_firstSelectedGameObject_m385908443CAABBD7BCDF17C3D0A43FE6CE697075 (void);
// 0x000005DD UnityEngine.GameObject UnityEngine.EventSystems.EventSystem::get_currentSelectedGameObject()
extern void EventSystem_get_currentSelectedGameObject_m999F9BFD4C20E2F00C56D4FED89602B6077EF70D (void);
// 0x000005DE UnityEngine.GameObject UnityEngine.EventSystems.EventSystem::get_lastSelectedGameObject()
extern void EventSystem_get_lastSelectedGameObject_m92B25EBE0F4D7F656C95B365474623107D93D4FB (void);
// 0x000005DF System.Boolean UnityEngine.EventSystems.EventSystem::get_isFocused()
extern void EventSystem_get_isFocused_m22370735AB4FCB930C65F3766E5965FCBDD55407 (void);
// 0x000005E0 System.Void UnityEngine.EventSystems.EventSystem::.ctor()
extern void EventSystem__ctor_m19F2A25AFA7FC3BCCAD198F84643C52ABF8402DD (void);
// 0x000005E1 System.Void UnityEngine.EventSystems.EventSystem::UpdateModules()
extern void EventSystem_UpdateModules_m30564B2CFC0B3AA885FCB6E6D8BE104019D5F0D9 (void);
// 0x000005E2 System.Boolean UnityEngine.EventSystems.EventSystem::get_alreadySelecting()
extern void EventSystem_get_alreadySelecting_mEC3798F034DC597BFD2D0E36E072C37196F1991A (void);
// 0x000005E3 System.Void UnityEngine.EventSystems.EventSystem::SetSelectedGameObject(UnityEngine.GameObject,UnityEngine.EventSystems.BaseEventData)
extern void EventSystem_SetSelectedGameObject_m7F0F2E78C18FD468E8B5083AFDA6E9D9364D3D5F (void);
// 0x000005E4 UnityEngine.EventSystems.BaseEventData UnityEngine.EventSystems.EventSystem::get_baseEventDataCache()
extern void EventSystem_get_baseEventDataCache_mDBDF0D5AEEADB4BF9AB9D11B961D9B2B083870AB (void);
// 0x000005E5 System.Void UnityEngine.EventSystems.EventSystem::SetSelectedGameObject(UnityEngine.GameObject)
extern void EventSystem_SetSelectedGameObject_m1B663E3ECF102F750BAA354FBD391BA13B8CBE55 (void);
// 0x000005E6 System.Int32 UnityEngine.EventSystems.EventSystem::RaycastComparer(UnityEngine.EventSystems.RaycastResult,UnityEngine.EventSystems.RaycastResult)
extern void EventSystem_RaycastComparer_mE4881B6740E6ECB12154D3E2753A98C89840CBFE (void);
// 0x000005E7 System.Void UnityEngine.EventSystems.EventSystem::RaycastAll(UnityEngine.EventSystems.PointerEventData,System.Collections.Generic.List`1<UnityEngine.EventSystems.RaycastResult>)
extern void EventSystem_RaycastAll_m45A359AEC0BED3D47B50A862A2F69911373DE934 (void);
// 0x000005E8 System.Boolean UnityEngine.EventSystems.EventSystem::IsPointerOverGameObject()
extern void EventSystem_IsPointerOverGameObject_m49888E0F63763086DCD74D5A6C5C5705A96F88EB (void);
// 0x000005E9 System.Boolean UnityEngine.EventSystems.EventSystem::IsPointerOverGameObject(System.Int32)
extern void EventSystem_IsPointerOverGameObject_mE7043E54617B8289C81A1C7342FBE0AE448C9E3A (void);
// 0x000005EA System.Void UnityEngine.EventSystems.EventSystem::OnEnable()
extern void EventSystem_OnEnable_m05C025C751DF60D22C29650A83893E9274903BC1 (void);
// 0x000005EB System.Void UnityEngine.EventSystems.EventSystem::OnDisable()
extern void EventSystem_OnDisable_m990C9CC348A614D8EB77D8FA2014D4E575CDABA9 (void);
// 0x000005EC System.Void UnityEngine.EventSystems.EventSystem::TickModules()
extern void EventSystem_TickModules_m421AFDE7169843B4FDB8AA174AD8CE6673EE5785 (void);
// 0x000005ED System.Void UnityEngine.EventSystems.EventSystem::OnApplicationFocus(System.Boolean)
extern void EventSystem_OnApplicationFocus_m586A4F937B5866B055FEA38B4C0D8016BBB8E247 (void);
// 0x000005EE System.Void UnityEngine.EventSystems.EventSystem::Update()
extern void EventSystem_Update_mF0C1580BB2C9A125C27282F471DB4DE6B772DE6D (void);
// 0x000005EF System.Void UnityEngine.EventSystems.EventSystem::ChangeEventModule(UnityEngine.EventSystems.BaseInputModule)
extern void EventSystem_ChangeEventModule_m44C4A0697EF6E624741DFCDC13E9C109559846D4 (void);
// 0x000005F0 System.String UnityEngine.EventSystems.EventSystem::ToString()
extern void EventSystem_ToString_m40B166AD49EE4E9CCC0872EE3AA44920A121D075 (void);
// 0x000005F1 System.Void UnityEngine.EventSystems.EventSystem::.cctor()
extern void EventSystem__cctor_m2BF2E38A1CE15A67C7B153C0B58592361E52B6F9 (void);
// 0x000005F2 System.Collections.Generic.List`1<UnityEngine.EventSystems.EventTrigger/Entry> UnityEngine.EventSystems.EventTrigger::get_delegates()
extern void EventTrigger_get_delegates_m3E7DA62318A815A551F877F67642A9F2E5A4AF97 (void);
// 0x000005F3 System.Void UnityEngine.EventSystems.EventTrigger::set_delegates(System.Collections.Generic.List`1<UnityEngine.EventSystems.EventTrigger/Entry>)
extern void EventTrigger_set_delegates_m9C5DBA73484F576E272808F9B4324EA456A1769A (void);
// 0x000005F4 System.Void UnityEngine.EventSystems.EventTrigger::.ctor()
extern void EventTrigger__ctor_m6C07DACEA4CBC537B714051B7DAFEFCE21ABC050 (void);
// 0x000005F5 System.Collections.Generic.List`1<UnityEngine.EventSystems.EventTrigger/Entry> UnityEngine.EventSystems.EventTrigger::get_triggers()
extern void EventTrigger_get_triggers_m84979D5401D19622466C657F16931BEE24021695 (void);
// 0x000005F6 System.Void UnityEngine.EventSystems.EventTrigger::set_triggers(System.Collections.Generic.List`1<UnityEngine.EventSystems.EventTrigger/Entry>)
extern void EventTrigger_set_triggers_m07BA2416DFBCB1BB168AC9B362A7E7782FDC788F (void);
// 0x000005F7 System.Void UnityEngine.EventSystems.EventTrigger::Execute(UnityEngine.EventSystems.EventTriggerType,UnityEngine.EventSystems.BaseEventData)
extern void EventTrigger_Execute_m3E270D493082B7A1307A233C78D54D2740ADF0C1 (void);
// 0x000005F8 System.Void UnityEngine.EventSystems.EventTrigger::OnPointerEnter(UnityEngine.EventSystems.PointerEventData)
extern void EventTrigger_OnPointerEnter_mF4C3838BCE04F82EDB95920EBA94B9297B27C109 (void);
// 0x000005F9 System.Void UnityEngine.EventSystems.EventTrigger::OnPointerExit(UnityEngine.EventSystems.PointerEventData)
extern void EventTrigger_OnPointerExit_m1E3BA5AE73D8394C8AD28566EA4B55B8568105C4 (void);
// 0x000005FA System.Void UnityEngine.EventSystems.EventTrigger::OnDrag(UnityEngine.EventSystems.PointerEventData)
extern void EventTrigger_OnDrag_mB6CE0C89FD6EE7B9750A095A9D87268BF72B552C (void);
// 0x000005FB System.Void UnityEngine.EventSystems.EventTrigger::OnDrop(UnityEngine.EventSystems.PointerEventData)
extern void EventTrigger_OnDrop_m781DA5355EAC20814ACC1D6C92431B51E2F5FB2C (void);
// 0x000005FC System.Void UnityEngine.EventSystems.EventTrigger::OnPointerDown(UnityEngine.EventSystems.PointerEventData)
extern void EventTrigger_OnPointerDown_m136FC8C7C99CB939A4C355A19204E9DDC75958F3 (void);
// 0x000005FD System.Void UnityEngine.EventSystems.EventTrigger::OnPointerUp(UnityEngine.EventSystems.PointerEventData)
extern void EventTrigger_OnPointerUp_mE54BD63E811EDB8AFB7A8AC04E2D4B9BB6E78F73 (void);
// 0x000005FE System.Void UnityEngine.EventSystems.EventTrigger::OnPointerClick(UnityEngine.EventSystems.PointerEventData)
extern void EventTrigger_OnPointerClick_mEF67FDE69498E521EDE99015C3BDD015CEA25954 (void);
// 0x000005FF System.Void UnityEngine.EventSystems.EventTrigger::OnSelect(UnityEngine.EventSystems.BaseEventData)
extern void EventTrigger_OnSelect_mA2DDB4D5FC8D24203A3F18205DF94605D7965678 (void);
// 0x00000600 System.Void UnityEngine.EventSystems.EventTrigger::OnDeselect(UnityEngine.EventSystems.BaseEventData)
extern void EventTrigger_OnDeselect_mEB27B1C63B0A305B0CECD8DA07E4E640292ACC50 (void);
// 0x00000601 System.Void UnityEngine.EventSystems.EventTrigger::OnScroll(UnityEngine.EventSystems.PointerEventData)
extern void EventTrigger_OnScroll_mA0D1332297A15CE361EAC4F188C81BDB97BE51CE (void);
// 0x00000602 System.Void UnityEngine.EventSystems.EventTrigger::OnMove(UnityEngine.EventSystems.AxisEventData)
extern void EventTrigger_OnMove_mD23375381AD67FFC4F5442A445D1930CBFFC3162 (void);
// 0x00000603 System.Void UnityEngine.EventSystems.EventTrigger::OnUpdateSelected(UnityEngine.EventSystems.BaseEventData)
extern void EventTrigger_OnUpdateSelected_m63066AC94D14F231FAEC2EA796F01418B3ABD0C2 (void);
// 0x00000604 System.Void UnityEngine.EventSystems.EventTrigger::OnInitializePotentialDrag(UnityEngine.EventSystems.PointerEventData)
extern void EventTrigger_OnInitializePotentialDrag_m0F00EA4F3A05BC081A71DACEF242000759D9968A (void);
// 0x00000605 System.Void UnityEngine.EventSystems.EventTrigger::OnBeginDrag(UnityEngine.EventSystems.PointerEventData)
extern void EventTrigger_OnBeginDrag_m5F9E87C7ECC639845489FE40809E78E6B7649E88 (void);
// 0x00000606 System.Void UnityEngine.EventSystems.EventTrigger::OnEndDrag(UnityEngine.EventSystems.PointerEventData)
extern void EventTrigger_OnEndDrag_m2954768898BB2EF5EBC87C18227ADB7767D1A42C (void);
// 0x00000607 System.Void UnityEngine.EventSystems.EventTrigger::OnSubmit(UnityEngine.EventSystems.BaseEventData)
extern void EventTrigger_OnSubmit_m2E749E13FFD211D3CF86A4247B508F37F3B6F880 (void);
// 0x00000608 System.Void UnityEngine.EventSystems.EventTrigger::OnCancel(UnityEngine.EventSystems.BaseEventData)
extern void EventTrigger_OnCancel_m7BCE623201D87C36B05386CFDD14BDDDC45B68FF (void);
// 0x00000609 System.Void UnityEngine.EventSystems.EventTrigger/TriggerEvent::.ctor()
extern void TriggerEvent__ctor_m6693E5CD57DF6DFB9411F5AE5C0C35958CC9FBFD (void);
// 0x0000060A System.Void UnityEngine.EventSystems.EventTrigger/Entry::.ctor()
extern void Entry__ctor_m033E853903D01DE79E1534A6DD2692E8CC9AAA7F (void);
// 0x0000060B T UnityEngine.EventSystems.ExecuteEvents::ValidateEventData(UnityEngine.EventSystems.BaseEventData)
// 0x0000060C System.Void UnityEngine.EventSystems.ExecuteEvents::Execute(UnityEngine.EventSystems.IPointerEnterHandler,UnityEngine.EventSystems.BaseEventData)
extern void ExecuteEvents_Execute_m536B5CEFBA924D2E26638E1C6B54F611B10E6A2E (void);
// 0x0000060D System.Void UnityEngine.EventSystems.ExecuteEvents::Execute(UnityEngine.EventSystems.IPointerExitHandler,UnityEngine.EventSystems.BaseEventData)
extern void ExecuteEvents_Execute_m30D10832652D26262469D572E19B7FE806B51ECE (void);
// 0x0000060E System.Void UnityEngine.EventSystems.ExecuteEvents::Execute(UnityEngine.EventSystems.IPointerDownHandler,UnityEngine.EventSystems.BaseEventData)
extern void ExecuteEvents_Execute_mDCAA504C218930ED94C32DCB5F257E68B3018377 (void);
// 0x0000060F System.Void UnityEngine.EventSystems.ExecuteEvents::Execute(UnityEngine.EventSystems.IPointerUpHandler,UnityEngine.EventSystems.BaseEventData)
extern void ExecuteEvents_Execute_mDB4E5999550D766846D363254C6727FE9C593E0F (void);
// 0x00000610 System.Void UnityEngine.EventSystems.ExecuteEvents::Execute(UnityEngine.EventSystems.IPointerClickHandler,UnityEngine.EventSystems.BaseEventData)
extern void ExecuteEvents_Execute_m709B2C8315D1D4E33B4BD0FCDE937621E9C1C440 (void);
// 0x00000611 System.Void UnityEngine.EventSystems.ExecuteEvents::Execute(UnityEngine.EventSystems.IInitializePotentialDragHandler,UnityEngine.EventSystems.BaseEventData)
extern void ExecuteEvents_Execute_mB1759A12A08782510DEB64E2F8852B0332005EAC (void);
// 0x00000612 System.Void UnityEngine.EventSystems.ExecuteEvents::Execute(UnityEngine.EventSystems.IBeginDragHandler,UnityEngine.EventSystems.BaseEventData)
extern void ExecuteEvents_Execute_mB5EC0B81C27785F239F1E4B42A8365EB4EC0F6B6 (void);
// 0x00000613 System.Void UnityEngine.EventSystems.ExecuteEvents::Execute(UnityEngine.EventSystems.IDragHandler,UnityEngine.EventSystems.BaseEventData)
extern void ExecuteEvents_Execute_mCCACEE2E4C3F164D70A966E4E5477E0AA2470F17 (void);
// 0x00000614 System.Void UnityEngine.EventSystems.ExecuteEvents::Execute(UnityEngine.EventSystems.IEndDragHandler,UnityEngine.EventSystems.BaseEventData)
extern void ExecuteEvents_Execute_mDAAEE18D8BB143EDF2595B8468F3DE7A44F45A7C (void);
// 0x00000615 System.Void UnityEngine.EventSystems.ExecuteEvents::Execute(UnityEngine.EventSystems.IDropHandler,UnityEngine.EventSystems.BaseEventData)
extern void ExecuteEvents_Execute_m3B01C05FC40D0AA092CCC0F830C91B9B47DF383E (void);
// 0x00000616 System.Void UnityEngine.EventSystems.ExecuteEvents::Execute(UnityEngine.EventSystems.IScrollHandler,UnityEngine.EventSystems.BaseEventData)
extern void ExecuteEvents_Execute_m2E84A550F3B3EFE1BF2C417EDD158BD944B48E49 (void);
// 0x00000617 System.Void UnityEngine.EventSystems.ExecuteEvents::Execute(UnityEngine.EventSystems.IUpdateSelectedHandler,UnityEngine.EventSystems.BaseEventData)
extern void ExecuteEvents_Execute_m2E26F3129D035DD3B43041DE39AB8E0880F9F0CA (void);
// 0x00000618 System.Void UnityEngine.EventSystems.ExecuteEvents::Execute(UnityEngine.EventSystems.ISelectHandler,UnityEngine.EventSystems.BaseEventData)
extern void ExecuteEvents_Execute_m597F69E7172FEDF9E9F37D5FE3978655BC01E456 (void);
// 0x00000619 System.Void UnityEngine.EventSystems.ExecuteEvents::Execute(UnityEngine.EventSystems.IDeselectHandler,UnityEngine.EventSystems.BaseEventData)
extern void ExecuteEvents_Execute_m621BABB42458C80357CA654A88ECF01058EF527B (void);
// 0x0000061A System.Void UnityEngine.EventSystems.ExecuteEvents::Execute(UnityEngine.EventSystems.IMoveHandler,UnityEngine.EventSystems.BaseEventData)
extern void ExecuteEvents_Execute_m49E6267D041559963C35D5468121DD835788C57D (void);
// 0x0000061B System.Void UnityEngine.EventSystems.ExecuteEvents::Execute(UnityEngine.EventSystems.ISubmitHandler,UnityEngine.EventSystems.BaseEventData)
extern void ExecuteEvents_Execute_m4523B7C8F3C34D2C24260200A66B7BB7E09E83B2 (void);
// 0x0000061C System.Void UnityEngine.EventSystems.ExecuteEvents::Execute(UnityEngine.EventSystems.ICancelHandler,UnityEngine.EventSystems.BaseEventData)
extern void ExecuteEvents_Execute_m32FDEFE4F6E949A5D166ABEC7672C10CBF033214 (void);
// 0x0000061D UnityEngine.EventSystems.ExecuteEvents/EventFunction`1<UnityEngine.EventSystems.IPointerEnterHandler> UnityEngine.EventSystems.ExecuteEvents::get_pointerEnterHandler()
extern void ExecuteEvents_get_pointerEnterHandler_m32A890182BF379E2CF1C1BE8450516852B6E198F (void);
// 0x0000061E UnityEngine.EventSystems.ExecuteEvents/EventFunction`1<UnityEngine.EventSystems.IPointerExitHandler> UnityEngine.EventSystems.ExecuteEvents::get_pointerExitHandler()
extern void ExecuteEvents_get_pointerExitHandler_mA36731A5F269B34CCB5397CBD656028D959AEACD (void);
// 0x0000061F UnityEngine.EventSystems.ExecuteEvents/EventFunction`1<UnityEngine.EventSystems.IPointerDownHandler> UnityEngine.EventSystems.ExecuteEvents::get_pointerDownHandler()
extern void ExecuteEvents_get_pointerDownHandler_m3DD79981D37B9E9E914685190D1E68DCA26E53B8 (void);
// 0x00000620 UnityEngine.EventSystems.ExecuteEvents/EventFunction`1<UnityEngine.EventSystems.IPointerUpHandler> UnityEngine.EventSystems.ExecuteEvents::get_pointerUpHandler()
extern void ExecuteEvents_get_pointerUpHandler_mD3046445618125409AE4DDE90DCC4100E08DCD26 (void);
// 0x00000621 UnityEngine.EventSystems.ExecuteEvents/EventFunction`1<UnityEngine.EventSystems.IPointerClickHandler> UnityEngine.EventSystems.ExecuteEvents::get_pointerClickHandler()
extern void ExecuteEvents_get_pointerClickHandler_m67F8690CE7DC954F2FE78817D2ED931DEE472014 (void);
// 0x00000622 UnityEngine.EventSystems.ExecuteEvents/EventFunction`1<UnityEngine.EventSystems.IInitializePotentialDragHandler> UnityEngine.EventSystems.ExecuteEvents::get_initializePotentialDrag()
extern void ExecuteEvents_get_initializePotentialDrag_mD2F94D3BD9F9125494B1379E0D63ECC414334A94 (void);
// 0x00000623 UnityEngine.EventSystems.ExecuteEvents/EventFunction`1<UnityEngine.EventSystems.IBeginDragHandler> UnityEngine.EventSystems.ExecuteEvents::get_beginDragHandler()
extern void ExecuteEvents_get_beginDragHandler_mD706383D0CF9271281EB0685C15BAD32D6C381BD (void);
// 0x00000624 UnityEngine.EventSystems.ExecuteEvents/EventFunction`1<UnityEngine.EventSystems.IDragHandler> UnityEngine.EventSystems.ExecuteEvents::get_dragHandler()
extern void ExecuteEvents_get_dragHandler_m0CE246D3F757907CE5C647FE3B20DE6A9D017DAD (void);
// 0x00000625 UnityEngine.EventSystems.ExecuteEvents/EventFunction`1<UnityEngine.EventSystems.IEndDragHandler> UnityEngine.EventSystems.ExecuteEvents::get_endDragHandler()
extern void ExecuteEvents_get_endDragHandler_m9DBA9494461A07E9ECFA512C8CA4553CE373747F (void);
// 0x00000626 UnityEngine.EventSystems.ExecuteEvents/EventFunction`1<UnityEngine.EventSystems.IDropHandler> UnityEngine.EventSystems.ExecuteEvents::get_dropHandler()
extern void ExecuteEvents_get_dropHandler_m8BB863BA4EDA61D3961F8B4E2722FD2DA79D63B1 (void);
// 0x00000627 UnityEngine.EventSystems.ExecuteEvents/EventFunction`1<UnityEngine.EventSystems.IScrollHandler> UnityEngine.EventSystems.ExecuteEvents::get_scrollHandler()
extern void ExecuteEvents_get_scrollHandler_m18125DE86004CCAA9D59B3826910168821C6DA9F (void);
// 0x00000628 UnityEngine.EventSystems.ExecuteEvents/EventFunction`1<UnityEngine.EventSystems.IUpdateSelectedHandler> UnityEngine.EventSystems.ExecuteEvents::get_updateSelectedHandler()
extern void ExecuteEvents_get_updateSelectedHandler_m5F24247DE551CFCDEE7C0F4C7BB1B3CCA3B2AD59 (void);
// 0x00000629 UnityEngine.EventSystems.ExecuteEvents/EventFunction`1<UnityEngine.EventSystems.ISelectHandler> UnityEngine.EventSystems.ExecuteEvents::get_selectHandler()
extern void ExecuteEvents_get_selectHandler_mAB1A2D6F20464477674056C4DC87C2C072BFA0F1 (void);
// 0x0000062A UnityEngine.EventSystems.ExecuteEvents/EventFunction`1<UnityEngine.EventSystems.IDeselectHandler> UnityEngine.EventSystems.ExecuteEvents::get_deselectHandler()
extern void ExecuteEvents_get_deselectHandler_m4E4AAA827016AE259476C761BF26C3112C1A418D (void);
// 0x0000062B UnityEngine.EventSystems.ExecuteEvents/EventFunction`1<UnityEngine.EventSystems.IMoveHandler> UnityEngine.EventSystems.ExecuteEvents::get_moveHandler()
extern void ExecuteEvents_get_moveHandler_m237D825C3257B500DE5755AF3EBD4BBA5ABC93AF (void);
// 0x0000062C UnityEngine.EventSystems.ExecuteEvents/EventFunction`1<UnityEngine.EventSystems.ISubmitHandler> UnityEngine.EventSystems.ExecuteEvents::get_submitHandler()
extern void ExecuteEvents_get_submitHandler_mD2C06DE887C9CFF4BB0A5A0957598B7F6796E2E3 (void);
// 0x0000062D UnityEngine.EventSystems.ExecuteEvents/EventFunction`1<UnityEngine.EventSystems.ICancelHandler> UnityEngine.EventSystems.ExecuteEvents::get_cancelHandler()
extern void ExecuteEvents_get_cancelHandler_m1111AE90C89A94F7AA74F55D84DE9A979F2BF6A3 (void);
// 0x0000062E System.Void UnityEngine.EventSystems.ExecuteEvents::GetEventChain(UnityEngine.GameObject,System.Collections.Generic.IList`1<UnityEngine.Transform>)
extern void ExecuteEvents_GetEventChain_m1B6EA60ED8AB222A384F1B1EC5CD95B6A2D0F52D (void);
// 0x0000062F System.Boolean UnityEngine.EventSystems.ExecuteEvents::Execute(UnityEngine.GameObject,UnityEngine.EventSystems.BaseEventData,UnityEngine.EventSystems.ExecuteEvents/EventFunction`1<T>)
// 0x00000630 UnityEngine.GameObject UnityEngine.EventSystems.ExecuteEvents::ExecuteHierarchy(UnityEngine.GameObject,UnityEngine.EventSystems.BaseEventData,UnityEngine.EventSystems.ExecuteEvents/EventFunction`1<T>)
// 0x00000631 System.Boolean UnityEngine.EventSystems.ExecuteEvents::ShouldSendToComponent(UnityEngine.Component)
// 0x00000632 System.Void UnityEngine.EventSystems.ExecuteEvents::GetEventList(UnityEngine.GameObject,System.Collections.Generic.IList`1<UnityEngine.EventSystems.IEventSystemHandler>)
// 0x00000633 System.Boolean UnityEngine.EventSystems.ExecuteEvents::CanHandleEvent(UnityEngine.GameObject)
// 0x00000634 UnityEngine.GameObject UnityEngine.EventSystems.ExecuteEvents::GetEventHandler(UnityEngine.GameObject)
// 0x00000635 System.Void UnityEngine.EventSystems.ExecuteEvents::.cctor()
extern void ExecuteEvents__cctor_m9913078BF1281F1EF3E643613EE2E28AAA26EE77 (void);
// 0x00000636 System.Void UnityEngine.EventSystems.ExecuteEvents/EventFunction`1::.ctor(System.Object,System.IntPtr)
// 0x00000637 System.Void UnityEngine.EventSystems.ExecuteEvents/EventFunction`1::Invoke(T1,UnityEngine.EventSystems.BaseEventData)
// 0x00000638 System.IAsyncResult UnityEngine.EventSystems.ExecuteEvents/EventFunction`1::BeginInvoke(T1,UnityEngine.EventSystems.BaseEventData,System.AsyncCallback,System.Object)
// 0x00000639 System.Void UnityEngine.EventSystems.ExecuteEvents/EventFunction`1::EndInvoke(System.IAsyncResult)
// 0x0000063A System.Void UnityEngine.EventSystems.ExecuteEvents/<>c::.cctor()
extern void U3CU3Ec__cctor_m6F61DB5FE620344D10CA0C69A9C460821AC456D4 (void);
// 0x0000063B System.Void UnityEngine.EventSystems.ExecuteEvents/<>c::.ctor()
extern void U3CU3Ec__ctor_m24D38AEEE1FA8E42B3C01646B97028C9CCEB1C4B (void);
// 0x0000063C System.Void UnityEngine.EventSystems.ExecuteEvents/<>c::<.cctor>b__79_0(System.Collections.Generic.List`1<UnityEngine.EventSystems.IEventSystemHandler>)
extern void U3CU3Ec_U3C_cctorU3Eb__79_0_m43DB792DBF6037E0969D46AD75F3CED7CD0918E0 (void);
// 0x0000063D System.String UnityEngine.EventSystems.BaseInput::get_compositionString()
extern void BaseInput_get_compositionString_mBE9575EE3844DEFF6DFD161AA198AD07BF7A1F7D (void);
// 0x0000063E UnityEngine.IMECompositionMode UnityEngine.EventSystems.BaseInput::get_imeCompositionMode()
extern void BaseInput_get_imeCompositionMode_mE29EA0423E9AE857D1A82754CDD89E0A7D2D17E2 (void);
// 0x0000063F System.Void UnityEngine.EventSystems.BaseInput::set_imeCompositionMode(UnityEngine.IMECompositionMode)
extern void BaseInput_set_imeCompositionMode_m9E241E681DFB158A35B61B9BEA8CF5F4C9B1E436 (void);
// 0x00000640 UnityEngine.Vector2 UnityEngine.EventSystems.BaseInput::get_compositionCursorPos()
extern void BaseInput_get_compositionCursorPos_m3035B5E76FFC1339B8DC57EC0F25D101E0DA289A (void);
// 0x00000641 System.Void UnityEngine.EventSystems.BaseInput::set_compositionCursorPos(UnityEngine.Vector2)
extern void BaseInput_set_compositionCursorPos_mF1D7CBA6575F4D4D527AEAB1EBD6555A6EFA2E78 (void);
// 0x00000642 System.Boolean UnityEngine.EventSystems.BaseInput::get_mousePresent()
extern void BaseInput_get_mousePresent_m20CD979E8F9CAF3A646AD6EDFA984A9A7DE62FBE (void);
// 0x00000643 System.Boolean UnityEngine.EventSystems.BaseInput::GetMouseButtonDown(System.Int32)
extern void BaseInput_GetMouseButtonDown_m9D82E7B9B737A6DFC021560E722882F33231B1DB (void);
// 0x00000644 System.Boolean UnityEngine.EventSystems.BaseInput::GetMouseButtonUp(System.Int32)
extern void BaseInput_GetMouseButtonUp_mBBAB91395DC957EC3DE40C43E5E586F6DA04AD1A (void);
// 0x00000645 System.Boolean UnityEngine.EventSystems.BaseInput::GetMouseButton(System.Int32)
extern void BaseInput_GetMouseButton_m9AE703917C75AF45B6930B245DD5C77D58DD7817 (void);
// 0x00000646 UnityEngine.Vector2 UnityEngine.EventSystems.BaseInput::get_mousePosition()
extern void BaseInput_get_mousePosition_mAAB1F817C064AC1D4CF89E043EFB4F5BD99A2D7A (void);
// 0x00000647 UnityEngine.Vector2 UnityEngine.EventSystems.BaseInput::get_mouseScrollDelta()
extern void BaseInput_get_mouseScrollDelta_m9DF1C36549FEF1E33F53610C51387A33CBFABD7C (void);
// 0x00000648 System.Boolean UnityEngine.EventSystems.BaseInput::get_touchSupported()
extern void BaseInput_get_touchSupported_m1A3C393FBBD3FE82DF903DDADC8DE9AEC972C343 (void);
// 0x00000649 System.Int32 UnityEngine.EventSystems.BaseInput::get_touchCount()
extern void BaseInput_get_touchCount_mB762096E18B7A7D9E53EB89A57A7DAC2C11EBBF1 (void);
// 0x0000064A UnityEngine.Touch UnityEngine.EventSystems.BaseInput::GetTouch(System.Int32)
extern void BaseInput_GetTouch_mFCDD1677E66D7F577E3A5812F5122F06EF7CA52E (void);
// 0x0000064B System.Single UnityEngine.EventSystems.BaseInput::GetAxisRaw(System.String)
extern void BaseInput_GetAxisRaw_m3C5080572EB58D05414F30186E05C9DA7A531E44 (void);
// 0x0000064C System.Boolean UnityEngine.EventSystems.BaseInput::GetButtonDown(System.String)
extern void BaseInput_GetButtonDown_m8AA48484C44378A1379C55D46B13376DC395A992 (void);
// 0x0000064D System.Void UnityEngine.EventSystems.BaseInput::.ctor()
extern void BaseInput__ctor_mA92AFE4DB6CFCD49C223BAC0C3E71B50037CB87A (void);
// 0x0000064E UnityEngine.EventSystems.BaseInput UnityEngine.EventSystems.BaseInputModule::get_input()
extern void BaseInputModule_get_input_mE238D28A1EB973EBB1FFF5DF2951F9E4CB0ED052 (void);
// 0x0000064F UnityEngine.EventSystems.BaseInput UnityEngine.EventSystems.BaseInputModule::get_inputOverride()
extern void BaseInputModule_get_inputOverride_mAEB5656BE9E6655B6B2EEB5DDDED6F76D330A150 (void);
// 0x00000650 System.Void UnityEngine.EventSystems.BaseInputModule::set_inputOverride(UnityEngine.EventSystems.BaseInput)
extern void BaseInputModule_set_inputOverride_m8A0835E8D9442B3B6BC1BEC17F1F288C9FCC4A37 (void);
// 0x00000651 UnityEngine.EventSystems.EventSystem UnityEngine.EventSystems.BaseInputModule::get_eventSystem()
extern void BaseInputModule_get_eventSystem_m84626EB81106D5CC20F49FB0F6724626D168EE8D (void);
// 0x00000652 System.Void UnityEngine.EventSystems.BaseInputModule::OnEnable()
extern void BaseInputModule_OnEnable_mDF384520411BFEF7B908D0149C7A27431B012190 (void);
// 0x00000653 System.Void UnityEngine.EventSystems.BaseInputModule::OnDisable()
extern void BaseInputModule_OnDisable_mD05849779BEF3EAB5DFAC2B51F4F2127E64FE719 (void);
// 0x00000654 System.Void UnityEngine.EventSystems.BaseInputModule::Process()
// 0x00000655 UnityEngine.EventSystems.RaycastResult UnityEngine.EventSystems.BaseInputModule::FindFirstRaycast(System.Collections.Generic.List`1<UnityEngine.EventSystems.RaycastResult>)
extern void BaseInputModule_FindFirstRaycast_mB83B31E8682EF7967F93F41E359E9F4C366162B3 (void);
// 0x00000656 UnityEngine.EventSystems.MoveDirection UnityEngine.EventSystems.BaseInputModule::DetermineMoveDirection(System.Single,System.Single)
extern void BaseInputModule_DetermineMoveDirection_m9903B3F5EE4B62CBC730821992A63473952AF04F (void);
// 0x00000657 UnityEngine.EventSystems.MoveDirection UnityEngine.EventSystems.BaseInputModule::DetermineMoveDirection(System.Single,System.Single,System.Single)
extern void BaseInputModule_DetermineMoveDirection_m8AC72D11BAF49B6AB8A1C683742F14FA2DFF1CD4 (void);
// 0x00000658 UnityEngine.GameObject UnityEngine.EventSystems.BaseInputModule::FindCommonRoot(UnityEngine.GameObject,UnityEngine.GameObject)
extern void BaseInputModule_FindCommonRoot_mE393C964E7B3E057FBBB695FE3032AF1CBA23563 (void);
// 0x00000659 System.Void UnityEngine.EventSystems.BaseInputModule::HandlePointerExitAndEnter(UnityEngine.EventSystems.PointerEventData,UnityEngine.GameObject)
extern void BaseInputModule_HandlePointerExitAndEnter_mC94EE79B9295384EF83DAABA1FB5EF1146DF969F (void);
// 0x0000065A UnityEngine.EventSystems.AxisEventData UnityEngine.EventSystems.BaseInputModule::GetAxisEventData(System.Single,System.Single,System.Single)
extern void BaseInputModule_GetAxisEventData_m1DF20BD8CE49684203394729ADE225B19D87054A (void);
// 0x0000065B UnityEngine.EventSystems.BaseEventData UnityEngine.EventSystems.BaseInputModule::GetBaseEventData()
extern void BaseInputModule_GetBaseEventData_m632271D84DBDF7AF30B38EC599574281525E673A (void);
// 0x0000065C System.Boolean UnityEngine.EventSystems.BaseInputModule::IsPointerOverGameObject(System.Int32)
extern void BaseInputModule_IsPointerOverGameObject_m45EFDCFA22749B1564DDCCB1555EAEC008012D84 (void);
// 0x0000065D System.Boolean UnityEngine.EventSystems.BaseInputModule::ShouldActivateModule()
extern void BaseInputModule_ShouldActivateModule_m6B2322F919981823C1859A6E51DAACDC9F2DAD61 (void);
// 0x0000065E System.Void UnityEngine.EventSystems.BaseInputModule::DeactivateModule()
extern void BaseInputModule_DeactivateModule_mCB2874A23D5FE0C781DE61D118E94DDC058D7EC5 (void);
// 0x0000065F System.Void UnityEngine.EventSystems.BaseInputModule::ActivateModule()
extern void BaseInputModule_ActivateModule_mA7960DD1DBAB0650F626B160128205601C86C0E4 (void);
// 0x00000660 System.Void UnityEngine.EventSystems.BaseInputModule::UpdateModule()
extern void BaseInputModule_UpdateModule_m6F816553728F18F3F8A86AC957AC9321864B0A1C (void);
// 0x00000661 System.Boolean UnityEngine.EventSystems.BaseInputModule::IsModuleSupported()
extern void BaseInputModule_IsModuleSupported_mB721E3AF9A62305509A080B20E543AB956CA36E3 (void);
// 0x00000662 System.Void UnityEngine.EventSystems.BaseInputModule::.ctor()
extern void BaseInputModule__ctor_m50C7488E22C0930A98E951F27E9235FCD2A593A6 (void);
// 0x00000663 System.Boolean UnityEngine.EventSystems.PointerInputModule::GetPointerData(System.Int32,UnityEngine.EventSystems.PointerEventData&,System.Boolean)
extern void PointerInputModule_GetPointerData_mCEFFC5B4DD8C15508224DC13BA3C4C0C4527A208 (void);
// 0x00000664 System.Void UnityEngine.EventSystems.PointerInputModule::RemovePointerData(UnityEngine.EventSystems.PointerEventData)
extern void PointerInputModule_RemovePointerData_m0DB8FD2375F00D7A1059AD4582F52C1CF048158B (void);
// 0x00000665 UnityEngine.EventSystems.PointerEventData UnityEngine.EventSystems.PointerInputModule::GetTouchPointerEventData(UnityEngine.Touch,System.Boolean&,System.Boolean&)
extern void PointerInputModule_GetTouchPointerEventData_mA53FE69943897DF12DAE6A1C342A53334A41E59F (void);
// 0x00000666 System.Void UnityEngine.EventSystems.PointerInputModule::CopyFromTo(UnityEngine.EventSystems.PointerEventData,UnityEngine.EventSystems.PointerEventData)
extern void PointerInputModule_CopyFromTo_m528B215D9DFA2A247297FEEAAB285F1FAFDD4947 (void);
// 0x00000667 UnityEngine.EventSystems.PointerEventData/FramePressState UnityEngine.EventSystems.PointerInputModule::StateForMouseButton(System.Int32)
extern void PointerInputModule_StateForMouseButton_m39F177D0FC7A73999559451475B98D53C90520CD (void);
// 0x00000668 UnityEngine.EventSystems.PointerInputModule/MouseState UnityEngine.EventSystems.PointerInputModule::GetMousePointerEventData()
extern void PointerInputModule_GetMousePointerEventData_m3B6B2642C1F0D293E6CEC42E75363CB78B779754 (void);
// 0x00000669 UnityEngine.EventSystems.PointerInputModule/MouseState UnityEngine.EventSystems.PointerInputModule::GetMousePointerEventData(System.Int32)
extern void PointerInputModule_GetMousePointerEventData_mE28E21995CF3A463A66C414A2FF63486DA25A172 (void);
// 0x0000066A UnityEngine.EventSystems.PointerEventData UnityEngine.EventSystems.PointerInputModule::GetLastPointerEventData(System.Int32)
extern void PointerInputModule_GetLastPointerEventData_m06FD0ACEF8FA7B77D2600271D386A235B9CB4113 (void);
// 0x0000066B System.Boolean UnityEngine.EventSystems.PointerInputModule::ShouldStartDrag(UnityEngine.Vector2,UnityEngine.Vector2,System.Single,System.Boolean)
extern void PointerInputModule_ShouldStartDrag_m823B785B5CDFB4AE2881FC5F8655453D75F76585 (void);
// 0x0000066C System.Void UnityEngine.EventSystems.PointerInputModule::ProcessMove(UnityEngine.EventSystems.PointerEventData)
extern void PointerInputModule_ProcessMove_mF04EC9467E26F5B1C4830398BCC1FEEAA86D206F (void);
// 0x0000066D System.Void UnityEngine.EventSystems.PointerInputModule::ProcessDrag(UnityEngine.EventSystems.PointerEventData)
extern void PointerInputModule_ProcessDrag_m0625CFD2FED814FF63B97977F65AA0C5F9A5D321 (void);
// 0x0000066E System.Boolean UnityEngine.EventSystems.PointerInputModule::IsPointerOverGameObject(System.Int32)
extern void PointerInputModule_IsPointerOverGameObject_m3C33FE9B778B5BFD623254246AD61ADDDC95DA56 (void);
// 0x0000066F System.Void UnityEngine.EventSystems.PointerInputModule::ClearSelection()
extern void PointerInputModule_ClearSelection_m98255DD7C5D23CDA50EE98C14A0EB2705CBD1233 (void);
// 0x00000670 System.String UnityEngine.EventSystems.PointerInputModule::ToString()
extern void PointerInputModule_ToString_m72DF55F5B65032139E30BCCEC86692929B1BCA22 (void);
// 0x00000671 System.Void UnityEngine.EventSystems.PointerInputModule::DeselectIfSelectionChanged(UnityEngine.GameObject,UnityEngine.EventSystems.BaseEventData)
extern void PointerInputModule_DeselectIfSelectionChanged_m691EBB4E49657B1C21D25B79FB1C2F6ABD870A92 (void);
// 0x00000672 System.Void UnityEngine.EventSystems.PointerInputModule::.ctor()
extern void PointerInputModule__ctor_m7286C77CA28195FA2034695E55DD8A9D9B696DC5 (void);
// 0x00000673 UnityEngine.EventSystems.PointerInputModule/MouseButtonEventData UnityEngine.EventSystems.PointerInputModule/ButtonState::get_eventData()
extern void ButtonState_get_eventData_mC7A3D0172F44EEE3570A751D9DD154C465F0C48F (void);
// 0x00000674 System.Void UnityEngine.EventSystems.PointerInputModule/ButtonState::set_eventData(UnityEngine.EventSystems.PointerInputModule/MouseButtonEventData)
extern void ButtonState_set_eventData_m85A92E7A2104B5A248A7AEA7A8C86F41DB47CC73 (void);
// 0x00000675 UnityEngine.EventSystems.PointerEventData/InputButton UnityEngine.EventSystems.PointerInputModule/ButtonState::get_button()
extern void ButtonState_get_button_m7C3B83551E176EDC1232A65589B4FC685CE022A5 (void);
// 0x00000676 System.Void UnityEngine.EventSystems.PointerInputModule/ButtonState::set_button(UnityEngine.EventSystems.PointerEventData/InputButton)
extern void ButtonState_set_button_mBEA15BAD80964F6716746E100CFF406537D38261 (void);
// 0x00000677 System.Void UnityEngine.EventSystems.PointerInputModule/ButtonState::.ctor()
extern void ButtonState__ctor_m7D9B7D5AB76C393C5A3CD720ECAA2FCE990E8E6F (void);
// 0x00000678 System.Boolean UnityEngine.EventSystems.PointerInputModule/MouseState::AnyPressesThisFrame()
extern void MouseState_AnyPressesThisFrame_mC4D468788B94FA92C544B911EF76F4B51EAD42DD (void);
// 0x00000679 System.Boolean UnityEngine.EventSystems.PointerInputModule/MouseState::AnyReleasesThisFrame()
extern void MouseState_AnyReleasesThisFrame_m1086E36B13BD7BA488138E0B1416AF02D51E8549 (void);
// 0x0000067A UnityEngine.EventSystems.PointerInputModule/ButtonState UnityEngine.EventSystems.PointerInputModule/MouseState::GetButtonState(UnityEngine.EventSystems.PointerEventData/InputButton)
extern void MouseState_GetButtonState_m4CB357F518E9333CAB0CE3A54755429A6B8D0A32 (void);
// 0x0000067B System.Void UnityEngine.EventSystems.PointerInputModule/MouseState::SetButtonState(UnityEngine.EventSystems.PointerEventData/InputButton,UnityEngine.EventSystems.PointerEventData/FramePressState,UnityEngine.EventSystems.PointerEventData)
extern void MouseState_SetButtonState_mA97DA94B17CF78F158EC17EC16283626BE513937 (void);
// 0x0000067C System.Void UnityEngine.EventSystems.PointerInputModule/MouseState::.ctor()
extern void MouseState__ctor_m16EF8D45AF8A178368547BD1CE4FBF9DBC563605 (void);
// 0x0000067D System.Boolean UnityEngine.EventSystems.PointerInputModule/MouseButtonEventData::PressedThisFrame()
extern void MouseButtonEventData_PressedThisFrame_mEB9CB4D5EFBFDD43BB877CBA36FCE0DA8F21C3FF (void);
// 0x0000067E System.Boolean UnityEngine.EventSystems.PointerInputModule/MouseButtonEventData::ReleasedThisFrame()
extern void MouseButtonEventData_ReleasedThisFrame_m014BA45901727A4D5C432BB239D0E076D8A82EA1 (void);
// 0x0000067F System.Void UnityEngine.EventSystems.PointerInputModule/MouseButtonEventData::.ctor()
extern void MouseButtonEventData__ctor_m66CCB772A4D986FB2A401E96F6296A56BBD6A238 (void);
// 0x00000680 System.Void UnityEngine.EventSystems.StandaloneInputModule::.ctor()
extern void StandaloneInputModule__ctor_mC5A24967FF3EE2090171F5284125781551B4CA03 (void);
// 0x00000681 UnityEngine.EventSystems.StandaloneInputModule/InputMode UnityEngine.EventSystems.StandaloneInputModule::get_inputMode()
extern void StandaloneInputModule_get_inputMode_m8045A329EAFE94211AFAAC7FB28597AC9BCC933B (void);
// 0x00000682 System.Boolean UnityEngine.EventSystems.StandaloneInputModule::get_allowActivationOnMobileDevice()
extern void StandaloneInputModule_get_allowActivationOnMobileDevice_m4BF8B0EBEA49CEA7A11A926BADFD21BD4EA78B05 (void);
// 0x00000683 System.Void UnityEngine.EventSystems.StandaloneInputModule::set_allowActivationOnMobileDevice(System.Boolean)
extern void StandaloneInputModule_set_allowActivationOnMobileDevice_mCFF331A9424D7484C0FE8460A86C92F1CCF0305B (void);
// 0x00000684 System.Boolean UnityEngine.EventSystems.StandaloneInputModule::get_forceModuleActive()
extern void StandaloneInputModule_get_forceModuleActive_mAA6BA392D58A841E38CD703DCDD6D9FBEF6F0E44 (void);
// 0x00000685 System.Void UnityEngine.EventSystems.StandaloneInputModule::set_forceModuleActive(System.Boolean)
extern void StandaloneInputModule_set_forceModuleActive_m95923244ACCDF3CCF37DA3EEB9024E8054C844EC (void);
// 0x00000686 System.Single UnityEngine.EventSystems.StandaloneInputModule::get_inputActionsPerSecond()
extern void StandaloneInputModule_get_inputActionsPerSecond_m13886FBCF0A097713959E0512944A0B25835CCCE (void);
// 0x00000687 System.Void UnityEngine.EventSystems.StandaloneInputModule::set_inputActionsPerSecond(System.Single)
extern void StandaloneInputModule_set_inputActionsPerSecond_m7FCEEB1ED0F3FA6AB68F5A3E261843FCE74C563C (void);
// 0x00000688 System.Single UnityEngine.EventSystems.StandaloneInputModule::get_repeatDelay()
extern void StandaloneInputModule_get_repeatDelay_m8CD97CB5F5C9A5BB94FA5652A94EFCED831C6C76 (void);
// 0x00000689 System.Void UnityEngine.EventSystems.StandaloneInputModule::set_repeatDelay(System.Single)
extern void StandaloneInputModule_set_repeatDelay_m1DE2E64F91DE672BB1D10EB8E84BDAABEDE8FDAE (void);
// 0x0000068A System.String UnityEngine.EventSystems.StandaloneInputModule::get_horizontalAxis()
extern void StandaloneInputModule_get_horizontalAxis_mCA9D35F564EBE1AC37D150B9C5223A2B231796D0 (void);
// 0x0000068B System.Void UnityEngine.EventSystems.StandaloneInputModule::set_horizontalAxis(System.String)
extern void StandaloneInputModule_set_horizontalAxis_mAE493FDCAC85A5C3C9B4C45206F1B6DF616B7CA0 (void);
// 0x0000068C System.String UnityEngine.EventSystems.StandaloneInputModule::get_verticalAxis()
extern void StandaloneInputModule_get_verticalAxis_mAE5B1955B8E33C0CE6A20D55787DC88FB3D8E1C8 (void);
// 0x0000068D System.Void UnityEngine.EventSystems.StandaloneInputModule::set_verticalAxis(System.String)
extern void StandaloneInputModule_set_verticalAxis_m5468886D85C85A4A53F42264D73E7E192DFADF8A (void);
// 0x0000068E System.String UnityEngine.EventSystems.StandaloneInputModule::get_submitButton()
extern void StandaloneInputModule_get_submitButton_m4352EE0238C6CF552AB370E34B73F9CFB07567FE (void);
// 0x0000068F System.Void UnityEngine.EventSystems.StandaloneInputModule::set_submitButton(System.String)
extern void StandaloneInputModule_set_submitButton_m0454BCA229604DFBFB917842CD923F5F79E1DB45 (void);
// 0x00000690 System.String UnityEngine.EventSystems.StandaloneInputModule::get_cancelButton()
extern void StandaloneInputModule_get_cancelButton_mE8453E7FE91651674F844B22250704B54BE0C7AB (void);
// 0x00000691 System.Void UnityEngine.EventSystems.StandaloneInputModule::set_cancelButton(System.String)
extern void StandaloneInputModule_set_cancelButton_m1BC3CEE85F591C710FA067F6A34F5D2A3F37ED10 (void);
// 0x00000692 System.Boolean UnityEngine.EventSystems.StandaloneInputModule::ShouldIgnoreEventsOnNoFocus()
extern void StandaloneInputModule_ShouldIgnoreEventsOnNoFocus_m27721F13F2C71F806C0CFFFB2D69CB647528911D (void);
// 0x00000693 System.Void UnityEngine.EventSystems.StandaloneInputModule::UpdateModule()
extern void StandaloneInputModule_UpdateModule_mAEF77BAA4F991BF7CF8E81294AFEB94B7DA1B278 (void);
// 0x00000694 System.Void UnityEngine.EventSystems.StandaloneInputModule::ReleaseMouse(UnityEngine.EventSystems.PointerEventData,UnityEngine.GameObject)
extern void StandaloneInputModule_ReleaseMouse_mEE3FAAA8B87CAE09F156322B7A38E2EC5460E1BB (void);
// 0x00000695 System.Boolean UnityEngine.EventSystems.StandaloneInputModule::IsModuleSupported()
extern void StandaloneInputModule_IsModuleSupported_m3570BE55A4362C2E4CD65603CFD94A425F15011D (void);
// 0x00000696 System.Boolean UnityEngine.EventSystems.StandaloneInputModule::ShouldActivateModule()
extern void StandaloneInputModule_ShouldActivateModule_m2108FFC28DBE61F2574DC0F3D6DAB46E1539F3CC (void);
// 0x00000697 System.Void UnityEngine.EventSystems.StandaloneInputModule::ActivateModule()
extern void StandaloneInputModule_ActivateModule_m7462FAE46BEB2A289F8BB2B001A3474206AB8E8F (void);
// 0x00000698 System.Void UnityEngine.EventSystems.StandaloneInputModule::DeactivateModule()
extern void StandaloneInputModule_DeactivateModule_m6268DCE57831BB227F94F374A1D80CA4BD10C5A2 (void);
// 0x00000699 System.Void UnityEngine.EventSystems.StandaloneInputModule::Process()
extern void StandaloneInputModule_Process_mEA8D89C754B958916467AEB75592670B15519D98 (void);
// 0x0000069A System.Boolean UnityEngine.EventSystems.StandaloneInputModule::ProcessTouchEvents()
extern void StandaloneInputModule_ProcessTouchEvents_m2C06F4FED9D3F300031E889330180C5004034DBA (void);
// 0x0000069B System.Void UnityEngine.EventSystems.StandaloneInputModule::ProcessTouchPress(UnityEngine.EventSystems.PointerEventData,System.Boolean,System.Boolean)
extern void StandaloneInputModule_ProcessTouchPress_m1ACFC2288CC51BD8C85C6894994923B1762B0B49 (void);
// 0x0000069C System.Boolean UnityEngine.EventSystems.StandaloneInputModule::SendSubmitEventToSelectedObject()
extern void StandaloneInputModule_SendSubmitEventToSelectedObject_m294066868523F9D8AB5DA828F9A326C2F6999ED0 (void);
// 0x0000069D UnityEngine.Vector2 UnityEngine.EventSystems.StandaloneInputModule::GetRawMoveVector()
extern void StandaloneInputModule_GetRawMoveVector_mDA3F235097E686FE09FEC4E1A3BC0EB6F8EDF1FE (void);
// 0x0000069E System.Boolean UnityEngine.EventSystems.StandaloneInputModule::SendMoveEventToSelectedObject()
extern void StandaloneInputModule_SendMoveEventToSelectedObject_mA86033B85BCC6D4BB5846B590AB1F2A21FE347ED (void);
// 0x0000069F System.Void UnityEngine.EventSystems.StandaloneInputModule::ProcessMouseEvent()
extern void StandaloneInputModule_ProcessMouseEvent_m0E5CCCC3F32DF86C32E02873DDE2BF29E9A05E37 (void);
// 0x000006A0 System.Boolean UnityEngine.EventSystems.StandaloneInputModule::ForceAutoSelect()
extern void StandaloneInputModule_ForceAutoSelect_m009DD883E1783D97901AFF2D7B7573EB28BC4DBC (void);
// 0x000006A1 System.Void UnityEngine.EventSystems.StandaloneInputModule::ProcessMouseEvent(System.Int32)
extern void StandaloneInputModule_ProcessMouseEvent_m1D697D9E5F2FDF5B770471185CD364D12A89B18A (void);
// 0x000006A2 System.Boolean UnityEngine.EventSystems.StandaloneInputModule::SendUpdateEventToSelectedObject()
extern void StandaloneInputModule_SendUpdateEventToSelectedObject_mDB8B0FD5B0C1AD356C91FF1B301E1EB64197506F (void);
// 0x000006A3 System.Void UnityEngine.EventSystems.StandaloneInputModule::ProcessMousePress(UnityEngine.EventSystems.PointerInputModule/MouseButtonEventData)
extern void StandaloneInputModule_ProcessMousePress_mE5D5A47900D7FAFCBBC58ACBDCB03BE2958FF7A6 (void);
// 0x000006A4 UnityEngine.GameObject UnityEngine.EventSystems.StandaloneInputModule::GetCurrentFocusedGameObject()
extern void StandaloneInputModule_GetCurrentFocusedGameObject_m2DFA63DC619408591F3B0D45186DF2BBEA36AAD1 (void);
// 0x000006A5 System.Void UnityEngine.EventSystems.TouchInputModule::.ctor()
extern void TouchInputModule__ctor_m6DA57CEEFDE230F74BEE3CDED82735AF0ED6E0A1 (void);
// 0x000006A6 System.Boolean UnityEngine.EventSystems.TouchInputModule::get_allowActivationOnStandalone()
extern void TouchInputModule_get_allowActivationOnStandalone_mA995FC27A62E6723C391AB3FDEBB7BCFFEAB6F93 (void);
// 0x000006A7 System.Void UnityEngine.EventSystems.TouchInputModule::set_allowActivationOnStandalone(System.Boolean)
extern void TouchInputModule_set_allowActivationOnStandalone_mB1EFDB582D30720FE84868FC957CC38042517BD9 (void);
// 0x000006A8 System.Boolean UnityEngine.EventSystems.TouchInputModule::get_forceModuleActive()
extern void TouchInputModule_get_forceModuleActive_m0D30D44DE67C0220BDE939DB70F47100344ABD62 (void);
// 0x000006A9 System.Void UnityEngine.EventSystems.TouchInputModule::set_forceModuleActive(System.Boolean)
extern void TouchInputModule_set_forceModuleActive_mDB0018F8FE614769EFBD8F59E088D13BDB9F6DC0 (void);
// 0x000006AA System.Void UnityEngine.EventSystems.TouchInputModule::UpdateModule()
extern void TouchInputModule_UpdateModule_m8B96D764DBEC18DCEF5C58D7DB787435600C8BE7 (void);
// 0x000006AB System.Boolean UnityEngine.EventSystems.TouchInputModule::IsModuleSupported()
extern void TouchInputModule_IsModuleSupported_mB634972650F8E3B3DDCC21B32CF85567FFD269C3 (void);
// 0x000006AC System.Boolean UnityEngine.EventSystems.TouchInputModule::ShouldActivateModule()
extern void TouchInputModule_ShouldActivateModule_mACEA2CCCEFD2A21BA3F43E0D806C386D9F426A52 (void);
// 0x000006AD System.Boolean UnityEngine.EventSystems.TouchInputModule::UseFakeInput()
extern void TouchInputModule_UseFakeInput_mAE7BEFCC688D9572A01983A0EADDC72C8BC55302 (void);
// 0x000006AE System.Void UnityEngine.EventSystems.TouchInputModule::Process()
extern void TouchInputModule_Process_m8A9D5A7E3C80EE254E8260DC59B369EB70C5EC9C (void);
// 0x000006AF System.Void UnityEngine.EventSystems.TouchInputModule::FakeTouches()
extern void TouchInputModule_FakeTouches_mF581740619A868F99690CCA249941049306D6227 (void);
// 0x000006B0 System.Void UnityEngine.EventSystems.TouchInputModule::ProcessTouchEvents()
extern void TouchInputModule_ProcessTouchEvents_mF1371956D57515679F23FD9A3CE7EEA1335C0C99 (void);
// 0x000006B1 System.Void UnityEngine.EventSystems.TouchInputModule::ProcessTouchPress(UnityEngine.EventSystems.PointerEventData,System.Boolean,System.Boolean)
extern void TouchInputModule_ProcessTouchPress_m3705E3E72EAB93BF7476A160ACFF17E8002E3A85 (void);
// 0x000006B2 System.Void UnityEngine.EventSystems.TouchInputModule::DeactivateModule()
extern void TouchInputModule_DeactivateModule_m9DBFE2349C399250B64EDF8BE50E094C8C6EC1E7 (void);
// 0x000006B3 System.String UnityEngine.EventSystems.TouchInputModule::ToString()
extern void TouchInputModule_ToString_m928AD9BE065AED416E067B806A95FBC650956142 (void);
// 0x000006B4 UnityEngine.GameObject UnityEngine.EventSystems.RaycastResult::get_gameObject()
extern void RaycastResult_get_gameObject_mABA10AC828B2E6603A6C088A4CCD40932F6AF5FF (void);
// 0x000006B5 System.Void UnityEngine.EventSystems.RaycastResult::set_gameObject(UnityEngine.GameObject)
extern void RaycastResult_set_gameObject_m3A1B023B578A8142A437B0C27AAE045DED6AAA20 (void);
// 0x000006B6 System.Boolean UnityEngine.EventSystems.RaycastResult::get_isValid()
extern void RaycastResult_get_isValid_m4BC5BF4FDB163ED6BC0D8C47B0B059E76C508ADD (void);
// 0x000006B7 System.Void UnityEngine.EventSystems.RaycastResult::Clear()
extern void RaycastResult_Clear_m00588011F0D28FD65E082C5DAA2E737E3F6AAB9C (void);
// 0x000006B8 System.String UnityEngine.EventSystems.RaycastResult::ToString()
extern void RaycastResult_ToString_mF55ECA45850A1CA8A43FC1BA8F932D6F86C60E3E (void);
// 0x000006B9 System.Void UnityEngine.EventSystems.RaycasterManager::AddRaycaster(UnityEngine.EventSystems.BaseRaycaster)
extern void RaycasterManager_AddRaycaster_mFAB4E0B7F75ED5D04D0ABE2F2C6D6487B1655A0D (void);
// 0x000006BA System.Collections.Generic.List`1<UnityEngine.EventSystems.BaseRaycaster> UnityEngine.EventSystems.RaycasterManager::GetRaycasters()
extern void RaycasterManager_GetRaycasters_m8E8BD8154C405B3E3A0CDB55D5566201C7EE09CC (void);
// 0x000006BB System.Void UnityEngine.EventSystems.RaycasterManager::RemoveRaycasters(UnityEngine.EventSystems.BaseRaycaster)
extern void RaycasterManager_RemoveRaycasters_m60961FD830DE987C78D3E51324AE79A57CC1E8D1 (void);
// 0x000006BC System.Void UnityEngine.EventSystems.RaycasterManager::.cctor()
extern void RaycasterManager__cctor_m191BF078929AC5105A542F2FDE78E0898FE1C46E (void);
// 0x000006BD System.Void UnityEngine.EventSystems.BaseRaycaster::Raycast(UnityEngine.EventSystems.PointerEventData,System.Collections.Generic.List`1<UnityEngine.EventSystems.RaycastResult>)
// 0x000006BE UnityEngine.Camera UnityEngine.EventSystems.BaseRaycaster::get_eventCamera()
// 0x000006BF System.Int32 UnityEngine.EventSystems.BaseRaycaster::get_priority()
extern void BaseRaycaster_get_priority_m98D876E84CB65E725BEA493E7BD55EF024090FE5 (void);
// 0x000006C0 System.Int32 UnityEngine.EventSystems.BaseRaycaster::get_sortOrderPriority()
extern void BaseRaycaster_get_sortOrderPriority_mC8BAA723774E479F397382A60B2F4568DED9268D (void);
// 0x000006C1 System.Int32 UnityEngine.EventSystems.BaseRaycaster::get_renderOrderPriority()
extern void BaseRaycaster_get_renderOrderPriority_mCA1E2F6F1223410DEB343C34FF6CB84DEBA2BCF3 (void);
// 0x000006C2 UnityEngine.EventSystems.BaseRaycaster UnityEngine.EventSystems.BaseRaycaster::get_rootRaycaster()
extern void BaseRaycaster_get_rootRaycaster_m2F2FC8638E1680699F0A6F17402BD7F67D13C996 (void);
// 0x000006C3 System.String UnityEngine.EventSystems.BaseRaycaster::ToString()
extern void BaseRaycaster_ToString_m980644F33B2B63B58E195697099A930A3B5A14F3 (void);
// 0x000006C4 System.Void UnityEngine.EventSystems.BaseRaycaster::OnEnable()
extern void BaseRaycaster_OnEnable_mA87FC2F224D137490912EBEB1039030059E86B5F (void);
// 0x000006C5 System.Void UnityEngine.EventSystems.BaseRaycaster::OnDisable()
extern void BaseRaycaster_OnDisable_mF4C177437644DDCA3EEE5F4EBFF85607A748BD0D (void);
// 0x000006C6 System.Void UnityEngine.EventSystems.BaseRaycaster::OnCanvasHierarchyChanged()
extern void BaseRaycaster_OnCanvasHierarchyChanged_m7F1B49A5A514FAB23FE8FF3CD5C87862316AF9D1 (void);
// 0x000006C7 System.Void UnityEngine.EventSystems.BaseRaycaster::OnTransformParentChanged()
extern void BaseRaycaster_OnTransformParentChanged_mD0500BD255F5BF56180D24C5A5B80095470DB5DC (void);
// 0x000006C8 System.Void UnityEngine.EventSystems.BaseRaycaster::.ctor()
extern void BaseRaycaster__ctor_m44ACAAC3E3CBDBBE74D2FCA997268EE1DF9D8A3C (void);
// 0x000006C9 System.Void UnityEngine.EventSystems.Physics2DRaycaster::.ctor()
extern void Physics2DRaycaster__ctor_mEB8E9794F72BC357DD518AB1519BBF716FA11017 (void);
// 0x000006CA System.Void UnityEngine.EventSystems.Physics2DRaycaster::Raycast(UnityEngine.EventSystems.PointerEventData,System.Collections.Generic.List`1<UnityEngine.EventSystems.RaycastResult>)
extern void Physics2DRaycaster_Raycast_m037A598BC662F1801E08353CF56BE300B34C36BF (void);
// 0x000006CB System.Void UnityEngine.EventSystems.PhysicsRaycaster::.ctor()
extern void PhysicsRaycaster__ctor_mEBFB0AC90FB9FB8A46B2CB8050A4BAE3D1BCC72E (void);
// 0x000006CC UnityEngine.Camera UnityEngine.EventSystems.PhysicsRaycaster::get_eventCamera()
extern void PhysicsRaycaster_get_eventCamera_mC067A7DEC69941B818286767ADE39AAECEC44CE3 (void);
// 0x000006CD System.Int32 UnityEngine.EventSystems.PhysicsRaycaster::get_depth()
extern void PhysicsRaycaster_get_depth_m9B1DC4C8868DF41F665EE4684779078F25ABA7AA (void);
// 0x000006CE System.Int32 UnityEngine.EventSystems.PhysicsRaycaster::get_finalEventMask()
extern void PhysicsRaycaster_get_finalEventMask_m984B1A6765136BB3454C1895EAB28DA03C49A6B2 (void);
// 0x000006CF UnityEngine.LayerMask UnityEngine.EventSystems.PhysicsRaycaster::get_eventMask()
extern void PhysicsRaycaster_get_eventMask_m210CAAB35833B472D235E601F4878C06EFD74DA6 (void);
// 0x000006D0 System.Void UnityEngine.EventSystems.PhysicsRaycaster::set_eventMask(UnityEngine.LayerMask)
extern void PhysicsRaycaster_set_eventMask_mCC9065510DB88DC4E3A44F7F2339B950E64D9FFF (void);
// 0x000006D1 System.Int32 UnityEngine.EventSystems.PhysicsRaycaster::get_maxRayIntersections()
extern void PhysicsRaycaster_get_maxRayIntersections_mDBE92413912BCFD8A50C54B520E5F43C95F3CD27 (void);
// 0x000006D2 System.Void UnityEngine.EventSystems.PhysicsRaycaster::set_maxRayIntersections(System.Int32)
extern void PhysicsRaycaster_set_maxRayIntersections_m5A8FBBE6CB9AE15F7ACC7AD6B46931049CD0BD3C (void);
// 0x000006D3 System.Boolean UnityEngine.EventSystems.PhysicsRaycaster::ComputeRayAndDistance(UnityEngine.EventSystems.PointerEventData,UnityEngine.Ray&,System.Int32&,System.Single&)
extern void PhysicsRaycaster_ComputeRayAndDistance_mE3C052D28ADC1DB6239774007CE80C96E8020A87 (void);
// 0x000006D4 System.Void UnityEngine.EventSystems.PhysicsRaycaster::Raycast(UnityEngine.EventSystems.PointerEventData,System.Collections.Generic.List`1<UnityEngine.EventSystems.RaycastResult>)
extern void PhysicsRaycaster_Raycast_mD597D05EB47ECBFD60E46C317547FB0AA5A2E940 (void);
// 0x000006D5 System.Int32 UnityEngine.EventSystems.PhysicsRaycaster/RaycastHitComparer::Compare(UnityEngine.RaycastHit,UnityEngine.RaycastHit)
extern void RaycastHitComparer_Compare_m856F219A57E2827C0E4E0E075D0A7205CC6C39CE (void);
// 0x000006D6 System.Void UnityEngine.EventSystems.PhysicsRaycaster/RaycastHitComparer::.ctor()
extern void RaycastHitComparer__ctor_m261F5A7BB4E3DD3FD760B27E70D84197DEF00F98 (void);
// 0x000006D7 System.Void UnityEngine.EventSystems.PhysicsRaycaster/RaycastHitComparer::.cctor()
extern void RaycastHitComparer__cctor_m32F4A6DC0DF0FAEA7B07676021CB775ECF867B2A (void);
// 0x000006D8 System.Void UnityEngine.EventSystems.UIBehaviour::Awake()
extern void UIBehaviour_Awake_m0A6FB0A0089B29A53768BFE65D6E06183A1B60BE (void);
// 0x000006D9 System.Void UnityEngine.EventSystems.UIBehaviour::OnEnable()
extern void UIBehaviour_OnEnable_m9BE8F521B232703E4A0EF14EA43F264EDAF3B3F0 (void);
// 0x000006DA System.Void UnityEngine.EventSystems.UIBehaviour::Start()
extern void UIBehaviour_Start_m7334773773C9454A7A6E95613E60762E68B728F7 (void);
// 0x000006DB System.Void UnityEngine.EventSystems.UIBehaviour::OnDisable()
extern void UIBehaviour_OnDisable_m7D3E0D1AC43330C5A50B17DD296D2CB84994CA23 (void);
// 0x000006DC System.Void UnityEngine.EventSystems.UIBehaviour::OnDestroy()
extern void UIBehaviour_OnDestroy_m7D4F82D8ADD8723A4712F376C5D5F0F18A856966 (void);
// 0x000006DD System.Boolean UnityEngine.EventSystems.UIBehaviour::IsActive()
extern void UIBehaviour_IsActive_m14EAD5699E8E72A360B1241146393349E5DCEF07 (void);
// 0x000006DE System.Void UnityEngine.EventSystems.UIBehaviour::OnRectTransformDimensionsChange()
extern void UIBehaviour_OnRectTransformDimensionsChange_mF5614DB1353F7D1E1FC8235641AECFE94DBE03E0 (void);
// 0x000006DF System.Void UnityEngine.EventSystems.UIBehaviour::OnBeforeTransformParentChanged()
extern void UIBehaviour_OnBeforeTransformParentChanged_mEFF5109EE955F34D2D068F0DAD4C4312F5297CD3 (void);
// 0x000006E0 System.Void UnityEngine.EventSystems.UIBehaviour::OnTransformParentChanged()
extern void UIBehaviour_OnTransformParentChanged_m8D3C0D2ADCDFF54D4FB6BD4DB0E91FA7199BB1DB (void);
// 0x000006E1 System.Void UnityEngine.EventSystems.UIBehaviour::OnDidApplyAnimationProperties()
extern void UIBehaviour_OnDidApplyAnimationProperties_mC4A4AF43FD946053995575D0899A4E1E4D444E16 (void);
// 0x000006E2 System.Void UnityEngine.EventSystems.UIBehaviour::OnCanvasGroupChanged()
extern void UIBehaviour_OnCanvasGroupChanged_m1DE1A5688A487CCD9028F5A544D7EC025C2E15BB (void);
// 0x000006E3 System.Void UnityEngine.EventSystems.UIBehaviour::OnCanvasHierarchyChanged()
extern void UIBehaviour_OnCanvasHierarchyChanged_mE516A02869AA87FCF106F85EC95A536C71C8CC67 (void);
// 0x000006E4 System.Boolean UnityEngine.EventSystems.UIBehaviour::IsDestroyed()
extern void UIBehaviour_IsDestroyed_m28D16D52C10659BE98248139BDE1D9C5423043A5 (void);
// 0x000006E5 System.Void UnityEngine.EventSystems.UIBehaviour::.ctor()
extern void UIBehaviour__ctor_m869436738107AF382FD4D10DE9641F8241B323C7 (void);
static Il2CppMethodPointer s_methodPointers[1765] = 
{
	AnimationTriggers_get_normalTrigger_m32A0AABB530B9DB501EEEAD4CD2B464A8D3AD44A,
	AnimationTriggers_set_normalTrigger_mC80FD5EF3A5E3C1DDBFB8D4E0ED537804D628989,
	AnimationTriggers_get_highlightedTrigger_m3EBBC2E67EAAB6F886A6405114FF1C00880938FD,
	AnimationTriggers_set_highlightedTrigger_m2B28CFEFFCDF7576FB796B00F1C218A1AB2D874E,
	AnimationTriggers_get_pressedTrigger_mEA76FE9A10B78E152D6392FF5311F9C92E3E15EC,
	AnimationTriggers_set_pressedTrigger_m80C1E8257889A25700B942282014DDB99F1B1156,
	AnimationTriggers_get_selectedTrigger_m9CD5D37F9D5883819546DEB24959B2DD408C2465,
	AnimationTriggers_set_selectedTrigger_m74C434D7BD62A7502646B7A1C9EE1FF28618CB70,
	AnimationTriggers_get_disabledTrigger_mA66CB9FDC15B8154F4AB4C71384E8E725C9FF4F8,
	AnimationTriggers_set_disabledTrigger_m7B29FA0B3C3E9E30BE8CD2689337D4853C789DB4,
	AnimationTriggers__ctor_m73EF30313E61AD0FB9E687F270ECAEB30CF9DB89,
	Button__ctor_m49611E4C5C9CCDACC1A95EACA4673A88DF1638CE,
	Button_get_onClick_m28BD8C670676D4E2B292B5A7F59387D4BF61F8F4,
	Button_set_onClick_m896AFE34EFF24C941B39AC00E07EEDE470D1D113,
	Button_Press_m24D98A1967E4A82133B5B730EE966513CCFAC124,
	Button_OnPointerClick_m0BB4BB85529C02F25F85D2D1BC82210608767B0E,
	Button_OnSubmit_m25CCF45D13DF76A7F294FBCA4C4D6F89BA90F416,
	Button_OnFinishSubmit_mA6C59616C7BFBFED1887E6C8D1F46D4A0C8C9108,
	ButtonClickedEvent__ctor_m83123C7524AB4DC1FFB95F86455F859572F1B6A8,
	U3COnFinishSubmitU3Ed__9__ctor_m4663BB761D062DC802EABBD3FDBD8FDFA03551EC,
	U3COnFinishSubmitU3Ed__9_System_IDisposable_Dispose_m1EB4BF5C53168DA8C48EFAC7FFE209B4E4090FB6,
	U3COnFinishSubmitU3Ed__9_MoveNext_m23D48ECBECED3420D3B57B062BA5D612B44907B2,
	U3COnFinishSubmitU3Ed__9_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mCE5F932F82D5E0662EB7EC1F04EA53AA6E9E3A62,
	U3COnFinishSubmitU3Ed__9_System_Collections_IEnumerator_Reset_m8C34DF18B3EE52EC98CAECD371811E60CCA7BC1D,
	U3COnFinishSubmitU3Ed__9_System_Collections_IEnumerator_get_Current_m1336764E1957C2B5EBE654AB0C8834996F183534,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	CanvasUpdateRegistry__ctor_m6D0581E2F1AE62935B2AFA50A8D669044AEDDD29,
	CanvasUpdateRegistry_get_instance_m81A024E19B9A7F49208A336886918DE3D232EC3D,
	CanvasUpdateRegistry_ObjectValidForUpdate_m8121661F105B8FB50AAF73674224CF5C647ECE31,
	CanvasUpdateRegistry_CleanInvalidItems_m945A3E5AFF8D2ABE719CC86C960BA7A19D530B1C,
	CanvasUpdateRegistry_PerformUpdate_mC0E85CD761EA6D619AB636E6671CB91DF6605A12,
	CanvasUpdateRegistry_ParentCount_mC41F1B074121B659B34DF850FA9CAC84067482AD,
	CanvasUpdateRegistry_SortLayoutList_m8FEA1C70AA15AF804FC8E2BF0F45824FF36315DF,
	CanvasUpdateRegistry_RegisterCanvasElementForLayoutRebuild_m9542EF33E111AF4F551348563148D791D90D0BAF,
	CanvasUpdateRegistry_TryRegisterCanvasElementForLayoutRebuild_mE82AD265E12721522B359CAD98134B02C3A086C1,
	CanvasUpdateRegistry_InternalRegisterCanvasElementForLayoutRebuild_mFDCDAD147183A8DAB00B3AEDDBB9963C319D7CF3,
	CanvasUpdateRegistry_RegisterCanvasElementForGraphicRebuild_m274722849A82887D5FD1EBC8F2880BF4A1E6D26C,
	CanvasUpdateRegistry_TryRegisterCanvasElementForGraphicRebuild_m3320ED3C5EEBF2F81573EBA0B1E7629136C57E9E,
	CanvasUpdateRegistry_InternalRegisterCanvasElementForGraphicRebuild_m6EA791E10DE1BC0F84CD30C47EAFA03DAF270C28,
	CanvasUpdateRegistry_UnRegisterCanvasElementForRebuild_m540CAC50C52279F2DC58809B4AC2798DBA104952,
	CanvasUpdateRegistry_DisableCanvasElementForRebuild_m401D4002D9B8A0FE78BB74F422DCB1E650EC1104,
	CanvasUpdateRegistry_InternalUnRegisterCanvasElementForLayoutRebuild_mA212240B063E15BD6E7BABBE97C495CCC0E0E977,
	CanvasUpdateRegistry_InternalUnRegisterCanvasElementForGraphicRebuild_m9D8F7A91F2DC520F674697BEF74F6A045B4C239E,
	CanvasUpdateRegistry_InternalDisableCanvasElementForLayoutRebuild_mB2D3B0FFC17A6779DFA0BDC9F3E2CD38D24B8CDB,
	CanvasUpdateRegistry_InternalDisableCanvasElementForGraphicRebuild_m55EF93896A58CFB2E1DF0080142E3CAC9299E1A4,
	CanvasUpdateRegistry_IsRebuildingLayout_mEF1D75977915C7B7775D0ACDC35E76F569C931EA,
	CanvasUpdateRegistry_IsRebuildingGraphics_m32B6EF4628CCF85397BC6F37D057BF5B955291AB,
	CanvasUpdateRegistry__cctor_m2ACC51DA4905EDE64620BABB78F33E4DE43EE388,
	ColorBlock_get_normalColor_m7C5A73BBE739FFA030EE1FE10CE78BECB8417126,
	ColorBlock_set_normalColor_m32EB40A0BB6DD89B8816945EF43CFED8A1ED78B9,
	ColorBlock_get_highlightedColor_mDA12AC25C740DF2AFFF086B62795533B4D2ED336,
	ColorBlock_set_highlightedColor_m176DF68369EC1536D089EABD1357AB8EAC33BC88,
	ColorBlock_get_pressedColor_mCEBCE4184CBAE7D33FE3D8AD3FDFA3931FD87C70,
	ColorBlock_set_pressedColor_m7F357DD0E16A18A84D2DCF039238ADE6695EB5C0,
	ColorBlock_get_selectedColor_m7C592B6652C94BACF43F0C007AC0093A2543F725,
	ColorBlock_set_selectedColor_mA1A0ADD43DBE600443C5A1D217922614D77A6C3C,
	ColorBlock_get_disabledColor_mF470FB3FA48E777F5C70F1C18EF218DB673970E9,
	ColorBlock_set_disabledColor_m0E9C6689B962550D1C3F15275DF2409FD3C28841,
	ColorBlock_get_colorMultiplier_m38A25E26F0AB48B6CB04A7A18EF1EAC2AFD2BA28,
	ColorBlock_set_colorMultiplier_m8A321BF2E07D4D184214014C32F5389C8BE06198,
	ColorBlock_get_fadeDuration_m37083141F2C18A45CC211E4683D1903E3A614B1C,
	ColorBlock_set_fadeDuration_m51827B5F57CFEF2FF8DFB03687A301E2F1B3CDCE,
	ColorBlock__cctor_m12A94594EB642025030047DAB52597B01B65804F,
	ColorBlock_Equals_m4A08484E3305C07A0CBE38C0205E21CDF05C88D7,
	ColorBlock_Equals_m4C4BE778C6BEFA266E497540E97CB3201B13FB6A,
	ColorBlock_op_Equality_mAE2E9F06E81EEC62E10EA85C90DAC9F7739A3C9C,
	ColorBlock_op_Inequality_mDF9B0DC55C4C2AAE2B38BC2C0367DCE14AA64FCB,
	ColorBlock_GetHashCode_m26AE64D514B61123616F54D3FC820A3FE11AA40E,
	ClipperRegistry__ctor_m767DBC8E8436902A800FF29E5C32C54A947AB455,
	ClipperRegistry_get_instance_m2463BECEF27C0014F7A741C34F21022C400B1FE8,
	ClipperRegistry_Cull_mA8642E827D39EF74E76C30E2DDFBB22332E77D2B,
	ClipperRegistry_Register_m6C2DD936413F7AF54EBCB0B5E05B89E8F0345370,
	ClipperRegistry_Unregister_m215BC68BFB16A39E496DC9D18343C9B42A40705B,
	ClipperRegistry_Disable_mD528316650DAF2622B044E960F93201230524F5D,
	Clipping_FindCullAndClipWorldRect_mCD9700613DC315DE81B430153D35A8A5439F453C,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	RectangularVertexClipper_GetCanvasRect_m57C6607045EDB62BEA830718C7731988681A415C,
	RectangularVertexClipper__ctor_m357C226F7734C4E4B4939BFD94382A10E5348541,
	DefaultControls_get_factory_m650E164FAFD5C88F5D265C579E91A6D5AB0C37FD,
	DefaultControls_CreateUIElementRoot_m263036E6FBF57322DD1CE45CB2B1500AA687C04A,
	DefaultControls_CreateUIObject_mD2182752F7F59067B563D01717CDEC050EBED6AE,
	DefaultControls_SetDefaultTextValues_m4AC6C3A0CD8763AD0DD3CA38FEB54D9057667664,
	DefaultControls_SetDefaultColorTransitionValues_m4A83B274EC1B37EEDE3D3A4873E918950E5CC89E,
	DefaultControls_SetParentAndAlign_m39F7E5C514E5F413148A64D79541568258E1D648,
	DefaultControls_SetLayerRecursively_m0ACD54B770C4BD8AFF5FCCE3B7683BD578D85EAC,
	DefaultControls_CreatePanel_m7BC6386B7779EAB89AED42C9034E3B6E6E525B63,
	DefaultControls_CreateButton_mD0D3662039D6A691FFEAF698A82D306A1A39AAB0,
	DefaultControls_CreateText_mE31FF96169C99E5F35C3771503D88D25E71607C4,
	DefaultControls_CreateImage_mA04F918B1FD3F336028770981B203466A5D4BC72,
	DefaultControls_CreateRawImage_mC824917CB780430B67442B223FC3B478F396C848,
	DefaultControls_CreateSlider_m6C83A0D5C3E4C7872442073E253E9F9035E008F0,
	DefaultControls_CreateScrollbar_m2C457F088FFC2ABCE11C213045D8A21EC26D7CA2,
	DefaultControls_CreateToggle_mDAFB086187684752D784F681CD5992CAE084F925,
	DefaultControls_CreateInputField_mEDCE8ECC28DE5430DA0F24D9B947E0D0B53A4FC3,
	DefaultControls_CreateDropdown_mDCBE27065F24B1F37AA993C559F764B57E668616,
	DefaultControls_CreateScrollView_mDF4D794DE8DE1994721455D5CC9BAAB9413D9A48,
	DefaultControls__cctor_mA82CEB5C54B5784FB65E15BC97FF938F68826252,
	NULL,
	DefaultRuntimeFactory_CreateGameObject_mC8B02B41466EBE09F2EF2501E6A060AA1796EF15,
	DefaultRuntimeFactory__ctor_m491525093C771A05048F78F1B5936D8B8F914F25,
	DefaultRuntimeFactory__cctor_m91FE60CB8690DB9AE103930E4DAB2F4D9993EF7C,
	Dropdown_get_template_mFDEACFB41DEB580848BEA60BFE844EA029DD407D,
	Dropdown_set_template_m3E706A431125DCD78183DB9DAA68C164AF90D430,
	Dropdown_get_captionText_mB90FCAF70EDDAD19CFD8EBCDF2EB88E4D2C55376,
	Dropdown_set_captionText_m4688634DB4D91214DD1BA38C57684F79E9F80B84,
	Dropdown_get_captionImage_mC31FFCF352F035CB9FC198A35D600E77ECCEE885,
	Dropdown_set_captionImage_m18F032892158167D1CD739F63999FA000CF63F68,
	Dropdown_get_itemText_m89586E53171746D3BD16F8EBA0C5A814006435C1,
	Dropdown_set_itemText_mEDA356F0B628755CE5584C0870993AB84C2EEBF7,
	Dropdown_get_itemImage_m0189F9EF2CD249331922CFCEBEC6A0594E78CE80,
	Dropdown_set_itemImage_m1E0E1615193CFD73E5540DC9FB1B10F432955D02,
	Dropdown_get_options_mF427A2157CDD901C12F1B160C4D1F8207D7111D0,
	Dropdown_set_options_m64FE183D8C988AC643870012A98F3095E2C2C14B,
	Dropdown_get_onValueChanged_m05479714AEB528CF5CE93ED09870E98E6EC94CFA,
	Dropdown_set_onValueChanged_mC153B430EEF7453F46811C081DEB648A90EC6CB5,
	Dropdown_get_alphaFadeSpeed_mB780F0BD78DE41F2EE162FB7D565CCB376E963F9,
	Dropdown_set_alphaFadeSpeed_m3AE1F3364445C8C77CE60D295CCDA95A2E99A0AD,
	Dropdown_get_value_mFBF47E0C72050C5CB96B8B6D33F41BA2D1368F26,
	Dropdown_set_value_mE4418D205D53F8A3AD23B957D1A8CD71489CB3B9,
	Dropdown_SetValueWithoutNotify_mC81063284C78D7458A0F33B8AAF9FB506B1585E9,
	Dropdown_Set_mC71A8A900EFA989FD5C216B2B4A91E3567DC0B6A,
	Dropdown__ctor_mF86953AE89E7391E626E8D3F4E1590DC51B0FB15,
	Dropdown_Awake_mBEBE3A27F3379095CF5F296C1E8AD391EA6EE687,
	Dropdown_Start_mFD1169E003C18BB5F93D17E6CD06BD4684FA21D4,
	Dropdown_OnDisable_mE8D6EB5D3A4FE63A1D3D2578441BD9676B216B2C,
	Dropdown_RefreshShownValue_m1D58FF9265C31D85B65F52410EA8A8234F974FBA,
	Dropdown_AddOptions_m9DEF8F740A7A1E8F5582849C2043967BFEFC834D,
	Dropdown_AddOptions_m2375327887D29ACE452F157863186FC8D0AA4AF9,
	Dropdown_AddOptions_mD622304344FA058D8CC1DD6B6C0DED4E933A9641,
	Dropdown_ClearOptions_m7F59A8B054698715921D2B0E37EB1808BE53C23C,
	Dropdown_SetupTemplate_m15EFE26E6D244C98E9A8AB5C3BB923A49DEB4EBD,
	NULL,
	Dropdown_OnPointerClick_m69E4562059144BC2D414B5E984E7FB0A91801512,
	Dropdown_OnSubmit_m5A0C1CDE9E75E21E60E0AB5AAE23647F6390B4C2,
	Dropdown_OnCancel_mEBEDBE7AC4AC120520B5BC138B67D21484BDF3C2,
	Dropdown_Show_m83EB3F87E6A7F0E02EF65F2763C4677FD546FCFB,
	Dropdown_CreateBlocker_mF6872BF2D24F5E4DF51F2F2B72AE208F98967A7B,
	Dropdown_DestroyBlocker_mA6E237A7B69FD7F952B5D959C01E163A44C17F2D,
	Dropdown_CreateDropdownList_m8126CEBE20918EC1EC807E0C2D1F2D1DD3265C18,
	Dropdown_DestroyDropdownList_m749F3F9AF5D410E4E8EED3EBA10D5FA5CD4C1FA0,
	Dropdown_CreateItem_m31638231195DD1DF15B8FB320AEB6782222764BC,
	Dropdown_DestroyItem_mDD3CF9E02F179A2A29DBC3010DEC110CE4DC7270,
	Dropdown_AddItem_m9708CFF7E67F17CCDD0071C68F97978863D81467,
	Dropdown_AlphaFadeList_m88C9E2306FC0DEA8C6737C561512A535CF7255AB,
	Dropdown_AlphaFadeList_m8EC421B16EA2A7D606AF6B0005F7F6DF5A75C5A0,
	Dropdown_SetAlpha_mA297B2AAE01EB7055193D514AF149C2AB21CEE36,
	Dropdown_Hide_m730F238F76DFA575F75C31AFADA880004B324544,
	Dropdown_DelayedDestroyDropdownList_m78CF275EA12EAB4537161863C7E2F12000A45387,
	Dropdown_ImmediateDestroyDropdownList_mA6162FD9DB206E8593ED2878AB2D3B8C95DA760E,
	Dropdown_OnSelectItem_m51485B5AF5732C5C7A63A7C0984267D00534E31C,
	Dropdown__cctor_m19D2E5F48D1BB0F07E7E5B328B1D4AF199E99118,
	DropdownItem_get_text_mFFE89EDD35FAA758C0793DC0743D2C7265150904,
	DropdownItem_set_text_mC11250A9655C633527F6D09FD3774BE37740B8D6,
	DropdownItem_get_image_m2CCA7CA013F2EBB8C75827D616370928023827D2,
	DropdownItem_set_image_mFE9F20CF013BAFC91ACE79C9FD199282D3038CE8,
	DropdownItem_get_rectTransform_m848D2741413CF956597F825EDCAA547655EAB7E4,
	DropdownItem_set_rectTransform_mFE5A54410202CA0E151BEECFAE71CC9D4B81E50F,
	DropdownItem_get_toggle_m696C6516BE86A6014F90D07B549868A999E2B247,
	DropdownItem_set_toggle_mACFADA4ED419E2959BE14A979DDF7195562A441A,
	DropdownItem_OnPointerEnter_m27B152E33C585BB46FA85CA0B155290CF7465618,
	DropdownItem_OnCancel_m2A6C5ACB0FDD94E53B364C56F39A70A891C9CB8F,
	DropdownItem__ctor_mC4429F24D5D1E49FA86D33B61DADC33464728B97,
	OptionData_get_text_m8652FE3866405C4C7C3782659009EF2C7E54D232,
	OptionData_set_text_m23C74889CF93559CD64F90EC8DA69C20C13FC549,
	OptionData_get_image_m3A9639CF8946C3DF2D8A2364B62E69D52FD6C1BC,
	OptionData_set_image_m575DC2D9B5CF0727CBEB9F32B51B9B1E219C5A0C,
	OptionData__ctor_mA1D3FE8359A7237C62D802A3E94221D451364056,
	OptionData__ctor_m5AF14BD8BBF6118AC51A7A9A38AE3AB2DE3C2675,
	OptionData__ctor_m8C980A8F61978E1BD5091A7476453792CD07FDF0,
	OptionData__ctor_mD0A7C0F3F57C0259BF4307389CE24E2B33C7FD8B,
	OptionDataList_get_options_mDC1404A7BE88BEF7957F1E5C2D66EEA7F0B1B712,
	OptionDataList_set_options_mC0550A0E7A192C60A93B5A6DF56D86BDC6609A8E,
	OptionDataList__ctor_mAEA703E51D910C8FA6BE11A012B9C9F932E187C7,
	DropdownEvent__ctor_mE2A2ECC494E83733FD196E30F74CB19B05B940B9,
	U3CU3Ec__DisplayClass63_0__ctor_mE228A7EF3A8402C0C54E10741528BA4C12FA19CD,
	U3CU3Ec__DisplayClass63_0_U3CShowU3Eb__0_m42E8F0816930DEB826D2091D0E5429947623F0FB,
	U3CDelayedDestroyDropdownListU3Ed__75__ctor_m47D084871CB3AF3216C6DC8D342881D7460E14C4,
	U3CDelayedDestroyDropdownListU3Ed__75_System_IDisposable_Dispose_m9746BE4CFDE332E7BB2B42BB56F16E4383F759EF,
	U3CDelayedDestroyDropdownListU3Ed__75_MoveNext_m2166A5CAE1107928834C346B4D52ACB983AC8845,
	U3CDelayedDestroyDropdownListU3Ed__75_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m5EA4D91EB06B4E048941BBC0AD8D92B7829B4D39,
	U3CDelayedDestroyDropdownListU3Ed__75_System_Collections_IEnumerator_Reset_mE42D3D74A267F697DA8EB36F64E317621923C98D,
	U3CDelayedDestroyDropdownListU3Ed__75_System_Collections_IEnumerator_get_Current_m5ED616A0AA9D4E548F5B8F39FAADB1F016155CBF,
	FontData_get_defaultFontData_m2690B96BA777BC52F286840D37C3C74371E60AC1,
	FontData_get_font_mF59D5C9E97B46D8F298E83AD5A91B59740ACB8AF,
	FontData_set_font_m026F16527DCD0CD4F25361B4DED1756553D0FAE8,
	FontData_get_fontSize_mE13F5F1B45827C6011C8A31B05E618B60832331B,
	FontData_set_fontSize_mE9B82951CCF0D998F6F115E6C9D8E5E907781D76,
	FontData_get_fontStyle_mBDCA14034A03D890A46B8BC82CFDE821352D1CB1,
	FontData_set_fontStyle_m7E34F839351D0096FA9B81CE87E5A22B995765D1,
	FontData_get_bestFit_mF1603689DD76EEBD462794B6F16E571AA84642DE,
	FontData_set_bestFit_m88B35F336FB48E710623DE8DCBF4809F257A76E4,
	FontData_get_minSize_m5EF405821A9665106B19F0B1C72ECD0FE27DE727,
	FontData_set_minSize_m882073EF72432C453CF5EE554F1C40EB369B1267,
	FontData_get_maxSize_m53ECFA4C6AD93DD56EA3D42414EF29BC83882A56,
	FontData_set_maxSize_m19265E3D2E977671F9AA2F5FA6B67893FC8B6D4D,
	FontData_get_alignment_m432230C0F14D50D39C51713158D703898B7B37A5,
	FontData_set_alignment_m37A3B04BD3E107BA0ED5790C113325979BE96B80,
	FontData_get_alignByGeometry_m0445778A81F8A695935D1DD8AF02E11CB054B753,
	FontData_set_alignByGeometry_m37B399E7776DD78B91DD17BA99521012A0AA9DB3,
	FontData_get_richText_mA3A81900C3BA0C464AD07736326CF5E01D1DE6A5,
	FontData_set_richText_mD08E389ADCE118C9B2043555896565070F4A61B3,
	FontData_get_horizontalOverflow_m4753C85F6030408730D122DA0EAD7266903A9958,
	FontData_set_horizontalOverflow_mFE27939FF5E996F996B9FFA277243D2F50566E03,
	FontData_get_verticalOverflow_m2F782F21A1721A387126B5968DD8C5616C8EA2BD,
	FontData_set_verticalOverflow_m1EBDF75A9D5F98CB815612FA35249CE177DC1E5C,
	FontData_get_lineSpacing_m5868C02CEDB7C34057BB5AE97ACE7721BD3B5110,
	FontData_set_lineSpacing_mEBE69BC6FF339D085BE81D829861627240F64EDD,
	FontData_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m332904D0F17795FFFBE6419EF65B67B92AB845F5,
	FontData_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m2769988B5DDE40E09030DCD62422E10336755A2E,
	FontData__ctor_m0DDBDF050D9690404B848157702BC3A52146B9FE,
	FontUpdateTracker_TrackText_m628C9A1120A76C180C11E3BBEDE99CEFF2953DCB,
	FontUpdateTracker_RebuildForFont_m053A36FD90A110F0E819E3B283ECA94256A3CC77,
	FontUpdateTracker_UntrackText_m73CFCABDCFB909E87B252ABA24D7A46C6AFF52C5,
	FontUpdateTracker__cctor_mD67474C1A4E60CE3C68663C81DD2CE57861E923C,
	Graphic_get_defaultGraphicMaterial_m11CD8ED7625283F7EF14EEC63D5CC08CEA3BE37E,
	Graphic_get_color_m922C082FFA75F2B2A893EF625589B1BE398DA9A8,
	Graphic_set_color_m69635A441D681D3D665214F4B856018FE9B287FC,
	Graphic_get_raycastTarget_m82EFC64ED0CCB4D377B5BF7B267BAFB82A93101C,
	Graphic_set_raycastTarget_m6CEEC04CD0ED1231CF359AAA8EBDC0D714119ECF,
	Graphic_get_raycastPadding_m4D6F2F7733620B7683F73995A9E9ADD042AB94E1,
	Graphic_set_raycastPadding_m8F7E6E55649D6EA562839909E4A2D9685E897E9F,
	Graphic_get_useLegacyMeshGeneration_mC0B873372429B274A516B1E2C002F76C7504018E,
	Graphic_set_useLegacyMeshGeneration_m115AE8DE204ADAC46F457D2E973B29FC122623DD,
	Graphic__ctor_m41CDFE33452C8382425A864410FB01D516C55D8F,
	Graphic_SetAllDirty_m026845332A5ABD19A89BCD2AFE311274937D89D4,
	Graphic_SetLayoutDirty_m21F1B38F8271F887CAE7B7966AAC7487B7C2EEBD,
	Graphic_SetVerticesDirty_m544965E052ADD5A8517BC69B0A04BC9324D6E19F,
	Graphic_SetMaterialDirty_m45496137F4A0BE6B86C8C23E4A849DA96DF17CFD,
	Graphic_SetRaycastDirty_mCAA09EEF8484643AE160246C682EE61C7BDF912A,
	Graphic_OnRectTransformDimensionsChange_m582C9953019AEAD41756B6218D16A132F78F18DD,
	Graphic_OnBeforeTransformParentChanged_m2BA694129A195D64D4AB4E79B3375964725142C2,
	Graphic_OnTransformParentChanged_m4E7241AAF724D464E6FB2159E3A5940AD3C938FD,
	Graphic_get_depth_m8AF43A1523D90A3A42A812835D516940E320CD17,
	Graphic_get_rectTransform_m87D5A808474C6B71649CBB153DEBF5F268189EFF,
	Graphic_get_canvas_mDB17EC66AF3FD40E8D368FC11C8F07319BB9D1B0,
	Graphic_CacheCanvas_mE48DF7983F345B68B89E27630DEB22C03B370249,
	Graphic_get_canvasRenderer_m33EC3A53310593E87C540654486C7A73A66FCF4A,
	Graphic_get_defaultMaterial_mAEF1BC02CA3DAB0494CF1DB58BA5EC44023196F6,
	Graphic_get_material_m58E72EBA935CBA9F9669B3A86B11DDB59BE7EF64,
	Graphic_set_material_m3D93A3A7E51D661643E9467E3C943CEB905CF125,
	Graphic_get_materialForRendering_mEFA79FBE242FDF474868D2F202E36B36AD7907A9,
	Graphic_get_mainTexture_m92495D19AF1E318C85255FCD82605A6FDD0C6E56,
	Graphic_OnEnable_mED9484501E8C3DFAB17F390568AE65C42D682F30,
	Graphic_OnDisable_m8E7D00E9C361D62A0A596B55CB1633E14C07A2B0,
	Graphic_OnDestroy_m21BA6F3D54572B9EBE18FC1FE685DA944F610849,
	Graphic_OnCanvasHierarchyChanged_mF23B9CAAE227B9322E32D41FBF753DEF9D1BBDB2,
	Graphic_OnCullingChanged_m50EBEDDA655F828722AB0C6EC0CC27BC84BC4EA3,
	Graphic_Rebuild_m581524D9BA8EB6BE80539919811F2A062017A0B7,
	Graphic_LayoutComplete_mA591050412BD81D7F9EF3D265B978F0CCFA33E0E,
	Graphic_GraphicUpdateComplete_m563B5685218C32201DF82180B653F09EC3BE669F,
	Graphic_UpdateMaterial_m5A5D02BFE3677C9EC1A30054B6CC486BEE3F45A9,
	Graphic_UpdateGeometry_m28D710BB5ABA1340DB4350B6CBC65DC687655EC5,
	Graphic_DoMeshGeneration_m753A422F3F1892128C98D9F73001DD22F108C06E,
	Graphic_DoLegacyMeshGeneration_mB0749616E597835253857FC89E4DD92F575BFD5A,
	Graphic_get_workerMesh_m5AEF73D150916905A623B1D4A64E85719805C56B,
	Graphic_OnFillVBO_mE640EA379BCBC021D296597D2131372D7CF29E38,
	Graphic_OnPopulateMesh_m7B2611B869E2E60F1E083D716026E7182DC48BF2,
	Graphic_OnPopulateMesh_m60171AAA2F2AF25E9304C8334FCE766711828DA5,
	Graphic_OnDidApplyAnimationProperties_m4D06551930C4D0D091C2D2A4499FCA2322362A1D,
	Graphic_SetNativeSize_m5A2E1E6D5AF7BB2B4490E6DBEC8CC99855400415,
	Graphic_Raycast_m203BB6CA95719A31F4462A98F61AA97C24EA40FD,
	Graphic_PixelAdjustPoint_m97EB91CCF7ED5D9892043E53DC0574FED3EF89AA,
	Graphic_GetPixelAdjustedRect_m97D803029E437D6E20057C7FBAF420532184D16C,
	Graphic_CrossFadeColor_mB6115D791ABCE6CF1ADC94158D7AA135E006A566,
	Graphic_CrossFadeColor_m01C2BA7648677A835C6E1D7234E7028206B0AB63,
	Graphic_CreateColorFromAlpha_m19B5529929135A5CF1687A9671E3958448C1B5D2,
	Graphic_CrossFadeAlpha_m15C6267D463C1531AC1E1F6EBE489FC6A41C3404,
	Graphic_RegisterDirtyLayoutCallback_m6F0C7B8E9712332EAC21ED730F2B8DA2DEF42165,
	Graphic_UnregisterDirtyLayoutCallback_m948B7781200B4F0AF75EB73B132F97C21CFED15A,
	Graphic_RegisterDirtyVerticesCallback_m445D5D558197F444F3D8EBED25DFB75901BF314D,
	Graphic_UnregisterDirtyVerticesCallback_mCEFBFF280C6F432096A373512DF62EAF9469E8C7,
	Graphic_RegisterDirtyMaterialCallback_m11C1085CED157B875005B6BAACD428664810AF8E,
	Graphic_UnregisterDirtyMaterialCallback_m993432BB86AB6CEB0260DB7552A1044C5065A475,
	Graphic__cctor_mF4E4372347905D073802DC52ADDD76D646A9EF6F,
	Graphic_UnityEngine_UI_ICanvasElement_get_transform_mC41FB4648C476A193FBC1F7E1847E051C642B402,
	GraphicRaycaster_get_sortOrderPriority_mE193C6656CB9FB78AEFE5FA4FCFA7E3FEBE893F6,
	GraphicRaycaster_get_renderOrderPriority_mF735C4553ECA17B8428200423AC4B17F0B89DF6A,
	GraphicRaycaster_get_ignoreReversedGraphics_m404FCBC6EC70E5193E56A1DB77605AF448EF4235,
	GraphicRaycaster_set_ignoreReversedGraphics_m3C6936127ECCDC00F192D18F3D5ED64B21E18897,
	GraphicRaycaster_get_blockingObjects_m89AF984ED21882BF086AC9DB069B8AB6D1B7636C,
	GraphicRaycaster_set_blockingObjects_m50F4BC7E0506311637A9108F7976BF2E3C2F743F,
	GraphicRaycaster_get_blockingMask_m220545FB5151EE500B7155D17226E37D9349BE12,
	GraphicRaycaster_set_blockingMask_mB328FB4F26453B7F72F4465FAD6E9C9ABD0BAE3E,
	GraphicRaycaster__ctor_mD77E88D530ECCB4CB237078BC57E5E541B87987F,
	GraphicRaycaster_get_canvas_m3EFC279CCA66AE0F492EF985F91EA43A587C6BF2,
	GraphicRaycaster_Raycast_m8EEDA350767E551AE3747D5B1AD420ADC02AA8F1,
	GraphicRaycaster_get_eventCamera_m955394C9A656E4D381195504005692BE15335BA7,
	GraphicRaycaster_Raycast_mA94B1F53ABEFCBBB136D90F2890704316624BDF3,
	GraphicRaycaster__cctor_m8B071B710A8344527D441A06E04957777C47E01D,
	U3CU3Ec__cctor_m3491A1CEC139E0F077D119BBFB4B223E0FF66206,
	U3CU3Ec__ctor_mD924EE81C8A13ED0718AF48F253124F294ABB2DB,
	U3CU3Ec_U3CRaycastU3Eb__27_0_mE359E2E78D7A20EDFC2AE14298F21CCD3245506E,
	GraphicRegistry__ctor_m5BC68A12EAC3B9A5AFF3FF57CF2C876C45B1C111,
	GraphicRegistry_get_instance_mCFC645966A874560156295A13E74A2D148C2FA62,
	GraphicRegistry_RegisterGraphicForCanvas_m9A8778558EC3015B04A4EEEFC7E643608E266497,
	GraphicRegistry_RegisterRaycastGraphicForCanvas_m15EC248B93C2BBB65F18B290535561683142AAC9,
	GraphicRegistry_UnregisterGraphicForCanvas_mA6C356F8DAE43E589A4533528F4D68900D4B5ED8,
	GraphicRegistry_UnregisterRaycastGraphicForCanvas_mB1F0F7263AEF9C47F640E40F7DD246AF8FB82651,
	GraphicRegistry_DisableGraphicForCanvas_mD22A5FE9364E7A692C85FF48C0F928A00BD11E70,
	GraphicRegistry_DisableRaycastGraphicForCanvas_mE334F07F6E26C51A71B8E1367804D4FACB3EAA6C,
	GraphicRegistry_GetGraphicsForCanvas_m6977E4571F576E30523E77D079169DEE3870AB94,
	GraphicRegistry_GetRaycastableGraphicsForCanvas_m68B2F2A1FB4AC54EBF7AECE253BF83F0815A09F9,
	GraphicRegistry__cctor_m4FCE9C4CCDDE1AC16F90AE8682FC5D1FB3BA917D,
	NULL,
	NULL,
	NULL,
	NULL,
	Image_get_sprite_mA6FB016B4E3FE5EFFAE4B3AEE2D2DF89C61E0AF3,
	Image_set_sprite_m55C50F18ABA0A98E926FC777F7D07FA18A7D04E4,
	Image_DisableSpriteOptimizations_mF314F3B31973284E92A8ED1508FED8BE38DEE80C,
	Image_get_overrideSprite_m8F1963AF2ED4861839C0F90F758050DD18CA96E0,
	Image_set_overrideSprite_m8BC8EE1CAD36C5F8C4310DBB2C0A7485106F8D2D,
	Image_get_activeSprite_m0B2FA144D32A1F4EEB6773BC470E4A632DB4497A,
	Image_get_type_m730305AA6DAA0AF5C57A8AD2C1B8A97E6B0B8229,
	Image_set_type_m2D19AE16A5C44F054CC9F9B6F423D95ACA0C960E,
	Image_get_preserveAspect_m912AF1F90CFCFE32FDAAB18C02DEA67E2DAC7A8B,
	Image_set_preserveAspect_m3D15FD6A69BE656FFC014C2162A0FE8636F478B8,
	Image_get_fillCenter_mD1F9DB6D03C955027D9C59D0A659752741C4AD86,
	Image_set_fillCenter_m6F53797CF5A74CDB9259E68D845EE8C318E856CC,
	Image_get_fillMethod_mA24F59E82D3A9307852DC572753EB24675BC9B1C,
	Image_set_fillMethod_m36C3359A19B0DE2F4B8F0454113D55450960027D,
	Image_get_fillAmount_mA6F275C1167931E2F166EA85058EF181D8008B09,
	Image_set_fillAmount_m1D28CFC9B15A45AB6C561AA42BD8F305605E9E3C,
	Image_get_fillClockwise_m0B214F4423A4B9C66549C28005A181F3197270C3,
	Image_set_fillClockwise_mD2296AD106CD954A5564EE0305D99248821C0CF9,
	Image_get_fillOrigin_m903CD74EBAC5F765ED046264BF66D3BE3A5B67AB,
	Image_set_fillOrigin_mFAA94C29AD228999205725E6183F6A572FDDC37C,
	Image_get_eventAlphaThreshold_m2FE3EFF6A13505702A4FF4CEA5AA238D035FABA4,
	Image_set_eventAlphaThreshold_m8ECE006ABAD57DC04E35E55BA618CE9C88B6DE55,
	Image_get_alphaHitTestMinimumThreshold_m4271487851417EC8581659AA2611705182F2EB1B,
	Image_set_alphaHitTestMinimumThreshold_m95763334366C9A33E68C0BBC6695D62E0DFE462B,
	Image_get_useSpriteMesh_mA2A1DEB7AC296C364B911D72B4A25828963BD8BE,
	Image_set_useSpriteMesh_m3BFF46CFA7C14E4646BC2E50D041C704B2A6B549,
	Image__ctor_m11AB7390F778F2BA5F3B110654400201123F1924,
	Image_get_defaultETC1GraphicMaterial_mFC59846AF686F1BC371F1722D16991F1F50A91AE,
	Image_get_mainTexture_m7650B5327838CEE79723AAE344CC05041E660B9F,
	Image_get_hasBorder_m444A40E69275ED3748079EBF6D6FD489FD17CA51,
	Image_get_pixelsPerUnitMultiplier_m1F9B9896852C675664DE06FB74A1C5105626A41D,
	Image_set_pixelsPerUnitMultiplier_m5F08D766BE960A22D846506E173B2E11D00A1E64,
	Image_get_pixelsPerUnit_m98A6403E0798B752A00DEF044DDF6B199B35593F,
	Image_get_multipliedPixelsPerUnit_m1C97AC7EFDE809F8A763CE7C4064678BB1E14270,
	Image_get_material_m64BB551A86DA68E6BB4824D80B87D1222B2A719A,
	Image_set_material_m666B9F6F4224CC1ACC26179CD32E491C6B2258A8,
	Image_OnBeforeSerialize_mC5AC9EFBACF39D25F135CDA97DBB233E9486936D,
	Image_OnAfterDeserialize_mAFE846C60D003A128F7945A8EE0887C677DD6CFC,
	Image_PreserveSpriteAspectRatio_mEA1EA46C681D08468D3EB86E7E0185E89FDEC667,
	Image_GetDrawingDimensions_mC40C4D11A97A7EEB627C9F1369D296199CAD5CD4,
	Image_SetNativeSize_m3431DB7456FD40DD2CBEDF3FD82EBF55B46E7D8E,
	Image_OnPopulateMesh_mA43AC802D098FB93CC941275F90AEBFCC81F4EB5,
	Image_TrackSprite_m807EE9E74775D2F4AF1D54B52F2A230C9B5C1143,
	Image_OnEnable_mF77EA3DC594DAE50115D24C3CA09C3E120F32A4F,
	Image_OnDisable_m9A63211DBBB352D9FEDB27B12E129FE42F86FF12,
	Image_UpdateMaterial_m6B56C4E5F44712288A92D4A0CED493C2803E218F,
	Image_OnCanvasHierarchyChanged_m8ACAB18B22A3B6D65F3D580886C5AA3C86C79687,
	Image_GenerateSimpleSprite_m76CEEC3CC99171B4BA9E26DC2E46B0BED05FD355,
	Image_GenerateSprite_mABC4D004A77A4203E4271EC29EC12A8E22A65858,
	Image_GenerateSlicedSprite_mA22D83C3A325339734B90ADA61204BC1F4ABE117,
	Image_GenerateTiledSprite_mD85AC44D6C08B374EF7EA03A4BF7943CC830DE37,
	Image_AddQuad_m0AE23278BA55C31B5716D4D9E86D869F1E4C6803,
	Image_AddQuad_m99E948C86E6BC6A57C6260FCF34A4D4ABB7E521C,
	Image_GetAdjustedBorders_mEE19FD1E4AD9939B9565C4A0B3937433F6A73596,
	Image_GenerateFilledSprite_mB32C81541BE95D5144A522E1487497A4139883BF,
	Image_RadialCut_m7A4BB3C664FF6469FC7579B2CCC300516C88C229,
	Image_RadialCut_m1785A0403C98D4E8FED402DC24A4370875D5848E,
	Image_CalculateLayoutInputHorizontal_m04FE856B2079DAA841634A767CE764DE5AFC7C94,
	Image_CalculateLayoutInputVertical_mFB4812D7E95B71CDA2A78981FA8E57C952AE16CA,
	Image_get_minWidth_m80F0CCAD55244FB5B0EDBE47A969AE727404A711,
	Image_get_preferredWidth_m632101B442589E11959948982B13E2A237645F4E,
	Image_get_flexibleWidth_m0334DEE5F5BB3D87FA70375C3C9F7C839DDE7AA4,
	Image_get_minHeight_m41442CB39DC88BF08A33408C65C3392D2752709A,
	Image_get_preferredHeight_mE2A282AF37B5669BA18B7FE022C50F6603E7946F,
	Image_get_flexibleHeight_m49E73811DD8518AC06CB59E240E6FA09A1FABFEA,
	Image_get_layoutPriority_m62067AC13C013976E5BB8E752A2896201A2BC8B6,
	Image_IsRaycastLocationValid_m216026118F2C011AA5026CB849F28C669D449CF6,
	Image_MapCoordinate_m4539737D09F1D8FD0296E59E8AB2A4C906D6E096,
	Image_RebuildImage_m19F129C68BAD55C32AB963738669D81E29C81352,
	Image_TrackImage_m18837F3C0DD115BD62F9DCC532626FC8DE3988F6,
	Image_UnTrackImage_mE1307DDE71EF1116A030342E664C38661F46C461,
	Image_OnDidApplyAnimationProperties_mAD738CA9AA44E112C09FDCA2CA4DD5B27CF4CC17,
	Image__cctor_m0C262032BB7F9AFFCC95D8D30C7625C4DB44B785,
	InputField_get_input_m2BF279A6233AFFC48AC5744CA27ECC7AFBC45320,
	InputField_get_compositionString_mE86C18CEB54EF0C00415F1B1E04586046BCDBB36,
	InputField__ctor_mC18C13BA47FF7230CD94BCC3E829E522E800CA3B,
	InputField_get_mesh_mC8B8AD9D1A849BE210E376C18193A662F8F015FA,
	InputField_get_cachedInputTextGenerator_m791D35AEA97BE7F89C42F5A0513E5408F0E6BF3C,
	InputField_set_shouldHideMobileInput_m682DA851218A025AADCBCC1A208149C8ECD9AC52,
	InputField_get_shouldHideMobileInput_m15A0741882D419292EACEC96DA38B7691F24263F,
	InputField_set_shouldActivateOnSelect_m346C4DAD42D68592BE5ABA56DB0D0EFB08311700,
	InputField_get_shouldActivateOnSelect_mC219F26075E9EF829F9ED0363EAB0C5D261FD82D,
	InputField_get_text_m15D0C784A4A104390610325B02216FC2A6F1077C,
	InputField_set_text_m30EE95E064474DFA2842CA2DD6E831FF3F3500BF,
	InputField_SetTextWithoutNotify_mD200F65D8CBD6B825CCF437FBB30BACD97365893,
	InputField_SetText_mA2D467E75DBB4E11719D893B45DB6EC1092AE956,
	InputField_get_isFocused_m60B873B25A63045E65D55BDC90268C8623D7C418,
	InputField_get_caretBlinkRate_mAA39E62C211CAFE475B1CC9B43E2B5E01D8B861F,
	InputField_set_caretBlinkRate_m3D311EA33F9D274CC09B83BC09B7B0784BFAFE30,
	InputField_get_caretWidth_m70D0EBCAD43971C20B8FD4E031A86587CD75ED83,
	InputField_set_caretWidth_m7F11F6C71B41CBC9A98E078B880D093F0901F25B,
	InputField_get_textComponent_mF2F6C6AB96152BA577A1364A663906315AD01D4F,
	InputField_set_textComponent_m4F14433F7DCF4FD7C427177BF1EAE71719A3CE7F,
	InputField_get_placeholder_mFA1B993866F8CD89D76AA238D8CFF2D48AC3970B,
	InputField_set_placeholder_m55B3E498EFFC547D3B4AEFFE51934D49641D97D3,
	InputField_get_caretColor_m766D1DB9AF41796F23E0A0DD8C565EE50829731C,
	InputField_set_caretColor_mD8C38138581CCC8ED15030569E4B96E65BDBDDE4,
	InputField_get_customCaretColor_mBF58C9552A845540FE36960BC3F29E3BF7DF7D15,
	InputField_set_customCaretColor_mAF774E66BB80B5E42586B1F2AB74765A0796FC29,
	InputField_get_selectionColor_m205696954151CB58BB8DF822563B39E78EE9F7BB,
	InputField_set_selectionColor_m7F0CA63B660916CCB1AC6C00F3772771497C3636,
	InputField_get_onEndEdit_m0C979736255DF520629D269CCDD6452EF4F52EB5,
	InputField_set_onEndEdit_m4123895F70108223E6A0BDC9FE72FD650E7D5F18,
	InputField_get_onValueChange_m140D8CE21B587746CB1AD299B6B0C8DA6173D60F,
	InputField_set_onValueChange_m060DFA02771168B0C5B1877D9483F60D4D8AB5E0,
	InputField_get_onValueChanged_mAF7D77CCBED34CF4A46A9E4EA77405D790C58449,
	InputField_set_onValueChanged_m5F3D099C06E6C12A1394D3F915533C83BA9A00DD,
	InputField_get_onValidateInput_mCBE2BB6D465B095BDA7F75091ED88BAFCC7EC9EE,
	InputField_set_onValidateInput_mDADA0C659939DF17C43501F6D09C7B12243FFB5C,
	InputField_get_characterLimit_mE37C423F3248D89801FD33A223878062DE951C51,
	InputField_set_characterLimit_m3CCF4D3F14B341DB38E5CDDB2BDF9F550A559613,
	InputField_get_contentType_m3717B7CB4EB125E353B4B7F48DFDE21F195AE9E9,
	InputField_set_contentType_mEB66E08EF42632A15C4A5A30BD0A19E20560C7B1,
	InputField_get_lineType_m02C63C6C52D3FDE31AA67AB9C77B315B7AEA231B,
	InputField_set_lineType_mE9D5E11A1A7F16648E082590395D21742CA53E5B,
	InputField_get_inputType_mB69EBB88308E8797FF13A77E5540BACE1277F065,
	InputField_set_inputType_mAA484C54A6D3A6DCBA6B12CC939186928BE031A4,
	InputField_get_touchScreenKeyboard_m99C235BD411F5970741599B1A704A5089B112086,
	InputField_get_keyboardType_mB9BE8A9EDAD0C10413436CC933EE187CC4A3CBEE,
	InputField_set_keyboardType_m4D7479C6DF0C3AEC74A1309D4A3528D7EE972F21,
	InputField_get_characterValidation_m9446E5CDD7DF9B41799344119032DAB48DDE9CFC,
	InputField_set_characterValidation_m3049D3C8558B473E505A8A0A6E41270202A07AE4,
	InputField_get_readOnly_m7E74B9F7F030D889835F94922325C9AFB3388CA0,
	InputField_set_readOnly_mFE554E845DEE5D76223D618E3E553169F7EF8AB2,
	InputField_get_multiLine_mA9BE5B7BFEE95E9764958FB83F61D1E69B2EA8B2,
	InputField_get_asteriskChar_m51B289120D5EF43D619DAF0C287938A629399E44,
	InputField_set_asteriskChar_m526E2E04223E307C4EB083E3377E907FEE9BC2A9,
	InputField_get_wasCanceled_mF527091ED7C92731E83F921F7ADDAF2D5E77086E,
	InputField_ClampPos_m4DA5D04357FF57128B5A03EBBD015AFCFA1F1A6A,
	InputField_get_caretPositionInternal_m9208E7E6A4ED51AB8E47C9E35538A431806BC488,
	InputField_set_caretPositionInternal_mB399A8BC983C2010A5CB6FD0BE19ECDB7CA49104,
	InputField_get_caretSelectPositionInternal_mE282EC08EADCAEC8741AE308178F4C82813B2E3D,
	InputField_set_caretSelectPositionInternal_mFF674F8B5A0BA7697E4FDA13E917BC5ECB83E137,
	InputField_get_hasSelection_m2CF3B8E665092331229BE635B40A6A32AEB47E92,
	InputField_get_caretPosition_mEC68FA5C979CA4282A59511BCE0701569AA69C7B,
	InputField_set_caretPosition_m7B6934DD91905D0DBF721EB09B1B7960ABA5BCA9,
	InputField_get_selectionAnchorPosition_mD6A77D08DB66FA68241D052D7D98570DBF3C6995,
	InputField_set_selectionAnchorPosition_mE03A165781A4BE3825A1246B829969F7E144A171,
	InputField_get_selectionFocusPosition_m477373F0852FDC320EC8F76E21948642B9D1B79B,
	InputField_set_selectionFocusPosition_mBA1CA496C3396A287AA866243DFA6F7EFAECF54D,
	InputField_OnEnable_mB72732E0908E821D56B9758D1A9533274A2D9D01,
	InputField_OnDisable_m31BB366D7FC75A02D98163A685DD0F5522084AB3,
	InputField_OnDestroy_m6559B5D023DE3AE9C70A3CAA528047B57D86EB77,
	InputField_CaretBlink_m04B107717E10F3C3D28E5D3C9ED2EC4645F75BE3,
	InputField_SetCaretVisible_m0FA23A6B2D29C01831B929417911C028FF76F787,
	InputField_SetCaretActive_m7F895A414EB388CCB62D99F342D96B4FF816DE48,
	InputField_UpdateCaretMaterial_m4CD8DA75BF5C8041413326AAD8E7CE00EC2D4F30,
	InputField_OnFocus_m12136DA7D04901A0D657FA260D7B6D29B4B777EA,
	InputField_SelectAll_mE0FC6C49370561EC070ADF1CEED5159DC4573176,
	InputField_MoveTextEnd_m72571BFED9CD0DFCA3E28561DE7A81FA435586F1,
	InputField_MoveTextStart_mB2AA4132A375A5DEAC2116D638DCC0C4F30810B7,
	InputField_get_clipboard_mA422380D8FDBD384589651DEC5EC994BEAD8B3F8,
	InputField_set_clipboard_mDB385A8B02A1DA01F8A8525642AD7B90A962E0D2,
	InputField_TouchScreenKeyboardShouldBeUsed_m2B607F8829742049E8D17348C30480DAC10F6272,
	InputField_InPlaceEditing_mEE428E0610B32F56F4707C1DAD9BF2644EEE759D,
	InputField_InPlaceEditingChanged_m4CF273C1B5FCEA9B39397C4248A13B9486F6554B,
	InputField_UpdateCaretFromKeyboard_m664E5EA17EAE912369D41DAA6D000EE43F797A18,
	InputField_LateUpdate_m795829E897B395D1CD675CE422E6EC9ADDDCD3EE,
	InputField_ScreenToLocal_mE3824345E77E14F8F3D96E67BC2AF8D133350DF8,
	InputField_GetUnclampedCharacterLineFromPosition_m0FFAA4A076B7A6A673FF90B7CF32C5BCF811CF09,
	InputField_GetCharacterIndexFromPosition_m4680EC4C185A5B1BF978A6928013CA02F2F4E04B,
	InputField_MayDrag_m646952F57A66908739FF2DB47553AAD0CC803BC6,
	InputField_OnBeginDrag_m496E54FBD1ADF8395DB80C51CC28DEDB5EA18B85,
	InputField_OnDrag_m36347819AF7E4D0CFF3317C1DE45E884FD11029C,
	InputField_MouseDragOutsideRect_mAFFDDB6F3FA3722599CF2CDA7E0EEB57DAA9A257,
	InputField_OnEndDrag_m5B9E0AE98C4B531C002E002EA64D6AB3536D8082,
	InputField_OnPointerDown_m5639510FC7BCE98EFA490A85B198E7D4F18B4DCA,
	InputField_KeyPressed_m0F50321EEE506CDA75CCA02F9747FD65E5DF896C,
	InputField_IsValidChar_m05A915535E3D8DF3B7123F0BAE9BE3365106CE81,
	InputField_ProcessEvent_m5F0E049EFBB30935E1A289C32FE2AF6D6856239D,
	InputField_OnUpdateSelected_mA7E87E9453287D7632A060B8BF4400657EB80092,
	InputField_GetSelectedString_m0BC31CC065E2258323259C646F0D33448914FCB2,
	InputField_FindtNextWordBegin_m8FC97151F226C4CCCEC6B8BA72E47B16278D9D32,
	InputField_MoveRight_m02C718260771AED239B61770F1DB38E7AE266D7A,
	InputField_FindtPrevWordBegin_m143AB3F7AE317D9F62A475A88E20E3A3F62D4A6D,
	InputField_MoveLeft_m0671A9AC1D833070233E3F966F8B00680D1E1FB3,
	InputField_DetermineCharacterLine_m083FE6AA16263F9E407FDFB445DA35FB2DA86915,
	InputField_LineUpCharacterPosition_mA0C6A2867A30798648A9833DB8F4B1C24E93FBFA,
	InputField_LineDownCharacterPosition_mBCC9AA30EC94EA0EF724BDB6C78D2E9952830BFA,
	InputField_MoveDown_m99F896D1A8F784A17BDA364D01D87A2F3CF313E8,
	InputField_MoveDown_m791D171F5C4611A775AF835297E5CB4505FC3E9B,
	InputField_MoveUp_mC87D4402849F1E530735D6D885CEE855C63FC372,
	InputField_MoveUp_mAC099D941C00DF9BE47A1C55D43C9CF7B9CD4304,
	InputField_Delete_mF2790369AD8584F70D359D229B596A909ACDDA97,
	InputField_ForwardSpace_m3063562008D859408F60DA255DE7C2AA324DA0E0,
	InputField_Backspace_m4DF759C80B208BCD552F1570E00FE8771D5C9D0C,
	InputField_Insert_mF32CF6706C7BE2D9E8B9510A17C1FA3936BE50BA,
	InputField_UpdateTouchKeyboardFromEditChanges_m2975A5DA3CEBE635175C45CB13FCAA38DC24BC45,
	InputField_SendOnValueChangedAndUpdateLabel_m830267D28C97DA267F51DDE82E36C446E2AB1A8C,
	InputField_SendOnValueChanged_m6E3270644F5990D04EC0F07E6E0314F9B5085DA4,
	InputField_SendOnSubmit_m61B6B9C63D10D04EB57F6E7EC4305B5E9470F929,
	InputField_Append_m636E533087317BDD9C392E4BCD27C2F735DC87A6,
	InputField_Append_m0405A57105B52A2BBC5CA6B06E804975B3343CE2,
	InputField_UpdateLabel_mF570FC1863A271F7B69C1701711F57BEC7E1633A,
	InputField_IsSelectionVisible_m448CAEA19B6E9048F1FB23EB4092F9362A996960,
	InputField_GetLineStartPosition_mD6F2D28455348713D7674FB7115F03C006FAE126,
	InputField_GetLineEndPosition_mE5634E70FE765F25C7DB1F9D20393B667358CF27,
	InputField_SetDrawRangeToContainCaretPosition_mC1D83691E0439C1629302420F00F390D6FFF72EE,
	InputField_ForceLabelUpdate_mCD4242F19DAA068A57D8A49FB45DBF20578C729C,
	InputField_MarkGeometryAsDirty_mE510B52A8F4814750C7F0FAF012E2735507DD5ED,
	InputField_Rebuild_mF5EA85AF39F59C0C614D91461FA6F580CD429C73,
	InputField_LayoutComplete_mC597409B0AAC5B2801BA7C59073955C58B21CF71,
	InputField_GraphicUpdateComplete_m03176EFF6E8BB4AE382CA905E497D237FAAF1409,
	InputField_UpdateGeometry_mE0A082526B600442DF0BB1AD683BA18253E4BD17,
	InputField_AssignPositioningIfNeeded_mA3B81E438571DD2FF198C6D94D5337410FE4E2DD,
	InputField_OnFillVBO_mF25F719237FB66905CD30D83A171E0C1715D5BBD,
	InputField_GenerateCaret_m87A1C4E932C7BC4873FE30FB7D5D82B4CC1920DB,
	InputField_CreateCursorVerts_m8569AF37F2846E937A8D6250B83BB17A1BE4D533,
	InputField_GenerateHighlight_m024AC6454AAC108CEF5FE3D13DE10796AC402212,
	InputField_Validate_mF6336A3F55070D1DB07C078DF6895EE99FC5A675,
	InputField_ActivateInputField_m97B9F9C150E4463DE299D1F8BC046CCA826064D1,
	InputField_ActivateInputFieldInternal_m04421DBA5D63B5516A25CA01E22F649638EF1BD2,
	InputField_OnSelect_mC01B78E1C2EDAAD2A3573BD6437DD128BBD24E8B,
	InputField_OnPointerClick_m8036C2B156FF0374258973655BD1F58DC2F10B6F,
	InputField_DeactivateInputField_m5560C45B3B91280675019F297463FBCED7A90C6C,
	InputField_OnDeselect_mAFD183BB78EEA3BB585304DA1AC42A671100F97C,
	InputField_OnSubmit_mF999728A0343ACD4DB69B23BB2A349A79EC70B73,
	InputField_EnforceContentType_mC4C55F7A66FC993DB252931E0E15FB9F44880027,
	InputField_EnforceTextHOverflow_m051A4A818627D6D39E78247D7C90F06E993FEC98,
	InputField_SetToCustomIfContentTypeIsNot_m33A8EA45715BE8A35F72C635323CF83F1C6F50DF,
	InputField_SetToCustom_mC31AC959C1A0D93521A79BB1E8B4FC165DF5A8F8,
	InputField_DoStateTransition_m2A4EC0AFC46330002ED61B4DDD3FC18F7436AD99,
	InputField_CalculateLayoutInputHorizontal_m12DE84494E7FDB173D18085440A531882B52B66E,
	InputField_CalculateLayoutInputVertical_m26E12F39F97EA7422A8EE82F137B9EE60C47F3D7,
	InputField_get_minWidth_m81FE96651BA4719B847DE7225830E7EEB3651639,
	InputField_get_preferredWidth_m3898E4B9FD50DCC8E0E81621FC23E4550CEA93EC,
	InputField_get_flexibleWidth_mF5D2ACD28D789C2ACD9EE69FF45EF21A2A91C878,
	InputField_get_minHeight_m8A4D5C88D7CEE000CB00E49A057D09349EBEFC5C,
	InputField_get_preferredHeight_mC4813F206B87CC7C97715341979DA9766CB7D083,
	InputField_get_flexibleHeight_mB510F9D956944F8894C7E487FD9C60B54402A7CD,
	InputField_get_layoutPriority_mB177909CAC82C3B460D4807A24D2277D0B7A8242,
	InputField__cctor_m135EE98A98FE68E18B474E15E09D83291C97173B,
	InputField_UnityEngine_UI_ICanvasElement_get_transform_mE65406876D4DB002FAE227E3772D9FF805964361,
	OnValidateInput__ctor_m79176985D76F3F168B4682FDE46B33C400806149,
	OnValidateInput_Invoke_mFD5B2C2FE9905B863CB61FC6FC6B1D20ED50FDBF,
	OnValidateInput_BeginInvoke_m3B34E2413C3D091F4A38582A8F786705F1CD4FED,
	OnValidateInput_EndInvoke_m12D40F5D1A80B60996DC349AD5B3CC3711DAA517,
	SubmitEvent__ctor_m32C23EA2D0183736A8039A9B638734819D760CE4,
	OnChangeEvent__ctor_mB146DCA915176957A9B5CF48F08FF1EF64E44F5F,
	U3CCaretBlinkU3Ed__165__ctor_mD52F27AA6D0FCEFB97DCD816060515DAD011332C,
	U3CCaretBlinkU3Ed__165_System_IDisposable_Dispose_m286379FC217519BE0837F50C7D73AA060EAE6765,
	U3CCaretBlinkU3Ed__165_MoveNext_mF45CF25F768884A0454D685E5E98DE51A47B5F1A,
	U3CCaretBlinkU3Ed__165_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC74CAEF32DC2E5A2171A03E704E0F5FBF7E6844C,
	U3CCaretBlinkU3Ed__165_System_Collections_IEnumerator_Reset_mAB5C4A532AFA5EBF4932537C737DD7805D41DA31,
	U3CCaretBlinkU3Ed__165_System_Collections_IEnumerator_get_Current_m298756EDEFB19F4E0AB9764AA97D561542FBB931,
	U3CMouseDragOutsideRectU3Ed__187__ctor_m4F54B77E13F77986DE053D7C835ECB3C1AD2EAD9,
	U3CMouseDragOutsideRectU3Ed__187_System_IDisposable_Dispose_m304F008C76437BC5CB56EEE617BD6657F326E532,
	U3CMouseDragOutsideRectU3Ed__187_MoveNext_m5289AF98EFB9C29E6E07398CB22E1706DD900170,
	U3CMouseDragOutsideRectU3Ed__187_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9FCCB5B7180351583F40846D4E09DE98B98FE66B,
	U3CMouseDragOutsideRectU3Ed__187_System_Collections_IEnumerator_Reset_m455B41A0D9F03DD2DC28021D10E73E4AB1FE0F72,
	U3CMouseDragOutsideRectU3Ed__187_System_Collections_IEnumerator_get_Current_mF099543528C346A5D23B19CC6EE2D9CE73D9BBFB,
	AspectRatioFitter_get_aspectMode_m66C318F8E4ECBABB190F32F9FE9A360DCEF8306F,
	AspectRatioFitter_set_aspectMode_m810BF480F4C07C609339AB7546445A29881103A0,
	AspectRatioFitter_get_aspectRatio_m44B5119268774856091D7711325C2AB81F1D67B8,
	AspectRatioFitter_set_aspectRatio_mA12F8575EB30A7B2E734F188169FA7F91192BB67,
	AspectRatioFitter_get_rectTransform_m8764D78A30C6496122E08EE1BAD137B8B6B14B57,
	AspectRatioFitter__ctor_mA790862A4DCA8878F2E7E8AAF82AF8BB7529FFAD,
	AspectRatioFitter_OnEnable_m38A96D4F9AC1143D68CA9E5E71E738392CCB8922,
	AspectRatioFitter_Start_m18B1282D4D9AFD417CF46B9232AEB2979C27F27D,
	AspectRatioFitter_OnDisable_m2C7C486ECD1DCE0A75498620CC597995619F5545,
	AspectRatioFitter_OnTransformParentChanged_m37F22AA99DEDC02E14217AFCB33A821A7C4E1AA2,
	AspectRatioFitter_Update_m3E413128D33AB67A7D08F12D9E8FD3B2F67AAF14,
	AspectRatioFitter_OnRectTransformDimensionsChange_mA05F29955CFC331A079CE71C19B6D0D14A0080E1,
	AspectRatioFitter_UpdateRect_m139CA7294F60E8CF54B8CCCA846059D56BCE972D,
	AspectRatioFitter_GetSizeDeltaToProduceSize_m5262FB8F1C6DBB9F3E7C4D52790420EA5267497C,
	AspectRatioFitter_GetParentSize_mB88924D7ED70E1DA5C59263D15D680C545EC17A9,
	AspectRatioFitter_SetLayoutHorizontal_m54B187C0FD7273A1606BA3041BCDD7B56BAEB764,
	AspectRatioFitter_SetLayoutVertical_mAFEA45A75696F6C078C2486E2ED1480A7D88B744,
	AspectRatioFitter_SetDirty_mB456E2343490B4488A973F06D2F978E1724B301F,
	AspectRatioFitter_IsComponentValidOnObject_m3A52897DC8A8ED421D0E8258901D39838A151F4E,
	AspectRatioFitter_IsAspectModeValid_mE2E56DEE17B7C09F539A24B46CEFBFDF352AE25E,
	AspectRatioFitter_DoesParentExists_m5F215E68AA01868AEF6BD74C7976ADB424AB7D21,
	CanvasScaler_get_uiScaleMode_m6B2801FE9E3816E01F2B85F46BB554695B6FC7A5,
	CanvasScaler_set_uiScaleMode_m75D0DFC352DB3934976F081732AB03CFB7565568,
	CanvasScaler_get_referencePixelsPerUnit_m750F6B1AA54243A8FF73E3F94894F76FFF82999C,
	CanvasScaler_set_referencePixelsPerUnit_m69FD2D7C21EA8FAF9E639F8D6D4FC3858AF9F926,
	CanvasScaler_get_scaleFactor_mC9E5B58CB6C5B97934900BE2AD20BB662A14829D,
	CanvasScaler_set_scaleFactor_mB0976DB810D4AB2410D7F50CD39BC75E1A143197,
	CanvasScaler_get_referenceResolution_mC88C9BB9FAB47FEDAFB14ECC57620870E965F542,
	CanvasScaler_set_referenceResolution_mD3200E76103B16C8064B6E48595EFC9C5BDCB719,
	CanvasScaler_get_screenMatchMode_m3AF1E5875BA22777EC08A70E308EFF74FE5F8C36,
	CanvasScaler_set_screenMatchMode_m2A140B7ABD7510CFA4130EFF84E1AC085C25351E,
	CanvasScaler_get_matchWidthOrHeight_mB0DF4FAA9AFC23C53DF212CC4092775162D59B42,
	CanvasScaler_set_matchWidthOrHeight_mE55542B64CA909A849FC57EEE1DD5E4C7447C8C8,
	CanvasScaler_get_physicalUnit_mAC6375D4B4AD8F24ED9CB9F5AD630A6903D4FEDA,
	CanvasScaler_set_physicalUnit_mA17EFA8B9034AC52A5F28B7A974412BB11078D4B,
	CanvasScaler_get_fallbackScreenDPI_mF5310101362D88C5E23A7CB83987279682C8BDE7,
	CanvasScaler_set_fallbackScreenDPI_m6E9B2C54C3E3498E2EE38791241C4859875D43F5,
	CanvasScaler_get_defaultSpriteDPI_m0919438D2993679F002EC4B723E4E4BA4880B2B6,
	CanvasScaler_set_defaultSpriteDPI_mD9CF4AF444F2CCAD4C377A05FA6324DC1EB4676F,
	CanvasScaler_get_dynamicPixelsPerUnit_m5CD7C22018C45A1283DE516361671E30FB2AAE6B,
	CanvasScaler_set_dynamicPixelsPerUnit_m9C1A6BAAB42388B770D93E9DF28C01CB76F8DE99,
	CanvasScaler__ctor_m163FF7D442D68D88E593709707770F3D52FC61B0,
	CanvasScaler_OnEnable_m8CA19CA8AAEAA0A7B9E045C8C327487C1DB2FBAA,
	CanvasScaler_Canvas_preWillRenderCanvases_mFF22B98C4E3D799799F88F36F8853C6782AE2526,
	CanvasScaler_OnDisable_m025E1C4B1629A29EA8C1C0A101C76973E8DCA8FF,
	CanvasScaler_Handle_m72CD0853B60F4AEF304B81A91173BA08B0F0C555,
	CanvasScaler_HandleWorldCanvas_m92E81FE6FA13E6A3D500F79FD1E9645CEDF91E41,
	CanvasScaler_HandleConstantPixelSize_mBCBF7101336FEAFE8DD14FE9EFFEB86D96CD70E6,
	CanvasScaler_HandleScaleWithScreenSize_m0182771F235A98D2733E568384ECCF77EA4A0973,
	CanvasScaler_HandleConstantPhysicalSize_m042683E0BFCE47F0AB6457A07FDC74435350D492,
	CanvasScaler_SetScaleFactor_mE8817AA99B5DB531BFB3304704B53DA2D848D7E4,
	CanvasScaler_SetReferencePixelsPerUnit_m34EE2290639A159BE66A23C7B7BC7AE6FA7B83C7,
	ContentSizeFitter_get_horizontalFit_mCC5F1FB35E99093EDF9E2A8031D6D40485654DED,
	ContentSizeFitter_set_horizontalFit_m2614655A0C802BD8FCE5B5C68642DC30EF48D678,
	ContentSizeFitter_get_verticalFit_mC1AA72ACD85DBFB0A90DE3B4DB636A12DFB27AC3,
	ContentSizeFitter_set_verticalFit_m03290C9732B5B9E014C01DC21DAE634C903B30B7,
	ContentSizeFitter_get_rectTransform_mF90F07D2624D24638CD10040BAFFB2F0DE04CB9A,
	ContentSizeFitter__ctor_m29659B73718BD0188F77D029C0D934672B3176ED,
	ContentSizeFitter_OnEnable_mF6ED50D7212B491EA29464AC4C6F652CB3D063F6,
	ContentSizeFitter_OnDisable_m3CAA304211378CA2113775A6211EAC36656DFCEB,
	ContentSizeFitter_OnRectTransformDimensionsChange_m6C5066B0EBC0316EF2FFE3B7E265D15D028E3B38,
	ContentSizeFitter_HandleSelfFittingAlongAxis_mBDAA1B648DCF90F864E232A99D1039D58F7F6BA9,
	ContentSizeFitter_SetLayoutHorizontal_mDF1737C0F62A8810857BA399E216BAC530D8611A,
	ContentSizeFitter_SetLayoutVertical_m2BE1A92C2CC913BC359F2861A77A9E167AF0FD34,
	ContentSizeFitter_SetDirty_mCEE7380AA0EDF47B90FF1D0962ED6069718C65AC,
	GridLayoutGroup_get_startCorner_m7B8BCE61E4A7CA9895A69610A9660EB4FF9B01EF,
	GridLayoutGroup_set_startCorner_m8CC0C6A40ED0BDD71B90B9FA1EF0536699AF6939,
	GridLayoutGroup_get_startAxis_m1CCE88FBF37EA3DA85F16679DBB39FF01ED675F9,
	GridLayoutGroup_set_startAxis_m70A706B56A1A5AC2B513DDC7DA6A8633901819E7,
	GridLayoutGroup_get_cellSize_m8F6B1D095560E6DAF3620280D00458A333C2C9DE,
	GridLayoutGroup_set_cellSize_m61514081AED88038DC0271BD20DCAB3D75E6A839,
	GridLayoutGroup_get_spacing_m6FBE62A17EB67001AECC9321F3BFC116749F55F0,
	GridLayoutGroup_set_spacing_m386B56A802B6EBE7EC5B43CEFB8D1355322939B3,
	GridLayoutGroup_get_constraint_mFA42B1E79F0E43DE5BBEC9D2FDA67AF680002CA3,
	GridLayoutGroup_set_constraint_m4363B759F362D1A718743CF430A9A1D1604056AC,
	GridLayoutGroup_get_constraintCount_m71979F3F937B006FC91512ED3C36345A66CE8195,
	GridLayoutGroup_set_constraintCount_mDA9246D502A94EAADE8554BEE7535649BC6995A6,
	GridLayoutGroup__ctor_mF7D4849C233DD54D341F49C69F54BCAB76A53967,
	GridLayoutGroup_CalculateLayoutInputHorizontal_mDEBF6BB5ABC44910FB07523C7E990AB50DA14B41,
	GridLayoutGroup_CalculateLayoutInputVertical_m810E7C656E03603A84AB66EA860C0B8AF8263638,
	GridLayoutGroup_SetLayoutHorizontal_m6171F3C7990EC2506EDBC465124716A0F7CD3EA4,
	GridLayoutGroup_SetLayoutVertical_m8A1A0B2B81D83339119D8E1219FF92CA4036D6C6,
	GridLayoutGroup_SetCellsAlongAxis_mEE61F59F97D16F5F692C79E7CFB7B85BC0380209,
	HorizontalLayoutGroup__ctor_m5BD32654793C99610B165A9A926B495952B03853,
	HorizontalLayoutGroup_CalculateLayoutInputHorizontal_m58F17FFCD8BAEEA48D962F4118F4E59D9F2B7D31,
	HorizontalLayoutGroup_CalculateLayoutInputVertical_mE7BA088589FD23BDF517FBB19B83AD0027A75685,
	HorizontalLayoutGroup_SetLayoutHorizontal_mBF6CA4AD5D92305DC79F350C62E6B5807DFBA238,
	HorizontalLayoutGroup_SetLayoutVertical_mAA537085E5664BE721EA09F25F4E0F0F74DB3FC6,
	HorizontalOrVerticalLayoutGroup_get_spacing_m52E2EAC47D467F71010583C5AAB31F4D523AC278,
	HorizontalOrVerticalLayoutGroup_set_spacing_mE5769AC8EBF0C7C9076D71509CC19DBEFF5CEE04,
	HorizontalOrVerticalLayoutGroup_get_childForceExpandWidth_m493C24DC7BCBAEBB4D0F2C3CA7041EEA32E543F7,
	HorizontalOrVerticalLayoutGroup_set_childForceExpandWidth_m6D4FAB24FD5620FA409E7102779E67894A05D116,
	HorizontalOrVerticalLayoutGroup_get_childForceExpandHeight_mD9D677E0E1D519AC1E046C52B23967A5B4416223,
	HorizontalOrVerticalLayoutGroup_set_childForceExpandHeight_m4C90F31DD9AB2098FA27C9E826F66C3CCF843E7B,
	HorizontalOrVerticalLayoutGroup_get_childControlWidth_m2C410BBD452AF6E2A74D97041D10B02DE93C17A2,
	HorizontalOrVerticalLayoutGroup_set_childControlWidth_m7B290C97F81F3F0519F8E0E4B4C532B9D0FB8A50,
	HorizontalOrVerticalLayoutGroup_get_childControlHeight_mFCCDB9F070C4FBD4B34C856931123F42287F4784,
	HorizontalOrVerticalLayoutGroup_set_childControlHeight_mB6FC9B6A66716FBA81C9D50526A00C14DACDD289,
	HorizontalOrVerticalLayoutGroup_get_childScaleWidth_mB5B84A9068062297EA5445B423B2BCBB379C9BDD,
	HorizontalOrVerticalLayoutGroup_set_childScaleWidth_mBFD464F83FAA3ADAB539E59A5A677B95768A2E27,
	HorizontalOrVerticalLayoutGroup_get_childScaleHeight_mB8F63923C43CCE678A7B3B2234C684C0D76867D9,
	HorizontalOrVerticalLayoutGroup_set_childScaleHeight_mC928D80D266D4A57E01CCE604821200E01D54FFE,
	HorizontalOrVerticalLayoutGroup_get_reverseArrangement_m8C11FEF137F0E3A9BFE8B6AE7FEDF8D1E38126C8,
	HorizontalOrVerticalLayoutGroup_set_reverseArrangement_m467D77B8C2C113AACF694DDF4561ED35C2290F66,
	HorizontalOrVerticalLayoutGroup_CalcAlongAxis_m88F784D17AA542ED1CD28A4541F422A7E90CBE14,
	HorizontalOrVerticalLayoutGroup_SetChildrenAlongAxis_m478E2367383D18BF103AD4C58360BDB002F7A88C,
	HorizontalOrVerticalLayoutGroup_GetChildSizes_m7FE67759B97A7270B9714ACD3D1DA62E0A81FE04,
	HorizontalOrVerticalLayoutGroup__ctor_m3FC0FB5106A29D484A1D08F92547715FBBB39337,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	LayoutElement_get_ignoreLayout_m90FB86E9510B28B452FF7CC2C318E4D84412391F,
	LayoutElement_set_ignoreLayout_m44FD1F001ECF85ED7E9A8CE6D3835111FF32623F,
	LayoutElement_CalculateLayoutInputHorizontal_m2AA33718284ADA0ED37CB7D2A1FB44FCCB56BF30,
	LayoutElement_CalculateLayoutInputVertical_m47451BFB2053F6C8384D1CA005B2D59247534528,
	LayoutElement_get_minWidth_mD835C4483246B5FC4D75762EBE915C53C79D1F14,
	LayoutElement_set_minWidth_m28700CB74FF7859E10F7FA479E7C46ABCCABE1A7,
	LayoutElement_get_minHeight_m5DC25C52E5E7A09848B09F720960D35CF9EB3A8D,
	LayoutElement_set_minHeight_m5C71632B3DC3222A9C876D56CAC70688CB84FF51,
	LayoutElement_get_preferredWidth_m73D4E83DCD8902E9335F99BB328F11CBE8F6356A,
	LayoutElement_set_preferredWidth_m0954FDE76D9B80A1E18394E038D703ED10EF426F,
	LayoutElement_get_preferredHeight_m1E8012BD187CBC867AB4EB71972F650EA1795CC8,
	LayoutElement_set_preferredHeight_mE369B9F754F79CDA63F01E2CBD2415AE5C7BDB46,
	LayoutElement_get_flexibleWidth_mEE15E7963CBC510FEE099F642066B0DF8EC60605,
	LayoutElement_set_flexibleWidth_mAC6031498B3C17D4DD9F7793ACE68B53A1A8E2FD,
	LayoutElement_get_flexibleHeight_m6C7B9FE7CDE05C968B84E7067765592CEE74A39C,
	LayoutElement_set_flexibleHeight_m5851510F4E7D4D21210053A429FF136E079F0E29,
	LayoutElement_get_layoutPriority_m88EDDAE40F620749222D39951F63FFFD0335A238,
	LayoutElement_set_layoutPriority_m7400A83A4C8BB6AD2E278B7971AF2524CF0C997C,
	LayoutElement__ctor_mEE483CE245BEE525E0C1198D888A6E63F11BDC42,
	LayoutElement_OnEnable_mECD267BF0D6E730570A9D856A15D83DCB673C60C,
	LayoutElement_OnTransformParentChanged_m60B50562ACAF5289FBEDC949EE137C0A2AD66AA5,
	LayoutElement_OnDisable_m8062A074769E24789C500041F75E5E93EAB25098,
	LayoutElement_OnDidApplyAnimationProperties_m18430F40F3F6090341BE86AA5D9E0E672D128FBA,
	LayoutElement_OnBeforeTransformParentChanged_mB3A9008D088FCFEAF7E4CADF7534645BB450703C,
	LayoutElement_SetDirty_mFCCF9B49870E36FCEDB5508C181FA4CB15C2E2D5,
	LayoutGroup_get_padding_m14312E35C2C558577068963736278D801BA9BFCB,
	LayoutGroup_set_padding_mBABB15FB92C0766BA1FA688FE7D7C252B9D6560A,
	LayoutGroup_get_childAlignment_m670498A8C95B7E523D0E819BB6FB4A52B5464B12,
	LayoutGroup_set_childAlignment_m574A13B9726C01BC30AB0C164C925D3B09C68748,
	LayoutGroup_get_rectTransform_mF81F1612DF0C56799E91552048508B5A94896CD9,
	LayoutGroup_get_rectChildren_mB8A4C237BEAE27F143A2F7571192BF6736B5E52E,
	LayoutGroup_CalculateLayoutInputHorizontal_m5E1D66D491C159A1F45014E6115A56719B3B9933,
	NULL,
	LayoutGroup_get_minWidth_mEF0E63E9F35D0085FB39887A2D104A9427E4D996,
	LayoutGroup_get_preferredWidth_mE4CBB2309F30250BE73BFA7913A52C09138EF801,
	LayoutGroup_get_flexibleWidth_mA2F83444A705FF8A20AE91EAC036D9015BFD46E2,
	LayoutGroup_get_minHeight_mB1AE8171B557C9A14744305181D2FDA300FD5C6F,
	LayoutGroup_get_preferredHeight_m0649F34C601D214D5CB51246FED7D79F40ABBFBB,
	LayoutGroup_get_flexibleHeight_m4943BC00D95A92703B653243EBCC1A6CA56D4B25,
	LayoutGroup_get_layoutPriority_mD389BE409DEB26E85405C0BA7477E21789EC10F3,
	NULL,
	NULL,
	LayoutGroup__ctor_mF6B995B1A6B39EAB7999D5A41260651463FDFE37,
	LayoutGroup_OnEnable_mD22B04F97D7FB19DD4E9F0E1F179B00DA305B42F,
	LayoutGroup_OnDisable_mF9EC32C322F3B4B15C1FEE77CA5F04CD4035D8F5,
	LayoutGroup_OnDidApplyAnimationProperties_m447CFDEA42F252105BCE07A58F7F26D1C93994D4,
	LayoutGroup_GetTotalMinSize_m0D5047B7CCD8A168F3DE7F6CF3CA68D62D50C32C,
	LayoutGroup_GetTotalPreferredSize_mD57F59092D1A3CD7ED35A13672FE3E9D706410B9,
	LayoutGroup_GetTotalFlexibleSize_m5C74F3CD2D5D236CD7A4B49B83E85A216B1530A4,
	LayoutGroup_GetStartOffset_m071CE94DE28E370E83633BBC8D5A699D91A45344,
	LayoutGroup_GetAlignmentOnAxis_m84AE6B5EF7769C6154E5B36FC04D66F8CBC93790,
	LayoutGroup_SetLayoutInputForAxis_mDAA241F8AF27C222C01B89CB659BB6446D87A299,
	LayoutGroup_SetChildAlongAxis_m8FDB65849BEDFD74A3E08D986122AE6B6807E739,
	LayoutGroup_SetChildAlongAxisWithScale_mD655B16F2D4CBF6F504A85349868F9668FA04623,
	LayoutGroup_SetChildAlongAxis_m5D13D8EF43A7C6126A33BD8E21F260956700C52C,
	LayoutGroup_SetChildAlongAxisWithScale_m65623CB84FBEF746D08E0003F261468AF67075C5,
	LayoutGroup_get_isRootLayoutGroup_mAED51B1FF22135348E2088D8746E5F51E0FFEC2B,
	LayoutGroup_OnRectTransformDimensionsChange_m85246BC71AA506DCC65AC2CEBE1AEE2DB28D287B,
	LayoutGroup_OnTransformChildrenChanged_mFDF8D16A0C6A4C50CAB1997ABB33E17677733E3C,
	NULL,
	LayoutGroup_SetDirty_mCD44774BE69A93938EE1622CC13FFC2C16DDB021,
	LayoutGroup_DelayedSetDirty_mD732CCC3419B94BF80F00D4E1126C3AAFA249C8B,
	U3CDelayedSetDirtyU3Ed__56__ctor_mA52F10924596857781781E2D32305ED395CCFC40,
	U3CDelayedSetDirtyU3Ed__56_System_IDisposable_Dispose_mB2EEBBFE1EDA19D7668469D89C5DC20135F92CA3,
	U3CDelayedSetDirtyU3Ed__56_MoveNext_m2600A9E03E1423A54B1E91926F6AD71E180719BC,
	U3CDelayedSetDirtyU3Ed__56_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC0766D0840885F5FD82287052C846A487A3C954D,
	U3CDelayedSetDirtyU3Ed__56_System_Collections_IEnumerator_Reset_mD3851B6C7275A35789E989AAFB19866B0240BEAC,
	U3CDelayedSetDirtyU3Ed__56_System_Collections_IEnumerator_get_Current_mFA346E997DCBDC2A7E20F57FD48C4A012EC0ED4E,
	LayoutRebuilder_Initialize_mA64FB3CEB4C28A10A77DE188CD36837A9C5F601F,
	LayoutRebuilder_Clear_m28BB858DDB42479A0E2C9FA91467381CF8755669,
	LayoutRebuilder__cctor_m93AF2F44B9B7411276E2374CCD10F4F70456E380,
	LayoutRebuilder_ReapplyDrivenProperties_m92F035C865057BF1D34CC02662DABB29EB543B0F,
	LayoutRebuilder_get_transform_m16D6996C3E2A1F975BA5F280E8F530E0EBA86B8C,
	LayoutRebuilder_IsDestroyed_m7C24E5FF56D4600C3368CC88511D92DA4B03BE57,
	LayoutRebuilder_StripDisabledBehavioursFromList_m1B50BD38F9EE421716F6E763AF6EF8D43B241227,
	LayoutRebuilder_ForceRebuildLayoutImmediate_mA56A6E02EFB1DD50371E603EDD49297707F146CA,
	LayoutRebuilder_Rebuild_mB7370F51240B9FE738FED8593FB729A953783EAB,
	LayoutRebuilder_PerformLayoutControl_m02DBD164DD8EDCC8D5573E86C26A65C59A933ADA,
	LayoutRebuilder_PerformLayoutCalculation_m7F8DF3422B31393955167737593C3AFF6B607531,
	LayoutRebuilder_MarkLayoutForRebuild_m025A52454C18402ADDE59FD652A6A93262DD4374,
	LayoutRebuilder_ValidController_m759455420379586151C15036400E7B8B1DCA6B38,
	LayoutRebuilder_MarkLayoutRootForRebuild_mD2CA04C7F9E68839AFBD3054FFE3948C036A9725,
	LayoutRebuilder_LayoutComplete_mE4F8C9D77EAF91B18B0C76979E0B3891986C22FE,
	LayoutRebuilder_GraphicUpdateComplete_mEE92F38BE479A86089EC72D083A14ABE1E5B3F1E,
	LayoutRebuilder_GetHashCode_m76283AE71310FB5F1D685EB98F62DA09C929AC0B,
	LayoutRebuilder_Equals_mC3E1CF820F9BC34537A9E5465DA0CBC795CC276C,
	LayoutRebuilder_ToString_mC07B5519A043C74D0600F98FD019214349ACA974,
	LayoutRebuilder__ctor_m463A9E152E52208B053AB1AB2188FD2D8A0ACB28,
	U3CU3Ec__cctor_m3F5D7F404B03B80C5F061CAABDC75ECA40BA1159,
	U3CU3Ec__ctor_m08AEBA4CF31106A157D26C8F80A5BAC82544DA2A,
	U3CU3Ec_U3C_cctorU3Eb__5_0_m8E25902030C5EC6F9AF84E1FACF3135A511EE87E,
	U3CU3Ec_U3CStripDisabledBehavioursFromListU3Eb__10_0_m701215783D65B7E21FBEDF6DD2D14AB22719C28D,
	U3CU3Ec_U3CRebuildU3Eb__12_0_mA041A1DCC1746B1827D000661B5CD0D3AABE0161,
	U3CU3Ec_U3CRebuildU3Eb__12_1_mB33AEEFC02E5A2BADD0EC1020E2975B7ABCA9107,
	U3CU3Ec_U3CRebuildU3Eb__12_2_mA37813EF3059FEDE8365C5F941899CD429089586,
	U3CU3Ec_U3CRebuildU3Eb__12_3_m50289A90C028D9B108DB0F8551E1237AD636337F,
	LayoutUtility_GetMinSize_m8D3AAF478A0A261BC05548DEB8ED61B795B5A554,
	LayoutUtility_GetPreferredSize_m3A02690B68FE61A89524BBB93F52D262061A838B,
	LayoutUtility_GetFlexibleSize_mC7D226041C128F868058F137299E20626F9F87AD,
	LayoutUtility_GetMinWidth_m5730683A1CF1F091BD18E17C897345EB35093315,
	LayoutUtility_GetPreferredWidth_m62BA20D2ECF0A0502BA8478F5D63658FA844947F,
	LayoutUtility_GetFlexibleWidth_m68EF94A84D1037BA28D99F15D3CDC20781712A2C,
	LayoutUtility_GetMinHeight_m527516709B779A3B459A54A9AD6F892828CD706F,
	LayoutUtility_GetPreferredHeight_m0D9083AAD4CAA0656A6EFB0449B64537FD2C4547,
	LayoutUtility_GetFlexibleHeight_m18B694D154FD9DB74320D96C51F2919CB0405920,
	LayoutUtility_GetLayoutProperty_mDD68B3F4FBB2D4AF14CD1434A5CC21381C964577,
	LayoutUtility_GetLayoutProperty_m5D0585B7B2F1E9DA5EAEE60FBCA3B387ADB9BFCD,
	U3CU3Ec__cctor_m4B1FD62EA1AD030EA991BE423C491A1E8F64CD7E,
	U3CU3Ec__ctor_m1B199444775398EC661E1E5A54F902DDEC91E77F,
	U3CU3Ec_U3CGetMinWidthU3Eb__3_0_mCBA85C4A1F5E5868352281A6D02ECB2DB3CA543A,
	U3CU3Ec_U3CGetPreferredWidthU3Eb__4_0_m98B9DCF76E6BF84270C3DA0B8E3A98868E056390,
	U3CU3Ec_U3CGetPreferredWidthU3Eb__4_1_m4460F5F291EA091DC96FA574787BFBE261FFD659,
	U3CU3Ec_U3CGetFlexibleWidthU3Eb__5_0_m168AAD0730CBC71061EF59ACE641DBAA2F73EEF5,
	U3CU3Ec_U3CGetMinHeightU3Eb__6_0_mE547AB359DF68B6250AC20EAE08BF7A6B370039B,
	U3CU3Ec_U3CGetPreferredHeightU3Eb__7_0_mD751E70588985A919D25922900C1C3CBE2CC7A9B,
	U3CU3Ec_U3CGetPreferredHeightU3Eb__7_1_m490DBFE3A1AFF5E10F751C55998C2E951C542594,
	U3CU3Ec_U3CGetFlexibleHeightU3Eb__8_0_m500C6DA403AFD260B45ABFCE746ED86872EECC2F,
	VerticalLayoutGroup__ctor_m50FC410FAF5C69492B239DA9A18189563054E4D0,
	VerticalLayoutGroup_CalculateLayoutInputHorizontal_m6C97EB48D36329CEDA4D94DC825DA2DD8D49AF99,
	VerticalLayoutGroup_CalculateLayoutInputVertical_mD376BB61F211CE987B424108EA749D9BA17D1F52,
	VerticalLayoutGroup_SetLayoutHorizontal_m6CE79D24AC376A32573E305024D9ED763BED8CDB,
	VerticalLayoutGroup_SetLayoutVertical_m41201990B993E828922B56DA65C656505F0FA2E3,
	Mask_get_rectTransform_mF1081D590012320A4D694F33A7965B7A8DFE4A28,
	Mask_get_showMaskGraphic_mA87FB0DBA07B8761DA2C923EA50CD60BD3EB7271,
	Mask_set_showMaskGraphic_m7AF49BD8866B82C70293D2C666D979FB320168D4,
	Mask_get_graphic_m3C7C03706C188F60DB2068C62459E1BF0277FED4,
	Mask__ctor_mC3A6C226EDD04AC7058424F80D413DB9A85570AE,
	Mask_MaskEnabled_m87EF5C7FCC4DA5A28BEDA3FE7986DC78BD3BCA04,
	Mask_OnSiblingGraphicEnabledDisabled_m7710190D8CD6A9D2F4C2C1DB55C1226637B52D1B,
	Mask_OnEnable_mD321373E240B3013212217903A2902D12C904A21,
	Mask_OnDisable_m24823C343812F3A0CE28B2B0347139B61B07B289,
	Mask_IsRaycastLocationValid_mD5F1B486F5816382572A751AF9D7A5329E9C4469,
	Mask_GetModifiedMaterial_mB0819C9D0018D8DC89510BBD5BD8DB35EF50837D,
	MaskUtilities_Notify2DMaskStateChanged_m78C7E6535470CF1345ED9471C3878CE24324A08B,
	MaskUtilities_NotifyStencilStateChanged_mF0A329778C7F1FD73762B86A0B37A02312D2BC87,
	MaskUtilities_FindRootSortOverrideCanvas_m8011C7BE2E9AA5F60134D65013FD5F5BE25CED6D,
	MaskUtilities_GetStencilDepth_m849019B4446DB533DEB78006CCC46C99620D92C6,
	MaskUtilities_IsDescendantOrSelf_mC354024F9052BA6D06229258CB686082C3D747FA,
	MaskUtilities_GetRectMaskForClippable_mDF3C0050FBD1491A248D70B9ED136F73688D2BB8,
	MaskUtilities_GetRectMasksForClip_m4DFEE101F2703345EB46AD9B217D4760822B0F94,
	MaskUtilities__ctor_mED82BFC80F6E0DDF6687DFCD89139F3F0B376B3E,
	MaskableGraphic_get_onCullStateChanged_mCF39F6FB561F9F9866E23662066E26718AB07CE0,
	MaskableGraphic_set_onCullStateChanged_m5239A4147A72D6113180B1A2C444C5F76D388A39,
	MaskableGraphic_get_maskable_mAB089F676929CD6D1CA9FC204EE04A10EF86B6E8,
	MaskableGraphic_set_maskable_m59171D018C5424DC8CA4E46D005A28F71AC2B1CB,
	MaskableGraphic_get_isMaskingGraphic_m147E8FC2072567862BAC9AFDFE265B76C7708202,
	MaskableGraphic_set_isMaskingGraphic_m274342FF4513DEB896214303AA74206944C13601,
	MaskableGraphic_GetModifiedMaterial_m58800A1D036E9FD9040E871F5CA2573D59EC211B,
	MaskableGraphic_Cull_mF3E7CA1619211B6695E398275AC8EB62F13D6CBF,
	MaskableGraphic_UpdateCull_m5E32F0412FD48EBAE03F539655B05A55220C2BD3,
	MaskableGraphic_SetClipRect_m8BBAB7D151E7F87910ED49FFC4145464CDC8D71C,
	MaskableGraphic_SetClipSoftness_mA1280583A758409DD41DADD9C0EE2178BBAB74AE,
	MaskableGraphic_OnEnable_m61F2B68A4560CAB2A40C3C6F6AF74C3C10D80AE8,
	MaskableGraphic_OnDisable_m85189B68E2DBE5ECCFBC9B2A1385F38050FE2686,
	MaskableGraphic_OnTransformParentChanged_mF98185DF6C98FEA60C6EF77969D2F23638C03846,
	MaskableGraphic_ParentMaskStateChanged_m3E76F4824392C343448165502617A717297457E4,
	MaskableGraphic_OnCanvasHierarchyChanged_mAB3381225CEB6CC2803046EC7B9C7CBBA8887EC6,
	MaskableGraphic_get_rootCanvasRect_mE2C5C1C3237308CFEED55BB501AE001174A75AA9,
	MaskableGraphic_UpdateClipParent_mF0C05D442F080B5902D081512371DE9E32F739B0,
	MaskableGraphic_RecalculateClipping_m0402575B6C04BC3A36CAE548FC29B380B923CC09,
	MaskableGraphic_RecalculateMasking_mC8BE4CA3AD0428C8C0F520864E47AC9BA64B4A2B,
	MaskableGraphic__ctor_m89126DB114322D1D18F67BA3B8D0695FF1371A4D,
	MaskableGraphic_UnityEngine_UI_IClippable_get_gameObject_mA760B34D8EFAD579D0277436706534CFE46FA238,
	CullStateChangedEvent__ctor_m733D6A4DFDEED4F4812B6EFD581007DB79C1FB57,
	NULL,
	Misc_Destroy_m66309EEE8B043D11C853ED50B66DDA0A830FA7F0,
	Misc_DestroyImmediate_mB67B6DF39213A22333EB9ADC8DC52C1D7B6B7F74,
	MultipleDisplayUtilities_GetRelativeMousePositionForDrag_mD23D7090ECC680ADC154DA4930EADE29B6D092B8,
	MultipleDisplayUtilities_RelativeMouseAtScaled_mE54C997DC566E0E97F97D5A5A4F109688A023C1B,
	Navigation_get_mode_mB995DE758F5FE0E01F6D54EC5FAC27E85D51E9D9,
	Navigation_set_mode_m06986291C80ABAB009A7968A3A5C557197B7CAD0,
	Navigation_get_wrapAround_mD84154C6A5DF2D999F9C68694660BF4C75E42E09,
	Navigation_set_wrapAround_m88C6E2CE8D45F409B10220931C7574A925478EEF,
	Navigation_get_selectOnUp_mE2CE90A504C34AE46D8BC9930C7E309F3BD61D05,
	Navigation_set_selectOnUp_m4FB3B1A6FD96E0F5AF5A029C49150E05520EAEB4,
	Navigation_get_selectOnDown_mDEAD9711CD57EC13A9622AA29E1D95E7FE49B26C,
	Navigation_set_selectOnDown_mFAAAE5BDCD7A658119C96474C108C9A59E62F2D5,
	Navigation_get_selectOnLeft_m7E124CA8B6205001892CA5F8FBCC3CE2A451ACF8,
	Navigation_set_selectOnLeft_mA33AA78D5F7F6BE93490C9EABF182E1D77C0E580,
	Navigation_get_selectOnRight_mA7E6599E6A54419AE4C532F76F4199EFF2188114,
	Navigation_set_selectOnRight_m6B60DBA287E342806BF4CF1AD332DA4EF875BD4A,
	Navigation_get_defaultNavigation_mEF98E8BBEC8608B4332A9417042B3E1D1EDC1955,
	Navigation_Equals_m1462D8660E507B770231FF9CBDAC296C1D751C7A,
	RawImage__ctor_mF745F95A5854034191E5787E9A22F2267943FDFE,
	RawImage_get_mainTexture_m6607D1D2672EF3ADA7BA65E5116516AE994B998E,
	RawImage_get_texture_m12060F8F44CCBFC6753E455FAF61A075E89022A1,
	RawImage_set_texture_m1D7BAE6CB629C36894B664D9F5D68CACA88B8D99,
	RawImage_get_uvRect_m5C5CB285CD0DBF30B9DE8A62C90011B22FBF523B,
	RawImage_set_uvRect_m529CC2006BBD8AB373C567F80F4FCABEBFF1242E,
	RawImage_SetNativeSize_m558874FAB55362D5ED28C5D2551A23E374CDF960,
	RawImage_OnPopulateMesh_m6EA21D82C0D9C4327339440CB4292CBAFAA3B87C,
	RawImage_OnDidApplyAnimationProperties_m7E77F85DF55B8F807033B1A69226A5C0DEF0B322,
	RectMask2D_get_padding_mDE45F1235EBC500D855A14D8A191C553864F5B6C,
	RectMask2D_set_padding_mC6682C99C43EE4EAA308EEF0734E31F2E9509BDE,
	RectMask2D_get_softness_m2759F63DF0C023E1B7C442EEEC79B2EFED2F0766,
	RectMask2D_set_softness_mBAA0308DDEF6B338515E73917DCDFBB5BBB69577,
	RectMask2D_get_Canvas_m2109052587563A271881729044E0896C8330EBCA,
	RectMask2D_get_canvasRect_mCF9908196FEF28E2493F401755BC48D49F55D8F2,
	RectMask2D_get_rectTransform_mF0BAAE5A88C9390C41E5CEB4A2D944A4F69DC33D,
	RectMask2D__ctor_mE0AA40ADEDAD189C28C17B038732C249C54F2C43,
	RectMask2D_OnEnable_mFD84098C65DC9A4C58A3A9127079B6ED17F79A98,
	RectMask2D_OnDisable_m995393F2508BCF5688DABE9F19F1CABF49A4709E,
	RectMask2D_OnDestroy_m2B7DFD2F15BAA18CF021C0E58740666989AD5170,
	RectMask2D_IsRaycastLocationValid_m45BB3B371ED9DAA908E18633FB1EA9B1005B127B,
	RectMask2D_get_rootCanvasRect_m215A31029C47BDE07D332433F58A77CF34A8572D,
	RectMask2D_PerformClipping_m2D6A62F7600B3897AA061A4D4760E4B532C6F768,
	RectMask2D_UpdateClipSoftness_mEF617C8CFF0C3284D06ACFD7B61F48CE2B6B7585,
	RectMask2D_AddClippable_mF1C7DF9391787AF278C408D179E1AD7C1798A6A0,
	RectMask2D_RemoveClippable_mF30C17434A5F117300B26C7D2D0C06E9E011B454,
	RectMask2D_OnTransformParentChanged_m6B083DD4655623AA054A02CFF269C0702A2605FC,
	RectMask2D_OnCanvasHierarchyChanged_mA96030132A4CA47A6070562B402261C1CF27F98B,
	ScrollRect_get_content_m80081CD9D9D066BB138099E6495390B37071C3B0,
	ScrollRect_set_content_mAE777214875D848F20F1F6795E8848E8E7EF6D65,
	ScrollRect_get_horizontal_m3C85C37B4136F69E47ACFAF287D05FC4104E5C8A,
	ScrollRect_set_horizontal_m45564FC7DD17DE132CCC1A843C6D83A9635C3BB4,
	ScrollRect_get_vertical_mC44745C3E8AC6BF41C3C2D3FA0AEDED3DE96B4EA,
	ScrollRect_set_vertical_m25BA9647871AD3CADF2A26CB938BE5418A13C22E,
	ScrollRect_get_movementType_m12C1D0EB101216FC3C545AECBF883AE23AD82101,
	ScrollRect_set_movementType_m63E3C04ECBDA708AF2341564DB987227D32075CC,
	ScrollRect_get_elasticity_m45FBA92BD4EFFAC555B8ACC4B0D385CE9E775F89,
	ScrollRect_set_elasticity_m6D3FBF1D599AE50DCC8C908E3E4ECE1098F15F9E,
	ScrollRect_get_inertia_mB2D4CBFDDF073DE6BA62E2A248E9BAAFE2886462,
	ScrollRect_set_inertia_m9CE7BE08CEB308DC8B9335436B901D557B4E6718,
	ScrollRect_get_decelerationRate_m318236CD5B50F314C311D61528785E703FDA8292,
	ScrollRect_set_decelerationRate_m1981EED127C9DEE4E02027B53F7779FF39AE2BCB,
	ScrollRect_get_scrollSensitivity_mD3FA21FAA3F5F0B73C93D00FF3F18E786B04D890,
	ScrollRect_set_scrollSensitivity_m9C2DFE6AF0977A4E9B9D9F208D83548EE7A95550,
	ScrollRect_get_viewport_m5F19B84F9A457DB423884801E0D58F53FE371382,
	ScrollRect_set_viewport_mAD1CEECC3A6C8B77CC96C48522D47EA7CB15F846,
	ScrollRect_get_horizontalScrollbar_m0E70E75626442D98852270657FB8535F63789C59,
	ScrollRect_set_horizontalScrollbar_mDEE90E50682241478EA86561DF2426F84C9BD5F0,
	ScrollRect_get_verticalScrollbar_m5A5637770B83D5542ECC2F72C4653FF6B3F6860B,
	ScrollRect_set_verticalScrollbar_m1733A8BFBDA05FC60882409B832C44FE14AED399,
	ScrollRect_get_horizontalScrollbarVisibility_m3DFDDB45471A160F3CDBBEF8C482CA44C6D8DD5F,
	ScrollRect_set_horizontalScrollbarVisibility_m496B18BB2183C7E44AE46D388A1780B0E8E07FC4,
	ScrollRect_get_verticalScrollbarVisibility_mE2CBD69FBB2F9B242D1CCCDDBCB1B7DCCB8657B8,
	ScrollRect_set_verticalScrollbarVisibility_m1D20BA7616E7642DC8E7054A06469D4C8B9D9FA9,
	ScrollRect_get_horizontalScrollbarSpacing_mAFDBCD05E05BF104C4465F6D8BE48ED38A57B525,
	ScrollRect_set_horizontalScrollbarSpacing_mCFDF9FF111EAC5CFA76489DB4F7FE1EF507A4D23,
	ScrollRect_get_verticalScrollbarSpacing_m63D70F75E9C1ED9AF8DE1362967E28F304E6A8AD,
	ScrollRect_set_verticalScrollbarSpacing_m4BA8BD3FFB47F67FA2654086A2D45B94E87B2885,
	ScrollRect_get_onValueChanged_m6CB9D48C2BFBDF7896EB011B2FA7977EEFC05B14,
	ScrollRect_set_onValueChanged_m8BF93395195F8F7C3F55127AC75E2F396B554E50,
	ScrollRect_get_viewRect_m0AA50C04284AB56F79258AA0CBD30A1FFDAE9299,
	ScrollRect_get_velocity_mF5519EB702F5A1BCBDA6EE50CE92EEFA3398BDC6,
	ScrollRect_set_velocity_m6CBDADD5F61163917B37C922FA6F42170D68E1D9,
	ScrollRect_get_rectTransform_mCA3A848543FE3C0BB1A594A11F4726DCF5C4CF91,
	ScrollRect__ctor_mB4B056D158F001158736D43FF95A79C4749F81EB,
	ScrollRect_Rebuild_m8779D60FEAF422554E9FDE675E8A1FF78DBB7B9C,
	ScrollRect_LayoutComplete_m85522038286BE8858947C3E9D1D4C7E36B25D840,
	ScrollRect_GraphicUpdateComplete_mC82C019BFB983269063A158D5BB5B0AD6A27373E,
	ScrollRect_UpdateCachedData_mE1AB66554F5D5B9F2AEA6D1369E678B19B34BEA0,
	ScrollRect_OnEnable_m71DB5029CAF2AA0C6AE3C5265195FA2BD8F3BF14,
	ScrollRect_OnDisable_mB88CAFFED9F9E8C3C1CF730A4C88FDB1C39EBB4F,
	ScrollRect_IsActive_m48C5CB5DA8DA18455B523E5039BD38CF0B6ADD21,
	ScrollRect_EnsureLayoutHasRebuilt_m6F55D294F93B8F0EA375C4A3479588FA1C836756,
	ScrollRect_StopMovement_m34AED9A8CF4EBD7422B90575C87B4A839FB83DDA,
	ScrollRect_OnScroll_m0D80BCED3E165E19CA4A9F30306E3B92A7D70406,
	ScrollRect_OnInitializePotentialDrag_m442FF00867FECD77D57B3ADD27ABB4736C4A783F,
	ScrollRect_OnBeginDrag_mFD8547604ADE7E19C1CB247BE3C3D30EDBF6F170,
	ScrollRect_OnEndDrag_m655F48C5CD01CFC9802144038A635B6F663FE828,
	ScrollRect_OnDrag_mAE4C9A8E16CE9E40F1F50F6CC9DBAC5107327C24,
	ScrollRect_SetContentAnchoredPosition_mF134A239555D56A9EFB5D7B0BBE7337608D9B8F5,
	ScrollRect_LateUpdate_mAFD5AD44F910FFB77D8969DF1C0C52966F79BE94,
	ScrollRect_UpdatePrevData_m5BA02E98ABA18860856725E547949B64C4C88AC2,
	ScrollRect_UpdateScrollbars_m95DCA6709A3A5E1DD0DA5BE024B694F22B7706C0,
	ScrollRect_get_normalizedPosition_m337A8A3C84D96C866B4851D654E69362CC3ABD6C,
	ScrollRect_set_normalizedPosition_mCC99F394BFA5FF1D1F52CF75BC41C0A761F41551,
	ScrollRect_get_horizontalNormalizedPosition_m2447569D682F6F02D64671B82B64FA9C80EE8FE2,
	ScrollRect_set_horizontalNormalizedPosition_m3552851D166277D4DEDFCB6B81BF6726666D3AF5,
	ScrollRect_get_verticalNormalizedPosition_mB5E338D332643DF0B7464F45CC9C04DD9868FBB4,
	ScrollRect_set_verticalNormalizedPosition_m18393A7B91359B013466A984325F233535A6941D,
	ScrollRect_SetHorizontalNormalizedPosition_m0831302489172DCA40C1AB185D551AFEB5B0087C,
	ScrollRect_SetVerticalNormalizedPosition_m5FD7F53D655877D67117439068280E9FB247634E,
	ScrollRect_SetNormalizedPosition_m1443593E3619BBF532744E7567AE36DE2A8C4196,
	ScrollRect_RubberDelta_mAEEA0DF9F667A8BB7B463A30CFF9B7862C4A6418,
	ScrollRect_OnRectTransformDimensionsChange_m98D63D7045CF46910BD761853193CBC1A56317EB,
	ScrollRect_get_hScrollingNeeded_m89C5EB3D4E25F46D317354DA0988B7340DC4AFCB,
	ScrollRect_get_vScrollingNeeded_mE8234F874E268A323926A3A13ED6C2F126805890,
	ScrollRect_CalculateLayoutInputHorizontal_m97CEF11B8DEE2EB2CF2FB5AE3E65B88ECD864042,
	ScrollRect_CalculateLayoutInputVertical_mCD39E6A18E8E9D08AEECC69F5ED4BCDEEFC60066,
	ScrollRect_get_minWidth_m4BFD7F0DC39B06D4744EAFBF7BEA0FA31304360D,
	ScrollRect_get_preferredWidth_m79A19083EAEA565D0FDDE830DC1592E3CCD1E9E2,
	ScrollRect_get_flexibleWidth_m49AC658BFB2CB9200C647FF7C2480F3D58541E73,
	ScrollRect_get_minHeight_mE504CAE24344F7FF00C6612CB075AA05DBCC1078,
	ScrollRect_get_preferredHeight_m02F31C2CEC4CDDD4E822CB8D919A4F40B936C995,
	ScrollRect_get_flexibleHeight_m83AF3CDF404A57FBC672646E252C0781D158407B,
	ScrollRect_get_layoutPriority_m1276F59CCD936DB745749DE12CFA1386958A347E,
	ScrollRect_SetLayoutHorizontal_m34E183E669122BE54199783ED1F15A38929CFCBC,
	ScrollRect_SetLayoutVertical_mC9CD0A1437205234CE2FB2704100A4A7A28CD7E4,
	ScrollRect_UpdateScrollbarVisibility_m1EB5A90580B6EA5A6855E6CBB784B8EA2929DC56,
	ScrollRect_UpdateOneScrollbarVisibility_m03BFF7787125947A60922EA18512BCF1DC0839D9,
	ScrollRect_UpdateScrollbarLayout_mEAB065DB24B0CE20EC5EE3AB9B2FBCF5CAF4E0B7,
	ScrollRect_UpdateBounds_m5D98C2EABD039BDFFC9A3362BE3F2D2DD33BDD7A,
	ScrollRect_AdjustBounds_m63C18824143696C5FAAAD706D24BA76393BA6D36,
	ScrollRect_GetBounds_m84B078D23326F6A5287D35A5FC4978A020FDA33A,
	ScrollRect_InternalGetBounds_mD8104E1E0DDFF1DB7C949F15B445E71FA25737FD,
	ScrollRect_CalculateOffset_mEDC05AE7605F106C328D03D1AC18E0EB8B2C2972,
	ScrollRect_InternalCalculateOffset_m7A3CCD6C9A7AC67A91250F5FA4E91628F66AC4FE,
	ScrollRect_SetDirty_m5007A787609DA72D331A2A42B0D52D88EB5A01AD,
	ScrollRect_SetDirtyCaching_mBA4C1977ED1E5AFECA3CB21823DB915C981C3BC9,
	ScrollRect_UnityEngine_UI_ICanvasElement_get_transform_m3C7EE3376326BEC74AE45A6C091753B4149BFCF7,
	ScrollRectEvent__ctor_mF8413852D2A70FE11491D494D0909CA5BD0FD303,
	Scrollbar_get_handleRect_m45CB651CA840761D5D3DE137D419FE5A86A6C21D,
	Scrollbar_set_handleRect_m718E47F2C0F307303898AC0B0CF60F3F176688B6,
	Scrollbar_get_direction_m9EF74B3DDD6C4DD7265952A4D315F2BA0FDE312C,
	Scrollbar_set_direction_mA23D0E4790E46B12B892488D6D13503D031EF02B,
	Scrollbar__ctor_m80CCB2881577F6AE404B53F35969419ECE24010E,
	Scrollbar_get_value_mC925448739BB4DC891D49F600D370D808296BD07,
	Scrollbar_set_value_mEDFFDDF8153EA01B897198648DCFB1D1EA539197,
	Scrollbar_SetValueWithoutNotify_m288FB57076EAF069E6E231BAA85EC6347693D1E1,
	Scrollbar_get_size_m5D2EDDF92A6BA31ED642067D57E8B7174778E69B,
	Scrollbar_set_size_m93B8718BD965E7188796F588928D240730CE22E6,
	Scrollbar_get_numberOfSteps_mF257DEFB3A88F3ECDEE9402D88BFE60EB7232730,
	Scrollbar_set_numberOfSteps_mB5A632F3D5CCA75A392675F925A6C806C09A9386,
	Scrollbar_get_onValueChanged_m40245AB9ADE9B6EC0B03A77874E9CAE51090FB34,
	Scrollbar_set_onValueChanged_mB77AC16CFBCF4DC9C775FB884C09C5AC6740FAA4,
	Scrollbar_get_stepSize_m5278BCD7DC4B4B1ABBEE2E307E8E9E23DDADEF76,
	Scrollbar_Rebuild_mE717E5E4A86B86FCFC060B25D1BA3FBD9854564C,
	Scrollbar_LayoutComplete_m8C11EDDC3142224049EB6FBE3A5D20B5D211CB7C,
	Scrollbar_GraphicUpdateComplete_m750E9739FB98B6CE947367E81174DD0B69B7B70A,
	Scrollbar_OnEnable_m118E712E4424022A2D3445A0143EFF90E3E47A5C,
	Scrollbar_OnDisable_m4C27D3F7D3309333CCDE609BC4AA8C7390B4ED6A,
	Scrollbar_Update_m9CDEA1F7A37F03D54DFA56304A4320493ED772DE,
	Scrollbar_UpdateCachedReferences_m665A5D25A9563092CBB93AE789C21D5D3A02FDB9,
	Scrollbar_Set_m7648F8AF6180528CB0F764E8336619A3F5BDD6DD,
	Scrollbar_OnRectTransformDimensionsChange_m279735A03AD38298ADFB35679214B7694B11B60D,
	Scrollbar_get_axis_m98559873075F3EC100F759F77D85F125DF3EAD5F,
	Scrollbar_get_reverseValue_mE21B1C18892A9E7A7B977092CA78D190DE9D2038,
	Scrollbar_UpdateVisuals_mF8DB48A1E85987DB7527BDF32DB6D8875CD87109,
	Scrollbar_UpdateDrag_mB92938245162616F94C421E5AD88FA20E545170C,
	Scrollbar_DoUpdateDrag_mE9E8F4734D1019CA486C2C42625A993929A85589,
	Scrollbar_MayDrag_mFE3DD24CC14FF172474172E0B0C04FD08A812D17,
	Scrollbar_OnBeginDrag_m7E55F54116ED0E93C44F217F9605F7261CBA4201,
	Scrollbar_OnDrag_mC731CB61B1B2DED2D282D5796340906A8385167E,
	Scrollbar_OnPointerDown_m35D22C28672555C90395DE39CAECF90D8920CAB7,
	Scrollbar_ClickRepeat_mA194AE50BAE28AC44A0F5AD82C33B459C3BA8498,
	Scrollbar_ClickRepeat_m7F4E106BDED75AFD5041ED1EDBC71D0FCD00B1A9,
	Scrollbar_OnPointerUp_m8487F735AF1B04F04B1C5A5AF0093825B34806F1,
	Scrollbar_OnMove_mB38F3B76B3176D3C68862C4A89B8FC602CBDEF15,
	Scrollbar_FindSelectableOnLeft_m3C5EC37268434F881E19E5672EA618FFD003E542,
	Scrollbar_FindSelectableOnRight_m92A7E0729E7A8D574D7A4CAB6B5DC1AB877DB650,
	Scrollbar_FindSelectableOnUp_mC04DED87BF27E25DDCFBE2E856756306029610D7,
	Scrollbar_FindSelectableOnDown_m8A3AC1A349CDF57806A6052BFDA50F3E4923394E,
	Scrollbar_OnInitializePotentialDrag_m3F193C72AFED340A173112D9FEBCE657612E1E17,
	Scrollbar_SetDirection_mA2EC79BD55CF96E8FA6EF13C0507EDDBD3DB5AD1,
	Scrollbar_UnityEngine_UI_ICanvasElement_get_transform_mAA5CFBC4FA92695E11E09688C9A356E3F35D26C2,
	ScrollEvent__ctor_m44DC6D36587E09ACF8AC837A3251B4345559ACE8,
	U3CClickRepeatU3Ed__58__ctor_m8873755FA35DD3F2FD77BC0BBF4790DEDAD63E34,
	U3CClickRepeatU3Ed__58_System_IDisposable_Dispose_m53CB1C56045E24999148EA191B041CB6815839BA,
	U3CClickRepeatU3Ed__58_MoveNext_mB0F8287D6C01B3C1F27761E3736E533046D2C813,
	U3CClickRepeatU3Ed__58_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2D1D5B3A420C829CDCF5FF31E77ACBE2CF12C033,
	U3CClickRepeatU3Ed__58_System_Collections_IEnumerator_Reset_m91332A8DFD75C0F222997DD4D8573EA9AAC27F36,
	U3CClickRepeatU3Ed__58_System_Collections_IEnumerator_get_Current_m52060FC222F29953E4BC040BC88CCC64EF3C8492,
	Selectable_get_allSelectablesArray_m82DC9617ED58417072BA0E125DBDFCBFAA0E6626,
	Selectable_get_allSelectableCount_mC344C7D2980DDEC1F25FB2D38899936FFA82E431,
	Selectable_get_allSelectables_mD4895881690BC278354FA3EC9B10B4EE256C449A,
	Selectable_AllSelectablesNoAlloc_m48336092D38D0D80EC2EDBFD77AE43D86317911D,
	Selectable_get_navigation_m5E66BC477203E3245F9FCBE3EABE51A8003980C1,
	Selectable_set_navigation_mCFB3BDFA535CA2F42299049ACDF02F91E15B4542,
	Selectable_get_transition_m9F5ADA0325416768CDAEEF85478C9BDE4AB24ACC,
	Selectable_set_transition_m394926F279C7B5054C3D75AAA99788C48BB50343,
	Selectable_get_colors_m47C712DD0CFA000DAACD750853E81E981C90B7D9,
	Selectable_set_colors_m2610F85E7DC191E0AA2D71E2447BA5B58B7C4621,
	Selectable_get_spriteState_m3599C4C35FCA7427F27D7C6CAD8DCF31476718E6,
	Selectable_set_spriteState_mD0DB578B3D6404A8D5B38865CB5FCF812D881C87,
	Selectable_get_animationTriggers_m26FB70A2F465548564190F6991B6F427628C2573,
	Selectable_set_animationTriggers_m6306E54670720009966BCFA605F86236DCAF06CE,
	Selectable_get_targetGraphic_mF2D1BFB9DE33BAB553933533CC4A9135A37F4A72,
	Selectable_set_targetGraphic_m668325D43E18A35560B4176477D004ECDB9C9604,
	Selectable_get_interactable_m4231515CC3C861B431AA22FE3345F831389B3840,
	Selectable_set_interactable_mE6F57D33A9E0484377174D0F490C4372BF7F0D40,
	Selectable_get_isPointerInside_m16EF4963CD1916567154D40FA6CDFB3C05A18785,
	Selectable_set_isPointerInside_m235566B1D1AC3989033566483BA78B3B6D65A080,
	Selectable_get_isPointerDown_m15EE53CB56493F9C65339EF06DBBFE35CF923526,
	Selectable_set_isPointerDown_m8B64367E49F20925A0D7755CE456CA5E91E84BB5,
	Selectable_get_hasSelection_m6D96C84C22E2B7B2E5044FFE7ED7F96B3C871235,
	Selectable_set_hasSelection_mCDC1198B44CED5A1B1A02881BC5E53FF574A19F3,
	Selectable__ctor_m71A423A365D0031DECFDAA82E5AC47BA4746834D,
	Selectable_get_image_mAB45C107C7C858ECBEFFFF540B8C69746BB6C6FE,
	Selectable_set_image_m3D9DB0594D31F38D71AE41A9CA9FD494607E0E6B,
	Selectable_get_animator_m64B7187172AE08620AFB3C616450AA63DA1C8AC3,
	Selectable_Awake_mDC49DAA01529547D77A25CD8ABC91D0E38D2E881,
	Selectable_OnCanvasGroupChanged_m412845BE2E955ECC1B34C657EF72A654FD1C7AD2,
	Selectable_ParentGroupAllowsInteraction_m5C2EEEF999B684CE9C12430AB0B0152294D0C082,
	Selectable_IsInteractable_m166B7B80827A2AF092DFAE6C95DC7B45ADB57D9F,
	Selectable_OnDidApplyAnimationProperties_mF971F5679B02796A1626742C7D4D66DFE6A9A122,
	Selectable_OnEnable_m16A76B731BE2E80E08B910F30F060608659B11B6,
	Selectable_OnTransformParentChanged_m6FFE375C64847CC5182DAD896EFB600F325F8104,
	Selectable_OnSetProperty_m0913D82035D767DDAF37F34AF4F82F395AC8663C,
	Selectable_OnDisable_m490A86E00A2060B312E8168C29BD26E9BED3F9D5,
	Selectable_get_currentSelectionState_m2F4651DC6AA8CD09F3395F178523D937DFDFCD2E,
	Selectable_InstantClearState_mDCA630365FB86AD677F7A6E34ADD3B46A7E83D8C,
	Selectable_DoStateTransition_mEFD5B374017C75F4FE9F9CC983ADC73E5A13B41E,
	Selectable_FindSelectable_mA6D4C4B4AB595BB51BEAB0DF7A4308FF71D40FD5,
	Selectable_GetPointOnRectEdge_mF3EF4B71604DB85D1093F2F0B7F1588AECFF8B80,
	Selectable_Navigate_mD1DC810617EEF6412FB9DF1E1AA1A9CBA659FF73,
	Selectable_FindSelectableOnLeft_mB42E50642047189B486186AC74F0D8FCC4E06240,
	Selectable_FindSelectableOnRight_mD2EC5BC567595EDDD7316609F2C23FF5FF8F22C0,
	Selectable_FindSelectableOnUp_mA6F1D56B00532781BA9FB379E8E33F7B249C029B,
	Selectable_FindSelectableOnDown_m548BF9DB6F72D1B836691C681232E9E4BAFD67AA,
	Selectable_OnMove_m309528EE263D12F664BF2572A0FFD2AB2A12BD24,
	Selectable_StartColorTween_mC0E53DA70ACFED66DA8E049922F22C4B54C2A4D1,
	Selectable_DoSpriteSwap_mF4A2E7DEECC2A0E68A86AE207E0A8679566341B6,
	Selectable_TriggerAnimation_m7DA58CA3E8C1A946C0926B461F9FB668498A5624,
	Selectable_IsHighlighted_m5E76C4938BF5CCF299CF7F2A4D67FFDB7C5303A9,
	Selectable_IsPressed_mCEF0D241284AE90C08B093CA0ADFA2A485007746,
	Selectable_EvaluateAndTransitionToSelectionState_mEA0BA58511B6D5755BADB7E9CEAEF42491AEB920,
	Selectable_OnPointerDown_mECD8313A4900B647F476CCF596DCF9C92B32F2AA,
	Selectable_OnPointerUp_mCC5A82EF3E566BE6720348BA5913A8461D7B3ECD,
	Selectable_OnPointerEnter_mECC5B6BB8CCE06CF95FA837E73F0C83F46160542,
	Selectable_OnPointerExit_m5A0E6FE0E9BAC0DA39F812B7A390FBEC1CACBFE7,
	Selectable_OnSelect_m6FD18DBF52BD234A676B3BF43DA2A3CD7FC63230,
	Selectable_OnDeselect_m2E0FC2E14667F2348242BB9FB1611BE9A79C2FDA,
	Selectable_Select_m8C877D0983976C5CE6A70B2219679B1F42B0971E,
	Selectable__cctor_m0A91753D744D8E5A286F83E8383D58F169ACA3A2,
	SetPropertyUtility_SetColor_mAB8BF8D69240EE5931FB5A53C2434867C48A6616,
	NULL,
	NULL,
	Slider_get_fillRect_m4D02B70BFAA5C003B34E8132C10CB80A0F022CAA,
	Slider_set_fillRect_mDD7F4FD79D92C9ED5D005612E4BF083D56560DCD,
	Slider_get_handleRect_mE1B614E89D5DFE5CED40D3F0BF4BE30871BC81D0,
	Slider_set_handleRect_m51044F33CE9CD375EC7FB1D41B420FA7B44D6086,
	Slider_get_direction_m599FD5897AEAD92856C45A6905F9AF62705746D5,
	Slider_set_direction_m1D8BE0408B11A471327AD2CC5B1DB0169315DC7F,
	Slider_get_minValue_m7B5A89FDE9916A4A111BDB91648750E23C034B08,
	Slider_set_minValue_m253C0E27C8B0275EDAAFD9E97F6DC1E426460F93,
	Slider_get_maxValue_m369FF59A4AEC91348D79BF1906F4012A2A850959,
	Slider_set_maxValue_m5CDA3D451B68CF2D3FCFF43D1738D1DCC1C6425B,
	Slider_get_wholeNumbers_m1D891AB6E780B340CA0EA364C7DF7425186930F6,
	Slider_set_wholeNumbers_mF278D1746B80BB0CAEC84362D060D12CB2DB0134,
	Slider_get_value_m787B367CC0F4BD00041FD80F297014DDA4C2D846,
	Slider_set_value_mC53042FE25A4FD98D41FDB23D9DDC0BFE935BB80,
	Slider_SetValueWithoutNotify_mDA8676600809F9207AC51FAADA2D757BA5FA35BE,
	Slider_get_normalizedValue_m09A06767F3E8064200CA1C954AF5C362C5138EC3,
	Slider_set_normalizedValue_m33A334123C4869919B6CF52711B4938F82AE2D41,
	Slider_get_onValueChanged_m7F480C569A6D668952BE1436691850D13825E129,
	Slider_set_onValueChanged_mBB24A666AC4F2DA637732CD77A41C014040782EE,
	Slider_get_stepSize_m4C4B9C8E3DD4989847E9770B0EAD66069DFB5885,
	Slider__ctor_mFC18E800E96F11533382DCECDE8D2F160DA19947,
	Slider_Rebuild_m35C747EFB7CC6D45BE2BB7DA8416FAE449BC4271,
	Slider_LayoutComplete_m6E92D0C7DF2A6B1F2F63BCEE44472071ABC78651,
	Slider_GraphicUpdateComplete_m7103B31E0991F4883DEF776797B3B839F3DDA827,
	Slider_OnEnable_m11539744EC2B374C6B1AABE7F2909457DD956022,
	Slider_OnDisable_mAEBBE8222ABEE88BFEF4A2209CA61CCD39BDCFFA,
	Slider_Update_m149A2FFECFAE8AA377777BCA848B0CAB0CB06694,
	Slider_OnDidApplyAnimationProperties_m8AA149F0F3E09E93F124971B5A1C94F8DB9970FC,
	Slider_UpdateCachedReferences_m07895017E8F07A1F6129E769D1F6A43FEF453BDC,
	Slider_ClampValue_m21176A1E21326F10E784782303DDBEE004FB6435,
	Slider_Set_m706D4F83CA9A006F1AC1ABB1AE8E165DACB8FA5F,
	Slider_OnRectTransformDimensionsChange_mB7578390513E3FB21BA08079225CE4CF751D640F,
	Slider_get_axis_mE52AD1AC11A9A6B0930E2A1C055F94234AD4BEFA,
	Slider_get_reverseValue_mD1BF64576A93FEC0E1394B5DAFAD3F05F7D29205,
	Slider_UpdateVisuals_m6985F921D2E0C14800D51257ABEA5784736243E7,
	Slider_UpdateDrag_m7E812610D0F98C7CC8CD45A7C2774B93010C9143,
	Slider_MayDrag_m81F9CDAF63CC4CB6661BE3C6D669F222C3DC105E,
	Slider_OnPointerDown_mD311547B8584197150D3B19317E8FD4CF74C5C69,
	Slider_OnDrag_mE7E7AD8A03992B99DAF76EF3A5F3DC114A917F3F,
	Slider_OnMove_mAD1581FCC361E0A2BB300C001B45015FCE53F911,
	Slider_FindSelectableOnLeft_mF26C5F8012D02EF3418D5B6078A40D611C331828,
	Slider_FindSelectableOnRight_m5F2861D5679DA3CD1CCF6263DF8FAEC1368325AD,
	Slider_FindSelectableOnUp_mFD9FF36CD4AADF0062D3C8DE37DDC3A95BB896FA,
	Slider_FindSelectableOnDown_mFF49AB2F8816150590F280859E07A78D85E1ADBF,
	Slider_OnInitializePotentialDrag_m13553E8E4A0FDD955FE69E888CDAE144EEA16F09,
	Slider_SetDirection_m7A4187E11D4F9F3701253C2EDCA3B58769DDC006,
	Slider_UnityEngine_UI_ICanvasElement_get_transform_m10BC38EEE32C2A84D963626863CD81C6DC011C37,
	SliderEvent__ctor_m9D53B3806FC27FCFEB6B8EE6CF86FD7257DC0E6F,
	SpriteState_get_highlightedSprite_m695FD2C0827908CBAFFF5D5033FEED380D4219FA,
	SpriteState_set_highlightedSprite_m3B5F7EF5AF584C6917BA3FB7155701F697B6070D,
	SpriteState_get_pressedSprite_mDCEB9F07BDD7C2CFCDC7F7680D05B47EA71965D6,
	SpriteState_set_pressedSprite_m21C5C37D35A794F750D6D4A95F794633B9027602,
	SpriteState_get_selectedSprite_mA85714CC6BF3801A63CC42B026E66CEDFD36949E,
	SpriteState_set_selectedSprite_m00EC0C38B3ADBA12D9524CAE982BE8B21F608A54,
	SpriteState_get_disabledSprite_m7AF976C63DA03ED035B031D5A98413C39894F50C,
	SpriteState_set_disabledSprite_mB368418E0E6ED9F220570BC9F066C6B6BF227B13,
	SpriteState_Equals_m2190A8BFFC45EC86766FC68C808F3DFE18E35827,
	StencilMaterial_Add_m6C73C30371938A7BF27E5D1585FF2CBFF8110D94,
	StencilMaterial_Add_mD22425AE22272F987211266F6A96AB28F4B96CE3,
	StencilMaterial_LogWarningWhenNotInBatchmode_m4FEF061F9A2DD6EC8535E354E891C2C7D7B3C0EB,
	StencilMaterial_Add_mC403BBB08D41E5B06FC6BAAA925D1EB944E8FA88,
	StencilMaterial_Remove_m74F6B3C15F50B2873959CF6B9D58801AEF4EA964,
	StencilMaterial_ClearAll_m9F8AA57D8DFB4F1B24D7AF781CC1C9645A37A8C8,
	StencilMaterial__cctor_mA9660B29DCE5AABDD4F2DF6119672AB7593895F9,
	MatEntry__ctor_mE5E902719906D17EAC17E5861CD3A6BB91B913A0,
	Text__ctor_mB8DEA3FF32B10CF70732F6F91D4B144506689FE7,
	Text_get_cachedTextGenerator_mC1CA3F78904E1B2E5759DEA6EFDB1C13AB3BBB65,
	Text_get_cachedTextGeneratorForLayout_m464140899A674C970F9BBAD836EDDC1AD74DFF66,
	Text_get_mainTexture_m3B1A372943D77082ED6C76201D5EB90AFC100991,
	Text_FontTextureChanged_mF4E2911B04AD15B2C482113179EB78653D6C33DF,
	Text_get_font_m8D2D6709C3C35D54331B6DB56F2CBBC929FFA86C,
	Text_set_font_m10F529719C942343F7B963D28480A20940CD0B52,
	Text_get_text_mA9A5B551F443FE797B101B12A8F109CBFE82251A,
	Text_set_text_mD2EA82E603CB39FCAE5BDFAE21E5D747307E4239,
	Text_get_supportRichText_mE259102B63D4404BA6C997BF184546FEADBECE89,
	Text_set_supportRichText_m62254112F808CB0F15355E81FEA2C8174FC6D66D,
	Text_get_resizeTextForBestFit_m3569E556E4152E54CF17021CCE84F31911EBCCD1,
	Text_set_resizeTextForBestFit_m0FFB3B0A6531A963B2A55122CF2903AE87CAEBE7,
	Text_get_resizeTextMinSize_mBCEDC46AF1B0F0BF2F282B6EB1A35AA86DEC5D39,
	Text_set_resizeTextMinSize_m78D4AF5080EFFB22D06943643D1F6771FAE617D6,
	Text_get_resizeTextMaxSize_m7F3E89688C621994454CD6507FBD711EFF630C8B,
	Text_set_resizeTextMaxSize_m94CD82876E80E3391B1FD5FF5140ACAACA2C5CF2,
	Text_get_alignment_m815A072002DEFDA14F8D523DE96403F3D70B2BBA,
	Text_set_alignment_mBA9424D5CCC6FB11861B67A40E0C0F6DDBFDAB2C,
	Text_get_alignByGeometry_m822AE54A8A9E711E634E9EE0DD5D23B410AFC6D6,
	Text_set_alignByGeometry_m6AF8EDE5E32FC1126F3C7C8D3D794C0EDDBC52E7,
	Text_get_fontSize_m63951F82E2028B2AAFCB4FEF0C4E6464370AE72A,
	Text_set_fontSize_m0D32489043916BCE64E51E0BDFCC12AC1B829411,
	Text_get_horizontalOverflow_m8613B65EAC12945ADC0CA56C742198DB59FB5BBE,
	Text_set_horizontalOverflow_m2D8B7DD9E784AE082C388FE483CFDB296950F60B,
	Text_get_verticalOverflow_mC80112C002317C58616F6CBC2489A113A2E3ECA8,
	Text_set_verticalOverflow_mBB9FA4C8CA6236354168B93AB6666BEB4D82D0BD,
	Text_get_lineSpacing_mA977B63BADFC5E82528B7B010E450B57F3BBCD09,
	Text_set_lineSpacing_mBEFCEE561D8E0827A7E0CD65BBAF9CD6A1C944F1,
	Text_get_fontStyle_m5068017317D8AE127A308676FD1AD98E6CCFE4A7,
	Text_set_fontStyle_mA7869224CE4806D14EC614E68DB3873C6C2D54DD,
	Text_get_pixelsPerUnit_mE181D725EA8DB4E273C725DFC9C9AA9712C8804A,
	Text_OnEnable_m868042773CA7FE4508671FB715140B0FCAAD79F3,
	Text_OnDisable_m678E9C3DB6A7DBDA6F55D454627C91F99B173A4E,
	Text_UpdateGeometry_m8CC00C6CE511C1CBD96E138025788A6DFDBFC9BC,
	Text_AssignDefaultFont_m480C1C4CC4F14095138E0E82EDBDD434D1545B0F,
	Text_GetGenerationSettings_m7ADF67C21E79A53624FCF42CE828C9BF57FA98CE,
	Text_GetTextAnchorPivot_mF42A2C864A8909F356D4C51819F12FEB9C57ED20,
	Text_OnPopulateMesh_m4E8D3BD94E7F21D6D0887B0A0BC8F40389A1C778,
	Text_CalculateLayoutInputHorizontal_mB2B8BAA95A0D8A825CB20C7A919EE9D857580139,
	Text_CalculateLayoutInputVertical_mEF4CCC05582EC841C0CB0C0F786213E78C64B13B,
	Text_get_minWidth_m31CB8C5C847BF46105FE2E6186AB801446D9FB9D,
	Text_get_preferredWidth_m7FEDB1F56EC6BC313DE2F8CBB443CEA29CCB3E8C,
	Text_get_flexibleWidth_mB59646E08036BC4316208E3911F29A46A8BD2322,
	Text_get_minHeight_m092B0806C09C26E338CCD04670E3CD1356789016,
	Text_get_preferredHeight_mD0CDFAE12ADBF007F24A5B895CB2ADE526219AC8,
	Text_get_flexibleHeight_mFF13C6F1C12057AE3757E99A2449E5F13EE6966A,
	Text_get_layoutPriority_mBB3F6A8BB6C56D9EEFA85D69F84A5F52867FE158,
	Text__cctor_m5F39DABD01825657CD9AF1D6D6BDE5C9C06F9D56,
	Toggle_get_group_m32DE73FB5899A95C8BC31B377F31F8D5167D2BE2,
	Toggle_set_group_mDE3F57C5F225B7A7856F40A7AB6CA1A22C0C2B23,
	Toggle__ctor_m73EB2B8A5201BDE4789E0317CF7F8D66A22F392E,
	Toggle_Rebuild_m03534F97F2ED9B61AF2E01F07B13A59B425DDA11,
	Toggle_LayoutComplete_m1E1D7A8F53C7AE28B65D6F7CFF406D093D875A66,
	Toggle_GraphicUpdateComplete_m68CA8BA30F7C56559E8CBEAFA28EB2B25F9E9EB6,
	Toggle_OnDestroy_m2E15215B509E798734CDFFECA1146A749ADC9A0F,
	Toggle_OnEnable_m88F408A8D38B70537BAEF7919CAF8AC33F32BB53,
	Toggle_OnDisable_m01F709F5D7780EB81C27B55DE74674DF3B4322DC,
	Toggle_OnDidApplyAnimationProperties_m0662478457D843C95CC4689CF0C46E1FF2052B3D,
	Toggle_SetToggleGroup_m50058F84A8AD3CF060D50147D7DF0FD9DA8FDD12,
	Toggle_get_isOn_m2B1F3640101A6FCDA6B5AF27924FFD10E3A89A6C,
	Toggle_set_isOn_mB018B9F410D7236AAB71D6D1A5BACC64C891F507,
	Toggle_SetIsOnWithoutNotify_mD07469424A970A7894F38F2AE3A84CC465AE7952,
	Toggle_Set_mDFEF33CCBD142D223B80FEBA43C75DD3A0ECA312,
	Toggle_PlayEffect_m60130B573D4FA4821127FFAFB1D1822315D5ACAA,
	Toggle_Start_mA6FA457EBD527A089B6B195C134C971F94918813,
	Toggle_InternalToggle_m3C04FA487B0F311CD814F7C6796D1F8EEBF9A594,
	Toggle_OnPointerClick_m917A59AE7AE323514F157EB7FF38BE346D1EC0EA,
	Toggle_OnSubmit_mD0E022F5E0799162461A46EF25BE058B47C14EDC,
	Toggle_UnityEngine_UI_ICanvasElement_get_transform_m824C6DB82B23058726C8ACE7F39AF72DE9125FBA,
	ToggleEvent__ctor_m8B27AC4348B70FDEF171E184CE39A0B40CD07022,
	ToggleGroup_get_allowSwitchOff_m970C9B6CFCC408D8146B2D4100780E6BECC080F0,
	ToggleGroup_set_allowSwitchOff_mFA7B1BA141BA27AF0B25FDACA84DCE31544828FC,
	ToggleGroup__ctor_mEB26AD500D667CC983BEB0E7F34B13145480A395,
	ToggleGroup_Start_mD457A294157374B7A5F913F0904D40E9C4A1819D,
	ToggleGroup_OnEnable_m8B12EB3F11AE1A3600B0C663486D005DABB1233F,
	ToggleGroup_ValidateToggleIsInGroup_mE666CF7D1CF799910B808A81855D087F9E44E93D,
	ToggleGroup_NotifyToggleOn_m4B1E6B18DFFFB672B2227C4DCAB68A26440FA33F,
	ToggleGroup_UnregisterToggle_m1903602F193762B2E5264642D7C09B2A91B52685,
	ToggleGroup_RegisterToggle_m7E87D7943C6D2CCBE0B792326F69AA18A726848C,
	ToggleGroup_EnsureValidState_m8995EE9A121B4ED71723E21A317B6264C08E03FE,
	ToggleGroup_AnyTogglesOn_mA6EB9869F012D763BF7150EC335DFF548A02837D,
	ToggleGroup_ActiveToggles_m4CF8A6DBB4637A10A5CDB852B42C4C4FBCFC3C00,
	ToggleGroup_GetFirstActiveToggle_mB4938A5F6C3AB10118C16C4F09B02E0EE1AD223A,
	ToggleGroup_SetAllTogglesOff_mCE1A2D61E940E3AE772367D181CD3221F4529090,
	U3CU3Ec__cctor_mF08B3226ECAD2C55446D4A1276EA0B33B8D27DB2,
	U3CU3Ec__ctor_mE529EC087B06A93509276E7E9CA68D3E3E6CC257,
	U3CU3Ec_U3CAnyTogglesOnU3Eb__13_0_m6B58E5D7E10F6C3A857BF297744D758E5D81B6B4,
	U3CU3Ec_U3CActiveTogglesU3Eb__14_0_m8A396237A2696D3A2068BE32BCB869F70904C9AD,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ReflectionMethodsCache__ctor_m9B1FD42D9BA69B5E82C794F29E0D76CD8B50A407,
	ReflectionMethodsCache_get_Singleton_m979239967F039C94A7E7ABABEBEDC43D461E9AD7,
	ReflectionMethodsCache__cctor_m1A8A28C0A1386C1B9B64179B0BB6727C2A209347,
	Raycast3DCallback__ctor_mAAD87D0BBDD341D9798A282988F3E29209F367EC,
	Raycast3DCallback_Invoke_mC1E4D81B6EF4118CAB6ED4ECEA3312AFB1E81744,
	Raycast3DCallback_BeginInvoke_mD96B941801A9E258E0E5F32AD7E37E99532A7DC7,
	Raycast3DCallback_EndInvoke_m45E04D8E5C1288FA778A3364599532EFB4834AC5,
	RaycastAllCallback__ctor_m219974A3E0AE674C4843A1638B9B97E967D942B6,
	RaycastAllCallback_Invoke_m4A107AB96A1A28BD319A50AFBFD401A791E8DD26,
	RaycastAllCallback_BeginInvoke_m4343AD48520C9F524B3206CE11A9D7F1BF7265C6,
	RaycastAllCallback_EndInvoke_mCD8F79E5C1A4870FAEEED315823042FD990D30D5,
	GetRaycastNonAllocCallback__ctor_m5EF88C8923C7A175F825682E242A0A21F7F9CDC1,
	GetRaycastNonAllocCallback_Invoke_m9F06CE1FAAC409FACB2138A85B6E69E6A38099E5,
	GetRaycastNonAllocCallback_BeginInvoke_m7AC50FE945DA1CE964D3C6BB7AD97DEB4440B32C,
	GetRaycastNonAllocCallback_EndInvoke_m8F4495A70A254C556EFC0730A30AC71E4BD4FB0B,
	Raycast2DCallback__ctor_mBE2DD6E8DBBE5E92D144CF9E21D3F7B446A84DF0,
	Raycast2DCallback_Invoke_mD30E994123A65522A82EF29EBAA5A75ED1A25097,
	Raycast2DCallback_BeginInvoke_m5A041AB9DB48B5D5C0CA2169D51BCC12200C319C,
	Raycast2DCallback_EndInvoke_m93B7E9EAD357455F829161F77D937B90361F648A,
	GetRayIntersectionAllCallback__ctor_m3FB3501090DF1536469BE66442205BC29D4D2725,
	GetRayIntersectionAllCallback_Invoke_mC413E2F7F934A21FCF43D6FC99DB7A16A85427FC,
	GetRayIntersectionAllCallback_BeginInvoke_m28BC711B1ADD07E7E1CFE655540570F1CB7CA8A5,
	GetRayIntersectionAllCallback_EndInvoke_m2CD8143576A1A90F130FB217CAE90D8E3701E2EF,
	GetRayIntersectionAllNonAllocCallback__ctor_mBC32BC06655600F7870640A99A1D0A4970FD0403,
	GetRayIntersectionAllNonAllocCallback_Invoke_m3A03F6D1E31D55B967B4688FE35EA70C618B1BC9,
	GetRayIntersectionAllNonAllocCallback_BeginInvoke_m11D1A4ADD9C26978DE03AB28C0A419E11D2A0569,
	GetRayIntersectionAllNonAllocCallback_EndInvoke_mD095B5BFC09387E0AE997CF77DE71D138E97C8C3,
	VertexHelper__ctor_m66DE6882DBEBE377C3E672DD0E9DEB88694069B6,
	VertexHelper__ctor_m393544BA4187E8E1FF628CC7FD3755AEB5EA76EE,
	VertexHelper_InitializeListIfRequired_m6CCC5B58B5B1EC87F651B36220440A58B38728CF,
	VertexHelper_Dispose_m1F4448E484FD377DDA18AE871DE116EEBE39A5FB,
	VertexHelper_Clear_mBF3FB3CEA5153F8F72C74FFD6006A7AFF62C18BA,
	VertexHelper_get_currentVertCount_m4E9932F9BBCC9CB9636B3415A03454D6B7A92807,
	VertexHelper_get_currentIndexCount_mBE8966E80B9260A6A8FF56FA7881E027E25702D8,
	VertexHelper_PopulateUIVertex_m540F0A80C1A55C7444259CEE118CAC61F198B555,
	VertexHelper_SetUIVertex_mE6E1BF09DA31C90FA922B6F96123D7C363A71D7E,
	VertexHelper_FillMesh_m69ADAB814A243F7F5578BC07086F373B85A34269,
	VertexHelper_AddVert_m0988345B2D2BCC66B875E9F07B99E12C68C4590C,
	VertexHelper_AddVert_m3428A0D5A377CBF2191350B793299EF1EC3503B1,
	VertexHelper_AddVert_m5CD02FDA1B6ADBD0E276037F948B68E08497D1F2,
	VertexHelper_AddVert_m7A43A65F746413AF697EBD1D0A8EA87A0A7ED032,
	VertexHelper_AddTriangle_m1EE93E4BF27E3BCCE69A348358FAF605105B63C6,
	VertexHelper_AddUIVertexQuad_m16C46AF7CE9A2D9E1AE47A4B9799081A707C47B5,
	VertexHelper_AddUIVertexStream_m979FD37B1176E5B5A217065C04B64EDB568DC85B,
	VertexHelper_AddUIVertexTriangleStream_m3FC7DF3D1DA3F0D40025258E3B8FF5830EE7CE55,
	VertexHelper_GetUIVertexStream_mA3E62A7B45BFFFC73D72BC7B8BFAD5388F8578BA,
	VertexHelper__cctor_m3DDE70E5F715BA724FEAFD8BD9A93385D2E44878,
	NULL,
	BaseVertexEffect__ctor_mD0598FFC9739945F3DE04BF263F33934BF9F38AC,
	BaseMeshEffect_get_graphic_m4FAFDA7300251A13F7DDE689145C54E8B971688D,
	BaseMeshEffect_OnEnable_mB23ADD5744D6132E260E3E8D40EC9FC8A1CB69E7,
	BaseMeshEffect_OnDisable_mD74FC12529C7975B8214235CB629BAD3702C7A1F,
	BaseMeshEffect_OnDidApplyAnimationProperties_m8624B9A46D7F47EE2DA080F4624C5CCAAED6A450,
	BaseMeshEffect_ModifyMesh_mDC045F4A3A571E76731F7773E7164E7D0E1143F3,
	NULL,
	BaseMeshEffect__ctor_m7D21D47A3B87CB9B715FCEEE1B955E417FEEF01B,
	NULL,
	NULL,
	NULL,
	Outline__ctor_m6143991EB8225AB600E3D212CD057A43DAC8A7DB,
	Outline_ModifyMesh_m21784D49980211B1AA3E6A6E6A2E2EEF03DBDC54,
	PositionAsUV1__ctor_mA7764A6F1AC48964112A831DCE71C75D36864E0A,
	PositionAsUV1_ModifyMesh_m23B103D2A5CA62A4FEB450B6BE15A6DAFEA939A3,
	Shadow__ctor_mC023CEF78072A0E21FCBB5EDE94582DE042A2C54,
	Shadow_get_effectColor_m00C1776542129598C244BB469E7128D60F6BCAC2,
	Shadow_set_effectColor_mFB6601937B8DCBB52A6095435A380C4AE0A807DF,
	Shadow_get_effectDistance_mD0C417FD305D3F674FB111F38B41C9B94808E7C0,
	Shadow_set_effectDistance_m5C9FAC6D8D46E952FF29D00852E790E6A3BF2E09,
	Shadow_get_useGraphicAlpha_mF5EAD2754C90C2C0BAA50786C514E6A1D834B6F0,
	Shadow_set_useGraphicAlpha_m819149C2D4E0B64D112B8BA8FF4D4E1383C5CF6A,
	Shadow_ApplyShadowZeroAlloc_m31E0AC08A226594BF2CB47E9B19CF5C816C1499F,
	Shadow_ApplyShadow_mB51E2C37515B2DB9D0242AE30FD16EB1AE36EF86,
	Shadow_ModifyMesh_mF44456F48248AF8EBA900E0E96C2A69F8DE655DD,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ColorTween_get_startColor_m17A0327111EF4CEB0C15009D70607065D7B2DD0C,
	ColorTween_set_startColor_m3FDDCF9C585451522E3DC19AC452B6020931418D,
	ColorTween_get_targetColor_m7FBFE43D0C87958C7F82CE25627ED2A6C1E04EBC,
	ColorTween_set_targetColor_m0172FC5EED7BEDA58CC0BF38D15484A9E621C9B2,
	ColorTween_get_tweenMode_mE64B5F00617D95B356E317C0DD27F4E047872840,
	ColorTween_set_tweenMode_m3EB2248A06FAC8B31730D2C0A7019E83FF3BFA54,
	ColorTween_get_duration_m0D54F8FF864F645287570B01A7263FDA284A3091,
	ColorTween_set_duration_mB1EF6D6212E52CE120C294C354188F55A3AAAE56,
	ColorTween_get_ignoreTimeScale_mB626FF1519AD4745DA9C6F1ACFB06665F7775740,
	ColorTween_set_ignoreTimeScale_mB859C6558AAA257A7C25E98E91A4C1E1B1F3FADC,
	ColorTween_TweenValue_m5F8B59F75D4CE627BC5F6E34A1345D41941FDCC6,
	ColorTween_AddOnChangedCallback_mABFB32C3DFECB9271E2CECA5FE79567F82D56DCF,
	ColorTween_GetIgnoreTimescale_mF956E0A7BCE90295B6341EB17641A36A9777B8FB,
	ColorTween_GetDuration_mDF63029E52D00CCFE62AD905D392D54523D3CA76,
	ColorTween_ValidTarget_m1A0F15E461C7359650F9B26BD2CE50EFCA2EA80A,
	ColorTweenCallback__ctor_m657B9693A9F289FE37673F95D76A89BCF250FAA9,
	FloatTween_get_startValue_m50E23F438012DBCDB77F79083F8B3E7C747ECEB3,
	FloatTween_set_startValue_mFB659473A8FB57C4D0F951DC53D9364ABF5435A5,
	FloatTween_get_targetValue_mAA60AB9B567184EB6DB1C960F2AED3B934FF5DFE,
	FloatTween_set_targetValue_m4DD565573FA7B4110EEDA4D524D369858571A0A2,
	FloatTween_get_duration_mD6CD9915EB7F798B9F07F20E096066B201CCFF73,
	FloatTween_set_duration_mB561C369977BAE6807268EAA9633B31F530151E9,
	FloatTween_get_ignoreTimeScale_m40168239ACB9A32B1A87EAF382CBA87FB641743D,
	FloatTween_set_ignoreTimeScale_mACD465EA2E70BAFBA3FD3FC4FECC4393E17E0786,
	FloatTween_TweenValue_mF21AE3A616B020B1D351E237D1F3145B508ACB11,
	FloatTween_AddOnChangedCallback_m07B7D701CF26857FE4E33DE9467716065381EBEE,
	FloatTween_GetIgnoreTimescale_m39FBA712B845E9E33B886502D2DB92A499D2F6D8,
	FloatTween_GetDuration_m1D2C252520C4713F105A75162AFAA1A7785C28BE,
	FloatTween_ValidTarget_mE1A5EA20038733B467C8B810E63BE6617F7DAAFF,
	FloatTweenCallback__ctor_m5BD825A7E915816E2F70423A9567C4D82959C309,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AxisEventData_get_moveVector_mC99249AB7977DA35D4BDCEA4B13BAF08986F6BDA,
	AxisEventData_set_moveVector_mE7D38A878DCC73C1E829C30A4AA6A1B2DE6DC0A7,
	AxisEventData_get_moveDir_mEE3B3409B871B022C83343228C554D4CBA4FDB7C,
	AxisEventData_set_moveDir_m2232A7B0D06742A92628CFD04478D42FFABCB3FA,
	AxisEventData__ctor_mC155A7CC3943E35A99D1F011DCEE37914835052B,
	AbstractEventData_Reset_m3E1463A5F5CBDC06D22019C342CF8EB560C18245,
	AbstractEventData_Use_m44D8C11EDB60410F54840BF720E04FDB95B0F5E3,
	AbstractEventData_get_used_m9A7B684ED21FE129F0A72AA87CCE980F0C18122A,
	AbstractEventData__ctor_m99EC216AC354D3644883C2FFC95A54217C628A3F,
	BaseEventData__ctor_m51BEBFA3BB83410335A06787E273E954CAFD4B84,
	BaseEventData_get_currentInputModule_m76CF69CF6C3042B20D3255C73CAEA013D992B7FF,
	BaseEventData_get_selectedObject_mFBA205D5EE9CCC6DAD5D606EC6DE23704669B093,
	BaseEventData_set_selectedObject_m828F8FC80B817712F0B77C82D5DFC8066A23B064,
	PointerEventData_get_pointerEnter_m6F16C8962F195BB6ED58150986AEF584E4B979CB,
	PointerEventData_set_pointerEnter_mA547F8B280EA1AE5DE27EB5FF14AC3CF156A86D1,
	PointerEventData_get_lastPress_m362C5876B8C9F50BACC27D9026DB3709D6950C0B,
	PointerEventData_set_lastPress_m6324AB7BF9620A37B34A2D0DF62ED2CCC97DDC22,
	PointerEventData_get_rawPointerPress_m0C23DB50BCE28ECC43609CC01E727CCA77FC6473,
	PointerEventData_set_rawPointerPress_m0BEEB9CA5E44F570C2C0803553BA9736F4DF58F0,
	PointerEventData_get_pointerDrag_m5FD1D758CA629D9EBB8BDA3207132BC9BAB91ACE,
	PointerEventData_set_pointerDrag_m2E9F059EC1CDF71E0A097A0D3CCBA564E0C463C2,
	PointerEventData_get_pointerClick_mBB8D52B230FF80A2ABCEA6B7C8E04AF5D6330F3F,
	PointerEventData_set_pointerClick_mDF51451241642D1771C8C6CF8598CD76CFF43A4E,
	PointerEventData_get_pointerCurrentRaycast_m8F200C53C20879FC2A2EECFDDFA9B453E63964B3,
	PointerEventData_set_pointerCurrentRaycast_m16C21C39E7763E52ABE4485B4171FC4DF2A2A6BC,
	PointerEventData_get_pointerPressRaycast_m3C5785CD2C31F91C91D6F1084D2EAC31BED56ACB,
	PointerEventData_set_pointerPressRaycast_mAF28B12216468A02DACA9900B0A57FA1BF3B94F4,
	PointerEventData_get_eligibleForClick_mEE3ADEFAD3CF5BCBBAC695A1974870E9F3781AA7,
	PointerEventData_set_eligibleForClick_m5CFAF671C2B33AF8E9153FA4826D93B9308C4C07,
	PointerEventData_get_pointerId_m50BE6AA34EE21DA6BE7AF07AAC9115CAB6B0636A,
	PointerEventData_set_pointerId_m7CD30C18A42AD6A99B435816B58F6CFDF42777D4,
	PointerEventData_get_position_mE65C1CF448C935678F7C2A6265B4F3906FD9D651,
	PointerEventData_set_position_m65960EBCA54317C91CEFFC4893466F87FB168BBF,
	PointerEventData_get_delta_mCEECFB10CBB95E1C5FFD8A24B54A3989D926CA34,
	PointerEventData_set_delta_m30E0BE702A57A13FEA52CA55D4B29DDE66931261,
	PointerEventData_get_pressPosition_mB8F60EB21F6E6892EC731382614BAB85E29ED642,
	PointerEventData_set_pressPosition_mE644EE1603DFF2087224FF6364EA0204D04D7939,
	PointerEventData_get_worldPosition_mFA3E9DD557E07F51C7D7D75F505DB93BEB2A9426,
	PointerEventData_set_worldPosition_m87A8206A970075895344E014F49FD2D58FE3B366,
	PointerEventData_get_worldNormal_m75ADD877237B3E030EEA436A28943BB2B8704066,
	PointerEventData_set_worldNormal_m2B698E666B463EAB0D60DABAB68DCC60A2B8EC9F,
	PointerEventData_get_clickTime_m08F7FD164EFE2AE7B47A15C70BC418632B9E5950,
	PointerEventData_set_clickTime_m215E254F8585FFC518E3161FAF9137388F64AC58,
	PointerEventData_get_clickCount_mB44AAB99335BD7D2BD93E40DAC282A56202E44F2,
	PointerEventData_set_clickCount_m2EAAB7F43CE26BF505B7FCF7D509C988DCFD7F28,
	PointerEventData_get_scrollDelta_m4E15304EBE0928F78F7178A5497C1533FC33E7A8,
	PointerEventData_set_scrollDelta_m3ECB852D4D953D59C617DF0FE9E44B0EEF8AEA13,
	PointerEventData_get_useDragThreshold_m683F708E7E5F8351D8DD354A3B2CA9B290683BEE,
	PointerEventData_set_useDragThreshold_m146893D383B122225651D7882A6998FFB4274C85,
	PointerEventData_get_dragging_m7FD3F5D4D8DAC559A57EDB88F2B2B5DEA4B48266,
	PointerEventData_set_dragging_mEB739C44F1B1848B4B3F4E7FBB9B376587C2C7E1,
	PointerEventData_get_button_m180AAB76815A20002896B6B3AAC5B27D9598CDC1,
	PointerEventData_set_button_m207898937082827843819AB2DBDA46F90EEDA9B7,
	PointerEventData__ctor_m3A877590C20995B4F549C6923BBE2B0901A684F2,
	PointerEventData_IsPointerMoving_m4D3F38B1E1DA05DF39C98D39F1355A0456C84923,
	PointerEventData_IsScrolling_mC0A9A01895E5028145FA87A0B633F76A5DEE6DA8,
	PointerEventData_get_enterEventCamera_m5C21DFBFE45E241DD29EA035D51146859DE03774,
	PointerEventData_get_pressEventCamera_m514C040A3C32E269345D0FC8B72BB2FE553FA448,
	PointerEventData_get_pointerPress_mB55C5528AF445DB7B912086E43F0BCD9CDFF409C,
	PointerEventData_set_pointerPress_mF37D23566DDB326EB2CFE59592F8538F23BA0EC0,
	PointerEventData_ToString_m9AE3F27AE062B31B4CC9B015B1E17152CF705E0C,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	EventSystem_get_current_m6DCD04BC8A3C673A2961DB3BC373EDC0BEAD8251,
	EventSystem_set_current_mAE3E20E127734F7FBF0481911903203FA07EE3C1,
	EventSystem_get_sendNavigationEvents_m6577B15136A3AAE95673BBE20109F12C4BB2D023,
	EventSystem_set_sendNavigationEvents_mC4AF68C06C2A8E1017142D7C9C9AA29018F56F96,
	EventSystem_get_pixelDragThreshold_mE6BC245DF53C04B06DCDF7663BF40087696D793A,
	EventSystem_set_pixelDragThreshold_m2EC2BBAAD11C1DDFC3D8AF1A6CBB827F47FDA951,
	EventSystem_get_currentInputModule_mA369862FF1DB0C9CD447DE69F1E77DF0C0AE37E3,
	EventSystem_get_firstSelectedGameObject_mE8CE4C529A7849B4A0C0EC51E61037A0F7227EF0,
	EventSystem_set_firstSelectedGameObject_m385908443CAABBD7BCDF17C3D0A43FE6CE697075,
	EventSystem_get_currentSelectedGameObject_m999F9BFD4C20E2F00C56D4FED89602B6077EF70D,
	EventSystem_get_lastSelectedGameObject_m92B25EBE0F4D7F656C95B365474623107D93D4FB,
	EventSystem_get_isFocused_m22370735AB4FCB930C65F3766E5965FCBDD55407,
	EventSystem__ctor_m19F2A25AFA7FC3BCCAD198F84643C52ABF8402DD,
	EventSystem_UpdateModules_m30564B2CFC0B3AA885FCB6E6D8BE104019D5F0D9,
	EventSystem_get_alreadySelecting_mEC3798F034DC597BFD2D0E36E072C37196F1991A,
	EventSystem_SetSelectedGameObject_m7F0F2E78C18FD468E8B5083AFDA6E9D9364D3D5F,
	EventSystem_get_baseEventDataCache_mDBDF0D5AEEADB4BF9AB9D11B961D9B2B083870AB,
	EventSystem_SetSelectedGameObject_m1B663E3ECF102F750BAA354FBD391BA13B8CBE55,
	EventSystem_RaycastComparer_mE4881B6740E6ECB12154D3E2753A98C89840CBFE,
	EventSystem_RaycastAll_m45A359AEC0BED3D47B50A862A2F69911373DE934,
	EventSystem_IsPointerOverGameObject_m49888E0F63763086DCD74D5A6C5C5705A96F88EB,
	EventSystem_IsPointerOverGameObject_mE7043E54617B8289C81A1C7342FBE0AE448C9E3A,
	EventSystem_OnEnable_m05C025C751DF60D22C29650A83893E9274903BC1,
	EventSystem_OnDisable_m990C9CC348A614D8EB77D8FA2014D4E575CDABA9,
	EventSystem_TickModules_m421AFDE7169843B4FDB8AA174AD8CE6673EE5785,
	EventSystem_OnApplicationFocus_m586A4F937B5866B055FEA38B4C0D8016BBB8E247,
	EventSystem_Update_mF0C1580BB2C9A125C27282F471DB4DE6B772DE6D,
	EventSystem_ChangeEventModule_m44C4A0697EF6E624741DFCDC13E9C109559846D4,
	EventSystem_ToString_m40B166AD49EE4E9CCC0872EE3AA44920A121D075,
	EventSystem__cctor_m2BF2E38A1CE15A67C7B153C0B58592361E52B6F9,
	EventTrigger_get_delegates_m3E7DA62318A815A551F877F67642A9F2E5A4AF97,
	EventTrigger_set_delegates_m9C5DBA73484F576E272808F9B4324EA456A1769A,
	EventTrigger__ctor_m6C07DACEA4CBC537B714051B7DAFEFCE21ABC050,
	EventTrigger_get_triggers_m84979D5401D19622466C657F16931BEE24021695,
	EventTrigger_set_triggers_m07BA2416DFBCB1BB168AC9B362A7E7782FDC788F,
	EventTrigger_Execute_m3E270D493082B7A1307A233C78D54D2740ADF0C1,
	EventTrigger_OnPointerEnter_mF4C3838BCE04F82EDB95920EBA94B9297B27C109,
	EventTrigger_OnPointerExit_m1E3BA5AE73D8394C8AD28566EA4B55B8568105C4,
	EventTrigger_OnDrag_mB6CE0C89FD6EE7B9750A095A9D87268BF72B552C,
	EventTrigger_OnDrop_m781DA5355EAC20814ACC1D6C92431B51E2F5FB2C,
	EventTrigger_OnPointerDown_m136FC8C7C99CB939A4C355A19204E9DDC75958F3,
	EventTrigger_OnPointerUp_mE54BD63E811EDB8AFB7A8AC04E2D4B9BB6E78F73,
	EventTrigger_OnPointerClick_mEF67FDE69498E521EDE99015C3BDD015CEA25954,
	EventTrigger_OnSelect_mA2DDB4D5FC8D24203A3F18205DF94605D7965678,
	EventTrigger_OnDeselect_mEB27B1C63B0A305B0CECD8DA07E4E640292ACC50,
	EventTrigger_OnScroll_mA0D1332297A15CE361EAC4F188C81BDB97BE51CE,
	EventTrigger_OnMove_mD23375381AD67FFC4F5442A445D1930CBFFC3162,
	EventTrigger_OnUpdateSelected_m63066AC94D14F231FAEC2EA796F01418B3ABD0C2,
	EventTrigger_OnInitializePotentialDrag_m0F00EA4F3A05BC081A71DACEF242000759D9968A,
	EventTrigger_OnBeginDrag_m5F9E87C7ECC639845489FE40809E78E6B7649E88,
	EventTrigger_OnEndDrag_m2954768898BB2EF5EBC87C18227ADB7767D1A42C,
	EventTrigger_OnSubmit_m2E749E13FFD211D3CF86A4247B508F37F3B6F880,
	EventTrigger_OnCancel_m7BCE623201D87C36B05386CFDD14BDDDC45B68FF,
	TriggerEvent__ctor_m6693E5CD57DF6DFB9411F5AE5C0C35958CC9FBFD,
	Entry__ctor_m033E853903D01DE79E1534A6DD2692E8CC9AAA7F,
	NULL,
	ExecuteEvents_Execute_m536B5CEFBA924D2E26638E1C6B54F611B10E6A2E,
	ExecuteEvents_Execute_m30D10832652D26262469D572E19B7FE806B51ECE,
	ExecuteEvents_Execute_mDCAA504C218930ED94C32DCB5F257E68B3018377,
	ExecuteEvents_Execute_mDB4E5999550D766846D363254C6727FE9C593E0F,
	ExecuteEvents_Execute_m709B2C8315D1D4E33B4BD0FCDE937621E9C1C440,
	ExecuteEvents_Execute_mB1759A12A08782510DEB64E2F8852B0332005EAC,
	ExecuteEvents_Execute_mB5EC0B81C27785F239F1E4B42A8365EB4EC0F6B6,
	ExecuteEvents_Execute_mCCACEE2E4C3F164D70A966E4E5477E0AA2470F17,
	ExecuteEvents_Execute_mDAAEE18D8BB143EDF2595B8468F3DE7A44F45A7C,
	ExecuteEvents_Execute_m3B01C05FC40D0AA092CCC0F830C91B9B47DF383E,
	ExecuteEvents_Execute_m2E84A550F3B3EFE1BF2C417EDD158BD944B48E49,
	ExecuteEvents_Execute_m2E26F3129D035DD3B43041DE39AB8E0880F9F0CA,
	ExecuteEvents_Execute_m597F69E7172FEDF9E9F37D5FE3978655BC01E456,
	ExecuteEvents_Execute_m621BABB42458C80357CA654A88ECF01058EF527B,
	ExecuteEvents_Execute_m49E6267D041559963C35D5468121DD835788C57D,
	ExecuteEvents_Execute_m4523B7C8F3C34D2C24260200A66B7BB7E09E83B2,
	ExecuteEvents_Execute_m32FDEFE4F6E949A5D166ABEC7672C10CBF033214,
	ExecuteEvents_get_pointerEnterHandler_m32A890182BF379E2CF1C1BE8450516852B6E198F,
	ExecuteEvents_get_pointerExitHandler_mA36731A5F269B34CCB5397CBD656028D959AEACD,
	ExecuteEvents_get_pointerDownHandler_m3DD79981D37B9E9E914685190D1E68DCA26E53B8,
	ExecuteEvents_get_pointerUpHandler_mD3046445618125409AE4DDE90DCC4100E08DCD26,
	ExecuteEvents_get_pointerClickHandler_m67F8690CE7DC954F2FE78817D2ED931DEE472014,
	ExecuteEvents_get_initializePotentialDrag_mD2F94D3BD9F9125494B1379E0D63ECC414334A94,
	ExecuteEvents_get_beginDragHandler_mD706383D0CF9271281EB0685C15BAD32D6C381BD,
	ExecuteEvents_get_dragHandler_m0CE246D3F757907CE5C647FE3B20DE6A9D017DAD,
	ExecuteEvents_get_endDragHandler_m9DBA9494461A07E9ECFA512C8CA4553CE373747F,
	ExecuteEvents_get_dropHandler_m8BB863BA4EDA61D3961F8B4E2722FD2DA79D63B1,
	ExecuteEvents_get_scrollHandler_m18125DE86004CCAA9D59B3826910168821C6DA9F,
	ExecuteEvents_get_updateSelectedHandler_m5F24247DE551CFCDEE7C0F4C7BB1B3CCA3B2AD59,
	ExecuteEvents_get_selectHandler_mAB1A2D6F20464477674056C4DC87C2C072BFA0F1,
	ExecuteEvents_get_deselectHandler_m4E4AAA827016AE259476C761BF26C3112C1A418D,
	ExecuteEvents_get_moveHandler_m237D825C3257B500DE5755AF3EBD4BBA5ABC93AF,
	ExecuteEvents_get_submitHandler_mD2C06DE887C9CFF4BB0A5A0957598B7F6796E2E3,
	ExecuteEvents_get_cancelHandler_m1111AE90C89A94F7AA74F55D84DE9A979F2BF6A3,
	ExecuteEvents_GetEventChain_m1B6EA60ED8AB222A384F1B1EC5CD95B6A2D0F52D,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ExecuteEvents__cctor_m9913078BF1281F1EF3E643613EE2E28AAA26EE77,
	NULL,
	NULL,
	NULL,
	NULL,
	U3CU3Ec__cctor_m6F61DB5FE620344D10CA0C69A9C460821AC456D4,
	U3CU3Ec__ctor_m24D38AEEE1FA8E42B3C01646B97028C9CCEB1C4B,
	U3CU3Ec_U3C_cctorU3Eb__79_0_m43DB792DBF6037E0969D46AD75F3CED7CD0918E0,
	BaseInput_get_compositionString_mBE9575EE3844DEFF6DFD161AA198AD07BF7A1F7D,
	BaseInput_get_imeCompositionMode_mE29EA0423E9AE857D1A82754CDD89E0A7D2D17E2,
	BaseInput_set_imeCompositionMode_m9E241E681DFB158A35B61B9BEA8CF5F4C9B1E436,
	BaseInput_get_compositionCursorPos_m3035B5E76FFC1339B8DC57EC0F25D101E0DA289A,
	BaseInput_set_compositionCursorPos_mF1D7CBA6575F4D4D527AEAB1EBD6555A6EFA2E78,
	BaseInput_get_mousePresent_m20CD979E8F9CAF3A646AD6EDFA984A9A7DE62FBE,
	BaseInput_GetMouseButtonDown_m9D82E7B9B737A6DFC021560E722882F33231B1DB,
	BaseInput_GetMouseButtonUp_mBBAB91395DC957EC3DE40C43E5E586F6DA04AD1A,
	BaseInput_GetMouseButton_m9AE703917C75AF45B6930B245DD5C77D58DD7817,
	BaseInput_get_mousePosition_mAAB1F817C064AC1D4CF89E043EFB4F5BD99A2D7A,
	BaseInput_get_mouseScrollDelta_m9DF1C36549FEF1E33F53610C51387A33CBFABD7C,
	BaseInput_get_touchSupported_m1A3C393FBBD3FE82DF903DDADC8DE9AEC972C343,
	BaseInput_get_touchCount_mB762096E18B7A7D9E53EB89A57A7DAC2C11EBBF1,
	BaseInput_GetTouch_mFCDD1677E66D7F577E3A5812F5122F06EF7CA52E,
	BaseInput_GetAxisRaw_m3C5080572EB58D05414F30186E05C9DA7A531E44,
	BaseInput_GetButtonDown_m8AA48484C44378A1379C55D46B13376DC395A992,
	BaseInput__ctor_mA92AFE4DB6CFCD49C223BAC0C3E71B50037CB87A,
	BaseInputModule_get_input_mE238D28A1EB973EBB1FFF5DF2951F9E4CB0ED052,
	BaseInputModule_get_inputOverride_mAEB5656BE9E6655B6B2EEB5DDDED6F76D330A150,
	BaseInputModule_set_inputOverride_m8A0835E8D9442B3B6BC1BEC17F1F288C9FCC4A37,
	BaseInputModule_get_eventSystem_m84626EB81106D5CC20F49FB0F6724626D168EE8D,
	BaseInputModule_OnEnable_mDF384520411BFEF7B908D0149C7A27431B012190,
	BaseInputModule_OnDisable_mD05849779BEF3EAB5DFAC2B51F4F2127E64FE719,
	NULL,
	BaseInputModule_FindFirstRaycast_mB83B31E8682EF7967F93F41E359E9F4C366162B3,
	BaseInputModule_DetermineMoveDirection_m9903B3F5EE4B62CBC730821992A63473952AF04F,
	BaseInputModule_DetermineMoveDirection_m8AC72D11BAF49B6AB8A1C683742F14FA2DFF1CD4,
	BaseInputModule_FindCommonRoot_mE393C964E7B3E057FBBB695FE3032AF1CBA23563,
	BaseInputModule_HandlePointerExitAndEnter_mC94EE79B9295384EF83DAABA1FB5EF1146DF969F,
	BaseInputModule_GetAxisEventData_m1DF20BD8CE49684203394729ADE225B19D87054A,
	BaseInputModule_GetBaseEventData_m632271D84DBDF7AF30B38EC599574281525E673A,
	BaseInputModule_IsPointerOverGameObject_m45EFDCFA22749B1564DDCCB1555EAEC008012D84,
	BaseInputModule_ShouldActivateModule_m6B2322F919981823C1859A6E51DAACDC9F2DAD61,
	BaseInputModule_DeactivateModule_mCB2874A23D5FE0C781DE61D118E94DDC058D7EC5,
	BaseInputModule_ActivateModule_mA7960DD1DBAB0650F626B160128205601C86C0E4,
	BaseInputModule_UpdateModule_m6F816553728F18F3F8A86AC957AC9321864B0A1C,
	BaseInputModule_IsModuleSupported_mB721E3AF9A62305509A080B20E543AB956CA36E3,
	BaseInputModule__ctor_m50C7488E22C0930A98E951F27E9235FCD2A593A6,
	PointerInputModule_GetPointerData_mCEFFC5B4DD8C15508224DC13BA3C4C0C4527A208,
	PointerInputModule_RemovePointerData_m0DB8FD2375F00D7A1059AD4582F52C1CF048158B,
	PointerInputModule_GetTouchPointerEventData_mA53FE69943897DF12DAE6A1C342A53334A41E59F,
	PointerInputModule_CopyFromTo_m528B215D9DFA2A247297FEEAAB285F1FAFDD4947,
	PointerInputModule_StateForMouseButton_m39F177D0FC7A73999559451475B98D53C90520CD,
	PointerInputModule_GetMousePointerEventData_m3B6B2642C1F0D293E6CEC42E75363CB78B779754,
	PointerInputModule_GetMousePointerEventData_mE28E21995CF3A463A66C414A2FF63486DA25A172,
	PointerInputModule_GetLastPointerEventData_m06FD0ACEF8FA7B77D2600271D386A235B9CB4113,
	PointerInputModule_ShouldStartDrag_m823B785B5CDFB4AE2881FC5F8655453D75F76585,
	PointerInputModule_ProcessMove_mF04EC9467E26F5B1C4830398BCC1FEEAA86D206F,
	PointerInputModule_ProcessDrag_m0625CFD2FED814FF63B97977F65AA0C5F9A5D321,
	PointerInputModule_IsPointerOverGameObject_m3C33FE9B778B5BFD623254246AD61ADDDC95DA56,
	PointerInputModule_ClearSelection_m98255DD7C5D23CDA50EE98C14A0EB2705CBD1233,
	PointerInputModule_ToString_m72DF55F5B65032139E30BCCEC86692929B1BCA22,
	PointerInputModule_DeselectIfSelectionChanged_m691EBB4E49657B1C21D25B79FB1C2F6ABD870A92,
	PointerInputModule__ctor_m7286C77CA28195FA2034695E55DD8A9D9B696DC5,
	ButtonState_get_eventData_mC7A3D0172F44EEE3570A751D9DD154C465F0C48F,
	ButtonState_set_eventData_m85A92E7A2104B5A248A7AEA7A8C86F41DB47CC73,
	ButtonState_get_button_m7C3B83551E176EDC1232A65589B4FC685CE022A5,
	ButtonState_set_button_mBEA15BAD80964F6716746E100CFF406537D38261,
	ButtonState__ctor_m7D9B7D5AB76C393C5A3CD720ECAA2FCE990E8E6F,
	MouseState_AnyPressesThisFrame_mC4D468788B94FA92C544B911EF76F4B51EAD42DD,
	MouseState_AnyReleasesThisFrame_m1086E36B13BD7BA488138E0B1416AF02D51E8549,
	MouseState_GetButtonState_m4CB357F518E9333CAB0CE3A54755429A6B8D0A32,
	MouseState_SetButtonState_mA97DA94B17CF78F158EC17EC16283626BE513937,
	MouseState__ctor_m16EF8D45AF8A178368547BD1CE4FBF9DBC563605,
	MouseButtonEventData_PressedThisFrame_mEB9CB4D5EFBFDD43BB877CBA36FCE0DA8F21C3FF,
	MouseButtonEventData_ReleasedThisFrame_m014BA45901727A4D5C432BB239D0E076D8A82EA1,
	MouseButtonEventData__ctor_m66CCB772A4D986FB2A401E96F6296A56BBD6A238,
	StandaloneInputModule__ctor_mC5A24967FF3EE2090171F5284125781551B4CA03,
	StandaloneInputModule_get_inputMode_m8045A329EAFE94211AFAAC7FB28597AC9BCC933B,
	StandaloneInputModule_get_allowActivationOnMobileDevice_m4BF8B0EBEA49CEA7A11A926BADFD21BD4EA78B05,
	StandaloneInputModule_set_allowActivationOnMobileDevice_mCFF331A9424D7484C0FE8460A86C92F1CCF0305B,
	StandaloneInputModule_get_forceModuleActive_mAA6BA392D58A841E38CD703DCDD6D9FBEF6F0E44,
	StandaloneInputModule_set_forceModuleActive_m95923244ACCDF3CCF37DA3EEB9024E8054C844EC,
	StandaloneInputModule_get_inputActionsPerSecond_m13886FBCF0A097713959E0512944A0B25835CCCE,
	StandaloneInputModule_set_inputActionsPerSecond_m7FCEEB1ED0F3FA6AB68F5A3E261843FCE74C563C,
	StandaloneInputModule_get_repeatDelay_m8CD97CB5F5C9A5BB94FA5652A94EFCED831C6C76,
	StandaloneInputModule_set_repeatDelay_m1DE2E64F91DE672BB1D10EB8E84BDAABEDE8FDAE,
	StandaloneInputModule_get_horizontalAxis_mCA9D35F564EBE1AC37D150B9C5223A2B231796D0,
	StandaloneInputModule_set_horizontalAxis_mAE493FDCAC85A5C3C9B4C45206F1B6DF616B7CA0,
	StandaloneInputModule_get_verticalAxis_mAE5B1955B8E33C0CE6A20D55787DC88FB3D8E1C8,
	StandaloneInputModule_set_verticalAxis_m5468886D85C85A4A53F42264D73E7E192DFADF8A,
	StandaloneInputModule_get_submitButton_m4352EE0238C6CF552AB370E34B73F9CFB07567FE,
	StandaloneInputModule_set_submitButton_m0454BCA229604DFBFB917842CD923F5F79E1DB45,
	StandaloneInputModule_get_cancelButton_mE8453E7FE91651674F844B22250704B54BE0C7AB,
	StandaloneInputModule_set_cancelButton_m1BC3CEE85F591C710FA067F6A34F5D2A3F37ED10,
	StandaloneInputModule_ShouldIgnoreEventsOnNoFocus_m27721F13F2C71F806C0CFFFB2D69CB647528911D,
	StandaloneInputModule_UpdateModule_mAEF77BAA4F991BF7CF8E81294AFEB94B7DA1B278,
	StandaloneInputModule_ReleaseMouse_mEE3FAAA8B87CAE09F156322B7A38E2EC5460E1BB,
	StandaloneInputModule_IsModuleSupported_m3570BE55A4362C2E4CD65603CFD94A425F15011D,
	StandaloneInputModule_ShouldActivateModule_m2108FFC28DBE61F2574DC0F3D6DAB46E1539F3CC,
	StandaloneInputModule_ActivateModule_m7462FAE46BEB2A289F8BB2B001A3474206AB8E8F,
	StandaloneInputModule_DeactivateModule_m6268DCE57831BB227F94F374A1D80CA4BD10C5A2,
	StandaloneInputModule_Process_mEA8D89C754B958916467AEB75592670B15519D98,
	StandaloneInputModule_ProcessTouchEvents_m2C06F4FED9D3F300031E889330180C5004034DBA,
	StandaloneInputModule_ProcessTouchPress_m1ACFC2288CC51BD8C85C6894994923B1762B0B49,
	StandaloneInputModule_SendSubmitEventToSelectedObject_m294066868523F9D8AB5DA828F9A326C2F6999ED0,
	StandaloneInputModule_GetRawMoveVector_mDA3F235097E686FE09FEC4E1A3BC0EB6F8EDF1FE,
	StandaloneInputModule_SendMoveEventToSelectedObject_mA86033B85BCC6D4BB5846B590AB1F2A21FE347ED,
	StandaloneInputModule_ProcessMouseEvent_m0E5CCCC3F32DF86C32E02873DDE2BF29E9A05E37,
	StandaloneInputModule_ForceAutoSelect_m009DD883E1783D97901AFF2D7B7573EB28BC4DBC,
	StandaloneInputModule_ProcessMouseEvent_m1D697D9E5F2FDF5B770471185CD364D12A89B18A,
	StandaloneInputModule_SendUpdateEventToSelectedObject_mDB8B0FD5B0C1AD356C91FF1B301E1EB64197506F,
	StandaloneInputModule_ProcessMousePress_mE5D5A47900D7FAFCBBC58ACBDCB03BE2958FF7A6,
	StandaloneInputModule_GetCurrentFocusedGameObject_m2DFA63DC619408591F3B0D45186DF2BBEA36AAD1,
	TouchInputModule__ctor_m6DA57CEEFDE230F74BEE3CDED82735AF0ED6E0A1,
	TouchInputModule_get_allowActivationOnStandalone_mA995FC27A62E6723C391AB3FDEBB7BCFFEAB6F93,
	TouchInputModule_set_allowActivationOnStandalone_mB1EFDB582D30720FE84868FC957CC38042517BD9,
	TouchInputModule_get_forceModuleActive_m0D30D44DE67C0220BDE939DB70F47100344ABD62,
	TouchInputModule_set_forceModuleActive_mDB0018F8FE614769EFBD8F59E088D13BDB9F6DC0,
	TouchInputModule_UpdateModule_m8B96D764DBEC18DCEF5C58D7DB787435600C8BE7,
	TouchInputModule_IsModuleSupported_mB634972650F8E3B3DDCC21B32CF85567FFD269C3,
	TouchInputModule_ShouldActivateModule_mACEA2CCCEFD2A21BA3F43E0D806C386D9F426A52,
	TouchInputModule_UseFakeInput_mAE7BEFCC688D9572A01983A0EADDC72C8BC55302,
	TouchInputModule_Process_m8A9D5A7E3C80EE254E8260DC59B369EB70C5EC9C,
	TouchInputModule_FakeTouches_mF581740619A868F99690CCA249941049306D6227,
	TouchInputModule_ProcessTouchEvents_mF1371956D57515679F23FD9A3CE7EEA1335C0C99,
	TouchInputModule_ProcessTouchPress_m3705E3E72EAB93BF7476A160ACFF17E8002E3A85,
	TouchInputModule_DeactivateModule_m9DBFE2349C399250B64EDF8BE50E094C8C6EC1E7,
	TouchInputModule_ToString_m928AD9BE065AED416E067B806A95FBC650956142,
	RaycastResult_get_gameObject_mABA10AC828B2E6603A6C088A4CCD40932F6AF5FF,
	RaycastResult_set_gameObject_m3A1B023B578A8142A437B0C27AAE045DED6AAA20,
	RaycastResult_get_isValid_m4BC5BF4FDB163ED6BC0D8C47B0B059E76C508ADD,
	RaycastResult_Clear_m00588011F0D28FD65E082C5DAA2E737E3F6AAB9C,
	RaycastResult_ToString_mF55ECA45850A1CA8A43FC1BA8F932D6F86C60E3E,
	RaycasterManager_AddRaycaster_mFAB4E0B7F75ED5D04D0ABE2F2C6D6487B1655A0D,
	RaycasterManager_GetRaycasters_m8E8BD8154C405B3E3A0CDB55D5566201C7EE09CC,
	RaycasterManager_RemoveRaycasters_m60961FD830DE987C78D3E51324AE79A57CC1E8D1,
	RaycasterManager__cctor_m191BF078929AC5105A542F2FDE78E0898FE1C46E,
	NULL,
	NULL,
	BaseRaycaster_get_priority_m98D876E84CB65E725BEA493E7BD55EF024090FE5,
	BaseRaycaster_get_sortOrderPriority_mC8BAA723774E479F397382A60B2F4568DED9268D,
	BaseRaycaster_get_renderOrderPriority_mCA1E2F6F1223410DEB343C34FF6CB84DEBA2BCF3,
	BaseRaycaster_get_rootRaycaster_m2F2FC8638E1680699F0A6F17402BD7F67D13C996,
	BaseRaycaster_ToString_m980644F33B2B63B58E195697099A930A3B5A14F3,
	BaseRaycaster_OnEnable_mA87FC2F224D137490912EBEB1039030059E86B5F,
	BaseRaycaster_OnDisable_mF4C177437644DDCA3EEE5F4EBFF85607A748BD0D,
	BaseRaycaster_OnCanvasHierarchyChanged_m7F1B49A5A514FAB23FE8FF3CD5C87862316AF9D1,
	BaseRaycaster_OnTransformParentChanged_mD0500BD255F5BF56180D24C5A5B80095470DB5DC,
	BaseRaycaster__ctor_m44ACAAC3E3CBDBBE74D2FCA997268EE1DF9D8A3C,
	Physics2DRaycaster__ctor_mEB8E9794F72BC357DD518AB1519BBF716FA11017,
	Physics2DRaycaster_Raycast_m037A598BC662F1801E08353CF56BE300B34C36BF,
	PhysicsRaycaster__ctor_mEBFB0AC90FB9FB8A46B2CB8050A4BAE3D1BCC72E,
	PhysicsRaycaster_get_eventCamera_mC067A7DEC69941B818286767ADE39AAECEC44CE3,
	PhysicsRaycaster_get_depth_m9B1DC4C8868DF41F665EE4684779078F25ABA7AA,
	PhysicsRaycaster_get_finalEventMask_m984B1A6765136BB3454C1895EAB28DA03C49A6B2,
	PhysicsRaycaster_get_eventMask_m210CAAB35833B472D235E601F4878C06EFD74DA6,
	PhysicsRaycaster_set_eventMask_mCC9065510DB88DC4E3A44F7F2339B950E64D9FFF,
	PhysicsRaycaster_get_maxRayIntersections_mDBE92413912BCFD8A50C54B520E5F43C95F3CD27,
	PhysicsRaycaster_set_maxRayIntersections_m5A8FBBE6CB9AE15F7ACC7AD6B46931049CD0BD3C,
	PhysicsRaycaster_ComputeRayAndDistance_mE3C052D28ADC1DB6239774007CE80C96E8020A87,
	PhysicsRaycaster_Raycast_mD597D05EB47ECBFD60E46C317547FB0AA5A2E940,
	RaycastHitComparer_Compare_m856F219A57E2827C0E4E0E075D0A7205CC6C39CE,
	RaycastHitComparer__ctor_m261F5A7BB4E3DD3FD760B27E70D84197DEF00F98,
	RaycastHitComparer__cctor_m32F4A6DC0DF0FAEA7B07676021CB775ECF867B2A,
	UIBehaviour_Awake_m0A6FB0A0089B29A53768BFE65D6E06183A1B60BE,
	UIBehaviour_OnEnable_m9BE8F521B232703E4A0EF14EA43F264EDAF3B3F0,
	UIBehaviour_Start_m7334773773C9454A7A6E95613E60762E68B728F7,
	UIBehaviour_OnDisable_m7D3E0D1AC43330C5A50B17DD296D2CB84994CA23,
	UIBehaviour_OnDestroy_m7D4F82D8ADD8723A4712F376C5D5F0F18A856966,
	UIBehaviour_IsActive_m14EAD5699E8E72A360B1241146393349E5DCEF07,
	UIBehaviour_OnRectTransformDimensionsChange_mF5614DB1353F7D1E1FC8235641AECFE94DBE03E0,
	UIBehaviour_OnBeforeTransformParentChanged_mEFF5109EE955F34D2D068F0DAD4C4312F5297CD3,
	UIBehaviour_OnTransformParentChanged_m8D3C0D2ADCDFF54D4FB6BD4DB0E91FA7199BB1DB,
	UIBehaviour_OnDidApplyAnimationProperties_mC4A4AF43FD946053995575D0899A4E1E4D444E16,
	UIBehaviour_OnCanvasGroupChanged_m1DE1A5688A487CCD9028F5A544D7EC025C2E15BB,
	UIBehaviour_OnCanvasHierarchyChanged_mE516A02869AA87FCF106F85EC95A536C71C8CC67,
	UIBehaviour_IsDestroyed_m28D16D52C10659BE98248139BDE1D9C5423043A5,
	UIBehaviour__ctor_m869436738107AF382FD4D10DE9641F8241B323C7,
};
extern void ColorBlock_get_normalColor_m7C5A73BBE739FFA030EE1FE10CE78BECB8417126_AdjustorThunk (void);
extern void ColorBlock_set_normalColor_m32EB40A0BB6DD89B8816945EF43CFED8A1ED78B9_AdjustorThunk (void);
extern void ColorBlock_get_highlightedColor_mDA12AC25C740DF2AFFF086B62795533B4D2ED336_AdjustorThunk (void);
extern void ColorBlock_set_highlightedColor_m176DF68369EC1536D089EABD1357AB8EAC33BC88_AdjustorThunk (void);
extern void ColorBlock_get_pressedColor_mCEBCE4184CBAE7D33FE3D8AD3FDFA3931FD87C70_AdjustorThunk (void);
extern void ColorBlock_set_pressedColor_m7F357DD0E16A18A84D2DCF039238ADE6695EB5C0_AdjustorThunk (void);
extern void ColorBlock_get_selectedColor_m7C592B6652C94BACF43F0C007AC0093A2543F725_AdjustorThunk (void);
extern void ColorBlock_set_selectedColor_mA1A0ADD43DBE600443C5A1D217922614D77A6C3C_AdjustorThunk (void);
extern void ColorBlock_get_disabledColor_mF470FB3FA48E777F5C70F1C18EF218DB673970E9_AdjustorThunk (void);
extern void ColorBlock_set_disabledColor_m0E9C6689B962550D1C3F15275DF2409FD3C28841_AdjustorThunk (void);
extern void ColorBlock_get_colorMultiplier_m38A25E26F0AB48B6CB04A7A18EF1EAC2AFD2BA28_AdjustorThunk (void);
extern void ColorBlock_set_colorMultiplier_m8A321BF2E07D4D184214014C32F5389C8BE06198_AdjustorThunk (void);
extern void ColorBlock_get_fadeDuration_m37083141F2C18A45CC211E4683D1903E3A614B1C_AdjustorThunk (void);
extern void ColorBlock_set_fadeDuration_m51827B5F57CFEF2FF8DFB03687A301E2F1B3CDCE_AdjustorThunk (void);
extern void ColorBlock_Equals_m4A08484E3305C07A0CBE38C0205E21CDF05C88D7_AdjustorThunk (void);
extern void ColorBlock_Equals_m4C4BE778C6BEFA266E497540E97CB3201B13FB6A_AdjustorThunk (void);
extern void ColorBlock_GetHashCode_m26AE64D514B61123616F54D3FC820A3FE11AA40E_AdjustorThunk (void);
extern void Navigation_get_mode_mB995DE758F5FE0E01F6D54EC5FAC27E85D51E9D9_AdjustorThunk (void);
extern void Navigation_set_mode_m06986291C80ABAB009A7968A3A5C557197B7CAD0_AdjustorThunk (void);
extern void Navigation_get_wrapAround_mD84154C6A5DF2D999F9C68694660BF4C75E42E09_AdjustorThunk (void);
extern void Navigation_set_wrapAround_m88C6E2CE8D45F409B10220931C7574A925478EEF_AdjustorThunk (void);
extern void Navigation_get_selectOnUp_mE2CE90A504C34AE46D8BC9930C7E309F3BD61D05_AdjustorThunk (void);
extern void Navigation_set_selectOnUp_m4FB3B1A6FD96E0F5AF5A029C49150E05520EAEB4_AdjustorThunk (void);
extern void Navigation_get_selectOnDown_mDEAD9711CD57EC13A9622AA29E1D95E7FE49B26C_AdjustorThunk (void);
extern void Navigation_set_selectOnDown_mFAAAE5BDCD7A658119C96474C108C9A59E62F2D5_AdjustorThunk (void);
extern void Navigation_get_selectOnLeft_m7E124CA8B6205001892CA5F8FBCC3CE2A451ACF8_AdjustorThunk (void);
extern void Navigation_set_selectOnLeft_mA33AA78D5F7F6BE93490C9EABF182E1D77C0E580_AdjustorThunk (void);
extern void Navigation_get_selectOnRight_mA7E6599E6A54419AE4C532F76F4199EFF2188114_AdjustorThunk (void);
extern void Navigation_set_selectOnRight_m6B60DBA287E342806BF4CF1AD332DA4EF875BD4A_AdjustorThunk (void);
extern void Navigation_Equals_m1462D8660E507B770231FF9CBDAC296C1D751C7A_AdjustorThunk (void);
extern void SpriteState_get_highlightedSprite_m695FD2C0827908CBAFFF5D5033FEED380D4219FA_AdjustorThunk (void);
extern void SpriteState_set_highlightedSprite_m3B5F7EF5AF584C6917BA3FB7155701F697B6070D_AdjustorThunk (void);
extern void SpriteState_get_pressedSprite_mDCEB9F07BDD7C2CFCDC7F7680D05B47EA71965D6_AdjustorThunk (void);
extern void SpriteState_set_pressedSprite_m21C5C37D35A794F750D6D4A95F794633B9027602_AdjustorThunk (void);
extern void SpriteState_get_selectedSprite_mA85714CC6BF3801A63CC42B026E66CEDFD36949E_AdjustorThunk (void);
extern void SpriteState_set_selectedSprite_m00EC0C38B3ADBA12D9524CAE982BE8B21F608A54_AdjustorThunk (void);
extern void SpriteState_get_disabledSprite_m7AF976C63DA03ED035B031D5A98413C39894F50C_AdjustorThunk (void);
extern void SpriteState_set_disabledSprite_mB368418E0E6ED9F220570BC9F066C6B6BF227B13_AdjustorThunk (void);
extern void SpriteState_Equals_m2190A8BFFC45EC86766FC68C808F3DFE18E35827_AdjustorThunk (void);
extern void ColorTween_get_startColor_m17A0327111EF4CEB0C15009D70607065D7B2DD0C_AdjustorThunk (void);
extern void ColorTween_set_startColor_m3FDDCF9C585451522E3DC19AC452B6020931418D_AdjustorThunk (void);
extern void ColorTween_get_targetColor_m7FBFE43D0C87958C7F82CE25627ED2A6C1E04EBC_AdjustorThunk (void);
extern void ColorTween_set_targetColor_m0172FC5EED7BEDA58CC0BF38D15484A9E621C9B2_AdjustorThunk (void);
extern void ColorTween_get_tweenMode_mE64B5F00617D95B356E317C0DD27F4E047872840_AdjustorThunk (void);
extern void ColorTween_set_tweenMode_m3EB2248A06FAC8B31730D2C0A7019E83FF3BFA54_AdjustorThunk (void);
extern void ColorTween_get_duration_m0D54F8FF864F645287570B01A7263FDA284A3091_AdjustorThunk (void);
extern void ColorTween_set_duration_mB1EF6D6212E52CE120C294C354188F55A3AAAE56_AdjustorThunk (void);
extern void ColorTween_get_ignoreTimeScale_mB626FF1519AD4745DA9C6F1ACFB06665F7775740_AdjustorThunk (void);
extern void ColorTween_set_ignoreTimeScale_mB859C6558AAA257A7C25E98E91A4C1E1B1F3FADC_AdjustorThunk (void);
extern void ColorTween_TweenValue_m5F8B59F75D4CE627BC5F6E34A1345D41941FDCC6_AdjustorThunk (void);
extern void ColorTween_AddOnChangedCallback_mABFB32C3DFECB9271E2CECA5FE79567F82D56DCF_AdjustorThunk (void);
extern void ColorTween_GetIgnoreTimescale_mF956E0A7BCE90295B6341EB17641A36A9777B8FB_AdjustorThunk (void);
extern void ColorTween_GetDuration_mDF63029E52D00CCFE62AD905D392D54523D3CA76_AdjustorThunk (void);
extern void ColorTween_ValidTarget_m1A0F15E461C7359650F9B26BD2CE50EFCA2EA80A_AdjustorThunk (void);
extern void FloatTween_get_startValue_m50E23F438012DBCDB77F79083F8B3E7C747ECEB3_AdjustorThunk (void);
extern void FloatTween_set_startValue_mFB659473A8FB57C4D0F951DC53D9364ABF5435A5_AdjustorThunk (void);
extern void FloatTween_get_targetValue_mAA60AB9B567184EB6DB1C960F2AED3B934FF5DFE_AdjustorThunk (void);
extern void FloatTween_set_targetValue_m4DD565573FA7B4110EEDA4D524D369858571A0A2_AdjustorThunk (void);
extern void FloatTween_get_duration_mD6CD9915EB7F798B9F07F20E096066B201CCFF73_AdjustorThunk (void);
extern void FloatTween_set_duration_mB561C369977BAE6807268EAA9633B31F530151E9_AdjustorThunk (void);
extern void FloatTween_get_ignoreTimeScale_m40168239ACB9A32B1A87EAF382CBA87FB641743D_AdjustorThunk (void);
extern void FloatTween_set_ignoreTimeScale_mACD465EA2E70BAFBA3FD3FC4FECC4393E17E0786_AdjustorThunk (void);
extern void FloatTween_TweenValue_mF21AE3A616B020B1D351E237D1F3145B508ACB11_AdjustorThunk (void);
extern void FloatTween_AddOnChangedCallback_m07B7D701CF26857FE4E33DE9467716065381EBEE_AdjustorThunk (void);
extern void FloatTween_GetIgnoreTimescale_m39FBA712B845E9E33B886502D2DB92A499D2F6D8_AdjustorThunk (void);
extern void FloatTween_GetDuration_m1D2C252520C4713F105A75162AFAA1A7785C28BE_AdjustorThunk (void);
extern void FloatTween_ValidTarget_mE1A5EA20038733B467C8B810E63BE6617F7DAAFF_AdjustorThunk (void);
extern void RaycastResult_get_gameObject_mABA10AC828B2E6603A6C088A4CCD40932F6AF5FF_AdjustorThunk (void);
extern void RaycastResult_set_gameObject_m3A1B023B578A8142A437B0C27AAE045DED6AAA20_AdjustorThunk (void);
extern void RaycastResult_get_isValid_m4BC5BF4FDB163ED6BC0D8C47B0B059E76C508ADD_AdjustorThunk (void);
extern void RaycastResult_Clear_m00588011F0D28FD65E082C5DAA2E737E3F6AAB9C_AdjustorThunk (void);
extern void RaycastResult_ToString_mF55ECA45850A1CA8A43FC1BA8F932D6F86C60E3E_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[72] = 
{
	{ 0x06000035, ColorBlock_get_normalColor_m7C5A73BBE739FFA030EE1FE10CE78BECB8417126_AdjustorThunk },
	{ 0x06000036, ColorBlock_set_normalColor_m32EB40A0BB6DD89B8816945EF43CFED8A1ED78B9_AdjustorThunk },
	{ 0x06000037, ColorBlock_get_highlightedColor_mDA12AC25C740DF2AFFF086B62795533B4D2ED336_AdjustorThunk },
	{ 0x06000038, ColorBlock_set_highlightedColor_m176DF68369EC1536D089EABD1357AB8EAC33BC88_AdjustorThunk },
	{ 0x06000039, ColorBlock_get_pressedColor_mCEBCE4184CBAE7D33FE3D8AD3FDFA3931FD87C70_AdjustorThunk },
	{ 0x0600003A, ColorBlock_set_pressedColor_m7F357DD0E16A18A84D2DCF039238ADE6695EB5C0_AdjustorThunk },
	{ 0x0600003B, ColorBlock_get_selectedColor_m7C592B6652C94BACF43F0C007AC0093A2543F725_AdjustorThunk },
	{ 0x0600003C, ColorBlock_set_selectedColor_mA1A0ADD43DBE600443C5A1D217922614D77A6C3C_AdjustorThunk },
	{ 0x0600003D, ColorBlock_get_disabledColor_mF470FB3FA48E777F5C70F1C18EF218DB673970E9_AdjustorThunk },
	{ 0x0600003E, ColorBlock_set_disabledColor_m0E9C6689B962550D1C3F15275DF2409FD3C28841_AdjustorThunk },
	{ 0x0600003F, ColorBlock_get_colorMultiplier_m38A25E26F0AB48B6CB04A7A18EF1EAC2AFD2BA28_AdjustorThunk },
	{ 0x06000040, ColorBlock_set_colorMultiplier_m8A321BF2E07D4D184214014C32F5389C8BE06198_AdjustorThunk },
	{ 0x06000041, ColorBlock_get_fadeDuration_m37083141F2C18A45CC211E4683D1903E3A614B1C_AdjustorThunk },
	{ 0x06000042, ColorBlock_set_fadeDuration_m51827B5F57CFEF2FF8DFB03687A301E2F1B3CDCE_AdjustorThunk },
	{ 0x06000044, ColorBlock_Equals_m4A08484E3305C07A0CBE38C0205E21CDF05C88D7_AdjustorThunk },
	{ 0x06000045, ColorBlock_Equals_m4C4BE778C6BEFA266E497540E97CB3201B13FB6A_AdjustorThunk },
	{ 0x06000048, ColorBlock_GetHashCode_m26AE64D514B61123616F54D3FC820A3FE11AA40E_AdjustorThunk },
	{ 0x0600035A, Navigation_get_mode_mB995DE758F5FE0E01F6D54EC5FAC27E85D51E9D9_AdjustorThunk },
	{ 0x0600035B, Navigation_set_mode_m06986291C80ABAB009A7968A3A5C557197B7CAD0_AdjustorThunk },
	{ 0x0600035C, Navigation_get_wrapAround_mD84154C6A5DF2D999F9C68694660BF4C75E42E09_AdjustorThunk },
	{ 0x0600035D, Navigation_set_wrapAround_m88C6E2CE8D45F409B10220931C7574A925478EEF_AdjustorThunk },
	{ 0x0600035E, Navigation_get_selectOnUp_mE2CE90A504C34AE46D8BC9930C7E309F3BD61D05_AdjustorThunk },
	{ 0x0600035F, Navigation_set_selectOnUp_m4FB3B1A6FD96E0F5AF5A029C49150E05520EAEB4_AdjustorThunk },
	{ 0x06000360, Navigation_get_selectOnDown_mDEAD9711CD57EC13A9622AA29E1D95E7FE49B26C_AdjustorThunk },
	{ 0x06000361, Navigation_set_selectOnDown_mFAAAE5BDCD7A658119C96474C108C9A59E62F2D5_AdjustorThunk },
	{ 0x06000362, Navigation_get_selectOnLeft_m7E124CA8B6205001892CA5F8FBCC3CE2A451ACF8_AdjustorThunk },
	{ 0x06000363, Navigation_set_selectOnLeft_mA33AA78D5F7F6BE93490C9EABF182E1D77C0E580_AdjustorThunk },
	{ 0x06000364, Navigation_get_selectOnRight_mA7E6599E6A54419AE4C532F76F4199EFF2188114_AdjustorThunk },
	{ 0x06000365, Navigation_set_selectOnRight_m6B60DBA287E342806BF4CF1AD332DA4EF875BD4A_AdjustorThunk },
	{ 0x06000367, Navigation_Equals_m1462D8660E507B770231FF9CBDAC296C1D751C7A_AdjustorThunk },
	{ 0x06000484, SpriteState_get_highlightedSprite_m695FD2C0827908CBAFFF5D5033FEED380D4219FA_AdjustorThunk },
	{ 0x06000485, SpriteState_set_highlightedSprite_m3B5F7EF5AF584C6917BA3FB7155701F697B6070D_AdjustorThunk },
	{ 0x06000486, SpriteState_get_pressedSprite_mDCEB9F07BDD7C2CFCDC7F7680D05B47EA71965D6_AdjustorThunk },
	{ 0x06000487, SpriteState_set_pressedSprite_m21C5C37D35A794F750D6D4A95F794633B9027602_AdjustorThunk },
	{ 0x06000488, SpriteState_get_selectedSprite_mA85714CC6BF3801A63CC42B026E66CEDFD36949E_AdjustorThunk },
	{ 0x06000489, SpriteState_set_selectedSprite_m00EC0C38B3ADBA12D9524CAE982BE8B21F608A54_AdjustorThunk },
	{ 0x0600048A, SpriteState_get_disabledSprite_m7AF976C63DA03ED035B031D5A98413C39894F50C_AdjustorThunk },
	{ 0x0600048B, SpriteState_set_disabledSprite_mB368418E0E6ED9F220570BC9F066C6B6BF227B13_AdjustorThunk },
	{ 0x0600048C, SpriteState_Equals_m2190A8BFFC45EC86766FC68C808F3DFE18E35827_AdjustorThunk },
	{ 0x0600055D, ColorTween_get_startColor_m17A0327111EF4CEB0C15009D70607065D7B2DD0C_AdjustorThunk },
	{ 0x0600055E, ColorTween_set_startColor_m3FDDCF9C585451522E3DC19AC452B6020931418D_AdjustorThunk },
	{ 0x0600055F, ColorTween_get_targetColor_m7FBFE43D0C87958C7F82CE25627ED2A6C1E04EBC_AdjustorThunk },
	{ 0x06000560, ColorTween_set_targetColor_m0172FC5EED7BEDA58CC0BF38D15484A9E621C9B2_AdjustorThunk },
	{ 0x06000561, ColorTween_get_tweenMode_mE64B5F00617D95B356E317C0DD27F4E047872840_AdjustorThunk },
	{ 0x06000562, ColorTween_set_tweenMode_m3EB2248A06FAC8B31730D2C0A7019E83FF3BFA54_AdjustorThunk },
	{ 0x06000563, ColorTween_get_duration_m0D54F8FF864F645287570B01A7263FDA284A3091_AdjustorThunk },
	{ 0x06000564, ColorTween_set_duration_mB1EF6D6212E52CE120C294C354188F55A3AAAE56_AdjustorThunk },
	{ 0x06000565, ColorTween_get_ignoreTimeScale_mB626FF1519AD4745DA9C6F1ACFB06665F7775740_AdjustorThunk },
	{ 0x06000566, ColorTween_set_ignoreTimeScale_mB859C6558AAA257A7C25E98E91A4C1E1B1F3FADC_AdjustorThunk },
	{ 0x06000567, ColorTween_TweenValue_m5F8B59F75D4CE627BC5F6E34A1345D41941FDCC6_AdjustorThunk },
	{ 0x06000568, ColorTween_AddOnChangedCallback_mABFB32C3DFECB9271E2CECA5FE79567F82D56DCF_AdjustorThunk },
	{ 0x06000569, ColorTween_GetIgnoreTimescale_mF956E0A7BCE90295B6341EB17641A36A9777B8FB_AdjustorThunk },
	{ 0x0600056A, ColorTween_GetDuration_mDF63029E52D00CCFE62AD905D392D54523D3CA76_AdjustorThunk },
	{ 0x0600056B, ColorTween_ValidTarget_m1A0F15E461C7359650F9B26BD2CE50EFCA2EA80A_AdjustorThunk },
	{ 0x0600056D, FloatTween_get_startValue_m50E23F438012DBCDB77F79083F8B3E7C747ECEB3_AdjustorThunk },
	{ 0x0600056E, FloatTween_set_startValue_mFB659473A8FB57C4D0F951DC53D9364ABF5435A5_AdjustorThunk },
	{ 0x0600056F, FloatTween_get_targetValue_mAA60AB9B567184EB6DB1C960F2AED3B934FF5DFE_AdjustorThunk },
	{ 0x06000570, FloatTween_set_targetValue_m4DD565573FA7B4110EEDA4D524D369858571A0A2_AdjustorThunk },
	{ 0x06000571, FloatTween_get_duration_mD6CD9915EB7F798B9F07F20E096066B201CCFF73_AdjustorThunk },
	{ 0x06000572, FloatTween_set_duration_mB561C369977BAE6807268EAA9633B31F530151E9_AdjustorThunk },
	{ 0x06000573, FloatTween_get_ignoreTimeScale_m40168239ACB9A32B1A87EAF382CBA87FB641743D_AdjustorThunk },
	{ 0x06000574, FloatTween_set_ignoreTimeScale_mACD465EA2E70BAFBA3FD3FC4FECC4393E17E0786_AdjustorThunk },
	{ 0x06000575, FloatTween_TweenValue_mF21AE3A616B020B1D351E237D1F3145B508ACB11_AdjustorThunk },
	{ 0x06000576, FloatTween_AddOnChangedCallback_m07B7D701CF26857FE4E33DE9467716065381EBEE_AdjustorThunk },
	{ 0x06000577, FloatTween_GetIgnoreTimescale_m39FBA712B845E9E33B886502D2DB92A499D2F6D8_AdjustorThunk },
	{ 0x06000578, FloatTween_GetDuration_m1D2C252520C4713F105A75162AFAA1A7785C28BE_AdjustorThunk },
	{ 0x06000579, FloatTween_ValidTarget_mE1A5EA20038733B467C8B810E63BE6617F7DAAFF_AdjustorThunk },
	{ 0x060006B4, RaycastResult_get_gameObject_mABA10AC828B2E6603A6C088A4CCD40932F6AF5FF_AdjustorThunk },
	{ 0x060006B5, RaycastResult_set_gameObject_m3A1B023B578A8142A437B0C27AAE045DED6AAA20_AdjustorThunk },
	{ 0x060006B6, RaycastResult_get_isValid_m4BC5BF4FDB163ED6BC0D8C47B0B059E76C508ADD_AdjustorThunk },
	{ 0x060006B7, RaycastResult_Clear_m00588011F0D28FD65E082C5DAA2E737E3F6AAB9C_AdjustorThunk },
	{ 0x060006B8, RaycastResult_ToString_mF55ECA45850A1CA8A43FC1BA8F932D6F86C60E3E_AdjustorThunk },
};
static const int32_t s_InvokerIndices[1765] = 
{
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3967,
	3967,
	3916,
	3348,
	3967,
	3348,
	3348,
	3916,
	3967,
	3313,
	3967,
	3863,
	3916,
	3967,
	3916,
	3313,
	3916,
	3967,
	3967,
	3863,
	3967,
	6270,
	2591,
	3967,
	3967,
	6005,
	5525,
	6218,
	5923,
	2591,
	6218,
	5923,
	2591,
	6218,
	6218,
	3348,
	3348,
	3348,
	3348,
	6256,
	6256,
	6289,
	3867,
	3281,
	3867,
	3281,
	3867,
	3281,
	3867,
	3281,
	3867,
	3281,
	3951,
	3380,
	3951,
	3380,
	6289,
	2591,
	2542,
	5417,
	5417,
	3893,
	3967,
	6270,
	3967,
	6218,
	6218,
	6218,
	5637,
	3967,
	3916,
	3967,
	3916,
	2192,
	2192,
	3396,
	1760,
	3967,
	6270,
	5204,
	5185,
	6218,
	6218,
	5834,
	5830,
	6103,
	6103,
	6103,
	6103,
	6103,
	6103,
	6103,
	6103,
	6103,
	6103,
	6103,
	6289,
	1717,
	1717,
	3967,
	6289,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3951,
	3380,
	3893,
	3313,
	3313,
	1934,
	3967,
	3967,
	3967,
	3967,
	3967,
	3348,
	3348,
	3348,
	3967,
	3348,
	-1,
	3348,
	3348,
	3348,
	3967,
	3047,
	3348,
	3047,
	3348,
	3047,
	3348,
	699,
	2201,
	1368,
	3380,
	3967,
	3048,
	3967,
	3348,
	6289,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3348,
	3348,
	3967,
	3916,
	3348,
	3916,
	3348,
	3967,
	3348,
	3348,
	2171,
	3916,
	3348,
	3967,
	3967,
	3967,
	3276,
	3313,
	3967,
	3863,
	3916,
	3967,
	3916,
	6270,
	3916,
	3348,
	3893,
	3313,
	3893,
	3313,
	3863,
	3276,
	3893,
	3313,
	3893,
	3313,
	3893,
	3313,
	3863,
	3276,
	3863,
	3276,
	3893,
	3313,
	3893,
	3313,
	3951,
	3380,
	3967,
	3967,
	3967,
	6218,
	6218,
	6218,
	6289,
	6270,
	3867,
	3281,
	3863,
	3276,
	3966,
	3400,
	3863,
	3276,
	3967,
	3967,
	3967,
	3967,
	3967,
	3967,
	3967,
	3967,
	3967,
	3893,
	3916,
	3916,
	3967,
	3916,
	3916,
	3916,
	3348,
	3916,
	3916,
	3967,
	3967,
	3967,
	3967,
	3967,
	3313,
	3967,
	3967,
	3967,
	3967,
	3967,
	3967,
	6270,
	3348,
	3348,
	3348,
	3967,
	3967,
	1512,
	3155,
	3938,
	829,
	360,
	5937,
	1366,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	6289,
	3916,
	3893,
	3893,
	3863,
	3276,
	3893,
	3313,
	3899,
	3321,
	3967,
	3916,
	2171,
	3916,
	4584,
	6289,
	6289,
	3967,
	1607,
	3967,
	6270,
	5834,
	5834,
	5834,
	5834,
	5834,
	5834,
	6091,
	6091,
	6289,
	3967,
	3863,
	3916,
	3967,
	3916,
	3348,
	3967,
	3916,
	3348,
	3916,
	3893,
	3313,
	3863,
	3276,
	3863,
	3276,
	3893,
	3313,
	3951,
	3380,
	3863,
	3276,
	3893,
	3313,
	3951,
	3380,
	3951,
	3380,
	3863,
	3276,
	3967,
	6270,
	3916,
	3863,
	3951,
	3380,
	3951,
	3951,
	3916,
	3348,
	3967,
	3967,
	1829,
	3160,
	3967,
	3348,
	3967,
	3967,
	3967,
	3967,
	3967,
	2158,
	2158,
	3348,
	3348,
	4981,
	4340,
	1820,
	2158,
	4363,
	4585,
	3967,
	3967,
	3951,
	3951,
	3951,
	3951,
	3951,
	3951,
	3893,
	1512,
	1805,
	6218,
	6218,
	6218,
	3967,
	6289,
	3916,
	3916,
	3967,
	3916,
	3916,
	3276,
	3863,
	3276,
	3863,
	3916,
	3348,
	3348,
	2158,
	3863,
	3951,
	3380,
	3893,
	3313,
	3916,
	3348,
	3916,
	3348,
	3867,
	3281,
	3863,
	3276,
	3867,
	3281,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3893,
	3313,
	3893,
	3313,
	3893,
	3313,
	3893,
	3313,
	3916,
	3893,
	3313,
	3893,
	3313,
	3863,
	3276,
	3863,
	3960,
	3391,
	3863,
	3255,
	3893,
	3313,
	3893,
	3313,
	3863,
	3893,
	3313,
	3893,
	3313,
	3893,
	3313,
	3967,
	3967,
	3967,
	3916,
	3967,
	3967,
	3967,
	3967,
	3967,
	3276,
	3276,
	6270,
	6218,
	3863,
	3863,
	3863,
	3967,
	3967,
	3155,
	1638,
	2936,
	2591,
	3348,
	3348,
	3047,
	3348,
	3348,
	2901,
	2635,
	3348,
	3348,
	3916,
	3893,
	1831,
	3893,
	1831,
	1594,
	1592,
	1592,
	3276,
	1831,
	3276,
	1831,
	3967,
	3967,
	3967,
	3391,
	3967,
	3967,
	3967,
	3967,
	3348,
	3391,
	3967,
	3863,
	5524,
	5524,
	3313,
	3967,
	3967,
	3313,
	3967,
	3967,
	3967,
	3967,
	3348,
	2186,
	3967,
	2186,
	1232,
	3967,
	3967,
	3348,
	3348,
	3967,
	3348,
	3348,
	3967,
	3967,
	3348,
	3967,
	1934,
	3967,
	3967,
	3951,
	3951,
	3951,
	3951,
	3951,
	3951,
	3893,
	6289,
	3916,
	2169,
	1232,
	333,
	3141,
	3967,
	3967,
	3313,
	3967,
	3863,
	3916,
	3967,
	3916,
	3313,
	3967,
	3863,
	3916,
	3967,
	3916,
	3893,
	3313,
	3951,
	3380,
	3916,
	3967,
	3967,
	3967,
	3967,
	3967,
	3967,
	3967,
	3967,
	1776,
	3963,
	3967,
	3967,
	3967,
	3863,
	3863,
	3863,
	3893,
	3313,
	3951,
	3380,
	3951,
	3380,
	3963,
	3396,
	3893,
	3313,
	3951,
	3380,
	3893,
	3313,
	3951,
	3380,
	3951,
	3380,
	3951,
	3380,
	3967,
	3967,
	3967,
	3967,
	3967,
	3967,
	3967,
	3967,
	3967,
	3380,
	3380,
	3893,
	3313,
	3893,
	3313,
	3916,
	3967,
	3967,
	3967,
	3967,
	3313,
	3967,
	3967,
	3967,
	3893,
	3313,
	3893,
	3313,
	3963,
	3396,
	3963,
	3396,
	3893,
	3313,
	3893,
	3313,
	3967,
	3967,
	3967,
	3967,
	3967,
	3313,
	3967,
	3967,
	3967,
	3967,
	3967,
	3951,
	3380,
	3863,
	3276,
	3863,
	3276,
	3863,
	3276,
	3863,
	3276,
	3863,
	3276,
	3863,
	3276,
	3863,
	3276,
	1934,
	1934,
	136,
	3967,
	3967,
	3967,
	3951,
	3951,
	3951,
	3951,
	3951,
	3951,
	3893,
	3967,
	3967,
	3863,
	3863,
	3276,
	3967,
	3967,
	3951,
	3380,
	3951,
	3380,
	3951,
	3380,
	3951,
	3380,
	3951,
	3380,
	3951,
	3380,
	3893,
	3313,
	3967,
	3967,
	3967,
	3967,
	3967,
	3967,
	3967,
	3916,
	3348,
	3893,
	3313,
	3916,
	3916,
	3967,
	3967,
	3951,
	3951,
	3951,
	3951,
	3951,
	3951,
	3893,
	3967,
	3967,
	3967,
	3967,
	3967,
	3967,
	3115,
	3115,
	3115,
	1771,
	3115,
	918,
	1327,
	879,
	879,
	404,
	3863,
	3967,
	3967,
	-1,
	3967,
	3047,
	3313,
	3967,
	3863,
	3916,
	3967,
	3916,
	3348,
	3967,
	6289,
	6218,
	3916,
	3863,
	6218,
	6218,
	3313,
	2171,
	2171,
	6218,
	5433,
	6218,
	3967,
	3967,
	3893,
	2591,
	3916,
	3967,
	6289,
	3967,
	3348,
	2591,
	3348,
	3348,
	3348,
	3348,
	5660,
	5660,
	5660,
	6139,
	6139,
	6139,
	6139,
	6139,
	6139,
	5240,
	4917,
	6289,
	3967,
	3116,
	3116,
	3116,
	3116,
	3116,
	3116,
	3116,
	3116,
	3967,
	3967,
	3967,
	3967,
	3967,
	3916,
	3863,
	3276,
	3916,
	3967,
	3863,
	3967,
	3967,
	3967,
	1512,
	3047,
	6218,
	6218,
	6091,
	5525,
	5433,
	6091,
	5834,
	3967,
	3916,
	3348,
	3863,
	3276,
	3863,
	3276,
	3047,
	2192,
	3276,
	2192,
	3396,
	3967,
	3967,
	3967,
	3967,
	3967,
	3938,
	3967,
	3967,
	3967,
	3967,
	3916,
	3967,
	3047,
	6218,
	6218,
	5430,
	6198,
	3893,
	3313,
	3863,
	3276,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	6269,
	2590,
	3967,
	3916,
	3916,
	3348,
	3938,
	3372,
	3967,
	3348,
	3967,
	3966,
	3400,
	3964,
	3397,
	3916,
	3938,
	3916,
	3967,
	3967,
	3967,
	3967,
	1512,
	3938,
	3967,
	3967,
	3348,
	3348,
	3967,
	3967,
	3916,
	3348,
	3863,
	3276,
	3863,
	3276,
	3893,
	3313,
	3951,
	3380,
	3863,
	3276,
	3951,
	3380,
	3951,
	3380,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3893,
	3313,
	3893,
	3313,
	3951,
	3380,
	3951,
	3380,
	3916,
	3348,
	3916,
	3963,
	3396,
	3916,
	3967,
	3313,
	3967,
	3967,
	3967,
	3967,
	3967,
	3863,
	3967,
	3967,
	3348,
	3348,
	3348,
	3348,
	3348,
	3396,
	3967,
	3967,
	3396,
	3963,
	3396,
	3951,
	3380,
	3951,
	3380,
	3380,
	3380,
	2199,
	5665,
	3967,
	3863,
	3863,
	3967,
	3967,
	3951,
	3951,
	3951,
	3951,
	3951,
	3951,
	3893,
	3967,
	3967,
	3967,
	4941,
	3967,
	3967,
	4935,
	3862,
	5398,
	3155,
	4315,
	3967,
	3967,
	3916,
	3967,
	3916,
	3348,
	3893,
	3313,
	3967,
	3951,
	3380,
	3380,
	3951,
	3380,
	3893,
	3313,
	3916,
	3348,
	3951,
	3313,
	3967,
	3967,
	3967,
	3967,
	3967,
	3967,
	2198,
	3967,
	3893,
	3863,
	3967,
	3348,
	2217,
	2591,
	3348,
	3348,
	3348,
	3047,
	1742,
	3348,
	3348,
	3916,
	3916,
	3916,
	3916,
	3348,
	1934,
	3916,
	3967,
	3313,
	3967,
	3863,
	3916,
	3967,
	3916,
	6270,
	6263,
	6270,
	6005,
	3914,
	3347,
	3893,
	3313,
	3870,
	3284,
	3952,
	3383,
	3916,
	3348,
	3916,
	3348,
	3863,
	3276,
	3863,
	3276,
	3863,
	3276,
	3863,
	3276,
	3967,
	3916,
	3348,
	3916,
	3967,
	3967,
	3863,
	3863,
	3967,
	3967,
	3967,
	3967,
	3967,
	3893,
	3967,
	1934,
	3057,
	5718,
	2171,
	3916,
	3916,
	3916,
	3916,
	3348,
	1838,
	3348,
	3348,
	3863,
	3863,
	3967,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3967,
	6289,
	5405,
	-1,
	-1,
	3916,
	3348,
	3916,
	3348,
	3893,
	3313,
	3951,
	3380,
	3951,
	3380,
	3863,
	3276,
	3951,
	3380,
	3380,
	3951,
	3380,
	3916,
	3348,
	3951,
	3967,
	3313,
	3967,
	3967,
	3967,
	3967,
	3967,
	3967,
	3967,
	3117,
	2198,
	3967,
	3893,
	3863,
	3967,
	2171,
	2591,
	3348,
	3348,
	3348,
	3916,
	3916,
	3916,
	3916,
	3348,
	1934,
	3916,
	3967,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	2626,
	5595,
	4486,
	5834,
	4163,
	6218,
	6289,
	6289,
	3967,
	3967,
	3916,
	3916,
	3916,
	3967,
	3916,
	3348,
	3916,
	3348,
	3863,
	3276,
	3863,
	3276,
	3893,
	3313,
	3893,
	3313,
	3893,
	3313,
	3863,
	3276,
	3893,
	3313,
	3893,
	3313,
	3893,
	3313,
	3951,
	3380,
	3893,
	3313,
	3951,
	3967,
	3967,
	3967,
	3967,
	3126,
	6190,
	3348,
	3967,
	3967,
	3951,
	3951,
	3951,
	3951,
	3951,
	3951,
	3893,
	6289,
	3916,
	3348,
	3967,
	3313,
	3967,
	3967,
	3967,
	3967,
	3967,
	3967,
	2158,
	3863,
	3276,
	3276,
	1831,
	3276,
	3967,
	3967,
	3348,
	3348,
	3916,
	3967,
	3863,
	3276,
	3967,
	3967,
	3967,
	3348,
	2158,
	3348,
	3348,
	3967,
	3863,
	3916,
	3916,
	3276,
	6289,
	3967,
	2591,
	2591,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	3967,
	6270,
	6289,
	2169,
	468,
	210,
	1425,
	2169,
	1145,
	350,
	3047,
	2169,
	580,
	211,
	2901,
	2169,
	788,
	213,
	3094,
	2169,
	1145,
	350,
	3047,
	2169,
	580,
	211,
	2901,
	3967,
	3348,
	3967,
	3967,
	3967,
	3893,
	3893,
	1824,
	2205,
	3348,
	107,
	281,
	1381,
	3390,
	1280,
	3348,
	2171,
	3348,
	3348,
	6289,
	3348,
	3967,
	3916,
	3967,
	3967,
	3967,
	3348,
	3348,
	3967,
	3348,
	3348,
	3348,
	3967,
	3348,
	3967,
	3348,
	3967,
	3867,
	3281,
	3963,
	3396,
	3863,
	3276,
	239,
	239,
	3348,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	3380,
	3863,
	3951,
	3863,
	3867,
	3281,
	3867,
	3281,
	3893,
	3313,
	3951,
	3380,
	3863,
	3276,
	3380,
	3348,
	3863,
	3951,
	3863,
	3967,
	3951,
	3380,
	3951,
	3380,
	3951,
	3380,
	3863,
	3276,
	3380,
	3348,
	3863,
	3951,
	3863,
	3967,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	3963,
	3396,
	3893,
	3313,
	3348,
	3967,
	3967,
	3863,
	3967,
	3348,
	3916,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3937,
	3371,
	3937,
	3371,
	3863,
	3276,
	3893,
	3313,
	3963,
	3396,
	3963,
	3396,
	3963,
	3396,
	3965,
	3398,
	3965,
	3398,
	3951,
	3380,
	3893,
	3313,
	3963,
	3396,
	3863,
	3276,
	3863,
	3276,
	3893,
	3313,
	3348,
	3863,
	3863,
	3916,
	3916,
	3916,
	3348,
	3916,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	6270,
	6218,
	3863,
	3276,
	3893,
	3313,
	3916,
	3916,
	3348,
	3916,
	3916,
	3863,
	3967,
	3967,
	3863,
	2171,
	3916,
	3348,
	5530,
	2171,
	3863,
	2570,
	3967,
	3967,
	3967,
	3276,
	3967,
	3348,
	3916,
	6289,
	3916,
	3348,
	3967,
	3916,
	3348,
	1981,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3348,
	3967,
	3967,
	-1,
	5834,
	5834,
	5834,
	5834,
	5834,
	5834,
	5834,
	5834,
	5834,
	5834,
	5834,
	5834,
	5834,
	5834,
	5834,
	5834,
	5834,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	5834,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	6289,
	-1,
	-1,
	-1,
	-1,
	6289,
	3967,
	3348,
	3916,
	3893,
	3313,
	3963,
	3396,
	3863,
	2570,
	2570,
	2570,
	3963,
	3963,
	3863,
	3893,
	3131,
	3116,
	2591,
	3967,
	3916,
	3916,
	3348,
	3916,
	3967,
	3967,
	3967,
	6110,
	5532,
	5113,
	5599,
	2171,
	1156,
	3916,
	2570,
	3863,
	3967,
	3967,
	3967,
	3863,
	3967,
	949,
	3348,
	1163,
	2171,
	2884,
	3916,
	3044,
	3044,
	4649,
	3348,
	3348,
	2570,
	3967,
	3916,
	2171,
	3967,
	3916,
	3348,
	3893,
	3313,
	3967,
	3863,
	3863,
	3044,
	1281,
	3967,
	3863,
	3863,
	3967,
	3967,
	3893,
	3863,
	3276,
	3863,
	3276,
	3951,
	3380,
	3951,
	3380,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3863,
	3967,
	2171,
	3863,
	3863,
	3967,
	3967,
	3967,
	3863,
	1311,
	3863,
	3963,
	3863,
	3967,
	3863,
	3313,
	3863,
	3348,
	3916,
	3967,
	3863,
	3276,
	3863,
	3276,
	3967,
	3863,
	3863,
	3863,
	3967,
	3967,
	3967,
	1311,
	3967,
	3916,
	3916,
	3348,
	3863,
	3967,
	3916,
	6218,
	6270,
	6218,
	6289,
	2171,
	3916,
	3893,
	3893,
	3893,
	3916,
	3916,
	3967,
	3967,
	3967,
	3967,
	3967,
	3967,
	2171,
	3967,
	3916,
	3893,
	3893,
	3899,
	3321,
	3893,
	3313,
	456,
	2171,
	1613,
	3967,
	6289,
	3967,
	3967,
	3967,
	3967,
	3967,
	3863,
	3967,
	3967,
	3967,
	3967,
	3967,
	3967,
	3863,
	3967,
};
static const Il2CppTokenRangePair s_rgctxIndices[16] = 
{
	{ 0x02000077, { 8, 9 } },
	{ 0x02000078, { 17, 12 } },
	{ 0x02000088, { 29, 26 } },
	{ 0x0200008F, { 55, 6 } },
	{ 0x02000090, { 61, 1 } },
	{ 0x0600008E, { 0, 3 } },
	{ 0x060002EC, { 3, 1 } },
	{ 0x06000452, { 4, 3 } },
	{ 0x06000453, { 7, 1 } },
	{ 0x0600060B, { 62, 2 } },
	{ 0x0600062F, { 64, 4 } },
	{ 0x06000630, { 68, 1 } },
	{ 0x06000631, { 69, 1 } },
	{ 0x06000632, { 70, 1 } },
	{ 0x06000633, { 71, 1 } },
	{ 0x06000634, { 72, 1 } },
};
static const Il2CppRGCTXDefinition s_rgctxValues[73] = 
{
	{ (Il2CppRGCTXDataType)3, 29338 },
	{ (Il2CppRGCTXDataType)2, 99 },
	{ (Il2CppRGCTXDataType)3, 29134 },
	{ (Il2CppRGCTXDataType)2, 175 },
	{ (Il2CppRGCTXDataType)3, 9923 },
	{ (Il2CppRGCTXDataType)2, 2459 },
	{ (Il2CppRGCTXDataType)3, 9922 },
	{ (Il2CppRGCTXDataType)2, 269 },
	{ (Il2CppRGCTXDataType)3, 15301 },
	{ (Il2CppRGCTXDataType)2, 3847 },
	{ (Il2CppRGCTXDataType)3, 21623 },
	{ (Il2CppRGCTXDataType)3, 21624 },
	{ (Il2CppRGCTXDataType)3, 15217 },
	{ (Il2CppRGCTXDataType)2, 5411 },
	{ (Il2CppRGCTXDataType)3, 24619 },
	{ (Il2CppRGCTXDataType)2, 4717 },
	{ (Il2CppRGCTXDataType)3, 21622 },
	{ (Il2CppRGCTXDataType)3, 21619 },
	{ (Il2CppRGCTXDataType)3, 21620 },
	{ (Il2CppRGCTXDataType)3, 22877 },
	{ (Il2CppRGCTXDataType)2, 4998 },
	{ (Il2CppRGCTXDataType)3, 22873 },
	{ (Il2CppRGCTXDataType)3, 25491 },
	{ (Il2CppRGCTXDataType)3, 21621 },
	{ (Il2CppRGCTXDataType)3, 22875 },
	{ (Il2CppRGCTXDataType)3, 24617 },
	{ (Il2CppRGCTXDataType)3, 22874 },
	{ (Il2CppRGCTXDataType)2, 648 },
	{ (Il2CppRGCTXDataType)3, 22876 },
	{ (Il2CppRGCTXDataType)3, 12700 },
	{ (Il2CppRGCTXDataType)3, 15293 },
	{ (Il2CppRGCTXDataType)3, 15298 },
	{ (Il2CppRGCTXDataType)3, 3818 },
	{ (Il2CppRGCTXDataType)3, 12702 },
	{ (Il2CppRGCTXDataType)3, 3820 },
	{ (Il2CppRGCTXDataType)3, 12701 },
	{ (Il2CppRGCTXDataType)3, 3822 },
	{ (Il2CppRGCTXDataType)3, 12706 },
	{ (Il2CppRGCTXDataType)3, 12705 },
	{ (Il2CppRGCTXDataType)3, 12703 },
	{ (Il2CppRGCTXDataType)3, 15294 },
	{ (Il2CppRGCTXDataType)3, 3819 },
	{ (Il2CppRGCTXDataType)3, 15295 },
	{ (Il2CppRGCTXDataType)3, 15299 },
	{ (Il2CppRGCTXDataType)3, 15296 },
	{ (Il2CppRGCTXDataType)3, 3821 },
	{ (Il2CppRGCTXDataType)3, 15300 },
	{ (Il2CppRGCTXDataType)3, 3823 },
	{ (Il2CppRGCTXDataType)3, 21693 },
	{ (Il2CppRGCTXDataType)3, 12704 },
	{ (Il2CppRGCTXDataType)3, 15297 },
	{ (Il2CppRGCTXDataType)2, 3881 },
	{ (Il2CppRGCTXDataType)3, 15292 },
	{ (Il2CppRGCTXDataType)2, 1973 },
	{ (Il2CppRGCTXDataType)3, 3817 },
	{ (Il2CppRGCTXDataType)2, 1332 },
	{ (Il2CppRGCTXDataType)3, 455 },
	{ (Il2CppRGCTXDataType)3, 23666 },
	{ (Il2CppRGCTXDataType)2, 690 },
	{ (Il2CppRGCTXDataType)3, 23665 },
	{ (Il2CppRGCTXDataType)2, 5165 },
	{ (Il2CppRGCTXDataType)2, 784 },
	{ (Il2CppRGCTXDataType)2, 120 },
	{ (Il2CppRGCTXDataType)1, 120 },
	{ (Il2CppRGCTXDataType)3, 29091 },
	{ (Il2CppRGCTXDataType)2, 117 },
	{ (Il2CppRGCTXDataType)1, 117 },
	{ (Il2CppRGCTXDataType)3, 10630 },
	{ (Il2CppRGCTXDataType)3, 29064 },
	{ (Il2CppRGCTXDataType)2, 118 },
	{ (Il2CppRGCTXDataType)3, 29093 },
	{ (Il2CppRGCTXDataType)3, 29090 },
	{ (Il2CppRGCTXDataType)3, 29062 },
};
extern const CustomAttributesCacheGenerator g_UnityEngine_UI_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_UI_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_UI_CodeGenModule = 
{
	"UnityEngine.UI.dll",
	1765,
	s_methodPointers,
	72,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	16,
	s_rgctxIndices,
	73,
	s_rgctxValues,
	NULL,
	g_UnityEngine_UI_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

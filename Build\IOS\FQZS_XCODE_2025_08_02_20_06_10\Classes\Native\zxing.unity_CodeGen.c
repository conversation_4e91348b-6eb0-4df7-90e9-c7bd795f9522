﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void ZXing.OneD.OneDReader::.cctor()
extern void OneDReader__cctor_m6D1E3602B2AF2FD45287C0D1302B7E181E1C02B7 (void);
// 0x00000002 ZXing.Common.BitMatrix ZXing.Writer::encode(System.String,ZXing.BarcodeFormat,System.Int32,System.Int32,System.Collections.Generic.IDictionary`2<ZXing.EncodeHintType,System.Object>)
// 0x00000003 System.Void ZXing.MultiFormatWriter::.cctor()
extern void MultiFormatWriter__cctor_m8A083BA385E9647A84F3129F023FBA3E4C78478E (void);
// 0x00000004 ZXing.Common.BitMatrix ZXing.MultiFormatWriter::encode(System.String,ZXing.BarcodeFormat,System.Int32,System.Int32,System.Collections.Generic.IDictionary`2<ZXing.EncodeHintType,System.Object>)
extern void MultiFormatWriter_encode_m28DB2A3C831F83DF837FB6688B183BF67D1349B6 (void);
// 0x00000005 System.Void ZXing.MultiFormatWriter::.ctor()
extern void MultiFormatWriter__ctor_mF916F2BE29FE5C6456F1386E059554E36BE0D59E (void);
// 0x00000006 ZXing.Writer ZXing.MultiFormatWriter::<.cctor>b__1()
extern void MultiFormatWriter_U3C_cctorU3Eb__1_m9BF9719F06C4359A081BA925DFC7026F62A24B42 (void);
// 0x00000007 ZXing.Writer ZXing.MultiFormatWriter::<.cctor>b__2()
extern void MultiFormatWriter_U3C_cctorU3Eb__2_mCFB6E6C9A708EA2DD0625555C68E15701CD7D9EF (void);
// 0x00000008 ZXing.Writer ZXing.MultiFormatWriter::<.cctor>b__3()
extern void MultiFormatWriter_U3C_cctorU3Eb__3_mCCF709582053223EE7637442358F3D927B67F5CD (void);
// 0x00000009 ZXing.Writer ZXing.MultiFormatWriter::<.cctor>b__4()
extern void MultiFormatWriter_U3C_cctorU3Eb__4_m2AD4B9976A19005FF355508080F0A125122EB9E3 (void);
// 0x0000000A ZXing.Writer ZXing.MultiFormatWriter::<.cctor>b__5()
extern void MultiFormatWriter_U3C_cctorU3Eb__5_m8F8B7E2DFA4EAD798C7291C3AEDB325F93377977 (void);
// 0x0000000B ZXing.Writer ZXing.MultiFormatWriter::<.cctor>b__6()
extern void MultiFormatWriter_U3C_cctorU3Eb__6_m58FB4941EAA96FB85E7FF677AB4C543B5AC07717 (void);
// 0x0000000C ZXing.Writer ZXing.MultiFormatWriter::<.cctor>b__7()
extern void MultiFormatWriter_U3C_cctorU3Eb__7_m6EFD0B69369259690E5E94952881E718E816DC71 (void);
// 0x0000000D ZXing.Writer ZXing.MultiFormatWriter::<.cctor>b__8()
extern void MultiFormatWriter_U3C_cctorU3Eb__8_m694B3D8CBA0A0CB755006BBB73B34C46F91E74E5 (void);
// 0x0000000E ZXing.Writer ZXing.MultiFormatWriter::<.cctor>b__9()
extern void MultiFormatWriter_U3C_cctorU3Eb__9_mBC14C543C6798A75DBEFA79D06DE1FDE8D7145BF (void);
// 0x0000000F ZXing.Writer ZXing.MultiFormatWriter::<.cctor>b__a()
extern void MultiFormatWriter_U3C_cctorU3Eb__a_m1886F305C81AEE7EE4F19F55D1937DBF8AA6C8BD (void);
// 0x00000010 ZXing.Writer ZXing.MultiFormatWriter::<.cctor>b__b()
extern void MultiFormatWriter_U3C_cctorU3Eb__b_m727545DF2B142F5C49440CB85DB20E147D8694BA (void);
// 0x00000011 ZXing.Writer ZXing.MultiFormatWriter::<.cctor>b__c()
extern void MultiFormatWriter_U3C_cctorU3Eb__c_mD672F1DCFB1A9E61EB6B11B35267FD5F25176810 (void);
// 0x00000012 ZXing.Writer ZXing.MultiFormatWriter::<.cctor>b__d()
extern void MultiFormatWriter_U3C_cctorU3Eb__d_m6CBCD20529F0ABC8302924657F268BAB8C06C57D (void);
// 0x00000013 ZXing.Writer ZXing.MultiFormatWriter::<.cctor>b__e()
extern void MultiFormatWriter_U3C_cctorU3Eb__e_m7A1E58FD349C798C99C97EEBC34D87DCA3C0FC9D (void);
// 0x00000014 ZXing.Writer ZXing.MultiFormatWriter::<.cctor>b__f()
extern void MultiFormatWriter_U3C_cctorU3Eb__f_m65E0C78DAA9B39A6C97C7CE142AF5C5357025165 (void);
// 0x00000015 System.Char ZXing.Datamatrix.Encoder.HighLevelEncoder::randomize253State(System.Char,System.Int32)
extern void HighLevelEncoder_randomize253State_mF9E054AB37E308CA777D1C2C88605EA40999C069 (void);
// 0x00000016 System.String ZXing.Datamatrix.Encoder.HighLevelEncoder::encodeHighLevel(System.String,ZXing.Datamatrix.Encoder.SymbolShapeHint,ZXing.Dimension,ZXing.Dimension,System.Int32)
extern void HighLevelEncoder_encodeHighLevel_m7508AE845410E8A7ADD53CF4A032632C044CE70F (void);
// 0x00000017 System.Int32 ZXing.Datamatrix.Encoder.HighLevelEncoder::lookAheadTest(System.String,System.Int32,System.Int32)
extern void HighLevelEncoder_lookAheadTest_mE924E6B480D3F76CEF50176348C81A383AADADFA (void);
// 0x00000018 System.Int32 ZXing.Datamatrix.Encoder.HighLevelEncoder::findMinimums(System.Single[],System.Int32[],System.Int32,System.Byte[])
extern void HighLevelEncoder_findMinimums_mDE70CF486AF6EB635C88BAD39CEDA7FC6790B1C9 (void);
// 0x00000019 System.Int32 ZXing.Datamatrix.Encoder.HighLevelEncoder::getMinimumCount(System.Byte[])
extern void HighLevelEncoder_getMinimumCount_m2EA959BFC2BA6BBEC6065AAB0591AB79C9A6CF48 (void);
// 0x0000001A System.Boolean ZXing.Datamatrix.Encoder.HighLevelEncoder::isDigit(System.Char)
extern void HighLevelEncoder_isDigit_m1F18210FC1FF8AEFE35643C5FAF4ED8CB41073AD (void);
// 0x0000001B System.Boolean ZXing.Datamatrix.Encoder.HighLevelEncoder::isExtendedASCII(System.Char)
extern void HighLevelEncoder_isExtendedASCII_m78EEBBBE80083BD912AAEF28F25D557A6141B80D (void);
// 0x0000001C System.Boolean ZXing.Datamatrix.Encoder.HighLevelEncoder::isNativeC40(System.Char)
extern void HighLevelEncoder_isNativeC40_m8515CA4F7D9F0C06B882189EC0D25FF7E3CD79FC (void);
// 0x0000001D System.Boolean ZXing.Datamatrix.Encoder.HighLevelEncoder::isNativeText(System.Char)
extern void HighLevelEncoder_isNativeText_m3A8AD615D070CF5A65ED43A778D8405009E68C53 (void);
// 0x0000001E System.Boolean ZXing.Datamatrix.Encoder.HighLevelEncoder::isNativeX12(System.Char)
extern void HighLevelEncoder_isNativeX12_m583597CC0CA875CEEB15A59D900ABF49D6D1EFD0 (void);
// 0x0000001F System.Boolean ZXing.Datamatrix.Encoder.HighLevelEncoder::isX12TermSep(System.Char)
extern void HighLevelEncoder_isX12TermSep_mF866000CFAC5E9F8EA58AAE248BDE16D515FF5A7 (void);
// 0x00000020 System.Boolean ZXing.Datamatrix.Encoder.HighLevelEncoder::isNativeEDIFACT(System.Char)
extern void HighLevelEncoder_isNativeEDIFACT_m8B725FF89DA849A8BF5C6420CDE347084C37C76D (void);
// 0x00000021 System.Boolean ZXing.Datamatrix.Encoder.HighLevelEncoder::isSpecialB256(System.Char)
extern void HighLevelEncoder_isSpecialB256_m35DAF20B0AED3F02D4CD05BE95428D3C56FEDB08 (void);
// 0x00000022 System.Int32 ZXing.Datamatrix.Encoder.HighLevelEncoder::determineConsecutiveDigitCount(System.String,System.Int32)
extern void HighLevelEncoder_determineConsecutiveDigitCount_m1830B626B2716A28E81E0375C2E907A879885EF3 (void);
// 0x00000023 System.Void ZXing.Datamatrix.Encoder.HighLevelEncoder::illegalCharacter(System.Char)
extern void HighLevelEncoder_illegalCharacter_m246DB50947B6A720135A793EA1D12E2318B816EE (void);
// 0x00000024 System.Void ZXing.Datamatrix.Encoder.Encoder::encode(ZXing.Datamatrix.Encoder.EncoderContext)
// 0x00000025 System.Int32 ZXing.Datamatrix.Encoder.ASCIIEncoder::get_EncodingMode()
extern void ASCIIEncoder_get_EncodingMode_m714E35CAA0585618B3F205F66C106C498E3967C8 (void);
// 0x00000026 System.Void ZXing.Datamatrix.Encoder.ASCIIEncoder::encode(ZXing.Datamatrix.Encoder.EncoderContext)
extern void ASCIIEncoder_encode_m9EC2E92478B8CC63E1F830402E8F23BE553D65B2 (void);
// 0x00000027 System.Char ZXing.Datamatrix.Encoder.ASCIIEncoder::encodeASCIIDigits(System.Char,System.Char)
extern void ASCIIEncoder_encodeASCIIDigits_m4962E90FA890451AE00012D51C12C63749D5EEDC (void);
// 0x00000028 System.Void ZXing.Datamatrix.Encoder.ASCIIEncoder::.ctor()
extern void ASCIIEncoder__ctor_m34D0BC4A08D4C8263CC4353277AF1456053047FC (void);
// 0x00000029 System.Int32 ZXing.Common.ECI::get_Value()
extern void ECI_get_Value_mAFCE9CEA1059F20F46DC146B9C91773045B1DE12 (void);
// 0x0000002A System.Void ZXing.Common.ECI::.ctor(System.Int32)
extern void ECI__ctor_mBB64D9D5E39ECAA0859EA90342C7175A38CC87C9 (void);
// 0x0000002B System.Void ZXing.Common.CharacterSetECI::.cctor()
extern void CharacterSetECI__cctor_m5DE82C5F2D8C60A448F9BDBBA265B5BBB6B7A145 (void);
// 0x0000002C System.Void ZXing.Common.CharacterSetECI::.ctor(System.Int32,System.String)
extern void CharacterSetECI__ctor_mB3A4C7F7804A00E116A1176AC1D86D3AC7E649C4 (void);
// 0x0000002D System.Void ZXing.Common.CharacterSetECI::addCharacterSet(System.Int32,System.String)
extern void CharacterSetECI_addCharacterSet_m84E203903A28D76EBDE6B7B08B870F2103CA1BF8 (void);
// 0x0000002E System.Void ZXing.Common.CharacterSetECI::addCharacterSet(System.Int32,System.String[])
extern void CharacterSetECI_addCharacterSet_m0CD83D58AF9CDE22ABAF7CEEF68C869ED2483CEC (void);
// 0x0000002F ZXing.Common.CharacterSetECI ZXing.Common.CharacterSetECI::getCharacterSetECIByName(System.String)
extern void CharacterSetECI_getCharacterSetECIByName_m27748548BB193DCC6BE9A858BC685AD8CDD94463 (void);
// 0x00000030 ZXing.BarcodeFormat ZXing.BarcodeWriterGeneric`1::get_Format()
// 0x00000031 System.Void ZXing.BarcodeWriterGeneric`1::set_Format(ZXing.BarcodeFormat)
// 0x00000032 ZXing.Common.EncodingOptions ZXing.BarcodeWriterGeneric`1::get_Options()
// 0x00000033 System.Void ZXing.BarcodeWriterGeneric`1::set_Options(ZXing.Common.EncodingOptions)
// 0x00000034 ZXing.Writer ZXing.BarcodeWriterGeneric`1::get_Encoder()
// 0x00000035 ZXing.Rendering.IBarcodeRenderer`1<TOutput> ZXing.BarcodeWriterGeneric`1::get_Renderer()
// 0x00000036 System.Void ZXing.BarcodeWriterGeneric`1::set_Renderer(ZXing.Rendering.IBarcodeRenderer`1<TOutput>)
// 0x00000037 ZXing.Common.BitMatrix ZXing.BarcodeWriterGeneric`1::Encode(System.String)
// 0x00000038 TOutput ZXing.BarcodeWriterGeneric`1::Write(System.String)
// 0x00000039 System.Void ZXing.BarcodeWriterGeneric`1::.ctor()
// 0x0000003A TOutput ZXing.Rendering.IBarcodeRenderer`1::Render(ZXing.Common.BitMatrix,ZXing.BarcodeFormat,System.String,ZXing.Common.EncodingOptions)
// 0x0000003B System.Void ZXing.OneD.ITFReader::.cctor()
extern void ITFReader__cctor_m27D12D3B57468F7788496207D5C6A2CDA4C377BD (void);
// 0x0000003C ZXing.Common.BitMatrix ZXing.OneD.OneDimensionalCodeWriter::encode(System.String,ZXing.BarcodeFormat,System.Int32,System.Int32,System.Collections.Generic.IDictionary`2<ZXing.EncodeHintType,System.Object>)
extern void OneDimensionalCodeWriter_encode_m8A45D09C89C3B08A0D18C14A1C91222437B96BF8 (void);
// 0x0000003D ZXing.Common.BitMatrix ZXing.OneD.OneDimensionalCodeWriter::renderResult(System.Boolean[],System.Int32,System.Int32,System.Int32)
extern void OneDimensionalCodeWriter_renderResult_mDDD0E5ACF8A3E7A17F7BADC3390295F9B086BED7 (void);
// 0x0000003E System.Int32 ZXing.OneD.OneDimensionalCodeWriter::appendPattern(System.Boolean[],System.Int32,System.Int32[],System.Boolean)
extern void OneDimensionalCodeWriter_appendPattern_m83B2498301EC73675ED78BB4362C87132000AAEE (void);
// 0x0000003F System.Int32 ZXing.OneD.OneDimensionalCodeWriter::get_DefaultMargin()
extern void OneDimensionalCodeWriter_get_DefaultMargin_mF43B6DFDAC4BC9DED66A4C44F7D3C7DB1D823552 (void);
// 0x00000040 System.Boolean[] ZXing.OneD.OneDimensionalCodeWriter::encode(System.String)
// 0x00000041 System.Void ZXing.OneD.OneDimensionalCodeWriter::.ctor()
extern void OneDimensionalCodeWriter__ctor_mF58C4F4C7A1EB5474A7F4E3E9F1DC26238668684 (void);
// 0x00000042 ZXing.Common.BitMatrix ZXing.OneD.Code93Writer::encode(System.String,ZXing.BarcodeFormat,System.Int32,System.Int32,System.Collections.Generic.IDictionary`2<ZXing.EncodeHintType,System.Object>)
extern void Code93Writer_encode_mC4471516DB40F5DB24DE44051C3E03731B280116 (void);
// 0x00000043 System.Boolean[] ZXing.OneD.Code93Writer::encode(System.String)
extern void Code93Writer_encode_mAEF79B30AA74DB5BEBC1F1EE79179487D629CB65 (void);
// 0x00000044 System.Void ZXing.OneD.Code93Writer::toIntArray(System.Int32,System.Int32[])
extern void Code93Writer_toIntArray_m300EC3AA432386CA5898DF7E7149CBAA4F67E290 (void);
// 0x00000045 System.Int32 ZXing.OneD.Code93Writer::appendPattern(System.Boolean[],System.Int32,System.Int32[])
extern void Code93Writer_appendPattern_mE538EB4AACA3662D637B87E0DD0F967114AC8990 (void);
// 0x00000046 System.Int32 ZXing.OneD.Code93Writer::computeChecksumIndex(System.String,System.Int32)
extern void Code93Writer_computeChecksumIndex_m2A3CB58B6502F7F5C65F29A93EDFEE952441B605 (void);
// 0x00000047 System.Void ZXing.OneD.Code93Writer::.ctor()
extern void Code93Writer__ctor_m6A654D1B59782AB2969FB09ACBFE613F56F5468F (void);
// 0x00000048 System.Void ZXing.OneD.Code93Reader::.cctor()
extern void Code93Reader__cctor_m4D3DF07AC9D6B7FBB241DF61E95AEB0BA2CC4F76 (void);
// 0x00000049 System.Boolean[] ZXing.OneD.CodaBarWriter::encode(System.String)
extern void CodaBarWriter_encode_m725C160ED9BBB89251897D621CA4FAD5E5190A50 (void);
// 0x0000004A System.Void ZXing.OneD.CodaBarWriter::.ctor()
extern void CodaBarWriter__ctor_m183028AC9C0CB274EAFE6344799605473AF8A95F (void);
// 0x0000004B System.Void ZXing.OneD.CodaBarWriter::.cctor()
extern void CodaBarWriter__cctor_m1322049CFC302604F49C4DE2D707610F20B2C486 (void);
// 0x0000004C System.Boolean ZXing.OneD.CodaBarReader::arrayContains(System.Char[],System.Char)
extern void CodaBarReader_arrayContains_mDC3E8721E012167C11398217812EE009CED0F40B (void);
// 0x0000004D System.Void ZXing.OneD.CodaBarReader::.cctor()
extern void CodaBarReader__cctor_mD9BB914030EF31AE39CD9D360E093D0B24765FFB (void);
// 0x0000004E System.Void ZXing.Common.ReedSolomon.ReedSolomonEncoder::.ctor(ZXing.Common.ReedSolomon.GenericGF)
extern void ReedSolomonEncoder__ctor_m4C5A8CB6AC110CFAF0D6A55B8F506F91DD6F6FEE (void);
// 0x0000004F ZXing.Common.ReedSolomon.GenericGFPoly ZXing.Common.ReedSolomon.ReedSolomonEncoder::buildGenerator(System.Int32)
extern void ReedSolomonEncoder_buildGenerator_m723B5228DECFF63C91CC0DC22B35566A1DCA7C15 (void);
// 0x00000050 System.Void ZXing.Common.ReedSolomon.ReedSolomonEncoder::encode(System.Int32[],System.Int32)
extern void ReedSolomonEncoder_encode_m29C07346741C8580CFD7239F69658B502BF8E6B1 (void);
// 0x00000051 System.Int32 ZXing.Common.BitArray::get_Size()
extern void BitArray_get_Size_mD964E9FCC5279F27EB40E08909551B8EFBCF29A2 (void);
// 0x00000052 System.Int32 ZXing.Common.BitArray::get_SizeInBytes()
extern void BitArray_get_SizeInBytes_mAFEE86E02252558B4A4517335B1F3B08850E44A7 (void);
// 0x00000053 System.Boolean ZXing.Common.BitArray::get_Item(System.Int32)
extern void BitArray_get_Item_mE788F08F574037B82629D832110395285ECFE0E1 (void);
// 0x00000054 System.Void ZXing.Common.BitArray::.ctor()
extern void BitArray__ctor_mCEEB282219C2D49A489D28D31D70F2A86355AB09 (void);
// 0x00000055 System.Void ZXing.Common.BitArray::.ctor(System.Int32)
extern void BitArray__ctor_m33A70C8FFF7CF28ADB28C2D6F992F194D8E4994A (void);
// 0x00000056 System.Void ZXing.Common.BitArray::ensureCapacity(System.Int32)
extern void BitArray_ensureCapacity_m5C759EB5AED39709A29DEC5243E73B8801A411E9 (void);
// 0x00000057 System.Void ZXing.Common.BitArray::setBulk(System.Int32,System.Int32)
extern void BitArray_setBulk_m4AA80DED9A6845D7C4680D34A91D1538955658B6 (void);
// 0x00000058 System.Void ZXing.Common.BitArray::clear()
extern void BitArray_clear_m9AD44A1693CB93F9047850B34249B9236721B569 (void);
// 0x00000059 System.Void ZXing.Common.BitArray::appendBit(System.Boolean)
extern void BitArray_appendBit_m9B4DF1D4833FA20CA1CEAD33C139477850C6A5A0 (void);
// 0x0000005A System.Int32[] ZXing.Common.BitArray::get_Array()
extern void BitArray_get_Array_m0EC18F2707C5BB3129234F18E56FE2483B96A204 (void);
// 0x0000005B System.Void ZXing.Common.BitArray::appendBits(System.Int32,System.Int32)
extern void BitArray_appendBits_m963D72E7F15E2BB9BC89F1BA216707C1D45C5D05 (void);
// 0x0000005C System.Void ZXing.Common.BitArray::appendBitArray(ZXing.Common.BitArray)
extern void BitArray_appendBitArray_m7054CCE50930C03002F1FA6944378D9B31D1FEFF (void);
// 0x0000005D System.Void ZXing.Common.BitArray::xor(ZXing.Common.BitArray)
extern void BitArray_xor_m52F73D9215A7634FE29A6793E506521DAB9A403D (void);
// 0x0000005E System.Void ZXing.Common.BitArray::toBytes(System.Int32,System.Byte[],System.Int32,System.Int32)
extern void BitArray_toBytes_m34696A7B3E14D80181CF38D4A69773C96EC40201 (void);
// 0x0000005F System.Int32[] ZXing.Common.BitArray::makeArray(System.Int32)
extern void BitArray_makeArray_m11F0D7B6BBB8E51A208C92D02CB224D71A5B7625 (void);
// 0x00000060 System.Boolean ZXing.Common.BitArray::Equals(System.Object)
extern void BitArray_Equals_mD5BC196EA999A84465474E9EFF43F595997F9C6F (void);
// 0x00000061 System.Int32 ZXing.Common.BitArray::GetHashCode()
extern void BitArray_GetHashCode_m8C96098E96BEE8684D2CE308251B6009CBF66F0D (void);
// 0x00000062 System.String ZXing.Common.BitArray::ToString()
extern void BitArray_ToString_mB90DE542AE96C62EDE9633BB86A6B396EDEEAC13 (void);
// 0x00000063 System.Void ZXing.Common.BitArray::.cctor()
extern void BitArray__cctor_mFC6914984B94AAD20C0AE3A25640B01F41B989D1 (void);
// 0x00000064 System.Void ZXing.SupportClass::Fill(T[],T)
// 0x00000065 System.String ZXing.SupportClass::ToBinaryString(System.Int32)
extern void SupportClass_ToBinaryString_m14232FBE8FC13E6A3BD4E6FA90102F9DB7FC99D0 (void);
// 0x00000066 ZXing.Common.BitMatrix ZXing.OneD.ITFWriter::encode(System.String,ZXing.BarcodeFormat,System.Int32,System.Int32,System.Collections.Generic.IDictionary`2<ZXing.EncodeHintType,System.Object>)
extern void ITFWriter_encode_mD8B944561937D8E17DC30274D4D622635508EB77 (void);
// 0x00000067 System.Boolean[] ZXing.OneD.ITFWriter::encode(System.String)
extern void ITFWriter_encode_mD74B04A6B6D6DB45A19074E27D71296C14B1A3FD (void);
// 0x00000068 System.Void ZXing.OneD.ITFWriter::.ctor()
extern void ITFWriter__ctor_m73DA1942D57B81BE0E174EABC3F978A8370FD766 (void);
// 0x00000069 System.Void ZXing.OneD.ITFWriter::.cctor()
extern void ITFWriter__cctor_mA97F915E025509AF54DC85B58EA40BA4AC0261C4 (void);
// 0x0000006A System.Void ZXing.Common.ReedSolomon.GenericGFPoly::.ctor(ZXing.Common.ReedSolomon.GenericGF,System.Int32[])
extern void GenericGFPoly__ctor_m9B08B4172331490D9D93EFE067D88E72884B69C6 (void);
// 0x0000006B System.Int32[] ZXing.Common.ReedSolomon.GenericGFPoly::get_Coefficients()
extern void GenericGFPoly_get_Coefficients_m0E76A2E1FC8BEEACE29EFB7A324CD6620A2390C4 (void);
// 0x0000006C System.Int32 ZXing.Common.ReedSolomon.GenericGFPoly::get_Degree()
extern void GenericGFPoly_get_Degree_m9B6DD73F4F3C591CA67ADBB3B9C7588702100AAB (void);
// 0x0000006D System.Boolean ZXing.Common.ReedSolomon.GenericGFPoly::get_isZero()
extern void GenericGFPoly_get_isZero_m30FB7DDD70F78EF4F925DC8A74C5A9EEA3BC8574 (void);
// 0x0000006E System.Int32 ZXing.Common.ReedSolomon.GenericGFPoly::getCoefficient(System.Int32)
extern void GenericGFPoly_getCoefficient_m0AD98EB4F854C754ADA15771642A945CEC5AA349 (void);
// 0x0000006F ZXing.Common.ReedSolomon.GenericGFPoly ZXing.Common.ReedSolomon.GenericGFPoly::addOrSubtract(ZXing.Common.ReedSolomon.GenericGFPoly)
extern void GenericGFPoly_addOrSubtract_m229B55C638590AA7CDBA81585E307EFB5CDF5E16 (void);
// 0x00000070 ZXing.Common.ReedSolomon.GenericGFPoly ZXing.Common.ReedSolomon.GenericGFPoly::multiply(ZXing.Common.ReedSolomon.GenericGFPoly)
extern void GenericGFPoly_multiply_m1DE669F0C60F7E6156222510FD722752D4D99043 (void);
// 0x00000071 ZXing.Common.ReedSolomon.GenericGFPoly ZXing.Common.ReedSolomon.GenericGFPoly::multiplyByMonomial(System.Int32,System.Int32)
extern void GenericGFPoly_multiplyByMonomial_m0F2111926793AAD668C851CE31EAE7CF40936B1B (void);
// 0x00000072 ZXing.Common.ReedSolomon.GenericGFPoly[] ZXing.Common.ReedSolomon.GenericGFPoly::divide(ZXing.Common.ReedSolomon.GenericGFPoly)
extern void GenericGFPoly_divide_m99562A309982EDBB814853C6BB943704E2DBF361 (void);
// 0x00000073 System.String ZXing.Common.ReedSolomon.GenericGFPoly::ToString()
extern void GenericGFPoly_ToString_mD77E8507EB77B29C054C29A942905470B4195274 (void);
// 0x00000074 System.Int32 ZXing.QrCode.Internal.MaskUtil::applyMaskPenaltyRule1(ZXing.QrCode.Internal.ByteMatrix)
extern void MaskUtil_applyMaskPenaltyRule1_m7AAA04BCB84C5C626C87E4F5DD3DB01DA9F7C43A (void);
// 0x00000075 System.Int32 ZXing.QrCode.Internal.MaskUtil::applyMaskPenaltyRule2(ZXing.QrCode.Internal.ByteMatrix)
extern void MaskUtil_applyMaskPenaltyRule2_mD213F1D75AFA546ADB179E6AEE0397CA2A50A673 (void);
// 0x00000076 System.Int32 ZXing.QrCode.Internal.MaskUtil::applyMaskPenaltyRule3(ZXing.QrCode.Internal.ByteMatrix)
extern void MaskUtil_applyMaskPenaltyRule3_m80A1FA90C7C5A80A8A1639763EA57F05AB42B69A (void);
// 0x00000077 System.Boolean ZXing.QrCode.Internal.MaskUtil::isWhiteHorizontal(System.Byte[],System.Int32,System.Int32)
extern void MaskUtil_isWhiteHorizontal_mD99C5C415418888BEADEF984476802B2A8F56CA0 (void);
// 0x00000078 System.Boolean ZXing.QrCode.Internal.MaskUtil::isWhiteVertical(System.Byte[][],System.Int32,System.Int32,System.Int32)
extern void MaskUtil_isWhiteVertical_m7361A510F324E2BA6D81CAA7D34A31BB0380A379 (void);
// 0x00000079 System.Int32 ZXing.QrCode.Internal.MaskUtil::applyMaskPenaltyRule4(ZXing.QrCode.Internal.ByteMatrix)
extern void MaskUtil_applyMaskPenaltyRule4_m4E20FBB2E47B44D1A8F3C2FB3160B76DCC97B0EF (void);
// 0x0000007A System.Boolean ZXing.QrCode.Internal.MaskUtil::getDataMaskBit(System.Int32,System.Int32,System.Int32)
extern void MaskUtil_getDataMaskBit_m11B095098DDDFFEDB7D388B5A0BD28E714DEFCF3 (void);
// 0x0000007B System.Int32 ZXing.QrCode.Internal.MaskUtil::applyMaskPenaltyRule1Internal(ZXing.QrCode.Internal.ByteMatrix,System.Boolean)
extern void MaskUtil_applyMaskPenaltyRule1Internal_mD1681B7DAE75391F6E65A3EA5B2909EE22C42542 (void);
// 0x0000007C System.Int32 ZXing.OneD.UPCEANWriter::get_DefaultMargin()
extern void UPCEANWriter_get_DefaultMargin_mF0861669301D1167B7142F052A0C3739112CB30A (void);
// 0x0000007D System.Void ZXing.OneD.UPCEANWriter::.ctor()
extern void UPCEANWriter__ctor_m2950D0E31755C982CF8D27BEFF1E650E20CC5ED5 (void);
// 0x0000007E ZXing.Common.BitMatrix ZXing.OneD.EAN8Writer::encode(System.String,ZXing.BarcodeFormat,System.Int32,System.Int32,System.Collections.Generic.IDictionary`2<ZXing.EncodeHintType,System.Object>)
extern void EAN8Writer_encode_mF46D26C417D7185F38B6761883A9F726AC5AE4B9 (void);
// 0x0000007F System.Boolean[] ZXing.OneD.EAN8Writer::encode(System.String)
extern void EAN8Writer_encode_m9E17E2560EADD298163D965A596BF3E61D828A3A (void);
// 0x00000080 System.Void ZXing.OneD.EAN8Writer::.ctor()
extern void EAN8Writer__ctor_mF78276B278A916515E5AA0343310EB7376EE07B8 (void);
// 0x00000081 System.Void ZXing.OneD.UPCEANReader::.cctor()
extern void UPCEANReader__cctor_m5EAECE36E7091DC48B2FEFBD9C6189B6CCB92FFC (void);
// 0x00000082 System.Boolean ZXing.OneD.UPCEANReader::checkStandardUPCEANChecksum(System.String)
extern void UPCEANReader_checkStandardUPCEANChecksum_mB321D9155CB8B1F09B8831544668121CD6852D2B (void);
// 0x00000083 System.Nullable`1<System.Int32> ZXing.OneD.UPCEANReader::getStandardUPCEANChecksum(System.String)
extern void UPCEANReader_getStandardUPCEANChecksum_mA42121994B1084A1DA5A94DEB78C75FD1AE66E9D (void);
// 0x00000084 System.Collections.Generic.IDictionary`2<ZXing.EncodeHintType,System.Object> ZXing.Common.EncodingOptions::get_Hints()
extern void EncodingOptions_get_Hints_m11AC6CBF296B50CEEBBBF0A0C17C0064297EE3C6 (void);
// 0x00000085 System.Void ZXing.Common.EncodingOptions::set_Hints(System.Collections.Generic.IDictionary`2<ZXing.EncodeHintType,System.Object>)
extern void EncodingOptions_set_Hints_m9423CF843812503DE489A3D7B511DD24287474BD (void);
// 0x00000086 System.Int32 ZXing.Common.EncodingOptions::get_Height()
extern void EncodingOptions_get_Height_mC197FC9E2D071FE86529C2EF2DE646A9533D452E (void);
// 0x00000087 System.Void ZXing.Common.EncodingOptions::set_Height(System.Int32)
extern void EncodingOptions_set_Height_m8A5D1321F588C473E908B70E156794EA37DC9124 (void);
// 0x00000088 System.Int32 ZXing.Common.EncodingOptions::get_Width()
extern void EncodingOptions_get_Width_m41D490AD0021D306C8F34679FFF5E1D6A24E6FB7 (void);
// 0x00000089 System.Void ZXing.Common.EncodingOptions::set_Width(System.Int32)
extern void EncodingOptions_set_Width_m1FD99866B8681E8247735155990908793FE3839A (void);
// 0x0000008A System.Void ZXing.Common.EncodingOptions::.ctor()
extern void EncodingOptions__ctor_mD4D89A578C1AA1D5116F449B6E1B31F128FD7CC1 (void);
// 0x0000008B ZXing.Aztec.Internal.AztecCode ZXing.Aztec.Internal.Encoder::encode(System.Byte[],System.Int32,System.Int32)
extern void Encoder_encode_mA3BB815DBCC8BDF35280A6433C71444D8CD30536 (void);
// 0x0000008C System.Void ZXing.Aztec.Internal.Encoder::drawBullsEye(ZXing.Common.BitMatrix,System.Int32,System.Int32)
extern void Encoder_drawBullsEye_mD37AB37AF330B8A5BA7BFE9FB323366FBE785F59 (void);
// 0x0000008D ZXing.Common.BitArray ZXing.Aztec.Internal.Encoder::generateModeMessage(System.Boolean,System.Int32,System.Int32)
extern void Encoder_generateModeMessage_m4D1FCC4DB867DD4F0E5DE19DECF36D5D8DF54B60 (void);
// 0x0000008E System.Void ZXing.Aztec.Internal.Encoder::drawModeMessage(ZXing.Common.BitMatrix,System.Boolean,System.Int32,ZXing.Common.BitArray)
extern void Encoder_drawModeMessage_m9559A41916BCF26F64D8BBB078F8D798D76D1204 (void);
// 0x0000008F ZXing.Common.BitArray ZXing.Aztec.Internal.Encoder::generateCheckWords(ZXing.Common.BitArray,System.Int32,System.Int32)
extern void Encoder_generateCheckWords_mF7D4645E26D45E38FAA4A079B690328A2938B65F (void);
// 0x00000090 System.Int32[] ZXing.Aztec.Internal.Encoder::bitsToWords(ZXing.Common.BitArray,System.Int32,System.Int32)
extern void Encoder_bitsToWords_m099083432A5F87948CFF6D782DCD40A60DBA8F76 (void);
// 0x00000091 ZXing.Common.ReedSolomon.GenericGF ZXing.Aztec.Internal.Encoder::getGF(System.Int32)
extern void Encoder_getGF_m13E1A2441C19EFE3DEEB5F0A0E2100ECF843FE9B (void);
// 0x00000092 ZXing.Common.BitArray ZXing.Aztec.Internal.Encoder::stuffBits(ZXing.Common.BitArray,System.Int32)
extern void Encoder_stuffBits_m1B63A78041C86F4D8DD136B59425EBDF6BA60558 (void);
// 0x00000093 System.Int32 ZXing.Aztec.Internal.Encoder::TotalBitsInLayer(System.Int32,System.Boolean)
extern void Encoder_TotalBitsInLayer_m07E143581184A8AD191FAA6561ED7FB8A7BEEB69 (void);
// 0x00000094 System.Void ZXing.Aztec.Internal.Encoder::.cctor()
extern void Encoder__cctor_m54327A81B49295767ACAC6768193512CAA5EDB6F (void);
// 0x00000095 System.Void ZXing.WriterException::.ctor()
extern void WriterException__ctor_m676F8A883738D7980A5C802B95D3880BD42E3AC8 (void);
// 0x00000096 System.Void ZXing.WriterException::.ctor(System.String)
extern void WriterException__ctor_mEEBB5A4BEA5F74994195D23D0A4A0AA6420D62DC (void);
// 0x00000097 System.Void ZXing.WriterException::.ctor(System.String,System.Exception)
extern void WriterException__ctor_m7E3C1EE31425FB8CE2B47483EF69EC794696AF4C (void);
// 0x00000098 System.Void ZXing.QrCode.Internal.BlockPair::.ctor(System.Byte[],System.Byte[])
extern void BlockPair__ctor_m0F9A306D5FC21E5A488164145D6EBF7C8D9BA79B (void);
// 0x00000099 System.Byte[] ZXing.QrCode.Internal.BlockPair::get_DataBytes()
extern void BlockPair_get_DataBytes_m98D9E3EE6C0DCABF4B36187054C47BFD903BC520 (void);
// 0x0000009A System.Byte[] ZXing.QrCode.Internal.BlockPair::get_ErrorCorrectionBytes()
extern void BlockPair_get_ErrorCorrectionBytes_m7B22B4027DE8F62B5428281400E0F93C095CC810 (void);
// 0x0000009B ZXing.Common.BitMatrix ZXing.OneD.PlesseyWriter::encode(System.String,ZXing.BarcodeFormat,System.Int32,System.Int32,System.Collections.Generic.IDictionary`2<ZXing.EncodeHintType,System.Object>)
extern void PlesseyWriter_encode_mE3D7A0CE1E757EE7CEAE350C9BE235418F81C711 (void);
// 0x0000009C System.Boolean[] ZXing.OneD.PlesseyWriter::encode(System.String)
extern void PlesseyWriter_encode_m35F4722C85E606384AD687601074C9A86AAAC43A (void);
// 0x0000009D System.Void ZXing.OneD.PlesseyWriter::.ctor()
extern void PlesseyWriter__ctor_mAA213488BEA456169FFFDC1F93DA18C6233DDDFE (void);
// 0x0000009E System.Void ZXing.OneD.PlesseyWriter::.cctor()
extern void PlesseyWriter__cctor_m73964CE1E38C3AA415EDC554143C0CFC30800E30 (void);
// 0x0000009F System.Void ZXing.QrCode.Internal.Version::.ctor(System.Int32,System.Int32[],ZXing.QrCode.Internal.Version/ECBlocks[])
extern void Version__ctor_m632065CF5A1FD956582B79A7C137207E0AEF0FF1 (void);
// 0x000000A0 System.Int32 ZXing.QrCode.Internal.Version::get_VersionNumber()
extern void Version_get_VersionNumber_mC7D233925DA58AB570CFB083E5D07EAEDBEBC312 (void);
// 0x000000A1 System.Int32 ZXing.QrCode.Internal.Version::get_TotalCodewords()
extern void Version_get_TotalCodewords_m71C758392A5C6A7632EAE88535B2D6C18E3957ED (void);
// 0x000000A2 System.Int32 ZXing.QrCode.Internal.Version::get_DimensionForVersion()
extern void Version_get_DimensionForVersion_mA1869848C121CF26EB61D60A25C8FD1814978D14 (void);
// 0x000000A3 ZXing.QrCode.Internal.Version/ECBlocks ZXing.QrCode.Internal.Version::getECBlocksForLevel(ZXing.QrCode.Internal.ErrorCorrectionLevel)
extern void Version_getECBlocksForLevel_m03F91D31A3FC0DEB93346D39EE1F7ED293D6B286 (void);
// 0x000000A4 ZXing.QrCode.Internal.Version ZXing.QrCode.Internal.Version::getVersionForNumber(System.Int32)
extern void Version_getVersionForNumber_mD3CBCF01090102DBB1B3DAC4AF0E23D1570C3328 (void);
// 0x000000A5 System.String ZXing.QrCode.Internal.Version::ToString()
extern void Version_ToString_mDC05BAC02564B3BBDCBD6BDB2CA9245D696DF4B3 (void);
// 0x000000A6 ZXing.QrCode.Internal.Version[] ZXing.QrCode.Internal.Version::buildVersions()
extern void Version_buildVersions_mBDD7EF05C1149E910FFB02124E03C33957F33E8E (void);
// 0x000000A7 System.Void ZXing.QrCode.Internal.Version::.cctor()
extern void Version__cctor_m9B567EFFFD86E465B350A555B997AB9B66A521B1 (void);
// 0x000000A8 System.Void ZXing.QrCode.Internal.Version/ECBlocks::.ctor(System.Int32,ZXing.QrCode.Internal.Version/ECB[])
extern void ECBlocks__ctor_m9D0FEA2A62AD87300386326630D71B2FE43D9011 (void);
// 0x000000A9 System.Int32 ZXing.QrCode.Internal.Version/ECBlocks::get_ECCodewordsPerBlock()
extern void ECBlocks_get_ECCodewordsPerBlock_mF5F7E0CB687399B198D52DD6069845FF62BFFFA9 (void);
// 0x000000AA System.Int32 ZXing.QrCode.Internal.Version/ECBlocks::get_NumBlocks()
extern void ECBlocks_get_NumBlocks_mAB97A37082ECA5AF5428F29A61A02873DCC4310A (void);
// 0x000000AB System.Int32 ZXing.QrCode.Internal.Version/ECBlocks::get_TotalECCodewords()
extern void ECBlocks_get_TotalECCodewords_m93CAC7D317E46A5596F928124939ADA688134E4B (void);
// 0x000000AC ZXing.QrCode.Internal.Version/ECB[] ZXing.QrCode.Internal.Version/ECBlocks::getECBlocks()
extern void ECBlocks_getECBlocks_m289C8698C02897868B44F8F02AD0A09713474112 (void);
// 0x000000AD System.Void ZXing.QrCode.Internal.Version/ECB::.ctor(System.Int32,System.Int32)
extern void ECB__ctor_mE5D63C7AE05AB1B6EC8237C68219F66AA1D05AFB (void);
// 0x000000AE System.Int32 ZXing.QrCode.Internal.Version/ECB::get_Count()
extern void ECB_get_Count_mB561812ADB3C72573B7448A4C15F77A8D98FD0E2 (void);
// 0x000000AF System.Int32 ZXing.QrCode.Internal.Version/ECB::get_DataCodewords()
extern void ECB_get_DataCodewords_m21EDF743302FBB953BD5E30111197CAABF8FDE5B (void);
// 0x000000B0 System.Void ZXing.ReaderException::.ctor()
extern void ReaderException__ctor_mA451AC8A99B53023FE2A2688BE53F0D0389BF358 (void);
// 0x000000B1 System.Void ZXing.FormatException::.ctor()
extern void FormatException__ctor_mBCCCB85A02EE0C55636CB1F326C5AD683E51F067 (void);
// 0x000000B2 System.Void BigIntegerLibrary.BigInteger::.ctor()
extern void BigInteger__ctor_m69A2D8A19CC9D704800DF0DB9C359E5DE14177F6 (void);
// 0x000000B3 System.Void BigIntegerLibrary.BigInteger::.ctor(System.Int64)
extern void BigInteger__ctor_mA9031AC26D86BF3500545C4CA63448C167596DC7 (void);
// 0x000000B4 System.Void BigIntegerLibrary.BigInteger::.ctor(BigIntegerLibrary.BigInteger)
extern void BigInteger__ctor_mAC829AB10C5217FA7CAA11C745991C339A5B0200 (void);
// 0x000000B5 System.Void BigIntegerLibrary.BigInteger::.ctor(System.String)
extern void BigInteger__ctor_mC880179410494FBF7999305DD1732B6EC9C7789F (void);
// 0x000000B6 System.Boolean BigIntegerLibrary.BigInteger::Equals(BigIntegerLibrary.BigInteger)
extern void BigInteger_Equals_m45066B3518201B7CDB4BB946A8B1C7F6E31BDF97 (void);
// 0x000000B7 System.Boolean BigIntegerLibrary.BigInteger::Equals(System.Object)
extern void BigInteger_Equals_m3D3303AF4D3EEB9C8A491D93D69766289731DA9E (void);
// 0x000000B8 System.Int32 BigIntegerLibrary.BigInteger::GetHashCode()
extern void BigInteger_GetHashCode_m15EE1915EB890F940083109DC4A55649A01DC0E1 (void);
// 0x000000B9 System.String BigIntegerLibrary.BigInteger::ToString()
extern void BigInteger_ToString_mA163999C1ABCA14CC54501CF89868920022AABC5 (void);
// 0x000000BA BigIntegerLibrary.BigInteger BigIntegerLibrary.BigInteger::Parse(System.String)
extern void BigInteger_Parse_m1760591815D06039FBA97B232C332A8045D268DB (void);
// 0x000000BB System.Int32 BigIntegerLibrary.BigInteger::CompareTo(BigIntegerLibrary.BigInteger)
extern void BigInteger_CompareTo_m933CD8D7F3A0BA498E0FAF2B58B311A4B304F2DA (void);
// 0x000000BC System.Int32 BigIntegerLibrary.BigInteger::CompareTo(System.Object)
extern void BigInteger_CompareTo_mA620358437EBACEB8F8250FD262B7F981B3F7B0B (void);
// 0x000000BD BigIntegerLibrary.BigInteger BigIntegerLibrary.BigInteger::Opposite(BigIntegerLibrary.BigInteger)
extern void BigInteger_Opposite_mDCED620041A9A733A29DA18A44D46DB5D911B315 (void);
// 0x000000BE System.Boolean BigIntegerLibrary.BigInteger::Greater(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_Greater_m4C21B747A816B2BC7AFD03FFADEE82618CAFF913 (void);
// 0x000000BF System.Boolean BigIntegerLibrary.BigInteger::GreaterOrEqual(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_GreaterOrEqual_mBCAF6155C2FDFE897DE4EF99002F7031384E0DD6 (void);
// 0x000000C0 System.Boolean BigIntegerLibrary.BigInteger::Smaller(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_Smaller_m5B446A2468367CF184CD685D0908D0139D713AB0 (void);
// 0x000000C1 System.Boolean BigIntegerLibrary.BigInteger::SmallerOrEqual(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_SmallerOrEqual_m4B0EB850758E5A1F820C74379D3CC6AF1146A87D (void);
// 0x000000C2 BigIntegerLibrary.BigInteger BigIntegerLibrary.BigInteger::Abs(BigIntegerLibrary.BigInteger)
extern void BigInteger_Abs_m9505B160241171EDC8F144B5E180869DCBB77745 (void);
// 0x000000C3 BigIntegerLibrary.BigInteger BigIntegerLibrary.BigInteger::Addition(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_Addition_m544A7AC1731E2A95BB8DF44E2F3F742A173D647E (void);
// 0x000000C4 BigIntegerLibrary.BigInteger BigIntegerLibrary.BigInteger::Subtraction(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_Subtraction_mF102CDAD8F220C5A3AD346017534A5A89F103203 (void);
// 0x000000C5 BigIntegerLibrary.BigInteger BigIntegerLibrary.BigInteger::Multiplication(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_Multiplication_m235E4DA5DDC2F9E0A541768A3F82B8133A7A39BA (void);
// 0x000000C6 BigIntegerLibrary.BigInteger BigIntegerLibrary.BigInteger::Division(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_Division_m1297CEAFB08D136C9CA4891DE68F5904A95C05CB (void);
// 0x000000C7 BigIntegerLibrary.BigInteger BigIntegerLibrary.BigInteger::Modulo(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_Modulo_m98F14DD2814355DBCB7814A3D5FE3E571FF66416 (void);
// 0x000000C8 BigIntegerLibrary.BigInteger BigIntegerLibrary.BigInteger::op_Implicit(System.Int64)
extern void BigInteger_op_Implicit_mAAA34C0DED0F6AB4A142F6B84AD34BE3A6323667 (void);
// 0x000000C9 System.Boolean BigIntegerLibrary.BigInteger::op_Equality(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_op_Equality_m6E2AC5561ECF42C27EE3535CF90837A705AB694F (void);
// 0x000000CA System.Boolean BigIntegerLibrary.BigInteger::op_Inequality(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_op_Inequality_m0878843BD4C02C6D98B61235D1E10753762B09AF (void);
// 0x000000CB System.Boolean BigIntegerLibrary.BigInteger::op_LessThan(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_op_LessThan_mA1D63FCADA6DAB0BDF36656D73E918655562158A (void);
// 0x000000CC System.Boolean BigIntegerLibrary.BigInteger::op_GreaterThanOrEqual(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_op_GreaterThanOrEqual_m5FED500101A7EAFCC745E61AFA3BBE9DB54C4391 (void);
// 0x000000CD System.Boolean BigIntegerLibrary.BigInteger::op_LessThanOrEqual(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_op_LessThanOrEqual_m9BAC9ADD76F62D2BF9144D623C55A85D5185E695 (void);
// 0x000000CE BigIntegerLibrary.BigInteger BigIntegerLibrary.BigInteger::op_UnaryNegation(BigIntegerLibrary.BigInteger)
extern void BigInteger_op_UnaryNegation_m77FBEFAB24AE5031E4C0992947D8AE1C66EF8DD9 (void);
// 0x000000CF BigIntegerLibrary.BigInteger BigIntegerLibrary.BigInteger::op_Addition(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_op_Addition_mD52A2F5AD09BA0255BB3206FBC2B0942F92365B4 (void);
// 0x000000D0 BigIntegerLibrary.BigInteger BigIntegerLibrary.BigInteger::op_Subtraction(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_op_Subtraction_m4A8D83920612E445AB8EE7A419BFD56BE54E2A7D (void);
// 0x000000D1 BigIntegerLibrary.BigInteger BigIntegerLibrary.BigInteger::op_Multiply(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_op_Multiply_mEAA931C712C0775A12BE4C98ECA73682AA62B449 (void);
// 0x000000D2 BigIntegerLibrary.BigInteger BigIntegerLibrary.BigInteger::op_Division(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_op_Division_m36389DDB09EBC1433E8BE4042D56F1FC66CF6832 (void);
// 0x000000D3 BigIntegerLibrary.BigInteger BigIntegerLibrary.BigInteger::Add(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_Add_m857D6A84B963A21E13AF4787B7798715EF908032 (void);
// 0x000000D4 BigIntegerLibrary.BigInteger BigIntegerLibrary.BigInteger::Subtract(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_Subtract_mD4B6208DC879F1107A289F3700AF89104E498AB0 (void);
// 0x000000D5 BigIntegerLibrary.BigInteger BigIntegerLibrary.BigInteger::Multiply(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_Multiply_m6FF8C4D44FB80FF6C06CB5F3F7F229E361172ED6 (void);
// 0x000000D6 BigIntegerLibrary.BigInteger BigIntegerLibrary.BigInteger::DivideByOneDigitNumber(BigIntegerLibrary.BigInteger,System.Int64)
extern void BigInteger_DivideByOneDigitNumber_m2F8E5B16C8A7CD8FE5F9CC0CB12B4B1FF9F028BE (void);
// 0x000000D7 BigIntegerLibrary.BigInteger BigIntegerLibrary.BigInteger::DivideByBigNumber(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger)
extern void BigInteger_DivideByBigNumber_mA6B20835A92324720AFBC794964F794B7098DBCE (void);
// 0x000000D8 System.Boolean BigIntegerLibrary.BigInteger::DivideByBigNumberSmaller(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger,System.Int32,System.Int32)
extern void BigInteger_DivideByBigNumberSmaller_mF93EC55EE62F096BD9854AEF588A583156166B0C (void);
// 0x000000D9 System.Void BigIntegerLibrary.BigInteger::Difference(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger,System.Int32,System.Int32)
extern void BigInteger_Difference_m6EAFD63AC836609B2C28F28E849D110C3071070F (void);
// 0x000000DA System.Int64 BigIntegerLibrary.BigInteger::Trial(BigIntegerLibrary.BigInteger,BigIntegerLibrary.BigInteger,System.Int32,System.Int32)
extern void BigInteger_Trial_m0A2384A514688E243620C2DCBABBC838EE467A34 (void);
// 0x000000DB System.Void BigIntegerLibrary.BigInteger::.cctor()
extern void BigInteger__cctor_mFF5A97A32AC1057194871567C8398550207FF1B8 (void);
// 0x000000DC System.Void BigIntegerLibrary.BigInteger/DigitContainer::.ctor()
extern void DigitContainer__ctor_mB6729FC739F1D8DE01C67483AB306596BCDCB527 (void);
// 0x000000DD System.Int64 BigIntegerLibrary.BigInteger/DigitContainer::get_Item(System.Int32)
extern void DigitContainer_get_Item_m13CCE57F6FC21EFEC29182C89C0246460BD8C3A6 (void);
// 0x000000DE System.Void BigIntegerLibrary.BigInteger/DigitContainer::set_Item(System.Int32,System.Int64)
extern void DigitContainer_set_Item_m8842DE3C1EF724D10D6BEBA617E57DFF8C492D65 (void);
// 0x000000DF System.Void ZXing.Aztec.Internal.State::.ctor(ZXing.Aztec.Internal.Token,System.Int32,System.Int32,System.Int32)
extern void State__ctor_mD095565D18FA60242472BE90A67CE29A788EE821 (void);
// 0x000000E0 System.Int32 ZXing.Aztec.Internal.State::get_Mode()
extern void State_get_Mode_mDCF57959DDE617773975B2B417B379473AFD223D (void);
// 0x000000E1 System.Int32 ZXing.Aztec.Internal.State::get_BinaryShiftByteCount()
extern void State_get_BinaryShiftByteCount_m5857FFEDAA0E093FBE01951B71E1A33EC7BD46F6 (void);
// 0x000000E2 System.Int32 ZXing.Aztec.Internal.State::get_BitCount()
extern void State_get_BitCount_m8C4F7294954E2523C1C980B7AC12F509746C27F9 (void);
// 0x000000E3 ZXing.Aztec.Internal.State ZXing.Aztec.Internal.State::latchAndAppend(System.Int32,System.Int32)
extern void State_latchAndAppend_m7483E3A43172836DD7F7720E9AA4461B66841507 (void);
// 0x000000E4 ZXing.Aztec.Internal.State ZXing.Aztec.Internal.State::shiftAndAppend(System.Int32,System.Int32)
extern void State_shiftAndAppend_m85C6673552D67393BA96CEAFD5D9711655BB417F (void);
// 0x000000E5 ZXing.Aztec.Internal.State ZXing.Aztec.Internal.State::addBinaryShiftChar(System.Int32)
extern void State_addBinaryShiftChar_mCD3C83DD3A8F303D384F74016DA4529F27901EC3 (void);
// 0x000000E6 ZXing.Aztec.Internal.State ZXing.Aztec.Internal.State::endBinaryShift(System.Int32)
extern void State_endBinaryShift_m566AC5C9823C9E5E16A9AAC8F4E316BC939E2089 (void);
// 0x000000E7 System.Boolean ZXing.Aztec.Internal.State::isBetterThanOrEqualTo(ZXing.Aztec.Internal.State)
extern void State_isBetterThanOrEqualTo_m2A89EEA101ABB8E0C9CE0186F7A4BB877EEB37FA (void);
// 0x000000E8 ZXing.Common.BitArray ZXing.Aztec.Internal.State::toBitArray(System.Byte[])
extern void State_toBitArray_m09CA20754E97593C71B29AF5DE79CB047083D6BF (void);
// 0x000000E9 System.String ZXing.Aztec.Internal.State::ToString()
extern void State_ToString_m16FFACF3EB7D11744E75530A156227C95685D205 (void);
// 0x000000EA System.Void ZXing.Aztec.Internal.State::.cctor()
extern void State__cctor_m11577A919CE62E0289265C6799469806D446735F (void);
// 0x000000EB System.Void ZXing.Aztec.Internal.AztecCode::set_isCompact(System.Boolean)
extern void AztecCode_set_isCompact_m597E612BC815EA9C72787E2D916F328FDA3F24BC (void);
// 0x000000EC System.Void ZXing.Aztec.Internal.AztecCode::set_Size(System.Int32)
extern void AztecCode_set_Size_m5C7C783E5F2D788C6684E23A49EC6CDF5A058495 (void);
// 0x000000ED System.Void ZXing.Aztec.Internal.AztecCode::set_Layers(System.Int32)
extern void AztecCode_set_Layers_m8B5FE0EF288E66B8C5F1B8A184C0E87D43B8F478 (void);
// 0x000000EE System.Void ZXing.Aztec.Internal.AztecCode::set_CodeWords(System.Int32)
extern void AztecCode_set_CodeWords_mB8C8D156EF6308F34AE895994B478B6138CCD2D9 (void);
// 0x000000EF ZXing.Common.BitMatrix ZXing.Aztec.Internal.AztecCode::get_Matrix()
extern void AztecCode_get_Matrix_m89D879400229206B4C9084D174E17B0CEC53C6EF (void);
// 0x000000F0 System.Void ZXing.Aztec.Internal.AztecCode::set_Matrix(ZXing.Common.BitMatrix)
extern void AztecCode_set_Matrix_m2C412B37E380796D2FB9A4952937C0CD2270BDB8 (void);
// 0x000000F1 System.Void ZXing.Aztec.Internal.AztecCode::.ctor()
extern void AztecCode__ctor_mCF74E2FCC26568EC7FFBA337F548316DF3486CF1 (void);
// 0x000000F2 System.Void ZXing.QrCode.Internal.ByteMatrix::.ctor(System.Int32,System.Int32)
extern void ByteMatrix__ctor_mD9ED8E0BC8269FD7C78521887BB257A369D61B21 (void);
// 0x000000F3 System.Int32 ZXing.QrCode.Internal.ByteMatrix::get_Height()
extern void ByteMatrix_get_Height_m431DA5356FC7A79050644F5A2D92FE2A23CD7A19 (void);
// 0x000000F4 System.Int32 ZXing.QrCode.Internal.ByteMatrix::get_Width()
extern void ByteMatrix_get_Width_m4EB618AB944DEEDC5912E727D030819DA99B0767 (void);
// 0x000000F5 System.Int32 ZXing.QrCode.Internal.ByteMatrix::get_Item(System.Int32,System.Int32)
extern void ByteMatrix_get_Item_m7F1D2BDC8A65006C16907F68A3DF7767A083D48B (void);
// 0x000000F6 System.Void ZXing.QrCode.Internal.ByteMatrix::set_Item(System.Int32,System.Int32,System.Int32)
extern void ByteMatrix_set_Item_m23426A3D1BE362763285B62829C7776459A87684 (void);
// 0x000000F7 System.Byte[][] ZXing.QrCode.Internal.ByteMatrix::get_Array()
extern void ByteMatrix_get_Array_m7131EF57DAF53905532CC248EC7588F2A09D692D (void);
// 0x000000F8 System.Void ZXing.QrCode.Internal.ByteMatrix::set(System.Int32,System.Int32,System.Boolean)
extern void ByteMatrix_set_m5CF2E267D45A4ED9EEEA0642EA897378623399C4 (void);
// 0x000000F9 System.Void ZXing.QrCode.Internal.ByteMatrix::clear(System.Byte)
extern void ByteMatrix_clear_m97879CC62C73B5CCBF1F22A3C72CD5908F57D78A (void);
// 0x000000FA System.String ZXing.QrCode.Internal.ByteMatrix::ToString()
extern void ByteMatrix_ToString_mEADA9D28C1A26AC7619892130004AEFFDA522C1F (void);
// 0x000000FB System.Void ZXing.PDF417.Internal.BarcodeRow::.ctor(System.Int32)
extern void BarcodeRow__ctor_mCA36490C152EC45A701B31D5F47D20DA6C90A9E0 (void);
// 0x000000FC System.Void ZXing.PDF417.Internal.BarcodeRow::set(System.Int32,System.Boolean)
extern void BarcodeRow_set_m88567D3CB189654EB765C60ABE54546540436C82 (void);
// 0x000000FD System.Void ZXing.PDF417.Internal.BarcodeRow::addBar(System.Boolean,System.Int32)
extern void BarcodeRow_addBar_m50082BAFF8575889C1C698BCEBC49ADABC153471 (void);
// 0x000000FE System.SByte[] ZXing.PDF417.Internal.BarcodeRow::getScaledRow(System.Int32)
extern void BarcodeRow_getScaledRow_m7D9CBFF8089D7D556176236A37537696662820B8 (void);
// 0x000000FF ZXing.Common.BitMatrix ZXing.OneD.UPCAWriter::encode(System.String,ZXing.BarcodeFormat,System.Int32,System.Int32,System.Collections.Generic.IDictionary`2<ZXing.EncodeHintType,System.Object>)
extern void UPCAWriter_encode_m4CD6BFE418E56A9618C0914178C377A4199C62B7 (void);
// 0x00000100 System.Void ZXing.OneD.UPCAWriter::.ctor()
extern void UPCAWriter__ctor_m7EECE77D24C1659896BD24A2D6A13BB13B1AE49A (void);
// 0x00000101 ZXing.Common.BitMatrix ZXing.OneD.EAN13Writer::encode(System.String,ZXing.BarcodeFormat,System.Int32,System.Int32,System.Collections.Generic.IDictionary`2<ZXing.EncodeHintType,System.Object>)
extern void EAN13Writer_encode_mB7A1F5621D73D0AD309EAE6FB63DA41128B55996 (void);
// 0x00000102 System.Boolean[] ZXing.OneD.EAN13Writer::encode(System.String)
extern void EAN13Writer_encode_mD642678EC1B40B586DC40276D9DE7BC88EAECFA6 (void);
// 0x00000103 System.Void ZXing.OneD.EAN13Writer::.ctor()
extern void EAN13Writer__ctor_mEC15BCEEA94A09741A5F4C784C958570445E052F (void);
// 0x00000104 System.Void ZXing.OneD.EAN13Reader::.cctor()
extern void EAN13Reader__cctor_m2ED31F1368082989D00A5AE69DA8130F3E4F9754 (void);
// 0x00000105 System.Int32 ZXing.Dimension::get_Width()
extern void Dimension_get_Width_m813D53F7D20E20C9E579017C7438D7140A5C0E43 (void);
// 0x00000106 System.Int32 ZXing.Dimension::get_Height()
extern void Dimension_get_Height_m1CC9FC7C85BB327FAB1A35ECC4FE02A3CB89D209 (void);
// 0x00000107 UnityEngine.Color32 ZXing.Color32Renderer::get_Foreground()
extern void Color32Renderer_get_Foreground_m834D2A31C6626724E141417D102E2BD6FDA9462C (void);
// 0x00000108 System.Void ZXing.Color32Renderer::set_Foreground(UnityEngine.Color32)
extern void Color32Renderer_set_Foreground_mDE1439A10E49A742C22D51F9D2FBA0CFAEADA2FB (void);
// 0x00000109 UnityEngine.Color32 ZXing.Color32Renderer::get_Background()
extern void Color32Renderer_get_Background_m51B69CE85809EB3420A58292A274BA1B56F4A4DF (void);
// 0x0000010A System.Void ZXing.Color32Renderer::set_Background(UnityEngine.Color32)
extern void Color32Renderer_set_Background_mCC0C1C48BC5E28A649930D77B256EEF9E398DE63 (void);
// 0x0000010B System.Void ZXing.Color32Renderer::.ctor()
extern void Color32Renderer__ctor_m022DE6BAF62F51CD8B9E4D37B73E87BDC407C2EA (void);
// 0x0000010C UnityEngine.Color32[] ZXing.Color32Renderer::Render(ZXing.Common.BitMatrix,ZXing.BarcodeFormat,System.String,ZXing.Common.EncodingOptions)
extern void Color32Renderer_Render_m6FE05E98B75976AA9F41400B9C6C2215B7B2E40F (void);
// 0x0000010D ZXing.Common.BitMatrix ZXing.OneD.MSIWriter::encode(System.String,ZXing.BarcodeFormat,System.Int32,System.Int32,System.Collections.Generic.IDictionary`2<ZXing.EncodeHintType,System.Object>)
extern void MSIWriter_encode_m11A07A46A43AA48D3A1F79AE30CE5579E6139289 (void);
// 0x0000010E System.Boolean[] ZXing.OneD.MSIWriter::encode(System.String)
extern void MSIWriter_encode_mA45E6992E21D35989DAAA7EFAAD50E4D17E4C5C9 (void);
// 0x0000010F System.Void ZXing.OneD.MSIWriter::.ctor()
extern void MSIWriter__ctor_m2F37F4D1ADA55C87AC1239919C5F7E4B69C12DAE (void);
// 0x00000110 System.Void ZXing.OneD.MSIWriter::.cctor()
extern void MSIWriter__cctor_mD2B3642FF3130314CA329B1275E552E9A89170E9 (void);
// 0x00000111 System.Void ZXing.OneD.MSIReader::.cctor()
extern void MSIReader__cctor_mDB5CCD8241C5F9E4FFBFB2CA06100D917054F763 (void);
// 0x00000112 System.Int32 ZXing.Datamatrix.Encoder.EdifactEncoder::get_EncodingMode()
extern void EdifactEncoder_get_EncodingMode_m638EC864DD8160E986A2A7EB7EAD665F8DB31B2A (void);
// 0x00000113 System.Void ZXing.Datamatrix.Encoder.EdifactEncoder::encode(ZXing.Datamatrix.Encoder.EncoderContext)
extern void EdifactEncoder_encode_m2261C384AF6CAAA752FF726BDC77040D06BA5321 (void);
// 0x00000114 System.Void ZXing.Datamatrix.Encoder.EdifactEncoder::handleEOD(ZXing.Datamatrix.Encoder.EncoderContext,System.Text.StringBuilder)
extern void EdifactEncoder_handleEOD_m8BFC42F21E70D93982591EDEDABD17D83ABF8341 (void);
// 0x00000115 System.Void ZXing.Datamatrix.Encoder.EdifactEncoder::encodeChar(System.Char,System.Text.StringBuilder)
extern void EdifactEncoder_encodeChar_mF4A1D39C900CEEA6D433C319C2C7D3599C908615 (void);
// 0x00000116 System.String ZXing.Datamatrix.Encoder.EdifactEncoder::encodeToCodewords(System.Text.StringBuilder,System.Int32)
extern void EdifactEncoder_encodeToCodewords_m8A01ECC5F50DF400B48457143244A4758F3EB360 (void);
// 0x00000117 System.Void ZXing.Datamatrix.Encoder.EdifactEncoder::.ctor()
extern void EdifactEncoder__ctor_m49E00027C578BA262F59C5A711D4E40F3A2AA347 (void);
// 0x00000118 System.Void ZXing.Datamatrix.Encoder.DefaultPlacement::.ctor(System.String,System.Int32,System.Int32)
extern void DefaultPlacement__ctor_m75A3B328D6960171ABA1D0D6491E6102EFE8573A (void);
// 0x00000119 System.Boolean ZXing.Datamatrix.Encoder.DefaultPlacement::getBit(System.Int32,System.Int32)
extern void DefaultPlacement_getBit_m5A08D0C45336BD4617318CC891ED4FD59CF2B723 (void);
// 0x0000011A System.Void ZXing.Datamatrix.Encoder.DefaultPlacement::setBit(System.Int32,System.Int32,System.Boolean)
extern void DefaultPlacement_setBit_mFE8F3F7700BD0558A90162067EB45DF2248CD3A4 (void);
// 0x0000011B System.Boolean ZXing.Datamatrix.Encoder.DefaultPlacement::hasBit(System.Int32,System.Int32)
extern void DefaultPlacement_hasBit_m84FE601210C961F40543C8D911F81289224F91DA (void);
// 0x0000011C System.Void ZXing.Datamatrix.Encoder.DefaultPlacement::place()
extern void DefaultPlacement_place_m0F093E38D19FA2C9DEFAF65D59C5C3CF1DBC3CE6 (void);
// 0x0000011D System.Void ZXing.Datamatrix.Encoder.DefaultPlacement::module(System.Int32,System.Int32,System.Int32,System.Int32)
extern void DefaultPlacement_module_mA6C4263AF83FFFFE5B785809AEDD24D5E00B32F2 (void);
// 0x0000011E System.Void ZXing.Datamatrix.Encoder.DefaultPlacement::utah(System.Int32,System.Int32,System.Int32)
extern void DefaultPlacement_utah_mCA157D0F894E3D27CD42C02C4BEC51E2B1CE558C (void);
// 0x0000011F System.Void ZXing.Datamatrix.Encoder.DefaultPlacement::corner1(System.Int32)
extern void DefaultPlacement_corner1_mD09EEDCE446A5AA84B5C91B8D96764EC50F4D344 (void);
// 0x00000120 System.Void ZXing.Datamatrix.Encoder.DefaultPlacement::corner2(System.Int32)
extern void DefaultPlacement_corner2_mB01814FD9C6566210B4994A602EF5B06363BBEFE (void);
// 0x00000121 System.Void ZXing.Datamatrix.Encoder.DefaultPlacement::corner3(System.Int32)
extern void DefaultPlacement_corner3_mF4BBB96F20D7BEE6F172B76759ADF6B258400E73 (void);
// 0x00000122 System.Void ZXing.Datamatrix.Encoder.DefaultPlacement::corner4(System.Int32)
extern void DefaultPlacement_corner4_m8243AED544D0CC53AAC238067D443623E549A5BA (void);
// 0x00000123 System.Void ZXing.Aztec.Internal.Token::.ctor(ZXing.Aztec.Internal.Token)
extern void Token__ctor_mB8D277B02EB356EC83D48D22EEF21B9249818CB4 (void);
// 0x00000124 ZXing.Aztec.Internal.Token ZXing.Aztec.Internal.Token::get_Previous()
extern void Token_get_Previous_mAF1A74400EC353C1D8BF256C6A4FE2798BE51F8E (void);
// 0x00000125 ZXing.Aztec.Internal.Token ZXing.Aztec.Internal.Token::add(System.Int32,System.Int32)
extern void Token_add_m92BD321030B141B51177797849794948481E179F (void);
// 0x00000126 ZXing.Aztec.Internal.Token ZXing.Aztec.Internal.Token::addBinaryShift(System.Int32,System.Int32)
extern void Token_addBinaryShift_mF3C69BE58890695D600AA6B3BA7A9818B2597C32 (void);
// 0x00000127 System.Void ZXing.Aztec.Internal.Token::appendTo(ZXing.Common.BitArray,System.Byte[])
// 0x00000128 System.Void ZXing.Aztec.Internal.Token::.cctor()
extern void Token__cctor_mDAE1E010697035A94080196656397F165257A3EE (void);
// 0x00000129 ZXing.Common.BitMatrix ZXing.QrCode.QRCodeWriter::encode(System.String,ZXing.BarcodeFormat,System.Int32,System.Int32,System.Collections.Generic.IDictionary`2<ZXing.EncodeHintType,System.Object>)
extern void QRCodeWriter_encode_m3AB9B337CACD90A15D8C2EC90489B1E4FC78E26F (void);
// 0x0000012A ZXing.Common.BitMatrix ZXing.QrCode.QRCodeWriter::renderResult(ZXing.QrCode.Internal.QRCode,System.Int32,System.Int32,System.Int32)
extern void QRCodeWriter_renderResult_m4A7C02561A8E80AE695F7618E4CAA52E96C6D978 (void);
// 0x0000012B System.Void ZXing.QrCode.QRCodeWriter::.ctor()
extern void QRCodeWriter__ctor_m3439AF691810E75852236707F0787DC6EC316BD6 (void);
// 0x0000012C System.Int32 ZXing.Datamatrix.Encoder.C40Encoder::get_EncodingMode()
extern void C40Encoder_get_EncodingMode_m1BB766D7F2F511BEFAD7319601C783E378FD1BA9 (void);
// 0x0000012D System.Void ZXing.Datamatrix.Encoder.C40Encoder::encode(ZXing.Datamatrix.Encoder.EncoderContext)
extern void C40Encoder_encode_m268230A0B4F811F2DAB33EE99C1E2963222B6451 (void);
// 0x0000012E System.Int32 ZXing.Datamatrix.Encoder.C40Encoder::backtrackOneCharacter(ZXing.Datamatrix.Encoder.EncoderContext,System.Text.StringBuilder,System.Text.StringBuilder,System.Int32)
extern void C40Encoder_backtrackOneCharacter_m0DE8AB9F95806BED76075E45EE462522870518E6 (void);
// 0x0000012F System.Void ZXing.Datamatrix.Encoder.C40Encoder::writeNextTriplet(ZXing.Datamatrix.Encoder.EncoderContext,System.Text.StringBuilder)
extern void C40Encoder_writeNextTriplet_mC28D9A4AE98BDBF51DFCCDBA97D0C1BCF215D16A (void);
// 0x00000130 System.Void ZXing.Datamatrix.Encoder.C40Encoder::handleEOD(ZXing.Datamatrix.Encoder.EncoderContext,System.Text.StringBuilder)
extern void C40Encoder_handleEOD_m4541691AE8274A4591AF6973ADDC8233F5776A65 (void);
// 0x00000131 System.Int32 ZXing.Datamatrix.Encoder.C40Encoder::encodeChar(System.Char,System.Text.StringBuilder)
extern void C40Encoder_encodeChar_mB4E43EBBD4C6B689A00CBEB01B363BB7BD996465 (void);
// 0x00000132 System.String ZXing.Datamatrix.Encoder.C40Encoder::encodeToCodewords(System.Text.StringBuilder,System.Int32)
extern void C40Encoder_encodeToCodewords_mE542999CAF6C1C12205929B0C19DC77F7EE6C27D (void);
// 0x00000133 System.Void ZXing.Datamatrix.Encoder.C40Encoder::.ctor()
extern void C40Encoder__ctor_m2BF4DEE8F84BF12EDB431C857C475D317A381EE9 (void);
// 0x00000134 System.Int32 ZXing.Datamatrix.Encoder.X12Encoder::get_EncodingMode()
extern void X12Encoder_get_EncodingMode_m114182FD5F4A0EFCAC7D3ECE20D6D29C4A4A8BB5 (void);
// 0x00000135 System.Void ZXing.Datamatrix.Encoder.X12Encoder::encode(ZXing.Datamatrix.Encoder.EncoderContext)
extern void X12Encoder_encode_m1E403628B395E933C2DC4542F9A688F181388549 (void);
// 0x00000136 System.Int32 ZXing.Datamatrix.Encoder.X12Encoder::encodeChar(System.Char,System.Text.StringBuilder)
extern void X12Encoder_encodeChar_m40D66B95BE9446537712801AF5A0C86D27213757 (void);
// 0x00000137 System.Void ZXing.Datamatrix.Encoder.X12Encoder::handleEOD(ZXing.Datamatrix.Encoder.EncoderContext,System.Text.StringBuilder)
extern void X12Encoder_handleEOD_mF027757C6C3F9CAE4C593FD4F45C3EA824D02795 (void);
// 0x00000138 System.Void ZXing.Datamatrix.Encoder.X12Encoder::.ctor()
extern void X12Encoder__ctor_m3209B93C13C02E5B0064DF0EBAAF4A52904118C6 (void);
// 0x00000139 System.Int32 ZXing.Common.BitMatrix::get_Width()
extern void BitMatrix_get_Width_m8055CEDC7ACBE606E27CE32DAD50DF85AC0E45DB (void);
// 0x0000013A System.Int32 ZXing.Common.BitMatrix::get_Height()
extern void BitMatrix_get_Height_m9A78F5C9E2B6133D13825F63B79852654734ABAF (void);
// 0x0000013B System.Void ZXing.Common.BitMatrix::.ctor(System.Int32)
extern void BitMatrix__ctor_mC3D9EDE74C513901EB96721ACA08844D5FB2D194 (void);
// 0x0000013C System.Void ZXing.Common.BitMatrix::.ctor(System.Int32,System.Int32)
extern void BitMatrix__ctor_mC5CB16859B35D776F2F3CDF0BFC1A3795C09F17C (void);
// 0x0000013D System.Boolean ZXing.Common.BitMatrix::get_Item(System.Int32,System.Int32)
extern void BitMatrix_get_Item_mFB70D0E9D1EF1BD7690BA65A7673B609DAEDE69B (void);
// 0x0000013E System.Void ZXing.Common.BitMatrix::set_Item(System.Int32,System.Int32,System.Boolean)
extern void BitMatrix_set_Item_mBC76D63A579D13FD2103B2CAC0416FAED5042B67 (void);
// 0x0000013F System.Void ZXing.Common.BitMatrix::clear()
extern void BitMatrix_clear_m37256F75D2C06D6E0BE8A15EF6AF1BB89BC7D808 (void);
// 0x00000140 System.Void ZXing.Common.BitMatrix::setRegion(System.Int32,System.Int32,System.Int32,System.Int32)
extern void BitMatrix_setRegion_m28EC620EA6468EA7609C1033F35C2EEB6073F19D (void);
// 0x00000141 ZXing.Common.BitArray ZXing.Common.BitMatrix::getRow(System.Int32,ZXing.Common.BitArray)
extern void BitMatrix_getRow_m96ADF03B068B2AB0EF061E1ACA42BD3B4C4BB7C2 (void);
// 0x00000142 System.Boolean ZXing.Common.BitMatrix::Equals(System.Object)
extern void BitMatrix_Equals_m4C8D5ED3DDADA6683B51074FC0676905A01353CF (void);
// 0x00000143 System.Int32 ZXing.Common.BitMatrix::GetHashCode()
extern void BitMatrix_GetHashCode_m2E790DA696226B3FA3443D1A743C82509620594D (void);
// 0x00000144 System.String ZXing.Common.BitMatrix::ToString()
extern void BitMatrix_ToString_mB26868A5C71A4E12E303D3C6356473C0CA353BEF (void);
// 0x00000145 System.String ZXing.Common.BitMatrix::ToString(System.String,System.String,System.String)
extern void BitMatrix_ToString_m1402BC7B08F5DF106C1AD61CACEB5E0831FFB545 (void);
// 0x00000146 System.String ZXing.Common.BitMatrix::buildToString(System.String,System.String,System.String)
extern void BitMatrix_buildToString_mA9D67165B179139929A21B90EC086C2C93C9BCCF (void);
// 0x00000147 System.Void ZXing.PDF417.Internal.BarcodeMatrix::.ctor(System.Int32,System.Int32)
extern void BarcodeMatrix__ctor_m835AF1210C8B22567777ACAC6BC2E472B054D67E (void);
// 0x00000148 System.Void ZXing.PDF417.Internal.BarcodeMatrix::startRow()
extern void BarcodeMatrix_startRow_m5042B956C49C626709426462FD84803DCBEF2DE3 (void);
// 0x00000149 ZXing.PDF417.Internal.BarcodeRow ZXing.PDF417.Internal.BarcodeMatrix::getCurrentRow()
extern void BarcodeMatrix_getCurrentRow_m600E94C404AFC8036307669D5B834D905D6C3D9B (void);
// 0x0000014A System.SByte[][] ZXing.PDF417.Internal.BarcodeMatrix::getScaledMatrix(System.Int32,System.Int32)
extern void BarcodeMatrix_getScaledMatrix_mBA9BA34833315E6413D70D194BC3EDCE7F00D56B (void);
// 0x0000014B ZXing.Common.BitMatrix ZXing.OneD.UPCEWriter::encode(System.String,ZXing.BarcodeFormat,System.Int32,System.Int32,System.Collections.Generic.IDictionary`2<ZXing.EncodeHintType,System.Object>)
extern void UPCEWriter_encode_m31A0A69CE46A63029EBA4352CCC0E7B2E84E2F9D (void);
// 0x0000014C System.Boolean[] ZXing.OneD.UPCEWriter::encode(System.String)
extern void UPCEWriter_encode_m35EECF221F1DB172BD681EDF2A7BF8216D6ED085 (void);
// 0x0000014D System.Void ZXing.OneD.UPCEWriter::.ctor()
extern void UPCEWriter__ctor_mB0D71BC3398A4E508AEF0DBDDBED25F109F2FB08 (void);
// 0x0000014E System.String ZXing.OneD.UPCEReader::convertUPCEtoUPCA(System.String)
extern void UPCEReader_convertUPCEtoUPCA_m95C330705DEC28D690E9FE0E41F0ABD334BE6BCF (void);
// 0x0000014F System.Void ZXing.OneD.UPCEReader::.cctor()
extern void UPCEReader__cctor_m0DD952293AE6A8EC5D8922BC5E27EC59DE072566 (void);
// 0x00000150 System.Void ZXing.Datamatrix.Encoder.SymbolInfo::.ctor(System.Boolean,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)
extern void SymbolInfo__ctor_mE896D19FC0AB4DADE4EDABD951EA74D620D689D1 (void);
// 0x00000151 System.Void ZXing.Datamatrix.Encoder.SymbolInfo::.ctor(System.Boolean,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)
extern void SymbolInfo__ctor_mD32419A493D777DCA51D0BA3C3BCD1FC7A9A8D5D (void);
// 0x00000152 ZXing.Datamatrix.Encoder.SymbolInfo ZXing.Datamatrix.Encoder.SymbolInfo::lookup(System.Int32,ZXing.Datamatrix.Encoder.SymbolShapeHint,ZXing.Dimension,ZXing.Dimension,System.Boolean)
extern void SymbolInfo_lookup_m6D860142F773C8279DEAA7637295D3EA60727FAB (void);
// 0x00000153 System.Int32 ZXing.Datamatrix.Encoder.SymbolInfo::getHorizontalDataRegions()
extern void SymbolInfo_getHorizontalDataRegions_mDDEA59520B1C53E74947063AE078272F2DC1EE09 (void);
// 0x00000154 System.Int32 ZXing.Datamatrix.Encoder.SymbolInfo::getVerticalDataRegions()
extern void SymbolInfo_getVerticalDataRegions_m960516C4F86B67FE551DE9E20C5BB0E34DF37D16 (void);
// 0x00000155 System.Int32 ZXing.Datamatrix.Encoder.SymbolInfo::getSymbolDataWidth()
extern void SymbolInfo_getSymbolDataWidth_m4ADB0EBCFF6B5FBA77FCCF7838B8B1A5FA92CF03 (void);
// 0x00000156 System.Int32 ZXing.Datamatrix.Encoder.SymbolInfo::getSymbolDataHeight()
extern void SymbolInfo_getSymbolDataHeight_m1E2DD77BDBB84C1746C9E0778904F0A39DBAE1D2 (void);
// 0x00000157 System.Int32 ZXing.Datamatrix.Encoder.SymbolInfo::getSymbolWidth()
extern void SymbolInfo_getSymbolWidth_m2A5232212AE2D7AB9CD437F65DF5C96BEA8CF137 (void);
// 0x00000158 System.Int32 ZXing.Datamatrix.Encoder.SymbolInfo::getSymbolHeight()
extern void SymbolInfo_getSymbolHeight_m64E010A58F37DD235B1CA2C97966F67BF2E5B429 (void);
// 0x00000159 System.Int32 ZXing.Datamatrix.Encoder.SymbolInfo::getInterleavedBlockCount()
extern void SymbolInfo_getInterleavedBlockCount_m643D4E0017A88D07F9927CD2E763EA7AB7866234 (void);
// 0x0000015A System.Int32 ZXing.Datamatrix.Encoder.SymbolInfo::getDataLengthForInterleavedBlock(System.Int32)
extern void SymbolInfo_getDataLengthForInterleavedBlock_mEF4522752046B58059C6E2EB4B4FD90284D0CBE9 (void);
// 0x0000015B System.Int32 ZXing.Datamatrix.Encoder.SymbolInfo::getErrorLengthForInterleavedBlock(System.Int32)
extern void SymbolInfo_getErrorLengthForInterleavedBlock_m22108C2443239C365E9AE4502D51CA5F85B85C93 (void);
// 0x0000015C System.String ZXing.Datamatrix.Encoder.SymbolInfo::ToString()
extern void SymbolInfo_ToString_mE0B64258DBD7DDB9499F408AF5A8D519E07A82EE (void);
// 0x0000015D System.Void ZXing.Datamatrix.Encoder.SymbolInfo::.cctor()
extern void SymbolInfo__cctor_m5FB8A3595211E99D9767A8B47832B70C4FDFD97D (void);
// 0x0000015E System.Void ZXing.Aztec.Internal.SimpleToken::.ctor(ZXing.Aztec.Internal.Token,System.Int32,System.Int32)
extern void SimpleToken__ctor_m0BF480450A6B082E9E8CB07346A0809E78AEFE34 (void);
// 0x0000015F System.Void ZXing.Aztec.Internal.SimpleToken::appendTo(ZXing.Common.BitArray,System.Byte[])
extern void SimpleToken_appendTo_mC4CCE296C154EE48514CAC9D52A20CF2DDDFF116 (void);
// 0x00000160 System.String ZXing.Aztec.Internal.SimpleToken::ToString()
extern void SimpleToken_ToString_mD85AB0D5568B250558B9A73E5E1A97F138CC3C5C (void);
// 0x00000161 System.Void ZXing.QrCode.Internal.ErrorCorrectionLevel::.ctor(System.Int32,System.Int32,System.String)
extern void ErrorCorrectionLevel__ctor_m429F20D98DEBBA56BEA004EC4A9D038AAF9901FF (void);
// 0x00000162 System.Int32 ZXing.QrCode.Internal.ErrorCorrectionLevel::get_Bits()
extern void ErrorCorrectionLevel_get_Bits_m1996EE0BA4324DDFF12D442D9BC4348C337FDF83 (void);
// 0x00000163 System.Int32 ZXing.QrCode.Internal.ErrorCorrectionLevel::ordinal()
extern void ErrorCorrectionLevel_ordinal_m717C8E22648A365039003E81AAFC2CB7323D500B (void);
// 0x00000164 System.String ZXing.QrCode.Internal.ErrorCorrectionLevel::ToString()
extern void ErrorCorrectionLevel_ToString_m0E99708B8EC0B4654AC1780098F1FA4E6D27229D (void);
// 0x00000165 System.Void ZXing.QrCode.Internal.ErrorCorrectionLevel::.cctor()
extern void ErrorCorrectionLevel__cctor_m528E72CA43E538918419806A11CDB226FF1D69F6 (void);
// 0x00000166 System.Int32 ZXing.PDF417.Internal.Dimensions::get_MinCols()
extern void Dimensions_get_MinCols_m7A1880CAF69A8ECF2C7D64D61477D9550C3853E9 (void);
// 0x00000167 System.Int32 ZXing.PDF417.Internal.Dimensions::get_MaxCols()
extern void Dimensions_get_MaxCols_m83484D119218ADE0F17C418F223CF01CD91B3702 (void);
// 0x00000168 System.Int32 ZXing.PDF417.Internal.Dimensions::get_MinRows()
extern void Dimensions_get_MinRows_m758E40D54DD48DD5CD4A5F03BD7759C357889711 (void);
// 0x00000169 System.Int32 ZXing.PDF417.Internal.Dimensions::get_MaxRows()
extern void Dimensions_get_MaxRows_m4C2057281745508367EFA88A021D118B2809AFA9 (void);
// 0x0000016A System.Int32 ZXing.QrCode.Internal.Encoder::calculateMaskPenalty(ZXing.QrCode.Internal.ByteMatrix)
extern void Encoder_calculateMaskPenalty_m8B33C7B7142D7E41B3E54B00775FEE5F7E25D194 (void);
// 0x0000016B ZXing.QrCode.Internal.QRCode ZXing.QrCode.Internal.Encoder::encode(System.String,ZXing.QrCode.Internal.ErrorCorrectionLevel,System.Collections.Generic.IDictionary`2<ZXing.EncodeHintType,System.Object>)
extern void Encoder_encode_m96B75ED56A4C2C5F1E2718E78F435FD0977E66CE (void);
// 0x0000016C ZXing.QrCode.Internal.Version ZXing.QrCode.Internal.Encoder::recommendVersion(ZXing.QrCode.Internal.ErrorCorrectionLevel,ZXing.QrCode.Internal.Mode,ZXing.Common.BitArray,ZXing.Common.BitArray)
extern void Encoder_recommendVersion_m2C0AA86DA09E6358CD4DD91DCC528B8D1BDDF033 (void);
// 0x0000016D System.Int32 ZXing.QrCode.Internal.Encoder::calculateBitsNeeded(ZXing.QrCode.Internal.Mode,ZXing.Common.BitArray,ZXing.Common.BitArray,ZXing.QrCode.Internal.Version)
extern void Encoder_calculateBitsNeeded_mE4D92C04CB45D6943333480362B382670CD94F4C (void);
// 0x0000016E System.Int32 ZXing.QrCode.Internal.Encoder::getAlphanumericCode(System.Int32)
extern void Encoder_getAlphanumericCode_m7E72C6041D31A30F1028AF64C274542922901594 (void);
// 0x0000016F ZXing.QrCode.Internal.Mode ZXing.QrCode.Internal.Encoder::chooseMode(System.String,System.String)
extern void Encoder_chooseMode_m5F3FE17B69E3480679DE0500DE3547F6C0E5AF8C (void);
// 0x00000170 System.Boolean ZXing.QrCode.Internal.Encoder::isOnlyDoubleByteKanji(System.String)
extern void Encoder_isOnlyDoubleByteKanji_mD833812B52E8562F08041FC8032C30B93022F2D8 (void);
// 0x00000171 System.Int32 ZXing.QrCode.Internal.Encoder::chooseMaskPattern(ZXing.Common.BitArray,ZXing.QrCode.Internal.ErrorCorrectionLevel,ZXing.QrCode.Internal.Version,ZXing.QrCode.Internal.ByteMatrix)
extern void Encoder_chooseMaskPattern_mFAFBAC3385B05EA78A88E1975F9C983140E9068B (void);
// 0x00000172 ZXing.QrCode.Internal.Version ZXing.QrCode.Internal.Encoder::chooseVersion(System.Int32,ZXing.QrCode.Internal.ErrorCorrectionLevel)
extern void Encoder_chooseVersion_m14E15AC482D1EB4E7F27409DB69C79B709DB9811 (void);
// 0x00000173 System.Boolean ZXing.QrCode.Internal.Encoder::willFit(System.Int32,ZXing.QrCode.Internal.Version,ZXing.QrCode.Internal.ErrorCorrectionLevel)
extern void Encoder_willFit_m82CB9AD903A0DE4B84DEC207ED9AF289BD3615C6 (void);
// 0x00000174 System.Void ZXing.QrCode.Internal.Encoder::terminateBits(System.Int32,ZXing.Common.BitArray)
extern void Encoder_terminateBits_mD0FC5AC8FEA159EDEC2F543336ED399782A9ADCA (void);
// 0x00000175 System.Void ZXing.QrCode.Internal.Encoder::getNumDataBytesAndNumECBytesForBlockID(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32[],System.Int32[])
extern void Encoder_getNumDataBytesAndNumECBytesForBlockID_m2DEB7EC8301A37269C3FE9E6B329F9B1A196ED2F (void);
// 0x00000176 ZXing.Common.BitArray ZXing.QrCode.Internal.Encoder::interleaveWithECBytes(ZXing.Common.BitArray,System.Int32,System.Int32,System.Int32)
extern void Encoder_interleaveWithECBytes_m6690BBECDA761C173430F2D56A73C13C3E43E204 (void);
// 0x00000177 System.Byte[] ZXing.QrCode.Internal.Encoder::generateECBytes(System.Byte[],System.Int32)
extern void Encoder_generateECBytes_mED468C37E511419419A5DC014A3CF5F73CE0625D (void);
// 0x00000178 System.Void ZXing.QrCode.Internal.Encoder::appendModeInfo(ZXing.QrCode.Internal.Mode,ZXing.Common.BitArray)
extern void Encoder_appendModeInfo_m96EC709BE578A7E80B6738445F113D3B1D011EB6 (void);
// 0x00000179 System.Void ZXing.QrCode.Internal.Encoder::appendLengthInfo(System.Int32,ZXing.QrCode.Internal.Version,ZXing.QrCode.Internal.Mode,ZXing.Common.BitArray)
extern void Encoder_appendLengthInfo_mB738A6FC23FF2BA38C7023A77EF54611B709AAFC (void);
// 0x0000017A System.Void ZXing.QrCode.Internal.Encoder::appendBytes(System.String,ZXing.QrCode.Internal.Mode,ZXing.Common.BitArray,System.String)
extern void Encoder_appendBytes_mBD40910F95A0736540D171E2E440B210E69E9277 (void);
// 0x0000017B System.Void ZXing.QrCode.Internal.Encoder::appendNumericBytes(System.String,ZXing.Common.BitArray)
extern void Encoder_appendNumericBytes_m441A0D6D0C9405F47ACA6FF12B0105CDA30424F7 (void);
// 0x0000017C System.Void ZXing.QrCode.Internal.Encoder::appendAlphanumericBytes(System.String,ZXing.Common.BitArray)
extern void Encoder_appendAlphanumericBytes_mCE43E85EA033076F9D0F306FDB5780F2C1C30EB1 (void);
// 0x0000017D System.Void ZXing.QrCode.Internal.Encoder::append8BitBytes(System.String,ZXing.Common.BitArray,System.String)
extern void Encoder_append8BitBytes_mBAEB7FFCB230FD209DC25BCDA6C52ACC32C5900F (void);
// 0x0000017E System.Void ZXing.QrCode.Internal.Encoder::appendKanjiBytes(System.String,ZXing.Common.BitArray)
extern void Encoder_appendKanjiBytes_m3401104089DF36B750A9277F523A9E8B3F589D07 (void);
// 0x0000017F System.Void ZXing.QrCode.Internal.Encoder::appendECI(ZXing.Common.CharacterSetECI,ZXing.Common.BitArray)
extern void Encoder_appendECI_m0A7938A8A2CE7A03B1D8929FD4D7335FA80116B0 (void);
// 0x00000180 System.Void ZXing.QrCode.Internal.Encoder::.cctor()
extern void Encoder__cctor_mB3981F467D3A46C954D808EBDC824004E20F62AA (void);
// 0x00000181 System.Int32 ZXing.PDF417.Internal.PDF417ErrorCorrection::getErrorCorrectionCodewordCount(System.Int32)
extern void PDF417ErrorCorrection_getErrorCorrectionCodewordCount_mEA8AEA17A48EC6AC34E11EE595A65397441A6FD5 (void);
// 0x00000182 System.String ZXing.PDF417.Internal.PDF417ErrorCorrection::generateErrorCorrection(System.String,System.Int32)
extern void PDF417ErrorCorrection_generateErrorCorrection_m43AEFCF5560F07235E2137D5F77232FDF7FE5BCC (void);
// 0x00000183 System.Void ZXing.PDF417.Internal.PDF417ErrorCorrection::.cctor()
extern void PDF417ErrorCorrection__cctor_mD78DAFD343B9F3D1B24511FDB791B3AC49652F55 (void);
// 0x00000184 System.Void ZXing.PDF417.Internal.PDF417::.ctor()
extern void PDF417__ctor_m93D24A8F67F9A334282A9913CB6EC9AB81ECBA60 (void);
// 0x00000185 System.Void ZXing.PDF417.Internal.PDF417::.ctor(System.Boolean)
extern void PDF417__ctor_m47D14096D8619E8C5E06B68F5D2516205A2F2EBA (void);
// 0x00000186 ZXing.PDF417.Internal.BarcodeMatrix ZXing.PDF417.Internal.PDF417::get_BarcodeMatrix()
extern void PDF417_get_BarcodeMatrix_mB2C1B0E9A02E069AD9DD553627A1124B5AB91A66 (void);
// 0x00000187 System.Int32 ZXing.PDF417.Internal.PDF417::calculateNumberOfRows(System.Int32,System.Int32,System.Int32)
extern void PDF417_calculateNumberOfRows_m25E7F2425401AABED6D516E360DFC1A503127A02 (void);
// 0x00000188 System.Int32 ZXing.PDF417.Internal.PDF417::getNumberOfPadCodewords(System.Int32,System.Int32,System.Int32,System.Int32)
extern void PDF417_getNumberOfPadCodewords_mBAF2AEFE2349F172B1A5FFDF591BD07AEEA27011 (void);
// 0x00000189 System.Void ZXing.PDF417.Internal.PDF417::encodeChar(System.Int32,System.Int32,ZXing.PDF417.Internal.BarcodeRow)
extern void PDF417_encodeChar_m493BC57D3450CB9468915EF96DDC592023F38532 (void);
// 0x0000018A System.Void ZXing.PDF417.Internal.PDF417::encodeLowLevel(System.String,System.Int32,System.Int32,System.Int32,ZXing.PDF417.Internal.BarcodeMatrix)
extern void PDF417_encodeLowLevel_m743AC0FC607671C291A543E16EE050937B9994E5 (void);
// 0x0000018B System.Void ZXing.PDF417.Internal.PDF417::generateBarcodeLogic(System.String,System.Int32)
extern void PDF417_generateBarcodeLogic_mBFA0CB969E407ECF162A4036D890844605ABEEC1 (void);
// 0x0000018C System.Int32[] ZXing.PDF417.Internal.PDF417::determineDimensions(System.Int32,System.Int32)
extern void PDF417_determineDimensions_m513D6E1804969890C40F6EC4E410AAF556DB12C0 (void);
// 0x0000018D System.Void ZXing.PDF417.Internal.PDF417::setDimensions(System.Int32,System.Int32,System.Int32,System.Int32)
extern void PDF417_setDimensions_m4F344A3850D4125EFD506205BB9586E858D34D52 (void);
// 0x0000018E System.Void ZXing.PDF417.Internal.PDF417::setCompaction(ZXing.PDF417.Internal.Compaction)
extern void PDF417_setCompaction_m4816B704079C947B9CD4CCA2199F889C3CDF191C (void);
// 0x0000018F System.Void ZXing.PDF417.Internal.PDF417::setCompact(System.Boolean)
extern void PDF417_setCompact_m7CB850572624F55938D503780DBB1FEFD350D1C3 (void);
// 0x00000190 System.Void ZXing.PDF417.Internal.PDF417::setEncoding(System.String)
extern void PDF417_setEncoding_mA02519C4DCA3607704762D54B6652BCE2EB97689 (void);
// 0x00000191 System.Void ZXing.PDF417.Internal.PDF417::setDisableEci(System.Boolean)
extern void PDF417_setDisableEci_mB64BDFAFCAC129ACB7B08D30BEC4CE8A13BEE401 (void);
// 0x00000192 System.Void ZXing.PDF417.Internal.PDF417::.cctor()
extern void PDF417__cctor_m835F252D0C81243AA4BE3ACEB20D5FAFB06FB68C (void);
// 0x00000193 System.Void BigIntegerLibrary.BigIntegerException::.ctor(System.String,System.Exception)
extern void BigIntegerException__ctor_mEF2948985729D00A43FF017560BE86FF2571885A (void);
// 0x00000194 System.Void ZXing.QrCode.Internal.QRCode::.ctor()
extern void QRCode__ctor_m6867EAB7E4BAC675631F6D5184FC3AE64D3F23CE (void);
// 0x00000195 ZXing.QrCode.Internal.Mode ZXing.QrCode.Internal.QRCode::get_Mode()
extern void QRCode_get_Mode_m33D91E088D7AF12E3E1415A8FFD02DF427D4AC18 (void);
// 0x00000196 System.Void ZXing.QrCode.Internal.QRCode::set_Mode(ZXing.QrCode.Internal.Mode)
extern void QRCode_set_Mode_m54400FA168DDB0166022CCD6BEBDC7D3BAD54960 (void);
// 0x00000197 ZXing.QrCode.Internal.ErrorCorrectionLevel ZXing.QrCode.Internal.QRCode::get_ECLevel()
extern void QRCode_get_ECLevel_m9D85C676D8DDD7A8C2122A2E12F82BEF8E7696C2 (void);
// 0x00000198 System.Void ZXing.QrCode.Internal.QRCode::set_ECLevel(ZXing.QrCode.Internal.ErrorCorrectionLevel)
extern void QRCode_set_ECLevel_m992CE7CECA778D10C8E27038E625ED387DC2387D (void);
// 0x00000199 ZXing.QrCode.Internal.Version ZXing.QrCode.Internal.QRCode::get_Version()
extern void QRCode_get_Version_m2BDFDC6D41ED5BB73ED800BC10F2D0608E4C5E22 (void);
// 0x0000019A System.Void ZXing.QrCode.Internal.QRCode::set_Version(ZXing.QrCode.Internal.Version)
extern void QRCode_set_Version_mCA44F076712002D1BCACB7D1235D9B35EB462748 (void);
// 0x0000019B System.Int32 ZXing.QrCode.Internal.QRCode::get_MaskPattern()
extern void QRCode_get_MaskPattern_mC40211D514C99CAD309D0B364783A85F27D3080A (void);
// 0x0000019C System.Void ZXing.QrCode.Internal.QRCode::set_MaskPattern(System.Int32)
extern void QRCode_set_MaskPattern_m4E89EF31D21F23D4BB15F0E0A52133FFFBF863D5 (void);
// 0x0000019D ZXing.QrCode.Internal.ByteMatrix ZXing.QrCode.Internal.QRCode::get_Matrix()
extern void QRCode_get_Matrix_mC0E1FE9FD38C7194089615225BC7B337A94BC502 (void);
// 0x0000019E System.Void ZXing.QrCode.Internal.QRCode::set_Matrix(ZXing.QrCode.Internal.ByteMatrix)
extern void QRCode_set_Matrix_m58CF18CE0B649E241AE22DD283932C2FC9D166D2 (void);
// 0x0000019F System.String ZXing.QrCode.Internal.QRCode::ToString()
extern void QRCode_ToString_m2D23C64CC443AB7FA6C61C579676C10D389B688D (void);
// 0x000001A0 System.Boolean ZXing.QrCode.Internal.QRCode::isValidMaskPattern(System.Int32)
extern void QRCode_isValidMaskPattern_m0F1782ACB752B172EF7CE871F0E950E13506AA6E (void);
// 0x000001A1 System.Void ZXing.QrCode.Internal.QRCode::.cctor()
extern void QRCode__cctor_mE81B64EA7C83CE3139BA3EAB8F9DCF68CA581AC0 (void);
// 0x000001A2 System.Void ZXing.QrCode.Internal.MatrixUtil::clearMatrix(ZXing.QrCode.Internal.ByteMatrix)
extern void MatrixUtil_clearMatrix_mFB1FA3A46CA97A6C0F89F668A2BF6C5BD73FA26B (void);
// 0x000001A3 System.Void ZXing.QrCode.Internal.MatrixUtil::buildMatrix(ZXing.Common.BitArray,ZXing.QrCode.Internal.ErrorCorrectionLevel,ZXing.QrCode.Internal.Version,System.Int32,ZXing.QrCode.Internal.ByteMatrix)
extern void MatrixUtil_buildMatrix_m2791F3ED643F2A9AF4CD1F06386DBDC641AF0BA1 (void);
// 0x000001A4 System.Void ZXing.QrCode.Internal.MatrixUtil::embedBasicPatterns(ZXing.QrCode.Internal.Version,ZXing.QrCode.Internal.ByteMatrix)
extern void MatrixUtil_embedBasicPatterns_mD5E469E80E5FB72EED772505ED40C4024A409BED (void);
// 0x000001A5 System.Void ZXing.QrCode.Internal.MatrixUtil::embedTypeInfo(ZXing.QrCode.Internal.ErrorCorrectionLevel,System.Int32,ZXing.QrCode.Internal.ByteMatrix)
extern void MatrixUtil_embedTypeInfo_mD43B1A38AD8BEE418D744134E0EC6F4BF52FF120 (void);
// 0x000001A6 System.Void ZXing.QrCode.Internal.MatrixUtil::maybeEmbedVersionInfo(ZXing.QrCode.Internal.Version,ZXing.QrCode.Internal.ByteMatrix)
extern void MatrixUtil_maybeEmbedVersionInfo_m4E40671CF4E3F2613089CE71EF3EC3C32720710D (void);
// 0x000001A7 System.Void ZXing.QrCode.Internal.MatrixUtil::embedDataBits(ZXing.Common.BitArray,System.Int32,ZXing.QrCode.Internal.ByteMatrix)
extern void MatrixUtil_embedDataBits_m6982E017A519BECEE4B449B18367DF22C794D3BE (void);
// 0x000001A8 System.Int32 ZXing.QrCode.Internal.MatrixUtil::findMSBSet(System.Int32)
extern void MatrixUtil_findMSBSet_mAF6FFAACA226C938479F9C44228B7E0F8EDDFB9C (void);
// 0x000001A9 System.Int32 ZXing.QrCode.Internal.MatrixUtil::calculateBCHCode(System.Int32,System.Int32)
extern void MatrixUtil_calculateBCHCode_m96802D858DC2D100DB04F66C4DF6342A12FB4C98 (void);
// 0x000001AA System.Void ZXing.QrCode.Internal.MatrixUtil::makeTypeInfoBits(ZXing.QrCode.Internal.ErrorCorrectionLevel,System.Int32,ZXing.Common.BitArray)
extern void MatrixUtil_makeTypeInfoBits_mD20DC2EAD0F2E0241EE918710E2E23CF3CD7AD86 (void);
// 0x000001AB System.Void ZXing.QrCode.Internal.MatrixUtil::makeVersionInfoBits(ZXing.QrCode.Internal.Version,ZXing.Common.BitArray)
extern void MatrixUtil_makeVersionInfoBits_m5B7A35EC194799F421B5B361FC570469510D1C3F (void);
// 0x000001AC System.Boolean ZXing.QrCode.Internal.MatrixUtil::isEmpty(System.Int32)
extern void MatrixUtil_isEmpty_m5EF05BE37D525B3BF155A8F9A2FFD71D722175B8 (void);
// 0x000001AD System.Void ZXing.QrCode.Internal.MatrixUtil::embedTimingPatterns(ZXing.QrCode.Internal.ByteMatrix)
extern void MatrixUtil_embedTimingPatterns_mF1B197899FB1AC4B98A14926927DB8B3A8F49125 (void);
// 0x000001AE System.Void ZXing.QrCode.Internal.MatrixUtil::embedDarkDotAtLeftBottomCorner(ZXing.QrCode.Internal.ByteMatrix)
extern void MatrixUtil_embedDarkDotAtLeftBottomCorner_m1A054C19B525928ED8F96DEA040758D9CEC79CBC (void);
// 0x000001AF System.Void ZXing.QrCode.Internal.MatrixUtil::embedHorizontalSeparationPattern(System.Int32,System.Int32,ZXing.QrCode.Internal.ByteMatrix)
extern void MatrixUtil_embedHorizontalSeparationPattern_mCB6C1D5DCFB13C0ECF7CB482E1F1D0CF775FDF50 (void);
// 0x000001B0 System.Void ZXing.QrCode.Internal.MatrixUtil::embedVerticalSeparationPattern(System.Int32,System.Int32,ZXing.QrCode.Internal.ByteMatrix)
extern void MatrixUtil_embedVerticalSeparationPattern_m025D10BF0F80BDD652070F71627CBB6A8C071263 (void);
// 0x000001B1 System.Void ZXing.QrCode.Internal.MatrixUtil::embedPositionAdjustmentPattern(System.Int32,System.Int32,ZXing.QrCode.Internal.ByteMatrix)
extern void MatrixUtil_embedPositionAdjustmentPattern_m9A1906444B10909451711EA6CE575C75B93F5D44 (void);
// 0x000001B2 System.Void ZXing.QrCode.Internal.MatrixUtil::embedPositionDetectionPattern(System.Int32,System.Int32,ZXing.QrCode.Internal.ByteMatrix)
extern void MatrixUtil_embedPositionDetectionPattern_m79F17C3CC6FD15A239269D65600584BFF76C9B21 (void);
// 0x000001B3 System.Void ZXing.QrCode.Internal.MatrixUtil::embedPositionDetectionPatternsAndSeparators(ZXing.QrCode.Internal.ByteMatrix)
extern void MatrixUtil_embedPositionDetectionPatternsAndSeparators_mD71AEEA9842EA32C7572B23CE9A2DACF29EE6F18 (void);
// 0x000001B4 System.Void ZXing.QrCode.Internal.MatrixUtil::maybeEmbedPositionAdjustmentPatterns(ZXing.QrCode.Internal.Version,ZXing.QrCode.Internal.ByteMatrix)
extern void MatrixUtil_maybeEmbedPositionAdjustmentPatterns_m262C872CADECD02718E0F6274BAF007FBF18FCC0 (void);
// 0x000001B5 System.Void ZXing.QrCode.Internal.MatrixUtil::.cctor()
extern void MatrixUtil__cctor_m5D5C630462D42E27F18BD725E2FF294489DDAE93 (void);
// 0x000001B6 System.Int32 ZXing.Datamatrix.Encoder.TextEncoder::get_EncodingMode()
extern void TextEncoder_get_EncodingMode_m4875AEF0A71342324D0A4D6F75E2A06F4C42C53C (void);
// 0x000001B7 System.Int32 ZXing.Datamatrix.Encoder.TextEncoder::encodeChar(System.Char,System.Text.StringBuilder)
extern void TextEncoder_encodeChar_m4571E8B515AE01125CBED86BEF7E8375EC414A3D (void);
// 0x000001B8 System.Void ZXing.Datamatrix.Encoder.TextEncoder::.ctor()
extern void TextEncoder__ctor_m305C31389F2E89196CA33029EF4EC0307A1D433B (void);
// 0x000001B9 System.Void ZXing.Datamatrix.Encoder.EncoderContext::.cctor()
extern void EncoderContext__cctor_mDEA4FB44ABA7602407D5D4620B42F81C1343762D (void);
// 0x000001BA System.Void ZXing.Datamatrix.Encoder.EncoderContext::.ctor(System.String)
extern void EncoderContext__ctor_mBF80EA74A77D043BE118FB4E8F77830D71A8B628 (void);
// 0x000001BB System.Void ZXing.Datamatrix.Encoder.EncoderContext::setSymbolShape(ZXing.Datamatrix.Encoder.SymbolShapeHint)
extern void EncoderContext_setSymbolShape_m178345421EF427C814491E91BDE404A6E1EBE78C (void);
// 0x000001BC System.Void ZXing.Datamatrix.Encoder.EncoderContext::setSizeConstraints(ZXing.Dimension,ZXing.Dimension)
extern void EncoderContext_setSizeConstraints_m9DF4D765CE06AB7D295CCECB1BCFDEDB79A51042 (void);
// 0x000001BD System.Void ZXing.Datamatrix.Encoder.EncoderContext::setSkipAtEnd(System.Int32)
extern void EncoderContext_setSkipAtEnd_m898D4D40F711C018AA308A9E5D32C94962D57DEC (void);
// 0x000001BE System.Char ZXing.Datamatrix.Encoder.EncoderContext::get_CurrentChar()
extern void EncoderContext_get_CurrentChar_m17CC24F702F8019C3EBC108ABFA352D2F7DAB9A3 (void);
// 0x000001BF System.Void ZXing.Datamatrix.Encoder.EncoderContext::writeCodewords(System.String)
extern void EncoderContext_writeCodewords_m39CEDD0E43F69BBF65546DA9A4415EF4DA9DC4DE (void);
// 0x000001C0 System.Void ZXing.Datamatrix.Encoder.EncoderContext::writeCodeword(System.Char)
extern void EncoderContext_writeCodeword_m05A8D6AAF3A9918D18A2F7F9F3A36E4E911F38E8 (void);
// 0x000001C1 System.Int32 ZXing.Datamatrix.Encoder.EncoderContext::get_CodewordCount()
extern void EncoderContext_get_CodewordCount_m86A2A4383E2C210DA7DAE7D1C180D1BC90238798 (void);
// 0x000001C2 System.Void ZXing.Datamatrix.Encoder.EncoderContext::signalEncoderChange(System.Int32)
extern void EncoderContext_signalEncoderChange_mADF1DAD66E8381B308016EEB037F640723B8E35D (void);
// 0x000001C3 System.Void ZXing.Datamatrix.Encoder.EncoderContext::resetEncoderSignal()
extern void EncoderContext_resetEncoderSignal_m58792CF544A12F27D34719F3A0D80BFBD01B8FE5 (void);
// 0x000001C4 System.Boolean ZXing.Datamatrix.Encoder.EncoderContext::get_HasMoreCharacters()
extern void EncoderContext_get_HasMoreCharacters_m91BCE0DF2F4D8EE92502837726242CE1AB84EC1A (void);
// 0x000001C5 System.Int32 ZXing.Datamatrix.Encoder.EncoderContext::get_TotalMessageCharCount()
extern void EncoderContext_get_TotalMessageCharCount_m0B56335D75E613B1C3DDC89EDB9A8F9F81365288 (void);
// 0x000001C6 System.Int32 ZXing.Datamatrix.Encoder.EncoderContext::get_RemainingCharacters()
extern void EncoderContext_get_RemainingCharacters_m0D4877E225AB71AF61E4049F92FB38B4340A8FEB (void);
// 0x000001C7 System.Void ZXing.Datamatrix.Encoder.EncoderContext::updateSymbolInfo()
extern void EncoderContext_updateSymbolInfo_mD58B4A0EA2A8ED121E31293CF2464A8A01FB4C7F (void);
// 0x000001C8 System.Void ZXing.Datamatrix.Encoder.EncoderContext::updateSymbolInfo(System.Int32)
extern void EncoderContext_updateSymbolInfo_m2C4D112B1B96EA9134040ADBB3F89EBA4BFA57B2 (void);
// 0x000001C9 System.Void ZXing.Datamatrix.Encoder.EncoderContext::resetSymbolInfo()
extern void EncoderContext_resetSymbolInfo_m3C76FC3374B3C6D3CA91A8C396729054333E4D6A (void);
// 0x000001CA System.Int32 ZXing.Datamatrix.Encoder.EncoderContext::get_Pos()
extern void EncoderContext_get_Pos_mB6FC37697D743FBE341043898E7B659559A5DECE (void);
// 0x000001CB System.Void ZXing.Datamatrix.Encoder.EncoderContext::set_Pos(System.Int32)
extern void EncoderContext_set_Pos_m11E60F0C66BD176B5030FCEB7A51452F28DCD522 (void);
// 0x000001CC System.Text.StringBuilder ZXing.Datamatrix.Encoder.EncoderContext::get_Codewords()
extern void EncoderContext_get_Codewords_m5D546339A26D9E229618EA64442983956D38012D (void);
// 0x000001CD ZXing.Datamatrix.Encoder.SymbolInfo ZXing.Datamatrix.Encoder.EncoderContext::get_SymbolInfo()
extern void EncoderContext_get_SymbolInfo_m94C5BE03529789BE8897F207EAC85431F4D2A316 (void);
// 0x000001CE System.Int32 ZXing.Datamatrix.Encoder.EncoderContext::get_NewEncoding()
extern void EncoderContext_get_NewEncoding_m7B6E49E8BCF0FC93642D2F58B3490B71F1E8AD57 (void);
// 0x000001CF System.String ZXing.Datamatrix.Encoder.EncoderContext::get_Message()
extern void EncoderContext_get_Message_m0CF5394F9C84697DBAD2DC5722C8EADC38E2074F (void);
// 0x000001D0 System.Void ZXing.Aztec.AztecWriter::.cctor()
extern void AztecWriter__cctor_m325BC1185CCF82D7E8524712CD0BC2E8662BD012 (void);
// 0x000001D1 ZXing.Common.BitMatrix ZXing.Aztec.AztecWriter::encode(System.String,ZXing.BarcodeFormat,System.Int32,System.Int32,System.Collections.Generic.IDictionary`2<ZXing.EncodeHintType,System.Object>)
extern void AztecWriter_encode_m0C18DECD2EA767DF24A8516B8E68D98BE30E8E70 (void);
// 0x000001D2 ZXing.Common.BitMatrix ZXing.Aztec.AztecWriter::encode(System.String,ZXing.BarcodeFormat,System.Int32,System.Int32,System.Text.Encoding,System.Int32,System.Int32)
extern void AztecWriter_encode_mBA3B22B21C0ECC52618CBC5EA817E875036B87FC (void);
// 0x000001D3 ZXing.Common.BitMatrix ZXing.Aztec.AztecWriter::renderResult(ZXing.Aztec.Internal.AztecCode,System.Int32,System.Int32)
extern void AztecWriter_renderResult_mD8895976254AB61BE2A0F3F390653C66695C46B0 (void);
// 0x000001D4 System.Void ZXing.Aztec.AztecWriter::.ctor()
extern void AztecWriter__ctor_m68A2CE5F202CE68C5485B6DFAD805D6F5CF31DEF (void);
// 0x000001D5 System.Void ZXing.QrCode.Internal.Mode::.ctor(System.Int32[],System.Int32,System.String)
extern void Mode__ctor_m30529DF91C26A385B7AEDD573D6DF857DF6778B8 (void);
// 0x000001D6 System.Int32 ZXing.QrCode.Internal.Mode::getCharacterCountBits(ZXing.QrCode.Internal.Version)
extern void Mode_getCharacterCountBits_mD54D1539285D6B814D6FA286FB140CA84A3FD839 (void);
// 0x000001D7 System.Int32 ZXing.QrCode.Internal.Mode::get_Bits()
extern void Mode_get_Bits_m94C8659A798B5D29EC10DF332242A14FC011788C (void);
// 0x000001D8 System.String ZXing.QrCode.Internal.Mode::ToString()
extern void Mode_ToString_m484CD08D1B6B629BFDA2ADD7BC410BE6D756A11F (void);
// 0x000001D9 System.Void ZXing.QrCode.Internal.Mode::.cctor()
extern void Mode__cctor_m6F86E9E1D428864EFD332F6916180019B4960CD7 (void);
// 0x000001DA ZXing.Common.BitMatrix ZXing.OneD.Code39Writer::encode(System.String,ZXing.BarcodeFormat,System.Int32,System.Int32,System.Collections.Generic.IDictionary`2<ZXing.EncodeHintType,System.Object>)
extern void Code39Writer_encode_mEDEC18DD7891E787FAFD5072325A0F79B0384D2B (void);
// 0x000001DB System.Boolean[] ZXing.OneD.Code39Writer::encode(System.String)
extern void Code39Writer_encode_mCD345524C18B36368A2EC18CE2BDFEAA74BA3A39 (void);
// 0x000001DC System.Void ZXing.OneD.Code39Writer::toIntArray(System.Int32,System.Int32[])
extern void Code39Writer_toIntArray_m26C96983BE3C2F3CAE30EBCDF8B5CA15E091FF54 (void);
// 0x000001DD System.Void ZXing.OneD.Code39Writer::.ctor()
extern void Code39Writer__ctor_m047B0C071B41C496FA64DCA019FAA2C07275D30D (void);
// 0x000001DE System.Void ZXing.OneD.Code39Reader::.cctor()
extern void Code39Reader__cctor_m0A682F3C5EC7D1D26C032C63F49F975C091E09A7 (void);
// 0x000001DF System.Void ZXing.OneD.Code128Reader::.cctor()
extern void Code128Reader__cctor_mE66EA0E3DAE56B2F268E3F32280DC5BEEE42777E (void);
// 0x000001E0 System.Void ZXing.Datamatrix.Encoder.DataMatrixSymbolInfo144::.ctor()
extern void DataMatrixSymbolInfo144__ctor_m27A624BEE81E8557E1B260AF5867D89F74C393C0 (void);
// 0x000001E1 System.Int32 ZXing.Datamatrix.Encoder.DataMatrixSymbolInfo144::getInterleavedBlockCount()
extern void DataMatrixSymbolInfo144_getInterleavedBlockCount_m1E361720B32850C61DE44EF9818DF203FA24DB46 (void);
// 0x000001E2 System.Int32 ZXing.Datamatrix.Encoder.DataMatrixSymbolInfo144::getDataLengthForInterleavedBlock(System.Int32)
extern void DataMatrixSymbolInfo144_getDataLengthForInterleavedBlock_mBB4E7B788BEF4F6CCBAD78319AB4065A43A131E9 (void);
// 0x000001E3 ZXing.Common.BitMatrix ZXing.OneD.Code128Writer::encode(System.String,ZXing.BarcodeFormat,System.Int32,System.Int32,System.Collections.Generic.IDictionary`2<ZXing.EncodeHintType,System.Object>)
extern void Code128Writer_encode_m5E12E96D86B07D6E80A41AD3610AB0C86BE2A008 (void);
// 0x000001E4 System.Boolean[] ZXing.OneD.Code128Writer::encode(System.String)
extern void Code128Writer_encode_mD8779CE4AA67BD5784CABABF0AC844781E7E57B6 (void);
// 0x000001E5 ZXing.OneD.Code128Writer/CType ZXing.OneD.Code128Writer::findCType(System.String,System.Int32)
extern void Code128Writer_findCType_mEA72367E6ACA06126078A74AB1B2EA36E0BB6579 (void);
// 0x000001E6 System.Int32 ZXing.OneD.Code128Writer::chooseCode(System.String,System.Int32,System.Int32)
extern void Code128Writer_chooseCode_m4148356E098C3163D47B5EA6B60939F39D4967F0 (void);
// 0x000001E7 System.Void ZXing.OneD.Code128Writer::.ctor()
extern void Code128Writer__ctor_m9E0C59215CBCC1C5BA789210DD1D17F6719D1715 (void);
// 0x000001E8 System.Void ZXing.Common.ReedSolomon.GenericGF::.ctor(System.Int32,System.Int32,System.Int32)
extern void GenericGF__ctor_m858EC075E3000FB217E9BBCBE24D1E28835950C4 (void);
// 0x000001E9 ZXing.Common.ReedSolomon.GenericGFPoly ZXing.Common.ReedSolomon.GenericGF::get_Zero()
extern void GenericGF_get_Zero_m4EEFD36C577BDD3A2FEE9CCED46017E10746A11C (void);
// 0x000001EA ZXing.Common.ReedSolomon.GenericGFPoly ZXing.Common.ReedSolomon.GenericGF::buildMonomial(System.Int32,System.Int32)
extern void GenericGF_buildMonomial_m6A66CDEF54C10D656775279BB62FA2C6491172B0 (void);
// 0x000001EB System.Int32 ZXing.Common.ReedSolomon.GenericGF::addOrSubtract(System.Int32,System.Int32)
extern void GenericGF_addOrSubtract_mD0C8013C59FE84B282C354A492B3DCAA4234BFCC (void);
// 0x000001EC System.Int32 ZXing.Common.ReedSolomon.GenericGF::exp(System.Int32)
extern void GenericGF_exp_mC107E851341CAF73E04862126BE9989124F888D5 (void);
// 0x000001ED System.Int32 ZXing.Common.ReedSolomon.GenericGF::log(System.Int32)
extern void GenericGF_log_m6729ED61D75F4EEC4A342CDFF682D221F18B17F9 (void);
// 0x000001EE System.Int32 ZXing.Common.ReedSolomon.GenericGF::inverse(System.Int32)
extern void GenericGF_inverse_m58997A25B60795AA9DAA8E3EF50FD5D59AF79A0C (void);
// 0x000001EF System.Int32 ZXing.Common.ReedSolomon.GenericGF::multiply(System.Int32,System.Int32)
extern void GenericGF_multiply_m6D437E1CB1E60B71FD8A28B43D6FC99D2771D1E3 (void);
// 0x000001F0 System.Int32 ZXing.Common.ReedSolomon.GenericGF::get_GeneratorBase()
extern void GenericGF_get_GeneratorBase_m51D32132194536409A90BBA7092BAFBD97B135D8 (void);
// 0x000001F1 System.String ZXing.Common.ReedSolomon.GenericGF::ToString()
extern void GenericGF_ToString_mBBDC59FCEC7D637116E92C056A3C400B542D9298 (void);
// 0x000001F2 System.Void ZXing.Common.ReedSolomon.GenericGF::.cctor()
extern void GenericGF__cctor_mD7C5F6B412C04D6DCC66F2335389F79EF5886B51 (void);
// 0x000001F3 ZXing.Common.BitMatrix ZXing.PDF417.PDF417Writer::encode(System.String,ZXing.BarcodeFormat,System.Int32,System.Int32,System.Collections.Generic.IDictionary`2<ZXing.EncodeHintType,System.Object>)
extern void PDF417Writer_encode_m7699D2D421E9B479760DCCE7604A856615CE37D2 (void);
// 0x000001F4 ZXing.Common.BitMatrix ZXing.PDF417.PDF417Writer::bitMatrixFromEncoder(ZXing.PDF417.Internal.PDF417,System.String,System.Int32,System.Int32,System.Int32,System.Int32)
extern void PDF417Writer_bitMatrixFromEncoder_m2B741D147065C75A49BF2608E0D341FC55D73527 (void);
// 0x000001F5 ZXing.Common.BitMatrix ZXing.PDF417.PDF417Writer::bitMatrixFromBitArray(System.SByte[][],System.Int32)
extern void PDF417Writer_bitMatrixFromBitArray_m45330B6397A00DE687156FF47174E719BC28FF41 (void);
// 0x000001F6 System.SByte[][] ZXing.PDF417.PDF417Writer::rotateArray(System.SByte[][])
extern void PDF417Writer_rotateArray_m263B428C14106F1B14B7BBABDBAFE72B79155853 (void);
// 0x000001F7 System.Void ZXing.PDF417.PDF417Writer::.ctor()
extern void PDF417Writer__ctor_m8F8732C49C0D65ADC86091000B11A9F1D7CBCC31 (void);
// 0x000001F8 System.Void ZXing.PDF417.Internal.PDF417HighLevelEncoder::.cctor()
extern void PDF417HighLevelEncoder__cctor_m108515117932F302E16D62D08247D94CA8C46880 (void);
// 0x000001F9 System.String ZXing.PDF417.Internal.PDF417HighLevelEncoder::encodeHighLevel(System.String,ZXing.PDF417.Internal.Compaction,System.Text.Encoding,System.Boolean)
extern void PDF417HighLevelEncoder_encodeHighLevel_mFF5A65A48EA2E9F0C8C19DF68FDCCA33B6A658C7 (void);
// 0x000001FA System.Text.Encoding ZXing.PDF417.Internal.PDF417HighLevelEncoder::getEncoder(System.Text.Encoding)
extern void PDF417HighLevelEncoder_getEncoder_mC33CAFB0D8F857745953169B5C6D5A387454C4CB (void);
// 0x000001FB System.Byte[] ZXing.PDF417.Internal.PDF417HighLevelEncoder::toBytes(System.String,System.Text.Encoding)
extern void PDF417HighLevelEncoder_toBytes_mCA3BD9C74A1F558EBB93C2F1B26CBC90E9657D40 (void);
// 0x000001FC System.Byte[] ZXing.PDF417.Internal.PDF417HighLevelEncoder::toBytes(System.Char,System.Text.Encoding)
extern void PDF417HighLevelEncoder_toBytes_m3FF17BC2CC9757F53FEC3123691F8434CF469C45 (void);
// 0x000001FD System.Int32 ZXing.PDF417.Internal.PDF417HighLevelEncoder::encodeText(System.String,System.Int32,System.Int32,System.Text.StringBuilder,System.Int32)
extern void PDF417HighLevelEncoder_encodeText_mE11968B5A4063A12EEC2D0760C2D43E8127ECC97 (void);
// 0x000001FE System.Void ZXing.PDF417.Internal.PDF417HighLevelEncoder::encodeBinary(System.Byte[],System.Int32,System.Int32,System.Int32,System.Text.StringBuilder)
extern void PDF417HighLevelEncoder_encodeBinary_mF4B21686DCC0879847E22088105DCAB2BC92B438 (void);
// 0x000001FF System.Void ZXing.PDF417.Internal.PDF417HighLevelEncoder::encodeNumeric(System.String,System.Int32,System.Int32,System.Text.StringBuilder)
extern void PDF417HighLevelEncoder_encodeNumeric_mA56D276C844D3946530E26FFA1611CC0DADBED15 (void);
// 0x00000200 System.Boolean ZXing.PDF417.Internal.PDF417HighLevelEncoder::isDigit(System.Char)
extern void PDF417HighLevelEncoder_isDigit_mCF967756CE968C9813CCE4B04E5D311834388C50 (void);
// 0x00000201 System.Boolean ZXing.PDF417.Internal.PDF417HighLevelEncoder::isAlphaUpper(System.Char)
extern void PDF417HighLevelEncoder_isAlphaUpper_mCABD8674B0586BC3E9E8CDE131C059B602E7ED02 (void);
// 0x00000202 System.Boolean ZXing.PDF417.Internal.PDF417HighLevelEncoder::isAlphaLower(System.Char)
extern void PDF417HighLevelEncoder_isAlphaLower_m44625FACE0995207CA9898B7A324A2C16B759F1D (void);
// 0x00000203 System.Boolean ZXing.PDF417.Internal.PDF417HighLevelEncoder::isMixed(System.Char)
extern void PDF417HighLevelEncoder_isMixed_m728EBC37573093DB853D7137D09D42E344B6E535 (void);
// 0x00000204 System.Boolean ZXing.PDF417.Internal.PDF417HighLevelEncoder::isPunctuation(System.Char)
extern void PDF417HighLevelEncoder_isPunctuation_m6FE529488EC7EB0428443D5BFC4210547ED22808 (void);
// 0x00000205 System.Boolean ZXing.PDF417.Internal.PDF417HighLevelEncoder::isText(System.Char)
extern void PDF417HighLevelEncoder_isText_mF4B1D5E6A4BA01BCE0D9CA001DAB29B3F7F40EF0 (void);
// 0x00000206 System.Int32 ZXing.PDF417.Internal.PDF417HighLevelEncoder::determineConsecutiveDigitCount(System.String,System.Int32)
extern void PDF417HighLevelEncoder_determineConsecutiveDigitCount_m42A40C3DD6FA77F0B61A2B84920EB96483B5D1FD (void);
// 0x00000207 System.Int32 ZXing.PDF417.Internal.PDF417HighLevelEncoder::determineConsecutiveTextCount(System.String,System.Int32)
extern void PDF417HighLevelEncoder_determineConsecutiveTextCount_m677252E3DE1036949B196F3CBD51EBA11C240CC3 (void);
// 0x00000208 System.Int32 ZXing.PDF417.Internal.PDF417HighLevelEncoder::determineConsecutiveBinaryCount(System.String,System.Byte[],System.Int32,System.Text.Encoding)
extern void PDF417HighLevelEncoder_determineConsecutiveBinaryCount_m9AF736A6510DEA4BFE08B5E8934D9DC14B623B55 (void);
// 0x00000209 System.Void ZXing.PDF417.Internal.PDF417HighLevelEncoder::encodingECI(System.Int32,System.Text.StringBuilder)
extern void PDF417HighLevelEncoder_encodingECI_m95C4C13DBEE538EDA48AB82686DBAFEC4BD3785E (void);
// 0x0000020A System.Void ZXing.Datamatrix.Encoder.ErrorCorrection::.cctor()
extern void ErrorCorrection__cctor_mE1C9C18E909F701D341AD81D46C89F9134771ED5 (void);
// 0x0000020B System.String ZXing.Datamatrix.Encoder.ErrorCorrection::encodeECC200(System.String,ZXing.Datamatrix.Encoder.SymbolInfo)
extern void ErrorCorrection_encodeECC200_m160DCB833BC11C55446D3521D9F494E7E39A9D78 (void);
// 0x0000020C System.String ZXing.Datamatrix.Encoder.ErrorCorrection::createECCBlock(System.String,System.Int32)
extern void ErrorCorrection_createECCBlock_m638C707ACC7AF5DFB5BADBAC349C6933C79FAB07 (void);
// 0x0000020D System.String ZXing.Datamatrix.Encoder.ErrorCorrection::createECCBlock(System.String,System.Int32,System.Int32,System.Int32)
extern void ErrorCorrection_createECCBlock_mA50D025D41B420C60A1D3A35B595B8BBF83E49DA (void);
// 0x0000020E System.Void ZXing.Aztec.Internal.BinaryShiftToken::.ctor(ZXing.Aztec.Internal.Token,System.Int32,System.Int32)
extern void BinaryShiftToken__ctor_mF766F25760FBFBA67EAA4C0F056D4AB55B104F21 (void);
// 0x0000020F System.Void ZXing.Aztec.Internal.BinaryShiftToken::appendTo(ZXing.Common.BitArray,System.Byte[])
extern void BinaryShiftToken_appendTo_m9487F04591FA959C965E1C26C909F67D0C7A7437 (void);
// 0x00000210 System.String ZXing.Aztec.Internal.BinaryShiftToken::ToString()
extern void BinaryShiftToken_ToString_m05A85E2B87DD718FC0F80545503E5C5F98FB78D5 (void);
// 0x00000211 System.Int32 ZXing.Datamatrix.Encoder.Base256Encoder::get_EncodingMode()
extern void Base256Encoder_get_EncodingMode_mC0FFCC7F1D061E83B0A8C9E85890FB0C94BE6584 (void);
// 0x00000212 System.Void ZXing.Datamatrix.Encoder.Base256Encoder::encode(ZXing.Datamatrix.Encoder.EncoderContext)
extern void Base256Encoder_encode_mD09F578294B00F96C72903D3FAFEC459FD60BE0B (void);
// 0x00000213 System.Char ZXing.Datamatrix.Encoder.Base256Encoder::randomize255State(System.Char,System.Int32)
extern void Base256Encoder_randomize255State_mC9D64C4A5FB394D21D7282EBB4F3907054AF5DEE (void);
// 0x00000214 System.Void ZXing.Datamatrix.Encoder.Base256Encoder::.ctor()
extern void Base256Encoder__ctor_mBA7F858CB96577D285AF7B39A3AE61E20D5B30AF (void);
// 0x00000215 ZXing.Common.BitMatrix ZXing.Datamatrix.DataMatrixWriter::encode(System.String,ZXing.BarcodeFormat,System.Int32,System.Int32,System.Collections.Generic.IDictionary`2<ZXing.EncodeHintType,System.Object>)
extern void DataMatrixWriter_encode_m459C61FE570E29E6B3FC8583ED3FB3A821883513 (void);
// 0x00000216 ZXing.Common.BitMatrix ZXing.Datamatrix.DataMatrixWriter::encodeLowLevel(ZXing.Datamatrix.Encoder.DefaultPlacement,ZXing.Datamatrix.Encoder.SymbolInfo)
extern void DataMatrixWriter_encodeLowLevel_m3B133AC3EB00D4B18958B0CA44C9887AD2D92A58 (void);
// 0x00000217 ZXing.Common.BitMatrix ZXing.Datamatrix.DataMatrixWriter::convertByteMatrixToBitMatrix(ZXing.QrCode.Internal.ByteMatrix)
extern void DataMatrixWriter_convertByteMatrixToBitMatrix_mB156C7B7ED0F2411078C8A9A15AFEAD7A70C3FB0 (void);
// 0x00000218 System.Void ZXing.Datamatrix.DataMatrixWriter::.ctor()
extern void DataMatrixWriter__ctor_mF7A94D25DE8C163B4D015FA1A65271AE71A6C0DA (void);
// 0x00000219 System.Void BigIntegerLibrary.Base10BigInteger::set_NumberSign(BigIntegerLibrary.Sign)
extern void Base10BigInteger_set_NumberSign_m773477D89D3B7D646368DF35BD90180A3DC8A41D (void);
// 0x0000021A System.Void BigIntegerLibrary.Base10BigInteger::.ctor()
extern void Base10BigInteger__ctor_mC940B222E4109A3FDF1818F9EEBBD04AA4E943D9 (void);
// 0x0000021B System.Void BigIntegerLibrary.Base10BigInteger::.ctor(System.Int64)
extern void Base10BigInteger__ctor_m44001801B40CED902AC51B373C4E510EC3B806EE (void);
// 0x0000021C System.Void BigIntegerLibrary.Base10BigInteger::.ctor(BigIntegerLibrary.Base10BigInteger)
extern void Base10BigInteger__ctor_m3FCB0509458DD98919172F0AAFF76143D465673F (void);
// 0x0000021D System.Boolean BigIntegerLibrary.Base10BigInteger::Equals(BigIntegerLibrary.Base10BigInteger)
extern void Base10BigInteger_Equals_m614429AF42C01075F3324C45C095FD9D07E02E73 (void);
// 0x0000021E System.Boolean BigIntegerLibrary.Base10BigInteger::Equals(System.Object)
extern void Base10BigInteger_Equals_m3F08A8AFCBA72BCCBE9A861BC9AF69ECA2B48D68 (void);
// 0x0000021F System.Int32 BigIntegerLibrary.Base10BigInteger::GetHashCode()
extern void Base10BigInteger_GetHashCode_mF9EA95875A037AF6BAE361381D8BA72D21763606 (void);
// 0x00000220 System.String BigIntegerLibrary.Base10BigInteger::ToString()
extern void Base10BigInteger_ToString_m7AD628696B4F0B5B26BAF294787BC8C6FCDABC8C (void);
// 0x00000221 BigIntegerLibrary.Base10BigInteger BigIntegerLibrary.Base10BigInteger::Opposite(BigIntegerLibrary.Base10BigInteger)
extern void Base10BigInteger_Opposite_m45CE78E2AD7420FCC684EBE446E66A1A8CB92C0E (void);
// 0x00000222 System.Boolean BigIntegerLibrary.Base10BigInteger::Greater(BigIntegerLibrary.Base10BigInteger,BigIntegerLibrary.Base10BigInteger)
extern void Base10BigInteger_Greater_m92D72CD61784C39F77FA8D63FBC255C3A836A64E (void);
// 0x00000223 System.Boolean BigIntegerLibrary.Base10BigInteger::GreaterOrEqual(BigIntegerLibrary.Base10BigInteger,BigIntegerLibrary.Base10BigInteger)
extern void Base10BigInteger_GreaterOrEqual_mE4A22463595CED0F882D5A54AEC298DAF2C03146 (void);
// 0x00000224 System.Boolean BigIntegerLibrary.Base10BigInteger::SmallerOrEqual(BigIntegerLibrary.Base10BigInteger,BigIntegerLibrary.Base10BigInteger)
extern void Base10BigInteger_SmallerOrEqual_m0807CF213E551BE52C302DB58CE7336642E1A988 (void);
// 0x00000225 BigIntegerLibrary.Base10BigInteger BigIntegerLibrary.Base10BigInteger::Abs(BigIntegerLibrary.Base10BigInteger)
extern void Base10BigInteger_Abs_m41837AA441C42EFB42B40B7E0D03445B054C7D32 (void);
// 0x00000226 BigIntegerLibrary.Base10BigInteger BigIntegerLibrary.Base10BigInteger::Addition(BigIntegerLibrary.Base10BigInteger,BigIntegerLibrary.Base10BigInteger)
extern void Base10BigInteger_Addition_mAF45BAB34E78076CB26E09636E5B1318C3B9545B (void);
// 0x00000227 BigIntegerLibrary.Base10BigInteger BigIntegerLibrary.Base10BigInteger::Multiplication(BigIntegerLibrary.Base10BigInteger,BigIntegerLibrary.Base10BigInteger)
extern void Base10BigInteger_Multiplication_mEBBEA552F63EC1A58D2141BE5726D7379DC180D0 (void);
// 0x00000228 BigIntegerLibrary.Base10BigInteger BigIntegerLibrary.Base10BigInteger::op_Implicit(System.Int64)
extern void Base10BigInteger_op_Implicit_m9077D758A4219A56FC6FF488844EB2E6A904163E (void);
// 0x00000229 System.Boolean BigIntegerLibrary.Base10BigInteger::op_Equality(BigIntegerLibrary.Base10BigInteger,BigIntegerLibrary.Base10BigInteger)
extern void Base10BigInteger_op_Equality_m69975CC1712D97A203BB3A04FA20F00FB9C374C1 (void);
// 0x0000022A System.Boolean BigIntegerLibrary.Base10BigInteger::op_Inequality(BigIntegerLibrary.Base10BigInteger,BigIntegerLibrary.Base10BigInteger)
extern void Base10BigInteger_op_Inequality_m7318E553756DE2DC6A4DF1A4093DB4E1B0DDF81C (void);
// 0x0000022B System.Boolean BigIntegerLibrary.Base10BigInteger::op_GreaterThanOrEqual(BigIntegerLibrary.Base10BigInteger,BigIntegerLibrary.Base10BigInteger)
extern void Base10BigInteger_op_GreaterThanOrEqual_mF1A7F7566F63169CC2DB12846151F53CA7F32BE5 (void);
// 0x0000022C System.Boolean BigIntegerLibrary.Base10BigInteger::op_LessThanOrEqual(BigIntegerLibrary.Base10BigInteger,BigIntegerLibrary.Base10BigInteger)
extern void Base10BigInteger_op_LessThanOrEqual_mF88645354061799A5CE4BE7385CD4B8FA9ABBFB9 (void);
// 0x0000022D BigIntegerLibrary.Base10BigInteger BigIntegerLibrary.Base10BigInteger::op_UnaryNegation(BigIntegerLibrary.Base10BigInteger)
extern void Base10BigInteger_op_UnaryNegation_m9C1B1218EF451340B34F6F7506D1699C78AD1C40 (void);
// 0x0000022E BigIntegerLibrary.Base10BigInteger BigIntegerLibrary.Base10BigInteger::op_Addition(BigIntegerLibrary.Base10BigInteger,BigIntegerLibrary.Base10BigInteger)
extern void Base10BigInteger_op_Addition_mED0B187AED89B117E63AB3DB647132B704560D80 (void);
// 0x0000022F BigIntegerLibrary.Base10BigInteger BigIntegerLibrary.Base10BigInteger::op_Multiply(BigIntegerLibrary.Base10BigInteger,BigIntegerLibrary.Base10BigInteger)
extern void Base10BigInteger_op_Multiply_m65939F644B649759CF99EF3CAC389D4A96175200 (void);
// 0x00000230 BigIntegerLibrary.Base10BigInteger BigIntegerLibrary.Base10BigInteger::Add(BigIntegerLibrary.Base10BigInteger,BigIntegerLibrary.Base10BigInteger)
extern void Base10BigInteger_Add_m3ECB4373BA1E77D18D3E882926582BACF221FAF6 (void);
// 0x00000231 BigIntegerLibrary.Base10BigInteger BigIntegerLibrary.Base10BigInteger::Subtract(BigIntegerLibrary.Base10BigInteger,BigIntegerLibrary.Base10BigInteger)
extern void Base10BigInteger_Subtract_m7FF811860772E105ABCD599EC37B01AE2A0CA8F3 (void);
// 0x00000232 BigIntegerLibrary.Base10BigInteger BigIntegerLibrary.Base10BigInteger::Multiply(BigIntegerLibrary.Base10BigInteger,BigIntegerLibrary.Base10BigInteger)
extern void Base10BigInteger_Multiply_m315267459F246DE9D6D78D3E194996F3CD07AD5D (void);
// 0x00000233 System.Void BigIntegerLibrary.Base10BigInteger::.cctor()
extern void Base10BigInteger__cctor_m1915583D91AE71645148852703C610B8BD1D1939 (void);
// 0x00000234 System.Void BigIntegerLibrary.Base10BigInteger/DigitContainer::.ctor()
extern void DigitContainer__ctor_m23D682353CA47F33FC605259DF26B0AFB35993E0 (void);
// 0x00000235 System.Int64 BigIntegerLibrary.Base10BigInteger/DigitContainer::get_Item(System.Int32)
extern void DigitContainer_get_Item_m5685F599915CD0D680E53ADFF38D30DD99620D71 (void);
// 0x00000236 System.Void BigIntegerLibrary.Base10BigInteger/DigitContainer::set_Item(System.Int32,System.Int64)
extern void DigitContainer_set_Item_m2D41AE85F3CEEBE7C0969C76ACF0F1F2153F899F (void);
// 0x00000237 System.Void ZXing.Aztec.Internal.HighLevelEncoder::.cctor()
extern void HighLevelEncoder__cctor_m0C3906E4A62E0E3EC6FA63DE26B25F664201250C (void);
// 0x00000238 System.Void ZXing.Aztec.Internal.HighLevelEncoder::.ctor(System.Byte[])
extern void HighLevelEncoder__ctor_mBD6FB379281A95AEA3DCEEADE049FE283B10350C (void);
// 0x00000239 ZXing.Common.BitArray ZXing.Aztec.Internal.HighLevelEncoder::encode()
extern void HighLevelEncoder_encode_mD0942D4141B7291015750F6D4DBD21440DE9CA23 (void);
// 0x0000023A System.Collections.Generic.ICollection`1<ZXing.Aztec.Internal.State> ZXing.Aztec.Internal.HighLevelEncoder::updateStateListForChar(System.Collections.Generic.IEnumerable`1<ZXing.Aztec.Internal.State>,System.Int32)
extern void HighLevelEncoder_updateStateListForChar_mF984BD1841FDDA12ABE867B5B362DB8F92019915 (void);
// 0x0000023B System.Void ZXing.Aztec.Internal.HighLevelEncoder::updateStateForChar(ZXing.Aztec.Internal.State,System.Int32,System.Collections.Generic.ICollection`1<ZXing.Aztec.Internal.State>)
extern void HighLevelEncoder_updateStateForChar_m82B1C136FA6CBD34D32A4A75C7C4D2937DB823D7 (void);
// 0x0000023C System.Collections.Generic.ICollection`1<ZXing.Aztec.Internal.State> ZXing.Aztec.Internal.HighLevelEncoder::updateStateListForPair(System.Collections.Generic.IEnumerable`1<ZXing.Aztec.Internal.State>,System.Int32,System.Int32)
extern void HighLevelEncoder_updateStateListForPair_m5336E4E032D1C9E718305E66EFDF75C7FB7E7370 (void);
// 0x0000023D System.Void ZXing.Aztec.Internal.HighLevelEncoder::updateStateForPair(ZXing.Aztec.Internal.State,System.Int32,System.Int32,System.Collections.Generic.ICollection`1<ZXing.Aztec.Internal.State>)
extern void HighLevelEncoder_updateStateForPair_mBF8370BA779CB6CF351E3988B69961DD380E53DD (void);
// 0x0000023E System.Collections.Generic.ICollection`1<ZXing.Aztec.Internal.State> ZXing.Aztec.Internal.HighLevelEncoder::simplifyStates(System.Collections.Generic.IEnumerable`1<ZXing.Aztec.Internal.State>)
extern void HighLevelEncoder_simplifyStates_m6D5A32F4107775651F3E12721686724609D3FAD3 (void);
static Il2CppMethodPointer s_methodPointers[574] = 
{
	OneDReader__cctor_m6D1E3602B2AF2FD45287C0D1302B7E181E1C02B7,
	NULL,
	MultiFormatWriter__cctor_m8A083BA385E9647A84F3129F023FBA3E4C78478E,
	MultiFormatWriter_encode_m28DB2A3C831F83DF837FB6688B183BF67D1349B6,
	MultiFormatWriter__ctor_mF916F2BE29FE5C6456F1386E059554E36BE0D59E,
	MultiFormatWriter_U3C_cctorU3Eb__1_m9BF9719F06C4359A081BA925DFC7026F62A24B42,
	MultiFormatWriter_U3C_cctorU3Eb__2_mCFB6E6C9A708EA2DD0625555C68E15701CD7D9EF,
	MultiFormatWriter_U3C_cctorU3Eb__3_mCCF709582053223EE7637442358F3D927B67F5CD,
	MultiFormatWriter_U3C_cctorU3Eb__4_m2AD4B9976A19005FF355508080F0A125122EB9E3,
	MultiFormatWriter_U3C_cctorU3Eb__5_m8F8B7E2DFA4EAD798C7291C3AEDB325F93377977,
	MultiFormatWriter_U3C_cctorU3Eb__6_m58FB4941EAA96FB85E7FF677AB4C543B5AC07717,
	MultiFormatWriter_U3C_cctorU3Eb__7_m6EFD0B69369259690E5E94952881E718E816DC71,
	MultiFormatWriter_U3C_cctorU3Eb__8_m694B3D8CBA0A0CB755006BBB73B34C46F91E74E5,
	MultiFormatWriter_U3C_cctorU3Eb__9_mBC14C543C6798A75DBEFA79D06DE1FDE8D7145BF,
	MultiFormatWriter_U3C_cctorU3Eb__a_m1886F305C81AEE7EE4F19F55D1937DBF8AA6C8BD,
	MultiFormatWriter_U3C_cctorU3Eb__b_m727545DF2B142F5C49440CB85DB20E147D8694BA,
	MultiFormatWriter_U3C_cctorU3Eb__c_mD672F1DCFB1A9E61EB6B11B35267FD5F25176810,
	MultiFormatWriter_U3C_cctorU3Eb__d_m6CBCD20529F0ABC8302924657F268BAB8C06C57D,
	MultiFormatWriter_U3C_cctorU3Eb__e_m7A1E58FD349C798C99C97EEBC34D87DCA3C0FC9D,
	MultiFormatWriter_U3C_cctorU3Eb__f_m65E0C78DAA9B39A6C97C7CE142AF5C5357025165,
	HighLevelEncoder_randomize253State_mF9E054AB37E308CA777D1C2C88605EA40999C069,
	HighLevelEncoder_encodeHighLevel_m7508AE845410E8A7ADD53CF4A032632C044CE70F,
	HighLevelEncoder_lookAheadTest_mE924E6B480D3F76CEF50176348C81A383AADADFA,
	HighLevelEncoder_findMinimums_mDE70CF486AF6EB635C88BAD39CEDA7FC6790B1C9,
	HighLevelEncoder_getMinimumCount_m2EA959BFC2BA6BBEC6065AAB0591AB79C9A6CF48,
	HighLevelEncoder_isDigit_m1F18210FC1FF8AEFE35643C5FAF4ED8CB41073AD,
	HighLevelEncoder_isExtendedASCII_m78EEBBBE80083BD912AAEF28F25D557A6141B80D,
	HighLevelEncoder_isNativeC40_m8515CA4F7D9F0C06B882189EC0D25FF7E3CD79FC,
	HighLevelEncoder_isNativeText_m3A8AD615D070CF5A65ED43A778D8405009E68C53,
	HighLevelEncoder_isNativeX12_m583597CC0CA875CEEB15A59D900ABF49D6D1EFD0,
	HighLevelEncoder_isX12TermSep_mF866000CFAC5E9F8EA58AAE248BDE16D515FF5A7,
	HighLevelEncoder_isNativeEDIFACT_m8B725FF89DA849A8BF5C6420CDE347084C37C76D,
	HighLevelEncoder_isSpecialB256_m35DAF20B0AED3F02D4CD05BE95428D3C56FEDB08,
	HighLevelEncoder_determineConsecutiveDigitCount_m1830B626B2716A28E81E0375C2E907A879885EF3,
	HighLevelEncoder_illegalCharacter_m246DB50947B6A720135A793EA1D12E2318B816EE,
	NULL,
	ASCIIEncoder_get_EncodingMode_m714E35CAA0585618B3F205F66C106C498E3967C8,
	ASCIIEncoder_encode_m9EC2E92478B8CC63E1F830402E8F23BE553D65B2,
	ASCIIEncoder_encodeASCIIDigits_m4962E90FA890451AE00012D51C12C63749D5EEDC,
	ASCIIEncoder__ctor_m34D0BC4A08D4C8263CC4353277AF1456053047FC,
	ECI_get_Value_mAFCE9CEA1059F20F46DC146B9C91773045B1DE12,
	ECI__ctor_mBB64D9D5E39ECAA0859EA90342C7175A38CC87C9,
	CharacterSetECI__cctor_m5DE82C5F2D8C60A448F9BDBBA265B5BBB6B7A145,
	CharacterSetECI__ctor_mB3A4C7F7804A00E116A1176AC1D86D3AC7E649C4,
	CharacterSetECI_addCharacterSet_m84E203903A28D76EBDE6B7B08B870F2103CA1BF8,
	CharacterSetECI_addCharacterSet_m0CD83D58AF9CDE22ABAF7CEEF68C869ED2483CEC,
	CharacterSetECI_getCharacterSetECIByName_m27748548BB193DCC6BE9A858BC685AD8CDD94463,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ITFReader__cctor_m27D12D3B57468F7788496207D5C6A2CDA4C377BD,
	OneDimensionalCodeWriter_encode_m8A45D09C89C3B08A0D18C14A1C91222437B96BF8,
	OneDimensionalCodeWriter_renderResult_mDDD0E5ACF8A3E7A17F7BADC3390295F9B086BED7,
	OneDimensionalCodeWriter_appendPattern_m83B2498301EC73675ED78BB4362C87132000AAEE,
	OneDimensionalCodeWriter_get_DefaultMargin_mF43B6DFDAC4BC9DED66A4C44F7D3C7DB1D823552,
	NULL,
	OneDimensionalCodeWriter__ctor_mF58C4F4C7A1EB5474A7F4E3E9F1DC26238668684,
	Code93Writer_encode_mC4471516DB40F5DB24DE44051C3E03731B280116,
	Code93Writer_encode_mAEF79B30AA74DB5BEBC1F1EE79179487D629CB65,
	Code93Writer_toIntArray_m300EC3AA432386CA5898DF7E7149CBAA4F67E290,
	Code93Writer_appendPattern_mE538EB4AACA3662D637B87E0DD0F967114AC8990,
	Code93Writer_computeChecksumIndex_m2A3CB58B6502F7F5C65F29A93EDFEE952441B605,
	Code93Writer__ctor_m6A654D1B59782AB2969FB09ACBFE613F56F5468F,
	Code93Reader__cctor_m4D3DF07AC9D6B7FBB241DF61E95AEB0BA2CC4F76,
	CodaBarWriter_encode_m725C160ED9BBB89251897D621CA4FAD5E5190A50,
	CodaBarWriter__ctor_m183028AC9C0CB274EAFE6344799605473AF8A95F,
	CodaBarWriter__cctor_m1322049CFC302604F49C4DE2D707610F20B2C486,
	CodaBarReader_arrayContains_mDC3E8721E012167C11398217812EE009CED0F40B,
	CodaBarReader__cctor_mD9BB914030EF31AE39CD9D360E093D0B24765FFB,
	ReedSolomonEncoder__ctor_m4C5A8CB6AC110CFAF0D6A55B8F506F91DD6F6FEE,
	ReedSolomonEncoder_buildGenerator_m723B5228DECFF63C91CC0DC22B35566A1DCA7C15,
	ReedSolomonEncoder_encode_m29C07346741C8580CFD7239F69658B502BF8E6B1,
	BitArray_get_Size_mD964E9FCC5279F27EB40E08909551B8EFBCF29A2,
	BitArray_get_SizeInBytes_mAFEE86E02252558B4A4517335B1F3B08850E44A7,
	BitArray_get_Item_mE788F08F574037B82629D832110395285ECFE0E1,
	BitArray__ctor_mCEEB282219C2D49A489D28D31D70F2A86355AB09,
	BitArray__ctor_m33A70C8FFF7CF28ADB28C2D6F992F194D8E4994A,
	BitArray_ensureCapacity_m5C759EB5AED39709A29DEC5243E73B8801A411E9,
	BitArray_setBulk_m4AA80DED9A6845D7C4680D34A91D1538955658B6,
	BitArray_clear_m9AD44A1693CB93F9047850B34249B9236721B569,
	BitArray_appendBit_m9B4DF1D4833FA20CA1CEAD33C139477850C6A5A0,
	BitArray_get_Array_m0EC18F2707C5BB3129234F18E56FE2483B96A204,
	BitArray_appendBits_m963D72E7F15E2BB9BC89F1BA216707C1D45C5D05,
	BitArray_appendBitArray_m7054CCE50930C03002F1FA6944378D9B31D1FEFF,
	BitArray_xor_m52F73D9215A7634FE29A6793E506521DAB9A403D,
	BitArray_toBytes_m34696A7B3E14D80181CF38D4A69773C96EC40201,
	BitArray_makeArray_m11F0D7B6BBB8E51A208C92D02CB224D71A5B7625,
	BitArray_Equals_mD5BC196EA999A84465474E9EFF43F595997F9C6F,
	BitArray_GetHashCode_m8C96098E96BEE8684D2CE308251B6009CBF66F0D,
	BitArray_ToString_mB90DE542AE96C62EDE9633BB86A6B396EDEEAC13,
	BitArray__cctor_mFC6914984B94AAD20C0AE3A25640B01F41B989D1,
	NULL,
	SupportClass_ToBinaryString_m14232FBE8FC13E6A3BD4E6FA90102F9DB7FC99D0,
	ITFWriter_encode_mD8B944561937D8E17DC30274D4D622635508EB77,
	ITFWriter_encode_mD74B04A6B6D6DB45A19074E27D71296C14B1A3FD,
	ITFWriter__ctor_m73DA1942D57B81BE0E174EABC3F978A8370FD766,
	ITFWriter__cctor_mA97F915E025509AF54DC85B58EA40BA4AC0261C4,
	GenericGFPoly__ctor_m9B08B4172331490D9D93EFE067D88E72884B69C6,
	GenericGFPoly_get_Coefficients_m0E76A2E1FC8BEEACE29EFB7A324CD6620A2390C4,
	GenericGFPoly_get_Degree_m9B6DD73F4F3C591CA67ADBB3B9C7588702100AAB,
	GenericGFPoly_get_isZero_m30FB7DDD70F78EF4F925DC8A74C5A9EEA3BC8574,
	GenericGFPoly_getCoefficient_m0AD98EB4F854C754ADA15771642A945CEC5AA349,
	GenericGFPoly_addOrSubtract_m229B55C638590AA7CDBA81585E307EFB5CDF5E16,
	GenericGFPoly_multiply_m1DE669F0C60F7E6156222510FD722752D4D99043,
	GenericGFPoly_multiplyByMonomial_m0F2111926793AAD668C851CE31EAE7CF40936B1B,
	GenericGFPoly_divide_m99562A309982EDBB814853C6BB943704E2DBF361,
	GenericGFPoly_ToString_mD77E8507EB77B29C054C29A942905470B4195274,
	MaskUtil_applyMaskPenaltyRule1_m7AAA04BCB84C5C626C87E4F5DD3DB01DA9F7C43A,
	MaskUtil_applyMaskPenaltyRule2_mD213F1D75AFA546ADB179E6AEE0397CA2A50A673,
	MaskUtil_applyMaskPenaltyRule3_m80A1FA90C7C5A80A8A1639763EA57F05AB42B69A,
	MaskUtil_isWhiteHorizontal_mD99C5C415418888BEADEF984476802B2A8F56CA0,
	MaskUtil_isWhiteVertical_m7361A510F324E2BA6D81CAA7D34A31BB0380A379,
	MaskUtil_applyMaskPenaltyRule4_m4E20FBB2E47B44D1A8F3C2FB3160B76DCC97B0EF,
	MaskUtil_getDataMaskBit_m11B095098DDDFFEDB7D388B5A0BD28E714DEFCF3,
	MaskUtil_applyMaskPenaltyRule1Internal_mD1681B7DAE75391F6E65A3EA5B2909EE22C42542,
	UPCEANWriter_get_DefaultMargin_mF0861669301D1167B7142F052A0C3739112CB30A,
	UPCEANWriter__ctor_m2950D0E31755C982CF8D27BEFF1E650E20CC5ED5,
	EAN8Writer_encode_mF46D26C417D7185F38B6761883A9F726AC5AE4B9,
	EAN8Writer_encode_m9E17E2560EADD298163D965A596BF3E61D828A3A,
	EAN8Writer__ctor_mF78276B278A916515E5AA0343310EB7376EE07B8,
	UPCEANReader__cctor_m5EAECE36E7091DC48B2FEFBD9C6189B6CCB92FFC,
	UPCEANReader_checkStandardUPCEANChecksum_mB321D9155CB8B1F09B8831544668121CD6852D2B,
	UPCEANReader_getStandardUPCEANChecksum_mA42121994B1084A1DA5A94DEB78C75FD1AE66E9D,
	EncodingOptions_get_Hints_m11AC6CBF296B50CEEBBBF0A0C17C0064297EE3C6,
	EncodingOptions_set_Hints_m9423CF843812503DE489A3D7B511DD24287474BD,
	EncodingOptions_get_Height_mC197FC9E2D071FE86529C2EF2DE646A9533D452E,
	EncodingOptions_set_Height_m8A5D1321F588C473E908B70E156794EA37DC9124,
	EncodingOptions_get_Width_m41D490AD0021D306C8F34679FFF5E1D6A24E6FB7,
	EncodingOptions_set_Width_m1FD99866B8681E8247735155990908793FE3839A,
	EncodingOptions__ctor_mD4D89A578C1AA1D5116F449B6E1B31F128FD7CC1,
	Encoder_encode_mA3BB815DBCC8BDF35280A6433C71444D8CD30536,
	Encoder_drawBullsEye_mD37AB37AF330B8A5BA7BFE9FB323366FBE785F59,
	Encoder_generateModeMessage_m4D1FCC4DB867DD4F0E5DE19DECF36D5D8DF54B60,
	Encoder_drawModeMessage_m9559A41916BCF26F64D8BBB078F8D798D76D1204,
	Encoder_generateCheckWords_mF7D4645E26D45E38FAA4A079B690328A2938B65F,
	Encoder_bitsToWords_m099083432A5F87948CFF6D782DCD40A60DBA8F76,
	Encoder_getGF_m13E1A2441C19EFE3DEEB5F0A0E2100ECF843FE9B,
	Encoder_stuffBits_m1B63A78041C86F4D8DD136B59425EBDF6BA60558,
	Encoder_TotalBitsInLayer_m07E143581184A8AD191FAA6561ED7FB8A7BEEB69,
	Encoder__cctor_m54327A81B49295767ACAC6768193512CAA5EDB6F,
	WriterException__ctor_m676F8A883738D7980A5C802B95D3880BD42E3AC8,
	WriterException__ctor_mEEBB5A4BEA5F74994195D23D0A4A0AA6420D62DC,
	WriterException__ctor_m7E3C1EE31425FB8CE2B47483EF69EC794696AF4C,
	BlockPair__ctor_m0F9A306D5FC21E5A488164145D6EBF7C8D9BA79B,
	BlockPair_get_DataBytes_m98D9E3EE6C0DCABF4B36187054C47BFD903BC520,
	BlockPair_get_ErrorCorrectionBytes_m7B22B4027DE8F62B5428281400E0F93C095CC810,
	PlesseyWriter_encode_mE3D7A0CE1E757EE7CEAE350C9BE235418F81C711,
	PlesseyWriter_encode_m35F4722C85E606384AD687601074C9A86AAAC43A,
	PlesseyWriter__ctor_mAA213488BEA456169FFFDC1F93DA18C6233DDDFE,
	PlesseyWriter__cctor_m73964CE1E38C3AA415EDC554143C0CFC30800E30,
	Version__ctor_m632065CF5A1FD956582B79A7C137207E0AEF0FF1,
	Version_get_VersionNumber_mC7D233925DA58AB570CFB083E5D07EAEDBEBC312,
	Version_get_TotalCodewords_m71C758392A5C6A7632EAE88535B2D6C18E3957ED,
	Version_get_DimensionForVersion_mA1869848C121CF26EB61D60A25C8FD1814978D14,
	Version_getECBlocksForLevel_m03F91D31A3FC0DEB93346D39EE1F7ED293D6B286,
	Version_getVersionForNumber_mD3CBCF01090102DBB1B3DAC4AF0E23D1570C3328,
	Version_ToString_mDC05BAC02564B3BBDCBD6BDB2CA9245D696DF4B3,
	Version_buildVersions_mBDD7EF05C1149E910FFB02124E03C33957F33E8E,
	Version__cctor_m9B567EFFFD86E465B350A555B997AB9B66A521B1,
	ECBlocks__ctor_m9D0FEA2A62AD87300386326630D71B2FE43D9011,
	ECBlocks_get_ECCodewordsPerBlock_mF5F7E0CB687399B198D52DD6069845FF62BFFFA9,
	ECBlocks_get_NumBlocks_mAB97A37082ECA5AF5428F29A61A02873DCC4310A,
	ECBlocks_get_TotalECCodewords_m93CAC7D317E46A5596F928124939ADA688134E4B,
	ECBlocks_getECBlocks_m289C8698C02897868B44F8F02AD0A09713474112,
	ECB__ctor_mE5D63C7AE05AB1B6EC8237C68219F66AA1D05AFB,
	ECB_get_Count_mB561812ADB3C72573B7448A4C15F77A8D98FD0E2,
	ECB_get_DataCodewords_m21EDF743302FBB953BD5E30111197CAABF8FDE5B,
	ReaderException__ctor_mA451AC8A99B53023FE2A2688BE53F0D0389BF358,
	FormatException__ctor_mBCCCB85A02EE0C55636CB1F326C5AD683E51F067,
	BigInteger__ctor_m69A2D8A19CC9D704800DF0DB9C359E5DE14177F6,
	BigInteger__ctor_mA9031AC26D86BF3500545C4CA63448C167596DC7,
	BigInteger__ctor_mAC829AB10C5217FA7CAA11C745991C339A5B0200,
	BigInteger__ctor_mC880179410494FBF7999305DD1732B6EC9C7789F,
	BigInteger_Equals_m45066B3518201B7CDB4BB946A8B1C7F6E31BDF97,
	BigInteger_Equals_m3D3303AF4D3EEB9C8A491D93D69766289731DA9E,
	BigInteger_GetHashCode_m15EE1915EB890F940083109DC4A55649A01DC0E1,
	BigInteger_ToString_mA163999C1ABCA14CC54501CF89868920022AABC5,
	BigInteger_Parse_m1760591815D06039FBA97B232C332A8045D268DB,
	BigInteger_CompareTo_m933CD8D7F3A0BA498E0FAF2B58B311A4B304F2DA,
	BigInteger_CompareTo_mA620358437EBACEB8F8250FD262B7F981B3F7B0B,
	BigInteger_Opposite_mDCED620041A9A733A29DA18A44D46DB5D911B315,
	BigInteger_Greater_m4C21B747A816B2BC7AFD03FFADEE82618CAFF913,
	BigInteger_GreaterOrEqual_mBCAF6155C2FDFE897DE4EF99002F7031384E0DD6,
	BigInteger_Smaller_m5B446A2468367CF184CD685D0908D0139D713AB0,
	BigInteger_SmallerOrEqual_m4B0EB850758E5A1F820C74379D3CC6AF1146A87D,
	BigInteger_Abs_m9505B160241171EDC8F144B5E180869DCBB77745,
	BigInteger_Addition_m544A7AC1731E2A95BB8DF44E2F3F742A173D647E,
	BigInteger_Subtraction_mF102CDAD8F220C5A3AD346017534A5A89F103203,
	BigInteger_Multiplication_m235E4DA5DDC2F9E0A541768A3F82B8133A7A39BA,
	BigInteger_Division_m1297CEAFB08D136C9CA4891DE68F5904A95C05CB,
	BigInteger_Modulo_m98F14DD2814355DBCB7814A3D5FE3E571FF66416,
	BigInteger_op_Implicit_mAAA34C0DED0F6AB4A142F6B84AD34BE3A6323667,
	BigInteger_op_Equality_m6E2AC5561ECF42C27EE3535CF90837A705AB694F,
	BigInteger_op_Inequality_m0878843BD4C02C6D98B61235D1E10753762B09AF,
	BigInteger_op_LessThan_mA1D63FCADA6DAB0BDF36656D73E918655562158A,
	BigInteger_op_GreaterThanOrEqual_m5FED500101A7EAFCC745E61AFA3BBE9DB54C4391,
	BigInteger_op_LessThanOrEqual_m9BAC9ADD76F62D2BF9144D623C55A85D5185E695,
	BigInteger_op_UnaryNegation_m77FBEFAB24AE5031E4C0992947D8AE1C66EF8DD9,
	BigInteger_op_Addition_mD52A2F5AD09BA0255BB3206FBC2B0942F92365B4,
	BigInteger_op_Subtraction_m4A8D83920612E445AB8EE7A419BFD56BE54E2A7D,
	BigInteger_op_Multiply_mEAA931C712C0775A12BE4C98ECA73682AA62B449,
	BigInteger_op_Division_m36389DDB09EBC1433E8BE4042D56F1FC66CF6832,
	BigInteger_Add_m857D6A84B963A21E13AF4787B7798715EF908032,
	BigInteger_Subtract_mD4B6208DC879F1107A289F3700AF89104E498AB0,
	BigInteger_Multiply_m6FF8C4D44FB80FF6C06CB5F3F7F229E361172ED6,
	BigInteger_DivideByOneDigitNumber_m2F8E5B16C8A7CD8FE5F9CC0CB12B4B1FF9F028BE,
	BigInteger_DivideByBigNumber_mA6B20835A92324720AFBC794964F794B7098DBCE,
	BigInteger_DivideByBigNumberSmaller_mF93EC55EE62F096BD9854AEF588A583156166B0C,
	BigInteger_Difference_m6EAFD63AC836609B2C28F28E849D110C3071070F,
	BigInteger_Trial_m0A2384A514688E243620C2DCBABBC838EE467A34,
	BigInteger__cctor_mFF5A97A32AC1057194871567C8398550207FF1B8,
	DigitContainer__ctor_mB6729FC739F1D8DE01C67483AB306596BCDCB527,
	DigitContainer_get_Item_m13CCE57F6FC21EFEC29182C89C0246460BD8C3A6,
	DigitContainer_set_Item_m8842DE3C1EF724D10D6BEBA617E57DFF8C492D65,
	State__ctor_mD095565D18FA60242472BE90A67CE29A788EE821,
	State_get_Mode_mDCF57959DDE617773975B2B417B379473AFD223D,
	State_get_BinaryShiftByteCount_m5857FFEDAA0E093FBE01951B71E1A33EC7BD46F6,
	State_get_BitCount_m8C4F7294954E2523C1C980B7AC12F509746C27F9,
	State_latchAndAppend_m7483E3A43172836DD7F7720E9AA4461B66841507,
	State_shiftAndAppend_m85C6673552D67393BA96CEAFD5D9711655BB417F,
	State_addBinaryShiftChar_mCD3C83DD3A8F303D384F74016DA4529F27901EC3,
	State_endBinaryShift_m566AC5C9823C9E5E16A9AAC8F4E316BC939E2089,
	State_isBetterThanOrEqualTo_m2A89EEA101ABB8E0C9CE0186F7A4BB877EEB37FA,
	State_toBitArray_m09CA20754E97593C71B29AF5DE79CB047083D6BF,
	State_ToString_m16FFACF3EB7D11744E75530A156227C95685D205,
	State__cctor_m11577A919CE62E0289265C6799469806D446735F,
	AztecCode_set_isCompact_m597E612BC815EA9C72787E2D916F328FDA3F24BC,
	AztecCode_set_Size_m5C7C783E5F2D788C6684E23A49EC6CDF5A058495,
	AztecCode_set_Layers_m8B5FE0EF288E66B8C5F1B8A184C0E87D43B8F478,
	AztecCode_set_CodeWords_mB8C8D156EF6308F34AE895994B478B6138CCD2D9,
	AztecCode_get_Matrix_m89D879400229206B4C9084D174E17B0CEC53C6EF,
	AztecCode_set_Matrix_m2C412B37E380796D2FB9A4952937C0CD2270BDB8,
	AztecCode__ctor_mCF74E2FCC26568EC7FFBA337F548316DF3486CF1,
	ByteMatrix__ctor_mD9ED8E0BC8269FD7C78521887BB257A369D61B21,
	ByteMatrix_get_Height_m431DA5356FC7A79050644F5A2D92FE2A23CD7A19,
	ByteMatrix_get_Width_m4EB618AB944DEEDC5912E727D030819DA99B0767,
	ByteMatrix_get_Item_m7F1D2BDC8A65006C16907F68A3DF7767A083D48B,
	ByteMatrix_set_Item_m23426A3D1BE362763285B62829C7776459A87684,
	ByteMatrix_get_Array_m7131EF57DAF53905532CC248EC7588F2A09D692D,
	ByteMatrix_set_m5CF2E267D45A4ED9EEEA0642EA897378623399C4,
	ByteMatrix_clear_m97879CC62C73B5CCBF1F22A3C72CD5908F57D78A,
	ByteMatrix_ToString_mEADA9D28C1A26AC7619892130004AEFFDA522C1F,
	BarcodeRow__ctor_mCA36490C152EC45A701B31D5F47D20DA6C90A9E0,
	BarcodeRow_set_m88567D3CB189654EB765C60ABE54546540436C82,
	BarcodeRow_addBar_m50082BAFF8575889C1C698BCEBC49ADABC153471,
	BarcodeRow_getScaledRow_m7D9CBFF8089D7D556176236A37537696662820B8,
	UPCAWriter_encode_m4CD6BFE418E56A9618C0914178C377A4199C62B7,
	UPCAWriter__ctor_m7EECE77D24C1659896BD24A2D6A13BB13B1AE49A,
	EAN13Writer_encode_mB7A1F5621D73D0AD309EAE6FB63DA41128B55996,
	EAN13Writer_encode_mD642678EC1B40B586DC40276D9DE7BC88EAECFA6,
	EAN13Writer__ctor_mEC15BCEEA94A09741A5F4C784C958570445E052F,
	EAN13Reader__cctor_m2ED31F1368082989D00A5AE69DA8130F3E4F9754,
	Dimension_get_Width_m813D53F7D20E20C9E579017C7438D7140A5C0E43,
	Dimension_get_Height_m1CC9FC7C85BB327FAB1A35ECC4FE02A3CB89D209,
	Color32Renderer_get_Foreground_m834D2A31C6626724E141417D102E2BD6FDA9462C,
	Color32Renderer_set_Foreground_mDE1439A10E49A742C22D51F9D2FBA0CFAEADA2FB,
	Color32Renderer_get_Background_m51B69CE85809EB3420A58292A274BA1B56F4A4DF,
	Color32Renderer_set_Background_mCC0C1C48BC5E28A649930D77B256EEF9E398DE63,
	Color32Renderer__ctor_m022DE6BAF62F51CD8B9E4D37B73E87BDC407C2EA,
	Color32Renderer_Render_m6FE05E98B75976AA9F41400B9C6C2215B7B2E40F,
	MSIWriter_encode_m11A07A46A43AA48D3A1F79AE30CE5579E6139289,
	MSIWriter_encode_mA45E6992E21D35989DAAA7EFAAD50E4D17E4C5C9,
	MSIWriter__ctor_m2F37F4D1ADA55C87AC1239919C5F7E4B69C12DAE,
	MSIWriter__cctor_mD2B3642FF3130314CA329B1275E552E9A89170E9,
	MSIReader__cctor_mDB5CCD8241C5F9E4FFBFB2CA06100D917054F763,
	EdifactEncoder_get_EncodingMode_m638EC864DD8160E986A2A7EB7EAD665F8DB31B2A,
	EdifactEncoder_encode_m2261C384AF6CAAA752FF726BDC77040D06BA5321,
	EdifactEncoder_handleEOD_m8BFC42F21E70D93982591EDEDABD17D83ABF8341,
	EdifactEncoder_encodeChar_mF4A1D39C900CEEA6D433C319C2C7D3599C908615,
	EdifactEncoder_encodeToCodewords_m8A01ECC5F50DF400B48457143244A4758F3EB360,
	EdifactEncoder__ctor_m49E00027C578BA262F59C5A711D4E40F3A2AA347,
	DefaultPlacement__ctor_m75A3B328D6960171ABA1D0D6491E6102EFE8573A,
	DefaultPlacement_getBit_m5A08D0C45336BD4617318CC891ED4FD59CF2B723,
	DefaultPlacement_setBit_mFE8F3F7700BD0558A90162067EB45DF2248CD3A4,
	DefaultPlacement_hasBit_m84FE601210C961F40543C8D911F81289224F91DA,
	DefaultPlacement_place_m0F093E38D19FA2C9DEFAF65D59C5C3CF1DBC3CE6,
	DefaultPlacement_module_mA6C4263AF83FFFFE5B785809AEDD24D5E00B32F2,
	DefaultPlacement_utah_mCA157D0F894E3D27CD42C02C4BEC51E2B1CE558C,
	DefaultPlacement_corner1_mD09EEDCE446A5AA84B5C91B8D96764EC50F4D344,
	DefaultPlacement_corner2_mB01814FD9C6566210B4994A602EF5B06363BBEFE,
	DefaultPlacement_corner3_mF4BBB96F20D7BEE6F172B76759ADF6B258400E73,
	DefaultPlacement_corner4_m8243AED544D0CC53AAC238067D443623E549A5BA,
	Token__ctor_mB8D277B02EB356EC83D48D22EEF21B9249818CB4,
	Token_get_Previous_mAF1A74400EC353C1D8BF256C6A4FE2798BE51F8E,
	Token_add_m92BD321030B141B51177797849794948481E179F,
	Token_addBinaryShift_mF3C69BE58890695D600AA6B3BA7A9818B2597C32,
	NULL,
	Token__cctor_mDAE1E010697035A94080196656397F165257A3EE,
	QRCodeWriter_encode_m3AB9B337CACD90A15D8C2EC90489B1E4FC78E26F,
	QRCodeWriter_renderResult_m4A7C02561A8E80AE695F7618E4CAA52E96C6D978,
	QRCodeWriter__ctor_m3439AF691810E75852236707F0787DC6EC316BD6,
	C40Encoder_get_EncodingMode_m1BB766D7F2F511BEFAD7319601C783E378FD1BA9,
	C40Encoder_encode_m268230A0B4F811F2DAB33EE99C1E2963222B6451,
	C40Encoder_backtrackOneCharacter_m0DE8AB9F95806BED76075E45EE462522870518E6,
	C40Encoder_writeNextTriplet_mC28D9A4AE98BDBF51DFCCDBA97D0C1BCF215D16A,
	C40Encoder_handleEOD_m4541691AE8274A4591AF6973ADDC8233F5776A65,
	C40Encoder_encodeChar_mB4E43EBBD4C6B689A00CBEB01B363BB7BD996465,
	C40Encoder_encodeToCodewords_mE542999CAF6C1C12205929B0C19DC77F7EE6C27D,
	C40Encoder__ctor_m2BF4DEE8F84BF12EDB431C857C475D317A381EE9,
	X12Encoder_get_EncodingMode_m114182FD5F4A0EFCAC7D3ECE20D6D29C4A4A8BB5,
	X12Encoder_encode_m1E403628B395E933C2DC4542F9A688F181388549,
	X12Encoder_encodeChar_m40D66B95BE9446537712801AF5A0C86D27213757,
	X12Encoder_handleEOD_mF027757C6C3F9CAE4C593FD4F45C3EA824D02795,
	X12Encoder__ctor_m3209B93C13C02E5B0064DF0EBAAF4A52904118C6,
	BitMatrix_get_Width_m8055CEDC7ACBE606E27CE32DAD50DF85AC0E45DB,
	BitMatrix_get_Height_m9A78F5C9E2B6133D13825F63B79852654734ABAF,
	BitMatrix__ctor_mC3D9EDE74C513901EB96721ACA08844D5FB2D194,
	BitMatrix__ctor_mC5CB16859B35D776F2F3CDF0BFC1A3795C09F17C,
	BitMatrix_get_Item_mFB70D0E9D1EF1BD7690BA65A7673B609DAEDE69B,
	BitMatrix_set_Item_mBC76D63A579D13FD2103B2CAC0416FAED5042B67,
	BitMatrix_clear_m37256F75D2C06D6E0BE8A15EF6AF1BB89BC7D808,
	BitMatrix_setRegion_m28EC620EA6468EA7609C1033F35C2EEB6073F19D,
	BitMatrix_getRow_m96ADF03B068B2AB0EF061E1ACA42BD3B4C4BB7C2,
	BitMatrix_Equals_m4C8D5ED3DDADA6683B51074FC0676905A01353CF,
	BitMatrix_GetHashCode_m2E790DA696226B3FA3443D1A743C82509620594D,
	BitMatrix_ToString_mB26868A5C71A4E12E303D3C6356473C0CA353BEF,
	BitMatrix_ToString_m1402BC7B08F5DF106C1AD61CACEB5E0831FFB545,
	BitMatrix_buildToString_mA9D67165B179139929A21B90EC086C2C93C9BCCF,
	BarcodeMatrix__ctor_m835AF1210C8B22567777ACAC6BC2E472B054D67E,
	BarcodeMatrix_startRow_m5042B956C49C626709426462FD84803DCBEF2DE3,
	BarcodeMatrix_getCurrentRow_m600E94C404AFC8036307669D5B834D905D6C3D9B,
	BarcodeMatrix_getScaledMatrix_mBA9BA34833315E6413D70D194BC3EDCE7F00D56B,
	UPCEWriter_encode_m31A0A69CE46A63029EBA4352CCC0E7B2E84E2F9D,
	UPCEWriter_encode_m35EECF221F1DB172BD681EDF2A7BF8216D6ED085,
	UPCEWriter__ctor_mB0D71BC3398A4E508AEF0DBDDBED25F109F2FB08,
	UPCEReader_convertUPCEtoUPCA_m95C330705DEC28D690E9FE0E41F0ABD334BE6BCF,
	UPCEReader__cctor_m0DD952293AE6A8EC5D8922BC5E27EC59DE072566,
	SymbolInfo__ctor_mE896D19FC0AB4DADE4EDABD951EA74D620D689D1,
	SymbolInfo__ctor_mD32419A493D777DCA51D0BA3C3BCD1FC7A9A8D5D,
	SymbolInfo_lookup_m6D860142F773C8279DEAA7637295D3EA60727FAB,
	SymbolInfo_getHorizontalDataRegions_mDDEA59520B1C53E74947063AE078272F2DC1EE09,
	SymbolInfo_getVerticalDataRegions_m960516C4F86B67FE551DE9E20C5BB0E34DF37D16,
	SymbolInfo_getSymbolDataWidth_m4ADB0EBCFF6B5FBA77FCCF7838B8B1A5FA92CF03,
	SymbolInfo_getSymbolDataHeight_m1E2DD77BDBB84C1746C9E0778904F0A39DBAE1D2,
	SymbolInfo_getSymbolWidth_m2A5232212AE2D7AB9CD437F65DF5C96BEA8CF137,
	SymbolInfo_getSymbolHeight_m64E010A58F37DD235B1CA2C97966F67BF2E5B429,
	SymbolInfo_getInterleavedBlockCount_m643D4E0017A88D07F9927CD2E763EA7AB7866234,
	SymbolInfo_getDataLengthForInterleavedBlock_mEF4522752046B58059C6E2EB4B4FD90284D0CBE9,
	SymbolInfo_getErrorLengthForInterleavedBlock_m22108C2443239C365E9AE4502D51CA5F85B85C93,
	SymbolInfo_ToString_mE0B64258DBD7DDB9499F408AF5A8D519E07A82EE,
	SymbolInfo__cctor_m5FB8A3595211E99D9767A8B47832B70C4FDFD97D,
	SimpleToken__ctor_m0BF480450A6B082E9E8CB07346A0809E78AEFE34,
	SimpleToken_appendTo_mC4CCE296C154EE48514CAC9D52A20CF2DDDFF116,
	SimpleToken_ToString_mD85AB0D5568B250558B9A73E5E1A97F138CC3C5C,
	ErrorCorrectionLevel__ctor_m429F20D98DEBBA56BEA004EC4A9D038AAF9901FF,
	ErrorCorrectionLevel_get_Bits_m1996EE0BA4324DDFF12D442D9BC4348C337FDF83,
	ErrorCorrectionLevel_ordinal_m717C8E22648A365039003E81AAFC2CB7323D500B,
	ErrorCorrectionLevel_ToString_m0E99708B8EC0B4654AC1780098F1FA4E6D27229D,
	ErrorCorrectionLevel__cctor_m528E72CA43E538918419806A11CDB226FF1D69F6,
	Dimensions_get_MinCols_m7A1880CAF69A8ECF2C7D64D61477D9550C3853E9,
	Dimensions_get_MaxCols_m83484D119218ADE0F17C418F223CF01CD91B3702,
	Dimensions_get_MinRows_m758E40D54DD48DD5CD4A5F03BD7759C357889711,
	Dimensions_get_MaxRows_m4C2057281745508367EFA88A021D118B2809AFA9,
	Encoder_calculateMaskPenalty_m8B33C7B7142D7E41B3E54B00775FEE5F7E25D194,
	Encoder_encode_m96B75ED56A4C2C5F1E2718E78F435FD0977E66CE,
	Encoder_recommendVersion_m2C0AA86DA09E6358CD4DD91DCC528B8D1BDDF033,
	Encoder_calculateBitsNeeded_mE4D92C04CB45D6943333480362B382670CD94F4C,
	Encoder_getAlphanumericCode_m7E72C6041D31A30F1028AF64C274542922901594,
	Encoder_chooseMode_m5F3FE17B69E3480679DE0500DE3547F6C0E5AF8C,
	Encoder_isOnlyDoubleByteKanji_mD833812B52E8562F08041FC8032C30B93022F2D8,
	Encoder_chooseMaskPattern_mFAFBAC3385B05EA78A88E1975F9C983140E9068B,
	Encoder_chooseVersion_m14E15AC482D1EB4E7F27409DB69C79B709DB9811,
	Encoder_willFit_m82CB9AD903A0DE4B84DEC207ED9AF289BD3615C6,
	Encoder_terminateBits_mD0FC5AC8FEA159EDEC2F543336ED399782A9ADCA,
	Encoder_getNumDataBytesAndNumECBytesForBlockID_m2DEB7EC8301A37269C3FE9E6B329F9B1A196ED2F,
	Encoder_interleaveWithECBytes_m6690BBECDA761C173430F2D56A73C13C3E43E204,
	Encoder_generateECBytes_mED468C37E511419419A5DC014A3CF5F73CE0625D,
	Encoder_appendModeInfo_m96EC709BE578A7E80B6738445F113D3B1D011EB6,
	Encoder_appendLengthInfo_mB738A6FC23FF2BA38C7023A77EF54611B709AAFC,
	Encoder_appendBytes_mBD40910F95A0736540D171E2E440B210E69E9277,
	Encoder_appendNumericBytes_m441A0D6D0C9405F47ACA6FF12B0105CDA30424F7,
	Encoder_appendAlphanumericBytes_mCE43E85EA033076F9D0F306FDB5780F2C1C30EB1,
	Encoder_append8BitBytes_mBAEB7FFCB230FD209DC25BCDA6C52ACC32C5900F,
	Encoder_appendKanjiBytes_m3401104089DF36B750A9277F523A9E8B3F589D07,
	Encoder_appendECI_m0A7938A8A2CE7A03B1D8929FD4D7335FA80116B0,
	Encoder__cctor_mB3981F467D3A46C954D808EBDC824004E20F62AA,
	PDF417ErrorCorrection_getErrorCorrectionCodewordCount_mEA8AEA17A48EC6AC34E11EE595A65397441A6FD5,
	PDF417ErrorCorrection_generateErrorCorrection_m43AEFCF5560F07235E2137D5F77232FDF7FE5BCC,
	PDF417ErrorCorrection__cctor_mD78DAFD343B9F3D1B24511FDB791B3AC49652F55,
	PDF417__ctor_m93D24A8F67F9A334282A9913CB6EC9AB81ECBA60,
	PDF417__ctor_m47D14096D8619E8C5E06B68F5D2516205A2F2EBA,
	PDF417_get_BarcodeMatrix_mB2C1B0E9A02E069AD9DD553627A1124B5AB91A66,
	PDF417_calculateNumberOfRows_m25E7F2425401AABED6D516E360DFC1A503127A02,
	PDF417_getNumberOfPadCodewords_mBAF2AEFE2349F172B1A5FFDF591BD07AEEA27011,
	PDF417_encodeChar_m493BC57D3450CB9468915EF96DDC592023F38532,
	PDF417_encodeLowLevel_m743AC0FC607671C291A543E16EE050937B9994E5,
	PDF417_generateBarcodeLogic_mBFA0CB969E407ECF162A4036D890844605ABEEC1,
	PDF417_determineDimensions_m513D6E1804969890C40F6EC4E410AAF556DB12C0,
	PDF417_setDimensions_m4F344A3850D4125EFD506205BB9586E858D34D52,
	PDF417_setCompaction_m4816B704079C947B9CD4CCA2199F889C3CDF191C,
	PDF417_setCompact_m7CB850572624F55938D503780DBB1FEFD350D1C3,
	PDF417_setEncoding_mA02519C4DCA3607704762D54B6652BCE2EB97689,
	PDF417_setDisableEci_mB64BDFAFCAC129ACB7B08D30BEC4CE8A13BEE401,
	PDF417__cctor_m835F252D0C81243AA4BE3ACEB20D5FAFB06FB68C,
	BigIntegerException__ctor_mEF2948985729D00A43FF017560BE86FF2571885A,
	QRCode__ctor_m6867EAB7E4BAC675631F6D5184FC3AE64D3F23CE,
	QRCode_get_Mode_m33D91E088D7AF12E3E1415A8FFD02DF427D4AC18,
	QRCode_set_Mode_m54400FA168DDB0166022CCD6BEBDC7D3BAD54960,
	QRCode_get_ECLevel_m9D85C676D8DDD7A8C2122A2E12F82BEF8E7696C2,
	QRCode_set_ECLevel_m992CE7CECA778D10C8E27038E625ED387DC2387D,
	QRCode_get_Version_m2BDFDC6D41ED5BB73ED800BC10F2D0608E4C5E22,
	QRCode_set_Version_mCA44F076712002D1BCACB7D1235D9B35EB462748,
	QRCode_get_MaskPattern_mC40211D514C99CAD309D0B364783A85F27D3080A,
	QRCode_set_MaskPattern_m4E89EF31D21F23D4BB15F0E0A52133FFFBF863D5,
	QRCode_get_Matrix_mC0E1FE9FD38C7194089615225BC7B337A94BC502,
	QRCode_set_Matrix_m58CF18CE0B649E241AE22DD283932C2FC9D166D2,
	QRCode_ToString_m2D23C64CC443AB7FA6C61C579676C10D389B688D,
	QRCode_isValidMaskPattern_m0F1782ACB752B172EF7CE871F0E950E13506AA6E,
	QRCode__cctor_mE81B64EA7C83CE3139BA3EAB8F9DCF68CA581AC0,
	MatrixUtil_clearMatrix_mFB1FA3A46CA97A6C0F89F668A2BF6C5BD73FA26B,
	MatrixUtil_buildMatrix_m2791F3ED643F2A9AF4CD1F06386DBDC641AF0BA1,
	MatrixUtil_embedBasicPatterns_mD5E469E80E5FB72EED772505ED40C4024A409BED,
	MatrixUtil_embedTypeInfo_mD43B1A38AD8BEE418D744134E0EC6F4BF52FF120,
	MatrixUtil_maybeEmbedVersionInfo_m4E40671CF4E3F2613089CE71EF3EC3C32720710D,
	MatrixUtil_embedDataBits_m6982E017A519BECEE4B449B18367DF22C794D3BE,
	MatrixUtil_findMSBSet_mAF6FFAACA226C938479F9C44228B7E0F8EDDFB9C,
	MatrixUtil_calculateBCHCode_m96802D858DC2D100DB04F66C4DF6342A12FB4C98,
	MatrixUtil_makeTypeInfoBits_mD20DC2EAD0F2E0241EE918710E2E23CF3CD7AD86,
	MatrixUtil_makeVersionInfoBits_m5B7A35EC194799F421B5B361FC570469510D1C3F,
	MatrixUtil_isEmpty_m5EF05BE37D525B3BF155A8F9A2FFD71D722175B8,
	MatrixUtil_embedTimingPatterns_mF1B197899FB1AC4B98A14926927DB8B3A8F49125,
	MatrixUtil_embedDarkDotAtLeftBottomCorner_m1A054C19B525928ED8F96DEA040758D9CEC79CBC,
	MatrixUtil_embedHorizontalSeparationPattern_mCB6C1D5DCFB13C0ECF7CB482E1F1D0CF775FDF50,
	MatrixUtil_embedVerticalSeparationPattern_m025D10BF0F80BDD652070F71627CBB6A8C071263,
	MatrixUtil_embedPositionAdjustmentPattern_m9A1906444B10909451711EA6CE575C75B93F5D44,
	MatrixUtil_embedPositionDetectionPattern_m79F17C3CC6FD15A239269D65600584BFF76C9B21,
	MatrixUtil_embedPositionDetectionPatternsAndSeparators_mD71AEEA9842EA32C7572B23CE9A2DACF29EE6F18,
	MatrixUtil_maybeEmbedPositionAdjustmentPatterns_m262C872CADECD02718E0F6274BAF007FBF18FCC0,
	MatrixUtil__cctor_m5D5C630462D42E27F18BD725E2FF294489DDAE93,
	TextEncoder_get_EncodingMode_m4875AEF0A71342324D0A4D6F75E2A06F4C42C53C,
	TextEncoder_encodeChar_m4571E8B515AE01125CBED86BEF7E8375EC414A3D,
	TextEncoder__ctor_m305C31389F2E89196CA33029EF4EC0307A1D433B,
	EncoderContext__cctor_mDEA4FB44ABA7602407D5D4620B42F81C1343762D,
	EncoderContext__ctor_mBF80EA74A77D043BE118FB4E8F77830D71A8B628,
	EncoderContext_setSymbolShape_m178345421EF427C814491E91BDE404A6E1EBE78C,
	EncoderContext_setSizeConstraints_m9DF4D765CE06AB7D295CCECB1BCFDEDB79A51042,
	EncoderContext_setSkipAtEnd_m898D4D40F711C018AA308A9E5D32C94962D57DEC,
	EncoderContext_get_CurrentChar_m17CC24F702F8019C3EBC108ABFA352D2F7DAB9A3,
	EncoderContext_writeCodewords_m39CEDD0E43F69BBF65546DA9A4415EF4DA9DC4DE,
	EncoderContext_writeCodeword_m05A8D6AAF3A9918D18A2F7F9F3A36E4E911F38E8,
	EncoderContext_get_CodewordCount_m86A2A4383E2C210DA7DAE7D1C180D1BC90238798,
	EncoderContext_signalEncoderChange_mADF1DAD66E8381B308016EEB037F640723B8E35D,
	EncoderContext_resetEncoderSignal_m58792CF544A12F27D34719F3A0D80BFBD01B8FE5,
	EncoderContext_get_HasMoreCharacters_m91BCE0DF2F4D8EE92502837726242CE1AB84EC1A,
	EncoderContext_get_TotalMessageCharCount_m0B56335D75E613B1C3DDC89EDB9A8F9F81365288,
	EncoderContext_get_RemainingCharacters_m0D4877E225AB71AF61E4049F92FB38B4340A8FEB,
	EncoderContext_updateSymbolInfo_mD58B4A0EA2A8ED121E31293CF2464A8A01FB4C7F,
	EncoderContext_updateSymbolInfo_m2C4D112B1B96EA9134040ADBB3F89EBA4BFA57B2,
	EncoderContext_resetSymbolInfo_m3C76FC3374B3C6D3CA91A8C396729054333E4D6A,
	EncoderContext_get_Pos_mB6FC37697D743FBE341043898E7B659559A5DECE,
	EncoderContext_set_Pos_m11E60F0C66BD176B5030FCEB7A51452F28DCD522,
	EncoderContext_get_Codewords_m5D546339A26D9E229618EA64442983956D38012D,
	EncoderContext_get_SymbolInfo_m94C5BE03529789BE8897F207EAC85431F4D2A316,
	EncoderContext_get_NewEncoding_m7B6E49E8BCF0FC93642D2F58B3490B71F1E8AD57,
	EncoderContext_get_Message_m0CF5394F9C84697DBAD2DC5722C8EADC38E2074F,
	AztecWriter__cctor_m325BC1185CCF82D7E8524712CD0BC2E8662BD012,
	AztecWriter_encode_m0C18DECD2EA767DF24A8516B8E68D98BE30E8E70,
	AztecWriter_encode_mBA3B22B21C0ECC52618CBC5EA817E875036B87FC,
	AztecWriter_renderResult_mD8895976254AB61BE2A0F3F390653C66695C46B0,
	AztecWriter__ctor_m68A2CE5F202CE68C5485B6DFAD805D6F5CF31DEF,
	Mode__ctor_m30529DF91C26A385B7AEDD573D6DF857DF6778B8,
	Mode_getCharacterCountBits_mD54D1539285D6B814D6FA286FB140CA84A3FD839,
	Mode_get_Bits_m94C8659A798B5D29EC10DF332242A14FC011788C,
	Mode_ToString_m484CD08D1B6B629BFDA2ADD7BC410BE6D756A11F,
	Mode__cctor_m6F86E9E1D428864EFD332F6916180019B4960CD7,
	Code39Writer_encode_mEDEC18DD7891E787FAFD5072325A0F79B0384D2B,
	Code39Writer_encode_mCD345524C18B36368A2EC18CE2BDFEAA74BA3A39,
	Code39Writer_toIntArray_m26C96983BE3C2F3CAE30EBCDF8B5CA15E091FF54,
	Code39Writer__ctor_m047B0C071B41C496FA64DCA019FAA2C07275D30D,
	Code39Reader__cctor_m0A682F3C5EC7D1D26C032C63F49F975C091E09A7,
	Code128Reader__cctor_mE66EA0E3DAE56B2F268E3F32280DC5BEEE42777E,
	DataMatrixSymbolInfo144__ctor_m27A624BEE81E8557E1B260AF5867D89F74C393C0,
	DataMatrixSymbolInfo144_getInterleavedBlockCount_m1E361720B32850C61DE44EF9818DF203FA24DB46,
	DataMatrixSymbolInfo144_getDataLengthForInterleavedBlock_mBB4E7B788BEF4F6CCBAD78319AB4065A43A131E9,
	Code128Writer_encode_m5E12E96D86B07D6E80A41AD3610AB0C86BE2A008,
	Code128Writer_encode_mD8779CE4AA67BD5784CABABF0AC844781E7E57B6,
	Code128Writer_findCType_mEA72367E6ACA06126078A74AB1B2EA36E0BB6579,
	Code128Writer_chooseCode_m4148356E098C3163D47B5EA6B60939F39D4967F0,
	Code128Writer__ctor_m9E0C59215CBCC1C5BA789210DD1D17F6719D1715,
	GenericGF__ctor_m858EC075E3000FB217E9BBCBE24D1E28835950C4,
	GenericGF_get_Zero_m4EEFD36C577BDD3A2FEE9CCED46017E10746A11C,
	GenericGF_buildMonomial_m6A66CDEF54C10D656775279BB62FA2C6491172B0,
	GenericGF_addOrSubtract_mD0C8013C59FE84B282C354A492B3DCAA4234BFCC,
	GenericGF_exp_mC107E851341CAF73E04862126BE9989124F888D5,
	GenericGF_log_m6729ED61D75F4EEC4A342CDFF682D221F18B17F9,
	GenericGF_inverse_m58997A25B60795AA9DAA8E3EF50FD5D59AF79A0C,
	GenericGF_multiply_m6D437E1CB1E60B71FD8A28B43D6FC99D2771D1E3,
	GenericGF_get_GeneratorBase_m51D32132194536409A90BBA7092BAFBD97B135D8,
	GenericGF_ToString_mBBDC59FCEC7D637116E92C056A3C400B542D9298,
	GenericGF__cctor_mD7C5F6B412C04D6DCC66F2335389F79EF5886B51,
	PDF417Writer_encode_m7699D2D421E9B479760DCCE7604A856615CE37D2,
	PDF417Writer_bitMatrixFromEncoder_m2B741D147065C75A49BF2608E0D341FC55D73527,
	PDF417Writer_bitMatrixFromBitArray_m45330B6397A00DE687156FF47174E719BC28FF41,
	PDF417Writer_rotateArray_m263B428C14106F1B14B7BBABDBAFE72B79155853,
	PDF417Writer__ctor_m8F8732C49C0D65ADC86091000B11A9F1D7CBCC31,
	PDF417HighLevelEncoder__cctor_m108515117932F302E16D62D08247D94CA8C46880,
	PDF417HighLevelEncoder_encodeHighLevel_mFF5A65A48EA2E9F0C8C19DF68FDCCA33B6A658C7,
	PDF417HighLevelEncoder_getEncoder_mC33CAFB0D8F857745953169B5C6D5A387454C4CB,
	PDF417HighLevelEncoder_toBytes_mCA3BD9C74A1F558EBB93C2F1B26CBC90E9657D40,
	PDF417HighLevelEncoder_toBytes_m3FF17BC2CC9757F53FEC3123691F8434CF469C45,
	PDF417HighLevelEncoder_encodeText_mE11968B5A4063A12EEC2D0760C2D43E8127ECC97,
	PDF417HighLevelEncoder_encodeBinary_mF4B21686DCC0879847E22088105DCAB2BC92B438,
	PDF417HighLevelEncoder_encodeNumeric_mA56D276C844D3946530E26FFA1611CC0DADBED15,
	PDF417HighLevelEncoder_isDigit_mCF967756CE968C9813CCE4B04E5D311834388C50,
	PDF417HighLevelEncoder_isAlphaUpper_mCABD8674B0586BC3E9E8CDE131C059B602E7ED02,
	PDF417HighLevelEncoder_isAlphaLower_m44625FACE0995207CA9898B7A324A2C16B759F1D,
	PDF417HighLevelEncoder_isMixed_m728EBC37573093DB853D7137D09D42E344B6E535,
	PDF417HighLevelEncoder_isPunctuation_m6FE529488EC7EB0428443D5BFC4210547ED22808,
	PDF417HighLevelEncoder_isText_mF4B1D5E6A4BA01BCE0D9CA001DAB29B3F7F40EF0,
	PDF417HighLevelEncoder_determineConsecutiveDigitCount_m42A40C3DD6FA77F0B61A2B84920EB96483B5D1FD,
	PDF417HighLevelEncoder_determineConsecutiveTextCount_m677252E3DE1036949B196F3CBD51EBA11C240CC3,
	PDF417HighLevelEncoder_determineConsecutiveBinaryCount_m9AF736A6510DEA4BFE08B5E8934D9DC14B623B55,
	PDF417HighLevelEncoder_encodingECI_m95C4C13DBEE538EDA48AB82686DBAFEC4BD3785E,
	ErrorCorrection__cctor_mE1C9C18E909F701D341AD81D46C89F9134771ED5,
	ErrorCorrection_encodeECC200_m160DCB833BC11C55446D3521D9F494E7E39A9D78,
	ErrorCorrection_createECCBlock_m638C707ACC7AF5DFB5BADBAC349C6933C79FAB07,
	ErrorCorrection_createECCBlock_mA50D025D41B420C60A1D3A35B595B8BBF83E49DA,
	BinaryShiftToken__ctor_mF766F25760FBFBA67EAA4C0F056D4AB55B104F21,
	BinaryShiftToken_appendTo_m9487F04591FA959C965E1C26C909F67D0C7A7437,
	BinaryShiftToken_ToString_m05A85E2B87DD718FC0F80545503E5C5F98FB78D5,
	Base256Encoder_get_EncodingMode_mC0FFCC7F1D061E83B0A8C9E85890FB0C94BE6584,
	Base256Encoder_encode_mD09F578294B00F96C72903D3FAFEC459FD60BE0B,
	Base256Encoder_randomize255State_mC9D64C4A5FB394D21D7282EBB4F3907054AF5DEE,
	Base256Encoder__ctor_mBA7F858CB96577D285AF7B39A3AE61E20D5B30AF,
	DataMatrixWriter_encode_m459C61FE570E29E6B3FC8583ED3FB3A821883513,
	DataMatrixWriter_encodeLowLevel_m3B133AC3EB00D4B18958B0CA44C9887AD2D92A58,
	DataMatrixWriter_convertByteMatrixToBitMatrix_mB156C7B7ED0F2411078C8A9A15AFEAD7A70C3FB0,
	DataMatrixWriter__ctor_mF7A94D25DE8C163B4D015FA1A65271AE71A6C0DA,
	Base10BigInteger_set_NumberSign_m773477D89D3B7D646368DF35BD90180A3DC8A41D,
	Base10BigInteger__ctor_mC940B222E4109A3FDF1818F9EEBBD04AA4E943D9,
	Base10BigInteger__ctor_m44001801B40CED902AC51B373C4E510EC3B806EE,
	Base10BigInteger__ctor_m3FCB0509458DD98919172F0AAFF76143D465673F,
	Base10BigInteger_Equals_m614429AF42C01075F3324C45C095FD9D07E02E73,
	Base10BigInteger_Equals_m3F08A8AFCBA72BCCBE9A861BC9AF69ECA2B48D68,
	Base10BigInteger_GetHashCode_mF9EA95875A037AF6BAE361381D8BA72D21763606,
	Base10BigInteger_ToString_m7AD628696B4F0B5B26BAF294787BC8C6FCDABC8C,
	Base10BigInteger_Opposite_m45CE78E2AD7420FCC684EBE446E66A1A8CB92C0E,
	Base10BigInteger_Greater_m92D72CD61784C39F77FA8D63FBC255C3A836A64E,
	Base10BigInteger_GreaterOrEqual_mE4A22463595CED0F882D5A54AEC298DAF2C03146,
	Base10BigInteger_SmallerOrEqual_m0807CF213E551BE52C302DB58CE7336642E1A988,
	Base10BigInteger_Abs_m41837AA441C42EFB42B40B7E0D03445B054C7D32,
	Base10BigInteger_Addition_mAF45BAB34E78076CB26E09636E5B1318C3B9545B,
	Base10BigInteger_Multiplication_mEBBEA552F63EC1A58D2141BE5726D7379DC180D0,
	Base10BigInteger_op_Implicit_m9077D758A4219A56FC6FF488844EB2E6A904163E,
	Base10BigInteger_op_Equality_m69975CC1712D97A203BB3A04FA20F00FB9C374C1,
	Base10BigInteger_op_Inequality_m7318E553756DE2DC6A4DF1A4093DB4E1B0DDF81C,
	Base10BigInteger_op_GreaterThanOrEqual_mF1A7F7566F63169CC2DB12846151F53CA7F32BE5,
	Base10BigInteger_op_LessThanOrEqual_mF88645354061799A5CE4BE7385CD4B8FA9ABBFB9,
	Base10BigInteger_op_UnaryNegation_m9C1B1218EF451340B34F6F7506D1699C78AD1C40,
	Base10BigInteger_op_Addition_mED0B187AED89B117E63AB3DB647132B704560D80,
	Base10BigInteger_op_Multiply_m65939F644B649759CF99EF3CAC389D4A96175200,
	Base10BigInteger_Add_m3ECB4373BA1E77D18D3E882926582BACF221FAF6,
	Base10BigInteger_Subtract_m7FF811860772E105ABCD599EC37B01AE2A0CA8F3,
	Base10BigInteger_Multiply_m315267459F246DE9D6D78D3E194996F3CD07AD5D,
	Base10BigInteger__cctor_m1915583D91AE71645148852703C610B8BD1D1939,
	DigitContainer__ctor_m23D682353CA47F33FC605259DF26B0AFB35993E0,
	DigitContainer_get_Item_m5685F599915CD0D680E53ADFF38D30DD99620D71,
	DigitContainer_set_Item_m2D41AE85F3CEEBE7C0969C76ACF0F1F2153F899F,
	HighLevelEncoder__cctor_m0C3906E4A62E0E3EC6FA63DE26B25F664201250C,
	HighLevelEncoder__ctor_mBD6FB379281A95AEA3DCEEADE049FE283B10350C,
	HighLevelEncoder_encode_mD0942D4141B7291015750F6D4DBD21440DE9CA23,
	HighLevelEncoder_updateStateListForChar_mF984BD1841FDDA12ABE867B5B362DB8F92019915,
	HighLevelEncoder_updateStateForChar_m82B1C136FA6CBD34D32A4A75C7C4D2937DB823D7,
	HighLevelEncoder_updateStateListForPair_m5336E4E032D1C9E718305E66EFDF75C7FB7E7370,
	HighLevelEncoder_updateStateForPair_mBF8370BA779CB6CF351E3988B69961DD380E53DD,
	HighLevelEncoder_simplifyStates_m6D5A32F4107775651F3E12721686724609D3FAD3,
};
static const int32_t s_InvokerIndices[574] = 
{
	6289,
	328,
	6289,
	328,
	3967,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	6270,
	5686,
	4487,
	5098,
	4720,
	6005,
	5928,
	5928,
	5928,
	5928,
	5928,
	5928,
	5928,
	5928,
	5524,
	6230,
	3348,
	3893,
	3348,
	5688,
	3967,
	3893,
	3313,
	6289,
	1981,
	5747,
	5747,
	6091,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	6289,
	328,
	4826,
	4708,
	3893,
	3047,
	3967,
	328,
	3047,
	5747,
	5100,
	5524,
	3967,
	6289,
	3047,
	3967,
	6289,
	5435,
	6289,
	3348,
	3044,
	2167,
	3893,
	3893,
	2570,
	3967,
	3313,
	3313,
	1961,
	3967,
	3276,
	3916,
	1961,
	3348,
	3348,
	846,
	6088,
	2591,
	3893,
	3916,
	6289,
	-1,
	6088,
	328,
	3047,
	3967,
	6289,
	2171,
	3916,
	3893,
	3863,
	2884,
	3047,
	3047,
	1703,
	3047,
	3916,
	6005,
	6005,
	6005,
	5036,
	4623,
	6005,
	5020,
	5523,
	3893,
	3967,
	328,
	3047,
	3967,
	6289,
	5923,
	5893,
	3916,
	3348,
	3893,
	3313,
	3893,
	3313,
	3967,
	5175,
	5326,
	5142,
	4967,
	5175,
	5175,
	6088,
	5595,
	5513,
	6289,
	3967,
	3348,
	2171,
	2171,
	3916,
	3916,
	328,
	3047,
	3967,
	6289,
	1289,
	3893,
	3893,
	3893,
	3047,
	6088,
	3916,
	6270,
	6289,
	1981,
	3893,
	3893,
	3893,
	3916,
	1961,
	3893,
	3893,
	3967,
	3967,
	3967,
	3314,
	3348,
	3348,
	2591,
	2591,
	3893,
	3916,
	6091,
	2901,
	2901,
	6091,
	5433,
	5433,
	5433,
	5433,
	6091,
	5599,
	5599,
	5599,
	5599,
	5599,
	6089,
	5433,
	5433,
	5433,
	5433,
	5433,
	6091,
	5599,
	5599,
	5599,
	5599,
	5599,
	5599,
	5599,
	5596,
	5599,
	4632,
	4983,
	4781,
	6289,
	3967,
	2995,
	1962,
	874,
	3893,
	3893,
	3893,
	1703,
	1703,
	3044,
	3044,
	2591,
	3047,
	3916,
	6289,
	3276,
	3313,
	3313,
	3313,
	3916,
	3348,
	3967,
	1961,
	3893,
	3893,
	1593,
	1280,
	3916,
	1277,
	3276,
	3916,
	3313,
	1934,
	1832,
	3044,
	328,
	3967,
	328,
	3047,
	3967,
	6289,
	3893,
	3893,
	3869,
	3283,
	3869,
	3283,
	3967,
	710,
	328,
	3047,
	3967,
	6289,
	6289,
	3893,
	3348,
	5834,
	5850,
	5595,
	3967,
	1324,
	1451,
	1277,
	1451,
	3967,
	837,
	1280,
	3313,
	3313,
	3313,
	3313,
	3348,
	3916,
	1703,
	1703,
	2171,
	6289,
	328,
	4826,
	3967,
	3893,
	3348,
	526,
	5834,
	2171,
	1631,
	5595,
	3967,
	3893,
	3348,
	1631,
	2171,
	3967,
	3893,
	3893,
	3313,
	1961,
	1451,
	1277,
	3967,
	837,
	1704,
	2591,
	3893,
	3916,
	1129,
	1129,
	1961,
	3967,
	3916,
	1703,
	328,
	3047,
	3967,
	6091,
	6289,
	219,
	86,
	4477,
	3893,
	3893,
	3893,
	3893,
	3893,
	3893,
	3893,
	2884,
	2884,
	3916,
	6289,
	1324,
	2171,
	3916,
	1281,
	3893,
	3893,
	3916,
	6289,
	3893,
	3893,
	3893,
	3893,
	6005,
	5185,
	4851,
	4722,
	6000,
	5599,
	5923,
	4722,
	5582,
	5021,
	5747,
	4323,
	4826,
	5595,
	5834,
	4954,
	4986,
	5834,
	5834,
	5337,
	5834,
	5834,
	6289,
	6000,
	5595,
	6289,
	3967,
	3276,
	3916,
	5081,
	4675,
	5285,
	399,
	2167,
	1703,
	837,
	3313,
	3276,
	3348,
	3276,
	6289,
	2171,
	3967,
	3916,
	3348,
	3916,
	3348,
	3916,
	3348,
	3893,
	3313,
	3916,
	3348,
	3916,
	5920,
	6289,
	6218,
	4578,
	5834,
	5328,
	5834,
	5328,
	6000,
	5514,
	5328,
	5834,
	5920,
	6218,
	6218,
	5285,
	5285,
	5285,
	5285,
	6218,
	5834,
	6289,
	3893,
	1631,
	3967,
	6289,
	3348,
	3313,
	2171,
	3313,
	3960,
	3348,
	3391,
	3893,
	3313,
	3967,
	3863,
	3893,
	3893,
	3967,
	3313,
	3967,
	3893,
	3313,
	3916,
	3916,
	3893,
	3916,
	6289,
	328,
	4164,
	5175,
	3967,
	1326,
	2901,
	3893,
	3916,
	6289,
	328,
	3047,
	5747,
	3967,
	6289,
	6289,
	3967,
	3893,
	2884,
	328,
	3047,
	5524,
	1034,
	3967,
	1280,
	3916,
	1703,
	5514,
	2884,
	2884,
	2884,
	1593,
	3893,
	3916,
	6289,
	328,
	4279,
	5595,
	6091,
	3967,
	6289,
	4830,
	6091,
	5599,
	5613,
	4409,
	4566,
	4972,
	5928,
	5928,
	5928,
	5928,
	5928,
	5928,
	5524,
	5524,
	4720,
	5747,
	6289,
	5599,
	5595,
	4826,
	1324,
	2171,
	3916,
	3893,
	3348,
	5686,
	3967,
	328,
	5599,
	6091,
	3967,
	3313,
	3967,
	3314,
	3348,
	2591,
	2591,
	3893,
	3916,
	6091,
	5433,
	5433,
	5433,
	6091,
	5599,
	5599,
	6089,
	5433,
	5433,
	5433,
	5433,
	6091,
	5599,
	5599,
	5599,
	5599,
	5599,
	6289,
	3967,
	2995,
	1962,
	6289,
	3348,
	3916,
	1716,
	1326,
	5175,
	4972,
	6091,
};
static const Il2CppTokenRangePair s_rgctxIndices[1] = 
{
	{ 0x0200000A, { 0, 6 } },
};
static const Il2CppRGCTXDefinition s_rgctxValues[6] = 
{
	{ (Il2CppRGCTXDataType)3, 2292 },
	{ (Il2CppRGCTXDataType)3, 2294 },
	{ (Il2CppRGCTXDataType)3, 2293 },
	{ (Il2CppRGCTXDataType)3, 2295 },
	{ (Il2CppRGCTXDataType)3, 2291 },
	{ (Il2CppRGCTXDataType)2, 3014 },
};
extern const CustomAttributesCacheGenerator g_zxing_unity_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_zxing_unity_CodeGenModule;
const Il2CppCodeGenModule g_zxing_unity_CodeGenModule = 
{
	"zxing.unity.dll",
	574,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	1,
	s_rgctxIndices,
	6,
	s_rgctxValues,
	NULL,
	g_zxing_unity_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

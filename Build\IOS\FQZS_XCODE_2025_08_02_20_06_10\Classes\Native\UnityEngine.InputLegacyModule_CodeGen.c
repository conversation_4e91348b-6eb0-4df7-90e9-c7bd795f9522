﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Int32 UnityEngine.Touch::get_fingerId()
extern void Touch_get_fingerId_mCED0E66949120E69BFE9294DC0A11A6F9FDBD129 (void);
// 0x00000002 UnityEngine.Vector2 UnityEngine.Touch::get_position()
extern void Touch_get_position_mE32B04C6DA32A0965C403A31847ED7F1725EA1DE (void);
// 0x00000003 UnityEngine.Vector2 UnityEngine.Touch::get_rawPosition()
extern void Touch_get_rawPosition_mCC88EEAB263CA3D423C6079787BD44AFB3C61697 (void);
// 0x00000004 UnityEngine.Vector2 UnityEngine.Touch::get_deltaPosition()
extern void Touch_get_deltaPosition_mF9D60C253E41DC4E4F832F88A1041BE8A9E7C0FB (void);
// 0x00000005 System.Single UnityEngine.Touch::get_deltaTime()
extern void Touch_get_deltaTime_mFDE8430AB54C2B10F32F0375ED5B2F9CE790AE37 (void);
// 0x00000006 System.Int32 UnityEngine.Touch::get_tapCount()
extern void Touch_get_tapCount_m3F2B62FA02EF54D5FA6C43ECB4A3A8854C5BA1F8 (void);
// 0x00000007 UnityEngine.TouchPhase UnityEngine.Touch::get_phase()
extern void Touch_get_phase_m576EA3F4FE1D12EB85510326AD8EC3C2EB267257 (void);
// 0x00000008 UnityEngine.TouchType UnityEngine.Touch::get_type()
extern void Touch_get_type_m33FB24B6A53A307E8AC9881ED3B483DD4B44C050 (void);
// 0x00000009 System.Boolean UnityEngine.LocationService::IsServiceEnabledByUser()
extern void LocationService_IsServiceEnabledByUser_m76C7A891EEBAA82E0284831CD72799882C3913B5 (void);
// 0x0000000A System.Boolean UnityEngine.LocationService::get_isEnabledByUser()
extern void LocationService_get_isEnabledByUser_mD26BB9D35EC9DE4B38F7771B5E910B14EFC2B00A (void);
// 0x0000000B System.Void UnityEngine.LocationService::.ctor()
extern void LocationService__ctor_m56E613208DF3884D18A8B50E106F7358C334580C (void);
// 0x0000000C UnityEngine.GameObject UnityEngine.CameraRaycastHelper::RaycastTry(UnityEngine.Camera,UnityEngine.Ray,System.Single,System.Int32)
extern void CameraRaycastHelper_RaycastTry_mB87669802545BA2EDEE19CCC6DA5AF90F71B9EBC (void);
// 0x0000000D UnityEngine.GameObject UnityEngine.CameraRaycastHelper::RaycastTry2D(UnityEngine.Camera,UnityEngine.Ray,System.Single,System.Int32)
extern void CameraRaycastHelper_RaycastTry2D_mAF29931274919F4EDC27D8CBA0F1D50A69B9018C (void);
// 0x0000000E UnityEngine.GameObject UnityEngine.CameraRaycastHelper::RaycastTry_Injected(UnityEngine.Camera,UnityEngine.Ray&,System.Single,System.Int32)
extern void CameraRaycastHelper_RaycastTry_Injected_m2B2B06CB00DE2D21C03F65131CDE226FB469C937 (void);
// 0x0000000F UnityEngine.GameObject UnityEngine.CameraRaycastHelper::RaycastTry2D_Injected(UnityEngine.Camera,UnityEngine.Ray&,System.Single,System.Int32)
extern void CameraRaycastHelper_RaycastTry2D_Injected_mE1280DA34FDAFD8DB06175027141C04F1EED1FDF (void);
// 0x00000010 System.Boolean UnityEngine.Input::GetKeyInt(UnityEngine.KeyCode)
extern void Input_GetKeyInt_m7476381058BB4B167E69B1DE6D0D8D4DE3FA63E4 (void);
// 0x00000011 System.Boolean UnityEngine.Input::GetKeyUpInt(UnityEngine.KeyCode)
extern void Input_GetKeyUpInt_m89C1EB67ED454B0855659FCD63667A2DE29B445B (void);
// 0x00000012 System.Boolean UnityEngine.Input::GetKeyDownInt(UnityEngine.KeyCode)
extern void Input_GetKeyDownInt_m396EF11DDAC5212B686E0EA37A90DA28320A6660 (void);
// 0x00000013 System.Single UnityEngine.Input::GetAxis(System.String)
extern void Input_GetAxis_m92483EBE554F6AC229B6A04487F2B8047772B4E4 (void);
// 0x00000014 System.Single UnityEngine.Input::GetAxisRaw(System.String)
extern void Input_GetAxisRaw_m6B145732584DA7EDEE6B38BB7F282AA246BD22B7 (void);
// 0x00000015 System.Boolean UnityEngine.Input::GetButtonDown(System.String)
extern void Input_GetButtonDown_m118FFE5239B51967E8EE77A3651B53E1E55412BA (void);
// 0x00000016 System.Boolean UnityEngine.Input::GetMouseButton(System.Int32)
extern void Input_GetMouseButton_mEF40689A9E574691E832255C430A9E626B005AF3 (void);
// 0x00000017 System.Boolean UnityEngine.Input::GetMouseButtonDown(System.Int32)
extern void Input_GetMouseButtonDown_m689599122AC9F35D1FCCA2BF2414387472B0799F (void);
// 0x00000018 System.Boolean UnityEngine.Input::GetMouseButtonUp(System.Int32)
extern void Input_GetMouseButtonUp_m72BC7AC98DF3D431D2AD59D0916216D4E31A9503 (void);
// 0x00000019 UnityEngine.Touch UnityEngine.Input::GetTouch(System.Int32)
extern void Input_GetTouch_m7C74CAE219D0DD4E0F41598BBC5869056575E62E (void);
// 0x0000001A System.Boolean UnityEngine.Input::GetKey(UnityEngine.KeyCode)
extern void Input_GetKey_m34EFCC2F4D8BDDEAC7CE83C0ADE0C4EA812E5900 (void);
// 0x0000001B System.Boolean UnityEngine.Input::GetKeyUp(UnityEngine.KeyCode)
extern void Input_GetKeyUp_m3DFABEAD727BF79544B63728EAF7CBBB6A631A29 (void);
// 0x0000001C System.Boolean UnityEngine.Input::GetKeyDown(UnityEngine.KeyCode)
extern void Input_GetKeyDown_m806A477EAE0AE49AA9C5E518A0800A3D80D873DC (void);
// 0x0000001D System.Boolean UnityEngine.Input::get_anyKeyDown()
extern void Input_get_anyKeyDown_m950DA1F8190434A4A9C5DB8D22DAC6DF812622F1 (void);
// 0x0000001E System.String UnityEngine.Input::get_inputString()
extern void Input_get_inputString_m3AC50FD8CF2E9A5A13948F5C9EC267D0E86D464B (void);
// 0x0000001F UnityEngine.Vector3 UnityEngine.Input::get_mousePosition()
extern void Input_get_mousePosition_mBCEAE9AEF8827512D6ADF03AB336DFA2C8A223E0 (void);
// 0x00000020 UnityEngine.Vector2 UnityEngine.Input::get_mouseScrollDelta()
extern void Input_get_mouseScrollDelta_m1F8DD2AC969D7F881CE0DC2A5A5C0C9F5DB73339 (void);
// 0x00000021 UnityEngine.IMECompositionMode UnityEngine.Input::get_imeCompositionMode()
extern void Input_get_imeCompositionMode_mAB5D7507E197FF50B1F07CC79B4DCFBA86E199E3 (void);
// 0x00000022 System.Void UnityEngine.Input::set_imeCompositionMode(UnityEngine.IMECompositionMode)
extern void Input_set_imeCompositionMode_m0469D1B6135613F33A69EE25346FE04284EEAF1C (void);
// 0x00000023 System.String UnityEngine.Input::get_compositionString()
extern void Input_get_compositionString_m1BDB0B72F75478EF89E4F1A07D6F151578721397 (void);
// 0x00000024 UnityEngine.Vector2 UnityEngine.Input::get_compositionCursorPos()
extern void Input_get_compositionCursorPos_m9C3ED95009D842F3C5DE9E3868536FE7EE567F2B (void);
// 0x00000025 System.Void UnityEngine.Input::set_compositionCursorPos(UnityEngine.Vector2)
extern void Input_set_compositionCursorPos_m77771F4CFA1D80D79B3E6CFC9F915FB7FCD47216 (void);
// 0x00000026 System.Boolean UnityEngine.Input::get_mousePresent()
extern void Input_get_mousePresent_mE4B5C121927B19CFB75E43C041878B4B5A0F0509 (void);
// 0x00000027 System.Int32 UnityEngine.Input::get_touchCount()
extern void Input_get_touchCount_m565DDA076AD6E8E57D4F3F698A760D7B5ACBF505 (void);
// 0x00000028 System.Boolean UnityEngine.Input::get_touchSupported()
extern void Input_get_touchSupported_m9DE2CCA4B50AC6716BB64826538DFD866F3D1717 (void);
// 0x00000029 System.Void UnityEngine.Input::set_multiTouchEnabled(System.Boolean)
extern void Input_set_multiTouchEnabled_mE121064CF93C3D5CD3249AAF0E6ACFECA051B8C2 (void);
// 0x0000002A UnityEngine.Vector3 UnityEngine.Input::get_acceleration()
extern void Input_get_acceleration_mEE59E8B523FA5ABEE8949B677A8FFE607A114218 (void);
// 0x0000002B UnityEngine.LocationService UnityEngine.Input::get_location()
extern void Input_get_location_m472A2ED032BC13CCF356526463A2B1E6060CAC87 (void);
// 0x0000002C UnityEngine.Touch[] UnityEngine.Input::get_touches()
extern void Input_get_touches_m970BDA1D26415B34ADDF828BFCDC5FC9E1DAEF7A (void);
// 0x0000002D System.Void UnityEngine.Input::GetTouch_Injected(System.Int32,UnityEngine.Touch&)
extern void Input_GetTouch_Injected_m170DA89CBB0F2A8146005B93768F46B83C1A9939 (void);
// 0x0000002E System.Void UnityEngine.Input::get_mousePosition_Injected(UnityEngine.Vector3&)
extern void Input_get_mousePosition_Injected_m1DDBE51119B9781DAA27852E6DEBAE7F28AF48DE (void);
// 0x0000002F System.Void UnityEngine.Input::get_mouseScrollDelta_Injected(UnityEngine.Vector2&)
extern void Input_get_mouseScrollDelta_Injected_m5CE6B71E63BF12ABE46DCC87309FAE9AB5153D0A (void);
// 0x00000030 System.Void UnityEngine.Input::get_compositionCursorPos_Injected(UnityEngine.Vector2&)
extern void Input_get_compositionCursorPos_Injected_m25176C7E91945EB495B63331A4E3E962BD7FD4A9 (void);
// 0x00000031 System.Void UnityEngine.Input::set_compositionCursorPos_Injected(UnityEngine.Vector2&)
extern void Input_set_compositionCursorPos_Injected_mD9B1A580A8AE8D1CF236F66E05EA5BAC82BF56FB (void);
// 0x00000032 System.Void UnityEngine.Input::get_acceleration_Injected(UnityEngine.Vector3&)
extern void Input_get_acceleration_Injected_m15FB04D6E1B4C841CE273D7A52D9F92C542F1466 (void);
// 0x00000033 System.Void UnityEngine.SendMouseEvents::SetMouseMoved()
extern void SendMouseEvents_SetMouseMoved_m4F6A576B20A96648D7B88D7E4EBB09433F9AB244 (void);
// 0x00000034 System.Void UnityEngine.SendMouseEvents::DoSendMouseEvents(System.Int32)
extern void SendMouseEvents_DoSendMouseEvents_mDA4BEA25A4802C7ACFE533F2CF7096F21D7CC59D (void);
// 0x00000035 System.Void UnityEngine.SendMouseEvents::SendEvents(System.Int32,UnityEngine.SendMouseEvents/HitInfo)
extern void SendMouseEvents_SendEvents_m8211E09B81BE733C5D2F305B21094D5549891A4B (void);
// 0x00000036 System.Void UnityEngine.SendMouseEvents::.cctor()
extern void SendMouseEvents__cctor_m584B7044FE1340436B11E3C5229647EE44F12D8D (void);
// 0x00000037 System.Void UnityEngine.SendMouseEvents/HitInfo::SendMessage(System.String)
extern void HitInfo_SendMessage_m2D813691948EAB9CDA487A3B8668678EABFCFA62 (void);
// 0x00000038 System.Boolean UnityEngine.SendMouseEvents/HitInfo::op_Implicit(UnityEngine.SendMouseEvents/HitInfo)
extern void HitInfo_op_Implicit_mB02918E7074EE84AE168A8F0F9F161DA6EB29E94 (void);
// 0x00000039 System.Boolean UnityEngine.SendMouseEvents/HitInfo::Compare(UnityEngine.SendMouseEvents/HitInfo,UnityEngine.SendMouseEvents/HitInfo)
extern void HitInfo_Compare_mC05742F6841072DA1F7AD852C9CAE8FEFE3D59BA (void);
static Il2CppMethodPointer s_methodPointers[57] = 
{
	Touch_get_fingerId_mCED0E66949120E69BFE9294DC0A11A6F9FDBD129,
	Touch_get_position_mE32B04C6DA32A0965C403A31847ED7F1725EA1DE,
	Touch_get_rawPosition_mCC88EEAB263CA3D423C6079787BD44AFB3C61697,
	Touch_get_deltaPosition_mF9D60C253E41DC4E4F832F88A1041BE8A9E7C0FB,
	Touch_get_deltaTime_mFDE8430AB54C2B10F32F0375ED5B2F9CE790AE37,
	Touch_get_tapCount_m3F2B62FA02EF54D5FA6C43ECB4A3A8854C5BA1F8,
	Touch_get_phase_m576EA3F4FE1D12EB85510326AD8EC3C2EB267257,
	Touch_get_type_m33FB24B6A53A307E8AC9881ED3B483DD4B44C050,
	LocationService_IsServiceEnabledByUser_m76C7A891EEBAA82E0284831CD72799882C3913B5,
	LocationService_get_isEnabledByUser_mD26BB9D35EC9DE4B38F7771B5E910B14EFC2B00A,
	LocationService__ctor_m56E613208DF3884D18A8B50E106F7358C334580C,
	CameraRaycastHelper_RaycastTry_mB87669802545BA2EDEE19CCC6DA5AF90F71B9EBC,
	CameraRaycastHelper_RaycastTry2D_mAF29931274919F4EDC27D8CBA0F1D50A69B9018C,
	CameraRaycastHelper_RaycastTry_Injected_m2B2B06CB00DE2D21C03F65131CDE226FB469C937,
	CameraRaycastHelper_RaycastTry2D_Injected_mE1280DA34FDAFD8DB06175027141C04F1EED1FDF,
	Input_GetKeyInt_m7476381058BB4B167E69B1DE6D0D8D4DE3FA63E4,
	Input_GetKeyUpInt_m89C1EB67ED454B0855659FCD63667A2DE29B445B,
	Input_GetKeyDownInt_m396EF11DDAC5212B686E0EA37A90DA28320A6660,
	Input_GetAxis_m92483EBE554F6AC229B6A04487F2B8047772B4E4,
	Input_GetAxisRaw_m6B145732584DA7EDEE6B38BB7F282AA246BD22B7,
	Input_GetButtonDown_m118FFE5239B51967E8EE77A3651B53E1E55412BA,
	Input_GetMouseButton_mEF40689A9E574691E832255C430A9E626B005AF3,
	Input_GetMouseButtonDown_m689599122AC9F35D1FCCA2BF2414387472B0799F,
	Input_GetMouseButtonUp_m72BC7AC98DF3D431D2AD59D0916216D4E31A9503,
	Input_GetTouch_m7C74CAE219D0DD4E0F41598BBC5869056575E62E,
	Input_GetKey_m34EFCC2F4D8BDDEAC7CE83C0ADE0C4EA812E5900,
	Input_GetKeyUp_m3DFABEAD727BF79544B63728EAF7CBBB6A631A29,
	Input_GetKeyDown_m806A477EAE0AE49AA9C5E518A0800A3D80D873DC,
	Input_get_anyKeyDown_m950DA1F8190434A4A9C5DB8D22DAC6DF812622F1,
	Input_get_inputString_m3AC50FD8CF2E9A5A13948F5C9EC267D0E86D464B,
	Input_get_mousePosition_mBCEAE9AEF8827512D6ADF03AB336DFA2C8A223E0,
	Input_get_mouseScrollDelta_m1F8DD2AC969D7F881CE0DC2A5A5C0C9F5DB73339,
	Input_get_imeCompositionMode_mAB5D7507E197FF50B1F07CC79B4DCFBA86E199E3,
	Input_set_imeCompositionMode_m0469D1B6135613F33A69EE25346FE04284EEAF1C,
	Input_get_compositionString_m1BDB0B72F75478EF89E4F1A07D6F151578721397,
	Input_get_compositionCursorPos_m9C3ED95009D842F3C5DE9E3868536FE7EE567F2B,
	Input_set_compositionCursorPos_m77771F4CFA1D80D79B3E6CFC9F915FB7FCD47216,
	Input_get_mousePresent_mE4B5C121927B19CFB75E43C041878B4B5A0F0509,
	Input_get_touchCount_m565DDA076AD6E8E57D4F3F698A760D7B5ACBF505,
	Input_get_touchSupported_m9DE2CCA4B50AC6716BB64826538DFD866F3D1717,
	Input_set_multiTouchEnabled_mE121064CF93C3D5CD3249AAF0E6ACFECA051B8C2,
	Input_get_acceleration_mEE59E8B523FA5ABEE8949B677A8FFE607A114218,
	Input_get_location_m472A2ED032BC13CCF356526463A2B1E6060CAC87,
	Input_get_touches_m970BDA1D26415B34ADDF828BFCDC5FC9E1DAEF7A,
	Input_GetTouch_Injected_m170DA89CBB0F2A8146005B93768F46B83C1A9939,
	Input_get_mousePosition_Injected_m1DDBE51119B9781DAA27852E6DEBAE7F28AF48DE,
	Input_get_mouseScrollDelta_Injected_m5CE6B71E63BF12ABE46DCC87309FAE9AB5153D0A,
	Input_get_compositionCursorPos_Injected_m25176C7E91945EB495B63331A4E3E962BD7FD4A9,
	Input_set_compositionCursorPos_Injected_mD9B1A580A8AE8D1CF236F66E05EA5BAC82BF56FB,
	Input_get_acceleration_Injected_m15FB04D6E1B4C841CE273D7A52D9F92C542F1466,
	SendMouseEvents_SetMouseMoved_m4F6A576B20A96648D7B88D7E4EBB09433F9AB244,
	SendMouseEvents_DoSendMouseEvents_mDA4BEA25A4802C7ACFE533F2CF7096F21D7CC59D,
	SendMouseEvents_SendEvents_m8211E09B81BE733C5D2F305B21094D5549891A4B,
	SendMouseEvents__cctor_m584B7044FE1340436B11E3C5229647EE44F12D8D,
	HitInfo_SendMessage_m2D813691948EAB9CDA487A3B8668678EABFCFA62,
	HitInfo_op_Implicit_mB02918E7074EE84AE168A8F0F9F161DA6EB29E94,
	HitInfo_Compare_mC05742F6841072DA1F7AD852C9CAE8FEFE3D59BA,
};
extern void Touch_get_fingerId_mCED0E66949120E69BFE9294DC0A11A6F9FDBD129_AdjustorThunk (void);
extern void Touch_get_position_mE32B04C6DA32A0965C403A31847ED7F1725EA1DE_AdjustorThunk (void);
extern void Touch_get_rawPosition_mCC88EEAB263CA3D423C6079787BD44AFB3C61697_AdjustorThunk (void);
extern void Touch_get_deltaPosition_mF9D60C253E41DC4E4F832F88A1041BE8A9E7C0FB_AdjustorThunk (void);
extern void Touch_get_deltaTime_mFDE8430AB54C2B10F32F0375ED5B2F9CE790AE37_AdjustorThunk (void);
extern void Touch_get_tapCount_m3F2B62FA02EF54D5FA6C43ECB4A3A8854C5BA1F8_AdjustorThunk (void);
extern void Touch_get_phase_m576EA3F4FE1D12EB85510326AD8EC3C2EB267257_AdjustorThunk (void);
extern void Touch_get_type_m33FB24B6A53A307E8AC9881ED3B483DD4B44C050_AdjustorThunk (void);
extern void HitInfo_SendMessage_m2D813691948EAB9CDA487A3B8668678EABFCFA62_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[9] = 
{
	{ 0x06000001, Touch_get_fingerId_mCED0E66949120E69BFE9294DC0A11A6F9FDBD129_AdjustorThunk },
	{ 0x06000002, Touch_get_position_mE32B04C6DA32A0965C403A31847ED7F1725EA1DE_AdjustorThunk },
	{ 0x06000003, Touch_get_rawPosition_mCC88EEAB263CA3D423C6079787BD44AFB3C61697_AdjustorThunk },
	{ 0x06000004, Touch_get_deltaPosition_mF9D60C253E41DC4E4F832F88A1041BE8A9E7C0FB_AdjustorThunk },
	{ 0x06000005, Touch_get_deltaTime_mFDE8430AB54C2B10F32F0375ED5B2F9CE790AE37_AdjustorThunk },
	{ 0x06000006, Touch_get_tapCount_m3F2B62FA02EF54D5FA6C43ECB4A3A8854C5BA1F8_AdjustorThunk },
	{ 0x06000007, Touch_get_phase_m576EA3F4FE1D12EB85510326AD8EC3C2EB267257_AdjustorThunk },
	{ 0x06000008, Touch_get_type_m33FB24B6A53A307E8AC9881ED3B483DD4B44C050_AdjustorThunk },
	{ 0x06000037, HitInfo_SendMessage_m2D813691948EAB9CDA487A3B8668678EABFCFA62_AdjustorThunk },
};
static const int32_t s_InvokerIndices[57] = 
{
	3893,
	3963,
	3963,
	3963,
	3951,
	3893,
	3893,
	3893,
	6256,
	3863,
	3967,
	4866,
	4866,
	4806,
	4806,
	5920,
	5920,
	5920,
	6139,
	6139,
	5923,
	5920,
	5920,
	5920,
	6150,
	5920,
	5920,
	5920,
	6256,
	6270,
	6287,
	6286,
	6263,
	6213,
	6270,
	6286,
	6233,
	6256,
	6263,
	6256,
	6208,
	6287,
	6270,
	6270,
	5741,
	6206,
	6206,
	6206,
	6206,
	6206,
	6289,
	6213,
	5752,
	6289,
	3348,
	5932,
	5462,
};
extern const CustomAttributesCacheGenerator g_UnityEngine_InputLegacyModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_InputLegacyModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_InputLegacyModule_CodeGenModule = 
{
	"UnityEngine.InputLegacyModule.dll",
	57,
	s_methodPointers,
	9,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_UnityEngine_InputLegacyModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void UnityEngine.AssetBundle::.ctor()
extern void AssetBundle__ctor_mCE6DB7758AAD0EDDB044FC67C5BC7EC987BF3F71 (void);
// 0x00000002 UnityEngine.Object UnityEngine.AssetBundle::get_mainAsset()
extern void AssetBundle_get_mainAsset_mDAF39948DFB48C4C8DA324C11A6E7C9FF2F7F58A (void);
// 0x00000003 UnityEngine.Object UnityEngine.AssetBundle::returnMainAsset(UnityEngine.AssetBundle)
extern void AssetBundle_returnMainAsset_mEADFB842D3D38FDAFF6052F93E178302C0A41FD6 (void);
// 0x00000004 System.Void UnityEngine.AssetBundle::UnloadAllAssetBundles(System.Boolean)
extern void AssetBundle_UnloadAllAssetBundles_mDC192BDAF0AEA5F6B1E9EFEEE11AB28B105CF521 (void);
// 0x00000005 UnityEngine.AssetBundle[] UnityEngine.AssetBundle::GetAllLoadedAssetBundles_Native()
extern void AssetBundle_GetAllLoadedAssetBundles_Native_mA2641B193E24C35A242E8125B49AEDF333E4BDCF (void);
// 0x00000006 System.Collections.Generic.IEnumerable`1<UnityEngine.AssetBundle> UnityEngine.AssetBundle::GetAllLoadedAssetBundles()
extern void AssetBundle_GetAllLoadedAssetBundles_mF192A985FCBCB5C8123A0E0BDF615BBBB7A45179 (void);
// 0x00000007 UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromFileAsync_Internal(System.String,System.UInt32,System.UInt64)
extern void AssetBundle_LoadFromFileAsync_Internal_m7DF7319EA7A245F43B9AC65483A4112CBD169F29 (void);
// 0x00000008 UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromFileAsync(System.String)
extern void AssetBundle_LoadFromFileAsync_mDF90143DECFE5149A8CF7D192E3D7ACBFEBE37D8 (void);
// 0x00000009 UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromFileAsync(System.String,System.UInt32)
extern void AssetBundle_LoadFromFileAsync_mCD630216EE5541040DEFF14F4FDC27DB348B42BC (void);
// 0x0000000A UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromFileAsync(System.String,System.UInt32,System.UInt64)
extern void AssetBundle_LoadFromFileAsync_mD919D804F62FBF3AF71410E77AC9940205375EA2 (void);
// 0x0000000B UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromFile_Internal(System.String,System.UInt32,System.UInt64)
extern void AssetBundle_LoadFromFile_Internal_mC5A549D462272D3E202D62CF295BDAF44F4E4B7C (void);
// 0x0000000C UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromFile(System.String)
extern void AssetBundle_LoadFromFile_mE9B8D7BC74F2F67AE710881EFBBB541E8A8F6B3A (void);
// 0x0000000D UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromFile(System.String,System.UInt32)
extern void AssetBundle_LoadFromFile_m4CE03165FB7013C3B3664B082A5B4FD455009DE1 (void);
// 0x0000000E UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromFile(System.String,System.UInt32,System.UInt64)
extern void AssetBundle_LoadFromFile_m21CB874684AF8C9F77252362CA4E3E43440FF851 (void);
// 0x0000000F UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromMemoryAsync_Internal(System.Byte[],System.UInt32)
extern void AssetBundle_LoadFromMemoryAsync_Internal_m3722CC697D35ADA206E6574527A4F6681882D5D4 (void);
// 0x00000010 UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromMemoryAsync(System.Byte[])
extern void AssetBundle_LoadFromMemoryAsync_m62DAF5E226F11D690E8DF1FC6A32625D7A7A733D (void);
// 0x00000011 UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromMemoryAsync(System.Byte[],System.UInt32)
extern void AssetBundle_LoadFromMemoryAsync_m75E26133C7B2665F8B0F522A530B2FC6C3A106A9 (void);
// 0x00000012 UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromMemory_Internal(System.Byte[],System.UInt32)
extern void AssetBundle_LoadFromMemory_Internal_mBF6BF6BFDE5DA4A76735766C969591424BB9649A (void);
// 0x00000013 UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromMemory(System.Byte[])
extern void AssetBundle_LoadFromMemory_m435A5FC269478E3E4809AB7355878B166D712E14 (void);
// 0x00000014 UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromMemory(System.Byte[],System.UInt32)
extern void AssetBundle_LoadFromMemory_m58D3EB0E61463CDF550EA9E9C2F6D962FF16457B (void);
// 0x00000015 System.Void UnityEngine.AssetBundle::ValidateLoadFromStream(System.IO.Stream)
extern void AssetBundle_ValidateLoadFromStream_m39F4CB6F1717C48017F09FFC1E537704BB47A5A1 (void);
// 0x00000016 UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromStreamAsync(System.IO.Stream,System.UInt32,System.UInt32)
extern void AssetBundle_LoadFromStreamAsync_m4891FFAF61A3100FEFC5BDBBA60D2D946A7FBFEB (void);
// 0x00000017 UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromStreamAsync(System.IO.Stream,System.UInt32)
extern void AssetBundle_LoadFromStreamAsync_m750F83983CD25FB7C26E103BFCED35AA04725A97 (void);
// 0x00000018 UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromStreamAsync(System.IO.Stream)
extern void AssetBundle_LoadFromStreamAsync_mC33AE0A0A1C96EDE7F23968397868A45DAFE01F2 (void);
// 0x00000019 UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromStream(System.IO.Stream,System.UInt32,System.UInt32)
extern void AssetBundle_LoadFromStream_mB07F4D123259845295A21A4D14D7EC2B7BB647DF (void);
// 0x0000001A UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromStream(System.IO.Stream,System.UInt32)
extern void AssetBundle_LoadFromStream_m4BCF56F96EE96106529A6D1366EA9C8B633A4895 (void);
// 0x0000001B UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromStream(System.IO.Stream)
extern void AssetBundle_LoadFromStream_mAE868802265CD014AAB9E8ACABB8F94DF3B6AE84 (void);
// 0x0000001C UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromStreamAsyncInternal(System.IO.Stream,System.UInt32,System.UInt32)
extern void AssetBundle_LoadFromStreamAsyncInternal_m8B64B48FD5D92F632AD5D755C5B9F6B48E5D1F51 (void);
// 0x0000001D UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromStreamInternal(System.IO.Stream,System.UInt32,System.UInt32)
extern void AssetBundle_LoadFromStreamInternal_m463BAC1F32549D1B4AE88A5185A135FE7676619A (void);
// 0x0000001E System.Boolean UnityEngine.AssetBundle::get_isStreamedSceneAssetBundle()
extern void AssetBundle_get_isStreamedSceneAssetBundle_m0F0FDECFC88F31CA01E7E36532E186224174A7A4 (void);
// 0x0000001F System.Boolean UnityEngine.AssetBundle::Contains(System.String)
extern void AssetBundle_Contains_m00D431624B6594F6222D946F50A7DCA8559B1B44 (void);
// 0x00000020 UnityEngine.Object UnityEngine.AssetBundle::LoadAsset(System.String)
extern void AssetBundle_LoadAsset_m598BB4E86B07C2BADAE6ED6FFBE5BB5E7A3392D5 (void);
// 0x00000021 T UnityEngine.AssetBundle::LoadAsset(System.String)
// 0x00000022 UnityEngine.Object UnityEngine.AssetBundle::LoadAsset(System.String,System.Type)
extern void AssetBundle_LoadAsset_m9139320F8B6D3E43B7D29AA7A60030306AE0A2C6 (void);
// 0x00000023 UnityEngine.Object UnityEngine.AssetBundle::LoadAsset_Internal(System.String,System.Type)
extern void AssetBundle_LoadAsset_Internal_mFB165539087545C4B5763BA8B590D84318C6FE1B (void);
// 0x00000024 UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAssetAsync(System.String)
extern void AssetBundle_LoadAssetAsync_m033CE66F7C38A5A46C627004B0D3AD830C4CE4F3 (void);
// 0x00000025 UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAssetAsync(System.String)
// 0x00000026 UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAssetAsync(System.String,System.Type)
extern void AssetBundle_LoadAssetAsync_m63C7C5654FA8D0824DC920A1B1530C37CCB3DF6E (void);
// 0x00000027 UnityEngine.Object[] UnityEngine.AssetBundle::LoadAssetWithSubAssets(System.String)
extern void AssetBundle_LoadAssetWithSubAssets_mB25B0117E71DABE78028CE638F61D6C6C9152DDC (void);
// 0x00000028 T[] UnityEngine.AssetBundle::ConvertObjects(UnityEngine.Object[])
// 0x00000029 UnityEngine.Object[] UnityEngine.AssetBundle::LoadAssetWithSubAssets(System.String,System.Type)
extern void AssetBundle_LoadAssetWithSubAssets_mAABE29A8F1719A85DC474932FF12A44B29CACB45 (void);
// 0x0000002A UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAssetWithSubAssetsAsync(System.String)
extern void AssetBundle_LoadAssetWithSubAssetsAsync_m48EEBA8BAC4B511F44F8B878882E6102D50E6843 (void);
// 0x0000002B UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAssetWithSubAssetsAsync(System.String,System.Type)
extern void AssetBundle_LoadAssetWithSubAssetsAsync_m655947AD33D64C1F71F822AEFDE3D2A57C809AF5 (void);
// 0x0000002C UnityEngine.Object[] UnityEngine.AssetBundle::LoadAllAssets()
extern void AssetBundle_LoadAllAssets_mB132E05BAFF4776C59BC80082A2FE68060741603 (void);
// 0x0000002D T[] UnityEngine.AssetBundle::LoadAllAssets()
// 0x0000002E UnityEngine.Object[] UnityEngine.AssetBundle::LoadAllAssets(System.Type)
extern void AssetBundle_LoadAllAssets_mAB1A820C148FF25D811D16DC5C8F5D39882D480A (void);
// 0x0000002F UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAllAssetsAsync()
extern void AssetBundle_LoadAllAssetsAsync_mFBD03164799EA8A821309FBE8C4C08340943A5E5 (void);
// 0x00000030 UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAllAssetsAsync(System.Type)
extern void AssetBundle_LoadAllAssetsAsync_m213D935C1F40DB2098F00BD852B5799F00542137 (void);
// 0x00000031 UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAssetAsync_Internal(System.String,System.Type)
extern void AssetBundle_LoadAssetAsync_Internal_m89D45EFB48D3C4CF7E481EF41F7ACF24500E7019 (void);
// 0x00000032 System.Void UnityEngine.AssetBundle::Unload(System.Boolean)
extern void AssetBundle_Unload_m0DEBACB284F6CECA8DF21486D1BBE1189F6A5D66 (void);
// 0x00000033 System.String[] UnityEngine.AssetBundle::GetAllAssetNames()
extern void AssetBundle_GetAllAssetNames_m7012B92C4E0BDF3975EBC22DE2515AFEB0E9D409 (void);
// 0x00000034 System.String[] UnityEngine.AssetBundle::GetAllScenePaths()
extern void AssetBundle_GetAllScenePaths_mFE0D5927A3BEEC04BDB62FA436FA9051299B7BC6 (void);
// 0x00000035 UnityEngine.Object[] UnityEngine.AssetBundle::LoadAssetWithSubAssets_Internal(System.String,System.Type)
extern void AssetBundle_LoadAssetWithSubAssets_Internal_m70FFE92F072F934C4121DF328D6C8E1D19D93E51 (void);
// 0x00000036 UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAssetWithSubAssetsAsync_Internal(System.String,System.Type)
extern void AssetBundle_LoadAssetWithSubAssetsAsync_Internal_mD671A7674C5E61F00AF0127C2D92F56DD34E3DCB (void);
// 0x00000037 UnityEngine.AssetBundleRecompressOperation UnityEngine.AssetBundle::RecompressAssetBundleAsync(System.String,System.String,UnityEngine.BuildCompression,System.UInt32,UnityEngine.ThreadPriority)
extern void AssetBundle_RecompressAssetBundleAsync_m18E3EE37DA690233BED8E8E266D3C605BD77D762 (void);
// 0x00000038 UnityEngine.AssetBundleRecompressOperation UnityEngine.AssetBundle::RecompressAssetBundleAsync_Internal(System.String,System.String,UnityEngine.BuildCompression,System.UInt32,UnityEngine.ThreadPriority)
extern void AssetBundle_RecompressAssetBundleAsync_Internal_mEDC6D9885F3E663C7F2B2B57D3C52AD2435BE95A (void);
// 0x00000039 System.UInt32 UnityEngine.AssetBundle::get_memoryBudgetKB()
extern void AssetBundle_get_memoryBudgetKB_m9F3859B7BCBC164757D2F767B2DA2D41F1A3CB3E (void);
// 0x0000003A System.Void UnityEngine.AssetBundle::set_memoryBudgetKB(System.UInt32)
extern void AssetBundle_set_memoryBudgetKB_m8C040A2AD6DD4ACB9EFD541BA54150AEC50BB9F0 (void);
// 0x0000003B UnityEngine.AssetBundleRecompressOperation UnityEngine.AssetBundle::RecompressAssetBundleAsync_Internal_Injected(System.String,System.String,UnityEngine.BuildCompression&,System.UInt32,UnityEngine.ThreadPriority)
extern void AssetBundle_RecompressAssetBundleAsync_Internal_Injected_m3BFD8CEF04345BCD8F54C83993E5F587B9A16402 (void);
// 0x0000003C UnityEngine.AssetBundle UnityEngine.AssetBundleCreateRequest::get_assetBundle()
extern void AssetBundleCreateRequest_get_assetBundle_m608C1516A7DC8E4B1F9D63EDCF6EE8D6C2CFF013 (void);
// 0x0000003D System.Void UnityEngine.AssetBundleCreateRequest::.ctor()
extern void AssetBundleCreateRequest__ctor_m7B04FFC9566D1B0F820DDE9844BA2822A74881B6 (void);
// 0x0000003E System.UInt32 UnityEngine.AssetBundleLoadingCache::get_maxBlocksPerFile()
extern void AssetBundleLoadingCache_get_maxBlocksPerFile_m1103DED6546DF3CABC67E610DA226C367FC6177F (void);
// 0x0000003F System.Void UnityEngine.AssetBundleLoadingCache::set_maxBlocksPerFile(System.UInt32)
extern void AssetBundleLoadingCache_set_maxBlocksPerFile_m9C8BCD2338E2A8E43FEB8541877C81052711EF79 (void);
// 0x00000040 System.UInt32 UnityEngine.AssetBundleLoadingCache::get_blockCount()
extern void AssetBundleLoadingCache_get_blockCount_m1C4097A665614846401024BDF05CE6AF86079962 (void);
// 0x00000041 System.Void UnityEngine.AssetBundleLoadingCache::set_blockCount(System.UInt32)
extern void AssetBundleLoadingCache_set_blockCount_mE518D94AB7139C672039F1B45A124F7B0CC87B98 (void);
// 0x00000042 System.UInt32 UnityEngine.AssetBundleLoadingCache::get_blockSize()
extern void AssetBundleLoadingCache_get_blockSize_mE576EAB7984D4E402963633A1BB85A9464175F5B (void);
// 0x00000043 System.UInt32 UnityEngine.AssetBundleLoadingCache::get_memoryBudgetKB()
extern void AssetBundleLoadingCache_get_memoryBudgetKB_m46A6F2C3142E554E68F9C2F804434938BBC5BBA0 (void);
// 0x00000044 System.Void UnityEngine.AssetBundleLoadingCache::set_memoryBudgetKB(System.UInt32)
extern void AssetBundleLoadingCache_set_memoryBudgetKB_mDBA50BB9E1F49290FB7EACE70C2CD6087C3A1D45 (void);
// 0x00000045 System.String[] UnityEngine.AssetBundleManifest::GetAllAssetBundles()
extern void AssetBundleManifest_GetAllAssetBundles_m88F1FE72F516B548E430E76EC3E75669BDE822FF (void);
// 0x00000046 System.String[] UnityEngine.AssetBundleManifest::GetAllAssetBundlesWithVariant()
extern void AssetBundleManifest_GetAllAssetBundlesWithVariant_m5FA63E47B38707E48A758DF594B4A9E407AA0091 (void);
// 0x00000047 UnityEngine.Hash128 UnityEngine.AssetBundleManifest::GetAssetBundleHash(System.String)
extern void AssetBundleManifest_GetAssetBundleHash_m3FB11557D4B1D4B52F2CF27B4E5BBCA298FFB07A (void);
// 0x00000048 System.String[] UnityEngine.AssetBundleManifest::GetAllDependencies(System.String)
extern void AssetBundleManifest_GetAllDependencies_mF331B5591D058286EA4D5792A5DCA3556E29C033 (void);
// 0x00000049 System.Void UnityEngine.AssetBundleManifest::GetAssetBundleHash_Injected(System.String,UnityEngine.Hash128&)
extern void AssetBundleManifest_GetAssetBundleHash_Injected_m99060916778ACCDF8476EB4B97F095FA93144D58 (void);
// 0x0000004A System.Void UnityEngine.AssetBundleRecompressOperation::.ctor()
extern void AssetBundleRecompressOperation__ctor_m80DDD7E70B5E1A1C4A52E3A3BCB8DC4DA8C37EF7 (void);
// 0x0000004B UnityEngine.Object UnityEngine.AssetBundleRequest::GetResult()
extern void AssetBundleRequest_GetResult_m792F2C703230D18A0B6B18C86636961125B1B9A2 (void);
// 0x0000004C UnityEngine.Object UnityEngine.AssetBundleRequest::get_asset()
extern void AssetBundleRequest_get_asset_mB0A96FBC026D143638E467DEB37228ACD55F1813 (void);
// 0x0000004D System.Void UnityEngine.AssetBundleRequest::.ctor()
extern void AssetBundleRequest__ctor_mD09AF030644EF7F3386ABB3B5C593F61ADE25017 (void);
// 0x0000004E System.Void UnityEngine.BuildCompression::set_compression(UnityEngine.CompressionType)
extern void BuildCompression_set_compression_mC4B164F707B54FB8D8CAFF6BBBE2AF251BCCF462 (void);
// 0x0000004F System.Void UnityEngine.BuildCompression::set_level(UnityEngine.CompressionLevel)
extern void BuildCompression_set_level_m02EF1CDC04A51C053146A6F2FE2889197F751F98 (void);
// 0x00000050 System.Void UnityEngine.BuildCompression::set_blockSize(System.UInt32)
extern void BuildCompression_set_blockSize_mF978E734073EC3863F93A845CD2AF4091B21F8AF (void);
// 0x00000051 System.Void UnityEngine.BuildCompression::.ctor(UnityEngine.CompressionType,UnityEngine.CompressionLevel,System.UInt32)
extern void BuildCompression__ctor_m6A5C664DDDCD0559A817D0C48B7AF10729B00B27 (void);
// 0x00000052 System.Void UnityEngine.BuildCompression::.cctor()
extern void BuildCompression__cctor_mFD1297745EB6A0C83DADF8470F430036802EA81F (void);
static Il2CppMethodPointer s_methodPointers[82] = 
{
	AssetBundle__ctor_mCE6DB7758AAD0EDDB044FC67C5BC7EC987BF3F71,
	AssetBundle_get_mainAsset_mDAF39948DFB48C4C8DA324C11A6E7C9FF2F7F58A,
	AssetBundle_returnMainAsset_mEADFB842D3D38FDAFF6052F93E178302C0A41FD6,
	AssetBundle_UnloadAllAssetBundles_mDC192BDAF0AEA5F6B1E9EFEEE11AB28B105CF521,
	AssetBundle_GetAllLoadedAssetBundles_Native_mA2641B193E24C35A242E8125B49AEDF333E4BDCF,
	AssetBundle_GetAllLoadedAssetBundles_mF192A985FCBCB5C8123A0E0BDF615BBBB7A45179,
	AssetBundle_LoadFromFileAsync_Internal_m7DF7319EA7A245F43B9AC65483A4112CBD169F29,
	AssetBundle_LoadFromFileAsync_mDF90143DECFE5149A8CF7D192E3D7ACBFEBE37D8,
	AssetBundle_LoadFromFileAsync_mCD630216EE5541040DEFF14F4FDC27DB348B42BC,
	AssetBundle_LoadFromFileAsync_mD919D804F62FBF3AF71410E77AC9940205375EA2,
	AssetBundle_LoadFromFile_Internal_mC5A549D462272D3E202D62CF295BDAF44F4E4B7C,
	AssetBundle_LoadFromFile_mE9B8D7BC74F2F67AE710881EFBBB541E8A8F6B3A,
	AssetBundle_LoadFromFile_m4CE03165FB7013C3B3664B082A5B4FD455009DE1,
	AssetBundle_LoadFromFile_m21CB874684AF8C9F77252362CA4E3E43440FF851,
	AssetBundle_LoadFromMemoryAsync_Internal_m3722CC697D35ADA206E6574527A4F6681882D5D4,
	AssetBundle_LoadFromMemoryAsync_m62DAF5E226F11D690E8DF1FC6A32625D7A7A733D,
	AssetBundle_LoadFromMemoryAsync_m75E26133C7B2665F8B0F522A530B2FC6C3A106A9,
	AssetBundle_LoadFromMemory_Internal_mBF6BF6BFDE5DA4A76735766C969591424BB9649A,
	AssetBundle_LoadFromMemory_m435A5FC269478E3E4809AB7355878B166D712E14,
	AssetBundle_LoadFromMemory_m58D3EB0E61463CDF550EA9E9C2F6D962FF16457B,
	AssetBundle_ValidateLoadFromStream_m39F4CB6F1717C48017F09FFC1E537704BB47A5A1,
	AssetBundle_LoadFromStreamAsync_m4891FFAF61A3100FEFC5BDBBA60D2D946A7FBFEB,
	AssetBundle_LoadFromStreamAsync_m750F83983CD25FB7C26E103BFCED35AA04725A97,
	AssetBundle_LoadFromStreamAsync_mC33AE0A0A1C96EDE7F23968397868A45DAFE01F2,
	AssetBundle_LoadFromStream_mB07F4D123259845295A21A4D14D7EC2B7BB647DF,
	AssetBundle_LoadFromStream_m4BCF56F96EE96106529A6D1366EA9C8B633A4895,
	AssetBundle_LoadFromStream_mAE868802265CD014AAB9E8ACABB8F94DF3B6AE84,
	AssetBundle_LoadFromStreamAsyncInternal_m8B64B48FD5D92F632AD5D755C5B9F6B48E5D1F51,
	AssetBundle_LoadFromStreamInternal_m463BAC1F32549D1B4AE88A5185A135FE7676619A,
	AssetBundle_get_isStreamedSceneAssetBundle_m0F0FDECFC88F31CA01E7E36532E186224174A7A4,
	AssetBundle_Contains_m00D431624B6594F6222D946F50A7DCA8559B1B44,
	AssetBundle_LoadAsset_m598BB4E86B07C2BADAE6ED6FFBE5BB5E7A3392D5,
	NULL,
	AssetBundle_LoadAsset_m9139320F8B6D3E43B7D29AA7A60030306AE0A2C6,
	AssetBundle_LoadAsset_Internal_mFB165539087545C4B5763BA8B590D84318C6FE1B,
	AssetBundle_LoadAssetAsync_m033CE66F7C38A5A46C627004B0D3AD830C4CE4F3,
	NULL,
	AssetBundle_LoadAssetAsync_m63C7C5654FA8D0824DC920A1B1530C37CCB3DF6E,
	AssetBundle_LoadAssetWithSubAssets_mB25B0117E71DABE78028CE638F61D6C6C9152DDC,
	NULL,
	AssetBundle_LoadAssetWithSubAssets_mAABE29A8F1719A85DC474932FF12A44B29CACB45,
	AssetBundle_LoadAssetWithSubAssetsAsync_m48EEBA8BAC4B511F44F8B878882E6102D50E6843,
	AssetBundle_LoadAssetWithSubAssetsAsync_m655947AD33D64C1F71F822AEFDE3D2A57C809AF5,
	AssetBundle_LoadAllAssets_mB132E05BAFF4776C59BC80082A2FE68060741603,
	NULL,
	AssetBundle_LoadAllAssets_mAB1A820C148FF25D811D16DC5C8F5D39882D480A,
	AssetBundle_LoadAllAssetsAsync_mFBD03164799EA8A821309FBE8C4C08340943A5E5,
	AssetBundle_LoadAllAssetsAsync_m213D935C1F40DB2098F00BD852B5799F00542137,
	AssetBundle_LoadAssetAsync_Internal_m89D45EFB48D3C4CF7E481EF41F7ACF24500E7019,
	AssetBundle_Unload_m0DEBACB284F6CECA8DF21486D1BBE1189F6A5D66,
	AssetBundle_GetAllAssetNames_m7012B92C4E0BDF3975EBC22DE2515AFEB0E9D409,
	AssetBundle_GetAllScenePaths_mFE0D5927A3BEEC04BDB62FA436FA9051299B7BC6,
	AssetBundle_LoadAssetWithSubAssets_Internal_m70FFE92F072F934C4121DF328D6C8E1D19D93E51,
	AssetBundle_LoadAssetWithSubAssetsAsync_Internal_mD671A7674C5E61F00AF0127C2D92F56DD34E3DCB,
	AssetBundle_RecompressAssetBundleAsync_m18E3EE37DA690233BED8E8E266D3C605BD77D762,
	AssetBundle_RecompressAssetBundleAsync_Internal_mEDC6D9885F3E663C7F2B2B57D3C52AD2435BE95A,
	AssetBundle_get_memoryBudgetKB_m9F3859B7BCBC164757D2F767B2DA2D41F1A3CB3E,
	AssetBundle_set_memoryBudgetKB_m8C040A2AD6DD4ACB9EFD541BA54150AEC50BB9F0,
	AssetBundle_RecompressAssetBundleAsync_Internal_Injected_m3BFD8CEF04345BCD8F54C83993E5F587B9A16402,
	AssetBundleCreateRequest_get_assetBundle_m608C1516A7DC8E4B1F9D63EDCF6EE8D6C2CFF013,
	AssetBundleCreateRequest__ctor_m7B04FFC9566D1B0F820DDE9844BA2822A74881B6,
	AssetBundleLoadingCache_get_maxBlocksPerFile_m1103DED6546DF3CABC67E610DA226C367FC6177F,
	AssetBundleLoadingCache_set_maxBlocksPerFile_m9C8BCD2338E2A8E43FEB8541877C81052711EF79,
	AssetBundleLoadingCache_get_blockCount_m1C4097A665614846401024BDF05CE6AF86079962,
	AssetBundleLoadingCache_set_blockCount_mE518D94AB7139C672039F1B45A124F7B0CC87B98,
	AssetBundleLoadingCache_get_blockSize_mE576EAB7984D4E402963633A1BB85A9464175F5B,
	AssetBundleLoadingCache_get_memoryBudgetKB_m46A6F2C3142E554E68F9C2F804434938BBC5BBA0,
	AssetBundleLoadingCache_set_memoryBudgetKB_mDBA50BB9E1F49290FB7EACE70C2CD6087C3A1D45,
	AssetBundleManifest_GetAllAssetBundles_m88F1FE72F516B548E430E76EC3E75669BDE822FF,
	AssetBundleManifest_GetAllAssetBundlesWithVariant_m5FA63E47B38707E48A758DF594B4A9E407AA0091,
	AssetBundleManifest_GetAssetBundleHash_m3FB11557D4B1D4B52F2CF27B4E5BBCA298FFB07A,
	AssetBundleManifest_GetAllDependencies_mF331B5591D058286EA4D5792A5DCA3556E29C033,
	AssetBundleManifest_GetAssetBundleHash_Injected_m99060916778ACCDF8476EB4B97F095FA93144D58,
	AssetBundleRecompressOperation__ctor_m80DDD7E70B5E1A1C4A52E3A3BCB8DC4DA8C37EF7,
	AssetBundleRequest_GetResult_m792F2C703230D18A0B6B18C86636961125B1B9A2,
	AssetBundleRequest_get_asset_mB0A96FBC026D143638E467DEB37228ACD55F1813,
	AssetBundleRequest__ctor_mD09AF030644EF7F3386ABB3B5C593F61ADE25017,
	BuildCompression_set_compression_mC4B164F707B54FB8D8CAFF6BBBE2AF251BCCF462,
	BuildCompression_set_level_m02EF1CDC04A51C053146A6F2FE2889197F751F98,
	BuildCompression_set_blockSize_mF978E734073EC3863F93A845CD2AF4091B21F8AF,
	BuildCompression__ctor_m6A5C664DDDCD0559A817D0C48B7AF10729B00B27,
	BuildCompression__cctor_mFD1297745EB6A0C83DADF8470F430036802EA81F,
};
extern void BuildCompression_set_compression_mC4B164F707B54FB8D8CAFF6BBBE2AF251BCCF462_AdjustorThunk (void);
extern void BuildCompression_set_level_m02EF1CDC04A51C053146A6F2FE2889197F751F98_AdjustorThunk (void);
extern void BuildCompression_set_blockSize_mF978E734073EC3863F93A845CD2AF4091B21F8AF_AdjustorThunk (void);
extern void BuildCompression__ctor_m6A5C664DDDCD0559A817D0C48B7AF10729B00B27_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[4] = 
{
	{ 0x0600004E, BuildCompression_set_compression_mC4B164F707B54FB8D8CAFF6BBBE2AF251BCCF462_AdjustorThunk },
	{ 0x0600004F, BuildCompression_set_level_m02EF1CDC04A51C053146A6F2FE2889197F751F98_AdjustorThunk },
	{ 0x06000050, BuildCompression_set_blockSize_mF978E734073EC3863F93A845CD2AF4091B21F8AF_AdjustorThunk },
	{ 0x06000051, BuildCompression__ctor_m6A5C664DDDCD0559A817D0C48B7AF10729B00B27_AdjustorThunk },
};
static const int32_t s_InvokerIndices[82] = 
{
	3967,
	3916,
	6091,
	6208,
	6270,
	6270,
	5201,
	6091,
	5604,
	5201,
	5201,
	6091,
	5604,
	5201,
	5604,
	6091,
	5604,
	5604,
	6091,
	5604,
	6218,
	5200,
	5604,
	6091,
	5200,
	5604,
	6091,
	5200,
	5200,
	3863,
	2591,
	3047,
	-1,
	1717,
	1717,
	3047,
	-1,
	1717,
	3047,
	-1,
	1717,
	3047,
	1717,
	3916,
	-1,
	3047,
	3916,
	3047,
	1717,
	3276,
	3916,
	3916,
	1717,
	1717,
	4491,
	4491,
	6284,
	6231,
	4490,
	3916,
	3967,
	6284,
	6231,
	6284,
	6231,
	6284,
	6284,
	6231,
	3916,
	3916,
	2755,
	3047,
	2155,
	3967,
	3916,
	3916,
	3967,
	3313,
	3313,
	3392,
	1284,
	6289,
};
static const Il2CppTokenRangePair s_rgctxIndices[4] = 
{
	{ 0x06000021, { 0, 2 } },
	{ 0x06000025, { 2, 1 } },
	{ 0x06000028, { 3, 2 } },
	{ 0x0600002D, { 5, 2 } },
};
static const Il2CppRGCTXDefinition s_rgctxValues[7] = 
{
	{ (Il2CppRGCTXDataType)1, 65 },
	{ (Il2CppRGCTXDataType)2, 65 },
	{ (Il2CppRGCTXDataType)1, 68 },
	{ (Il2CppRGCTXDataType)2, 5883 },
	{ (Il2CppRGCTXDataType)2, 66 },
	{ (Il2CppRGCTXDataType)1, 67 },
	{ (Il2CppRGCTXDataType)3, 28351 },
};
extern const CustomAttributesCacheGenerator g_UnityEngine_AssetBundleModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_AssetBundleModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_AssetBundleModule_CodeGenModule = 
{
	"UnityEngine.AssetBundleModule.dll",
	82,
	s_methodPointers,
	4,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	4,
	s_rgctxIndices,
	7,
	s_rgctxValues,
	NULL,
	g_UnityEngine_AssetBundleModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

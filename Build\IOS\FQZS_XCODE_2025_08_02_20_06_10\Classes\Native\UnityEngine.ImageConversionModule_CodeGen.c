﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Byte[] UnityEngine.ImageConversion::EncodeToPNG(UnityEngine.Texture2D)
extern void ImageConversion_EncodeToPNG_m016F2AE8469302E2AC6F8D1BECA39F2298E1F6C0 (void);
// 0x00000002 System.Byte[] UnityEngine.ImageConversion::EncodeToJPG(UnityEngine.Texture2D,System.Int32)
extern void ImageConversion_EncodeToJPG_m9F68F7EFADCCE33F43417127B3A671286542B865 (void);
// 0x00000003 System.Byte[] UnityEngine.ImageConversion::EncodeToJPG(UnityEngine.Texture2D)
extern void ImageConversion_EncodeToJPG_mDE9A991A80442770719DD798B503A63F1D099758 (void);
// 0x00000004 System.Boolean UnityEngine.ImageConversion::LoadImage(UnityEngine.Texture2D,System.Byte[],System.Boolean)
extern void ImageConversion_LoadImage_m43087185337C14708DC3CAFD02C4E0D92278AC15 (void);
// 0x00000005 System.Boolean UnityEngine.ImageConversion::LoadImage(UnityEngine.Texture2D,System.Byte[])
extern void ImageConversion_LoadImage_m5DB6AFC9E2157EB82985C340A8D32D15CE6E4B43 (void);
static Il2CppMethodPointer s_methodPointers[5] = 
{
	ImageConversion_EncodeToPNG_m016F2AE8469302E2AC6F8D1BECA39F2298E1F6C0,
	ImageConversion_EncodeToJPG_m9F68F7EFADCCE33F43417127B3A671286542B865,
	ImageConversion_EncodeToJPG_mDE9A991A80442770719DD798B503A63F1D099758,
	ImageConversion_LoadImage_m43087185337C14708DC3CAFD02C4E0D92278AC15,
	ImageConversion_LoadImage_m5DB6AFC9E2157EB82985C340A8D32D15CE6E4B43,
};
static const int32_t s_InvokerIndices[5] = 
{
	6091,
	5595,
	6091,
	5040,
	5433,
};
extern const CustomAttributesCacheGenerator g_UnityEngine_ImageConversionModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_ImageConversionModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_ImageConversionModule_CodeGenModule = 
{
	"UnityEngine.ImageConversionModule.dll",
	5,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_UnityEngine_ImageConversionModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

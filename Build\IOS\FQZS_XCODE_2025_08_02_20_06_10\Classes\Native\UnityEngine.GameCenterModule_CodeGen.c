﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void UnityEngine.SocialPlatforms.ISocialPlatform::Authenticate(UnityEngine.SocialPlatforms.ILocalUser,System.Action`1<System.Boolean>)
// 0x00000002 System.Void UnityEngine.SocialPlatforms.ISocialPlatform::Authenticate(UnityEngine.SocialPlatforms.ILocalUser,System.Action`2<System.Boolean,System.String>)
// 0x00000003 System.Void UnityEngine.SocialPlatforms.ISocialPlatform::LoadFriends(UnityEngine.SocialPlatforms.ILocalUser,System.Action`1<System.Boolean>)
// 0x00000004 System.Boolean UnityEngine.SocialPlatforms.ILocalUser::get_authenticated()
// 0x00000005 System.Void UnityEngine.SocialPlatforms.Range::.ctor(System.Int32,System.Int32)
extern void Range__ctor_mD7BBA6A816E8FBFDDC618308BC5D5882297443F8 (void);
// 0x00000006 System.String UnityEngine.SocialPlatforms.ILeaderboard::get_id()
// 0x00000007 UnityEngine.SocialPlatforms.UserScope UnityEngine.SocialPlatforms.ILeaderboard::get_userScope()
// 0x00000008 UnityEngine.SocialPlatforms.Range UnityEngine.SocialPlatforms.ILeaderboard::get_range()
// 0x00000009 UnityEngine.SocialPlatforms.TimeScope UnityEngine.SocialPlatforms.ILeaderboard::get_timeScope()
// 0x0000000A System.Void UnityEngine.SocialPlatforms.Impl.LocalUser::.ctor()
extern void LocalUser__ctor_mE1D89C27BD5A6DAE982610A87A472E736644605A (void);
// 0x0000000B System.Void UnityEngine.SocialPlatforms.Impl.LocalUser::SetFriends(UnityEngine.SocialPlatforms.IUserProfile[])
extern void LocalUser_SetFriends_m54CD46805170EDB993E87E0A9FA3BF82A910E489 (void);
// 0x0000000C System.Void UnityEngine.SocialPlatforms.Impl.LocalUser::SetAuthenticated(System.Boolean)
extern void LocalUser_SetAuthenticated_mCAFB7406BCB0A184B405E25695F280DA52EFE582 (void);
// 0x0000000D System.Void UnityEngine.SocialPlatforms.Impl.LocalUser::SetUnderage(System.Boolean)
extern void LocalUser_SetUnderage_mA767EE5BC8615E452A6CC57376CA9B9DA601386E (void);
// 0x0000000E System.Boolean UnityEngine.SocialPlatforms.Impl.LocalUser::get_authenticated()
extern void LocalUser_get_authenticated_m7D219E0639A779F4527590E43E66F31C2A482BD1 (void);
// 0x0000000F System.Void UnityEngine.SocialPlatforms.Impl.UserProfile::.ctor()
extern void UserProfile__ctor_m65DBFCC8D74A39E029B7EEBF5B24A8790C3668AA (void);
// 0x00000010 System.Void UnityEngine.SocialPlatforms.Impl.UserProfile::.ctor(System.String,System.String,System.String,System.Boolean,UnityEngine.SocialPlatforms.UserState,UnityEngine.Texture2D)
extern void UserProfile__ctor_m82A71921E766018566AFB09BCE3091C8F1055F39 (void);
// 0x00000011 System.String UnityEngine.SocialPlatforms.Impl.UserProfile::ToString()
extern void UserProfile_ToString_mE86E8A99DF0EE73C93A55624E890806213567924 (void);
// 0x00000012 System.Void UnityEngine.SocialPlatforms.Impl.UserProfile::SetUserName(System.String)
extern void UserProfile_SetUserName_m25580633F0CE2894355406F1F8C86CCEA3E79BBA (void);
// 0x00000013 System.Void UnityEngine.SocialPlatforms.Impl.UserProfile::SetUserID(System.String)
extern void UserProfile_SetUserID_mD1A6F3DEE1C2887A8EDF41444C1D13A2835DF719 (void);
// 0x00000014 System.Void UnityEngine.SocialPlatforms.Impl.UserProfile::SetLegacyUserID(System.String)
extern void UserProfile_SetLegacyUserID_m707552F7298E3A711C41FF8744C8309276E398A4 (void);
// 0x00000015 System.Void UnityEngine.SocialPlatforms.Impl.UserProfile::SetUserGameID(System.String)
extern void UserProfile_SetUserGameID_mF610AEAED6A05E06A24F1C2DC82EC8BD5CAAB4E0 (void);
// 0x00000016 System.Void UnityEngine.SocialPlatforms.Impl.UserProfile::SetImage(UnityEngine.Texture2D)
extern void UserProfile_SetImage_m756E17B9BFE41D572C76A506E2BDA81A2D7D9605 (void);
// 0x00000017 System.String UnityEngine.SocialPlatforms.Impl.UserProfile::get_userName()
extern void UserProfile_get_userName_mB127337E5455C94299C39FB66C2965FF3421142E (void);
// 0x00000018 System.String UnityEngine.SocialPlatforms.Impl.UserProfile::get_id()
extern void UserProfile_get_id_m2C9612A3B81A7F76F86C4FE87AD4D3FADEF3E385 (void);
// 0x00000019 System.Boolean UnityEngine.SocialPlatforms.Impl.UserProfile::get_isFriend()
extern void UserProfile_get_isFriend_m7ACC8963AD01FB384D215AF8C215DD93CB5322EA (void);
// 0x0000001A UnityEngine.SocialPlatforms.UserState UnityEngine.SocialPlatforms.Impl.UserProfile::get_state()
extern void UserProfile_get_state_mF93F969F5923A0A3528923E5A452138D00D4E248 (void);
// 0x0000001B System.Void UnityEngine.SocialPlatforms.Impl.Achievement::.ctor(System.String,System.Double,System.Boolean,System.Boolean,System.DateTime)
extern void Achievement__ctor_m6BFE0E7DB18B35A485855AC3B1C0D4E25AB56C0C (void);
// 0x0000001C System.Void UnityEngine.SocialPlatforms.Impl.Achievement::.ctor(System.String,System.Double)
extern void Achievement__ctor_m8E491BFB569F645FC9BF4CC8F58F5718C54901E0 (void);
// 0x0000001D System.Void UnityEngine.SocialPlatforms.Impl.Achievement::.ctor()
extern void Achievement__ctor_m570954380BDC68F860E558D037369D03269C72A4 (void);
// 0x0000001E System.String UnityEngine.SocialPlatforms.Impl.Achievement::ToString()
extern void Achievement_ToString_m443278D0F151B7B9BAF041785378EFEAC98E6052 (void);
// 0x0000001F System.String UnityEngine.SocialPlatforms.Impl.Achievement::get_id()
extern void Achievement_get_id_m39931C0B44D8A4948E12527FDE77C9211028267B (void);
// 0x00000020 System.Void UnityEngine.SocialPlatforms.Impl.Achievement::set_id(System.String)
extern void Achievement_set_id_m16A4091CBC207FB287FEFF608C5F45D9D21106A0 (void);
// 0x00000021 System.Double UnityEngine.SocialPlatforms.Impl.Achievement::get_percentCompleted()
extern void Achievement_get_percentCompleted_m864BBAA8D955D724A506E62F6741B52D43F38F9D (void);
// 0x00000022 System.Void UnityEngine.SocialPlatforms.Impl.Achievement::set_percentCompleted(System.Double)
extern void Achievement_set_percentCompleted_mD433F22DBB60B94FD45CC2A73C9A22C93B0CB9CB (void);
// 0x00000023 System.Boolean UnityEngine.SocialPlatforms.Impl.Achievement::get_completed()
extern void Achievement_get_completed_m26C3793203BC368F6A44678FEB4E8B94976DC66F (void);
// 0x00000024 System.Boolean UnityEngine.SocialPlatforms.Impl.Achievement::get_hidden()
extern void Achievement_get_hidden_m732D2ED47F705384C1A8CA28E40AADE16944CD11 (void);
// 0x00000025 System.DateTime UnityEngine.SocialPlatforms.Impl.Achievement::get_lastReportedDate()
extern void Achievement_get_lastReportedDate_m0C90E0EB0392E57E70D62D2FB182527613BB285C (void);
// 0x00000026 System.Void UnityEngine.SocialPlatforms.Impl.AchievementDescription::.ctor(System.String,System.String,UnityEngine.Texture2D,System.String,System.String,System.Boolean,System.Int32)
extern void AchievementDescription__ctor_m5EA1DE335B7A97FCDE944FBFD2B979D870D3F37A (void);
// 0x00000027 System.String UnityEngine.SocialPlatforms.Impl.AchievementDescription::ToString()
extern void AchievementDescription_ToString_m48909B3814359ED0FAF88F29F3B80C024F0C5D36 (void);
// 0x00000028 System.Void UnityEngine.SocialPlatforms.Impl.AchievementDescription::SetImage(UnityEngine.Texture2D)
extern void AchievementDescription_SetImage_mB1653A512095EE6A795031B009513071A559C248 (void);
// 0x00000029 System.String UnityEngine.SocialPlatforms.Impl.AchievementDescription::get_id()
extern void AchievementDescription_get_id_mD5FEE2DBF0C88887C99E3EDCD9BC65C32EFBAD4A (void);
// 0x0000002A System.Void UnityEngine.SocialPlatforms.Impl.AchievementDescription::set_id(System.String)
extern void AchievementDescription_set_id_m61C4DA5B9AD3353518524A8E34723CE7836D432E (void);
// 0x0000002B System.String UnityEngine.SocialPlatforms.Impl.AchievementDescription::get_title()
extern void AchievementDescription_get_title_mF4E46C1A1BBF0087858BE6046604D00DB130E81F (void);
// 0x0000002C System.String UnityEngine.SocialPlatforms.Impl.AchievementDescription::get_achievedDescription()
extern void AchievementDescription_get_achievedDescription_m4901EFA817B61139B1A5284CAF14378A531B7537 (void);
// 0x0000002D System.String UnityEngine.SocialPlatforms.Impl.AchievementDescription::get_unachievedDescription()
extern void AchievementDescription_get_unachievedDescription_m90706792FD9A6FD0BC4975E41E531F91AEC3F33C (void);
// 0x0000002E System.Boolean UnityEngine.SocialPlatforms.Impl.AchievementDescription::get_hidden()
extern void AchievementDescription_get_hidden_m8FDE6EBDC27F05F8D04B53B4F044B5B06F2EDA0A (void);
// 0x0000002F System.Int32 UnityEngine.SocialPlatforms.Impl.AchievementDescription::get_points()
extern void AchievementDescription_get_points_mCBD1A433E28AC73B7AECBE3E41FFDB6B335031AF (void);
// 0x00000030 System.Void UnityEngine.SocialPlatforms.Impl.Score::.ctor(System.String,System.Int64)
extern void Score__ctor_m88494757EDE00B4CA1BE7027A1F84C82E09EC958 (void);
// 0x00000031 System.Void UnityEngine.SocialPlatforms.Impl.Score::.ctor(System.String,System.Int64,System.String,System.DateTime,System.String,System.Int32)
extern void Score__ctor_m94026C33B1E9EE0F31614511DB8F512E8A7A4FE3 (void);
// 0x00000032 System.String UnityEngine.SocialPlatforms.Impl.Score::ToString()
extern void Score_ToString_mBE1B8E8846C633AAA58A0680C888F21915454FC4 (void);
// 0x00000033 System.String UnityEngine.SocialPlatforms.Impl.Score::get_leaderboardID()
extern void Score_get_leaderboardID_m079BDC75AED9D8C06DC36F78AC233171F023FB4F (void);
// 0x00000034 System.Void UnityEngine.SocialPlatforms.Impl.Score::set_leaderboardID(System.String)
extern void Score_set_leaderboardID_m05255F12ADDC2FC6D7E9646CBAE68D2924E66F65 (void);
// 0x00000035 System.Int64 UnityEngine.SocialPlatforms.Impl.Score::get_value()
extern void Score_get_value_m750646EDC0619A11179177F597C21374F13FCB9E (void);
// 0x00000036 System.Void UnityEngine.SocialPlatforms.Impl.Score::set_value(System.Int64)
extern void Score_set_value_mFECF5E9D1928EEAE7E46F497CDA159549EEB74C4 (void);
// 0x00000037 System.Void UnityEngine.SocialPlatforms.Impl.Leaderboard::.ctor()
extern void Leaderboard__ctor_mC09955DEF2101303E698FC13E1BBFFA90B3365F4 (void);
// 0x00000038 System.String UnityEngine.SocialPlatforms.Impl.Leaderboard::ToString()
extern void Leaderboard_ToString_mE59DCCF117736269AF0C6CD21876DB3448BD14ED (void);
// 0x00000039 System.Void UnityEngine.SocialPlatforms.Impl.Leaderboard::SetLocalUserScore(UnityEngine.SocialPlatforms.IScore)
extern void Leaderboard_SetLocalUserScore_m17939BC032AC6FDCC1152234807C50E97A72D600 (void);
// 0x0000003A System.Void UnityEngine.SocialPlatforms.Impl.Leaderboard::SetMaxRange(System.UInt32)
extern void Leaderboard_SetMaxRange_m5D699FC22BC5BED04386C4B6D4661663B49FD3B4 (void);
// 0x0000003B System.Void UnityEngine.SocialPlatforms.Impl.Leaderboard::SetScores(UnityEngine.SocialPlatforms.IScore[])
extern void Leaderboard_SetScores_m2DB8C9FB943AD849B23FCEB56D76FAE4E11C2084 (void);
// 0x0000003C System.Void UnityEngine.SocialPlatforms.Impl.Leaderboard::SetTitle(System.String)
extern void Leaderboard_SetTitle_m5FE6EFFE4099E7BA2B7F1F6644B0A9E1C5A93627 (void);
// 0x0000003D System.String[] UnityEngine.SocialPlatforms.Impl.Leaderboard::GetUserFilter()
extern void Leaderboard_GetUserFilter_m229A294CB0ED73326836FABAC438E517F6517550 (void);
// 0x0000003E System.String UnityEngine.SocialPlatforms.Impl.Leaderboard::get_id()
extern void Leaderboard_get_id_m4A262BB19BCACE6C9B19874F5D68C777846C6CD6 (void);
// 0x0000003F System.Void UnityEngine.SocialPlatforms.Impl.Leaderboard::set_id(System.String)
extern void Leaderboard_set_id_mC863AFBCE6B992935BCE9E8B567CDA40CCD94B42 (void);
// 0x00000040 UnityEngine.SocialPlatforms.UserScope UnityEngine.SocialPlatforms.Impl.Leaderboard::get_userScope()
extern void Leaderboard_get_userScope_m8304E736582482D0E5705C109D95F0CEDF2EE54C (void);
// 0x00000041 System.Void UnityEngine.SocialPlatforms.Impl.Leaderboard::set_userScope(UnityEngine.SocialPlatforms.UserScope)
extern void Leaderboard_set_userScope_m66BBCC760CDAF1C44639BD10E4D901EFD7C3F778 (void);
// 0x00000042 UnityEngine.SocialPlatforms.Range UnityEngine.SocialPlatforms.Impl.Leaderboard::get_range()
extern void Leaderboard_get_range_mB9C6C91869423F791E017B71FCDB17A354585519 (void);
// 0x00000043 System.Void UnityEngine.SocialPlatforms.Impl.Leaderboard::set_range(UnityEngine.SocialPlatforms.Range)
extern void Leaderboard_set_range_m29352D47548D750E0E42EC9627C861C69D084444 (void);
// 0x00000044 UnityEngine.SocialPlatforms.TimeScope UnityEngine.SocialPlatforms.Impl.Leaderboard::get_timeScope()
extern void Leaderboard_get_timeScope_mE1F4CBEBE4B2E1E6BD245E3BE518680F13BBF276 (void);
// 0x00000045 System.Void UnityEngine.SocialPlatforms.Impl.Leaderboard::set_timeScope(UnityEngine.SocialPlatforms.TimeScope)
extern void Leaderboard_set_timeScope_m36122610C955D05BD7FC713415CA78076986B901 (void);
// 0x00000046 UnityEngine.SocialPlatforms.Impl.UserProfile UnityEngine.SocialPlatforms.GameCenter.GcUserProfileData::ToUserProfile()
extern void GcUserProfileData_ToUserProfile_m500A5A38D89D400333D9ED8D2005449AFEC91F24 (void);
// 0x00000047 System.Void UnityEngine.SocialPlatforms.GameCenter.GcUserProfileData::AddToArray(UnityEngine.SocialPlatforms.Impl.UserProfile[]&,System.Int32)
extern void GcUserProfileData_AddToArray_mA189B901FEF32E5CA9AF7DEB58D8143E87E41EC2 (void);
// 0x00000048 UnityEngine.SocialPlatforms.Impl.AchievementDescription UnityEngine.SocialPlatforms.GameCenter.GcAchievementDescriptionData::ToAchievementDescription()
extern void GcAchievementDescriptionData_ToAchievementDescription_m9942E64237B72699916331A3EED62FC8E0DB8B86 (void);
// 0x00000049 UnityEngine.SocialPlatforms.Impl.Achievement UnityEngine.SocialPlatforms.GameCenter.GcAchievementData::ToAchievement()
extern void GcAchievementData_ToAchievement_mA3EFDC224A485DA99E1ED1CC90DEEB2CB68B539E (void);
// 0x0000004A UnityEngine.SocialPlatforms.Impl.Score UnityEngine.SocialPlatforms.GameCenter.GcScoreData::ToScore()
extern void GcScoreData_ToScore_m6B29510CCE935E2A67EAFC653CB4A0A4B8B32B49 (void);
// 0x0000004B System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::ClearAchievementDescriptions(System.Int32)
extern void GameCenterPlatform_ClearAchievementDescriptions_m656409DDC86BC2426E26E73244AAE1CFDF3FB893 (void);
// 0x0000004C System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::SetAchievementDescription(UnityEngine.SocialPlatforms.GameCenter.GcAchievementDescriptionData,System.Int32)
extern void GameCenterPlatform_SetAchievementDescription_m54037323FA3D723CAEC0B6B3849F2567CAB56E6B (void);
// 0x0000004D System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::SetAchievementDescriptionImage(UnityEngine.Texture2D,System.Int32)
extern void GameCenterPlatform_SetAchievementDescriptionImage_m23317B6438DB1783760514ABC1882DCBA76FED29 (void);
// 0x0000004E System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::TriggerAchievementDescriptionCallback(System.Action`1<UnityEngine.SocialPlatforms.IAchievementDescription[]>)
extern void GameCenterPlatform_TriggerAchievementDescriptionCallback_m3E49388CCC8EF048838C1020E8A7B82E8DC9865E (void);
// 0x0000004F System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::AuthenticateCallbackWrapper(System.Int32,System.String)
extern void GameCenterPlatform_AuthenticateCallbackWrapper_m0607EC8652F4831D78B922D9D01F04245DC033D6 (void);
// 0x00000050 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::ClearFriends(System.Int32)
extern void GameCenterPlatform_ClearFriends_mBC97C1F1ABA638B34B1F9AED1A0EB2911B9405C1 (void);
// 0x00000051 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::SetFriends(UnityEngine.SocialPlatforms.GameCenter.GcUserProfileData,System.Int32)
extern void GameCenterPlatform_SetFriends_m3E79AC6C0E5FD2319A3CADBC24FBBFE4D6238300 (void);
// 0x00000052 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::SetFriendImage(UnityEngine.Texture2D,System.Int32)
extern void GameCenterPlatform_SetFriendImage_m522525502B10C15F1147FEE893F3B60942998F03 (void);
// 0x00000053 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::TriggerFriendsCallbackWrapper(System.Action`1<System.Boolean>,System.Int32)
extern void GameCenterPlatform_TriggerFriendsCallbackWrapper_mFA57C0C3621E2E44CB38BCB006780B81CBB31443 (void);
// 0x00000054 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::AchievementCallbackWrapper(System.Action`1<UnityEngine.SocialPlatforms.IAchievement[]>,UnityEngine.SocialPlatforms.GameCenter.GcAchievementData[])
extern void GameCenterPlatform_AchievementCallbackWrapper_m1257FE98EE0E742DB4FBDA5176D0B844412CDB27 (void);
// 0x00000055 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::ProgressCallbackWrapper(System.Action`1<System.Boolean>,System.Boolean)
extern void GameCenterPlatform_ProgressCallbackWrapper_m4026F09939C2203AD1EE00F72F2F6EAFCEE79AA5 (void);
// 0x00000056 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::ScoreCallbackWrapper(System.Action`1<System.Boolean>,System.Boolean)
extern void GameCenterPlatform_ScoreCallbackWrapper_m3FCDF12166DEC51D2B5A770EFDA47AFDABA45810 (void);
// 0x00000057 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::ScoreLoaderCallbackWrapper(System.Action`1<UnityEngine.SocialPlatforms.IScore[]>,UnityEngine.SocialPlatforms.GameCenter.GcScoreData[])
extern void GameCenterPlatform_ScoreLoaderCallbackWrapper_m5D838BB12756DB8AF4561E333A6B21E19C4DAD3C (void);
// 0x00000058 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::UnityEngine.SocialPlatforms.ISocialPlatform.LoadFriends(UnityEngine.SocialPlatforms.ILocalUser,System.Action`1<System.Boolean>)
extern void GameCenterPlatform_UnityEngine_SocialPlatforms_ISocialPlatform_LoadFriends_m1DFD532E0674BB256884F22C83495D9FFBB66CB0 (void);
// 0x00000059 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::UnityEngine.SocialPlatforms.ISocialPlatform.Authenticate(UnityEngine.SocialPlatforms.ILocalUser,System.Action`1<System.Boolean>)
extern void GameCenterPlatform_UnityEngine_SocialPlatforms_ISocialPlatform_Authenticate_m1906287A9BD6679297200FD68303DA2CA9BB4EFB (void);
// 0x0000005A System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::UnityEngine.SocialPlatforms.ISocialPlatform.Authenticate(UnityEngine.SocialPlatforms.ILocalUser,System.Action`2<System.Boolean,System.String>)
extern void GameCenterPlatform_UnityEngine_SocialPlatforms_ISocialPlatform_Authenticate_m3ACFDDBE3CD2501B919983BEDF89463CE33572BE (void);
// 0x0000005B UnityEngine.SocialPlatforms.ILocalUser UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::get_localUser()
extern void GameCenterPlatform_get_localUser_m7E985071FAA0B7393A5B2A056917DF06B41BD0A4 (void);
// 0x0000005C System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::PopulateLocalUser()
extern void GameCenterPlatform_PopulateLocalUser_m67257D02ABCAB80407FD27BC218AB5D2A5212D1A (void);
// 0x0000005D System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::LoadAchievementDescriptions(System.Action`1<UnityEngine.SocialPlatforms.IAchievementDescription[]>)
extern void GameCenterPlatform_LoadAchievementDescriptions_m72A5167A9328B903358163FC83A800B9FCEB9AD6 (void);
// 0x0000005E System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::ReportProgress(System.String,System.Double,System.Action`1<System.Boolean>)
extern void GameCenterPlatform_ReportProgress_mD284779838DA024C5CA4EC40D1B2ECA25569D5C0 (void);
// 0x0000005F System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::LoadAchievements(System.Action`1<UnityEngine.SocialPlatforms.IAchievement[]>)
extern void GameCenterPlatform_LoadAchievements_m63737CE0EF90F20A31DD1F43EE3D39D79836DF7F (void);
// 0x00000060 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::ReportScore(System.Int64,System.String,System.Action`1<System.Boolean>)
extern void GameCenterPlatform_ReportScore_m2ACB935864BC415729EF552832518FA3FF890171 (void);
// 0x00000061 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::LoadScores(System.String,System.Action`1<UnityEngine.SocialPlatforms.IScore[]>)
extern void GameCenterPlatform_LoadScores_mC191ABA1B69A13CC88A44AF25BD89E1D289CBF52 (void);
// 0x00000062 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::LoadScores(UnityEngine.SocialPlatforms.ILeaderboard,System.Action`1<System.Boolean>)
extern void GameCenterPlatform_LoadScores_m54BF2C6FAB4A19BC80B168F3BB985BDBAFA96A1B (void);
// 0x00000063 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::LeaderboardCallbackWrapper(System.Action`1<System.Boolean>,System.Boolean)
extern void GameCenterPlatform_LeaderboardCallbackWrapper_mADA9228337DBFC03A16D3D9E290582B509E01454 (void);
// 0x00000064 System.Boolean UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::GetLoading(UnityEngine.SocialPlatforms.ILeaderboard)
extern void GameCenterPlatform_GetLoading_m06F9588A6143126F03D9C0AAEAA853EC1C9FC3C1 (void);
// 0x00000065 System.Boolean UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::VerifyAuthentication()
extern void GameCenterPlatform_VerifyAuthentication_m9605DB240711AB73F461074D8B93F195E894554E (void);
// 0x00000066 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::ShowAchievementsUI()
extern void GameCenterPlatform_ShowAchievementsUI_m425068FBB95E73A5E2C34A8045394F9583F27728 (void);
// 0x00000067 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::ShowLeaderboardUI()
extern void GameCenterPlatform_ShowLeaderboardUI_m25410C4C825818ABC415F3231AFD4C0B3D80100C (void);
// 0x00000068 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::ClearUsers(System.Int32)
extern void GameCenterPlatform_ClearUsers_m04659D6A21590E9DC875EB48A7F821CD809232B7 (void);
// 0x00000069 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::SetUser(UnityEngine.SocialPlatforms.GameCenter.GcUserProfileData,System.Int32)
extern void GameCenterPlatform_SetUser_mA06041F15EDF1E8B2232B2C97D3E0CE2168CCF63 (void);
// 0x0000006A System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::SetUserImage(UnityEngine.Texture2D,System.Int32)
extern void GameCenterPlatform_SetUserImage_m5D1525B67471D5A1705CC459A70D816BA6454CF8 (void);
// 0x0000006B System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::TriggerUsersCallbackWrapper(System.Action`1<UnityEngine.SocialPlatforms.IUserProfile[]>)
extern void GameCenterPlatform_TriggerUsersCallbackWrapper_m003A05DCFF29307CA093E8982AF4FFA9573BDDD1 (void);
// 0x0000006C System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::LoadUsers(System.String[],System.Action`1<UnityEngine.SocialPlatforms.IUserProfile[]>)
extern void GameCenterPlatform_LoadUsers_m10FB18D65C6C997E1AB6F0BEB27891B0C1A4923C (void);
// 0x0000006D System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::SafeSetUserImage(UnityEngine.SocialPlatforms.Impl.UserProfile[]&,UnityEngine.Texture2D,System.Int32)
extern void GameCenterPlatform_SafeSetUserImage_mFD5E65D092CF5DAE75E5120CFA6F00E85F2573C6 (void);
// 0x0000006E System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::SafeClearArray(UnityEngine.SocialPlatforms.Impl.UserProfile[]&,System.Int32)
extern void GameCenterPlatform_SafeClearArray_m51942DE4C82E5F7510632DB5792D4CD95DC29FAC (void);
// 0x0000006F UnityEngine.SocialPlatforms.ILeaderboard UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::CreateLeaderboard()
extern void GameCenterPlatform_CreateLeaderboard_mC2B336F8C9569B3FD7059ED7861B208DD44B0EAB (void);
// 0x00000070 UnityEngine.SocialPlatforms.IAchievement UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::CreateAchievement()
extern void GameCenterPlatform_CreateAchievement_mC8BE646E6BB63CCF284C62FABEE712BF7590DB1A (void);
// 0x00000071 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::TriggerResetAchievementCallback(System.Boolean)
extern void GameCenterPlatform_TriggerResetAchievementCallback_m6AFB9EF2A78FB3B06BCB74A53C8B419840997997 (void);
// 0x00000072 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::Authenticate()
extern void GameCenterPlatform_Authenticate_mC8451B5E84D0D178EE6B66D3B526DC0ADE7FA87E (void);
// 0x00000073 System.Boolean UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::GetAuthenticated()
extern void GameCenterPlatform_GetAuthenticated_m9F50127C839BB750231C452B6950C8E2064284B0 (void);
// 0x00000074 System.String UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::Internal_UserName()
extern void GameCenterPlatform_Internal_UserName_mC50BC991DC325E7322D29DB1F1CD0A7BC1CF4EAE (void);
// 0x00000075 System.String UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::Internal_UserID()
extern void GameCenterPlatform_Internal_UserID_m240D2694A438AC48E8CE9BEABA49D2E73147F508 (void);
// 0x00000076 System.String UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::Internal_UserGameID()
extern void GameCenterPlatform_Internal_UserGameID_m13CFCE0B6A926BF5C2A4A06B78ED327567C3A3C1 (void);
// 0x00000077 System.String UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::Internal_LegacyUserID()
extern void GameCenterPlatform_Internal_LegacyUserID_m04084AEA289328791C394BAB9F04A85B8C77C031 (void);
// 0x00000078 System.Boolean UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::GetIsUnderage()
extern void GameCenterPlatform_GetIsUnderage_m025892C5F978C9E0900CE7727D3C5AA8F5C2B59C (void);
// 0x00000079 UnityEngine.Texture2D UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::GetUserImage()
extern void GameCenterPlatform_GetUserImage_mD9238183C0922B79959BAD621E9E4E6CFD306C09 (void);
// 0x0000007A System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::LoadFriends(System.Object)
extern void GameCenterPlatform_LoadFriends_mC66BFB8FDD72BD19FB58B616A13F327F68DA9190 (void);
// 0x0000007B System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::InternalLoadAchievementDescriptions(System.Object)
extern void GameCenterPlatform_InternalLoadAchievementDescriptions_m9B65A5F58A40E18D318A896433746D2D1948EC56 (void);
// 0x0000007C System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::InternalLoadAchievements(System.Object)
extern void GameCenterPlatform_InternalLoadAchievements_m97F2BBFEE314065ABB004C34D13494EC83F56B3A (void);
// 0x0000007D System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::InternalReportProgress(System.String,System.Double,System.Object)
extern void GameCenterPlatform_InternalReportProgress_mA38127444F4F1412E442D42B3164413B5F56DF0A (void);
// 0x0000007E System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::InternalReportScore(System.Int64,System.String,System.Object)
extern void GameCenterPlatform_InternalReportScore_m7E4444621D68C1BB352F110E86806609F6C69666 (void);
// 0x0000007F System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::InternalLoadScores(System.String,System.Object)
extern void GameCenterPlatform_InternalLoadScores_m8A6891634F5469F218E87AF058854F0BED83004C (void);
// 0x00000080 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::Internal_ShowAchievementsUI()
extern void GameCenterPlatform_Internal_ShowAchievementsUI_mAEADFB3EB3302DCBB59271EC3E788805BD7BF5B6 (void);
// 0x00000081 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::Internal_ShowLeaderboardUI()
extern void GameCenterPlatform_Internal_ShowLeaderboardUI_mB7FC414D67ABFD80CEE868581B736ECC4C2E7C91 (void);
// 0x00000082 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::Internal_LoadUsers(System.String[],System.Object)
extern void GameCenterPlatform_Internal_LoadUsers_m9D0013A4B08DA4F9BE19BFB45ECE404872D17C27 (void);
// 0x00000083 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::ResetAllAchievements()
extern void GameCenterPlatform_ResetAllAchievements_mD43B366C4266A485E227DC8F8C2EA69B2104E6D7 (void);
// 0x00000084 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::ShowDefaultAchievementBanner(System.Boolean)
extern void GameCenterPlatform_ShowDefaultAchievementBanner_m257ECCCFE7D2AFBC693BC2B290C74F44C9CD627F (void);
// 0x00000085 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::ResetAllAchievements(System.Action`1<System.Boolean>)
extern void GameCenterPlatform_ResetAllAchievements_m4EC3D1A47A0D31FA58A6CF4A82D078C7F1C6F2C9 (void);
// 0x00000086 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::ShowDefaultAchievementCompletionBanner(System.Boolean)
extern void GameCenterPlatform_ShowDefaultAchievementCompletionBanner_m25F389BEC1413C7AFA3C9886D32BB5ABF276409C (void);
// 0x00000087 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::ShowLeaderboardUI(System.String,UnityEngine.SocialPlatforms.TimeScope)
extern void GameCenterPlatform_ShowLeaderboardUI_m9F69D292F83FF87F761CEE71F56631186A06EFCA (void);
// 0x00000088 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::ShowSpecificLeaderboardUI(System.String,System.Int32)
extern void GameCenterPlatform_ShowSpecificLeaderboardUI_mC368775E6A25D686CA5FBB8A5C8EC27257F2B842 (void);
// 0x00000089 System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::.ctor()
extern void GameCenterPlatform__ctor_m12106EF272A85BC20F2CA464C0A92DF42CAB375A (void);
// 0x0000008A System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform::.cctor()
extern void GameCenterPlatform__cctor_m9905CEACFF099D1CEA8B9CFDD48E2DE36EF97C12 (void);
// 0x0000008B System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform/<>c__DisplayClass21_0::.ctor()
extern void U3CU3Ec__DisplayClass21_0__ctor_mEAC1B8E5722EF1563CE164CE0A2C2EF061A3B65B (void);
// 0x0000008C System.Void UnityEngine.SocialPlatforms.GameCenter.GameCenterPlatform/<>c__DisplayClass21_0::<UnityEngine.SocialPlatforms.ISocialPlatform.Authenticate>b__0(System.Boolean,System.String)
extern void U3CU3Ec__DisplayClass21_0_U3CUnityEngine_SocialPlatforms_ISocialPlatform_AuthenticateU3Eb__0_m4B82E8613D69236F6B072A688C556241D1CA9E5C (void);
// 0x0000008D System.Void UnityEngine.SocialPlatforms.GameCenter.GcLeaderboard::.ctor(UnityEngine.SocialPlatforms.Impl.Leaderboard)
extern void GcLeaderboard__ctor_mC6DCBFD1A415763A634A19381C936254D8B2F4E7 (void);
// 0x0000008E System.Void UnityEngine.SocialPlatforms.GameCenter.GcLeaderboard::Finalize()
extern void GcLeaderboard_Finalize_m0DA6F9152780D5EFC4EE884E5CAA6A469CE81E2A (void);
// 0x0000008F System.Boolean UnityEngine.SocialPlatforms.GameCenter.GcLeaderboard::Contains(UnityEngine.SocialPlatforms.Impl.Leaderboard)
extern void GcLeaderboard_Contains_m599B4319BA3FD351DC157B5E23D0489D8186279C (void);
// 0x00000090 System.Void UnityEngine.SocialPlatforms.GameCenter.GcLeaderboard::SetScores(UnityEngine.SocialPlatforms.GameCenter.GcScoreData[])
extern void GcLeaderboard_SetScores_m0FE1B289E42EAF2415193FFA1BEAE3FE38AA6B4B (void);
// 0x00000091 System.Void UnityEngine.SocialPlatforms.GameCenter.GcLeaderboard::SetLocalScore(UnityEngine.SocialPlatforms.GameCenter.GcScoreData)
extern void GcLeaderboard_SetLocalScore_mB3EB59503BACFA37E9F112DB5E1906174C67AF32 (void);
// 0x00000092 System.Void UnityEngine.SocialPlatforms.GameCenter.GcLeaderboard::SetMaxRange(System.UInt32)
extern void GcLeaderboard_SetMaxRange_m9926B80DD3377EF7798718F0FD57587DADE4DC31 (void);
// 0x00000093 System.Void UnityEngine.SocialPlatforms.GameCenter.GcLeaderboard::SetTitle(System.String)
extern void GcLeaderboard_SetTitle_m964511DCF53C69F7208CDC1DFD68885E2BCECF2A (void);
// 0x00000094 System.Void UnityEngine.SocialPlatforms.GameCenter.GcLeaderboard::Internal_LoadScores(System.String,System.Int32,System.Int32,System.String[],System.Int32,System.Int32,System.Object)
extern void GcLeaderboard_Internal_LoadScores_mDFC3A947F5DC0FC7EF83B6C5B605D93BC959675F (void);
// 0x00000095 System.IntPtr UnityEngine.SocialPlatforms.GameCenter.GcLeaderboard::GcLeaderboard_LoadScores(System.Object,System.String,System.Int32,System.Int32,System.String[],System.Int32,System.Int32,System.Object)
extern void GcLeaderboard_GcLeaderboard_LoadScores_m341D807EC732903746586EEF57958884952E8E04 (void);
// 0x00000096 System.Boolean UnityEngine.SocialPlatforms.GameCenter.GcLeaderboard::Loading()
extern void GcLeaderboard_Loading_m825936D6AB51CC355DAB8DAAF6177AC4ABBC78B2 (void);
// 0x00000097 System.Boolean UnityEngine.SocialPlatforms.GameCenter.GcLeaderboard::GcLeaderboard_Loading(System.IntPtr)
extern void GcLeaderboard_GcLeaderboard_Loading_m632F32F44B1E4383BD5E24767A2A4CC96CA01C92 (void);
// 0x00000098 System.Void UnityEngine.SocialPlatforms.GameCenter.GcLeaderboard::Dispose()
extern void GcLeaderboard_Dispose_m54F8C8544A7F65898B6444B3EE11916153C20B6F (void);
// 0x00000099 System.Void UnityEngine.SocialPlatforms.GameCenter.GcLeaderboard::GcLeaderboard_Dispose(System.IntPtr)
extern void GcLeaderboard_GcLeaderboard_Dispose_mC316EE2638A7A84A3FCBDE8EBC1AB42A758BAE5E (void);
static Il2CppMethodPointer s_methodPointers[153] = 
{
	NULL,
	NULL,
	NULL,
	NULL,
	Range__ctor_mD7BBA6A816E8FBFDDC618308BC5D5882297443F8,
	NULL,
	NULL,
	NULL,
	NULL,
	LocalUser__ctor_mE1D89C27BD5A6DAE982610A87A472E736644605A,
	LocalUser_SetFriends_m54CD46805170EDB993E87E0A9FA3BF82A910E489,
	LocalUser_SetAuthenticated_mCAFB7406BCB0A184B405E25695F280DA52EFE582,
	LocalUser_SetUnderage_mA767EE5BC8615E452A6CC57376CA9B9DA601386E,
	LocalUser_get_authenticated_m7D219E0639A779F4527590E43E66F31C2A482BD1,
	UserProfile__ctor_m65DBFCC8D74A39E029B7EEBF5B24A8790C3668AA,
	UserProfile__ctor_m82A71921E766018566AFB09BCE3091C8F1055F39,
	UserProfile_ToString_mE86E8A99DF0EE73C93A55624E890806213567924,
	UserProfile_SetUserName_m25580633F0CE2894355406F1F8C86CCEA3E79BBA,
	UserProfile_SetUserID_mD1A6F3DEE1C2887A8EDF41444C1D13A2835DF719,
	UserProfile_SetLegacyUserID_m707552F7298E3A711C41FF8744C8309276E398A4,
	UserProfile_SetUserGameID_mF610AEAED6A05E06A24F1C2DC82EC8BD5CAAB4E0,
	UserProfile_SetImage_m756E17B9BFE41D572C76A506E2BDA81A2D7D9605,
	UserProfile_get_userName_mB127337E5455C94299C39FB66C2965FF3421142E,
	UserProfile_get_id_m2C9612A3B81A7F76F86C4FE87AD4D3FADEF3E385,
	UserProfile_get_isFriend_m7ACC8963AD01FB384D215AF8C215DD93CB5322EA,
	UserProfile_get_state_mF93F969F5923A0A3528923E5A452138D00D4E248,
	Achievement__ctor_m6BFE0E7DB18B35A485855AC3B1C0D4E25AB56C0C,
	Achievement__ctor_m8E491BFB569F645FC9BF4CC8F58F5718C54901E0,
	Achievement__ctor_m570954380BDC68F860E558D037369D03269C72A4,
	Achievement_ToString_m443278D0F151B7B9BAF041785378EFEAC98E6052,
	Achievement_get_id_m39931C0B44D8A4948E12527FDE77C9211028267B,
	Achievement_set_id_m16A4091CBC207FB287FEFF608C5F45D9D21106A0,
	Achievement_get_percentCompleted_m864BBAA8D955D724A506E62F6741B52D43F38F9D,
	Achievement_set_percentCompleted_mD433F22DBB60B94FD45CC2A73C9A22C93B0CB9CB,
	Achievement_get_completed_m26C3793203BC368F6A44678FEB4E8B94976DC66F,
	Achievement_get_hidden_m732D2ED47F705384C1A8CA28E40AADE16944CD11,
	Achievement_get_lastReportedDate_m0C90E0EB0392E57E70D62D2FB182527613BB285C,
	AchievementDescription__ctor_m5EA1DE335B7A97FCDE944FBFD2B979D870D3F37A,
	AchievementDescription_ToString_m48909B3814359ED0FAF88F29F3B80C024F0C5D36,
	AchievementDescription_SetImage_mB1653A512095EE6A795031B009513071A559C248,
	AchievementDescription_get_id_mD5FEE2DBF0C88887C99E3EDCD9BC65C32EFBAD4A,
	AchievementDescription_set_id_m61C4DA5B9AD3353518524A8E34723CE7836D432E,
	AchievementDescription_get_title_mF4E46C1A1BBF0087858BE6046604D00DB130E81F,
	AchievementDescription_get_achievedDescription_m4901EFA817B61139B1A5284CAF14378A531B7537,
	AchievementDescription_get_unachievedDescription_m90706792FD9A6FD0BC4975E41E531F91AEC3F33C,
	AchievementDescription_get_hidden_m8FDE6EBDC27F05F8D04B53B4F044B5B06F2EDA0A,
	AchievementDescription_get_points_mCBD1A433E28AC73B7AECBE3E41FFDB6B335031AF,
	Score__ctor_m88494757EDE00B4CA1BE7027A1F84C82E09EC958,
	Score__ctor_m94026C33B1E9EE0F31614511DB8F512E8A7A4FE3,
	Score_ToString_mBE1B8E8846C633AAA58A0680C888F21915454FC4,
	Score_get_leaderboardID_m079BDC75AED9D8C06DC36F78AC233171F023FB4F,
	Score_set_leaderboardID_m05255F12ADDC2FC6D7E9646CBAE68D2924E66F65,
	Score_get_value_m750646EDC0619A11179177F597C21374F13FCB9E,
	Score_set_value_mFECF5E9D1928EEAE7E46F497CDA159549EEB74C4,
	Leaderboard__ctor_mC09955DEF2101303E698FC13E1BBFFA90B3365F4,
	Leaderboard_ToString_mE59DCCF117736269AF0C6CD21876DB3448BD14ED,
	Leaderboard_SetLocalUserScore_m17939BC032AC6FDCC1152234807C50E97A72D600,
	Leaderboard_SetMaxRange_m5D699FC22BC5BED04386C4B6D4661663B49FD3B4,
	Leaderboard_SetScores_m2DB8C9FB943AD849B23FCEB56D76FAE4E11C2084,
	Leaderboard_SetTitle_m5FE6EFFE4099E7BA2B7F1F6644B0A9E1C5A93627,
	Leaderboard_GetUserFilter_m229A294CB0ED73326836FABAC438E517F6517550,
	Leaderboard_get_id_m4A262BB19BCACE6C9B19874F5D68C777846C6CD6,
	Leaderboard_set_id_mC863AFBCE6B992935BCE9E8B567CDA40CCD94B42,
	Leaderboard_get_userScope_m8304E736582482D0E5705C109D95F0CEDF2EE54C,
	Leaderboard_set_userScope_m66BBCC760CDAF1C44639BD10E4D901EFD7C3F778,
	Leaderboard_get_range_mB9C6C91869423F791E017B71FCDB17A354585519,
	Leaderboard_set_range_m29352D47548D750E0E42EC9627C861C69D084444,
	Leaderboard_get_timeScope_mE1F4CBEBE4B2E1E6BD245E3BE518680F13BBF276,
	Leaderboard_set_timeScope_m36122610C955D05BD7FC713415CA78076986B901,
	GcUserProfileData_ToUserProfile_m500A5A38D89D400333D9ED8D2005449AFEC91F24,
	GcUserProfileData_AddToArray_mA189B901FEF32E5CA9AF7DEB58D8143E87E41EC2,
	GcAchievementDescriptionData_ToAchievementDescription_m9942E64237B72699916331A3EED62FC8E0DB8B86,
	GcAchievementData_ToAchievement_mA3EFDC224A485DA99E1ED1CC90DEEB2CB68B539E,
	GcScoreData_ToScore_m6B29510CCE935E2A67EAFC653CB4A0A4B8B32B49,
	GameCenterPlatform_ClearAchievementDescriptions_m656409DDC86BC2426E26E73244AAE1CFDF3FB893,
	GameCenterPlatform_SetAchievementDescription_m54037323FA3D723CAEC0B6B3849F2567CAB56E6B,
	GameCenterPlatform_SetAchievementDescriptionImage_m23317B6438DB1783760514ABC1882DCBA76FED29,
	GameCenterPlatform_TriggerAchievementDescriptionCallback_m3E49388CCC8EF048838C1020E8A7B82E8DC9865E,
	GameCenterPlatform_AuthenticateCallbackWrapper_m0607EC8652F4831D78B922D9D01F04245DC033D6,
	GameCenterPlatform_ClearFriends_mBC97C1F1ABA638B34B1F9AED1A0EB2911B9405C1,
	GameCenterPlatform_SetFriends_m3E79AC6C0E5FD2319A3CADBC24FBBFE4D6238300,
	GameCenterPlatform_SetFriendImage_m522525502B10C15F1147FEE893F3B60942998F03,
	GameCenterPlatform_TriggerFriendsCallbackWrapper_mFA57C0C3621E2E44CB38BCB006780B81CBB31443,
	GameCenterPlatform_AchievementCallbackWrapper_m1257FE98EE0E742DB4FBDA5176D0B844412CDB27,
	GameCenterPlatform_ProgressCallbackWrapper_m4026F09939C2203AD1EE00F72F2F6EAFCEE79AA5,
	GameCenterPlatform_ScoreCallbackWrapper_m3FCDF12166DEC51D2B5A770EFDA47AFDABA45810,
	GameCenterPlatform_ScoreLoaderCallbackWrapper_m5D838BB12756DB8AF4561E333A6B21E19C4DAD3C,
	GameCenterPlatform_UnityEngine_SocialPlatforms_ISocialPlatform_LoadFriends_m1DFD532E0674BB256884F22C83495D9FFBB66CB0,
	GameCenterPlatform_UnityEngine_SocialPlatforms_ISocialPlatform_Authenticate_m1906287A9BD6679297200FD68303DA2CA9BB4EFB,
	GameCenterPlatform_UnityEngine_SocialPlatforms_ISocialPlatform_Authenticate_m3ACFDDBE3CD2501B919983BEDF89463CE33572BE,
	GameCenterPlatform_get_localUser_m7E985071FAA0B7393A5B2A056917DF06B41BD0A4,
	GameCenterPlatform_PopulateLocalUser_m67257D02ABCAB80407FD27BC218AB5D2A5212D1A,
	GameCenterPlatform_LoadAchievementDescriptions_m72A5167A9328B903358163FC83A800B9FCEB9AD6,
	GameCenterPlatform_ReportProgress_mD284779838DA024C5CA4EC40D1B2ECA25569D5C0,
	GameCenterPlatform_LoadAchievements_m63737CE0EF90F20A31DD1F43EE3D39D79836DF7F,
	GameCenterPlatform_ReportScore_m2ACB935864BC415729EF552832518FA3FF890171,
	GameCenterPlatform_LoadScores_mC191ABA1B69A13CC88A44AF25BD89E1D289CBF52,
	GameCenterPlatform_LoadScores_m54BF2C6FAB4A19BC80B168F3BB985BDBAFA96A1B,
	GameCenterPlatform_LeaderboardCallbackWrapper_mADA9228337DBFC03A16D3D9E290582B509E01454,
	GameCenterPlatform_GetLoading_m06F9588A6143126F03D9C0AAEAA853EC1C9FC3C1,
	GameCenterPlatform_VerifyAuthentication_m9605DB240711AB73F461074D8B93F195E894554E,
	GameCenterPlatform_ShowAchievementsUI_m425068FBB95E73A5E2C34A8045394F9583F27728,
	GameCenterPlatform_ShowLeaderboardUI_m25410C4C825818ABC415F3231AFD4C0B3D80100C,
	GameCenterPlatform_ClearUsers_m04659D6A21590E9DC875EB48A7F821CD809232B7,
	GameCenterPlatform_SetUser_mA06041F15EDF1E8B2232B2C97D3E0CE2168CCF63,
	GameCenterPlatform_SetUserImage_m5D1525B67471D5A1705CC459A70D816BA6454CF8,
	GameCenterPlatform_TriggerUsersCallbackWrapper_m003A05DCFF29307CA093E8982AF4FFA9573BDDD1,
	GameCenterPlatform_LoadUsers_m10FB18D65C6C997E1AB6F0BEB27891B0C1A4923C,
	GameCenterPlatform_SafeSetUserImage_mFD5E65D092CF5DAE75E5120CFA6F00E85F2573C6,
	GameCenterPlatform_SafeClearArray_m51942DE4C82E5F7510632DB5792D4CD95DC29FAC,
	GameCenterPlatform_CreateLeaderboard_mC2B336F8C9569B3FD7059ED7861B208DD44B0EAB,
	GameCenterPlatform_CreateAchievement_mC8BE646E6BB63CCF284C62FABEE712BF7590DB1A,
	GameCenterPlatform_TriggerResetAchievementCallback_m6AFB9EF2A78FB3B06BCB74A53C8B419840997997,
	GameCenterPlatform_Authenticate_mC8451B5E84D0D178EE6B66D3B526DC0ADE7FA87E,
	GameCenterPlatform_GetAuthenticated_m9F50127C839BB750231C452B6950C8E2064284B0,
	GameCenterPlatform_Internal_UserName_mC50BC991DC325E7322D29DB1F1CD0A7BC1CF4EAE,
	GameCenterPlatform_Internal_UserID_m240D2694A438AC48E8CE9BEABA49D2E73147F508,
	GameCenterPlatform_Internal_UserGameID_m13CFCE0B6A926BF5C2A4A06B78ED327567C3A3C1,
	GameCenterPlatform_Internal_LegacyUserID_m04084AEA289328791C394BAB9F04A85B8C77C031,
	GameCenterPlatform_GetIsUnderage_m025892C5F978C9E0900CE7727D3C5AA8F5C2B59C,
	GameCenterPlatform_GetUserImage_mD9238183C0922B79959BAD621E9E4E6CFD306C09,
	GameCenterPlatform_LoadFriends_mC66BFB8FDD72BD19FB58B616A13F327F68DA9190,
	GameCenterPlatform_InternalLoadAchievementDescriptions_m9B65A5F58A40E18D318A896433746D2D1948EC56,
	GameCenterPlatform_InternalLoadAchievements_m97F2BBFEE314065ABB004C34D13494EC83F56B3A,
	GameCenterPlatform_InternalReportProgress_mA38127444F4F1412E442D42B3164413B5F56DF0A,
	GameCenterPlatform_InternalReportScore_m7E4444621D68C1BB352F110E86806609F6C69666,
	GameCenterPlatform_InternalLoadScores_m8A6891634F5469F218E87AF058854F0BED83004C,
	GameCenterPlatform_Internal_ShowAchievementsUI_mAEADFB3EB3302DCBB59271EC3E788805BD7BF5B6,
	GameCenterPlatform_Internal_ShowLeaderboardUI_mB7FC414D67ABFD80CEE868581B736ECC4C2E7C91,
	GameCenterPlatform_Internal_LoadUsers_m9D0013A4B08DA4F9BE19BFB45ECE404872D17C27,
	GameCenterPlatform_ResetAllAchievements_mD43B366C4266A485E227DC8F8C2EA69B2104E6D7,
	GameCenterPlatform_ShowDefaultAchievementBanner_m257ECCCFE7D2AFBC693BC2B290C74F44C9CD627F,
	GameCenterPlatform_ResetAllAchievements_m4EC3D1A47A0D31FA58A6CF4A82D078C7F1C6F2C9,
	GameCenterPlatform_ShowDefaultAchievementCompletionBanner_m25F389BEC1413C7AFA3C9886D32BB5ABF276409C,
	GameCenterPlatform_ShowLeaderboardUI_m9F69D292F83FF87F761CEE71F56631186A06EFCA,
	GameCenterPlatform_ShowSpecificLeaderboardUI_mC368775E6A25D686CA5FBB8A5C8EC27257F2B842,
	GameCenterPlatform__ctor_m12106EF272A85BC20F2CA464C0A92DF42CAB375A,
	GameCenterPlatform__cctor_m9905CEACFF099D1CEA8B9CFDD48E2DE36EF97C12,
	U3CU3Ec__DisplayClass21_0__ctor_mEAC1B8E5722EF1563CE164CE0A2C2EF061A3B65B,
	U3CU3Ec__DisplayClass21_0_U3CUnityEngine_SocialPlatforms_ISocialPlatform_AuthenticateU3Eb__0_m4B82E8613D69236F6B072A688C556241D1CA9E5C,
	GcLeaderboard__ctor_mC6DCBFD1A415763A634A19381C936254D8B2F4E7,
	GcLeaderboard_Finalize_m0DA6F9152780D5EFC4EE884E5CAA6A469CE81E2A,
	GcLeaderboard_Contains_m599B4319BA3FD351DC157B5E23D0489D8186279C,
	GcLeaderboard_SetScores_m0FE1B289E42EAF2415193FFA1BEAE3FE38AA6B4B,
	GcLeaderboard_SetLocalScore_mB3EB59503BACFA37E9F112DB5E1906174C67AF32,
	GcLeaderboard_SetMaxRange_m9926B80DD3377EF7798718F0FD57587DADE4DC31,
	GcLeaderboard_SetTitle_m964511DCF53C69F7208CDC1DFD68885E2BCECF2A,
	GcLeaderboard_Internal_LoadScores_mDFC3A947F5DC0FC7EF83B6C5B605D93BC959675F,
	GcLeaderboard_GcLeaderboard_LoadScores_m341D807EC732903746586EEF57958884952E8E04,
	GcLeaderboard_Loading_m825936D6AB51CC355DAB8DAAF6177AC4ABBC78B2,
	GcLeaderboard_GcLeaderboard_Loading_m632F32F44B1E4383BD5E24767A2A4CC96CA01C92,
	GcLeaderboard_Dispose_m54F8C8544A7F65898B6444B3EE11916153C20B6F,
	GcLeaderboard_GcLeaderboard_Dispose_mC316EE2638A7A84A3FCBDE8EBC1AB42A758BAE5E,
};
extern void Range__ctor_mD7BBA6A816E8FBFDDC618308BC5D5882297443F8_AdjustorThunk (void);
extern void GcUserProfileData_ToUserProfile_m500A5A38D89D400333D9ED8D2005449AFEC91F24_AdjustorThunk (void);
extern void GcUserProfileData_AddToArray_mA189B901FEF32E5CA9AF7DEB58D8143E87E41EC2_AdjustorThunk (void);
extern void GcAchievementDescriptionData_ToAchievementDescription_m9942E64237B72699916331A3EED62FC8E0DB8B86_AdjustorThunk (void);
extern void GcAchievementData_ToAchievement_mA3EFDC224A485DA99E1ED1CC90DEEB2CB68B539E_AdjustorThunk (void);
extern void GcScoreData_ToScore_m6B29510CCE935E2A67EAFC653CB4A0A4B8B32B49_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[6] = 
{
	{ 0x06000005, Range__ctor_mD7BBA6A816E8FBFDDC618308BC5D5882297443F8_AdjustorThunk },
	{ 0x06000046, GcUserProfileData_ToUserProfile_m500A5A38D89D400333D9ED8D2005449AFEC91F24_AdjustorThunk },
	{ 0x06000047, GcUserProfileData_AddToArray_mA189B901FEF32E5CA9AF7DEB58D8143E87E41EC2_AdjustorThunk },
	{ 0x06000048, GcAchievementDescriptionData_ToAchievementDescription_m9942E64237B72699916331A3EED62FC8E0DB8B86_AdjustorThunk },
	{ 0x06000049, GcAchievementData_ToAchievement_mA3EFDC224A485DA99E1ED1CC90DEEB2CB68B539E_AdjustorThunk },
	{ 0x0600004A, GcScoreData_ToScore_m6B29510CCE935E2A67EAFC653CB4A0A4B8B32B49_AdjustorThunk },
};
static const int32_t s_InvokerIndices[153] = 
{
	2171,
	2171,
	2171,
	3863,
	1961,
	3916,
	3893,
	3931,
	3893,
	3967,
	3348,
	3276,
	3276,
	3863,
	3967,
	257,
	3916,
	3348,
	3348,
	3348,
	3348,
	3348,
	3916,
	3916,
	3863,
	3893,
	393,
	2164,
	3967,
	3916,
	3916,
	3348,
	3882,
	3301,
	3863,
	3863,
	3877,
	159,
	3916,
	3348,
	3916,
	3348,
	3916,
	3916,
	3916,
	3863,
	3893,
	2168,
	249,
	3916,
	3916,
	3348,
	3894,
	3314,
	3967,
	3916,
	3348,
	3392,
	3348,
	3348,
	3916,
	3916,
	3348,
	3893,
	3313,
	3931,
	3365,
	3893,
	3313,
	3916,
	1824,
	3916,
	3916,
	3916,
	6213,
	5739,
	5830,
	6218,
	5747,
	6213,
	5740,
	5830,
	5830,
	5834,
	5825,
	5825,
	5834,
	2171,
	2171,
	2171,
	3916,
	6289,
	3348,
	1321,
	3348,
	1301,
	2171,
	2171,
	5825,
	2591,
	3863,
	3967,
	3967,
	6213,
	5740,
	5830,
	6218,
	2171,
	5277,
	5731,
	3916,
	3916,
	6208,
	6289,
	6256,
	6270,
	6270,
	6270,
	6270,
	6256,
	6270,
	6218,
	6218,
	6218,
	5322,
	5293,
	5834,
	6289,
	6289,
	5834,
	6289,
	6208,
	6218,
	6208,
	5830,
	5830,
	3967,
	6289,
	3967,
	1833,
	3348,
	3967,
	2591,
	3348,
	3307,
	3392,
	3348,
	140,
	4097,
	3863,
	5922,
	3967,
	6215,
};
extern const CustomAttributesCacheGenerator g_UnityEngine_GameCenterModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_GameCenterModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_GameCenterModule_CodeGenModule = 
{
	"UnityEngine.GameCenterModule.dll",
	153,
	s_methodPointers,
	6,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_UnityEngine_GameCenterModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

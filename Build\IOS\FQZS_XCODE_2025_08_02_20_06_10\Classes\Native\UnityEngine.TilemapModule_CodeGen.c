﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void UnityEngine.Tilemaps.ITilemap::.ctor()
extern void ITilemap__ctor_m94E2A628CC647832AA3B576274241AAB0CDAC504 (void);
// 0x00000002 System.Void UnityEngine.Tilemaps.ITilemap::RefreshTile(UnityEngine.Vector3Int)
extern void ITilemap_RefreshTile_mC602A7D2A938BFF0D959CAE20BCF8253A34B4D87 (void);
// 0x00000003 UnityEngine.Tilemaps.ITilemap UnityEngine.Tilemaps.ITilemap::CreateInstance()
extern void ITilemap_CreateInstance_m5A58F83B96B5B0720E93B093DBC9AC10AC900BC0 (void);
// 0x00000004 UnityEngine.Sprite UnityEngine.Tilemaps.Tile::get_sprite()
extern void Tile_get_sprite_m55D8F25A18CA3FA4DAF1FB60E77D8552978FC7E9 (void);
// 0x00000005 System.Void UnityEngine.Tilemaps.Tile::set_sprite(UnityEngine.Sprite)
extern void Tile_set_sprite_m29904A3BC52CDFB8BF48604CD0FED0EF3477BCE4 (void);
// 0x00000006 UnityEngine.Color UnityEngine.Tilemaps.Tile::get_color()
extern void Tile_get_color_mD8EBC5D6FFD0CCCE7AE4C23F4AFEE7FA73CCE838 (void);
// 0x00000007 System.Void UnityEngine.Tilemaps.Tile::set_color(UnityEngine.Color)
extern void Tile_set_color_m855E5C00F35C08077450D27BC5BB18BA7F2D67FC (void);
// 0x00000008 UnityEngine.Matrix4x4 UnityEngine.Tilemaps.Tile::get_transform()
extern void Tile_get_transform_m010FA5FE2A3C48BCE79DAF095D2B7A652BD2B32B (void);
// 0x00000009 System.Void UnityEngine.Tilemaps.Tile::set_transform(UnityEngine.Matrix4x4)
extern void Tile_set_transform_mDD4E70226F1A89377D3E4822DDD666B1E4F7EDFB (void);
// 0x0000000A UnityEngine.GameObject UnityEngine.Tilemaps.Tile::get_gameObject()
extern void Tile_get_gameObject_m5121800140E9009AE43AA782AC2437EB05E0477B (void);
// 0x0000000B System.Void UnityEngine.Tilemaps.Tile::set_gameObject(UnityEngine.GameObject)
extern void Tile_set_gameObject_mB450C0800EDB44EDB8E8F56DAFF75B7C13FC6420 (void);
// 0x0000000C UnityEngine.Tilemaps.TileFlags UnityEngine.Tilemaps.Tile::get_flags()
extern void Tile_get_flags_m10DF194D35F7B96B52795369365B332F1432143C (void);
// 0x0000000D System.Void UnityEngine.Tilemaps.Tile::set_flags(UnityEngine.Tilemaps.TileFlags)
extern void Tile_set_flags_mAF0336180878A6D396DFFE4B9D3C188F6D6010CB (void);
// 0x0000000E UnityEngine.Tilemaps.Tile/ColliderType UnityEngine.Tilemaps.Tile::get_colliderType()
extern void Tile_get_colliderType_m978D12FD1D9210E990576B0515A6296B0566025E (void);
// 0x0000000F System.Void UnityEngine.Tilemaps.Tile::set_colliderType(UnityEngine.Tilemaps.Tile/ColliderType)
extern void Tile_set_colliderType_m0D7FFF0A6A80C2C026030149CF924E9FB8A852C1 (void);
// 0x00000010 System.Void UnityEngine.Tilemaps.Tile::GetTileData(UnityEngine.Vector3Int,UnityEngine.Tilemaps.ITilemap,UnityEngine.Tilemaps.TileData&)
extern void Tile_GetTileData_m6BDFA53AD74BF38AAD340203F43D1C796BAB8F9A (void);
// 0x00000011 System.Void UnityEngine.Tilemaps.Tile::.ctor()
extern void Tile__ctor_m8783DD13225005DB9323B7A7E86AB8641033B4A8 (void);
// 0x00000012 System.Void UnityEngine.Tilemaps.TileBase::RefreshTile(UnityEngine.Vector3Int,UnityEngine.Tilemaps.ITilemap)
extern void TileBase_RefreshTile_m5614A37F408CCA8FABACB0A911D5EB5883ED051D (void);
// 0x00000013 System.Void UnityEngine.Tilemaps.TileBase::GetTileData(UnityEngine.Vector3Int,UnityEngine.Tilemaps.ITilemap,UnityEngine.Tilemaps.TileData&)
extern void TileBase_GetTileData_m936097A6D3A32A3C3E54EE7FF4EDA22CC7FF6088 (void);
// 0x00000014 UnityEngine.Tilemaps.TileData UnityEngine.Tilemaps.TileBase::GetTileDataNoRef(UnityEngine.Vector3Int,UnityEngine.Tilemaps.ITilemap)
extern void TileBase_GetTileDataNoRef_mF9B5CA6886A8761D504478146F053484AF3CFACA (void);
// 0x00000015 System.Boolean UnityEngine.Tilemaps.TileBase::GetTileAnimationData(UnityEngine.Vector3Int,UnityEngine.Tilemaps.ITilemap,UnityEngine.Tilemaps.TileAnimationData&)
extern void TileBase_GetTileAnimationData_mA54F70D100129EC2074DC2F17471FDE315FF0C02 (void);
// 0x00000016 UnityEngine.Tilemaps.TileAnimationData UnityEngine.Tilemaps.TileBase::GetTileAnimationDataNoRef(UnityEngine.Vector3Int,UnityEngine.Tilemaps.ITilemap)
extern void TileBase_GetTileAnimationDataNoRef_m0562FCB94DCECA5DFD526806900B14D69ADB2FBF (void);
// 0x00000017 System.Boolean UnityEngine.Tilemaps.TileBase::StartUp(UnityEngine.Vector3Int,UnityEngine.Tilemaps.ITilemap,UnityEngine.GameObject)
extern void TileBase_StartUp_mD3221FE426FCFC3F0CC0ECDEE2DFEE7E6F94C8E7 (void);
// 0x00000018 System.Void UnityEngine.Tilemaps.TileBase::.ctor()
extern void TileBase__ctor_mEF753C200728FD143A358A5AF2B7978D9A982A67 (void);
// 0x00000019 System.Void UnityEngine.Tilemaps.Tilemap::RefreshTile(UnityEngine.Vector3Int)
extern void Tilemap_RefreshTile_m1A2B0119B58A5E409BF899A1F88CBD3E23599193 (void);
// 0x0000001A System.Void UnityEngine.Tilemaps.Tilemap::RefreshTile_Injected(UnityEngine.Vector3Int&)
extern void Tilemap_RefreshTile_Injected_mB711BD24637434562FA9278325FEE07FC0931B32 (void);
// 0x0000001B System.Void UnityEngine.Tilemaps.TilemapRenderer::RegisterSpriteAtlasRegistered()
extern void TilemapRenderer_RegisterSpriteAtlasRegistered_mB338307A0CCCF3193B76CAF873FCC898463C1DF8 (void);
// 0x0000001C System.Void UnityEngine.Tilemaps.TilemapRenderer::UnregisterSpriteAtlasRegistered()
extern void TilemapRenderer_UnregisterSpriteAtlasRegistered_mCE258B84F549040922D062E22F1205C562E0577A (void);
// 0x0000001D System.Void UnityEngine.Tilemaps.TilemapRenderer::OnSpriteAtlasRegistered(UnityEngine.U2D.SpriteAtlas)
extern void TilemapRenderer_OnSpriteAtlasRegistered_m5C732CF912E2BC489C5E621EB3820DA1AF0A7093 (void);
// 0x0000001E System.Void UnityEngine.Tilemaps.TileData::set_sprite(UnityEngine.Sprite)
extern void TileData_set_sprite_m24F99D8E52155C9E6F56B5CF647C7A423ACB76E8 (void);
// 0x0000001F System.Void UnityEngine.Tilemaps.TileData::set_color(UnityEngine.Color)
extern void TileData_set_color_m821F675529A25C6ED8D8786DBD2DFE286A6385AF (void);
// 0x00000020 System.Void UnityEngine.Tilemaps.TileData::set_transform(UnityEngine.Matrix4x4)
extern void TileData_set_transform_m39FBC6A129739589B8993B4185DFF72B0B472E2C (void);
// 0x00000021 System.Void UnityEngine.Tilemaps.TileData::set_gameObject(UnityEngine.GameObject)
extern void TileData_set_gameObject_m02BDDD787C6E5AD0DFA29E510E4FFD101090D685 (void);
// 0x00000022 System.Void UnityEngine.Tilemaps.TileData::set_flags(UnityEngine.Tilemaps.TileFlags)
extern void TileData_set_flags_m1AC7BA3912E9B4B85F4F0B322FE2361AE475B0E4 (void);
// 0x00000023 System.Void UnityEngine.Tilemaps.TileData::set_colliderType(UnityEngine.Tilemaps.Tile/ColliderType)
extern void TileData_set_colliderType_mEF741658774E43C8AB0986EEA9FBDA1838BF34A3 (void);
static Il2CppMethodPointer s_methodPointers[35] = 
{
	ITilemap__ctor_m94E2A628CC647832AA3B576274241AAB0CDAC504,
	ITilemap_RefreshTile_mC602A7D2A938BFF0D959CAE20BCF8253A34B4D87,
	ITilemap_CreateInstance_m5A58F83B96B5B0720E93B093DBC9AC10AC900BC0,
	Tile_get_sprite_m55D8F25A18CA3FA4DAF1FB60E77D8552978FC7E9,
	Tile_set_sprite_m29904A3BC52CDFB8BF48604CD0FED0EF3477BCE4,
	Tile_get_color_mD8EBC5D6FFD0CCCE7AE4C23F4AFEE7FA73CCE838,
	Tile_set_color_m855E5C00F35C08077450D27BC5BB18BA7F2D67FC,
	Tile_get_transform_m010FA5FE2A3C48BCE79DAF095D2B7A652BD2B32B,
	Tile_set_transform_mDD4E70226F1A89377D3E4822DDD666B1E4F7EDFB,
	Tile_get_gameObject_m5121800140E9009AE43AA782AC2437EB05E0477B,
	Tile_set_gameObject_mB450C0800EDB44EDB8E8F56DAFF75B7C13FC6420,
	Tile_get_flags_m10DF194D35F7B96B52795369365B332F1432143C,
	Tile_set_flags_mAF0336180878A6D396DFFE4B9D3C188F6D6010CB,
	Tile_get_colliderType_m978D12FD1D9210E990576B0515A6296B0566025E,
	Tile_set_colliderType_m0D7FFF0A6A80C2C026030149CF924E9FB8A852C1,
	Tile_GetTileData_m6BDFA53AD74BF38AAD340203F43D1C796BAB8F9A,
	Tile__ctor_m8783DD13225005DB9323B7A7E86AB8641033B4A8,
	TileBase_RefreshTile_m5614A37F408CCA8FABACB0A911D5EB5883ED051D,
	TileBase_GetTileData_m936097A6D3A32A3C3E54EE7FF4EDA22CC7FF6088,
	TileBase_GetTileDataNoRef_mF9B5CA6886A8761D504478146F053484AF3CFACA,
	TileBase_GetTileAnimationData_mA54F70D100129EC2074DC2F17471FDE315FF0C02,
	TileBase_GetTileAnimationDataNoRef_m0562FCB94DCECA5DFD526806900B14D69ADB2FBF,
	TileBase_StartUp_mD3221FE426FCFC3F0CC0ECDEE2DFEE7E6F94C8E7,
	TileBase__ctor_mEF753C200728FD143A358A5AF2B7978D9A982A67,
	Tilemap_RefreshTile_m1A2B0119B58A5E409BF899A1F88CBD3E23599193,
	Tilemap_RefreshTile_Injected_mB711BD24637434562FA9278325FEE07FC0931B32,
	TilemapRenderer_RegisterSpriteAtlasRegistered_mB338307A0CCCF3193B76CAF873FCC898463C1DF8,
	TilemapRenderer_UnregisterSpriteAtlasRegistered_mCE258B84F549040922D062E22F1205C562E0577A,
	TilemapRenderer_OnSpriteAtlasRegistered_m5C732CF912E2BC489C5E621EB3820DA1AF0A7093,
	TileData_set_sprite_m24F99D8E52155C9E6F56B5CF647C7A423ACB76E8,
	TileData_set_color_m821F675529A25C6ED8D8786DBD2DFE286A6385AF,
	TileData_set_transform_m39FBC6A129739589B8993B4185DFF72B0B472E2C,
	TileData_set_gameObject_m02BDDD787C6E5AD0DFA29E510E4FFD101090D685,
	TileData_set_flags_m1AC7BA3912E9B4B85F4F0B322FE2361AE475B0E4,
	TileData_set_colliderType_mEF741658774E43C8AB0986EEA9FBDA1838BF34A3,
};
extern void TileData_set_sprite_m24F99D8E52155C9E6F56B5CF647C7A423ACB76E8_AdjustorThunk (void);
extern void TileData_set_color_m821F675529A25C6ED8D8786DBD2DFE286A6385AF_AdjustorThunk (void);
extern void TileData_set_transform_m39FBC6A129739589B8993B4185DFF72B0B472E2C_AdjustorThunk (void);
extern void TileData_set_gameObject_m02BDDD787C6E5AD0DFA29E510E4FFD101090D685_AdjustorThunk (void);
extern void TileData_set_flags_m1AC7BA3912E9B4B85F4F0B322FE2361AE475B0E4_AdjustorThunk (void);
extern void TileData_set_colliderType_mEF741658774E43C8AB0986EEA9FBDA1838BF34A3_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[6] = 
{
	{ 0x0600001E, TileData_set_sprite_m24F99D8E52155C9E6F56B5CF647C7A423ACB76E8_AdjustorThunk },
	{ 0x0600001F, TileData_set_color_m821F675529A25C6ED8D8786DBD2DFE286A6385AF_AdjustorThunk },
	{ 0x06000020, TileData_set_transform_m39FBC6A129739589B8993B4185DFF72B0B472E2C_AdjustorThunk },
	{ 0x06000021, TileData_set_gameObject_m02BDDD787C6E5AD0DFA29E510E4FFD101090D685_AdjustorThunk },
	{ 0x06000022, TileData_set_flags_m1AC7BA3912E9B4B85F4F0B322FE2361AE475B0E4_AdjustorThunk },
	{ 0x06000023, TileData_set_colliderType_mEF741658774E43C8AB0986EEA9FBDA1838BF34A3_AdjustorThunk },
};
static const int32_t s_InvokerIndices[35] = 
{
	3967,
	3399,
	6270,
	3916,
	3348,
	3867,
	3281,
	3911,
	3344,
	3916,
	3348,
	3893,
	3313,
	3893,
	3313,
	1389,
	3967,
	2224,
	1389,
	1781,
	1001,
	1780,
	1002,
	3967,
	3399,
	3255,
	3967,
	3967,
	3348,
	3348,
	3281,
	3344,
	3348,
	3313,
	3313,
};
extern const CustomAttributesCacheGenerator g_UnityEngine_TilemapModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_TilemapModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_TilemapModule_CodeGenModule = 
{
	"UnityEngine.TilemapModule.dll",
	35,
	s_methodPointers,
	6,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_UnityEngine_TilemapModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

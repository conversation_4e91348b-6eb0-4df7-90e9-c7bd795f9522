﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void UnityEngine.JointLimits::set_min(System.Single)
extern void JointLimits_set_min_m500E297C572E2690D0678EC354BFE9AF7F27D2FE (void);
// 0x00000002 System.Void UnityEngine.JointLimits::set_max(System.Single)
extern void JointLimits_set_max_mE767D8DE579BBFBF505B5A8316A0B04D8F53775C (void);
// 0x00000003 UnityEngine.GameObject UnityEngine.Collision::get_gameObject()
extern void Collision_get_gameObject_m5682F872FD28419AA36F0651CE8B19825A21859D (void);
// 0x00000004 System.Void UnityEngine.PhysicMaterial::.ctor()
extern void PhysicMaterial__ctor_m8E7DE1AB54FEC51DE0DA4207E5BB55BB45EFC873 (void);
// 0x00000005 System.Void UnityEngine.PhysicMaterial::Internal_CreateDynamicsMaterial(UnityEngine.PhysicMaterial,System.String)
extern void PhysicMaterial_Internal_CreateDynamicsMaterial_mAE144DFC84442CB32A328052430C7FDF4F9834CA (void);
// 0x00000006 UnityEngine.Collider UnityEngine.RaycastHit::get_collider()
extern void RaycastHit_get_collider_m13A3DE16FBC631E0A1F987E0B22CE70AF8AB539E (void);
// 0x00000007 UnityEngine.Vector3 UnityEngine.RaycastHit::get_point()
extern void RaycastHit_get_point_m32F7282CBB2E13393A33BAD046BDA218E48DD21E (void);
// 0x00000008 UnityEngine.Vector3 UnityEngine.RaycastHit::get_normal()
extern void RaycastHit_get_normal_m2C813B25BAECD87FD9E9CB294278B291F4CC6674 (void);
// 0x00000009 System.Single UnityEngine.RaycastHit::get_distance()
extern void RaycastHit_get_distance_m85FCA98D7957C3BF1D449CA1B48C116CCD6226FA (void);
// 0x0000000A UnityEngine.Transform UnityEngine.RaycastHit::get_transform()
extern void RaycastHit_get_transform_m2DD983DBD3602DE848DE287EE5233FD02EEC608D (void);
// 0x0000000B UnityEngine.Rigidbody UnityEngine.RaycastHit::get_rigidbody()
extern void RaycastHit_get_rigidbody_mE48E45893FDAFD03162C6A73AAF02C6CB0E079FB (void);
// 0x0000000C UnityEngine.Vector3 UnityEngine.Rigidbody::get_velocity()
extern void Rigidbody_get_velocity_mCFB033F3BD14C2BA68E797DFA4950F9307EC8E2C (void);
// 0x0000000D System.Void UnityEngine.Rigidbody::set_velocity(UnityEngine.Vector3)
extern void Rigidbody_set_velocity_m8DC0988916EB38DFD7D4584830B41D79140BF18D (void);
// 0x0000000E UnityEngine.Vector3 UnityEngine.Rigidbody::get_angularVelocity()
extern void Rigidbody_get_angularVelocity_m6737340DF546452900D199246279231D80A21DCF (void);
// 0x0000000F System.Void UnityEngine.Rigidbody::set_angularVelocity(UnityEngine.Vector3)
extern void Rigidbody_set_angularVelocity_m3A40B7F195E9E217AE29A0964D7E7540E2E23080 (void);
// 0x00000010 System.Single UnityEngine.Rigidbody::get_drag()
extern void Rigidbody_get_drag_m0C617963D9BBBC4018D3A8B2DB5D6190615F4A64 (void);
// 0x00000011 System.Void UnityEngine.Rigidbody::set_drag(System.Single)
extern void Rigidbody_set_drag_m60E39BE31529DE5163116785A69FACC77C52DA98 (void);
// 0x00000012 System.Single UnityEngine.Rigidbody::get_angularDrag()
extern void Rigidbody_get_angularDrag_m0E53FD8F8A09DFA941C52C868288DBBC030C5082 (void);
// 0x00000013 System.Void UnityEngine.Rigidbody::set_angularDrag(System.Single)
extern void Rigidbody_set_angularDrag_m8BF3771789B32FB09FDD8066BAFA0A0B661372A4 (void);
// 0x00000014 System.Single UnityEngine.Rigidbody::get_mass()
extern void Rigidbody_get_mass_mB7B19406DAC6336A8244E98BE271BDA8B5C26223 (void);
// 0x00000015 System.Void UnityEngine.Rigidbody::set_mass(System.Single)
extern void Rigidbody_set_mass_m54FCACE073F5E7742DB1D7C0BA19CD0C0F3DDA3F (void);
// 0x00000016 System.Void UnityEngine.Rigidbody::SetDensity(System.Single)
extern void Rigidbody_SetDensity_m794AA2513F044BCFC3BC87AB39387131BA1EA282 (void);
// 0x00000017 System.Boolean UnityEngine.Rigidbody::get_useGravity()
extern void Rigidbody_get_useGravity_mDA0FB6F456377840E6E46C42B9210F93264E2B28 (void);
// 0x00000018 System.Void UnityEngine.Rigidbody::set_useGravity(System.Boolean)
extern void Rigidbody_set_useGravity_m1057292FB3199E87664F40B8BCBA7A7E64D1A096 (void);
// 0x00000019 System.Single UnityEngine.Rigidbody::get_maxDepenetrationVelocity()
extern void Rigidbody_get_maxDepenetrationVelocity_mE2F07995501D4EFAB39C3B5E6A91ECD6D899272E (void);
// 0x0000001A System.Void UnityEngine.Rigidbody::set_maxDepenetrationVelocity(System.Single)
extern void Rigidbody_set_maxDepenetrationVelocity_mF2D2B5F040B69E32E48544EBA5F6D43656798A02 (void);
// 0x0000001B System.Boolean UnityEngine.Rigidbody::get_isKinematic()
extern void Rigidbody_get_isKinematic_m597B48C45021313B6C6C4B126E405EF566C5C80C (void);
// 0x0000001C System.Void UnityEngine.Rigidbody::set_isKinematic(System.Boolean)
extern void Rigidbody_set_isKinematic_mCF74D680205544826F2DE2CAB929C9F25409A311 (void);
// 0x0000001D System.Boolean UnityEngine.Rigidbody::get_freezeRotation()
extern void Rigidbody_get_freezeRotation_m52850F7355488269E7026A1E9C6AEB69EA1A1CCE (void);
// 0x0000001E System.Void UnityEngine.Rigidbody::set_freezeRotation(System.Boolean)
extern void Rigidbody_set_freezeRotation_mE08A39E98D46F82D6DD86CC389D86D242C694D52 (void);
// 0x0000001F UnityEngine.RigidbodyConstraints UnityEngine.Rigidbody::get_constraints()
extern void Rigidbody_get_constraints_m5EBE3E9C0A2084BAAE0C783E212C368A1CB1DD04 (void);
// 0x00000020 System.Void UnityEngine.Rigidbody::set_constraints(UnityEngine.RigidbodyConstraints)
extern void Rigidbody_set_constraints_mA76F562D16D3BE8889E095D0309C8FE38DA914F1 (void);
// 0x00000021 UnityEngine.CollisionDetectionMode UnityEngine.Rigidbody::get_collisionDetectionMode()
extern void Rigidbody_get_collisionDetectionMode_m1328BE903DD8B2DBD3A67B64FCECFEE91DC49FCE (void);
// 0x00000022 System.Void UnityEngine.Rigidbody::set_collisionDetectionMode(UnityEngine.CollisionDetectionMode)
extern void Rigidbody_set_collisionDetectionMode_m4BC31A84B26540ACD15273774C47F31CDF5381C2 (void);
// 0x00000023 UnityEngine.Vector3 UnityEngine.Rigidbody::get_centerOfMass()
extern void Rigidbody_get_centerOfMass_mEF7838582F7977AAD2952F6A09E2203D9B0ABC53 (void);
// 0x00000024 System.Void UnityEngine.Rigidbody::set_centerOfMass(UnityEngine.Vector3)
extern void Rigidbody_set_centerOfMass_m3B13BE412D99CE5133606643F14501CF5C63CCEC (void);
// 0x00000025 UnityEngine.Vector3 UnityEngine.Rigidbody::get_worldCenterOfMass()
extern void Rigidbody_get_worldCenterOfMass_mF825B0D5110903BD0A3FDC1DC4317902305DE143 (void);
// 0x00000026 UnityEngine.Quaternion UnityEngine.Rigidbody::get_inertiaTensorRotation()
extern void Rigidbody_get_inertiaTensorRotation_m9EFA1F67FF15DC3E996146737149C69A5003D3C1 (void);
// 0x00000027 System.Void UnityEngine.Rigidbody::set_inertiaTensorRotation(UnityEngine.Quaternion)
extern void Rigidbody_set_inertiaTensorRotation_m18E191AC25EE29C24DE6A862A26633A7A21A9D9B (void);
// 0x00000028 UnityEngine.Vector3 UnityEngine.Rigidbody::get_inertiaTensor()
extern void Rigidbody_get_inertiaTensor_m55F35467FD2088D883060D57595AD5BF14BC5C6F (void);
// 0x00000029 System.Void UnityEngine.Rigidbody::set_inertiaTensor(UnityEngine.Vector3)
extern void Rigidbody_set_inertiaTensor_mCDE8A0CCFD0B6464B5F99CC1A54DF5300652D5D1 (void);
// 0x0000002A System.Boolean UnityEngine.Rigidbody::get_detectCollisions()
extern void Rigidbody_get_detectCollisions_m6741D1A8C1942766F95258677352A75451D99D21 (void);
// 0x0000002B System.Void UnityEngine.Rigidbody::set_detectCollisions(System.Boolean)
extern void Rigidbody_set_detectCollisions_mB94256836724071B1EFE622A5E9BA435B6572E9A (void);
// 0x0000002C UnityEngine.Vector3 UnityEngine.Rigidbody::get_position()
extern void Rigidbody_get_position_m5F429382F610E324F39F33E8498A29D0828AD8E8 (void);
// 0x0000002D System.Void UnityEngine.Rigidbody::set_position(UnityEngine.Vector3)
extern void Rigidbody_set_position_mD1A5B0DB2FD8861778CAA575E1D7B72E2AEF4024 (void);
// 0x0000002E UnityEngine.Quaternion UnityEngine.Rigidbody::get_rotation()
extern void Rigidbody_get_rotation_mEB90F9D223B0BA32A1962971E3A93DEE1670D18A (void);
// 0x0000002F System.Void UnityEngine.Rigidbody::set_rotation(UnityEngine.Quaternion)
extern void Rigidbody_set_rotation_m3024C151FEC9BB75735DE9B4BA64F16AA779C5D6 (void);
// 0x00000030 UnityEngine.RigidbodyInterpolation UnityEngine.Rigidbody::get_interpolation()
extern void Rigidbody_get_interpolation_m17422952D016117813B4520CB65842D2537C8E12 (void);
// 0x00000031 System.Void UnityEngine.Rigidbody::set_interpolation(UnityEngine.RigidbodyInterpolation)
extern void Rigidbody_set_interpolation_mEBAF9DF8DE317E2D58848735D26ED9E69CC425CE (void);
// 0x00000032 System.Int32 UnityEngine.Rigidbody::get_solverIterations()
extern void Rigidbody_get_solverIterations_m9A62E73E257401EFDC44E7338CD36E9E006E8CA1 (void);
// 0x00000033 System.Void UnityEngine.Rigidbody::set_solverIterations(System.Int32)
extern void Rigidbody_set_solverIterations_m71644E473B3DB97C10199A7D5AEBAD790DD586EC (void);
// 0x00000034 System.Single UnityEngine.Rigidbody::get_sleepThreshold()
extern void Rigidbody_get_sleepThreshold_m1469D01B7634195511EEC913BA69617618DE5EEC (void);
// 0x00000035 System.Void UnityEngine.Rigidbody::set_sleepThreshold(System.Single)
extern void Rigidbody_set_sleepThreshold_mA71700BF6225A4366FE6BE1365E26B0BF832C00B (void);
// 0x00000036 System.Single UnityEngine.Rigidbody::get_maxAngularVelocity()
extern void Rigidbody_get_maxAngularVelocity_m8804A10C23EF0C073D5D829ED6BBF5253B0A2DFE (void);
// 0x00000037 System.Void UnityEngine.Rigidbody::set_maxAngularVelocity(System.Single)
extern void Rigidbody_set_maxAngularVelocity_m55AB6C34E2C33E6EABB07EFCB7AE443F5D3BD060 (void);
// 0x00000038 System.Void UnityEngine.Rigidbody::MovePosition(UnityEngine.Vector3)
extern void Rigidbody_MovePosition_mB3CBBF21FD0ABB88BC6C004B993DED25673001C7 (void);
// 0x00000039 System.Void UnityEngine.Rigidbody::MoveRotation(UnityEngine.Quaternion)
extern void Rigidbody_MoveRotation_m08A1449DC0D514A70065CD80D067597765BDA5B2 (void);
// 0x0000003A System.Void UnityEngine.Rigidbody::Sleep()
extern void Rigidbody_Sleep_m60350AEF3E52D57FBE448CADBC06BA98DAEA2115 (void);
// 0x0000003B System.Boolean UnityEngine.Rigidbody::IsSleeping()
extern void Rigidbody_IsSleeping_mD41EDC4429411110D1FE36101C89B0277F8D62BA (void);
// 0x0000003C System.Void UnityEngine.Rigidbody::WakeUp()
extern void Rigidbody_WakeUp_m89308E6756834CF3705D9CDE89D636EEEE409316 (void);
// 0x0000003D System.Void UnityEngine.Rigidbody::ResetCenterOfMass()
extern void Rigidbody_ResetCenterOfMass_m6F34341B309D3AAE2A8B3CC963A1E38383EB0FD9 (void);
// 0x0000003E System.Void UnityEngine.Rigidbody::ResetInertiaTensor()
extern void Rigidbody_ResetInertiaTensor_m8888FABEC8D40828D8F594915AF20CB01E9511D7 (void);
// 0x0000003F UnityEngine.Vector3 UnityEngine.Rigidbody::GetRelativePointVelocity(UnityEngine.Vector3)
extern void Rigidbody_GetRelativePointVelocity_mBE38822FEE8B1D4ECBDCD65AA20D13D0423B1FCF (void);
// 0x00000040 UnityEngine.Vector3 UnityEngine.Rigidbody::GetPointVelocity(UnityEngine.Vector3)
extern void Rigidbody_GetPointVelocity_m48BA0D6C61636CADE0764CC2BF5B02E1B61F1273 (void);
// 0x00000041 System.Int32 UnityEngine.Rigidbody::get_solverVelocityIterations()
extern void Rigidbody_get_solverVelocityIterations_m94EF00A61F9CD356D0E56D7285D06EAC4FD2ABEA (void);
// 0x00000042 System.Void UnityEngine.Rigidbody::set_solverVelocityIterations(System.Int32)
extern void Rigidbody_set_solverVelocityIterations_mA345A4D3722FFFEDD9391720F9296A9721647AED (void);
// 0x00000043 System.Void UnityEngine.Rigidbody::AddForce(UnityEngine.Vector3,UnityEngine.ForceMode)
extern void Rigidbody_AddForce_m78B9D94F505E19F3C63461362AD6DE7EA0836700 (void);
// 0x00000044 System.Void UnityEngine.Rigidbody::AddForce(UnityEngine.Vector3)
extern void Rigidbody_AddForce_mDFB0D57C25682B826999B0074F5C0FD399C6401D (void);
// 0x00000045 System.Void UnityEngine.Rigidbody::AddForce(System.Single,System.Single,System.Single,UnityEngine.ForceMode)
extern void Rigidbody_AddForce_mB32FE7E26D05833A284C6CB9CF7E941A8570934E (void);
// 0x00000046 System.Void UnityEngine.Rigidbody::AddForce(System.Single,System.Single,System.Single)
extern void Rigidbody_AddForce_mFF44DF454FDA13ADFD3CAA4958C4265E45BCF773 (void);
// 0x00000047 System.Void UnityEngine.Rigidbody::AddRelativeForce(UnityEngine.Vector3,UnityEngine.ForceMode)
extern void Rigidbody_AddRelativeForce_mD1AA911C950F80F697130C0A3DEDEECC8B532752 (void);
// 0x00000048 System.Void UnityEngine.Rigidbody::AddRelativeForce(UnityEngine.Vector3)
extern void Rigidbody_AddRelativeForce_m45E6C06CE742C72905BB126945B454FBA54D84E1 (void);
// 0x00000049 System.Void UnityEngine.Rigidbody::AddRelativeForce(System.Single,System.Single,System.Single,UnityEngine.ForceMode)
extern void Rigidbody_AddRelativeForce_m60FECB22FDEA509AF396844051F9953C25C92B55 (void);
// 0x0000004A System.Void UnityEngine.Rigidbody::AddRelativeForce(System.Single,System.Single,System.Single)
extern void Rigidbody_AddRelativeForce_m3997E5306302262D5CB451DA1FEFE687BF42B042 (void);
// 0x0000004B System.Void UnityEngine.Rigidbody::AddTorque(UnityEngine.Vector3,UnityEngine.ForceMode)
extern void Rigidbody_AddTorque_mEDE3483056FB07222A4D096F22D45C7D8A6E2552 (void);
// 0x0000004C System.Void UnityEngine.Rigidbody::AddTorque(UnityEngine.Vector3)
extern void Rigidbody_AddTorque_mAEB5758FA773B1A0ECDD328934BB3A7202D21EB3 (void);
// 0x0000004D System.Void UnityEngine.Rigidbody::AddTorque(System.Single,System.Single,System.Single,UnityEngine.ForceMode)
extern void Rigidbody_AddTorque_m181DAF83E45841384AC7E2E7DB83EC1747623DA4 (void);
// 0x0000004E System.Void UnityEngine.Rigidbody::AddTorque(System.Single,System.Single,System.Single)
extern void Rigidbody_AddTorque_m37159A78400CF4E39F460871437FC3E60CB60E48 (void);
// 0x0000004F System.Void UnityEngine.Rigidbody::AddRelativeTorque(UnityEngine.Vector3,UnityEngine.ForceMode)
extern void Rigidbody_AddRelativeTorque_m1C03D022550534C7C08BF4F8D7983EE8823A785B (void);
// 0x00000050 System.Void UnityEngine.Rigidbody::AddRelativeTorque(UnityEngine.Vector3)
extern void Rigidbody_AddRelativeTorque_m02D008355A3626183BF1B6D4451324FA045F3887 (void);
// 0x00000051 System.Void UnityEngine.Rigidbody::AddRelativeTorque(System.Single,System.Single,System.Single,UnityEngine.ForceMode)
extern void Rigidbody_AddRelativeTorque_m4C699C552995284B52445F59C398D55D4E2AE9E8 (void);
// 0x00000052 System.Void UnityEngine.Rigidbody::AddRelativeTorque(System.Single,System.Single,System.Single)
extern void Rigidbody_AddRelativeTorque_m088537EE9F7B75DC820FDFDE1CDCFCAAF7214CD9 (void);
// 0x00000053 System.Void UnityEngine.Rigidbody::AddForceAtPosition(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.ForceMode)
extern void Rigidbody_AddForceAtPosition_mEE49C058A6D57C1D5A78207494BFED5906D80D0F (void);
// 0x00000054 System.Void UnityEngine.Rigidbody::AddForceAtPosition(UnityEngine.Vector3,UnityEngine.Vector3)
extern void Rigidbody_AddForceAtPosition_m5190249D95CE1882B37481C5BFD2ABF201902BA5 (void);
// 0x00000055 System.Void UnityEngine.Rigidbody::AddExplosionForce(System.Single,UnityEngine.Vector3,System.Single,System.Single,UnityEngine.ForceMode)
extern void Rigidbody_AddExplosionForce_mA81BFBF84914CEA89D18047ADE14B47D171280DD (void);
// 0x00000056 System.Void UnityEngine.Rigidbody::AddExplosionForce(System.Single,UnityEngine.Vector3,System.Single,System.Single)
extern void Rigidbody_AddExplosionForce_m857BD26BDE42BFDC503AADE97899F94E92F6A6FC (void);
// 0x00000057 System.Void UnityEngine.Rigidbody::AddExplosionForce(System.Single,UnityEngine.Vector3,System.Single)
extern void Rigidbody_AddExplosionForce_m8B6FFD506CCCDD31C8C018A4B635E82B5499648F (void);
// 0x00000058 System.Void UnityEngine.Rigidbody::Internal_ClosestPointOnBounds(UnityEngine.Vector3,UnityEngine.Vector3&,System.Single&)
extern void Rigidbody_Internal_ClosestPointOnBounds_mA448D126F605336DC36AAA31237742A267A923D7 (void);
// 0x00000059 UnityEngine.Vector3 UnityEngine.Rigidbody::ClosestPointOnBounds(UnityEngine.Vector3)
extern void Rigidbody_ClosestPointOnBounds_m449FB43B807BDD87CB528311EA0E9EE7E46CFCAD (void);
// 0x0000005A UnityEngine.RaycastHit UnityEngine.Rigidbody::SweepTest(UnityEngine.Vector3,System.Single,UnityEngine.QueryTriggerInteraction,System.Boolean&)
extern void Rigidbody_SweepTest_m3CD0217FAA41AB8B51B7AA8CF4CBE6CFAFBBD9E2 (void);
// 0x0000005B System.Boolean UnityEngine.Rigidbody::SweepTest(UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single,UnityEngine.QueryTriggerInteraction)
extern void Rigidbody_SweepTest_mB96D6E932712DCABA13A2FF341511F2A525BD559 (void);
// 0x0000005C System.Boolean UnityEngine.Rigidbody::SweepTest(UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single)
extern void Rigidbody_SweepTest_m10E90C88C57072C4D093CD75909D3B0B9116D911 (void);
// 0x0000005D System.Boolean UnityEngine.Rigidbody::SweepTest(UnityEngine.Vector3,UnityEngine.RaycastHit&)
extern void Rigidbody_SweepTest_mDC7248518295568AD6FC8ED49168242EABD04918 (void);
// 0x0000005E UnityEngine.RaycastHit[] UnityEngine.Rigidbody::Internal_SweepTestAll(UnityEngine.Vector3,System.Single,UnityEngine.QueryTriggerInteraction)
extern void Rigidbody_Internal_SweepTestAll_mAF90D437ED68D4E1FCE3C993E45BCBBC7C1DD66A (void);
// 0x0000005F UnityEngine.RaycastHit[] UnityEngine.Rigidbody::SweepTestAll(UnityEngine.Vector3,System.Single,UnityEngine.QueryTriggerInteraction)
extern void Rigidbody_SweepTestAll_m1D341AB79E8097404255AC95C3C1F5AF8AB83EA5 (void);
// 0x00000060 UnityEngine.RaycastHit[] UnityEngine.Rigidbody::SweepTestAll(UnityEngine.Vector3,System.Single)
extern void Rigidbody_SweepTestAll_mB45AA1BEF24E98CF8D9150C2E1E107A6C81851D8 (void);
// 0x00000061 UnityEngine.RaycastHit[] UnityEngine.Rigidbody::SweepTestAll(UnityEngine.Vector3)
extern void Rigidbody_SweepTestAll_m50B229C454076D4CC2085E8F053B2ED419B314DC (void);
// 0x00000062 System.Void UnityEngine.Rigidbody::.ctor()
extern void Rigidbody__ctor_m0E43BA3B0E70E71B2CA62B165EE5B7CFAEFACDE9 (void);
// 0x00000063 System.Void UnityEngine.Rigidbody::get_velocity_Injected(UnityEngine.Vector3&)
extern void Rigidbody_get_velocity_Injected_m79C6BB8C054D0B5F03F3F13325910BC068E5B796 (void);
// 0x00000064 System.Void UnityEngine.Rigidbody::set_velocity_Injected(UnityEngine.Vector3&)
extern void Rigidbody_set_velocity_Injected_mBFBC7681B33942F69A5CD908941D399777F66ADD (void);
// 0x00000065 System.Void UnityEngine.Rigidbody::get_angularVelocity_Injected(UnityEngine.Vector3&)
extern void Rigidbody_get_angularVelocity_Injected_mD00F8790DFF2A31A033487AC67C4C018F28D0D13 (void);
// 0x00000066 System.Void UnityEngine.Rigidbody::set_angularVelocity_Injected(UnityEngine.Vector3&)
extern void Rigidbody_set_angularVelocity_Injected_m5ED47D0F131F6B3788B8B736DA7854FD63C13D56 (void);
// 0x00000067 System.Void UnityEngine.Rigidbody::get_centerOfMass_Injected(UnityEngine.Vector3&)
extern void Rigidbody_get_centerOfMass_Injected_m4350E78A3E7EC4B0B426A87824EC735462B9946E (void);
// 0x00000068 System.Void UnityEngine.Rigidbody::set_centerOfMass_Injected(UnityEngine.Vector3&)
extern void Rigidbody_set_centerOfMass_Injected_m60EC1BE2558EC9A9BD40ED144A1C848E6A4BCD9D (void);
// 0x00000069 System.Void UnityEngine.Rigidbody::get_worldCenterOfMass_Injected(UnityEngine.Vector3&)
extern void Rigidbody_get_worldCenterOfMass_Injected_m6FADF4899BDD58E674C0CDF09811505E42218E44 (void);
// 0x0000006A System.Void UnityEngine.Rigidbody::get_inertiaTensorRotation_Injected(UnityEngine.Quaternion&)
extern void Rigidbody_get_inertiaTensorRotation_Injected_m4147A057A803E19787F39B4B44F237D4E3D7B5F4 (void);
// 0x0000006B System.Void UnityEngine.Rigidbody::set_inertiaTensorRotation_Injected(UnityEngine.Quaternion&)
extern void Rigidbody_set_inertiaTensorRotation_Injected_m3EF9205426B41821EFEE2E9902792EEDC498A0C2 (void);
// 0x0000006C System.Void UnityEngine.Rigidbody::get_inertiaTensor_Injected(UnityEngine.Vector3&)
extern void Rigidbody_get_inertiaTensor_Injected_mF43CDE1538A2E5FCD06552A971D3828C12D45480 (void);
// 0x0000006D System.Void UnityEngine.Rigidbody::set_inertiaTensor_Injected(UnityEngine.Vector3&)
extern void Rigidbody_set_inertiaTensor_Injected_mB625F9A0523C4FF7AB51334B095B3284D6FF062C (void);
// 0x0000006E System.Void UnityEngine.Rigidbody::get_position_Injected(UnityEngine.Vector3&)
extern void Rigidbody_get_position_Injected_m16D551CF5A925BD26F4E77116483B2B36115A079 (void);
// 0x0000006F System.Void UnityEngine.Rigidbody::set_position_Injected(UnityEngine.Vector3&)
extern void Rigidbody_set_position_Injected_mC4BF2A57546741F3E26E82E988646AD1BB22D44E (void);
// 0x00000070 System.Void UnityEngine.Rigidbody::get_rotation_Injected(UnityEngine.Quaternion&)
extern void Rigidbody_get_rotation_Injected_mA9DA175CC81C9D6D4D7098C34CF5378C4C2955D8 (void);
// 0x00000071 System.Void UnityEngine.Rigidbody::set_rotation_Injected(UnityEngine.Quaternion&)
extern void Rigidbody_set_rotation_Injected_mBB08A477E5E134A4E6FFF381BFDE9E2959844EE9 (void);
// 0x00000072 System.Void UnityEngine.Rigidbody::MovePosition_Injected(UnityEngine.Vector3&)
extern void Rigidbody_MovePosition_Injected_m06454253A0DF550B2EAD47F545734E8735BA0732 (void);
// 0x00000073 System.Void UnityEngine.Rigidbody::MoveRotation_Injected(UnityEngine.Quaternion&)
extern void Rigidbody_MoveRotation_Injected_mB730FBD0786AE2DEC454E76326E08ED79CEEF440 (void);
// 0x00000074 System.Void UnityEngine.Rigidbody::GetRelativePointVelocity_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)
extern void Rigidbody_GetRelativePointVelocity_Injected_mAFF00EBA1B693EBFFF2E66480C87E40675C9482C (void);
// 0x00000075 System.Void UnityEngine.Rigidbody::GetPointVelocity_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)
extern void Rigidbody_GetPointVelocity_Injected_m24C6296C3FB436843956E904B02F907181867118 (void);
// 0x00000076 System.Void UnityEngine.Rigidbody::AddForce_Injected(UnityEngine.Vector3&,UnityEngine.ForceMode)
extern void Rigidbody_AddForce_Injected_m233C3E22C3FE9D2BCBBC510132B82CE26057370C (void);
// 0x00000077 System.Void UnityEngine.Rigidbody::AddRelativeForce_Injected(UnityEngine.Vector3&,UnityEngine.ForceMode)
extern void Rigidbody_AddRelativeForce_Injected_m2807F26D600ACF197910A90301FFD74A384775FD (void);
// 0x00000078 System.Void UnityEngine.Rigidbody::AddTorque_Injected(UnityEngine.Vector3&,UnityEngine.ForceMode)
extern void Rigidbody_AddTorque_Injected_mFFD31FDF8F82D3D740EA83348BBC0D0D5EB0DB3A (void);
// 0x00000079 System.Void UnityEngine.Rigidbody::AddRelativeTorque_Injected(UnityEngine.Vector3&,UnityEngine.ForceMode)
extern void Rigidbody_AddRelativeTorque_Injected_m0B2D9B655EE6D105CB658C788E52C8228EE195B0 (void);
// 0x0000007A System.Void UnityEngine.Rigidbody::AddForceAtPosition_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.ForceMode)
extern void Rigidbody_AddForceAtPosition_Injected_m60ED364228EF475F97B0FF1D0F4EFCAB97382DDB (void);
// 0x0000007B System.Void UnityEngine.Rigidbody::AddExplosionForce_Injected(System.Single,UnityEngine.Vector3&,System.Single,System.Single,UnityEngine.ForceMode)
extern void Rigidbody_AddExplosionForce_Injected_m8CCDC7FDD8F5F1BC231094105B3076815A16E22D (void);
// 0x0000007C System.Void UnityEngine.Rigidbody::Internal_ClosestPointOnBounds_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single&)
extern void Rigidbody_Internal_ClosestPointOnBounds_Injected_m717C820FF2668DB17C27A6EE470660BB8531D4A7 (void);
// 0x0000007D System.Void UnityEngine.Rigidbody::SweepTest_Injected(UnityEngine.Vector3&,System.Single,UnityEngine.QueryTriggerInteraction,System.Boolean&,UnityEngine.RaycastHit&)
extern void Rigidbody_SweepTest_Injected_mD4F788EFA83E0082B497F8FEA1A2985E058F3A8E (void);
// 0x0000007E UnityEngine.RaycastHit[] UnityEngine.Rigidbody::Internal_SweepTestAll_Injected(UnityEngine.Vector3&,System.Single,UnityEngine.QueryTriggerInteraction)
extern void Rigidbody_Internal_SweepTestAll_Injected_m8C341BB6B39648FDEC63B3743F45E4955877ABCC (void);
// 0x0000007F System.Boolean UnityEngine.Collider::get_enabled()
extern void Collider_get_enabled_m03B73B5C97033F939387D1785BDF2619CADAEEB0 (void);
// 0x00000080 System.Void UnityEngine.Collider::set_enabled(System.Boolean)
extern void Collider_set_enabled_m047B4D830755CD36671F7A60BFAA9C0D61F6C4A1 (void);
// 0x00000081 UnityEngine.Rigidbody UnityEngine.Collider::get_attachedRigidbody()
extern void Collider_get_attachedRigidbody_m101FED12AD292F372F98E94A6D02A5E428AA896A (void);
// 0x00000082 System.Boolean UnityEngine.Collider::get_isTrigger()
extern void Collider_get_isTrigger_m3A9C990365C94B7125DB5993D782D3D0FE876A60 (void);
// 0x00000083 System.Void UnityEngine.Collider::set_isTrigger(System.Boolean)
extern void Collider_set_isTrigger_mEDFE3DFA29D42E9DEB9D91A3D25BACC4470305ED (void);
// 0x00000084 System.Single UnityEngine.Collider::get_contactOffset()
extern void Collider_get_contactOffset_mBAB14623F6F9874C126201F785B39CB77CB0F77C (void);
// 0x00000085 System.Void UnityEngine.Collider::set_contactOffset(System.Single)
extern void Collider_set_contactOffset_m94DF399C14ACBA8C58924BAC2E20384664272139 (void);
// 0x00000086 UnityEngine.Vector3 UnityEngine.Collider::ClosestPoint(UnityEngine.Vector3)
extern void Collider_ClosestPoint_m7777917E298B31796DEE906B54F0102F6ED76676 (void);
// 0x00000087 UnityEngine.Bounds UnityEngine.Collider::get_bounds()
extern void Collider_get_bounds_mE341D29E1DA184ADD53A474D57D9082A3550EACB (void);
// 0x00000088 UnityEngine.PhysicMaterial UnityEngine.Collider::get_sharedMaterial()
extern void Collider_get_sharedMaterial_mF09CE26DD4F93A42DEA914489D8201B625EB61F5 (void);
// 0x00000089 System.Void UnityEngine.Collider::set_sharedMaterial(UnityEngine.PhysicMaterial)
extern void Collider_set_sharedMaterial_mAAB481A26A050DC39F7C160909D33971E50F3952 (void);
// 0x0000008A UnityEngine.PhysicMaterial UnityEngine.Collider::get_material()
extern void Collider_get_material_mD442FB26B6A328C13EF4C0091DB8900096B34979 (void);
// 0x0000008B System.Void UnityEngine.Collider::set_material(UnityEngine.PhysicMaterial)
extern void Collider_set_material_m3B07EBDE2756F6F250C6202EA1F67C95072B9D72 (void);
// 0x0000008C UnityEngine.RaycastHit UnityEngine.Collider::Raycast(UnityEngine.Ray,System.Single,System.Boolean&)
extern void Collider_Raycast_mEB5EDB2C67ABBA9929AE8A898660641E8C82E609 (void);
// 0x0000008D System.Boolean UnityEngine.Collider::Raycast(UnityEngine.Ray,UnityEngine.RaycastHit&,System.Single)
extern void Collider_Raycast_m41CA5C3C07B92F5325CB81890BE3A611C3C70784 (void);
// 0x0000008E System.Void UnityEngine.Collider::Internal_ClosestPointOnBounds(UnityEngine.Vector3,UnityEngine.Vector3&,System.Single&)
extern void Collider_Internal_ClosestPointOnBounds_m6A81164A787C9A96D7641C2C46F726AAD36DCB22 (void);
// 0x0000008F UnityEngine.Vector3 UnityEngine.Collider::ClosestPointOnBounds(UnityEngine.Vector3)
extern void Collider_ClosestPointOnBounds_m3AF12FC44A9FA016DE1AF04B5C281D48F9CC2FE1 (void);
// 0x00000090 System.Void UnityEngine.Collider::.ctor()
extern void Collider__ctor_m09D7A9B985D74FD50346DA08D88EB1874E968B69 (void);
// 0x00000091 System.Void UnityEngine.Collider::ClosestPoint_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)
extern void Collider_ClosestPoint_Injected_m6D72FF73D51838EE47234CB4D6234521C08B780D (void);
// 0x00000092 System.Void UnityEngine.Collider::get_bounds_Injected(UnityEngine.Bounds&)
extern void Collider_get_bounds_Injected_m9BA8C3BC133BC241D571849C4F10F67A951CA962 (void);
// 0x00000093 System.Void UnityEngine.Collider::Raycast_Injected(UnityEngine.Ray&,System.Single,System.Boolean&,UnityEngine.RaycastHit&)
extern void Collider_Raycast_Injected_m7176D9E8617D67331E20F519855B8708C63B9E9E (void);
// 0x00000094 System.Void UnityEngine.Collider::Internal_ClosestPointOnBounds_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single&)
extern void Collider_Internal_ClosestPointOnBounds_Injected_mEE9EB425CB119C0E3FAFE788E7FA20E418DFBB82 (void);
// 0x00000095 UnityEngine.CollisionFlags UnityEngine.CharacterController::Move(UnityEngine.Vector3)
extern void CharacterController_Move_mE0EBC32C72A0BEC18EDEBE748D44309A4BA32E60 (void);
// 0x00000096 System.Boolean UnityEngine.CharacterController::get_isGrounded()
extern void CharacterController_get_isGrounded_m327A1A1940F225FE81E751F255316BB0D8698CBC (void);
// 0x00000097 UnityEngine.CollisionFlags UnityEngine.CharacterController::Move_Injected(UnityEngine.Vector3&)
extern void CharacterController_Move_Injected_m6A2168A4CDC70CB62E4A4DCB15A74133E8C6C46D (void);
// 0x00000098 UnityEngine.Vector3 UnityEngine.BoxCollider::get_center()
extern void BoxCollider_get_center_m832B93439717C72D4A3B427C6E8F5B54E2DBD669 (void);
// 0x00000099 System.Void UnityEngine.BoxCollider::set_center(UnityEngine.Vector3)
extern void BoxCollider_set_center_m02D745E759A66BBEF405D33CE4ACE34B7E064178 (void);
// 0x0000009A UnityEngine.Vector3 UnityEngine.BoxCollider::get_size()
extern void BoxCollider_get_size_mBC38D4926D4BE54A6532F6E1D642F363CA3A58A1 (void);
// 0x0000009B System.Void UnityEngine.BoxCollider::set_size(UnityEngine.Vector3)
extern void BoxCollider_set_size_mD9153B4AE4C366ACAB9E5F49075D919A89168B2E (void);
// 0x0000009C System.Void UnityEngine.BoxCollider::get_center_Injected(UnityEngine.Vector3&)
extern void BoxCollider_get_center_Injected_m5F060E93AE651FB756B9371C7A716BDAA2A96F51 (void);
// 0x0000009D System.Void UnityEngine.BoxCollider::set_center_Injected(UnityEngine.Vector3&)
extern void BoxCollider_set_center_Injected_m54F8740514B14E3B53063FB4C7623BF0F2A48F0A (void);
// 0x0000009E System.Void UnityEngine.BoxCollider::get_size_Injected(UnityEngine.Vector3&)
extern void BoxCollider_get_size_Injected_mC9ABFED6FEC27351BC116B44021578CB39CBCB22 (void);
// 0x0000009F System.Void UnityEngine.BoxCollider::set_size_Injected(UnityEngine.Vector3&)
extern void BoxCollider_set_size_Injected_m7A8B807F4AB573BF955610F55AAD372FF868C967 (void);
// 0x000000A0 System.Single UnityEngine.SphereCollider::get_radius()
extern void SphereCollider_get_radius_m403989140BDAD513299276953B481167CF08D02F (void);
// 0x000000A1 System.Void UnityEngine.SphereCollider::set_radius(System.Single)
extern void SphereCollider_set_radius_m55A0D144B32871AECC2A83FBCF423FBE1E5A63A0 (void);
// 0x000000A2 UnityEngine.Rigidbody UnityEngine.Joint::get_connectedBody()
extern void Joint_get_connectedBody_m35ED1900CACED65F78E5D612BE8979142CBA68A5 (void);
// 0x000000A3 System.Void UnityEngine.Joint::set_connectedBody(UnityEngine.Rigidbody)
extern void Joint_set_connectedBody_m572C6C32E2FC5263AECDC460D50E5B0F79727B30 (void);
// 0x000000A4 System.Void UnityEngine.Joint::set_axis(UnityEngine.Vector3)
extern void Joint_set_axis_m185064B312585680B9384D0B6CDC912646C1503B (void);
// 0x000000A5 UnityEngine.Vector3 UnityEngine.Joint::get_connectedAnchor()
extern void Joint_get_connectedAnchor_m63E98DACC749ED8F0660FB7998B979CD995AE7BB (void);
// 0x000000A6 System.Void UnityEngine.Joint::set_connectedAnchor(UnityEngine.Vector3)
extern void Joint_set_connectedAnchor_mE47E99BDFB177E3CAAEFEAD0EF119DBAAF1D94CE (void);
// 0x000000A7 System.Void UnityEngine.Joint::set_enableCollision(System.Boolean)
extern void Joint_set_enableCollision_mF0E7F8D20E2DD89870C2A57FBE19ED34FA3E5E11 (void);
// 0x000000A8 System.Void UnityEngine.Joint::set_axis_Injected(UnityEngine.Vector3&)
extern void Joint_set_axis_Injected_m625AB8D052B7B9E62C699A8855A9CE4AB734F713 (void);
// 0x000000A9 System.Void UnityEngine.Joint::get_connectedAnchor_Injected(UnityEngine.Vector3&)
extern void Joint_get_connectedAnchor_Injected_mB79FC8C3EB1414C24CAABFDF28C1C8E016403F3A (void);
// 0x000000AA System.Void UnityEngine.Joint::set_connectedAnchor_Injected(UnityEngine.Vector3&)
extern void Joint_set_connectedAnchor_Injected_mA7EAFFA89F0D2ECBA30BB8816423FE33C1A83247 (void);
// 0x000000AB System.Void UnityEngine.HingeJoint::set_limits(UnityEngine.JointLimits)
extern void HingeJoint_set_limits_m53D8107DCC9A2C46EBB3B3FB1E1C1B33B9F6CCC4 (void);
// 0x000000AC System.Void UnityEngine.HingeJoint::set_useLimits(System.Boolean)
extern void HingeJoint_set_useLimits_m7E10377C2CBA985B9FC737E04673099E1A758DEC (void);
// 0x000000AD System.Void UnityEngine.HingeJoint::set_limits_Injected(UnityEngine.JointLimits&)
extern void HingeJoint_set_limits_Injected_mC260E29EF806543F485B4BAB31AEB61C4357FB42 (void);
// 0x000000AE System.String UnityEngine.PhysicsScene::ToString()
extern void PhysicsScene_ToString_mBB6D0AC1E3E2EDC34CB4A7A34485B24B6271903F (void);
// 0x000000AF System.Int32 UnityEngine.PhysicsScene::GetHashCode()
extern void PhysicsScene_GetHashCode_m5344CC6CCCB1CA6EBB149775EE028089DD74B8A2 (void);
// 0x000000B0 System.Boolean UnityEngine.PhysicsScene::Equals(System.Object)
extern void PhysicsScene_Equals_m2645ABB96C598F197F734EB712494795928CBCEC (void);
// 0x000000B1 System.Boolean UnityEngine.PhysicsScene::Equals(UnityEngine.PhysicsScene)
extern void PhysicsScene_Equals_m8C948B86D105177D519A3608816CDC698C8686B8 (void);
// 0x000000B2 System.Boolean UnityEngine.PhysicsScene::Raycast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Raycast_m8C3E61EB7C3DB8AAC6FEB098D815062BEF821187 (void);
// 0x000000B3 System.Boolean UnityEngine.PhysicsScene::Internal_RaycastTest(UnityEngine.PhysicsScene,UnityEngine.Ray,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_RaycastTest_m2FA574D023348CBE214F4F594C9AC155DFB2AD72 (void);
// 0x000000B4 System.Boolean UnityEngine.PhysicsScene::Raycast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Raycast_mFC0D59C4439EE9DA6E8AA5F6891915132D587208 (void);
// 0x000000B5 System.Boolean UnityEngine.PhysicsScene::Internal_Raycast(UnityEngine.PhysicsScene,UnityEngine.Ray,System.Single,UnityEngine.RaycastHit&,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_Raycast_m74B409FBA4F2E010CB5668558C70231C656EF249 (void);
// 0x000000B6 System.Int32 UnityEngine.PhysicsScene::Raycast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Raycast_m95CAB68EB9165E3AB2A4D441F7DF9767AD4B7F73 (void);
// 0x000000B7 System.Int32 UnityEngine.PhysicsScene::Internal_RaycastNonAlloc(UnityEngine.PhysicsScene,UnityEngine.Ray,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_RaycastNonAlloc_mAF1F46E294C23325F3BE18D69987473CB94DE24C (void);
// 0x000000B8 System.Boolean UnityEngine.PhysicsScene::Query_CapsuleCast(UnityEngine.PhysicsScene,UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,System.Single,UnityEngine.RaycastHit&,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Query_CapsuleCast_mC84C883774E53A1C1E16BCF78AC97A4ECDA63495 (void);
// 0x000000B9 System.Boolean UnityEngine.PhysicsScene::Internal_CapsuleCast(UnityEngine.PhysicsScene,UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_CapsuleCast_mAC8303A37BE78D40A38ED2C43596AE86CDEF7402 (void);
// 0x000000BA System.Boolean UnityEngine.PhysicsScene::CapsuleCast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_CapsuleCast_mE4112B782763A75FE885111A8C45F0B74C2CB784 (void);
// 0x000000BB System.Int32 UnityEngine.PhysicsScene::Internal_CapsuleCastNonAlloc(UnityEngine.PhysicsScene,UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_CapsuleCastNonAlloc_m85C990028A307ACEDD117987D1195702D6D17A6F (void);
// 0x000000BC System.Int32 UnityEngine.PhysicsScene::CapsuleCast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_CapsuleCast_m16EF2FF9A1944C84082EEC798FE7339A8A78912C (void);
// 0x000000BD System.Int32 UnityEngine.PhysicsScene::OverlapCapsuleNonAlloc_Internal(UnityEngine.PhysicsScene,UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Collider[],System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_OverlapCapsuleNonAlloc_Internal_m3644F81F43A7CD947854EED502D95B05ED4CBF24 (void);
// 0x000000BE System.Int32 UnityEngine.PhysicsScene::OverlapCapsule(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Collider[],System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_OverlapCapsule_mF3E09C4D0CE2D752FB53995D04E1558D8FF69F84 (void);
// 0x000000BF System.Boolean UnityEngine.PhysicsScene::Query_SphereCast(UnityEngine.PhysicsScene,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,System.Single,UnityEngine.RaycastHit&,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Query_SphereCast_m5150D76348F358DA3D88B812F52747A5B0AC10CF (void);
// 0x000000C0 System.Boolean UnityEngine.PhysicsScene::Internal_SphereCast(UnityEngine.PhysicsScene,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_SphereCast_mBC10DBEC20A03FE3F8B0705EE891F5DE019BAA49 (void);
// 0x000000C1 System.Boolean UnityEngine.PhysicsScene::SphereCast(UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_SphereCast_m72928994362EE11F9A4F72BD79954444B9FD1C2D (void);
// 0x000000C2 System.Int32 UnityEngine.PhysicsScene::Internal_SphereCastNonAlloc(UnityEngine.PhysicsScene,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_SphereCastNonAlloc_mBF2B70F0518C54C87CF119C1592CE2D74C9110EC (void);
// 0x000000C3 System.Int32 UnityEngine.PhysicsScene::SphereCast(UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_SphereCast_m36723F888676E712CC40A0E54D72E95F73D193D3 (void);
// 0x000000C4 System.Int32 UnityEngine.PhysicsScene::OverlapSphereNonAlloc_Internal(UnityEngine.PhysicsScene,UnityEngine.Vector3,System.Single,UnityEngine.Collider[],System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_OverlapSphereNonAlloc_Internal_m6CCFA59110DAACD0B5150140E221278715A89C84 (void);
// 0x000000C5 System.Int32 UnityEngine.PhysicsScene::OverlapSphere(UnityEngine.Vector3,System.Single,UnityEngine.Collider[],System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_OverlapSphere_mBFBB0C810534D52967EE4FDC97DB67301D769ACC (void);
// 0x000000C6 System.Boolean UnityEngine.PhysicsScene::Query_BoxCast(UnityEngine.PhysicsScene,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Quaternion,System.Single,UnityEngine.RaycastHit&,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Query_BoxCast_m25D4BB18A9C4B77B2F2EB6C603948D3B492F8ABD (void);
// 0x000000C7 System.Boolean UnityEngine.PhysicsScene::Internal_BoxCast(UnityEngine.PhysicsScene,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Quaternion,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_BoxCast_m4E15ED6F445822250915FA7A58A1F91572419F86 (void);
// 0x000000C8 System.Boolean UnityEngine.PhysicsScene::BoxCast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&,UnityEngine.Quaternion,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_BoxCast_mAE5E55D4420B36B8A6678D420411F29D5687DCEC (void);
// 0x000000C9 System.Int32 UnityEngine.PhysicsScene::OverlapBoxNonAlloc_Internal(UnityEngine.PhysicsScene,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Collider[],UnityEngine.Quaternion,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_OverlapBoxNonAlloc_Internal_m4835D863262589F253CEDC6347CBA1949E68E6A0 (void);
// 0x000000CA System.Int32 UnityEngine.PhysicsScene::OverlapBox(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Collider[],UnityEngine.Quaternion,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_OverlapBox_mCC9E06BCEE764C2C0A9F2C544A69B996BB37CF27 (void);
// 0x000000CB System.Int32 UnityEngine.PhysicsScene::Internal_BoxCastNonAlloc(UnityEngine.PhysicsScene,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit[],UnityEngine.Quaternion,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_BoxCastNonAlloc_m3AEAE41B64528896C2001D5B8FA9A53B3A01D324 (void);
// 0x000000CC System.Int32 UnityEngine.PhysicsScene::BoxCast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit[],UnityEngine.Quaternion,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_BoxCast_mDD8A02400064E72BF99C00DE9DFFA29087CC7C4A (void);
// 0x000000CD System.Boolean UnityEngine.PhysicsScene::Internal_RaycastTest_Injected(UnityEngine.PhysicsScene&,UnityEngine.Ray&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_RaycastTest_Injected_mD9CAD732E985F53D637AF9CCDF5DB8BC34F8FC29 (void);
// 0x000000CE System.Boolean UnityEngine.PhysicsScene::Internal_Raycast_Injected(UnityEngine.PhysicsScene&,UnityEngine.Ray&,System.Single,UnityEngine.RaycastHit&,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_Raycast_Injected_mC82D984CE2303F04316EBBCD5313051B1D34B6B4 (void);
// 0x000000CF System.Int32 UnityEngine.PhysicsScene::Internal_RaycastNonAlloc_Injected(UnityEngine.PhysicsScene&,UnityEngine.Ray&,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_RaycastNonAlloc_Injected_mC9CFA581B636F4DF370330671BB1E4B3ACC2B45B (void);
// 0x000000D0 System.Boolean UnityEngine.PhysicsScene::Query_CapsuleCast_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,UnityEngine.Vector3&,System.Single,UnityEngine.RaycastHit&,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Query_CapsuleCast_Injected_m0ADF6977DFF6ECB5E9AA1F89168BD303EE88BA8A (void);
// 0x000000D1 System.Int32 UnityEngine.PhysicsScene::Internal_CapsuleCastNonAlloc_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,UnityEngine.Vector3&,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_CapsuleCastNonAlloc_Injected_m0A4E0190481F3E7955DBEB8DEB54224837041EF2 (void);
// 0x000000D2 System.Int32 UnityEngine.PhysicsScene::OverlapCapsuleNonAlloc_Internal_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,UnityEngine.Collider[],System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_OverlapCapsuleNonAlloc_Internal_Injected_m52E4D03F7C223E812DE419E4A9E8187199AFB0B6 (void);
// 0x000000D3 System.Boolean UnityEngine.PhysicsScene::Query_SphereCast_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,System.Single,UnityEngine.Vector3&,System.Single,UnityEngine.RaycastHit&,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Query_SphereCast_Injected_m7E28C57614720C5F0E83AFC3589696D96C477105 (void);
// 0x000000D4 System.Int32 UnityEngine.PhysicsScene::Internal_SphereCastNonAlloc_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,System.Single,UnityEngine.Vector3&,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_SphereCastNonAlloc_Injected_m65852DE0EC1AEDB7779B1853A4AD89CB2052DC0D (void);
// 0x000000D5 System.Int32 UnityEngine.PhysicsScene::OverlapSphereNonAlloc_Internal_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,System.Single,UnityEngine.Collider[],System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_OverlapSphereNonAlloc_Internal_Injected_mA640B629980F886775675074E03F59AD2EEC2C9C (void);
// 0x000000D6 System.Boolean UnityEngine.PhysicsScene::Query_BoxCast_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Quaternion&,System.Single,UnityEngine.RaycastHit&,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Query_BoxCast_Injected_mCD6D0B2A284845FA7D43EC7B2E77F52B7B284681 (void);
// 0x000000D7 System.Int32 UnityEngine.PhysicsScene::OverlapBoxNonAlloc_Internal_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Collider[],UnityEngine.Quaternion&,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_OverlapBoxNonAlloc_Internal_Injected_m76D78AF795A0DC08207701B6573712967A13338B (void);
// 0x000000D8 System.Int32 UnityEngine.PhysicsScene::Internal_BoxCastNonAlloc_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.RaycastHit[],UnityEngine.Quaternion&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void PhysicsScene_Internal_BoxCastNonAlloc_Injected_mD53C95CED27205E43EA37253B5BB179777EFCACB (void);
// 0x000000D9 UnityEngine.Vector3 UnityEngine.Physics::get_gravity()
extern void Physics_get_gravity_mCF549D37AAB738732815F2E45DB47552061571EF (void);
// 0x000000DA System.Void UnityEngine.Physics::set_gravity(UnityEngine.Vector3)
extern void Physics_set_gravity_mA651A465DC97AC2D6C5846AF91AFB62F2594DF85 (void);
// 0x000000DB System.Single UnityEngine.Physics::get_defaultContactOffset()
extern void Physics_get_defaultContactOffset_m4A5BF641B9187316F95FCF65CEBEFD3786BDFBF4 (void);
// 0x000000DC System.Void UnityEngine.Physics::set_defaultContactOffset(System.Single)
extern void Physics_set_defaultContactOffset_m1ED6925D6DDAD60D1BA7A643D9718B214D02E129 (void);
// 0x000000DD System.Single UnityEngine.Physics::get_sleepThreshold()
extern void Physics_get_sleepThreshold_m97ECEAB5C22071AB0219488CA7C7CBD78CA41B1F (void);
// 0x000000DE System.Void UnityEngine.Physics::set_sleepThreshold(System.Single)
extern void Physics_set_sleepThreshold_m40D2DBD5A5B45FF93F1A7EB17F65D77728D729E0 (void);
// 0x000000DF System.Boolean UnityEngine.Physics::get_queriesHitTriggers()
extern void Physics_get_queriesHitTriggers_mBAA08B9F06EC19C8A0647556EE7CFA459D36E887 (void);
// 0x000000E0 System.Void UnityEngine.Physics::set_queriesHitTriggers(System.Boolean)
extern void Physics_set_queriesHitTriggers_m3B2B28824562A6CB94BE472038BFE4F234FF641C (void);
// 0x000000E1 System.Boolean UnityEngine.Physics::get_queriesHitBackfaces()
extern void Physics_get_queriesHitBackfaces_m29273BEEDF63D1148338F9EF9CC2D3E08A643C0F (void);
// 0x000000E2 System.Void UnityEngine.Physics::set_queriesHitBackfaces(System.Boolean)
extern void Physics_set_queriesHitBackfaces_m8D34CD58EF94CFCD569778970F13E3EE9B3F836B (void);
// 0x000000E3 System.Single UnityEngine.Physics::get_bounceThreshold()
extern void Physics_get_bounceThreshold_m420A9C0CDDE0D681F01D4E53BA5ACB94722D69AA (void);
// 0x000000E4 System.Void UnityEngine.Physics::set_bounceThreshold(System.Single)
extern void Physics_set_bounceThreshold_m238F8AC404A1D80055C456353C915BC59E095397 (void);
// 0x000000E5 System.Int32 UnityEngine.Physics::get_defaultSolverIterations()
extern void Physics_get_defaultSolverIterations_mEBF50C28729C00A527F3A43C169ACAE53C327DD6 (void);
// 0x000000E6 System.Void UnityEngine.Physics::set_defaultSolverIterations(System.Int32)
extern void Physics_set_defaultSolverIterations_m0054664B7BE9957DAF84E9DDD27B35CFB717104E (void);
// 0x000000E7 System.Int32 UnityEngine.Physics::get_defaultSolverVelocityIterations()
extern void Physics_get_defaultSolverVelocityIterations_m0FF2EFD6CA19B89614FA753C88CF206D27DF946C (void);
// 0x000000E8 System.Void UnityEngine.Physics::set_defaultSolverVelocityIterations(System.Int32)
extern void Physics_set_defaultSolverVelocityIterations_m8DA5B46039E09805F3B5C14BC86717673BD2D081 (void);
// 0x000000E9 System.Single UnityEngine.Physics::get_defaultMaxAngularSpeed()
extern void Physics_get_defaultMaxAngularSpeed_m2D2136434C45AFF227A2DB2A973785A62076DA93 (void);
// 0x000000EA System.Void UnityEngine.Physics::set_defaultMaxAngularSpeed(System.Single)
extern void Physics_set_defaultMaxAngularSpeed_m3CDDEBDFE971F4798B52075D16A0EC2C0E2E46D6 (void);
// 0x000000EB UnityEngine.PhysicsScene UnityEngine.Physics::get_defaultPhysicsScene()
extern void Physics_get_defaultPhysicsScene_m288502B1B143487DF1448AC572A68D3B28FB2BF4 (void);
// 0x000000EC System.Void UnityEngine.Physics::IgnoreCollision(UnityEngine.Collider,UnityEngine.Collider,System.Boolean)
extern void Physics_IgnoreCollision_m7B4B87FA8DAE966DB15A6825652452ADAB43BEB8 (void);
// 0x000000ED System.Void UnityEngine.Physics::IgnoreCollision(UnityEngine.Collider,UnityEngine.Collider)
extern void Physics_IgnoreCollision_m769C875FBAD8A0DB42F7A69B79E88C71DA0F1063 (void);
// 0x000000EE System.Void UnityEngine.Physics::IgnoreLayerCollision(System.Int32,System.Int32,System.Boolean)
extern void Physics_IgnoreLayerCollision_m99FDB1D2B4F7724B13A2C09E47525A251316C148 (void);
// 0x000000EF System.Void UnityEngine.Physics::IgnoreLayerCollision(System.Int32,System.Int32)
extern void Physics_IgnoreLayerCollision_m4F9369FFB51FF3E5A019D242CCF50FBFEE318ECC (void);
// 0x000000F0 System.Boolean UnityEngine.Physics::GetIgnoreLayerCollision(System.Int32,System.Int32)
extern void Physics_GetIgnoreLayerCollision_m8F3D7439829F66E2492184848C24A9A9D0D53CFC (void);
// 0x000000F1 System.Boolean UnityEngine.Physics::GetIgnoreCollision(UnityEngine.Collider,UnityEngine.Collider)
extern void Physics_GetIgnoreCollision_m336E3E9FA9352CF75D6086790662DEB1D36CFA8F (void);
// 0x000000F2 System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_Raycast_mC2ED77DB34C975B8A8864A8799B844D44800E8FE (void);
// 0x000000F3 System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Int32)
extern void Physics_Raycast_m8B96F9B0424A82607EED1B73D708B56E2E61ACD0 (void);
// 0x000000F4 System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single)
extern void Physics_Raycast_m1F314F0281C2AA6B467EB66931B95F0A92C55708 (void);
// 0x000000F5 System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Vector3,UnityEngine.Vector3)
extern void Physics_Raycast_m7B0F0A428505EE139EF5F36BE2E2463A4A6C1C95 (void);
// 0x000000F6 System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_Raycast_m9DC5CC69286EA72DB6CEC663A5F938080831CED6 (void);
// 0x000000F7 System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single,System.Int32)
extern void Physics_Raycast_mE9A6DAE4102F053486651A3BF1F3C8B0F4CD7A32 (void);
// 0x000000F8 System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single)
extern void Physics_Raycast_m43E2A84B040AAAFC9A9F436C6A04573A83B997C7 (void);
// 0x000000F9 System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&)
extern void Physics_Raycast_m1E3A92D5F7F0003A8ED720CD5755B0FEB12BD8DB (void);
// 0x000000FA System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Ray,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_Raycast_m8288105D3EC843C79ED946886D58232BF9140E38 (void);
// 0x000000FB System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Ray,System.Single,System.Int32)
extern void Physics_Raycast_mDE7B06FE5C7BDA1976A3D5C458133E2118AF7D0A (void);
// 0x000000FC System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Ray,System.Single)
extern void Physics_Raycast_mF3D9C6730E052E32589B7392D041A21BCE79FBA8 (void);
// 0x000000FD System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Ray)
extern void Physics_Raycast_m0CC2DCC02C393A610EA468445B5357402CF4B23B (void);
// 0x000000FE System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Ray,UnityEngine.RaycastHit&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_Raycast_m995228D47F1AA54815D5271D1D739449F7CF39D6 (void);
// 0x000000FF System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Ray,UnityEngine.RaycastHit&,System.Single,System.Int32)
extern void Physics_Raycast_mAA9D393D6D057BF7F08BA93E904295214E1F8CCC (void);
// 0x00000100 System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Ray,UnityEngine.RaycastHit&,System.Single)
extern void Physics_Raycast_mF473DC2E69B2A4E013F03867CE044FB9901565C6 (void);
// 0x00000101 System.Boolean UnityEngine.Physics::Raycast(UnityEngine.Ray,UnityEngine.RaycastHit&)
extern void Physics_Raycast_m4BD6E5347507B86D0D50C5834607BB23D3DBD82A (void);
// 0x00000102 System.Boolean UnityEngine.Physics::Linecast(UnityEngine.Vector3,UnityEngine.Vector3,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_Linecast_mD175C9F520F54D09AA3432A71D80A5FC643DAD09 (void);
// 0x00000103 System.Boolean UnityEngine.Physics::Linecast(UnityEngine.Vector3,UnityEngine.Vector3,System.Int32)
extern void Physics_Linecast_mCFA4ADE87BFF8EBEDC892DC05768235100899459 (void);
// 0x00000104 System.Boolean UnityEngine.Physics::Linecast(UnityEngine.Vector3,UnityEngine.Vector3)
extern void Physics_Linecast_m194E27535A8948EAD7F1AE58522344522E2CB414 (void);
// 0x00000105 System.Boolean UnityEngine.Physics::Linecast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_Linecast_m95E69EC3D42F8D5362590118844045949D360F12 (void);
// 0x00000106 System.Boolean UnityEngine.Physics::Linecast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Int32)
extern void Physics_Linecast_m2F66BEABC1E34AFC3F9717DCB4F1915CE3546731 (void);
// 0x00000107 System.Boolean UnityEngine.Physics::Linecast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&)
extern void Physics_Linecast_m98AF5B5C9E77E3B681C8974A490B4218F502719B (void);
// 0x00000108 System.Boolean UnityEngine.Physics::CapsuleCast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_CapsuleCast_mD00B6D94F44E25202B741D283891BCB1138B6A8E (void);
// 0x00000109 System.Boolean UnityEngine.Physics::CapsuleCast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,System.Single,System.Int32)
extern void Physics_CapsuleCast_m28FCEE4A3DF8A68E9C6F248A0AC4287936D697A7 (void);
// 0x0000010A System.Boolean UnityEngine.Physics::CapsuleCast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,System.Single)
extern void Physics_CapsuleCast_m30A116B9E18E5F3517BDC4478CFEB1ED973DA643 (void);
// 0x0000010B System.Boolean UnityEngine.Physics::CapsuleCast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3)
extern void Physics_CapsuleCast_m0BDCBBA11A3479147AF50CFA204AA86F244CA178 (void);
// 0x0000010C System.Boolean UnityEngine.Physics::CapsuleCast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_CapsuleCast_m7A44E2160394EDC5C08A47B143DADE088C5A8E4C (void);
// 0x0000010D System.Boolean UnityEngine.Physics::CapsuleCast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single,System.Int32)
extern void Physics_CapsuleCast_mFC4A2A611CF0B360297E8D87AF8E5A7A2D6315A2 (void);
// 0x0000010E System.Boolean UnityEngine.Physics::CapsuleCast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single)
extern void Physics_CapsuleCast_m477D45784AE0F4CC74192C7C63001B11663CDD89 (void);
// 0x0000010F System.Boolean UnityEngine.Physics::CapsuleCast(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit&)
extern void Physics_CapsuleCast_mB0AA4D1A09551AA956D69996AC9C2D945C2AAD23 (void);
// 0x00000110 System.Boolean UnityEngine.Physics::SphereCast(UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_SphereCast_mC123DF7428845FC3112819137F950D29B42A2382 (void);
// 0x00000111 System.Boolean UnityEngine.Physics::SphereCast(UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single,System.Int32)
extern void Physics_SphereCast_m01F30F8108E427351C03CC12B782ADAD289E5D60 (void);
// 0x00000112 System.Boolean UnityEngine.Physics::SphereCast(UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit&,System.Single)
extern void Physics_SphereCast_mCAEAA10F0E51A8C5AA11B5F7B35C3914394B7B0A (void);
// 0x00000113 System.Boolean UnityEngine.Physics::SphereCast(UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit&)
extern void Physics_SphereCast_mF0DC9AD714B54734965733FE4AA490E6D07F1D93 (void);
// 0x00000114 System.Boolean UnityEngine.Physics::SphereCast(UnityEngine.Ray,System.Single,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_SphereCast_mBEBFBC17AB2E31C665C19B1DB778C2385E62B0D5 (void);
// 0x00000115 System.Boolean UnityEngine.Physics::SphereCast(UnityEngine.Ray,System.Single,System.Single,System.Int32)
extern void Physics_SphereCast_m3840EDBB05C4844D16A1A30449A00652220061E7 (void);
// 0x00000116 System.Boolean UnityEngine.Physics::SphereCast(UnityEngine.Ray,System.Single,System.Single)
extern void Physics_SphereCast_mEFCE461C3C125942FCA06724D158873FE8A9F314 (void);
// 0x00000117 System.Boolean UnityEngine.Physics::SphereCast(UnityEngine.Ray,System.Single)
extern void Physics_SphereCast_m1C39876096F8D1FDBBF2D41E54E02FADF09C5841 (void);
// 0x00000118 System.Boolean UnityEngine.Physics::SphereCast(UnityEngine.Ray,System.Single,UnityEngine.RaycastHit&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_SphereCast_m670A7A5DE3A132CFE61DCEDC3E664630E0FAF619 (void);
// 0x00000119 System.Boolean UnityEngine.Physics::SphereCast(UnityEngine.Ray,System.Single,UnityEngine.RaycastHit&,System.Single,System.Int32)
extern void Physics_SphereCast_mD46CFBE396789673EF42AC03F26326A8C133229E (void);
// 0x0000011A System.Boolean UnityEngine.Physics::SphereCast(UnityEngine.Ray,System.Single,UnityEngine.RaycastHit&,System.Single)
extern void Physics_SphereCast_m9ED6FDBE7726A67E713EE768ABDE60268D19F420 (void);
// 0x0000011B System.Boolean UnityEngine.Physics::SphereCast(UnityEngine.Ray,System.Single,UnityEngine.RaycastHit&)
extern void Physics_SphereCast_m5901E70B4124F6510FFC7E077C5BF4143B739585 (void);
// 0x0000011C System.Boolean UnityEngine.Physics::BoxCast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Quaternion,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_BoxCast_mD64DA9359F8E3E76CC3F42CC7FEA1EA0CA79CC81 (void);
// 0x0000011D System.Boolean UnityEngine.Physics::BoxCast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Quaternion,System.Single,System.Int32)
extern void Physics_BoxCast_m042D832EAA9ACD0DAD3A8329E03951BC249EB05A (void);
// 0x0000011E System.Boolean UnityEngine.Physics::BoxCast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Quaternion,System.Single)
extern void Physics_BoxCast_mBB6CBDA0AD8BB036B4C7582F3DD9B1027BF6978B (void);
// 0x0000011F System.Boolean UnityEngine.Physics::BoxCast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Quaternion)
extern void Physics_BoxCast_mFFBFFC3C48436EA4D4B5F4AC447A42015F170B74 (void);
// 0x00000120 System.Boolean UnityEngine.Physics::BoxCast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3)
extern void Physics_BoxCast_mA48BAA61954ABE2C1A216EEF05C118B24D8A2EF5 (void);
// 0x00000121 System.Boolean UnityEngine.Physics::BoxCast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&,UnityEngine.Quaternion,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_BoxCast_m0CB099A9DF4E018CA2577889001B823066C3D9AA (void);
// 0x00000122 System.Boolean UnityEngine.Physics::BoxCast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&,UnityEngine.Quaternion,System.Single,System.Int32)
extern void Physics_BoxCast_m727075A9E9CC0D62D85F400F5B103171EC4A320F (void);
// 0x00000123 System.Boolean UnityEngine.Physics::BoxCast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&,UnityEngine.Quaternion,System.Single)
extern void Physics_BoxCast_mAA9130AED2A87AD6CD8BE207388214DBC182B1B3 (void);
// 0x00000124 System.Boolean UnityEngine.Physics::BoxCast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&,UnityEngine.Quaternion)
extern void Physics_BoxCast_m79A26C19C10E0F725DE832DE8F72F2AA6E28687F (void);
// 0x00000125 System.Boolean UnityEngine.Physics::BoxCast(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit&)
extern void Physics_BoxCast_m55051B35127A00F2C9652895A09C5D34EF47FA13 (void);
// 0x00000126 UnityEngine.RaycastHit[] UnityEngine.Physics::Internal_RaycastAll(UnityEngine.PhysicsScene,UnityEngine.Ray,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_Internal_RaycastAll_m9DC824664677D74824150C2ABC56DDE36727DC26 (void);
// 0x00000127 UnityEngine.RaycastHit[] UnityEngine.Physics::RaycastAll(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_RaycastAll_m50B811C17026AE8F23D5171E588F0DA77BE9C5FB (void);
// 0x00000128 UnityEngine.RaycastHit[] UnityEngine.Physics::RaycastAll(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Int32)
extern void Physics_RaycastAll_m4230866EDA233176EE4074CA63C69B8B8B672181 (void);
// 0x00000129 UnityEngine.RaycastHit[] UnityEngine.Physics::RaycastAll(UnityEngine.Vector3,UnityEngine.Vector3,System.Single)
extern void Physics_RaycastAll_m0A1AE92D5D3B597D09E76F11F1D1C09493F4C0E8 (void);
// 0x0000012A UnityEngine.RaycastHit[] UnityEngine.Physics::RaycastAll(UnityEngine.Vector3,UnityEngine.Vector3)
extern void Physics_RaycastAll_m14F828FD2B643D48B6A2C8C78C978826989C07B4 (void);
// 0x0000012B UnityEngine.RaycastHit[] UnityEngine.Physics::RaycastAll(UnityEngine.Ray,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_RaycastAll_m02B60871145F3BB5CA7D555A6C0C157EF0251BC6 (void);
// 0x0000012C UnityEngine.RaycastHit[] UnityEngine.Physics::RaycastAll(UnityEngine.Ray,System.Single,System.Int32)
extern void Physics_RaycastAll_m34E79BFFFC6E6FE555793C1BFB4E922C81D4FA9D (void);
// 0x0000012D UnityEngine.RaycastHit[] UnityEngine.Physics::RaycastAll(UnityEngine.Ray,System.Single)
extern void Physics_RaycastAll_m6CCB6B2F7A8A0DB9F4E2952AAD3CD31D00B8266B (void);
// 0x0000012E UnityEngine.RaycastHit[] UnityEngine.Physics::RaycastAll(UnityEngine.Ray)
extern void Physics_RaycastAll_m21E71941A58CD333D3F838D54BAE8BA26987A020 (void);
// 0x0000012F System.Int32 UnityEngine.Physics::RaycastNonAlloc(UnityEngine.Ray,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_RaycastNonAlloc_m207571490C7194D4C6679BE398AE4EE661053FD0 (void);
// 0x00000130 System.Int32 UnityEngine.Physics::RaycastNonAlloc(UnityEngine.Ray,UnityEngine.RaycastHit[],System.Single,System.Int32)
extern void Physics_RaycastNonAlloc_m3F953754DE454BDFC9D028DB608C5F29AB6C210C (void);
// 0x00000131 System.Int32 UnityEngine.Physics::RaycastNonAlloc(UnityEngine.Ray,UnityEngine.RaycastHit[],System.Single)
extern void Physics_RaycastNonAlloc_m6200490E0C211A635056EB97BD6C5B94A8F3C9F7 (void);
// 0x00000132 System.Int32 UnityEngine.Physics::RaycastNonAlloc(UnityEngine.Ray,UnityEngine.RaycastHit[])
extern void Physics_RaycastNonAlloc_m58C5B4D8925665228D7C13CFC5D82930321DE65A (void);
// 0x00000133 System.Int32 UnityEngine.Physics::RaycastNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_RaycastNonAlloc_m6B62C55FFC3F78B03C7AAA54047137702E7B82F6 (void);
// 0x00000134 System.Int32 UnityEngine.Physics::RaycastNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit[],System.Single,System.Int32)
extern void Physics_RaycastNonAlloc_mFA018B555BF77B9FDDCF991B4318EEFEE6BE55F8 (void);
// 0x00000135 System.Int32 UnityEngine.Physics::RaycastNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit[],System.Single)
extern void Physics_RaycastNonAlloc_m8ED1C92DD870E106825951F32E86EDBA9FA83998 (void);
// 0x00000136 System.Int32 UnityEngine.Physics::RaycastNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit[])
extern void Physics_RaycastNonAlloc_m6724BF59D2C8D914FD2A68801CFC9EC103D8B119 (void);
// 0x00000137 UnityEngine.RaycastHit[] UnityEngine.Physics::Query_CapsuleCastAll(UnityEngine.PhysicsScene,UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_Query_CapsuleCastAll_mEF1513DF9E25AF52278569B40B6075CB385FC06C (void);
// 0x00000138 UnityEngine.RaycastHit[] UnityEngine.Physics::CapsuleCastAll(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_CapsuleCastAll_m90620435B6C908294E630FF753CB9C343C457833 (void);
// 0x00000139 UnityEngine.RaycastHit[] UnityEngine.Physics::CapsuleCastAll(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,System.Single,System.Int32)
extern void Physics_CapsuleCastAll_m5A64F9F9D8E1BB0B8F1C58B054F4EE7E328C6D3C (void);
// 0x0000013A UnityEngine.RaycastHit[] UnityEngine.Physics::CapsuleCastAll(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,System.Single)
extern void Physics_CapsuleCastAll_mF89FEFC7979B1349BC53C1FA0C851FE58F511EF0 (void);
// 0x0000013B UnityEngine.RaycastHit[] UnityEngine.Physics::CapsuleCastAll(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3)
extern void Physics_CapsuleCastAll_mB31F2D50FB62F15EAE5D1504A62E9604ABC2D42F (void);
// 0x0000013C UnityEngine.RaycastHit[] UnityEngine.Physics::Query_SphereCastAll(UnityEngine.PhysicsScene,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_Query_SphereCastAll_m32DE4D6204490ED1E0DE4A181220671228E50FD3 (void);
// 0x0000013D UnityEngine.RaycastHit[] UnityEngine.Physics::SphereCastAll(UnityEngine.Vector3,System.Single,UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_SphereCastAll_m2B8F24D21CB6700CC9F20A5758136067100770A8 (void);
// 0x0000013E UnityEngine.RaycastHit[] UnityEngine.Physics::SphereCastAll(UnityEngine.Vector3,System.Single,UnityEngine.Vector3,System.Single,System.Int32)
extern void Physics_SphereCastAll_mB61075364B1C10840BBF8F53071C461B331EA9DB (void);
// 0x0000013F UnityEngine.RaycastHit[] UnityEngine.Physics::SphereCastAll(UnityEngine.Vector3,System.Single,UnityEngine.Vector3,System.Single)
extern void Physics_SphereCastAll_m57254E256F92A72C77FDFBEB9ECABD9605683333 (void);
// 0x00000140 UnityEngine.RaycastHit[] UnityEngine.Physics::SphereCastAll(UnityEngine.Vector3,System.Single,UnityEngine.Vector3)
extern void Physics_SphereCastAll_m023EFD34F22884F3ED82CB6B0C025CC33308A28B (void);
// 0x00000141 UnityEngine.RaycastHit[] UnityEngine.Physics::SphereCastAll(UnityEngine.Ray,System.Single,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_SphereCastAll_m4CCF0ED9EB61C5530500ED6D3B8E29F5E4103F64 (void);
// 0x00000142 UnityEngine.RaycastHit[] UnityEngine.Physics::SphereCastAll(UnityEngine.Ray,System.Single,System.Single,System.Int32)
extern void Physics_SphereCastAll_mE22B49CCBB29AF282B40E364017C62F7CE2963A6 (void);
// 0x00000143 UnityEngine.RaycastHit[] UnityEngine.Physics::SphereCastAll(UnityEngine.Ray,System.Single,System.Single)
extern void Physics_SphereCastAll_m90D17F15D8C473D0ABD035AE948CFC196A0301A2 (void);
// 0x00000144 UnityEngine.RaycastHit[] UnityEngine.Physics::SphereCastAll(UnityEngine.Ray,System.Single)
extern void Physics_SphereCastAll_m4CB0FFC0DF4E2C126601FEFE46477391C1F8FACE (void);
// 0x00000145 UnityEngine.Collider[] UnityEngine.Physics::OverlapCapsule_Internal(UnityEngine.PhysicsScene,UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_OverlapCapsule_Internal_m611EAB1AF03BAB47D00967B03FAF55B38113C658 (void);
// 0x00000146 UnityEngine.Collider[] UnityEngine.Physics::OverlapCapsule(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_OverlapCapsule_m89D89478C2B9A19CF308090CD763C0E3981194EE (void);
// 0x00000147 UnityEngine.Collider[] UnityEngine.Physics::OverlapCapsule(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Int32)
extern void Physics_OverlapCapsule_mCF12621D08D097BF930031F2E8EFF4E98912D912 (void);
// 0x00000148 UnityEngine.Collider[] UnityEngine.Physics::OverlapCapsule(UnityEngine.Vector3,UnityEngine.Vector3,System.Single)
extern void Physics_OverlapCapsule_m4A76BB201F50BC3A3FCE53B0F8821121C243B89C (void);
// 0x00000149 UnityEngine.Collider[] UnityEngine.Physics::OverlapSphere_Internal(UnityEngine.PhysicsScene,UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_OverlapSphere_Internal_mADD6D653247DE323AAD64B0641352F764C8854A0 (void);
// 0x0000014A UnityEngine.Collider[] UnityEngine.Physics::OverlapSphere(UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_OverlapSphere_m704A1ACBC7123FD92E077440D4F96F2DE409624F (void);
// 0x0000014B UnityEngine.Collider[] UnityEngine.Physics::OverlapSphere(UnityEngine.Vector3,System.Single,System.Int32)
extern void Physics_OverlapSphere_m7B1E4E0363F42D9802125B0D6E08F5D6D67262E2 (void);
// 0x0000014C UnityEngine.Collider[] UnityEngine.Physics::OverlapSphere(UnityEngine.Vector3,System.Single)
extern void Physics_OverlapSphere_m885BB636D248BA40DF5758A7AA108C010A942E62 (void);
// 0x0000014D System.Void UnityEngine.Physics::Simulate_Internal(UnityEngine.PhysicsScene,System.Single)
extern void Physics_Simulate_Internal_m4460711B1389A3B4E3D40188FCCE0D5D48D5384D (void);
// 0x0000014E System.Void UnityEngine.Physics::Simulate(System.Single)
extern void Physics_Simulate_m0B827845B3B97CC9A71B52A0AC9877E3F6FCE3BC (void);
// 0x0000014F System.Boolean UnityEngine.Physics::get_autoSimulation()
extern void Physics_get_autoSimulation_mC34C28A28B053F73E40EC2A09FA676A180A6F8C2 (void);
// 0x00000150 System.Void UnityEngine.Physics::set_autoSimulation(System.Boolean)
extern void Physics_set_autoSimulation_mD5AF1E2E7425452E2BA0F6E71ADF107BB08835BF (void);
// 0x00000151 System.Void UnityEngine.Physics::SyncTransforms()
extern void Physics_SyncTransforms_m3FF00DAB8D8E79D9DEA225E8CA0C00E206EB8C74 (void);
// 0x00000152 System.Boolean UnityEngine.Physics::get_autoSyncTransforms()
extern void Physics_get_autoSyncTransforms_m2C8DA55CD646DD8E6271AF2C5160AFC31A41D131 (void);
// 0x00000153 System.Void UnityEngine.Physics::set_autoSyncTransforms(System.Boolean)
extern void Physics_set_autoSyncTransforms_m83D9B428CBC18F8E1FF334A5DD5F5556737A4CF8 (void);
// 0x00000154 System.Boolean UnityEngine.Physics::get_reuseCollisionCallbacks()
extern void Physics_get_reuseCollisionCallbacks_mBF40FFE360ECC9C866D74528EBC156F088BB071A (void);
// 0x00000155 System.Void UnityEngine.Physics::set_reuseCollisionCallbacks(System.Boolean)
extern void Physics_set_reuseCollisionCallbacks_m3FB20EE28FEAFCD0F41C8B4EC6715A29996FD67F (void);
// 0x00000156 System.Boolean UnityEngine.Physics::Query_ComputePenetration(UnityEngine.Collider,UnityEngine.Vector3,UnityEngine.Quaternion,UnityEngine.Collider,UnityEngine.Vector3,UnityEngine.Quaternion,UnityEngine.Vector3&,System.Single&)
extern void Physics_Query_ComputePenetration_m27C7018A8A1669FA0E36ACE99B8CCEAE8EC1FB40 (void);
// 0x00000157 System.Boolean UnityEngine.Physics::ComputePenetration(UnityEngine.Collider,UnityEngine.Vector3,UnityEngine.Quaternion,UnityEngine.Collider,UnityEngine.Vector3,UnityEngine.Quaternion,UnityEngine.Vector3&,System.Single&)
extern void Physics_ComputePenetration_m3507EFEA7923266CFA2961A5A587152F35BCB250 (void);
// 0x00000158 UnityEngine.Vector3 UnityEngine.Physics::Query_ClosestPoint(UnityEngine.Collider,UnityEngine.Vector3,UnityEngine.Quaternion,UnityEngine.Vector3)
extern void Physics_Query_ClosestPoint_mF5BA23B046F75113515288123F33A0D1A7B72CFC (void);
// 0x00000159 UnityEngine.Vector3 UnityEngine.Physics::ClosestPoint(UnityEngine.Vector3,UnityEngine.Collider,UnityEngine.Vector3,UnityEngine.Quaternion)
extern void Physics_ClosestPoint_m81A2630C69D5A78A2CA64466E076F412393C1AFF (void);
// 0x0000015A System.Single UnityEngine.Physics::get_interCollisionDistance()
extern void Physics_get_interCollisionDistance_m4D3641513DEC83AD79F83E3D222A179F1854D9FC (void);
// 0x0000015B System.Void UnityEngine.Physics::set_interCollisionDistance(System.Single)
extern void Physics_set_interCollisionDistance_mB2FAA04952085BC7AF9BB1CD5FF0C3B77CCE4796 (void);
// 0x0000015C System.Single UnityEngine.Physics::get_interCollisionStiffness()
extern void Physics_get_interCollisionStiffness_mADD8F984B28FFBD4FEB51D9A420B1C1A733BED76 (void);
// 0x0000015D System.Void UnityEngine.Physics::set_interCollisionStiffness(System.Single)
extern void Physics_set_interCollisionStiffness_m879C28BD4960D01CFBD497333A7C90DD458446F8 (void);
// 0x0000015E System.Boolean UnityEngine.Physics::get_interCollisionSettingsToggle()
extern void Physics_get_interCollisionSettingsToggle_m8D9C709B397617445F0CB4503B2C302D35684D09 (void);
// 0x0000015F System.Void UnityEngine.Physics::set_interCollisionSettingsToggle(System.Boolean)
extern void Physics_set_interCollisionSettingsToggle_m5870AA9109D6E63D030E0A79A64E74600273830E (void);
// 0x00000160 UnityEngine.Vector3 UnityEngine.Physics::get_clothGravity()
extern void Physics_get_clothGravity_m12196FC75700B233CEAACBA1625F749C154455EE (void);
// 0x00000161 System.Void UnityEngine.Physics::set_clothGravity(UnityEngine.Vector3)
extern void Physics_set_clothGravity_mA3277A26FF78F2645D7FD5A7B2DF9D6751712685 (void);
// 0x00000162 System.Int32 UnityEngine.Physics::OverlapSphereNonAlloc(UnityEngine.Vector3,System.Single,UnityEngine.Collider[],System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_OverlapSphereNonAlloc_m581B60F800CD970C67BE48AFA30322B4EA2D8590 (void);
// 0x00000163 System.Int32 UnityEngine.Physics::OverlapSphereNonAlloc(UnityEngine.Vector3,System.Single,UnityEngine.Collider[],System.Int32)
extern void Physics_OverlapSphereNonAlloc_m1854FF819705ACDD1429795466C8763400924DA1 (void);
// 0x00000164 System.Int32 UnityEngine.Physics::OverlapSphereNonAlloc(UnityEngine.Vector3,System.Single,UnityEngine.Collider[])
extern void Physics_OverlapSphereNonAlloc_m04C0A399F168EF52077546DCA9C657928CEBE3BB (void);
// 0x00000165 System.Boolean UnityEngine.Physics::CheckSphere_Internal(UnityEngine.PhysicsScene,UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_CheckSphere_Internal_mB1F09D00D7F7602F50730EE91DD42FFB3D934D9B (void);
// 0x00000166 System.Boolean UnityEngine.Physics::CheckSphere(UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_CheckSphere_mB1295ED25B19CDECC79C15378AD27B7E4B95C919 (void);
// 0x00000167 System.Boolean UnityEngine.Physics::CheckSphere(UnityEngine.Vector3,System.Single,System.Int32)
extern void Physics_CheckSphere_mD36EC68D5324CF6F8100D76FBC0B2C8D29EA21E1 (void);
// 0x00000168 System.Boolean UnityEngine.Physics::CheckSphere(UnityEngine.Vector3,System.Single)
extern void Physics_CheckSphere_m595752F39C0F4FFF79F25A49A8F90FCF34130540 (void);
// 0x00000169 System.Int32 UnityEngine.Physics::CapsuleCastNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_CapsuleCastNonAlloc_mACC7729211C47FFDEDFEC81B2C381107BDD7E8EA (void);
// 0x0000016A System.Int32 UnityEngine.Physics::CapsuleCastNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit[],System.Single,System.Int32)
extern void Physics_CapsuleCastNonAlloc_m7E511B9D9300B4FBD745377E7B435B1E12138450 (void);
// 0x0000016B System.Int32 UnityEngine.Physics::CapsuleCastNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit[],System.Single)
extern void Physics_CapsuleCastNonAlloc_mFF5EF48CD3901AEEA12BF57246ABFF398ACC75BC (void);
// 0x0000016C System.Int32 UnityEngine.Physics::CapsuleCastNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit[])
extern void Physics_CapsuleCastNonAlloc_mA13B34C4D1CEA663A52EA7845680C853948CA454 (void);
// 0x0000016D System.Int32 UnityEngine.Physics::SphereCastNonAlloc(UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_SphereCastNonAlloc_mE792A0BF759B32E2F80B7465D3971A6FAAF78E49 (void);
// 0x0000016E System.Int32 UnityEngine.Physics::SphereCastNonAlloc(UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit[],System.Single,System.Int32)
extern void Physics_SphereCastNonAlloc_m0EE4E1CFE7225F8ABF36344D2BF94F915428EA2E (void);
// 0x0000016F System.Int32 UnityEngine.Physics::SphereCastNonAlloc(UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit[],System.Single)
extern void Physics_SphereCastNonAlloc_mDB4CE103704C3B243613A0826AA4BC796F773C82 (void);
// 0x00000170 System.Int32 UnityEngine.Physics::SphereCastNonAlloc(UnityEngine.Vector3,System.Single,UnityEngine.Vector3,UnityEngine.RaycastHit[])
extern void Physics_SphereCastNonAlloc_mD45E46F06F9A10743D36C13C9BCEA9042EFFB07E (void);
// 0x00000171 System.Int32 UnityEngine.Physics::SphereCastNonAlloc(UnityEngine.Ray,System.Single,UnityEngine.RaycastHit[],System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_SphereCastNonAlloc_m23C464FB433A863BF7EF37F77D3BF1A5646DE916 (void);
// 0x00000172 System.Int32 UnityEngine.Physics::SphereCastNonAlloc(UnityEngine.Ray,System.Single,UnityEngine.RaycastHit[],System.Single,System.Int32)
extern void Physics_SphereCastNonAlloc_mB43840A3BD3D4C030B5EC41AE36003A84620B4EE (void);
// 0x00000173 System.Int32 UnityEngine.Physics::SphereCastNonAlloc(UnityEngine.Ray,System.Single,UnityEngine.RaycastHit[],System.Single)
extern void Physics_SphereCastNonAlloc_m48B3F8CD32F7C79519732D191EC5A6EF3342FA54 (void);
// 0x00000174 System.Int32 UnityEngine.Physics::SphereCastNonAlloc(UnityEngine.Ray,System.Single,UnityEngine.RaycastHit[])
extern void Physics_SphereCastNonAlloc_mFC9FEF63227FBF067D9E297611D1AAD3633A6CA5 (void);
// 0x00000175 System.Boolean UnityEngine.Physics::CheckCapsule_Internal(UnityEngine.PhysicsScene,UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_CheckCapsule_Internal_m358855F921DCA4CCCF24E4CD26FD422FC6065A04 (void);
// 0x00000176 System.Boolean UnityEngine.Physics::CheckCapsule(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_CheckCapsule_m0FFF18BD59B797BAEE4C2F33E57B12D25FB2AFF7 (void);
// 0x00000177 System.Boolean UnityEngine.Physics::CheckCapsule(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Int32)
extern void Physics_CheckCapsule_m2D66253F9DD04BC6E80CCABCB4FD66FB486B2A76 (void);
// 0x00000178 System.Boolean UnityEngine.Physics::CheckCapsule(UnityEngine.Vector3,UnityEngine.Vector3,System.Single)
extern void Physics_CheckCapsule_m302F3C16A0600B390FD7427FA36348C2C7F12701 (void);
// 0x00000179 System.Boolean UnityEngine.Physics::CheckBox_Internal(UnityEngine.PhysicsScene,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Quaternion,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_CheckBox_Internal_m02F89F4BBCA2A05655629028A2518273752F5FBA (void);
// 0x0000017A System.Boolean UnityEngine.Physics::CheckBox(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Quaternion,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_CheckBox_m873FC33A295AC107EEED26DA42C270163F00D117 (void);
// 0x0000017B System.Boolean UnityEngine.Physics::CheckBox(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Quaternion,System.Int32)
extern void Physics_CheckBox_m2AC5348166BB0F9A95016FB1C7589D17A2A991F6 (void);
// 0x0000017C System.Boolean UnityEngine.Physics::CheckBox(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Quaternion)
extern void Physics_CheckBox_m3EAFA5E4FBC70232F85D606AAB433938315D6458 (void);
// 0x0000017D System.Boolean UnityEngine.Physics::CheckBox(UnityEngine.Vector3,UnityEngine.Vector3)
extern void Physics_CheckBox_m7FB3EE66186B392CA3A892743464AE15268B2ACA (void);
// 0x0000017E UnityEngine.Collider[] UnityEngine.Physics::OverlapBox_Internal(UnityEngine.PhysicsScene,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Quaternion,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_OverlapBox_Internal_mAF19FB424100FB79AB7224B417E413DED97BE18E (void);
// 0x0000017F UnityEngine.Collider[] UnityEngine.Physics::OverlapBox(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Quaternion,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_OverlapBox_mADF055F975454C0628F78AD24507B13BAB91BE65 (void);
// 0x00000180 UnityEngine.Collider[] UnityEngine.Physics::OverlapBox(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Quaternion,System.Int32)
extern void Physics_OverlapBox_m77C350471E227B1EDD9E42EE520538D2E2B7804A (void);
// 0x00000181 UnityEngine.Collider[] UnityEngine.Physics::OverlapBox(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Quaternion)
extern void Physics_OverlapBox_m1EAB50779182ADD2653CC3368720A76799BC0559 (void);
// 0x00000182 UnityEngine.Collider[] UnityEngine.Physics::OverlapBox(UnityEngine.Vector3,UnityEngine.Vector3)
extern void Physics_OverlapBox_mC73C23157BDA00BC00DDF4DA48D33C1B81F409CA (void);
// 0x00000183 System.Int32 UnityEngine.Physics::OverlapBoxNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Collider[],UnityEngine.Quaternion,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_OverlapBoxNonAlloc_mE89462281DE54BC30F6172BEB192EAC91E1AD9C4 (void);
// 0x00000184 System.Int32 UnityEngine.Physics::OverlapBoxNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Collider[],UnityEngine.Quaternion,System.Int32)
extern void Physics_OverlapBoxNonAlloc_m0CA09577CF79A0A264775BE4D26E756BD8B7EC8B (void);
// 0x00000185 System.Int32 UnityEngine.Physics::OverlapBoxNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Collider[],UnityEngine.Quaternion)
extern void Physics_OverlapBoxNonAlloc_m211E1209B7E4EC9D93DFA65BB0911C1B0CD50BBD (void);
// 0x00000186 System.Int32 UnityEngine.Physics::OverlapBoxNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Collider[])
extern void Physics_OverlapBoxNonAlloc_mE22635EBC8813B63F5DB11BA659CD0868B6BE15A (void);
// 0x00000187 System.Int32 UnityEngine.Physics::BoxCastNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit[],UnityEngine.Quaternion,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_BoxCastNonAlloc_m1F42BF8BE68DE74C75191FD3D7825A7733FF7D5F (void);
// 0x00000188 System.Int32 UnityEngine.Physics::BoxCastNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit[],UnityEngine.Quaternion)
extern void Physics_BoxCastNonAlloc_mA43BADF85365434F5BDAB835DC34F32729CAE0CF (void);
// 0x00000189 System.Int32 UnityEngine.Physics::BoxCastNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit[],UnityEngine.Quaternion,System.Single)
extern void Physics_BoxCastNonAlloc_mFA2B2C59E8B24A54587E57CCFB3FF1614769A13D (void);
// 0x0000018A System.Int32 UnityEngine.Physics::BoxCastNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit[],UnityEngine.Quaternion,System.Single,System.Int32)
extern void Physics_BoxCastNonAlloc_m1451356413E14DA402AC5D9F967A6EDD993C1005 (void);
// 0x0000018B System.Int32 UnityEngine.Physics::BoxCastNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.RaycastHit[])
extern void Physics_BoxCastNonAlloc_m48C7CC1B46D2922D27E3771B68F6084FA933F682 (void);
// 0x0000018C UnityEngine.RaycastHit[] UnityEngine.Physics::Internal_BoxCastAll(UnityEngine.PhysicsScene,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Quaternion,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_Internal_BoxCastAll_m4414579DD2981AE23D714D2BC2D231CE1F3CDE07 (void);
// 0x0000018D UnityEngine.RaycastHit[] UnityEngine.Physics::BoxCastAll(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Quaternion,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_BoxCastAll_m052716E5F18B96C40671B75EC508B5F3477C10E7 (void);
// 0x0000018E UnityEngine.RaycastHit[] UnityEngine.Physics::BoxCastAll(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Quaternion,System.Single,System.Int32)
extern void Physics_BoxCastAll_m1B41F3BB2532BF9924F69749FC3E9C1B02AD4E68 (void);
// 0x0000018F UnityEngine.RaycastHit[] UnityEngine.Physics::BoxCastAll(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Quaternion,System.Single)
extern void Physics_BoxCastAll_m9125F90A557E2132569AE1836BB03C3BC2C232AF (void);
// 0x00000190 UnityEngine.RaycastHit[] UnityEngine.Physics::BoxCastAll(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Quaternion)
extern void Physics_BoxCastAll_mC2770103DE07FAD2A04E6179EF2D3A86CF0EBB8D (void);
// 0x00000191 UnityEngine.RaycastHit[] UnityEngine.Physics::BoxCastAll(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3)
extern void Physics_BoxCastAll_mE2EA842AF0A63D23F56CFF850C11DC656BB19180 (void);
// 0x00000192 System.Int32 UnityEngine.Physics::OverlapCapsuleNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Collider[],System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_OverlapCapsuleNonAlloc_mCA6619D7D415CDC6850A7F6DD697254032177150 (void);
// 0x00000193 System.Int32 UnityEngine.Physics::OverlapCapsuleNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Collider[],System.Int32)
extern void Physics_OverlapCapsuleNonAlloc_m202BFCF12138EB1A71F75A4D31C7AAA369BDB0E4 (void);
// 0x00000194 System.Int32 UnityEngine.Physics::OverlapCapsuleNonAlloc(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,UnityEngine.Collider[])
extern void Physics_OverlapCapsuleNonAlloc_mCEB1FBC60B432B1FFEDC3008E996536C645C03FD (void);
// 0x00000195 System.Void UnityEngine.Physics::Internal_RebuildBroadphaseRegions(UnityEngine.Bounds,System.Int32)
extern void Physics_Internal_RebuildBroadphaseRegions_m2ECCEDCB20F5768F7B3A9AC7910539DD8153E67B (void);
// 0x00000196 System.Void UnityEngine.Physics::RebuildBroadphaseRegions(UnityEngine.Bounds,System.Int32)
extern void Physics_RebuildBroadphaseRegions_m3711D2CA3B931D45B21F150661C887F5DD21DC06 (void);
// 0x00000197 System.Void UnityEngine.Physics::BakeMesh(System.Int32,System.Boolean)
extern void Physics_BakeMesh_mF4A190E866D7FF2A67D22396FE0677AD30B860EC (void);
// 0x00000198 System.Void UnityEngine.Physics::get_gravity_Injected(UnityEngine.Vector3&)
extern void Physics_get_gravity_Injected_mF329CFCFDD3CCBB9C83FB3FC27D54226544548F9 (void);
// 0x00000199 System.Void UnityEngine.Physics::set_gravity_Injected(UnityEngine.Vector3&)
extern void Physics_set_gravity_Injected_m25AE2B7326ADA49229C493A84F327B94B61FD276 (void);
// 0x0000019A System.Void UnityEngine.Physics::get_defaultPhysicsScene_Injected(UnityEngine.PhysicsScene&)
extern void Physics_get_defaultPhysicsScene_Injected_m6B5189999E482654591F8988C09EADA72F6A478C (void);
// 0x0000019B UnityEngine.RaycastHit[] UnityEngine.Physics::Internal_RaycastAll_Injected(UnityEngine.PhysicsScene&,UnityEngine.Ray&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_Internal_RaycastAll_Injected_mE0CDD075B9C29933B7189DD9E99A692FB9010C26 (void);
// 0x0000019C UnityEngine.RaycastHit[] UnityEngine.Physics::Query_CapsuleCastAll_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,UnityEngine.Vector3&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_Query_CapsuleCastAll_Injected_mF02B3FD2575254AB6DA9C4E56D408D53DE0220E1 (void);
// 0x0000019D UnityEngine.RaycastHit[] UnityEngine.Physics::Query_SphereCastAll_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,System.Single,UnityEngine.Vector3&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_Query_SphereCastAll_Injected_m695204A02A22A17BEAA4120E7334A2F84D2993F4 (void);
// 0x0000019E UnityEngine.Collider[] UnityEngine.Physics::OverlapCapsule_Internal_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_OverlapCapsule_Internal_Injected_m0318F1E1F0F3C28043B73411CCEC1FC2A9935407 (void);
// 0x0000019F UnityEngine.Collider[] UnityEngine.Physics::OverlapSphere_Internal_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_OverlapSphere_Internal_Injected_mAB077CE432470B2525C904ECD186B5F46FFF0C4D (void);
// 0x000001A0 System.Void UnityEngine.Physics::Simulate_Internal_Injected(UnityEngine.PhysicsScene&,System.Single)
extern void Physics_Simulate_Internal_Injected_mFC4DAFF4D3BE67C136C0007A0C74410B3B3C97E4 (void);
// 0x000001A1 System.Boolean UnityEngine.Physics::Query_ComputePenetration_Injected(UnityEngine.Collider,UnityEngine.Vector3&,UnityEngine.Quaternion&,UnityEngine.Collider,UnityEngine.Vector3&,UnityEngine.Quaternion&,UnityEngine.Vector3&,System.Single&)
extern void Physics_Query_ComputePenetration_Injected_m2CC6ABE59D42D70E50A372F183E804008EBEF9B2 (void);
// 0x000001A2 System.Void UnityEngine.Physics::Query_ClosestPoint_Injected(UnityEngine.Collider,UnityEngine.Vector3&,UnityEngine.Quaternion&,UnityEngine.Vector3&,UnityEngine.Vector3&)
extern void Physics_Query_ClosestPoint_Injected_m30B87B53D0F89297194D75ABD18176A54BF14DC7 (void);
// 0x000001A3 System.Void UnityEngine.Physics::get_clothGravity_Injected(UnityEngine.Vector3&)
extern void Physics_get_clothGravity_Injected_m0A51AE32AB3C836684157CB2EAE8E992EBB7D195 (void);
// 0x000001A4 System.Void UnityEngine.Physics::set_clothGravity_Injected(UnityEngine.Vector3&)
extern void Physics_set_clothGravity_Injected_mF80D3003F454871C216746B88250E0BCB5FE2779 (void);
// 0x000001A5 System.Boolean UnityEngine.Physics::CheckSphere_Internal_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_CheckSphere_Internal_Injected_m06FDDD418D8EADC1D95623F5EF1190A7403D27E8 (void);
// 0x000001A6 System.Boolean UnityEngine.Physics::CheckCapsule_Internal_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_CheckCapsule_Internal_Injected_mFC3C06D76A130ED877585C8B6B395BAFC8576E38 (void);
// 0x000001A7 System.Boolean UnityEngine.Physics::CheckBox_Internal_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Quaternion&,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_CheckBox_Internal_Injected_m0E704E4D55BC1E51BE4EBC1B6F1FB6D7A714D07D (void);
// 0x000001A8 UnityEngine.Collider[] UnityEngine.Physics::OverlapBox_Internal_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Quaternion&,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_OverlapBox_Internal_Injected_m2222CE0F8F5F58A020B7F00BDAD43F6AA5B62817 (void);
// 0x000001A9 UnityEngine.RaycastHit[] UnityEngine.Physics::Internal_BoxCastAll_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Quaternion&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
extern void Physics_Internal_BoxCastAll_Injected_mD1C1259FD5D4B991CBD3045BC6D2A6890B33735B (void);
// 0x000001AA System.Void UnityEngine.Physics::Internal_RebuildBroadphaseRegions_Injected(UnityEngine.Bounds&,System.Int32)
extern void Physics_Internal_RebuildBroadphaseRegions_Injected_mAFB25CEEE3B2D8BFA9B85AA0C3C4A90B6358CAB2 (void);
static Il2CppMethodPointer s_methodPointers[426] = 
{
	JointLimits_set_min_m500E297C572E2690D0678EC354BFE9AF7F27D2FE,
	JointLimits_set_max_mE767D8DE579BBFBF505B5A8316A0B04D8F53775C,
	Collision_get_gameObject_m5682F872FD28419AA36F0651CE8B19825A21859D,
	PhysicMaterial__ctor_m8E7DE1AB54FEC51DE0DA4207E5BB55BB45EFC873,
	PhysicMaterial_Internal_CreateDynamicsMaterial_mAE144DFC84442CB32A328052430C7FDF4F9834CA,
	RaycastHit_get_collider_m13A3DE16FBC631E0A1F987E0B22CE70AF8AB539E,
	RaycastHit_get_point_m32F7282CBB2E13393A33BAD046BDA218E48DD21E,
	RaycastHit_get_normal_m2C813B25BAECD87FD9E9CB294278B291F4CC6674,
	RaycastHit_get_distance_m85FCA98D7957C3BF1D449CA1B48C116CCD6226FA,
	RaycastHit_get_transform_m2DD983DBD3602DE848DE287EE5233FD02EEC608D,
	RaycastHit_get_rigidbody_mE48E45893FDAFD03162C6A73AAF02C6CB0E079FB,
	Rigidbody_get_velocity_mCFB033F3BD14C2BA68E797DFA4950F9307EC8E2C,
	Rigidbody_set_velocity_m8DC0988916EB38DFD7D4584830B41D79140BF18D,
	Rigidbody_get_angularVelocity_m6737340DF546452900D199246279231D80A21DCF,
	Rigidbody_set_angularVelocity_m3A40B7F195E9E217AE29A0964D7E7540E2E23080,
	Rigidbody_get_drag_m0C617963D9BBBC4018D3A8B2DB5D6190615F4A64,
	Rigidbody_set_drag_m60E39BE31529DE5163116785A69FACC77C52DA98,
	Rigidbody_get_angularDrag_m0E53FD8F8A09DFA941C52C868288DBBC030C5082,
	Rigidbody_set_angularDrag_m8BF3771789B32FB09FDD8066BAFA0A0B661372A4,
	Rigidbody_get_mass_mB7B19406DAC6336A8244E98BE271BDA8B5C26223,
	Rigidbody_set_mass_m54FCACE073F5E7742DB1D7C0BA19CD0C0F3DDA3F,
	Rigidbody_SetDensity_m794AA2513F044BCFC3BC87AB39387131BA1EA282,
	Rigidbody_get_useGravity_mDA0FB6F456377840E6E46C42B9210F93264E2B28,
	Rigidbody_set_useGravity_m1057292FB3199E87664F40B8BCBA7A7E64D1A096,
	Rigidbody_get_maxDepenetrationVelocity_mE2F07995501D4EFAB39C3B5E6A91ECD6D899272E,
	Rigidbody_set_maxDepenetrationVelocity_mF2D2B5F040B69E32E48544EBA5F6D43656798A02,
	Rigidbody_get_isKinematic_m597B48C45021313B6C6C4B126E405EF566C5C80C,
	Rigidbody_set_isKinematic_mCF74D680205544826F2DE2CAB929C9F25409A311,
	Rigidbody_get_freezeRotation_m52850F7355488269E7026A1E9C6AEB69EA1A1CCE,
	Rigidbody_set_freezeRotation_mE08A39E98D46F82D6DD86CC389D86D242C694D52,
	Rigidbody_get_constraints_m5EBE3E9C0A2084BAAE0C783E212C368A1CB1DD04,
	Rigidbody_set_constraints_mA76F562D16D3BE8889E095D0309C8FE38DA914F1,
	Rigidbody_get_collisionDetectionMode_m1328BE903DD8B2DBD3A67B64FCECFEE91DC49FCE,
	Rigidbody_set_collisionDetectionMode_m4BC31A84B26540ACD15273774C47F31CDF5381C2,
	Rigidbody_get_centerOfMass_mEF7838582F7977AAD2952F6A09E2203D9B0ABC53,
	Rigidbody_set_centerOfMass_m3B13BE412D99CE5133606643F14501CF5C63CCEC,
	Rigidbody_get_worldCenterOfMass_mF825B0D5110903BD0A3FDC1DC4317902305DE143,
	Rigidbody_get_inertiaTensorRotation_m9EFA1F67FF15DC3E996146737149C69A5003D3C1,
	Rigidbody_set_inertiaTensorRotation_m18E191AC25EE29C24DE6A862A26633A7A21A9D9B,
	Rigidbody_get_inertiaTensor_m55F35467FD2088D883060D57595AD5BF14BC5C6F,
	Rigidbody_set_inertiaTensor_mCDE8A0CCFD0B6464B5F99CC1A54DF5300652D5D1,
	Rigidbody_get_detectCollisions_m6741D1A8C1942766F95258677352A75451D99D21,
	Rigidbody_set_detectCollisions_mB94256836724071B1EFE622A5E9BA435B6572E9A,
	Rigidbody_get_position_m5F429382F610E324F39F33E8498A29D0828AD8E8,
	Rigidbody_set_position_mD1A5B0DB2FD8861778CAA575E1D7B72E2AEF4024,
	Rigidbody_get_rotation_mEB90F9D223B0BA32A1962971E3A93DEE1670D18A,
	Rigidbody_set_rotation_m3024C151FEC9BB75735DE9B4BA64F16AA779C5D6,
	Rigidbody_get_interpolation_m17422952D016117813B4520CB65842D2537C8E12,
	Rigidbody_set_interpolation_mEBAF9DF8DE317E2D58848735D26ED9E69CC425CE,
	Rigidbody_get_solverIterations_m9A62E73E257401EFDC44E7338CD36E9E006E8CA1,
	Rigidbody_set_solverIterations_m71644E473B3DB97C10199A7D5AEBAD790DD586EC,
	Rigidbody_get_sleepThreshold_m1469D01B7634195511EEC913BA69617618DE5EEC,
	Rigidbody_set_sleepThreshold_mA71700BF6225A4366FE6BE1365E26B0BF832C00B,
	Rigidbody_get_maxAngularVelocity_m8804A10C23EF0C073D5D829ED6BBF5253B0A2DFE,
	Rigidbody_set_maxAngularVelocity_m55AB6C34E2C33E6EABB07EFCB7AE443F5D3BD060,
	Rigidbody_MovePosition_mB3CBBF21FD0ABB88BC6C004B993DED25673001C7,
	Rigidbody_MoveRotation_m08A1449DC0D514A70065CD80D067597765BDA5B2,
	Rigidbody_Sleep_m60350AEF3E52D57FBE448CADBC06BA98DAEA2115,
	Rigidbody_IsSleeping_mD41EDC4429411110D1FE36101C89B0277F8D62BA,
	Rigidbody_WakeUp_m89308E6756834CF3705D9CDE89D636EEEE409316,
	Rigidbody_ResetCenterOfMass_m6F34341B309D3AAE2A8B3CC963A1E38383EB0FD9,
	Rigidbody_ResetInertiaTensor_m8888FABEC8D40828D8F594915AF20CB01E9511D7,
	Rigidbody_GetRelativePointVelocity_mBE38822FEE8B1D4ECBDCD65AA20D13D0423B1FCF,
	Rigidbody_GetPointVelocity_m48BA0D6C61636CADE0764CC2BF5B02E1B61F1273,
	Rigidbody_get_solverVelocityIterations_m94EF00A61F9CD356D0E56D7285D06EAC4FD2ABEA,
	Rigidbody_set_solverVelocityIterations_mA345A4D3722FFFEDD9391720F9296A9721647AED,
	Rigidbody_AddForce_m78B9D94F505E19F3C63461362AD6DE7EA0836700,
	Rigidbody_AddForce_mDFB0D57C25682B826999B0074F5C0FD399C6401D,
	Rigidbody_AddForce_mB32FE7E26D05833A284C6CB9CF7E941A8570934E,
	Rigidbody_AddForce_mFF44DF454FDA13ADFD3CAA4958C4265E45BCF773,
	Rigidbody_AddRelativeForce_mD1AA911C950F80F697130C0A3DEDEECC8B532752,
	Rigidbody_AddRelativeForce_m45E6C06CE742C72905BB126945B454FBA54D84E1,
	Rigidbody_AddRelativeForce_m60FECB22FDEA509AF396844051F9953C25C92B55,
	Rigidbody_AddRelativeForce_m3997E5306302262D5CB451DA1FEFE687BF42B042,
	Rigidbody_AddTorque_mEDE3483056FB07222A4D096F22D45C7D8A6E2552,
	Rigidbody_AddTorque_mAEB5758FA773B1A0ECDD328934BB3A7202D21EB3,
	Rigidbody_AddTorque_m181DAF83E45841384AC7E2E7DB83EC1747623DA4,
	Rigidbody_AddTorque_m37159A78400CF4E39F460871437FC3E60CB60E48,
	Rigidbody_AddRelativeTorque_m1C03D022550534C7C08BF4F8D7983EE8823A785B,
	Rigidbody_AddRelativeTorque_m02D008355A3626183BF1B6D4451324FA045F3887,
	Rigidbody_AddRelativeTorque_m4C699C552995284B52445F59C398D55D4E2AE9E8,
	Rigidbody_AddRelativeTorque_m088537EE9F7B75DC820FDFDE1CDCFCAAF7214CD9,
	Rigidbody_AddForceAtPosition_mEE49C058A6D57C1D5A78207494BFED5906D80D0F,
	Rigidbody_AddForceAtPosition_m5190249D95CE1882B37481C5BFD2ABF201902BA5,
	Rigidbody_AddExplosionForce_mA81BFBF84914CEA89D18047ADE14B47D171280DD,
	Rigidbody_AddExplosionForce_m857BD26BDE42BFDC503AADE97899F94E92F6A6FC,
	Rigidbody_AddExplosionForce_m8B6FFD506CCCDD31C8C018A4B635E82B5499648F,
	Rigidbody_Internal_ClosestPointOnBounds_mA448D126F605336DC36AAA31237742A267A923D7,
	Rigidbody_ClosestPointOnBounds_m449FB43B807BDD87CB528311EA0E9EE7E46CFCAD,
	Rigidbody_SweepTest_m3CD0217FAA41AB8B51B7AA8CF4CBE6CFAFBBD9E2,
	Rigidbody_SweepTest_mB96D6E932712DCABA13A2FF341511F2A525BD559,
	Rigidbody_SweepTest_m10E90C88C57072C4D093CD75909D3B0B9116D911,
	Rigidbody_SweepTest_mDC7248518295568AD6FC8ED49168242EABD04918,
	Rigidbody_Internal_SweepTestAll_mAF90D437ED68D4E1FCE3C993E45BCBBC7C1DD66A,
	Rigidbody_SweepTestAll_m1D341AB79E8097404255AC95C3C1F5AF8AB83EA5,
	Rigidbody_SweepTestAll_mB45AA1BEF24E98CF8D9150C2E1E107A6C81851D8,
	Rigidbody_SweepTestAll_m50B229C454076D4CC2085E8F053B2ED419B314DC,
	Rigidbody__ctor_m0E43BA3B0E70E71B2CA62B165EE5B7CFAEFACDE9,
	Rigidbody_get_velocity_Injected_m79C6BB8C054D0B5F03F3F13325910BC068E5B796,
	Rigidbody_set_velocity_Injected_mBFBC7681B33942F69A5CD908941D399777F66ADD,
	Rigidbody_get_angularVelocity_Injected_mD00F8790DFF2A31A033487AC67C4C018F28D0D13,
	Rigidbody_set_angularVelocity_Injected_m5ED47D0F131F6B3788B8B736DA7854FD63C13D56,
	Rigidbody_get_centerOfMass_Injected_m4350E78A3E7EC4B0B426A87824EC735462B9946E,
	Rigidbody_set_centerOfMass_Injected_m60EC1BE2558EC9A9BD40ED144A1C848E6A4BCD9D,
	Rigidbody_get_worldCenterOfMass_Injected_m6FADF4899BDD58E674C0CDF09811505E42218E44,
	Rigidbody_get_inertiaTensorRotation_Injected_m4147A057A803E19787F39B4B44F237D4E3D7B5F4,
	Rigidbody_set_inertiaTensorRotation_Injected_m3EF9205426B41821EFEE2E9902792EEDC498A0C2,
	Rigidbody_get_inertiaTensor_Injected_mF43CDE1538A2E5FCD06552A971D3828C12D45480,
	Rigidbody_set_inertiaTensor_Injected_mB625F9A0523C4FF7AB51334B095B3284D6FF062C,
	Rigidbody_get_position_Injected_m16D551CF5A925BD26F4E77116483B2B36115A079,
	Rigidbody_set_position_Injected_mC4BF2A57546741F3E26E82E988646AD1BB22D44E,
	Rigidbody_get_rotation_Injected_mA9DA175CC81C9D6D4D7098C34CF5378C4C2955D8,
	Rigidbody_set_rotation_Injected_mBB08A477E5E134A4E6FFF381BFDE9E2959844EE9,
	Rigidbody_MovePosition_Injected_m06454253A0DF550B2EAD47F545734E8735BA0732,
	Rigidbody_MoveRotation_Injected_mB730FBD0786AE2DEC454E76326E08ED79CEEF440,
	Rigidbody_GetRelativePointVelocity_Injected_mAFF00EBA1B693EBFFF2E66480C87E40675C9482C,
	Rigidbody_GetPointVelocity_Injected_m24C6296C3FB436843956E904B02F907181867118,
	Rigidbody_AddForce_Injected_m233C3E22C3FE9D2BCBBC510132B82CE26057370C,
	Rigidbody_AddRelativeForce_Injected_m2807F26D600ACF197910A90301FFD74A384775FD,
	Rigidbody_AddTorque_Injected_mFFD31FDF8F82D3D740EA83348BBC0D0D5EB0DB3A,
	Rigidbody_AddRelativeTorque_Injected_m0B2D9B655EE6D105CB658C788E52C8228EE195B0,
	Rigidbody_AddForceAtPosition_Injected_m60ED364228EF475F97B0FF1D0F4EFCAB97382DDB,
	Rigidbody_AddExplosionForce_Injected_m8CCDC7FDD8F5F1BC231094105B3076815A16E22D,
	Rigidbody_Internal_ClosestPointOnBounds_Injected_m717C820FF2668DB17C27A6EE470660BB8531D4A7,
	Rigidbody_SweepTest_Injected_mD4F788EFA83E0082B497F8FEA1A2985E058F3A8E,
	Rigidbody_Internal_SweepTestAll_Injected_m8C341BB6B39648FDEC63B3743F45E4955877ABCC,
	Collider_get_enabled_m03B73B5C97033F939387D1785BDF2619CADAEEB0,
	Collider_set_enabled_m047B4D830755CD36671F7A60BFAA9C0D61F6C4A1,
	Collider_get_attachedRigidbody_m101FED12AD292F372F98E94A6D02A5E428AA896A,
	Collider_get_isTrigger_m3A9C990365C94B7125DB5993D782D3D0FE876A60,
	Collider_set_isTrigger_mEDFE3DFA29D42E9DEB9D91A3D25BACC4470305ED,
	Collider_get_contactOffset_mBAB14623F6F9874C126201F785B39CB77CB0F77C,
	Collider_set_contactOffset_m94DF399C14ACBA8C58924BAC2E20384664272139,
	Collider_ClosestPoint_m7777917E298B31796DEE906B54F0102F6ED76676,
	Collider_get_bounds_mE341D29E1DA184ADD53A474D57D9082A3550EACB,
	Collider_get_sharedMaterial_mF09CE26DD4F93A42DEA914489D8201B625EB61F5,
	Collider_set_sharedMaterial_mAAB481A26A050DC39F7C160909D33971E50F3952,
	Collider_get_material_mD442FB26B6A328C13EF4C0091DB8900096B34979,
	Collider_set_material_m3B07EBDE2756F6F250C6202EA1F67C95072B9D72,
	Collider_Raycast_mEB5EDB2C67ABBA9929AE8A898660641E8C82E609,
	Collider_Raycast_m41CA5C3C07B92F5325CB81890BE3A611C3C70784,
	Collider_Internal_ClosestPointOnBounds_m6A81164A787C9A96D7641C2C46F726AAD36DCB22,
	Collider_ClosestPointOnBounds_m3AF12FC44A9FA016DE1AF04B5C281D48F9CC2FE1,
	Collider__ctor_m09D7A9B985D74FD50346DA08D88EB1874E968B69,
	Collider_ClosestPoint_Injected_m6D72FF73D51838EE47234CB4D6234521C08B780D,
	Collider_get_bounds_Injected_m9BA8C3BC133BC241D571849C4F10F67A951CA962,
	Collider_Raycast_Injected_m7176D9E8617D67331E20F519855B8708C63B9E9E,
	Collider_Internal_ClosestPointOnBounds_Injected_mEE9EB425CB119C0E3FAFE788E7FA20E418DFBB82,
	CharacterController_Move_mE0EBC32C72A0BEC18EDEBE748D44309A4BA32E60,
	CharacterController_get_isGrounded_m327A1A1940F225FE81E751F255316BB0D8698CBC,
	CharacterController_Move_Injected_m6A2168A4CDC70CB62E4A4DCB15A74133E8C6C46D,
	BoxCollider_get_center_m832B93439717C72D4A3B427C6E8F5B54E2DBD669,
	BoxCollider_set_center_m02D745E759A66BBEF405D33CE4ACE34B7E064178,
	BoxCollider_get_size_mBC38D4926D4BE54A6532F6E1D642F363CA3A58A1,
	BoxCollider_set_size_mD9153B4AE4C366ACAB9E5F49075D919A89168B2E,
	BoxCollider_get_center_Injected_m5F060E93AE651FB756B9371C7A716BDAA2A96F51,
	BoxCollider_set_center_Injected_m54F8740514B14E3B53063FB4C7623BF0F2A48F0A,
	BoxCollider_get_size_Injected_mC9ABFED6FEC27351BC116B44021578CB39CBCB22,
	BoxCollider_set_size_Injected_m7A8B807F4AB573BF955610F55AAD372FF868C967,
	SphereCollider_get_radius_m403989140BDAD513299276953B481167CF08D02F,
	SphereCollider_set_radius_m55A0D144B32871AECC2A83FBCF423FBE1E5A63A0,
	Joint_get_connectedBody_m35ED1900CACED65F78E5D612BE8979142CBA68A5,
	Joint_set_connectedBody_m572C6C32E2FC5263AECDC460D50E5B0F79727B30,
	Joint_set_axis_m185064B312585680B9384D0B6CDC912646C1503B,
	Joint_get_connectedAnchor_m63E98DACC749ED8F0660FB7998B979CD995AE7BB,
	Joint_set_connectedAnchor_mE47E99BDFB177E3CAAEFEAD0EF119DBAAF1D94CE,
	Joint_set_enableCollision_mF0E7F8D20E2DD89870C2A57FBE19ED34FA3E5E11,
	Joint_set_axis_Injected_m625AB8D052B7B9E62C699A8855A9CE4AB734F713,
	Joint_get_connectedAnchor_Injected_mB79FC8C3EB1414C24CAABFDF28C1C8E016403F3A,
	Joint_set_connectedAnchor_Injected_mA7EAFFA89F0D2ECBA30BB8816423FE33C1A83247,
	HingeJoint_set_limits_m53D8107DCC9A2C46EBB3B3FB1E1C1B33B9F6CCC4,
	HingeJoint_set_useLimits_m7E10377C2CBA985B9FC737E04673099E1A758DEC,
	HingeJoint_set_limits_Injected_mC260E29EF806543F485B4BAB31AEB61C4357FB42,
	PhysicsScene_ToString_mBB6D0AC1E3E2EDC34CB4A7A34485B24B6271903F,
	PhysicsScene_GetHashCode_m5344CC6CCCB1CA6EBB149775EE028089DD74B8A2,
	PhysicsScene_Equals_m2645ABB96C598F197F734EB712494795928CBCEC,
	PhysicsScene_Equals_m8C948B86D105177D519A3608816CDC698C8686B8,
	PhysicsScene_Raycast_m8C3E61EB7C3DB8AAC6FEB098D815062BEF821187,
	PhysicsScene_Internal_RaycastTest_m2FA574D023348CBE214F4F594C9AC155DFB2AD72,
	PhysicsScene_Raycast_mFC0D59C4439EE9DA6E8AA5F6891915132D587208,
	PhysicsScene_Internal_Raycast_m74B409FBA4F2E010CB5668558C70231C656EF249,
	PhysicsScene_Raycast_m95CAB68EB9165E3AB2A4D441F7DF9767AD4B7F73,
	PhysicsScene_Internal_RaycastNonAlloc_mAF1F46E294C23325F3BE18D69987473CB94DE24C,
	PhysicsScene_Query_CapsuleCast_mC84C883774E53A1C1E16BCF78AC97A4ECDA63495,
	PhysicsScene_Internal_CapsuleCast_mAC8303A37BE78D40A38ED2C43596AE86CDEF7402,
	PhysicsScene_CapsuleCast_mE4112B782763A75FE885111A8C45F0B74C2CB784,
	PhysicsScene_Internal_CapsuleCastNonAlloc_m85C990028A307ACEDD117987D1195702D6D17A6F,
	PhysicsScene_CapsuleCast_m16EF2FF9A1944C84082EEC798FE7339A8A78912C,
	PhysicsScene_OverlapCapsuleNonAlloc_Internal_m3644F81F43A7CD947854EED502D95B05ED4CBF24,
	PhysicsScene_OverlapCapsule_mF3E09C4D0CE2D752FB53995D04E1558D8FF69F84,
	PhysicsScene_Query_SphereCast_m5150D76348F358DA3D88B812F52747A5B0AC10CF,
	PhysicsScene_Internal_SphereCast_mBC10DBEC20A03FE3F8B0705EE891F5DE019BAA49,
	PhysicsScene_SphereCast_m72928994362EE11F9A4F72BD79954444B9FD1C2D,
	PhysicsScene_Internal_SphereCastNonAlloc_mBF2B70F0518C54C87CF119C1592CE2D74C9110EC,
	PhysicsScene_SphereCast_m36723F888676E712CC40A0E54D72E95F73D193D3,
	PhysicsScene_OverlapSphereNonAlloc_Internal_m6CCFA59110DAACD0B5150140E221278715A89C84,
	PhysicsScene_OverlapSphere_mBFBB0C810534D52967EE4FDC97DB67301D769ACC,
	PhysicsScene_Query_BoxCast_m25D4BB18A9C4B77B2F2EB6C603948D3B492F8ABD,
	PhysicsScene_Internal_BoxCast_m4E15ED6F445822250915FA7A58A1F91572419F86,
	PhysicsScene_BoxCast_mAE5E55D4420B36B8A6678D420411F29D5687DCEC,
	PhysicsScene_OverlapBoxNonAlloc_Internal_m4835D863262589F253CEDC6347CBA1949E68E6A0,
	PhysicsScene_OverlapBox_mCC9E06BCEE764C2C0A9F2C544A69B996BB37CF27,
	PhysicsScene_Internal_BoxCastNonAlloc_m3AEAE41B64528896C2001D5B8FA9A53B3A01D324,
	PhysicsScene_BoxCast_mDD8A02400064E72BF99C00DE9DFFA29087CC7C4A,
	PhysicsScene_Internal_RaycastTest_Injected_mD9CAD732E985F53D637AF9CCDF5DB8BC34F8FC29,
	PhysicsScene_Internal_Raycast_Injected_mC82D984CE2303F04316EBBCD5313051B1D34B6B4,
	PhysicsScene_Internal_RaycastNonAlloc_Injected_mC9CFA581B636F4DF370330671BB1E4B3ACC2B45B,
	PhysicsScene_Query_CapsuleCast_Injected_m0ADF6977DFF6ECB5E9AA1F89168BD303EE88BA8A,
	PhysicsScene_Internal_CapsuleCastNonAlloc_Injected_m0A4E0190481F3E7955DBEB8DEB54224837041EF2,
	PhysicsScene_OverlapCapsuleNonAlloc_Internal_Injected_m52E4D03F7C223E812DE419E4A9E8187199AFB0B6,
	PhysicsScene_Query_SphereCast_Injected_m7E28C57614720C5F0E83AFC3589696D96C477105,
	PhysicsScene_Internal_SphereCastNonAlloc_Injected_m65852DE0EC1AEDB7779B1853A4AD89CB2052DC0D,
	PhysicsScene_OverlapSphereNonAlloc_Internal_Injected_mA640B629980F886775675074E03F59AD2EEC2C9C,
	PhysicsScene_Query_BoxCast_Injected_mCD6D0B2A284845FA7D43EC7B2E77F52B7B284681,
	PhysicsScene_OverlapBoxNonAlloc_Internal_Injected_m76D78AF795A0DC08207701B6573712967A13338B,
	PhysicsScene_Internal_BoxCastNonAlloc_Injected_mD53C95CED27205E43EA37253B5BB179777EFCACB,
	Physics_get_gravity_mCF549D37AAB738732815F2E45DB47552061571EF,
	Physics_set_gravity_mA651A465DC97AC2D6C5846AF91AFB62F2594DF85,
	Physics_get_defaultContactOffset_m4A5BF641B9187316F95FCF65CEBEFD3786BDFBF4,
	Physics_set_defaultContactOffset_m1ED6925D6DDAD60D1BA7A643D9718B214D02E129,
	Physics_get_sleepThreshold_m97ECEAB5C22071AB0219488CA7C7CBD78CA41B1F,
	Physics_set_sleepThreshold_m40D2DBD5A5B45FF93F1A7EB17F65D77728D729E0,
	Physics_get_queriesHitTriggers_mBAA08B9F06EC19C8A0647556EE7CFA459D36E887,
	Physics_set_queriesHitTriggers_m3B2B28824562A6CB94BE472038BFE4F234FF641C,
	Physics_get_queriesHitBackfaces_m29273BEEDF63D1148338F9EF9CC2D3E08A643C0F,
	Physics_set_queriesHitBackfaces_m8D34CD58EF94CFCD569778970F13E3EE9B3F836B,
	Physics_get_bounceThreshold_m420A9C0CDDE0D681F01D4E53BA5ACB94722D69AA,
	Physics_set_bounceThreshold_m238F8AC404A1D80055C456353C915BC59E095397,
	Physics_get_defaultSolverIterations_mEBF50C28729C00A527F3A43C169ACAE53C327DD6,
	Physics_set_defaultSolverIterations_m0054664B7BE9957DAF84E9DDD27B35CFB717104E,
	Physics_get_defaultSolverVelocityIterations_m0FF2EFD6CA19B89614FA753C88CF206D27DF946C,
	Physics_set_defaultSolverVelocityIterations_m8DA5B46039E09805F3B5C14BC86717673BD2D081,
	Physics_get_defaultMaxAngularSpeed_m2D2136434C45AFF227A2DB2A973785A62076DA93,
	Physics_set_defaultMaxAngularSpeed_m3CDDEBDFE971F4798B52075D16A0EC2C0E2E46D6,
	Physics_get_defaultPhysicsScene_m288502B1B143487DF1448AC572A68D3B28FB2BF4,
	Physics_IgnoreCollision_m7B4B87FA8DAE966DB15A6825652452ADAB43BEB8,
	Physics_IgnoreCollision_m769C875FBAD8A0DB42F7A69B79E88C71DA0F1063,
	Physics_IgnoreLayerCollision_m99FDB1D2B4F7724B13A2C09E47525A251316C148,
	Physics_IgnoreLayerCollision_m4F9369FFB51FF3E5A019D242CCF50FBFEE318ECC,
	Physics_GetIgnoreLayerCollision_m8F3D7439829F66E2492184848C24A9A9D0D53CFC,
	Physics_GetIgnoreCollision_m336E3E9FA9352CF75D6086790662DEB1D36CFA8F,
	Physics_Raycast_mC2ED77DB34C975B8A8864A8799B844D44800E8FE,
	Physics_Raycast_m8B96F9B0424A82607EED1B73D708B56E2E61ACD0,
	Physics_Raycast_m1F314F0281C2AA6B467EB66931B95F0A92C55708,
	Physics_Raycast_m7B0F0A428505EE139EF5F36BE2E2463A4A6C1C95,
	Physics_Raycast_m9DC5CC69286EA72DB6CEC663A5F938080831CED6,
	Physics_Raycast_mE9A6DAE4102F053486651A3BF1F3C8B0F4CD7A32,
	Physics_Raycast_m43E2A84B040AAAFC9A9F436C6A04573A83B997C7,
	Physics_Raycast_m1E3A92D5F7F0003A8ED720CD5755B0FEB12BD8DB,
	Physics_Raycast_m8288105D3EC843C79ED946886D58232BF9140E38,
	Physics_Raycast_mDE7B06FE5C7BDA1976A3D5C458133E2118AF7D0A,
	Physics_Raycast_mF3D9C6730E052E32589B7392D041A21BCE79FBA8,
	Physics_Raycast_m0CC2DCC02C393A610EA468445B5357402CF4B23B,
	Physics_Raycast_m995228D47F1AA54815D5271D1D739449F7CF39D6,
	Physics_Raycast_mAA9D393D6D057BF7F08BA93E904295214E1F8CCC,
	Physics_Raycast_mF473DC2E69B2A4E013F03867CE044FB9901565C6,
	Physics_Raycast_m4BD6E5347507B86D0D50C5834607BB23D3DBD82A,
	Physics_Linecast_mD175C9F520F54D09AA3432A71D80A5FC643DAD09,
	Physics_Linecast_mCFA4ADE87BFF8EBEDC892DC05768235100899459,
	Physics_Linecast_m194E27535A8948EAD7F1AE58522344522E2CB414,
	Physics_Linecast_m95E69EC3D42F8D5362590118844045949D360F12,
	Physics_Linecast_m2F66BEABC1E34AFC3F9717DCB4F1915CE3546731,
	Physics_Linecast_m98AF5B5C9E77E3B681C8974A490B4218F502719B,
	Physics_CapsuleCast_mD00B6D94F44E25202B741D283891BCB1138B6A8E,
	Physics_CapsuleCast_m28FCEE4A3DF8A68E9C6F248A0AC4287936D697A7,
	Physics_CapsuleCast_m30A116B9E18E5F3517BDC4478CFEB1ED973DA643,
	Physics_CapsuleCast_m0BDCBBA11A3479147AF50CFA204AA86F244CA178,
	Physics_CapsuleCast_m7A44E2160394EDC5C08A47B143DADE088C5A8E4C,
	Physics_CapsuleCast_mFC4A2A611CF0B360297E8D87AF8E5A7A2D6315A2,
	Physics_CapsuleCast_m477D45784AE0F4CC74192C7C63001B11663CDD89,
	Physics_CapsuleCast_mB0AA4D1A09551AA956D69996AC9C2D945C2AAD23,
	Physics_SphereCast_mC123DF7428845FC3112819137F950D29B42A2382,
	Physics_SphereCast_m01F30F8108E427351C03CC12B782ADAD289E5D60,
	Physics_SphereCast_mCAEAA10F0E51A8C5AA11B5F7B35C3914394B7B0A,
	Physics_SphereCast_mF0DC9AD714B54734965733FE4AA490E6D07F1D93,
	Physics_SphereCast_mBEBFBC17AB2E31C665C19B1DB778C2385E62B0D5,
	Physics_SphereCast_m3840EDBB05C4844D16A1A30449A00652220061E7,
	Physics_SphereCast_mEFCE461C3C125942FCA06724D158873FE8A9F314,
	Physics_SphereCast_m1C39876096F8D1FDBBF2D41E54E02FADF09C5841,
	Physics_SphereCast_m670A7A5DE3A132CFE61DCEDC3E664630E0FAF619,
	Physics_SphereCast_mD46CFBE396789673EF42AC03F26326A8C133229E,
	Physics_SphereCast_m9ED6FDBE7726A67E713EE768ABDE60268D19F420,
	Physics_SphereCast_m5901E70B4124F6510FFC7E077C5BF4143B739585,
	Physics_BoxCast_mD64DA9359F8E3E76CC3F42CC7FEA1EA0CA79CC81,
	Physics_BoxCast_m042D832EAA9ACD0DAD3A8329E03951BC249EB05A,
	Physics_BoxCast_mBB6CBDA0AD8BB036B4C7582F3DD9B1027BF6978B,
	Physics_BoxCast_mFFBFFC3C48436EA4D4B5F4AC447A42015F170B74,
	Physics_BoxCast_mA48BAA61954ABE2C1A216EEF05C118B24D8A2EF5,
	Physics_BoxCast_m0CB099A9DF4E018CA2577889001B823066C3D9AA,
	Physics_BoxCast_m727075A9E9CC0D62D85F400F5B103171EC4A320F,
	Physics_BoxCast_mAA9130AED2A87AD6CD8BE207388214DBC182B1B3,
	Physics_BoxCast_m79A26C19C10E0F725DE832DE8F72F2AA6E28687F,
	Physics_BoxCast_m55051B35127A00F2C9652895A09C5D34EF47FA13,
	Physics_Internal_RaycastAll_m9DC824664677D74824150C2ABC56DDE36727DC26,
	Physics_RaycastAll_m50B811C17026AE8F23D5171E588F0DA77BE9C5FB,
	Physics_RaycastAll_m4230866EDA233176EE4074CA63C69B8B8B672181,
	Physics_RaycastAll_m0A1AE92D5D3B597D09E76F11F1D1C09493F4C0E8,
	Physics_RaycastAll_m14F828FD2B643D48B6A2C8C78C978826989C07B4,
	Physics_RaycastAll_m02B60871145F3BB5CA7D555A6C0C157EF0251BC6,
	Physics_RaycastAll_m34E79BFFFC6E6FE555793C1BFB4E922C81D4FA9D,
	Physics_RaycastAll_m6CCB6B2F7A8A0DB9F4E2952AAD3CD31D00B8266B,
	Physics_RaycastAll_m21E71941A58CD333D3F838D54BAE8BA26987A020,
	Physics_RaycastNonAlloc_m207571490C7194D4C6679BE398AE4EE661053FD0,
	Physics_RaycastNonAlloc_m3F953754DE454BDFC9D028DB608C5F29AB6C210C,
	Physics_RaycastNonAlloc_m6200490E0C211A635056EB97BD6C5B94A8F3C9F7,
	Physics_RaycastNonAlloc_m58C5B4D8925665228D7C13CFC5D82930321DE65A,
	Physics_RaycastNonAlloc_m6B62C55FFC3F78B03C7AAA54047137702E7B82F6,
	Physics_RaycastNonAlloc_mFA018B555BF77B9FDDCF991B4318EEFEE6BE55F8,
	Physics_RaycastNonAlloc_m8ED1C92DD870E106825951F32E86EDBA9FA83998,
	Physics_RaycastNonAlloc_m6724BF59D2C8D914FD2A68801CFC9EC103D8B119,
	Physics_Query_CapsuleCastAll_mEF1513DF9E25AF52278569B40B6075CB385FC06C,
	Physics_CapsuleCastAll_m90620435B6C908294E630FF753CB9C343C457833,
	Physics_CapsuleCastAll_m5A64F9F9D8E1BB0B8F1C58B054F4EE7E328C6D3C,
	Physics_CapsuleCastAll_mF89FEFC7979B1349BC53C1FA0C851FE58F511EF0,
	Physics_CapsuleCastAll_mB31F2D50FB62F15EAE5D1504A62E9604ABC2D42F,
	Physics_Query_SphereCastAll_m32DE4D6204490ED1E0DE4A181220671228E50FD3,
	Physics_SphereCastAll_m2B8F24D21CB6700CC9F20A5758136067100770A8,
	Physics_SphereCastAll_mB61075364B1C10840BBF8F53071C461B331EA9DB,
	Physics_SphereCastAll_m57254E256F92A72C77FDFBEB9ECABD9605683333,
	Physics_SphereCastAll_m023EFD34F22884F3ED82CB6B0C025CC33308A28B,
	Physics_SphereCastAll_m4CCF0ED9EB61C5530500ED6D3B8E29F5E4103F64,
	Physics_SphereCastAll_mE22B49CCBB29AF282B40E364017C62F7CE2963A6,
	Physics_SphereCastAll_m90D17F15D8C473D0ABD035AE948CFC196A0301A2,
	Physics_SphereCastAll_m4CB0FFC0DF4E2C126601FEFE46477391C1F8FACE,
	Physics_OverlapCapsule_Internal_m611EAB1AF03BAB47D00967B03FAF55B38113C658,
	Physics_OverlapCapsule_m89D89478C2B9A19CF308090CD763C0E3981194EE,
	Physics_OverlapCapsule_mCF12621D08D097BF930031F2E8EFF4E98912D912,
	Physics_OverlapCapsule_m4A76BB201F50BC3A3FCE53B0F8821121C243B89C,
	Physics_OverlapSphere_Internal_mADD6D653247DE323AAD64B0641352F764C8854A0,
	Physics_OverlapSphere_m704A1ACBC7123FD92E077440D4F96F2DE409624F,
	Physics_OverlapSphere_m7B1E4E0363F42D9802125B0D6E08F5D6D67262E2,
	Physics_OverlapSphere_m885BB636D248BA40DF5758A7AA108C010A942E62,
	Physics_Simulate_Internal_m4460711B1389A3B4E3D40188FCCE0D5D48D5384D,
	Physics_Simulate_m0B827845B3B97CC9A71B52A0AC9877E3F6FCE3BC,
	Physics_get_autoSimulation_mC34C28A28B053F73E40EC2A09FA676A180A6F8C2,
	Physics_set_autoSimulation_mD5AF1E2E7425452E2BA0F6E71ADF107BB08835BF,
	Physics_SyncTransforms_m3FF00DAB8D8E79D9DEA225E8CA0C00E206EB8C74,
	Physics_get_autoSyncTransforms_m2C8DA55CD646DD8E6271AF2C5160AFC31A41D131,
	Physics_set_autoSyncTransforms_m83D9B428CBC18F8E1FF334A5DD5F5556737A4CF8,
	Physics_get_reuseCollisionCallbacks_mBF40FFE360ECC9C866D74528EBC156F088BB071A,
	Physics_set_reuseCollisionCallbacks_m3FB20EE28FEAFCD0F41C8B4EC6715A29996FD67F,
	Physics_Query_ComputePenetration_m27C7018A8A1669FA0E36ACE99B8CCEAE8EC1FB40,
	Physics_ComputePenetration_m3507EFEA7923266CFA2961A5A587152F35BCB250,
	Physics_Query_ClosestPoint_mF5BA23B046F75113515288123F33A0D1A7B72CFC,
	Physics_ClosestPoint_m81A2630C69D5A78A2CA64466E076F412393C1AFF,
	Physics_get_interCollisionDistance_m4D3641513DEC83AD79F83E3D222A179F1854D9FC,
	Physics_set_interCollisionDistance_mB2FAA04952085BC7AF9BB1CD5FF0C3B77CCE4796,
	Physics_get_interCollisionStiffness_mADD8F984B28FFBD4FEB51D9A420B1C1A733BED76,
	Physics_set_interCollisionStiffness_m879C28BD4960D01CFBD497333A7C90DD458446F8,
	Physics_get_interCollisionSettingsToggle_m8D9C709B397617445F0CB4503B2C302D35684D09,
	Physics_set_interCollisionSettingsToggle_m5870AA9109D6E63D030E0A79A64E74600273830E,
	Physics_get_clothGravity_m12196FC75700B233CEAACBA1625F749C154455EE,
	Physics_set_clothGravity_mA3277A26FF78F2645D7FD5A7B2DF9D6751712685,
	Physics_OverlapSphereNonAlloc_m581B60F800CD970C67BE48AFA30322B4EA2D8590,
	Physics_OverlapSphereNonAlloc_m1854FF819705ACDD1429795466C8763400924DA1,
	Physics_OverlapSphereNonAlloc_m04C0A399F168EF52077546DCA9C657928CEBE3BB,
	Physics_CheckSphere_Internal_mB1F09D00D7F7602F50730EE91DD42FFB3D934D9B,
	Physics_CheckSphere_mB1295ED25B19CDECC79C15378AD27B7E4B95C919,
	Physics_CheckSphere_mD36EC68D5324CF6F8100D76FBC0B2C8D29EA21E1,
	Physics_CheckSphere_m595752F39C0F4FFF79F25A49A8F90FCF34130540,
	Physics_CapsuleCastNonAlloc_mACC7729211C47FFDEDFEC81B2C381107BDD7E8EA,
	Physics_CapsuleCastNonAlloc_m7E511B9D9300B4FBD745377E7B435B1E12138450,
	Physics_CapsuleCastNonAlloc_mFF5EF48CD3901AEEA12BF57246ABFF398ACC75BC,
	Physics_CapsuleCastNonAlloc_mA13B34C4D1CEA663A52EA7845680C853948CA454,
	Physics_SphereCastNonAlloc_mE792A0BF759B32E2F80B7465D3971A6FAAF78E49,
	Physics_SphereCastNonAlloc_m0EE4E1CFE7225F8ABF36344D2BF94F915428EA2E,
	Physics_SphereCastNonAlloc_mDB4CE103704C3B243613A0826AA4BC796F773C82,
	Physics_SphereCastNonAlloc_mD45E46F06F9A10743D36C13C9BCEA9042EFFB07E,
	Physics_SphereCastNonAlloc_m23C464FB433A863BF7EF37F77D3BF1A5646DE916,
	Physics_SphereCastNonAlloc_mB43840A3BD3D4C030B5EC41AE36003A84620B4EE,
	Physics_SphereCastNonAlloc_m48B3F8CD32F7C79519732D191EC5A6EF3342FA54,
	Physics_SphereCastNonAlloc_mFC9FEF63227FBF067D9E297611D1AAD3633A6CA5,
	Physics_CheckCapsule_Internal_m358855F921DCA4CCCF24E4CD26FD422FC6065A04,
	Physics_CheckCapsule_m0FFF18BD59B797BAEE4C2F33E57B12D25FB2AFF7,
	Physics_CheckCapsule_m2D66253F9DD04BC6E80CCABCB4FD66FB486B2A76,
	Physics_CheckCapsule_m302F3C16A0600B390FD7427FA36348C2C7F12701,
	Physics_CheckBox_Internal_m02F89F4BBCA2A05655629028A2518273752F5FBA,
	Physics_CheckBox_m873FC33A295AC107EEED26DA42C270163F00D117,
	Physics_CheckBox_m2AC5348166BB0F9A95016FB1C7589D17A2A991F6,
	Physics_CheckBox_m3EAFA5E4FBC70232F85D606AAB433938315D6458,
	Physics_CheckBox_m7FB3EE66186B392CA3A892743464AE15268B2ACA,
	Physics_OverlapBox_Internal_mAF19FB424100FB79AB7224B417E413DED97BE18E,
	Physics_OverlapBox_mADF055F975454C0628F78AD24507B13BAB91BE65,
	Physics_OverlapBox_m77C350471E227B1EDD9E42EE520538D2E2B7804A,
	Physics_OverlapBox_m1EAB50779182ADD2653CC3368720A76799BC0559,
	Physics_OverlapBox_mC73C23157BDA00BC00DDF4DA48D33C1B81F409CA,
	Physics_OverlapBoxNonAlloc_mE89462281DE54BC30F6172BEB192EAC91E1AD9C4,
	Physics_OverlapBoxNonAlloc_m0CA09577CF79A0A264775BE4D26E756BD8B7EC8B,
	Physics_OverlapBoxNonAlloc_m211E1209B7E4EC9D93DFA65BB0911C1B0CD50BBD,
	Physics_OverlapBoxNonAlloc_mE22635EBC8813B63F5DB11BA659CD0868B6BE15A,
	Physics_BoxCastNonAlloc_m1F42BF8BE68DE74C75191FD3D7825A7733FF7D5F,
	Physics_BoxCastNonAlloc_mA43BADF85365434F5BDAB835DC34F32729CAE0CF,
	Physics_BoxCastNonAlloc_mFA2B2C59E8B24A54587E57CCFB3FF1614769A13D,
	Physics_BoxCastNonAlloc_m1451356413E14DA402AC5D9F967A6EDD993C1005,
	Physics_BoxCastNonAlloc_m48C7CC1B46D2922D27E3771B68F6084FA933F682,
	Physics_Internal_BoxCastAll_m4414579DD2981AE23D714D2BC2D231CE1F3CDE07,
	Physics_BoxCastAll_m052716E5F18B96C40671B75EC508B5F3477C10E7,
	Physics_BoxCastAll_m1B41F3BB2532BF9924F69749FC3E9C1B02AD4E68,
	Physics_BoxCastAll_m9125F90A557E2132569AE1836BB03C3BC2C232AF,
	Physics_BoxCastAll_mC2770103DE07FAD2A04E6179EF2D3A86CF0EBB8D,
	Physics_BoxCastAll_mE2EA842AF0A63D23F56CFF850C11DC656BB19180,
	Physics_OverlapCapsuleNonAlloc_mCA6619D7D415CDC6850A7F6DD697254032177150,
	Physics_OverlapCapsuleNonAlloc_m202BFCF12138EB1A71F75A4D31C7AAA369BDB0E4,
	Physics_OverlapCapsuleNonAlloc_mCEB1FBC60B432B1FFEDC3008E996536C645C03FD,
	Physics_Internal_RebuildBroadphaseRegions_m2ECCEDCB20F5768F7B3A9AC7910539DD8153E67B,
	Physics_RebuildBroadphaseRegions_m3711D2CA3B931D45B21F150661C887F5DD21DC06,
	Physics_BakeMesh_mF4A190E866D7FF2A67D22396FE0677AD30B860EC,
	Physics_get_gravity_Injected_mF329CFCFDD3CCBB9C83FB3FC27D54226544548F9,
	Physics_set_gravity_Injected_m25AE2B7326ADA49229C493A84F327B94B61FD276,
	Physics_get_defaultPhysicsScene_Injected_m6B5189999E482654591F8988C09EADA72F6A478C,
	Physics_Internal_RaycastAll_Injected_mE0CDD075B9C29933B7189DD9E99A692FB9010C26,
	Physics_Query_CapsuleCastAll_Injected_mF02B3FD2575254AB6DA9C4E56D408D53DE0220E1,
	Physics_Query_SphereCastAll_Injected_m695204A02A22A17BEAA4120E7334A2F84D2993F4,
	Physics_OverlapCapsule_Internal_Injected_m0318F1E1F0F3C28043B73411CCEC1FC2A9935407,
	Physics_OverlapSphere_Internal_Injected_mAB077CE432470B2525C904ECD186B5F46FFF0C4D,
	Physics_Simulate_Internal_Injected_mFC4DAFF4D3BE67C136C0007A0C74410B3B3C97E4,
	Physics_Query_ComputePenetration_Injected_m2CC6ABE59D42D70E50A372F183E804008EBEF9B2,
	Physics_Query_ClosestPoint_Injected_m30B87B53D0F89297194D75ABD18176A54BF14DC7,
	Physics_get_clothGravity_Injected_m0A51AE32AB3C836684157CB2EAE8E992EBB7D195,
	Physics_set_clothGravity_Injected_mF80D3003F454871C216746B88250E0BCB5FE2779,
	Physics_CheckSphere_Internal_Injected_m06FDDD418D8EADC1D95623F5EF1190A7403D27E8,
	Physics_CheckCapsule_Internal_Injected_mFC3C06D76A130ED877585C8B6B395BAFC8576E38,
	Physics_CheckBox_Internal_Injected_m0E704E4D55BC1E51BE4EBC1B6F1FB6D7A714D07D,
	Physics_OverlapBox_Internal_Injected_m2222CE0F8F5F58A020B7F00BDAD43F6AA5B62817,
	Physics_Internal_BoxCastAll_Injected_mD1C1259FD5D4B991CBD3045BC6D2A6890B33735B,
	Physics_Internal_RebuildBroadphaseRegions_Injected_mAFB25CEEE3B2D8BFA9B85AA0C3C4A90B6358CAB2,
};
extern void JointLimits_set_min_m500E297C572E2690D0678EC354BFE9AF7F27D2FE_AdjustorThunk (void);
extern void JointLimits_set_max_mE767D8DE579BBFBF505B5A8316A0B04D8F53775C_AdjustorThunk (void);
extern void RaycastHit_get_collider_m13A3DE16FBC631E0A1F987E0B22CE70AF8AB539E_AdjustorThunk (void);
extern void RaycastHit_get_point_m32F7282CBB2E13393A33BAD046BDA218E48DD21E_AdjustorThunk (void);
extern void RaycastHit_get_normal_m2C813B25BAECD87FD9E9CB294278B291F4CC6674_AdjustorThunk (void);
extern void RaycastHit_get_distance_m85FCA98D7957C3BF1D449CA1B48C116CCD6226FA_AdjustorThunk (void);
extern void RaycastHit_get_transform_m2DD983DBD3602DE848DE287EE5233FD02EEC608D_AdjustorThunk (void);
extern void RaycastHit_get_rigidbody_mE48E45893FDAFD03162C6A73AAF02C6CB0E079FB_AdjustorThunk (void);
extern void PhysicsScene_ToString_mBB6D0AC1E3E2EDC34CB4A7A34485B24B6271903F_AdjustorThunk (void);
extern void PhysicsScene_GetHashCode_m5344CC6CCCB1CA6EBB149775EE028089DD74B8A2_AdjustorThunk (void);
extern void PhysicsScene_Equals_m2645ABB96C598F197F734EB712494795928CBCEC_AdjustorThunk (void);
extern void PhysicsScene_Equals_m8C948B86D105177D519A3608816CDC698C8686B8_AdjustorThunk (void);
extern void PhysicsScene_Raycast_m8C3E61EB7C3DB8AAC6FEB098D815062BEF821187_AdjustorThunk (void);
extern void PhysicsScene_Raycast_mFC0D59C4439EE9DA6E8AA5F6891915132D587208_AdjustorThunk (void);
extern void PhysicsScene_Raycast_m95CAB68EB9165E3AB2A4D441F7DF9767AD4B7F73_AdjustorThunk (void);
extern void PhysicsScene_CapsuleCast_mE4112B782763A75FE885111A8C45F0B74C2CB784_AdjustorThunk (void);
extern void PhysicsScene_CapsuleCast_m16EF2FF9A1944C84082EEC798FE7339A8A78912C_AdjustorThunk (void);
extern void PhysicsScene_OverlapCapsule_mF3E09C4D0CE2D752FB53995D04E1558D8FF69F84_AdjustorThunk (void);
extern void PhysicsScene_SphereCast_m72928994362EE11F9A4F72BD79954444B9FD1C2D_AdjustorThunk (void);
extern void PhysicsScene_SphereCast_m36723F888676E712CC40A0E54D72E95F73D193D3_AdjustorThunk (void);
extern void PhysicsScene_OverlapSphere_mBFBB0C810534D52967EE4FDC97DB67301D769ACC_AdjustorThunk (void);
extern void PhysicsScene_BoxCast_mAE5E55D4420B36B8A6678D420411F29D5687DCEC_AdjustorThunk (void);
extern void PhysicsScene_OverlapBox_mCC9E06BCEE764C2C0A9F2C544A69B996BB37CF27_AdjustorThunk (void);
extern void PhysicsScene_BoxCast_mDD8A02400064E72BF99C00DE9DFFA29087CC7C4A_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[24] = 
{
	{ 0x06000001, JointLimits_set_min_m500E297C572E2690D0678EC354BFE9AF7F27D2FE_AdjustorThunk },
	{ 0x06000002, JointLimits_set_max_mE767D8DE579BBFBF505B5A8316A0B04D8F53775C_AdjustorThunk },
	{ 0x06000006, RaycastHit_get_collider_m13A3DE16FBC631E0A1F987E0B22CE70AF8AB539E_AdjustorThunk },
	{ 0x06000007, RaycastHit_get_point_m32F7282CBB2E13393A33BAD046BDA218E48DD21E_AdjustorThunk },
	{ 0x06000008, RaycastHit_get_normal_m2C813B25BAECD87FD9E9CB294278B291F4CC6674_AdjustorThunk },
	{ 0x06000009, RaycastHit_get_distance_m85FCA98D7957C3BF1D449CA1B48C116CCD6226FA_AdjustorThunk },
	{ 0x0600000A, RaycastHit_get_transform_m2DD983DBD3602DE848DE287EE5233FD02EEC608D_AdjustorThunk },
	{ 0x0600000B, RaycastHit_get_rigidbody_mE48E45893FDAFD03162C6A73AAF02C6CB0E079FB_AdjustorThunk },
	{ 0x060000AE, PhysicsScene_ToString_mBB6D0AC1E3E2EDC34CB4A7A34485B24B6271903F_AdjustorThunk },
	{ 0x060000AF, PhysicsScene_GetHashCode_m5344CC6CCCB1CA6EBB149775EE028089DD74B8A2_AdjustorThunk },
	{ 0x060000B0, PhysicsScene_Equals_m2645ABB96C598F197F734EB712494795928CBCEC_AdjustorThunk },
	{ 0x060000B1, PhysicsScene_Equals_m8C948B86D105177D519A3608816CDC698C8686B8_AdjustorThunk },
	{ 0x060000B2, PhysicsScene_Raycast_m8C3E61EB7C3DB8AAC6FEB098D815062BEF821187_AdjustorThunk },
	{ 0x060000B4, PhysicsScene_Raycast_mFC0D59C4439EE9DA6E8AA5F6891915132D587208_AdjustorThunk },
	{ 0x060000B6, PhysicsScene_Raycast_m95CAB68EB9165E3AB2A4D441F7DF9767AD4B7F73_AdjustorThunk },
	{ 0x060000BA, PhysicsScene_CapsuleCast_mE4112B782763A75FE885111A8C45F0B74C2CB784_AdjustorThunk },
	{ 0x060000BC, PhysicsScene_CapsuleCast_m16EF2FF9A1944C84082EEC798FE7339A8A78912C_AdjustorThunk },
	{ 0x060000BE, PhysicsScene_OverlapCapsule_mF3E09C4D0CE2D752FB53995D04E1558D8FF69F84_AdjustorThunk },
	{ 0x060000C1, PhysicsScene_SphereCast_m72928994362EE11F9A4F72BD79954444B9FD1C2D_AdjustorThunk },
	{ 0x060000C3, PhysicsScene_SphereCast_m36723F888676E712CC40A0E54D72E95F73D193D3_AdjustorThunk },
	{ 0x060000C5, PhysicsScene_OverlapSphere_mBFBB0C810534D52967EE4FDC97DB67301D769ACC_AdjustorThunk },
	{ 0x060000C8, PhysicsScene_BoxCast_mAE5E55D4420B36B8A6678D420411F29D5687DCEC_AdjustorThunk },
	{ 0x060000CA, PhysicsScene_OverlapBox_mCC9E06BCEE764C2C0A9F2C544A69B996BB37CF27_AdjustorThunk },
	{ 0x060000CC, PhysicsScene_BoxCast_mDD8A02400064E72BF99C00DE9DFFA29087CC7C4A_AdjustorThunk },
};
static const int32_t s_InvokerIndices[426] = 
{
	3380,
	3380,
	3916,
	3967,
	5834,
	3916,
	3965,
	3965,
	3951,
	3916,
	3916,
	3965,
	3398,
	3965,
	3398,
	3951,
	3380,
	3951,
	3380,
	3951,
	3380,
	3380,
	3863,
	3276,
	3951,
	3380,
	3863,
	3276,
	3863,
	3276,
	3893,
	3313,
	3893,
	3313,
	3965,
	3398,
	3965,
	3930,
	3363,
	3965,
	3398,
	3863,
	3276,
	3965,
	3398,
	3930,
	3363,
	3893,
	3313,
	3893,
	3313,
	3951,
	3380,
	3951,
	3380,
	3398,
	3363,
	3967,
	3863,
	3967,
	3967,
	3967,
	3159,
	3159,
	3893,
	3313,
	2219,
	3398,
	918,
	1368,
	2219,
	3398,
	918,
	1368,
	2219,
	3398,
	918,
	1368,
	2219,
	3398,
	918,
	1368,
	1386,
	2223,
	440,
	921,
	1369,
	1380,
	3159,
	786,
	471,
	1000,
	1514,
	1181,
	1181,
	1746,
	3057,
	3967,
	3255,
	3255,
	3255,
	3255,
	3255,
	3255,
	3255,
	3255,
	3255,
	3255,
	3255,
	3255,
	3255,
	3255,
	3255,
	3255,
	3255,
	1822,
	1822,
	1824,
	1824,
	1824,
	1824,
	1245,
	436,
	1244,
	357,
	1061,
	3863,
	3276,
	3916,
	3863,
	3276,
	3951,
	3380,
	3159,
	3862,
	3916,
	3348,
	3916,
	3348,
	1208,
	994,
	1380,
	3159,
	3967,
	1822,
	3255,
	808,
	1244,
	2937,
	3863,
	2845,
	3965,
	3398,
	3965,
	3398,
	3255,
	3255,
	3255,
	3255,
	3951,
	3380,
	3916,
	3348,
	3398,
	3965,
	3398,
	3276,
	3255,
	3255,
	3255,
	3319,
	3276,
	3255,
	3916,
	3893,
	2591,
	2595,
	293,
	4364,
	174,
	4228,
	179,
	4253,
	4060,
	4059,
	68,
	4064,
	74,
	4153,
	180,
	4088,
	4087,
	111,
	4094,
	114,
	4254,
	308,
	4061,
	4058,
	69,
	4152,
	178,
	4065,
	75,
	4347,
	4205,
	4242,
	4056,
	4063,
	4144,
	4079,
	4091,
	4243,
	4055,
	4143,
	4062,
	6287,
	6234,
	6281,
	6228,
	6281,
	6228,
	6256,
	6208,
	6256,
	6208,
	6281,
	6228,
	6263,
	6213,
	6263,
	6213,
	6281,
	6228,
	6271,
	5334,
	5834,
	5283,
	5744,
	5424,
	5433,
	4375,
	4656,
	5058,
	5459,
	4235,
	4373,
	4653,
	5055,
	4641,
	5046,
	5441,
	5924,
	4366,
	4639,
	5044,
	5440,
	4654,
	5056,
	5459,
	4372,
	4652,
	5055,
	4140,
	4237,
	4377,
	4657,
	4089,
	4139,
	4236,
	4376,
	4138,
	4234,
	4371,
	4651,
	4368,
	4642,
	5047,
	5441,
	4231,
	4367,
	4640,
	5045,
	4142,
	4239,
	4379,
	4659,
	5059,
	4090,
	4141,
	4238,
	4378,
	4658,
	4530,
	4537,
	4908,
	5224,
	5618,
	4898,
	5210,
	5606,
	6092,
	4458,
	4767,
	5111,
	5529,
	4261,
	4464,
	4773,
	5116,
	4109,
	4180,
	4302,
	4538,
	4909,
	4179,
	4301,
	4535,
	4906,
	5222,
	4533,
	4899,
	5211,
	5606,
	4299,
	4537,
	4908,
	5224,
	4531,
	4905,
	5221,
	5617,
	5843,
	6228,
	6256,
	6208,
	6289,
	6256,
	6208,
	6256,
	6208,
	4086,
	4086,
	4931,
	4933,
	6281,
	6228,
	6281,
	6228,
	6256,
	6208,
	6287,
	6234,
	4461,
	4770,
	5115,
	4365,
	4650,
	5054,
	5458,
	4095,
	4155,
	4263,
	4466,
	4154,
	4259,
	4462,
	4771,
	4257,
	4459,
	4768,
	5112,
	4230,
	4375,
	4656,
	5058,
	4229,
	4374,
	4655,
	5057,
	5459,
	4298,
	4536,
	4907,
	5223,
	5618,
	4260,
	4463,
	4772,
	5116,
	4096,
	4467,
	4264,
	4156,
	4775,
	4110,
	4181,
	4303,
	4539,
	4910,
	5225,
	4262,
	4465,
	4774,
	5735,
	5735,
	5742,
	6206,
	6206,
	6206,
	4472,
	4099,
	4157,
	4268,
	4472,
	5734,
	4084,
	4558,
	6206,
	6206,
	4347,
	4202,
	4201,
	4267,
	4098,
	5731,
};
extern const CustomAttributesCacheGenerator g_UnityEngine_PhysicsModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_PhysicsModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_PhysicsModule_CodeGenModule = 
{
	"UnityEngine.PhysicsModule.dll",
	426,
	s_methodPointers,
	24,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_UnityEngine_PhysicsModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void Microsoft.CodeAnalysis.EmbeddedAttribute::.ctor()
extern void EmbeddedAttribute__ctor_m4E9EEA70016F5DDF1137BD478DC0BB0D2A3EB1A0 (void);
// 0x00000002 System.Void System.Runtime.CompilerServices.IsReadOnlyAttribute::.ctor()
extern void IsReadOnlyAttribute__ctor_m6627A1A5600798A4080F1046794105F485DEB04B (void);
// 0x00000003 System.Void UnityEngine.XR.InputTracking::InvokeTrackingEvent(UnityEngine.XR.InputTracking/TrackingStateEventType,UnityEngine.XR.XRNode,System.Int64,System.Boolean)
extern void InputTracking_InvokeTrackingEvent_m732DB62FD504609050D52FF6BA12C6D46E5A7669 (void);
// 0x00000004 System.Void UnityEngine.XR.InputTracking::.cctor()
extern void InputTracking__cctor_m28CF1AB55D1D93F9A27D14175010EA72686185BA (void);
// 0x00000005 System.Void UnityEngine.XR.XRNodeState::set_uniqueID(System.UInt64)
extern void XRNodeState_set_uniqueID_m09D6E80AD1B81D7485242CFA27A7173A6231CA87 (void);
// 0x00000006 System.Void UnityEngine.XR.XRNodeState::set_nodeType(UnityEngine.XR.XRNode)
extern void XRNodeState_set_nodeType_m9ADECAA5D665042FCA2F8E81726AFD1A1FA8B30D (void);
// 0x00000007 System.Void UnityEngine.XR.XRNodeState::set_tracked(System.Boolean)
extern void XRNodeState_set_tracked_m27DDD443D10F5F43B5B9AA83BFE901DC12316B9C (void);
// 0x00000008 System.String UnityEngine.XR.InputFeatureUsage::get_name()
extern void InputFeatureUsage_get_name_m08FB0101027B503D080EE38F4273BB61C59E3988 (void);
// 0x00000009 UnityEngine.XR.InputFeatureType UnityEngine.XR.InputFeatureUsage::get_internalType()
extern void InputFeatureUsage_get_internalType_m55D87EBFF5CA6781AA831A3FAAA75BD80A13B065 (void);
// 0x0000000A System.Boolean UnityEngine.XR.InputFeatureUsage::Equals(System.Object)
extern void InputFeatureUsage_Equals_mC0A1A665A98F42B2D5896BB9BC4CBA42FB59D582 (void);
// 0x0000000B System.Boolean UnityEngine.XR.InputFeatureUsage::Equals(UnityEngine.XR.InputFeatureUsage)
extern void InputFeatureUsage_Equals_mD7107D9A754188766EACB7FAAF015E184FE706B9 (void);
// 0x0000000C System.Int32 UnityEngine.XR.InputFeatureUsage::GetHashCode()
extern void InputFeatureUsage_GetHashCode_m08673D24DA7804D87915443A647AA65447C511D1 (void);
// 0x0000000D System.Void UnityEngine.XR.InputDevice::.ctor(System.UInt64)
extern void InputDevice__ctor_m610666CA01BCAF92464BE9C17BFB133A23A663BE (void);
// 0x0000000E System.UInt64 UnityEngine.XR.InputDevice::get_deviceId()
extern void InputDevice_get_deviceId_m86C962E24F26D879240A6E889E9E8D3D94F546A5 (void);
// 0x0000000F System.Boolean UnityEngine.XR.InputDevice::Equals(System.Object)
extern void InputDevice_Equals_mF29A225E81A87941551F70A2351CB803A6D94063 (void);
// 0x00000010 System.Boolean UnityEngine.XR.InputDevice::Equals(UnityEngine.XR.InputDevice)
extern void InputDevice_Equals_m9661F95B50387146BC3C6F5DC63FDF2B1303ABB7 (void);
// 0x00000011 System.Int32 UnityEngine.XR.InputDevice::GetHashCode()
extern void InputDevice_GetHashCode_mFD9C9A6015B91E254DD53E14651C8D08F715D7F8 (void);
// 0x00000012 System.UInt64 UnityEngine.XR.Hand::get_deviceId()
extern void Hand_get_deviceId_mD45907CDFDBF2CE754444358020196A8C6BEE3D1 (void);
// 0x00000013 System.UInt32 UnityEngine.XR.Hand::get_featureIndex()
extern void Hand_get_featureIndex_mB8A458180449B2C2D38D5405BA26577CF1A60897 (void);
// 0x00000014 System.Boolean UnityEngine.XR.Hand::Equals(System.Object)
extern void Hand_Equals_m727113281F30E554A3A60DEFC4ED61CC94901775 (void);
// 0x00000015 System.Boolean UnityEngine.XR.Hand::Equals(UnityEngine.XR.Hand)
extern void Hand_Equals_m099C7921633095BB40C830B6F5558F99ED0D7BD0 (void);
// 0x00000016 System.Int32 UnityEngine.XR.Hand::GetHashCode()
extern void Hand_GetHashCode_mFC8C4732F0B728122C3ABCC623699DA7E47D1CCB (void);
// 0x00000017 System.UInt64 UnityEngine.XR.Eyes::get_deviceId()
extern void Eyes_get_deviceId_m5587223FE834DE2ABDC32F9E8FE6D768D4DDF936 (void);
// 0x00000018 System.UInt32 UnityEngine.XR.Eyes::get_featureIndex()
extern void Eyes_get_featureIndex_mBABAD5CFCF0585EE4791C0E1C3E2562627491237 (void);
// 0x00000019 System.Boolean UnityEngine.XR.Eyes::Equals(System.Object)
extern void Eyes_Equals_m58897DB2EEC48809233B94BB4CEA166B8ACEBFF2 (void);
// 0x0000001A System.Boolean UnityEngine.XR.Eyes::Equals(UnityEngine.XR.Eyes)
extern void Eyes_Equals_m60083B041FECD045D717F2873BF2E21AA9A4FD01 (void);
// 0x0000001B System.Int32 UnityEngine.XR.Eyes::GetHashCode()
extern void Eyes_GetHashCode_mA9742A7C19500C6C17EDCD5EED7954A9FE91C0DD (void);
// 0x0000001C System.UInt64 UnityEngine.XR.Bone::get_deviceId()
extern void Bone_get_deviceId_mF1EBAD176E69C1074F55BFFF36372A48840B2FA2 (void);
// 0x0000001D System.UInt32 UnityEngine.XR.Bone::get_featureIndex()
extern void Bone_get_featureIndex_m291227F499665BECC9D40723157367ECB56781C3 (void);
// 0x0000001E System.Boolean UnityEngine.XR.Bone::Equals(System.Object)
extern void Bone_Equals_m2FBDCFEA8B90663E546294EBEB4763538DEEA412 (void);
// 0x0000001F System.Boolean UnityEngine.XR.Bone::Equals(UnityEngine.XR.Bone)
extern void Bone_Equals_m025222EEBF2374226C62C32E1A7ADE3538C13AFB (void);
// 0x00000020 System.Int32 UnityEngine.XR.Bone::GetHashCode()
extern void Bone_GetHashCode_m0ED7925C274DE8439749951FBE4E4EC28A797BFB (void);
// 0x00000021 System.Void UnityEngine.XR.InputDevices::InvokeConnectionEvent(System.UInt64,UnityEngine.XR.ConnectionChangeType)
extern void InputDevices_InvokeConnectionEvent_mCC2A12B38DA3EDC1EF3DFB5E49E79B8BA82CE07C (void);
// 0x00000022 System.Void UnityEngine.XR.XRDisplaySubsystem::InvokeDisplayFocusChanged(System.Boolean)
extern void XRDisplaySubsystem_InvokeDisplayFocusChanged_mF8F7E4D08B964907140FD3F8841F130159C7DBA7 (void);
// 0x00000023 System.Void UnityEngine.XR.XRDisplaySubsystem::.ctor()
extern void XRDisplaySubsystem__ctor_mCC516BAAAC7175CC9CEECA04E488F5D9BF0FB774 (void);
// 0x00000024 System.Void UnityEngine.XR.XRDisplaySubsystemDescriptor::.ctor()
extern void XRDisplaySubsystemDescriptor__ctor_mB045E1EBFB4D2B7CBE05D85D5AC622F7A971E056 (void);
// 0x00000025 System.Void UnityEngine.XR.XRInputSubsystem::InvokeTrackingOriginUpdatedEvent(System.IntPtr)
extern void XRInputSubsystem_InvokeTrackingOriginUpdatedEvent_m85C26631B03079D033D0EDE3E43AA8A4FD5B43F3 (void);
// 0x00000026 System.Void UnityEngine.XR.XRInputSubsystem::InvokeBoundaryChangedEvent(System.IntPtr)
extern void XRInputSubsystem_InvokeBoundaryChangedEvent_mF795B6F61B84803E0013D2BBF55E6734D985A5EC (void);
// 0x00000027 System.Void UnityEngine.XR.XRInputSubsystem::.ctor()
extern void XRInputSubsystem__ctor_m80AE13105C9C373B38E4814244886DCB7AA3E7E8 (void);
// 0x00000028 System.Void UnityEngine.XR.XRInputSubsystemDescriptor::.ctor()
extern void XRInputSubsystemDescriptor__ctor_m1620DD409E907F5AAA03D3DE504AC48D9D3E9576 (void);
// 0x00000029 System.String UnityEngine.XR.MeshId::ToString()
extern void MeshId_ToString_mA9CDBA01DD0C110252F6C4AA7437C507B2025705 (void);
// 0x0000002A System.Int32 UnityEngine.XR.MeshId::GetHashCode()
extern void MeshId_GetHashCode_mCD3E4355DB5EE90C389CE1D742E4CCA6471E2AA6 (void);
// 0x0000002B System.Boolean UnityEngine.XR.MeshId::Equals(System.Object)
extern void MeshId_Equals_m77D4535F7643D5C1FEA20600C92B73818DD8675E (void);
// 0x0000002C System.Boolean UnityEngine.XR.MeshId::Equals(UnityEngine.XR.MeshId)
extern void MeshId_Equals_m685A94F74A3A06E6E51C60F1D40386CAA8F01834 (void);
// 0x0000002D System.Void UnityEngine.XR.MeshId::.cctor()
extern void MeshId__cctor_mF671FCA395E19600E3F18ED7BB8D555D448099F2 (void);
// 0x0000002E System.Int32 UnityEngine.XR.HashCodeHelper::Combine(System.Int32,System.Int32)
extern void HashCodeHelper_Combine_m2B976D4EF2D99C6862A12259B63F8383652956C5 (void);
// 0x0000002F UnityEngine.XR.MeshId UnityEngine.XR.MeshGenerationResult::get_MeshId()
extern void MeshGenerationResult_get_MeshId_m55663E958B980188CFD406BB2469B24D8089BA9E (void);
// 0x00000030 UnityEngine.Mesh UnityEngine.XR.MeshGenerationResult::get_Mesh()
extern void MeshGenerationResult_get_Mesh_mDBEB5E83FC729856B17AF62B9438C9B9A79A0200 (void);
// 0x00000031 UnityEngine.MeshCollider UnityEngine.XR.MeshGenerationResult::get_MeshCollider()
extern void MeshGenerationResult_get_MeshCollider_m0285F3BFFFF0778DC8DDA97BFDAE30A19AEBF283 (void);
// 0x00000032 UnityEngine.XR.MeshGenerationStatus UnityEngine.XR.MeshGenerationResult::get_Status()
extern void MeshGenerationResult_get_Status_m5AF51D2745EC947BB722550DC95665D430CDB178 (void);
// 0x00000033 UnityEngine.XR.MeshVertexAttributes UnityEngine.XR.MeshGenerationResult::get_Attributes()
extern void MeshGenerationResult_get_Attributes_m24D66B0694F827D4DFFE210069EF0F5578F4845F (void);
// 0x00000034 System.Boolean UnityEngine.XR.MeshGenerationResult::Equals(System.Object)
extern void MeshGenerationResult_Equals_m511B6FD46B1187D90919F4C0D2E853DE4A16BD44 (void);
// 0x00000035 System.Boolean UnityEngine.XR.MeshGenerationResult::Equals(UnityEngine.XR.MeshGenerationResult)
extern void MeshGenerationResult_Equals_mA9685353D67F169013BF8CC6B34EAFE69DB341B4 (void);
// 0x00000036 System.Int32 UnityEngine.XR.MeshGenerationResult::GetHashCode()
extern void MeshGenerationResult_GetHashCode_m511BFBE4F21B162C59E462DF9DC9883A06CF9CC7 (void);
// 0x00000037 System.Void UnityEngine.XR.XRMeshSubsystem::InvokeMeshReadyDelegate(UnityEngine.XR.MeshGenerationResult,System.Action`1<UnityEngine.XR.MeshGenerationResult>)
extern void XRMeshSubsystem_InvokeMeshReadyDelegate_mDD6390D57F2CD0F7EBD64B628DB780D23424FD8F (void);
// 0x00000038 System.Void UnityEngine.XR.XRMeshSubsystem::.ctor()
extern void XRMeshSubsystem__ctor_mBA5B9B6A417BB2B477705E8BF6D1BFACF94AEF74 (void);
// 0x00000039 System.Void UnityEngine.XR.XRMeshSubsystemDescriptor::.ctor()
extern void XRMeshSubsystemDescriptor__ctor_m52169EE2174077BA5575410A1031C23915BBA6D2 (void);
static Il2CppMethodPointer s_methodPointers[57] = 
{
	EmbeddedAttribute__ctor_m4E9EEA70016F5DDF1137BD478DC0BB0D2A3EB1A0,
	IsReadOnlyAttribute__ctor_m6627A1A5600798A4080F1046794105F485DEB04B,
	InputTracking_InvokeTrackingEvent_m732DB62FD504609050D52FF6BA12C6D46E5A7669,
	InputTracking__cctor_m28CF1AB55D1D93F9A27D14175010EA72686185BA,
	XRNodeState_set_uniqueID_m09D6E80AD1B81D7485242CFA27A7173A6231CA87,
	XRNodeState_set_nodeType_m9ADECAA5D665042FCA2F8E81726AFD1A1FA8B30D,
	XRNodeState_set_tracked_m27DDD443D10F5F43B5B9AA83BFE901DC12316B9C,
	InputFeatureUsage_get_name_m08FB0101027B503D080EE38F4273BB61C59E3988,
	InputFeatureUsage_get_internalType_m55D87EBFF5CA6781AA831A3FAAA75BD80A13B065,
	InputFeatureUsage_Equals_mC0A1A665A98F42B2D5896BB9BC4CBA42FB59D582,
	InputFeatureUsage_Equals_mD7107D9A754188766EACB7FAAF015E184FE706B9,
	InputFeatureUsage_GetHashCode_m08673D24DA7804D87915443A647AA65447C511D1,
	InputDevice__ctor_m610666CA01BCAF92464BE9C17BFB133A23A663BE,
	InputDevice_get_deviceId_m86C962E24F26D879240A6E889E9E8D3D94F546A5,
	InputDevice_Equals_mF29A225E81A87941551F70A2351CB803A6D94063,
	InputDevice_Equals_m9661F95B50387146BC3C6F5DC63FDF2B1303ABB7,
	InputDevice_GetHashCode_mFD9C9A6015B91E254DD53E14651C8D08F715D7F8,
	Hand_get_deviceId_mD45907CDFDBF2CE754444358020196A8C6BEE3D1,
	Hand_get_featureIndex_mB8A458180449B2C2D38D5405BA26577CF1A60897,
	Hand_Equals_m727113281F30E554A3A60DEFC4ED61CC94901775,
	Hand_Equals_m099C7921633095BB40C830B6F5558F99ED0D7BD0,
	Hand_GetHashCode_mFC8C4732F0B728122C3ABCC623699DA7E47D1CCB,
	Eyes_get_deviceId_m5587223FE834DE2ABDC32F9E8FE6D768D4DDF936,
	Eyes_get_featureIndex_mBABAD5CFCF0585EE4791C0E1C3E2562627491237,
	Eyes_Equals_m58897DB2EEC48809233B94BB4CEA166B8ACEBFF2,
	Eyes_Equals_m60083B041FECD045D717F2873BF2E21AA9A4FD01,
	Eyes_GetHashCode_mA9742A7C19500C6C17EDCD5EED7954A9FE91C0DD,
	Bone_get_deviceId_mF1EBAD176E69C1074F55BFFF36372A48840B2FA2,
	Bone_get_featureIndex_m291227F499665BECC9D40723157367ECB56781C3,
	Bone_Equals_m2FBDCFEA8B90663E546294EBEB4763538DEEA412,
	Bone_Equals_m025222EEBF2374226C62C32E1A7ADE3538C13AFB,
	Bone_GetHashCode_m0ED7925C274DE8439749951FBE4E4EC28A797BFB,
	InputDevices_InvokeConnectionEvent_mCC2A12B38DA3EDC1EF3DFB5E49E79B8BA82CE07C,
	XRDisplaySubsystem_InvokeDisplayFocusChanged_mF8F7E4D08B964907140FD3F8841F130159C7DBA7,
	XRDisplaySubsystem__ctor_mCC516BAAAC7175CC9CEECA04E488F5D9BF0FB774,
	XRDisplaySubsystemDescriptor__ctor_mB045E1EBFB4D2B7CBE05D85D5AC622F7A971E056,
	XRInputSubsystem_InvokeTrackingOriginUpdatedEvent_m85C26631B03079D033D0EDE3E43AA8A4FD5B43F3,
	XRInputSubsystem_InvokeBoundaryChangedEvent_mF795B6F61B84803E0013D2BBF55E6734D985A5EC,
	XRInputSubsystem__ctor_m80AE13105C9C373B38E4814244886DCB7AA3E7E8,
	XRInputSubsystemDescriptor__ctor_m1620DD409E907F5AAA03D3DE504AC48D9D3E9576,
	MeshId_ToString_mA9CDBA01DD0C110252F6C4AA7437C507B2025705,
	MeshId_GetHashCode_mCD3E4355DB5EE90C389CE1D742E4CCA6471E2AA6,
	MeshId_Equals_m77D4535F7643D5C1FEA20600C92B73818DD8675E,
	MeshId_Equals_m685A94F74A3A06E6E51C60F1D40386CAA8F01834,
	MeshId__cctor_mF671FCA395E19600E3F18ED7BB8D555D448099F2,
	HashCodeHelper_Combine_m2B976D4EF2D99C6862A12259B63F8383652956C5,
	MeshGenerationResult_get_MeshId_m55663E958B980188CFD406BB2469B24D8089BA9E,
	MeshGenerationResult_get_Mesh_mDBEB5E83FC729856B17AF62B9438C9B9A79A0200,
	MeshGenerationResult_get_MeshCollider_m0285F3BFFFF0778DC8DDA97BFDAE30A19AEBF283,
	MeshGenerationResult_get_Status_m5AF51D2745EC947BB722550DC95665D430CDB178,
	MeshGenerationResult_get_Attributes_m24D66B0694F827D4DFFE210069EF0F5578F4845F,
	MeshGenerationResult_Equals_m511B6FD46B1187D90919F4C0D2E853DE4A16BD44,
	MeshGenerationResult_Equals_mA9685353D67F169013BF8CC6B34EAFE69DB341B4,
	MeshGenerationResult_GetHashCode_m511BFBE4F21B162C59E462DF9DC9883A06CF9CC7,
	XRMeshSubsystem_InvokeMeshReadyDelegate_mDD6390D57F2CD0F7EBD64B628DB780D23424FD8F,
	XRMeshSubsystem__ctor_mBA5B9B6A417BB2B477705E8BF6D1BFACF94AEF74,
	XRMeshSubsystemDescriptor__ctor_m52169EE2174077BA5575410A1031C23915BBA6D2,
};
extern void XRNodeState_set_uniqueID_m09D6E80AD1B81D7485242CFA27A7173A6231CA87_AdjustorThunk (void);
extern void XRNodeState_set_nodeType_m9ADECAA5D665042FCA2F8E81726AFD1A1FA8B30D_AdjustorThunk (void);
extern void XRNodeState_set_tracked_m27DDD443D10F5F43B5B9AA83BFE901DC12316B9C_AdjustorThunk (void);
extern void InputFeatureUsage_get_name_m08FB0101027B503D080EE38F4273BB61C59E3988_AdjustorThunk (void);
extern void InputFeatureUsage_get_internalType_m55D87EBFF5CA6781AA831A3FAAA75BD80A13B065_AdjustorThunk (void);
extern void InputFeatureUsage_Equals_mC0A1A665A98F42B2D5896BB9BC4CBA42FB59D582_AdjustorThunk (void);
extern void InputFeatureUsage_Equals_mD7107D9A754188766EACB7FAAF015E184FE706B9_AdjustorThunk (void);
extern void InputFeatureUsage_GetHashCode_m08673D24DA7804D87915443A647AA65447C511D1_AdjustorThunk (void);
extern void InputDevice__ctor_m610666CA01BCAF92464BE9C17BFB133A23A663BE_AdjustorThunk (void);
extern void InputDevice_get_deviceId_m86C962E24F26D879240A6E889E9E8D3D94F546A5_AdjustorThunk (void);
extern void InputDevice_Equals_mF29A225E81A87941551F70A2351CB803A6D94063_AdjustorThunk (void);
extern void InputDevice_Equals_m9661F95B50387146BC3C6F5DC63FDF2B1303ABB7_AdjustorThunk (void);
extern void InputDevice_GetHashCode_mFD9C9A6015B91E254DD53E14651C8D08F715D7F8_AdjustorThunk (void);
extern void Hand_get_deviceId_mD45907CDFDBF2CE754444358020196A8C6BEE3D1_AdjustorThunk (void);
extern void Hand_get_featureIndex_mB8A458180449B2C2D38D5405BA26577CF1A60897_AdjustorThunk (void);
extern void Hand_Equals_m727113281F30E554A3A60DEFC4ED61CC94901775_AdjustorThunk (void);
extern void Hand_Equals_m099C7921633095BB40C830B6F5558F99ED0D7BD0_AdjustorThunk (void);
extern void Hand_GetHashCode_mFC8C4732F0B728122C3ABCC623699DA7E47D1CCB_AdjustorThunk (void);
extern void Eyes_get_deviceId_m5587223FE834DE2ABDC32F9E8FE6D768D4DDF936_AdjustorThunk (void);
extern void Eyes_get_featureIndex_mBABAD5CFCF0585EE4791C0E1C3E2562627491237_AdjustorThunk (void);
extern void Eyes_Equals_m58897DB2EEC48809233B94BB4CEA166B8ACEBFF2_AdjustorThunk (void);
extern void Eyes_Equals_m60083B041FECD045D717F2873BF2E21AA9A4FD01_AdjustorThunk (void);
extern void Eyes_GetHashCode_mA9742A7C19500C6C17EDCD5EED7954A9FE91C0DD_AdjustorThunk (void);
extern void Bone_get_deviceId_mF1EBAD176E69C1074F55BFFF36372A48840B2FA2_AdjustorThunk (void);
extern void Bone_get_featureIndex_m291227F499665BECC9D40723157367ECB56781C3_AdjustorThunk (void);
extern void Bone_Equals_m2FBDCFEA8B90663E546294EBEB4763538DEEA412_AdjustorThunk (void);
extern void Bone_Equals_m025222EEBF2374226C62C32E1A7ADE3538C13AFB_AdjustorThunk (void);
extern void Bone_GetHashCode_m0ED7925C274DE8439749951FBE4E4EC28A797BFB_AdjustorThunk (void);
extern void MeshId_ToString_mA9CDBA01DD0C110252F6C4AA7437C507B2025705_AdjustorThunk (void);
extern void MeshId_GetHashCode_mCD3E4355DB5EE90C389CE1D742E4CCA6471E2AA6_AdjustorThunk (void);
extern void MeshId_Equals_m77D4535F7643D5C1FEA20600C92B73818DD8675E_AdjustorThunk (void);
extern void MeshId_Equals_m685A94F74A3A06E6E51C60F1D40386CAA8F01834_AdjustorThunk (void);
extern void MeshGenerationResult_get_MeshId_m55663E958B980188CFD406BB2469B24D8089BA9E_AdjustorThunk (void);
extern void MeshGenerationResult_get_Mesh_mDBEB5E83FC729856B17AF62B9438C9B9A79A0200_AdjustorThunk (void);
extern void MeshGenerationResult_get_MeshCollider_m0285F3BFFFF0778DC8DDA97BFDAE30A19AEBF283_AdjustorThunk (void);
extern void MeshGenerationResult_get_Status_m5AF51D2745EC947BB722550DC95665D430CDB178_AdjustorThunk (void);
extern void MeshGenerationResult_get_Attributes_m24D66B0694F827D4DFFE210069EF0F5578F4845F_AdjustorThunk (void);
extern void MeshGenerationResult_Equals_m511B6FD46B1187D90919F4C0D2E853DE4A16BD44_AdjustorThunk (void);
extern void MeshGenerationResult_Equals_mA9685353D67F169013BF8CC6B34EAFE69DB341B4_AdjustorThunk (void);
extern void MeshGenerationResult_GetHashCode_m511BFBE4F21B162C59E462DF9DC9883A06CF9CC7_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[40] = 
{
	{ 0x06000005, XRNodeState_set_uniqueID_m09D6E80AD1B81D7485242CFA27A7173A6231CA87_AdjustorThunk },
	{ 0x06000006, XRNodeState_set_nodeType_m9ADECAA5D665042FCA2F8E81726AFD1A1FA8B30D_AdjustorThunk },
	{ 0x06000007, XRNodeState_set_tracked_m27DDD443D10F5F43B5B9AA83BFE901DC12316B9C_AdjustorThunk },
	{ 0x06000008, InputFeatureUsage_get_name_m08FB0101027B503D080EE38F4273BB61C59E3988_AdjustorThunk },
	{ 0x06000009, InputFeatureUsage_get_internalType_m55D87EBFF5CA6781AA831A3FAAA75BD80A13B065_AdjustorThunk },
	{ 0x0600000A, InputFeatureUsage_Equals_mC0A1A665A98F42B2D5896BB9BC4CBA42FB59D582_AdjustorThunk },
	{ 0x0600000B, InputFeatureUsage_Equals_mD7107D9A754188766EACB7FAAF015E184FE706B9_AdjustorThunk },
	{ 0x0600000C, InputFeatureUsage_GetHashCode_m08673D24DA7804D87915443A647AA65447C511D1_AdjustorThunk },
	{ 0x0600000D, InputDevice__ctor_m610666CA01BCAF92464BE9C17BFB133A23A663BE_AdjustorThunk },
	{ 0x0600000E, InputDevice_get_deviceId_m86C962E24F26D879240A6E889E9E8D3D94F546A5_AdjustorThunk },
	{ 0x0600000F, InputDevice_Equals_mF29A225E81A87941551F70A2351CB803A6D94063_AdjustorThunk },
	{ 0x06000010, InputDevice_Equals_m9661F95B50387146BC3C6F5DC63FDF2B1303ABB7_AdjustorThunk },
	{ 0x06000011, InputDevice_GetHashCode_mFD9C9A6015B91E254DD53E14651C8D08F715D7F8_AdjustorThunk },
	{ 0x06000012, Hand_get_deviceId_mD45907CDFDBF2CE754444358020196A8C6BEE3D1_AdjustorThunk },
	{ 0x06000013, Hand_get_featureIndex_mB8A458180449B2C2D38D5405BA26577CF1A60897_AdjustorThunk },
	{ 0x06000014, Hand_Equals_m727113281F30E554A3A60DEFC4ED61CC94901775_AdjustorThunk },
	{ 0x06000015, Hand_Equals_m099C7921633095BB40C830B6F5558F99ED0D7BD0_AdjustorThunk },
	{ 0x06000016, Hand_GetHashCode_mFC8C4732F0B728122C3ABCC623699DA7E47D1CCB_AdjustorThunk },
	{ 0x06000017, Eyes_get_deviceId_m5587223FE834DE2ABDC32F9E8FE6D768D4DDF936_AdjustorThunk },
	{ 0x06000018, Eyes_get_featureIndex_mBABAD5CFCF0585EE4791C0E1C3E2562627491237_AdjustorThunk },
	{ 0x06000019, Eyes_Equals_m58897DB2EEC48809233B94BB4CEA166B8ACEBFF2_AdjustorThunk },
	{ 0x0600001A, Eyes_Equals_m60083B041FECD045D717F2873BF2E21AA9A4FD01_AdjustorThunk },
	{ 0x0600001B, Eyes_GetHashCode_mA9742A7C19500C6C17EDCD5EED7954A9FE91C0DD_AdjustorThunk },
	{ 0x0600001C, Bone_get_deviceId_mF1EBAD176E69C1074F55BFFF36372A48840B2FA2_AdjustorThunk },
	{ 0x0600001D, Bone_get_featureIndex_m291227F499665BECC9D40723157367ECB56781C3_AdjustorThunk },
	{ 0x0600001E, Bone_Equals_m2FBDCFEA8B90663E546294EBEB4763538DEEA412_AdjustorThunk },
	{ 0x0600001F, Bone_Equals_m025222EEBF2374226C62C32E1A7ADE3538C13AFB_AdjustorThunk },
	{ 0x06000020, Bone_GetHashCode_m0ED7925C274DE8439749951FBE4E4EC28A797BFB_AdjustorThunk },
	{ 0x06000029, MeshId_ToString_mA9CDBA01DD0C110252F6C4AA7437C507B2025705_AdjustorThunk },
	{ 0x0600002A, MeshId_GetHashCode_mCD3E4355DB5EE90C389CE1D742E4CCA6471E2AA6_AdjustorThunk },
	{ 0x0600002B, MeshId_Equals_m77D4535F7643D5C1FEA20600C92B73818DD8675E_AdjustorThunk },
	{ 0x0600002C, MeshId_Equals_m685A94F74A3A06E6E51C60F1D40386CAA8F01834_AdjustorThunk },
	{ 0x0600002F, MeshGenerationResult_get_MeshId_m55663E958B980188CFD406BB2469B24D8089BA9E_AdjustorThunk },
	{ 0x06000030, MeshGenerationResult_get_Mesh_mDBEB5E83FC729856B17AF62B9438C9B9A79A0200_AdjustorThunk },
	{ 0x06000031, MeshGenerationResult_get_MeshCollider_m0285F3BFFFF0778DC8DDA97BFDAE30A19AEBF283_AdjustorThunk },
	{ 0x06000032, MeshGenerationResult_get_Status_m5AF51D2745EC947BB722550DC95665D430CDB178_AdjustorThunk },
	{ 0x06000033, MeshGenerationResult_get_Attributes_m24D66B0694F827D4DFFE210069EF0F5578F4845F_AdjustorThunk },
	{ 0x06000034, MeshGenerationResult_Equals_m511B6FD46B1187D90919F4C0D2E853DE4A16BD44_AdjustorThunk },
	{ 0x06000035, MeshGenerationResult_Equals_mA9685353D67F169013BF8CC6B34EAFE69DB341B4_AdjustorThunk },
	{ 0x06000036, MeshGenerationResult_GetHashCode_m511BFBE4F21B162C59E462DF9DC9883A06CF9CC7_AdjustorThunk },
};
static const int32_t s_InvokerIndices[57] = 
{
	3967,
	3967,
	4947,
	6289,
	3393,
	3313,
	3276,
	3916,
	3961,
	2591,
	2568,
	3893,
	3393,
	3962,
	2591,
	2567,
	3893,
	3962,
	3961,
	2591,
	2564,
	3893,
	3962,
	3961,
	2591,
	2558,
	3893,
	3962,
	3961,
	2591,
	2532,
	3893,
	5854,
	3276,
	3967,
	3967,
	6215,
	6215,
	3967,
	3967,
	3916,
	3893,
	2591,
	2589,
	6289,
	5514,
	3913,
	3916,
	3916,
	3893,
	3893,
	2591,
	2588,
	3893,
	2148,
	3967,
	3967,
};
extern const CustomAttributesCacheGenerator g_UnityEngine_XRModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_XRModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_XRModule_CodeGenModule = 
{
	"UnityEngine.XRModule.dll",
	57,
	s_methodPointers,
	40,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_UnityEngine_XRModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

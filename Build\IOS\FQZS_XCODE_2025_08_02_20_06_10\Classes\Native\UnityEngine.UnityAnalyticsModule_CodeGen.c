﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void UnityEngine.RemoteSettings::RemoteSettingsUpdated(System.Boolean)
extern void RemoteSettings_RemoteSettingsUpdated_mEC0C7B0259ACB2DE3E1D741448B4FF092E3CEB59 (void);
// 0x00000002 System.Void UnityEngine.RemoteSettings::RemoteSettingsBeforeFetchFromServer()
extern void RemoteSettings_RemoteSettingsBeforeFetchFromServer_m517483A70A73E58EBAC9D588E3D0B26C5D93073E (void);
// 0x00000003 System.Void UnityEngine.RemoteSettings::RemoteSettingsUpdateCompleted(System.<PERSON>,System.<PERSON>,System.Int32)
extern void RemoteSettings_RemoteSettingsUpdateCompleted_mF6360AF934947BD8201E08632402AF398A00E350 (void);
// 0x00000004 System.Void UnityEngine.RemoteSettings/UpdatedEventHandler::.ctor(System.Object,System.IntPtr)
extern void UpdatedEventHandler__ctor_m47CAF85102B04A37A20F7043F57404A76DE62153 (void);
// 0x00000005 System.Void UnityEngine.RemoteSettings/UpdatedEventHandler::Invoke()
extern void UpdatedEventHandler_Invoke_mF27D3BF226F882F90575FE822F5F154B1E86FE1A (void);
// 0x00000006 System.IAsyncResult UnityEngine.RemoteSettings/UpdatedEventHandler::BeginInvoke(System.AsyncCallback,System.Object)
extern void UpdatedEventHandler_BeginInvoke_m937F7EF5F566073B30BB5D8AD630A723F4580746 (void);
// 0x00000007 System.Void UnityEngine.RemoteSettings/UpdatedEventHandler::EndInvoke(System.IAsyncResult)
extern void UpdatedEventHandler_EndInvoke_mD99843879F04C60C61D6A9BBAC751F6DD4C74982 (void);
// 0x00000008 System.Void UnityEngine.RemoteConfigSettings::RemoteConfigSettingsUpdated(UnityEngine.RemoteConfigSettings,System.Boolean)
extern void RemoteConfigSettings_RemoteConfigSettingsUpdated_mDD9D1AAF8743AE48E5FD54B0AD4094D19F1F3704 (void);
// 0x00000009 System.Void UnityEngine.Analytics.AnalyticsSessionInfo::CallSessionStateChanged(UnityEngine.Analytics.AnalyticsSessionState,System.Int64,System.Int64,System.Boolean)
extern void AnalyticsSessionInfo_CallSessionStateChanged_mEA6F4144B8394D6F7D0DE3F321341C81005F1468 (void);
// 0x0000000A System.Void UnityEngine.Analytics.AnalyticsSessionInfo::CallIdentityTokenChanged(System.String)
extern void AnalyticsSessionInfo_CallIdentityTokenChanged_mA1D7580A3F01AFA125CC60431F6A1495184CE110 (void);
// 0x0000000B System.Void UnityEngine.Analytics.AnalyticsSessionInfo/SessionStateChanged::.ctor(System.Object,System.IntPtr)
extern void SessionStateChanged__ctor_m20755FFAAF071B0C0C5CB8001E00235A68631E87 (void);
// 0x0000000C System.Void UnityEngine.Analytics.AnalyticsSessionInfo/SessionStateChanged::Invoke(UnityEngine.Analytics.AnalyticsSessionState,System.Int64,System.Int64,System.Boolean)
extern void SessionStateChanged_Invoke_mC75D45C1580508D5A628BA0D959E7FC937972343 (void);
// 0x0000000D System.IAsyncResult UnityEngine.Analytics.AnalyticsSessionInfo/SessionStateChanged::BeginInvoke(UnityEngine.Analytics.AnalyticsSessionState,System.Int64,System.Int64,System.Boolean,System.AsyncCallback,System.Object)
extern void SessionStateChanged_BeginInvoke_m1FC5FEA2CB085F8EB742E87CAED9E6EDA0A4CB06 (void);
// 0x0000000E System.Void UnityEngine.Analytics.AnalyticsSessionInfo/SessionStateChanged::EndInvoke(System.IAsyncResult)
extern void SessionStateChanged_EndInvoke_mD526629995FB93D3E61F98B43D56805F22CCBCE9 (void);
// 0x0000000F System.Void UnityEngine.Analytics.AnalyticsSessionInfo/IdentityTokenChanged::.ctor(System.Object,System.IntPtr)
extern void IdentityTokenChanged__ctor_m11460EC0DCF16CC424C6CECFDFE8862E0EB6133A (void);
// 0x00000010 System.Void UnityEngine.Analytics.AnalyticsSessionInfo/IdentityTokenChanged::Invoke(System.String)
extern void IdentityTokenChanged_Invoke_m73580C534C2D30CFA7F455946EF94F4978DC991B (void);
// 0x00000011 System.IAsyncResult UnityEngine.Analytics.AnalyticsSessionInfo/IdentityTokenChanged::BeginInvoke(System.String,System.AsyncCallback,System.Object)
extern void IdentityTokenChanged_BeginInvoke_m996658BCDA945DC240BB8DCCEB74237B1B3ECBDB (void);
// 0x00000012 System.Void UnityEngine.Analytics.AnalyticsSessionInfo/IdentityTokenChanged::EndInvoke(System.IAsyncResult)
extern void IdentityTokenChanged_EndInvoke_m7103AA2B42EE8FD1C80047657E4EABB635243872 (void);
static Il2CppMethodPointer s_methodPointers[18] = 
{
	RemoteSettings_RemoteSettingsUpdated_mEC0C7B0259ACB2DE3E1D741448B4FF092E3CEB59,
	RemoteSettings_RemoteSettingsBeforeFetchFromServer_m517483A70A73E58EBAC9D588E3D0B26C5D93073E,
	RemoteSettings_RemoteSettingsUpdateCompleted_mF6360AF934947BD8201E08632402AF398A00E350,
	UpdatedEventHandler__ctor_m47CAF85102B04A37A20F7043F57404A76DE62153,
	UpdatedEventHandler_Invoke_mF27D3BF226F882F90575FE822F5F154B1E86FE1A,
	UpdatedEventHandler_BeginInvoke_m937F7EF5F566073B30BB5D8AD630A723F4580746,
	UpdatedEventHandler_EndInvoke_mD99843879F04C60C61D6A9BBAC751F6DD4C74982,
	RemoteConfigSettings_RemoteConfigSettingsUpdated_mDD9D1AAF8743AE48E5FD54B0AD4094D19F1F3704,
	AnalyticsSessionInfo_CallSessionStateChanged_mEA6F4144B8394D6F7D0DE3F321341C81005F1468,
	AnalyticsSessionInfo_CallIdentityTokenChanged_mA1D7580A3F01AFA125CC60431F6A1495184CE110,
	SessionStateChanged__ctor_m20755FFAAF071B0C0C5CB8001E00235A68631E87,
	SessionStateChanged_Invoke_mC75D45C1580508D5A628BA0D959E7FC937972343,
	SessionStateChanged_BeginInvoke_m1FC5FEA2CB085F8EB742E87CAED9E6EDA0A4CB06,
	SessionStateChanged_EndInvoke_mD526629995FB93D3E61F98B43D56805F22CCBCE9,
	IdentityTokenChanged__ctor_m11460EC0DCF16CC424C6CECFDFE8862E0EB6133A,
	IdentityTokenChanged_Invoke_m73580C534C2D30CFA7F455946EF94F4978DC991B,
	IdentityTokenChanged_BeginInvoke_m996658BCDA945DC240BB8DCCEB74237B1B3ECBDB,
	IdentityTokenChanged_EndInvoke_m7103AA2B42EE8FD1C80047657E4EABB635243872,
};
static const int32_t s_InvokerIndices[18] = 
{
	6208,
	6289,
	5279,
	2169,
	3967,
	1717,
	3348,
	5825,
	4951,
	6218,
	2169,
	843,
	189,
	3348,
	2169,
	3348,
	1129,
	3348,
};
extern const CustomAttributesCacheGenerator g_UnityEngine_UnityAnalyticsModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_UnityAnalyticsModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_UnityAnalyticsModule_CodeGenModule = 
{
	"UnityEngine.UnityAnalyticsModule.dll",
	18,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_UnityEngine_UnityAnalyticsModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

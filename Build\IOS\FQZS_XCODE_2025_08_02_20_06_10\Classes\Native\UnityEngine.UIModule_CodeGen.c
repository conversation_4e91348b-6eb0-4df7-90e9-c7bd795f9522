﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Boolean UnityEngine.ICanvasRaycastFilter::IsRaycastLocationValid(UnityEngine.Vector2,UnityEngine.Camera)
// 0x00000002 System.Single UnityEngine.CanvasGroup::get_alpha()
extern void CanvasGroup_get_alpha_m38E292E68116A55A99F222F4E1F31CBE189690D9 (void);
// 0x00000003 System.Void UnityEngine.CanvasGroup::set_alpha(System.Single)
extern void CanvasGroup_set_alpha_m522B58BDF64D87252B0D43D254FF3A4D5993DC53 (void);
// 0x00000004 System.Boolean UnityEngine.CanvasGroup::get_interactable()
extern void CanvasGroup_get_interactable_m643399D4E2F8F483C031ADAEEBDCC4A6A4708DA1 (void);
// 0x00000005 System.Boolean UnityEngine.CanvasGroup::get_blocksRaycasts()
extern void CanvasGroup_get_blocksRaycasts_m54BEB93C34E4B4ADB35A33679B0D475A0094F3B4 (void);
// 0x00000006 System.Void UnityEngine.CanvasGroup::set_blocksRaycasts(System.Boolean)
extern void CanvasGroup_set_blocksRaycasts_m322FC5A1B70A23524463A84CC707BF50FD284B3A (void);
// 0x00000007 System.Boolean UnityEngine.CanvasGroup::get_ignoreParentGroups()
extern void CanvasGroup_get_ignoreParentGroups_m191E84D6A34F34A69767B396DFB6E69C8BE33A81 (void);
// 0x00000008 System.Boolean UnityEngine.CanvasGroup::IsRaycastLocationValid(UnityEngine.Vector2,UnityEngine.Camera)
extern void CanvasGroup_IsRaycastLocationValid_mBEEEFF19F0813F16B005D4A3021B48671F9EB124 (void);
// 0x00000009 System.Void UnityEngine.CanvasRenderer::set_hasPopInstruction(System.Boolean)
extern void CanvasRenderer_set_hasPopInstruction_mA80C720999AE5965E56F873FE29B54471B8C6329 (void);
// 0x0000000A System.Int32 UnityEngine.CanvasRenderer::get_materialCount()
extern void CanvasRenderer_get_materialCount_mF29C9816FEC6D8189DDC7AB16B40EC56FAB59488 (void);
// 0x0000000B System.Void UnityEngine.CanvasRenderer::set_materialCount(System.Int32)
extern void CanvasRenderer_set_materialCount_m00FE6113ACA7FE9AD51DA0A9A0B013D7C811E5DB (void);
// 0x0000000C System.Void UnityEngine.CanvasRenderer::set_popMaterialCount(System.Int32)
extern void CanvasRenderer_set_popMaterialCount_mA35E4F9676B3FDA7E9E88EC1EBC711F2936CD973 (void);
// 0x0000000D System.Int32 UnityEngine.CanvasRenderer::get_absoluteDepth()
extern void CanvasRenderer_get_absoluteDepth_m66093A08853DE029E61146E5A271AD775B2B4330 (void);
// 0x0000000E System.Boolean UnityEngine.CanvasRenderer::get_hasMoved()
extern void CanvasRenderer_get_hasMoved_m7CF5BF1CD654A0CB0DAC1C4E4EF8C83AD9DED82A (void);
// 0x0000000F System.Boolean UnityEngine.CanvasRenderer::get_cull()
extern void CanvasRenderer_get_cull_m214173F051F22A3377BD4C91F91422CB9EEC7581 (void);
// 0x00000010 System.Void UnityEngine.CanvasRenderer::set_cull(System.Boolean)
extern void CanvasRenderer_set_cull_m8DCF5D7E70A6904457B66669661B1D5ABE1FCF94 (void);
// 0x00000011 System.Void UnityEngine.CanvasRenderer::SetColor(UnityEngine.Color)
extern void CanvasRenderer_SetColor_mF9B22FEEB69F8577BB2952217F75E877885841F7 (void);
// 0x00000012 UnityEngine.Color UnityEngine.CanvasRenderer::GetColor()
extern void CanvasRenderer_GetColor_mEE82D01DA3B43136DAEBEC212A38AABC16D20931 (void);
// 0x00000013 System.Void UnityEngine.CanvasRenderer::EnableRectClipping(UnityEngine.Rect)
extern void CanvasRenderer_EnableRectClipping_mF39FD2754A2E6E4370597F1FBC7889A6170E31F1 (void);
// 0x00000014 System.Void UnityEngine.CanvasRenderer::set_clippingSoftness(UnityEngine.Vector2)
extern void CanvasRenderer_set_clippingSoftness_m0B99BD07E0C28E3B12D2F0944B2C0D0F63EAA5AE (void);
// 0x00000015 System.Void UnityEngine.CanvasRenderer::DisableRectClipping()
extern void CanvasRenderer_DisableRectClipping_m736771B6C7040BE07B47181CEDA705325849E95A (void);
// 0x00000016 System.Void UnityEngine.CanvasRenderer::SetMaterial(UnityEngine.Material,System.Int32)
extern void CanvasRenderer_SetMaterial_m1D7A8BD75D5DEFC5F0A27FFBA2A2A84755EE421F (void);
// 0x00000017 UnityEngine.Material UnityEngine.CanvasRenderer::GetMaterial(System.Int32)
extern void CanvasRenderer_GetMaterial_mA674C9DC47C63CCFCDED7382D923A7796C8BE308 (void);
// 0x00000018 System.Void UnityEngine.CanvasRenderer::SetPopMaterial(UnityEngine.Material,System.Int32)
extern void CanvasRenderer_SetPopMaterial_m2E9BBCA625FAD591DF3480287B318BFCC38A1E49 (void);
// 0x00000019 System.Void UnityEngine.CanvasRenderer::SetTexture(UnityEngine.Texture)
extern void CanvasRenderer_SetTexture_m390FCD8FBC6E992F4AAC9967EBFA3F32A2BD93C1 (void);
// 0x0000001A System.Void UnityEngine.CanvasRenderer::SetAlphaTexture(UnityEngine.Texture)
extern void CanvasRenderer_SetAlphaTexture_m0A867B30B6475B1D72CE28117AABED84D4A1C006 (void);
// 0x0000001B System.Void UnityEngine.CanvasRenderer::SetMesh(UnityEngine.Mesh)
extern void CanvasRenderer_SetMesh_mB506682F318E5D8D8FE3888BF50E40DC34B726DF (void);
// 0x0000001C System.Void UnityEngine.CanvasRenderer::Clear()
extern void CanvasRenderer_Clear_m8793B46F28365E540BB2B2ADBA439D255E350CC4 (void);
// 0x0000001D System.Void UnityEngine.CanvasRenderer::SetMaterial(UnityEngine.Material,UnityEngine.Texture)
extern void CanvasRenderer_SetMaterial_mDCF29309742914F21D88D129C1E8C25F2E8A14FA (void);
// 0x0000001E System.Void UnityEngine.CanvasRenderer::SplitUIVertexStreams(System.Collections.Generic.List`1<UnityEngine.UIVertex>,System.Collections.Generic.List`1<UnityEngine.Vector3>,System.Collections.Generic.List`1<UnityEngine.Color32>,System.Collections.Generic.List`1<UnityEngine.Vector4>,System.Collections.Generic.List`1<UnityEngine.Vector4>,System.Collections.Generic.List`1<UnityEngine.Vector4>,System.Collections.Generic.List`1<UnityEngine.Vector4>,System.Collections.Generic.List`1<UnityEngine.Vector3>,System.Collections.Generic.List`1<UnityEngine.Vector4>,System.Collections.Generic.List`1<System.Int32>)
extern void CanvasRenderer_SplitUIVertexStreams_m718EF0055D9309767575CBC8032EACCFB19F804D (void);
// 0x0000001F System.Void UnityEngine.CanvasRenderer::CreateUIVertexStream(System.Collections.Generic.List`1<UnityEngine.UIVertex>,System.Collections.Generic.List`1<UnityEngine.Vector3>,System.Collections.Generic.List`1<UnityEngine.Color32>,System.Collections.Generic.List`1<UnityEngine.Vector4>,System.Collections.Generic.List`1<UnityEngine.Vector4>,System.Collections.Generic.List`1<UnityEngine.Vector4>,System.Collections.Generic.List`1<UnityEngine.Vector4>,System.Collections.Generic.List`1<UnityEngine.Vector3>,System.Collections.Generic.List`1<UnityEngine.Vector4>,System.Collections.Generic.List`1<System.Int32>)
extern void CanvasRenderer_CreateUIVertexStream_m27CA19BF8CE74B3EBE01B16601DC002EB092338D (void);
// 0x00000020 System.Void UnityEngine.CanvasRenderer::AddUIVertexStream(System.Collections.Generic.List`1<UnityEngine.UIVertex>,System.Collections.Generic.List`1<UnityEngine.Vector3>,System.Collections.Generic.List`1<UnityEngine.Color32>,System.Collections.Generic.List`1<UnityEngine.Vector4>,System.Collections.Generic.List`1<UnityEngine.Vector4>,System.Collections.Generic.List`1<UnityEngine.Vector4>,System.Collections.Generic.List`1<UnityEngine.Vector4>,System.Collections.Generic.List`1<UnityEngine.Vector3>,System.Collections.Generic.List`1<UnityEngine.Vector4>)
extern void CanvasRenderer_AddUIVertexStream_m099A0E10770C7C603EDDBC97A05D7C1285EB310D (void);
// 0x00000021 System.Void UnityEngine.CanvasRenderer::SplitIndicesStreamsInternal(System.Object,System.Object)
extern void CanvasRenderer_SplitIndicesStreamsInternal_mCF7F959B344233C7D557437CF642BC780FCCE582 (void);
// 0x00000022 System.Void UnityEngine.CanvasRenderer::SplitUIVertexStreamsInternal(System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object)
extern void CanvasRenderer_SplitUIVertexStreamsInternal_m4F66A6BF6D08CE684FA56F6B12C1ECC07FDCB8D0 (void);
// 0x00000023 System.Void UnityEngine.CanvasRenderer::CreateUIVertexStreamInternal(System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object)
extern void CanvasRenderer_CreateUIVertexStreamInternal_mCD68FA7913DCF23EC17E5D686C002C6883FC429C (void);
// 0x00000024 System.Void UnityEngine.CanvasRenderer::SetColor_Injected(UnityEngine.Color&)
extern void CanvasRenderer_SetColor_Injected_m18315753FF6B5E9711C522331517ED21D5FD5F58 (void);
// 0x00000025 System.Void UnityEngine.CanvasRenderer::GetColor_Injected(UnityEngine.Color&)
extern void CanvasRenderer_GetColor_Injected_mA46BD781F57174120BA58E9E569AC6AB1C3131E3 (void);
// 0x00000026 System.Void UnityEngine.CanvasRenderer::EnableRectClipping_Injected(UnityEngine.Rect&)
extern void CanvasRenderer_EnableRectClipping_Injected_m61118840C9AD8B28949DE0ABF3F5BDBBC5D1AAE0 (void);
// 0x00000027 System.Void UnityEngine.CanvasRenderer::set_clippingSoftness_Injected(UnityEngine.Vector2&)
extern void CanvasRenderer_set_clippingSoftness_Injected_mCA346C58AF7E78D8C2AC2B39710A019E77A909BA (void);
// 0x00000028 UnityEngine.Vector2 UnityEngine.RectTransformUtility::PixelAdjustPoint(UnityEngine.Vector2,UnityEngine.Transform,UnityEngine.Canvas)
extern void RectTransformUtility_PixelAdjustPoint_mA07D1645718685E85B077E7D6A9FB4D65077E83B (void);
// 0x00000029 UnityEngine.Rect UnityEngine.RectTransformUtility::PixelAdjustRect(UnityEngine.RectTransform,UnityEngine.Canvas)
extern void RectTransformUtility_PixelAdjustRect_m7EA78AE30783AACF0D123287C451BD9A73258927 (void);
// 0x0000002A System.Boolean UnityEngine.RectTransformUtility::PointInRectangle(UnityEngine.Vector2,UnityEngine.RectTransform,UnityEngine.Camera,UnityEngine.Vector4)
extern void RectTransformUtility_PointInRectangle_mFCECDBA154165B8750F709EBFC2A3930EEB5032C (void);
// 0x0000002B System.Boolean UnityEngine.RectTransformUtility::RectangleContainsScreenPoint(UnityEngine.RectTransform,UnityEngine.Vector2,UnityEngine.Camera)
extern void RectTransformUtility_RectangleContainsScreenPoint_m7E7E3B78B91D45C9F498F78841EADA3445A02BF4 (void);
// 0x0000002C System.Boolean UnityEngine.RectTransformUtility::RectangleContainsScreenPoint(UnityEngine.RectTransform,UnityEngine.Vector2,UnityEngine.Camera,UnityEngine.Vector4)
extern void RectTransformUtility_RectangleContainsScreenPoint_mE4F81A74C8157E3FA749521B21EDC9C483641B16 (void);
// 0x0000002D System.Boolean UnityEngine.RectTransformUtility::ScreenPointToWorldPointInRectangle(UnityEngine.RectTransform,UnityEngine.Vector2,UnityEngine.Camera,UnityEngine.Vector3&)
extern void RectTransformUtility_ScreenPointToWorldPointInRectangle_mD8A64D9BB1B560375B92377504425644C19E951D (void);
// 0x0000002E System.Boolean UnityEngine.RectTransformUtility::ScreenPointToLocalPointInRectangle(UnityEngine.RectTransform,UnityEngine.Vector2,UnityEngine.Camera,UnityEngine.Vector2&)
extern void RectTransformUtility_ScreenPointToLocalPointInRectangle_m3C56E006591714917B01C30AA186C5316334E2B8 (void);
// 0x0000002F UnityEngine.Ray UnityEngine.RectTransformUtility::ScreenPointToRay(UnityEngine.Camera,UnityEngine.Vector2)
extern void RectTransformUtility_ScreenPointToRay_mFB1D8F8416DADCB12F1055CA21BAF266653F0FE5 (void);
// 0x00000030 UnityEngine.Vector2 UnityEngine.RectTransformUtility::WorldToScreenPoint(UnityEngine.Camera,UnityEngine.Vector3)
extern void RectTransformUtility_WorldToScreenPoint_mA29C11F4E906970A355DCF24F7B6BAF63975386F (void);
// 0x00000031 System.Void UnityEngine.RectTransformUtility::FlipLayoutOnAxis(UnityEngine.RectTransform,System.Int32,System.Boolean,System.Boolean)
extern void RectTransformUtility_FlipLayoutOnAxis_m1C6AF90E1B52D12F908DA2324EF38D4529120EDD (void);
// 0x00000032 System.Void UnityEngine.RectTransformUtility::FlipLayoutAxes(UnityEngine.RectTransform,System.Boolean,System.Boolean)
extern void RectTransformUtility_FlipLayoutAxes_m6D5A7D175F5A972977D9AC71AF96CFD4CF87AAC8 (void);
// 0x00000033 UnityEngine.Vector2 UnityEngine.RectTransformUtility::GetTransposed(UnityEngine.Vector2)
extern void RectTransformUtility_GetTransposed_m01A19AFD4177D8BE65750F6880A4FD7B32CC1AEF (void);
// 0x00000034 System.Void UnityEngine.RectTransformUtility::.cctor()
extern void RectTransformUtility__cctor_m050932F98B7461DDEFBB132B8628DD7E6C952A0F (void);
// 0x00000035 System.Void UnityEngine.RectTransformUtility::PixelAdjustPoint_Injected(UnityEngine.Vector2&,UnityEngine.Transform,UnityEngine.Canvas,UnityEngine.Vector2&)
extern void RectTransformUtility_PixelAdjustPoint_Injected_m3A85C077BFBA09901B1EAB1B8458939D9A6DE014 (void);
// 0x00000036 System.Void UnityEngine.RectTransformUtility::PixelAdjustRect_Injected(UnityEngine.RectTransform,UnityEngine.Canvas,UnityEngine.Rect&)
extern void RectTransformUtility_PixelAdjustRect_Injected_m6505261AC2D142E00F5987B6E343482DAAB6E946 (void);
// 0x00000037 System.Boolean UnityEngine.RectTransformUtility::PointInRectangle_Injected(UnityEngine.Vector2&,UnityEngine.RectTransform,UnityEngine.Camera,UnityEngine.Vector4&)
extern void RectTransformUtility_PointInRectangle_Injected_m07A43DDAC748DC8EA043C9AA5177DC90017AA204 (void);
// 0x00000038 System.Void UnityEngine.Canvas::add_preWillRenderCanvases(UnityEngine.Canvas/WillRenderCanvases)
extern void Canvas_add_preWillRenderCanvases_m4FE6F477229ED7D8670B28630A23DCEF55FFBFB8 (void);
// 0x00000039 System.Void UnityEngine.Canvas::remove_preWillRenderCanvases(UnityEngine.Canvas/WillRenderCanvases)
extern void Canvas_remove_preWillRenderCanvases_m073AD491608B47CC7375C5179B3220C32B03EA59 (void);
// 0x0000003A System.Void UnityEngine.Canvas::add_willRenderCanvases(UnityEngine.Canvas/WillRenderCanvases)
extern void Canvas_add_willRenderCanvases_m0C0EB3B7B6EFEEDDECFA4DE742E62E7FBC9C8783 (void);
// 0x0000003B System.Void UnityEngine.Canvas::remove_willRenderCanvases(UnityEngine.Canvas/WillRenderCanvases)
extern void Canvas_remove_willRenderCanvases_mD5A19782872134C6BA9C448B4B02193D5B5641B5 (void);
// 0x0000003C UnityEngine.RenderMode UnityEngine.Canvas::get_renderMode()
extern void Canvas_get_renderMode_mAEC8A341577CC74EC89D5890E6D6E4A82B03574D (void);
// 0x0000003D System.Void UnityEngine.Canvas::set_renderMode(UnityEngine.RenderMode)
extern void Canvas_set_renderMode_mE19EA5E3E4758704CCA6A6A2E4EB84DB75096088 (void);
// 0x0000003E System.Boolean UnityEngine.Canvas::get_isRootCanvas()
extern void Canvas_get_isRootCanvas_mCAA9356718F8FEFB235F6F55347A26764B642FBF (void);
// 0x0000003F System.Single UnityEngine.Canvas::get_scaleFactor()
extern void Canvas_get_scaleFactor_m3F0D7E3B97B0493F4E98B2BBCA7A57BC1E1CB710 (void);
// 0x00000040 System.Void UnityEngine.Canvas::set_scaleFactor(System.Single)
extern void Canvas_set_scaleFactor_m707F18DB5EB8E34BC55B584426E4037CA0E78667 (void);
// 0x00000041 System.Single UnityEngine.Canvas::get_referencePixelsPerUnit()
extern void Canvas_get_referencePixelsPerUnit_m001FE1F0C8D84A3539DBB772416D05E93233395E (void);
// 0x00000042 System.Void UnityEngine.Canvas::set_referencePixelsPerUnit(System.Single)
extern void Canvas_set_referencePixelsPerUnit_mF07A80422A4C2F7A0C8CD0CD5C39A9F335571F0E (void);
// 0x00000043 System.Boolean UnityEngine.Canvas::get_pixelPerfect()
extern void Canvas_get_pixelPerfect_mCAC921FAC61E9B0E42656EB8EC511AF21EC99A77 (void);
// 0x00000044 System.Void UnityEngine.Canvas::set_pixelPerfect(System.Boolean)
extern void Canvas_set_pixelPerfect_mD477DD1867E742FF7EB7689C6DBAE243E533ED69 (void);
// 0x00000045 System.Void UnityEngine.Canvas::set_planeDistance(System.Single)
extern void Canvas_set_planeDistance_m8CC362AC0D0259678E9088C0C441429629E8C9D9 (void);
// 0x00000046 System.Int32 UnityEngine.Canvas::get_renderOrder()
extern void Canvas_get_renderOrder_m6EA8415A6C65D304F973FF07C10E6FA41345B06B (void);
// 0x00000047 System.Boolean UnityEngine.Canvas::get_overrideSorting()
extern void Canvas_get_overrideSorting_m2A3D1772DD606F717F8F4B861411A19D1444DE90 (void);
// 0x00000048 System.Void UnityEngine.Canvas::set_overrideSorting(System.Boolean)
extern void Canvas_set_overrideSorting_m47944AC440D44131ED379071C4F81C166C941045 (void);
// 0x00000049 System.Int32 UnityEngine.Canvas::get_sortingOrder()
extern void Canvas_get_sortingOrder_m2024155C98059AE97E268327C71E33F1444F8FC4 (void);
// 0x0000004A System.Void UnityEngine.Canvas::set_sortingOrder(System.Int32)
extern void Canvas_set_sortingOrder_m217F79CAFA4654335E6FF70D1BAF4420EE46482A (void);
// 0x0000004B System.Int32 UnityEngine.Canvas::get_targetDisplay()
extern void Canvas_get_targetDisplay_m4EE59F02963F99EDCAA3FCD00F4BE5168951C10F (void);
// 0x0000004C System.Int32 UnityEngine.Canvas::get_sortingLayerID()
extern void Canvas_get_sortingLayerID_mA0AB0BB70E4E6072EEC3340FE552BE4A78C48064 (void);
// 0x0000004D System.Void UnityEngine.Canvas::set_sortingLayerID(System.Int32)
extern void Canvas_set_sortingLayerID_m3BE132868D12C0DEA8EF5A45842948DBFACF6C6D (void);
// 0x0000004E UnityEngine.AdditionalCanvasShaderChannels UnityEngine.Canvas::get_additionalShaderChannels()
extern void Canvas_get_additionalShaderChannels_mCAA1006118961EDC76A8A79B084D14768A42F206 (void);
// 0x0000004F System.Void UnityEngine.Canvas::set_additionalShaderChannels(UnityEngine.AdditionalCanvasShaderChannels)
extern void Canvas_set_additionalShaderChannels_m857104DC8EA363A2E470CD3B873C60483135C4BD (void);
// 0x00000050 UnityEngine.Canvas UnityEngine.Canvas::get_rootCanvas()
extern void Canvas_get_rootCanvas_mB1C93410A4AA793D88130FD08C05D71327641DC5 (void);
// 0x00000051 UnityEngine.Vector2 UnityEngine.Canvas::get_renderingDisplaySize()
extern void Canvas_get_renderingDisplaySize_mA5BF3D543E45DA1ED74A45BAFD2CB8F29844AD9B (void);
// 0x00000052 UnityEngine.Camera UnityEngine.Canvas::get_worldCamera()
extern void Canvas_get_worldCamera_mFE4C9FDA7996FE20AC5CA3CB45B4190C40122D51 (void);
// 0x00000053 UnityEngine.Material UnityEngine.Canvas::GetDefaultCanvasMaterial()
extern void Canvas_GetDefaultCanvasMaterial_mBE95E64939424D4E6B52FF7D19685BF0CD1319DF (void);
// 0x00000054 UnityEngine.Material UnityEngine.Canvas::GetETC1SupportedCanvasMaterial()
extern void Canvas_GetETC1SupportedCanvasMaterial_m8AEA54BD6AA14DAB2C980B427472C6F473FAEFB0 (void);
// 0x00000055 System.Void UnityEngine.Canvas::ForceUpdateCanvases()
extern void Canvas_ForceUpdateCanvases_m5A81BBD794A3E844F63DFDDB070C44108E2BA006 (void);
// 0x00000056 System.Void UnityEngine.Canvas::SendPreWillRenderCanvases()
extern void Canvas_SendPreWillRenderCanvases_m59BB887BB53DE4345E3232D31C20C3303B717C58 (void);
// 0x00000057 System.Void UnityEngine.Canvas::SendWillRenderCanvases()
extern void Canvas_SendWillRenderCanvases_mB01E98A38733B889ACF6118D138F8072F88E5D86 (void);
// 0x00000058 System.Void UnityEngine.Canvas::.ctor()
extern void Canvas__ctor_m618D74C51703A5E69A0AE7CCA9746870ECDF26F8 (void);
// 0x00000059 System.Void UnityEngine.Canvas::get_renderingDisplaySize_Injected(UnityEngine.Vector2&)
extern void Canvas_get_renderingDisplaySize_Injected_m83A2E9FDA216401726B45506BACBC770D5847081 (void);
// 0x0000005A System.Void UnityEngine.Canvas/WillRenderCanvases::.ctor(System.Object,System.IntPtr)
extern void WillRenderCanvases__ctor_m8A46E9A5DED6B54DC2A8A3137AE3637081EADFB6 (void);
// 0x0000005B System.Void UnityEngine.Canvas/WillRenderCanvases::Invoke()
extern void WillRenderCanvases_Invoke_mFCD97A3223FD31C109A2B6283ECE7FE307E89282 (void);
// 0x0000005C System.IAsyncResult UnityEngine.Canvas/WillRenderCanvases::BeginInvoke(System.AsyncCallback,System.Object)
extern void WillRenderCanvases_BeginInvoke_mE22191890AD77883774E1C96019195E74427BA23 (void);
// 0x0000005D System.Void UnityEngine.Canvas/WillRenderCanvases::EndInvoke(System.IAsyncResult)
extern void WillRenderCanvases_EndInvoke_m351019B596E70CF9F5AB1317DEFBAD0D85B87813 (void);
// 0x0000005E System.Void UnityEngine.UISystemProfilerApi::BeginSample(UnityEngine.UISystemProfilerApi/SampleType)
extern void UISystemProfilerApi_BeginSample_m465C5970B7D01D43192F57D418E4D9A00A92266E (void);
// 0x0000005F System.Void UnityEngine.UISystemProfilerApi::EndSample(UnityEngine.UISystemProfilerApi/SampleType)
extern void UISystemProfilerApi_EndSample_mAB6227F7482CC70F65A198FEAAC8CF073DCB2B2D (void);
// 0x00000060 System.Void UnityEngine.UISystemProfilerApi::AddMarker(System.String,UnityEngine.Object)
extern void UISystemProfilerApi_AddMarker_mEE67561AF4CBBEC933C08296F1DC36B66A1B6E7A (void);
static Il2CppMethodPointer s_methodPointers[96] = 
{
	NULL,
	CanvasGroup_get_alpha_m38E292E68116A55A99F222F4E1F31CBE189690D9,
	CanvasGroup_set_alpha_m522B58BDF64D87252B0D43D254FF3A4D5993DC53,
	CanvasGroup_get_interactable_m643399D4E2F8F483C031ADAEEBDCC4A6A4708DA1,
	CanvasGroup_get_blocksRaycasts_m54BEB93C34E4B4ADB35A33679B0D475A0094F3B4,
	CanvasGroup_set_blocksRaycasts_m322FC5A1B70A23524463A84CC707BF50FD284B3A,
	CanvasGroup_get_ignoreParentGroups_m191E84D6A34F34A69767B396DFB6E69C8BE33A81,
	CanvasGroup_IsRaycastLocationValid_mBEEEFF19F0813F16B005D4A3021B48671F9EB124,
	CanvasRenderer_set_hasPopInstruction_mA80C720999AE5965E56F873FE29B54471B8C6329,
	CanvasRenderer_get_materialCount_mF29C9816FEC6D8189DDC7AB16B40EC56FAB59488,
	CanvasRenderer_set_materialCount_m00FE6113ACA7FE9AD51DA0A9A0B013D7C811E5DB,
	CanvasRenderer_set_popMaterialCount_mA35E4F9676B3FDA7E9E88EC1EBC711F2936CD973,
	CanvasRenderer_get_absoluteDepth_m66093A08853DE029E61146E5A271AD775B2B4330,
	CanvasRenderer_get_hasMoved_m7CF5BF1CD654A0CB0DAC1C4E4EF8C83AD9DED82A,
	CanvasRenderer_get_cull_m214173F051F22A3377BD4C91F91422CB9EEC7581,
	CanvasRenderer_set_cull_m8DCF5D7E70A6904457B66669661B1D5ABE1FCF94,
	CanvasRenderer_SetColor_mF9B22FEEB69F8577BB2952217F75E877885841F7,
	CanvasRenderer_GetColor_mEE82D01DA3B43136DAEBEC212A38AABC16D20931,
	CanvasRenderer_EnableRectClipping_mF39FD2754A2E6E4370597F1FBC7889A6170E31F1,
	CanvasRenderer_set_clippingSoftness_m0B99BD07E0C28E3B12D2F0944B2C0D0F63EAA5AE,
	CanvasRenderer_DisableRectClipping_m736771B6C7040BE07B47181CEDA705325849E95A,
	CanvasRenderer_SetMaterial_m1D7A8BD75D5DEFC5F0A27FFBA2A2A84755EE421F,
	CanvasRenderer_GetMaterial_mA674C9DC47C63CCFCDED7382D923A7796C8BE308,
	CanvasRenderer_SetPopMaterial_m2E9BBCA625FAD591DF3480287B318BFCC38A1E49,
	CanvasRenderer_SetTexture_m390FCD8FBC6E992F4AAC9967EBFA3F32A2BD93C1,
	CanvasRenderer_SetAlphaTexture_m0A867B30B6475B1D72CE28117AABED84D4A1C006,
	CanvasRenderer_SetMesh_mB506682F318E5D8D8FE3888BF50E40DC34B726DF,
	CanvasRenderer_Clear_m8793B46F28365E540BB2B2ADBA439D255E350CC4,
	CanvasRenderer_SetMaterial_mDCF29309742914F21D88D129C1E8C25F2E8A14FA,
	CanvasRenderer_SplitUIVertexStreams_m718EF0055D9309767575CBC8032EACCFB19F804D,
	CanvasRenderer_CreateUIVertexStream_m27CA19BF8CE74B3EBE01B16601DC002EB092338D,
	CanvasRenderer_AddUIVertexStream_m099A0E10770C7C603EDDBC97A05D7C1285EB310D,
	CanvasRenderer_SplitIndicesStreamsInternal_mCF7F959B344233C7D557437CF642BC780FCCE582,
	CanvasRenderer_SplitUIVertexStreamsInternal_m4F66A6BF6D08CE684FA56F6B12C1ECC07FDCB8D0,
	CanvasRenderer_CreateUIVertexStreamInternal_mCD68FA7913DCF23EC17E5D686C002C6883FC429C,
	CanvasRenderer_SetColor_Injected_m18315753FF6B5E9711C522331517ED21D5FD5F58,
	CanvasRenderer_GetColor_Injected_mA46BD781F57174120BA58E9E569AC6AB1C3131E3,
	CanvasRenderer_EnableRectClipping_Injected_m61118840C9AD8B28949DE0ABF3F5BDBBC5D1AAE0,
	CanvasRenderer_set_clippingSoftness_Injected_mCA346C58AF7E78D8C2AC2B39710A019E77A909BA,
	RectTransformUtility_PixelAdjustPoint_mA07D1645718685E85B077E7D6A9FB4D65077E83B,
	RectTransformUtility_PixelAdjustRect_m7EA78AE30783AACF0D123287C451BD9A73258927,
	RectTransformUtility_PointInRectangle_mFCECDBA154165B8750F709EBFC2A3930EEB5032C,
	RectTransformUtility_RectangleContainsScreenPoint_m7E7E3B78B91D45C9F498F78841EADA3445A02BF4,
	RectTransformUtility_RectangleContainsScreenPoint_mE4F81A74C8157E3FA749521B21EDC9C483641B16,
	RectTransformUtility_ScreenPointToWorldPointInRectangle_mD8A64D9BB1B560375B92377504425644C19E951D,
	RectTransformUtility_ScreenPointToLocalPointInRectangle_m3C56E006591714917B01C30AA186C5316334E2B8,
	RectTransformUtility_ScreenPointToRay_mFB1D8F8416DADCB12F1055CA21BAF266653F0FE5,
	RectTransformUtility_WorldToScreenPoint_mA29C11F4E906970A355DCF24F7B6BAF63975386F,
	RectTransformUtility_FlipLayoutOnAxis_m1C6AF90E1B52D12F908DA2324EF38D4529120EDD,
	RectTransformUtility_FlipLayoutAxes_m6D5A7D175F5A972977D9AC71AF96CFD4CF87AAC8,
	RectTransformUtility_GetTransposed_m01A19AFD4177D8BE65750F6880A4FD7B32CC1AEF,
	RectTransformUtility__cctor_m050932F98B7461DDEFBB132B8628DD7E6C952A0F,
	RectTransformUtility_PixelAdjustPoint_Injected_m3A85C077BFBA09901B1EAB1B8458939D9A6DE014,
	RectTransformUtility_PixelAdjustRect_Injected_m6505261AC2D142E00F5987B6E343482DAAB6E946,
	RectTransformUtility_PointInRectangle_Injected_m07A43DDAC748DC8EA043C9AA5177DC90017AA204,
	Canvas_add_preWillRenderCanvases_m4FE6F477229ED7D8670B28630A23DCEF55FFBFB8,
	Canvas_remove_preWillRenderCanvases_m073AD491608B47CC7375C5179B3220C32B03EA59,
	Canvas_add_willRenderCanvases_m0C0EB3B7B6EFEEDDECFA4DE742E62E7FBC9C8783,
	Canvas_remove_willRenderCanvases_mD5A19782872134C6BA9C448B4B02193D5B5641B5,
	Canvas_get_renderMode_mAEC8A341577CC74EC89D5890E6D6E4A82B03574D,
	Canvas_set_renderMode_mE19EA5E3E4758704CCA6A6A2E4EB84DB75096088,
	Canvas_get_isRootCanvas_mCAA9356718F8FEFB235F6F55347A26764B642FBF,
	Canvas_get_scaleFactor_m3F0D7E3B97B0493F4E98B2BBCA7A57BC1E1CB710,
	Canvas_set_scaleFactor_m707F18DB5EB8E34BC55B584426E4037CA0E78667,
	Canvas_get_referencePixelsPerUnit_m001FE1F0C8D84A3539DBB772416D05E93233395E,
	Canvas_set_referencePixelsPerUnit_mF07A80422A4C2F7A0C8CD0CD5C39A9F335571F0E,
	Canvas_get_pixelPerfect_mCAC921FAC61E9B0E42656EB8EC511AF21EC99A77,
	Canvas_set_pixelPerfect_mD477DD1867E742FF7EB7689C6DBAE243E533ED69,
	Canvas_set_planeDistance_m8CC362AC0D0259678E9088C0C441429629E8C9D9,
	Canvas_get_renderOrder_m6EA8415A6C65D304F973FF07C10E6FA41345B06B,
	Canvas_get_overrideSorting_m2A3D1772DD606F717F8F4B861411A19D1444DE90,
	Canvas_set_overrideSorting_m47944AC440D44131ED379071C4F81C166C941045,
	Canvas_get_sortingOrder_m2024155C98059AE97E268327C71E33F1444F8FC4,
	Canvas_set_sortingOrder_m217F79CAFA4654335E6FF70D1BAF4420EE46482A,
	Canvas_get_targetDisplay_m4EE59F02963F99EDCAA3FCD00F4BE5168951C10F,
	Canvas_get_sortingLayerID_mA0AB0BB70E4E6072EEC3340FE552BE4A78C48064,
	Canvas_set_sortingLayerID_m3BE132868D12C0DEA8EF5A45842948DBFACF6C6D,
	Canvas_get_additionalShaderChannels_mCAA1006118961EDC76A8A79B084D14768A42F206,
	Canvas_set_additionalShaderChannels_m857104DC8EA363A2E470CD3B873C60483135C4BD,
	Canvas_get_rootCanvas_mB1C93410A4AA793D88130FD08C05D71327641DC5,
	Canvas_get_renderingDisplaySize_mA5BF3D543E45DA1ED74A45BAFD2CB8F29844AD9B,
	Canvas_get_worldCamera_mFE4C9FDA7996FE20AC5CA3CB45B4190C40122D51,
	Canvas_GetDefaultCanvasMaterial_mBE95E64939424D4E6B52FF7D19685BF0CD1319DF,
	Canvas_GetETC1SupportedCanvasMaterial_m8AEA54BD6AA14DAB2C980B427472C6F473FAEFB0,
	Canvas_ForceUpdateCanvases_m5A81BBD794A3E844F63DFDDB070C44108E2BA006,
	Canvas_SendPreWillRenderCanvases_m59BB887BB53DE4345E3232D31C20C3303B717C58,
	Canvas_SendWillRenderCanvases_mB01E98A38733B889ACF6118D138F8072F88E5D86,
	Canvas__ctor_m618D74C51703A5E69A0AE7CCA9746870ECDF26F8,
	Canvas_get_renderingDisplaySize_Injected_m83A2E9FDA216401726B45506BACBC770D5847081,
	WillRenderCanvases__ctor_m8A46E9A5DED6B54DC2A8A3137AE3637081EADFB6,
	WillRenderCanvases_Invoke_mFCD97A3223FD31C109A2B6283ECE7FE307E89282,
	WillRenderCanvases_BeginInvoke_mE22191890AD77883774E1C96019195E74427BA23,
	WillRenderCanvases_EndInvoke_m351019B596E70CF9F5AB1317DEFBAD0D85B87813,
	UISystemProfilerApi_BeginSample_m465C5970B7D01D43192F57D418E4D9A00A92266E,
	UISystemProfilerApi_EndSample_mAB6227F7482CC70F65A198FEAAC8CF073DCB2B2D,
	UISystemProfilerApi_AddMarker_mEE67561AF4CBBEC933C08296F1DC36B66A1B6E7A,
};
static const int32_t s_InvokerIndices[96] = 
{
	1512,
	3951,
	3380,
	3863,
	3863,
	3276,
	3863,
	1512,
	3276,
	3893,
	3313,
	3313,
	3893,
	3863,
	3863,
	3276,
	3281,
	3867,
	3372,
	3396,
	3967,
	2167,
	3044,
	2167,
	3348,
	3348,
	3348,
	3967,
	2171,
	4054,
	4054,
	4075,
	5834,
	4075,
	4054,
	3255,
	3255,
	3255,
	3255,
	5258,
	5640,
	4648,
	5043,
	4638,
	4637,
	4637,
	5631,
	5707,
	4969,
	5318,
	6192,
	6289,
	4940,
	5333,
	4606,
	6218,
	6218,
	6218,
	6218,
	3893,
	3313,
	3863,
	3951,
	3380,
	3951,
	3380,
	3863,
	3276,
	3380,
	3893,
	3863,
	3276,
	3893,
	3313,
	3893,
	3893,
	3313,
	3893,
	3313,
	3916,
	3963,
	3916,
	6270,
	6270,
	6289,
	6289,
	6289,
	3967,
	3255,
	2169,
	3967,
	1717,
	3348,
	6213,
	6213,
	5834,
};
extern const CustomAttributesCacheGenerator g_UnityEngine_UIModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_UIModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_UIModule_CodeGenModule = 
{
	"UnityEngine.UIModule.dll",
	96,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_UnityEngine_UIModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

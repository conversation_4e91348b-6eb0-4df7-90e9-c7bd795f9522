﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void UnityEngine.AudioSettings::InvokeOnAudioConfigurationChanged(System.Boolean)
extern void AudioSettings_InvokeOnAudioConfigurationChanged_mCEA4121A18F3A9147BDF5CE1A4796A068A80FCBB (void);
// 0x00000002 System.Void UnityEngine.AudioSettings::InvokeOnAudioSystemShuttingDown()
extern void AudioSettings_InvokeOnAudioSystemShuttingDown_mE72E9B5505532ACA8B18F44E2FC75CDE7C3E58B9 (void);
// 0x00000003 System.Void UnityEngine.AudioSettings::InvokeOnAudioSystemStartedUp()
extern void AudioSettings_InvokeOnAudioSystemStartedUp_mC13F04020607A761698C62C7544FE29EC96DB0D9 (void);
// 0x00000004 System.Boolean UnityEngine.AudioSettings::StartAudioOutput()
extern void AudioSettings_StartAudioOutput_m927BDB1D0DB34E6E9BAEF0A35F32A092E3E9ACDF (void);
// 0x00000005 System.Boolean UnityEngine.AudioSettings::StopAudioOutput()
extern void AudioSettings_StopAudioOutput_m069991615A9A05152DC91665DD094F5B7831EA0D (void);
// 0x00000006 System.Void UnityEngine.AudioSettings/AudioConfigurationChangeHandler::.ctor(System.Object,System.IntPtr)
extern void AudioConfigurationChangeHandler__ctor_mB63AFBABA4712DF64F06A65CC7CE3C9E8C58080B (void);
// 0x00000007 System.Void UnityEngine.AudioSettings/AudioConfigurationChangeHandler::Invoke(System.Boolean)
extern void AudioConfigurationChangeHandler_Invoke_mDC001A19067B6A02B0DE21A4D66FC8D82529F911 (void);
// 0x00000008 System.IAsyncResult UnityEngine.AudioSettings/AudioConfigurationChangeHandler::BeginInvoke(System.Boolean,System.AsyncCallback,System.Object)
extern void AudioConfigurationChangeHandler_BeginInvoke_m2F17366BD1D34B7BB9FC60C1EF34EDC074D0003B (void);
// 0x00000009 System.Void UnityEngine.AudioSettings/AudioConfigurationChangeHandler::EndInvoke(System.IAsyncResult)
extern void AudioConfigurationChangeHandler_EndInvoke_m78A4BB19DA6FBEACEC4F57A51D8076A337E02F4C (void);
// 0x0000000A System.Boolean UnityEngine.AudioSettings/Mobile::get_muteState()
extern void Mobile_get_muteState_m14B80E56C6AE9E2D13EE6B7DD3FA5DC9628AF7BE (void);
// 0x0000000B System.Void UnityEngine.AudioSettings/Mobile::set_muteState(System.Boolean)
extern void Mobile_set_muteState_mDA127136DD611F6DB2E35576ECADC1205E95E9B8 (void);
// 0x0000000C System.Boolean UnityEngine.AudioSettings/Mobile::get_stopAudioOutputOnMute()
extern void Mobile_get_stopAudioOutputOnMute_mC2A8DD11430E953ABBB6379F657F4ADCCCEE27F2 (void);
// 0x0000000D System.Void UnityEngine.AudioSettings/Mobile::InvokeOnMuteStateChanged(System.Boolean)
extern void Mobile_InvokeOnMuteStateChanged_mFB91D323BB710C162D0CECBA5CA45687434AE18D (void);
// 0x0000000E System.Boolean UnityEngine.AudioSettings/Mobile::InvokeIsStopAudioOutputOnMuteEnabled()
extern void Mobile_InvokeIsStopAudioOutputOnMuteEnabled_m07AC77CE4A68BB6B44EC8B7CDE8BB8B0E5BC8ACA (void);
// 0x0000000F System.Void UnityEngine.AudioSettings/Mobile::StartAudioOutput()
extern void Mobile_StartAudioOutput_mB0931C33A4522DC5E2C051ACF6C58F7D4180973A (void);
// 0x00000010 System.Void UnityEngine.AudioSettings/Mobile::StopAudioOutput()
extern void Mobile_StopAudioOutput_m20612FFD41E519B459FAA320D917DDD6D254EFF7 (void);
// 0x00000011 System.Void UnityEngine.AudioSettings/Mobile::.cctor()
extern void Mobile__cctor_m3F4AA2BF2C6A66DC729078D7239B4B810B0B345A (void);
// 0x00000012 System.Void UnityEngine.AudioClip::.ctor()
extern void AudioClip__ctor_m25D83075F6C4735DF4E997F08E13E7EF6114C9B3 (void);
// 0x00000013 System.Boolean UnityEngine.AudioClip::GetData(UnityEngine.AudioClip,System.Single[],System.Int32,System.Int32)
extern void AudioClip_GetData_mE4AEC5F2F6F0AE633C1809D82B88A7BB9DC35FB0 (void);
// 0x00000014 System.Boolean UnityEngine.AudioClip::SetData(UnityEngine.AudioClip,System.Single[],System.Int32,System.Int32)
extern void AudioClip_SetData_mDF8EC2E91008AC18607FFA18B52A9B612BE93A1E (void);
// 0x00000015 UnityEngine.AudioClip UnityEngine.AudioClip::Construct_Internal()
extern void AudioClip_Construct_Internal_m3D090BB3A0767C33067D254D5EB68CA3212C7EF5 (void);
// 0x00000016 System.String UnityEngine.AudioClip::GetName()
extern void AudioClip_GetName_m7EC70BE0E037DDF4281B20EDDFEB9DD6D0CD541E (void);
// 0x00000017 System.Void UnityEngine.AudioClip::CreateUserSound(System.String,System.Int32,System.Int32,System.Int32,System.Boolean)
extern void AudioClip_CreateUserSound_mD9A467D0EE65E7C08122A0751C3CBF748E7A680D (void);
// 0x00000018 System.Single UnityEngine.AudioClip::get_length()
extern void AudioClip_get_length_m2223F2281D853F847BE0048620BA6F61F26440E4 (void);
// 0x00000019 System.Int32 UnityEngine.AudioClip::get_samples()
extern void AudioClip_get_samples_m741BFBA562FBFDBE67AFE98A38B1B4A871D2D567 (void);
// 0x0000001A System.Int32 UnityEngine.AudioClip::get_channels()
extern void AudioClip_get_channels_m7592B378317BFA41DF2228636124E4DD5B86D3B8 (void);
// 0x0000001B System.Int32 UnityEngine.AudioClip::get_frequency()
extern void AudioClip_get_frequency_m5346F253EE3761A2535B20E0E27B6AB0B875BFCD (void);
// 0x0000001C UnityEngine.AudioClipLoadType UnityEngine.AudioClip::get_loadType()
extern void AudioClip_get_loadType_mA61A7646B30D2F51889E8A70D69068AB27288D4C (void);
// 0x0000001D System.Boolean UnityEngine.AudioClip::LoadAudioData()
extern void AudioClip_LoadAudioData_m2A16166E36E999B940CC8D522E3003E9ED46DBBF (void);
// 0x0000001E System.Boolean UnityEngine.AudioClip::UnloadAudioData()
extern void AudioClip_UnloadAudioData_m0520CBC31DB627C288772BEED55665B86DADDC6B (void);
// 0x0000001F System.Boolean UnityEngine.AudioClip::get_preloadAudioData()
extern void AudioClip_get_preloadAudioData_m285293402C858FBE933B251C339FDC868ECD8113 (void);
// 0x00000020 System.Boolean UnityEngine.AudioClip::get_ambisonic()
extern void AudioClip_get_ambisonic_mF76D08F65799348092F7B72FBC39D460970AFD67 (void);
// 0x00000021 System.Boolean UnityEngine.AudioClip::get_loadInBackground()
extern void AudioClip_get_loadInBackground_mDE0B898CE96C8EAEE3AC446FBFCF2604C7453B03 (void);
// 0x00000022 UnityEngine.AudioDataLoadState UnityEngine.AudioClip::get_loadState()
extern void AudioClip_get_loadState_m1FF966C14C813FEFD41778D79DC79D557BE67B06 (void);
// 0x00000023 System.Boolean UnityEngine.AudioClip::GetData(System.Single[],System.Int32)
extern void AudioClip_GetData_m2D7410645789EBED93CAA8146D271C79156E2CB0 (void);
// 0x00000024 System.Boolean UnityEngine.AudioClip::SetData(System.Single[],System.Int32)
extern void AudioClip_SetData_m3424F9C6C5B7A15491B79F961C30D8F0051E34F7 (void);
// 0x00000025 UnityEngine.AudioClip UnityEngine.AudioClip::Create(System.String,System.Int32,System.Int32,System.Int32,System.Boolean)
extern void AudioClip_Create_m7B07E6C3D3F254CE8C9F135524C0768EE1680445 (void);
// 0x00000026 UnityEngine.AudioClip UnityEngine.AudioClip::Create(System.String,System.Int32,System.Int32,System.Int32,System.Boolean,UnityEngine.AudioClip/PCMReaderCallback)
extern void AudioClip_Create_mF0E9924D4E310F13C6E1F8B6C0C90313D9699037 (void);
// 0x00000027 UnityEngine.AudioClip UnityEngine.AudioClip::Create(System.String,System.Int32,System.Int32,System.Int32,System.Boolean,UnityEngine.AudioClip/PCMReaderCallback,UnityEngine.AudioClip/PCMSetPositionCallback)
extern void AudioClip_Create_m270A4CE586596EA324A82BE38D036D110AFB31D3 (void);
// 0x00000028 System.Void UnityEngine.AudioClip::add_m_PCMReaderCallback(UnityEngine.AudioClip/PCMReaderCallback)
extern void AudioClip_add_m_PCMReaderCallback_m748B6F62E0A9B5B4E36E6B5E9E40C1D883F293E5 (void);
// 0x00000029 System.Void UnityEngine.AudioClip::remove_m_PCMReaderCallback(UnityEngine.AudioClip/PCMReaderCallback)
extern void AudioClip_remove_m_PCMReaderCallback_m48FFC0B2A29E0A0ABF8340633DBB5A9EACD9D92E (void);
// 0x0000002A System.Void UnityEngine.AudioClip::add_m_PCMSetPositionCallback(UnityEngine.AudioClip/PCMSetPositionCallback)
extern void AudioClip_add_m_PCMSetPositionCallback_mDFD28A1080ACD0B27D55AD632B758E1D5FA9A8DC (void);
// 0x0000002B System.Void UnityEngine.AudioClip::remove_m_PCMSetPositionCallback(UnityEngine.AudioClip/PCMSetPositionCallback)
extern void AudioClip_remove_m_PCMSetPositionCallback_mBFD5F6A665BEF29D72BF6C7E0CC82FD2729B472E (void);
// 0x0000002C System.Void UnityEngine.AudioClip::InvokePCMReaderCallback_Internal(System.Single[])
extern void AudioClip_InvokePCMReaderCallback_Internal_m9CB2976CDC2C73A92479F8C11C30B17FAA05751F (void);
// 0x0000002D System.Void UnityEngine.AudioClip::InvokePCMSetPositionCallback_Internal(System.Int32)
extern void AudioClip_InvokePCMSetPositionCallback_Internal_m9F3ACF3A244349568C0D0D1D40EE72EF013FB45D (void);
// 0x0000002E System.Void UnityEngine.AudioClip/PCMReaderCallback::.ctor(System.Object,System.IntPtr)
extern void PCMReaderCallback__ctor_mCA9CC5271DE0E4083B85759CA74EED1C1CD219F7 (void);
// 0x0000002F System.Void UnityEngine.AudioClip/PCMReaderCallback::Invoke(System.Single[])
extern void PCMReaderCallback_Invoke_mE5E7A777A52B9627F9A6A57A140E5C4AAB5A1387 (void);
// 0x00000030 System.IAsyncResult UnityEngine.AudioClip/PCMReaderCallback::BeginInvoke(System.Single[],System.AsyncCallback,System.Object)
extern void PCMReaderCallback_BeginInvoke_mA4B5DF478312B7B7A00E13D869DF0AD4E01795A9 (void);
// 0x00000031 System.Void UnityEngine.AudioClip/PCMReaderCallback::EndInvoke(System.IAsyncResult)
extern void PCMReaderCallback_EndInvoke_m79DD97AB2919A10EBC91F78331213523EA5494DE (void);
// 0x00000032 System.Void UnityEngine.AudioClip/PCMSetPositionCallback::.ctor(System.Object,System.IntPtr)
extern void PCMSetPositionCallback__ctor_m0204C8557D7FB9E95F33168EDFD64182D9342002 (void);
// 0x00000033 System.Void UnityEngine.AudioClip/PCMSetPositionCallback::Invoke(System.Int32)
extern void PCMSetPositionCallback_Invoke_m1FBFFA5FC15B57601D6D13F4A574F7CAD2A93B7E (void);
// 0x00000034 System.IAsyncResult UnityEngine.AudioClip/PCMSetPositionCallback::BeginInvoke(System.Int32,System.AsyncCallback,System.Object)
extern void PCMSetPositionCallback_BeginInvoke_mF8CA8E07FDAF6B0821D2F1AE8F8545DC0FADC3A3 (void);
// 0x00000035 System.Void UnityEngine.AudioClip/PCMSetPositionCallback::EndInvoke(System.IAsyncResult)
extern void PCMSetPositionCallback_EndInvoke_m7096B994374E95A90C84B87625494BE1076B8574 (void);
// 0x00000036 System.Void UnityEngine.AudioBehaviour::.ctor()
extern void AudioBehaviour__ctor_mCB1009464D3656F4D8E4C112A3A6A9FDC24B2340 (void);
// 0x00000037 System.Single UnityEngine.AudioListener::get_volume()
extern void AudioListener_get_volume_m335A63461B29AD922F7BE67B8223FE4F4753B31A (void);
// 0x00000038 System.Void UnityEngine.AudioListener::set_volume(System.Single)
extern void AudioListener_set_volume_mA0C47585895B6FEF38E90DA76C77129255B1B7CD (void);
// 0x00000039 System.Single UnityEngine.AudioSource::GetPitch(UnityEngine.AudioSource)
extern void AudioSource_GetPitch_m2021D2F8359EC2533E1F70D4635067696CF78957 (void);
// 0x0000003A System.Void UnityEngine.AudioSource::SetPitch(UnityEngine.AudioSource,System.Single)
extern void AudioSource_SetPitch_mF45373E9B32A01B6752906723B09A87E5F4720C4 (void);
// 0x0000003B System.Void UnityEngine.AudioSource::PlayHelper(UnityEngine.AudioSource,System.UInt64)
extern void AudioSource_PlayHelper_m0BB0F623080F7DDECD9E111C8854A586405A9F7A (void);
// 0x0000003C System.Void UnityEngine.AudioSource::Play(System.Double)
extern void AudioSource_Play_m1F0F190F0535691D80CB663A3EF67D14BAE78566 (void);
// 0x0000003D System.Void UnityEngine.AudioSource::PlayOneShotHelper(UnityEngine.AudioSource,UnityEngine.AudioClip,System.Single)
extern void AudioSource_PlayOneShotHelper_mF4684736FC34FF733E442EB12771710A659DAA85 (void);
// 0x0000003E System.Void UnityEngine.AudioSource::Stop(System.Boolean)
extern void AudioSource_Stop_mD2260A580FFE563C64EFBD98DAC6376021ABF826 (void);
// 0x0000003F System.Void UnityEngine.AudioSource::SetCustomCurveHelper(UnityEngine.AudioSource,UnityEngine.AudioSourceCurveType,UnityEngine.AnimationCurve)
extern void AudioSource_SetCustomCurveHelper_mC674DC5F71F6658C306FABACCFD953D663FE24EB (void);
// 0x00000040 UnityEngine.AnimationCurve UnityEngine.AudioSource::GetCustomCurveHelper(UnityEngine.AudioSource,UnityEngine.AudioSourceCurveType)
extern void AudioSource_GetCustomCurveHelper_mB1FBB54475D0A55FF982FF2EAAE6AC9C7ACDB13E (void);
// 0x00000041 System.Void UnityEngine.AudioSource::GetOutputDataHelper(UnityEngine.AudioSource,System.Single[],System.Int32)
extern void AudioSource_GetOutputDataHelper_m1B65E98D9D376D51039E029FB3809E2B933A5881 (void);
// 0x00000042 System.Void UnityEngine.AudioSource::GetSpectrumDataHelper(UnityEngine.AudioSource,System.Single[],System.Int32,UnityEngine.FFTWindow)
extern void AudioSource_GetSpectrumDataHelper_mC1D891C8E867B60240B3F974B01372F887CBDDF8 (void);
// 0x00000043 System.Single UnityEngine.AudioSource::get_volume()
extern void AudioSource_get_volume_mFD700932958E0A96199B012E28AE4E9F7D6C1556 (void);
// 0x00000044 System.Void UnityEngine.AudioSource::set_volume(System.Single)
extern void AudioSource_set_volume_m37B6B2EACA7C2C18ABEE55EE5EA404085E94EE58 (void);
// 0x00000045 System.Single UnityEngine.AudioSource::get_pitch()
extern void AudioSource_get_pitch_mB0A139A0693803E201D08B797BFD122B94453C7D (void);
// 0x00000046 System.Void UnityEngine.AudioSource::set_pitch(System.Single)
extern void AudioSource_set_pitch_m9E9EEFADFAB44ADEC656573425CF34D0069A0CBC (void);
// 0x00000047 System.Single UnityEngine.AudioSource::get_time()
extern void AudioSource_get_time_m2185448A5B8A50225410220AF67BC34F5596D210 (void);
// 0x00000048 System.Void UnityEngine.AudioSource::set_time(System.Single)
extern void AudioSource_set_time_mE8F36DDE8E610126F9733D13F299C13F71BCE690 (void);
// 0x00000049 System.Int32 UnityEngine.AudioSource::get_timeSamples()
extern void AudioSource_get_timeSamples_m2D16DA78011B58C24BCA679B44D2BF832DFCA0BE (void);
// 0x0000004A System.Void UnityEngine.AudioSource::set_timeSamples(System.Int32)
extern void AudioSource_set_timeSamples_m0C976854DF09CEF28A8A21C0E069905E55BC2F33 (void);
// 0x0000004B UnityEngine.AudioClip UnityEngine.AudioSource::get_clip()
extern void AudioSource_get_clip_mE4454E38D2C0A4C8CC780A435FC1DBD4D47D16DC (void);
// 0x0000004C System.Void UnityEngine.AudioSource::set_clip(UnityEngine.AudioClip)
extern void AudioSource_set_clip_mD1F50F7BA6EA3AF25B4922473352C5180CFF7B2B (void);
// 0x0000004D UnityEngine.Audio.AudioMixerGroup UnityEngine.AudioSource::get_outputAudioMixerGroup()
extern void AudioSource_get_outputAudioMixerGroup_mBB5231BEBD764B7D5AB5225470B8AC6423B18A5D (void);
// 0x0000004E System.Void UnityEngine.AudioSource::set_outputAudioMixerGroup(UnityEngine.Audio.AudioMixerGroup)
extern void AudioSource_set_outputAudioMixerGroup_mE63B2B8C7E3F4E8C670E2474D1F22F9864DB71C8 (void);
// 0x0000004F System.Void UnityEngine.AudioSource::Play()
extern void AudioSource_Play_mED16664B8F8F3E4D68785C8C00FC96C4DF053AE1 (void);
// 0x00000050 System.Void UnityEngine.AudioSource::Play(System.UInt64)
extern void AudioSource_Play_m76D05EE3D8A6A6B3EC4C18D3BF7DE3732826B515 (void);
// 0x00000051 System.Void UnityEngine.AudioSource::PlayDelayed(System.Single)
extern void AudioSource_PlayDelayed_m9C6234595614E2F4A8035CB4D501E83A8C793DD4 (void);
// 0x00000052 System.Void UnityEngine.AudioSource::PlayScheduled(System.Double)
extern void AudioSource_PlayScheduled_m80E8DC1432D331243D69F028D5751BFCA0BD89C7 (void);
// 0x00000053 System.Void UnityEngine.AudioSource::PlayOneShot(UnityEngine.AudioClip)
extern void AudioSource_PlayOneShot_mA90B136041A61C30909301D45D0315088CA7D796 (void);
// 0x00000054 System.Void UnityEngine.AudioSource::PlayOneShot(UnityEngine.AudioClip,System.Single)
extern void AudioSource_PlayOneShot_mBFCD838C503CE4334501C9864C091FE0061CF024 (void);
// 0x00000055 System.Void UnityEngine.AudioSource::SetScheduledStartTime(System.Double)
extern void AudioSource_SetScheduledStartTime_mC734241BC608A55787FFF503530225582A813473 (void);
// 0x00000056 System.Void UnityEngine.AudioSource::SetScheduledEndTime(System.Double)
extern void AudioSource_SetScheduledEndTime_mEB26C97CBB00B5D490B99021D8A1F9E419B1F7F1 (void);
// 0x00000057 System.Void UnityEngine.AudioSource::Stop()
extern void AudioSource_Stop_mADA564D223832A64F8CF3EFBDEB534C0D658810F (void);
// 0x00000058 System.Void UnityEngine.AudioSource::Pause()
extern void AudioSource_Pause_mC4F9932A77B6AA2CC3FB720721B7837CF57B675D (void);
// 0x00000059 System.Void UnityEngine.AudioSource::UnPause()
extern void AudioSource_UnPause_m17BEE2B5A9607757EF0635450D210E4FD5E3EAF2 (void);
// 0x0000005A System.Boolean UnityEngine.AudioSource::get_isPlaying()
extern void AudioSource_get_isPlaying_mEA69477C77D542971F7B454946EF25DFBE0AF6A8 (void);
// 0x0000005B System.Boolean UnityEngine.AudioSource::get_isVirtual()
extern void AudioSource_get_isVirtual_mAB046E2B544DD465D460CC666D40F253427601A6 (void);
// 0x0000005C System.Void UnityEngine.AudioSource::PlayClipAtPoint(UnityEngine.AudioClip,UnityEngine.Vector3)
extern void AudioSource_PlayClipAtPoint_mF22C2AFDB311114BC4DAFA31B7CB417DFDB8ACA5 (void);
// 0x0000005D System.Void UnityEngine.AudioSource::PlayClipAtPoint(UnityEngine.AudioClip,UnityEngine.Vector3,System.Single)
extern void AudioSource_PlayClipAtPoint_m9CD1C8E54064EBBE08F03B8B32E843180269FDE0 (void);
// 0x0000005E System.Boolean UnityEngine.AudioSource::get_loop()
extern void AudioSource_get_loop_m4CC6F50D9332DCC4B3CF380D12622F787BB8FB8D (void);
// 0x0000005F System.Void UnityEngine.AudioSource::set_loop(System.Boolean)
extern void AudioSource_set_loop_mDD9FB746D8A7392472E5484EEF8D0A667993E3E0 (void);
// 0x00000060 System.Boolean UnityEngine.AudioSource::get_ignoreListenerVolume()
extern void AudioSource_get_ignoreListenerVolume_m35CC0A1BECA356AD668B052B36C3D321712EB13E (void);
// 0x00000061 System.Void UnityEngine.AudioSource::set_ignoreListenerVolume(System.Boolean)
extern void AudioSource_set_ignoreListenerVolume_mDD129EDA08A2F41647CEAF64A505F55F33A342C2 (void);
// 0x00000062 System.Boolean UnityEngine.AudioSource::get_playOnAwake()
extern void AudioSource_get_playOnAwake_mDB89801F304962AA2577AB653A7BA3F38F8FFF18 (void);
// 0x00000063 System.Void UnityEngine.AudioSource::set_playOnAwake(System.Boolean)
extern void AudioSource_set_playOnAwake_mBE8FAD0E1555C431C56E0DEB4BF7371E76E236F5 (void);
// 0x00000064 System.Boolean UnityEngine.AudioSource::get_ignoreListenerPause()
extern void AudioSource_get_ignoreListenerPause_mAAB7CCE687F4B7C1B160C1E4D1F5ED24AEC93DF1 (void);
// 0x00000065 System.Void UnityEngine.AudioSource::set_ignoreListenerPause(System.Boolean)
extern void AudioSource_set_ignoreListenerPause_mE3962A93D28E974C6BD7CAB8AE98C09162B3B968 (void);
// 0x00000066 UnityEngine.AudioVelocityUpdateMode UnityEngine.AudioSource::get_velocityUpdateMode()
extern void AudioSource_get_velocityUpdateMode_m883F604B18AB40C1AF65777C13BD05402FF351DB (void);
// 0x00000067 System.Void UnityEngine.AudioSource::set_velocityUpdateMode(UnityEngine.AudioVelocityUpdateMode)
extern void AudioSource_set_velocityUpdateMode_m63201E0CAC0E5BC81B5F31F96F1796FD8A43982A (void);
// 0x00000068 System.Single UnityEngine.AudioSource::get_panStereo()
extern void AudioSource_get_panStereo_m38649C79038F845D64A8A473A5CF1A083BF7BDB3 (void);
// 0x00000069 System.Void UnityEngine.AudioSource::set_panStereo(System.Single)
extern void AudioSource_set_panStereo_mACEB98E6EBB22616F66175EEFC5ADEAFACB3209C (void);
// 0x0000006A System.Single UnityEngine.AudioSource::get_spatialBlend()
extern void AudioSource_get_spatialBlend_m6DA88A44F069A8085C29490FEEDDD5CF9F5DF163 (void);
// 0x0000006B System.Void UnityEngine.AudioSource::set_spatialBlend(System.Single)
extern void AudioSource_set_spatialBlend_m7DD3DA6F68BA6710EDB2EE8482F876D16F9A9BEC (void);
// 0x0000006C System.Boolean UnityEngine.AudioSource::get_spatialize()
extern void AudioSource_get_spatialize_m2DA76AA88FBD2EEB8ED4E45CF497C5CA44FDA058 (void);
// 0x0000006D System.Void UnityEngine.AudioSource::set_spatialize(System.Boolean)
extern void AudioSource_set_spatialize_m55AE6DFEBBB2A15A1A5A1ECFF0B8CFD38927BD41 (void);
// 0x0000006E System.Boolean UnityEngine.AudioSource::get_spatializePostEffects()
extern void AudioSource_get_spatializePostEffects_m6AD56340718E536C0FD40A95CE582F90D99A31CC (void);
// 0x0000006F System.Void UnityEngine.AudioSource::set_spatializePostEffects(System.Boolean)
extern void AudioSource_set_spatializePostEffects_mDEC6094ED4000B2C69BA4105D0D2A3ABE32C005A (void);
// 0x00000070 System.Void UnityEngine.AudioSource::SetCustomCurve(UnityEngine.AudioSourceCurveType,UnityEngine.AnimationCurve)
extern void AudioSource_SetCustomCurve_m7F9FDB65C9A26D7491E041600157340A09552D56 (void);
// 0x00000071 UnityEngine.AnimationCurve UnityEngine.AudioSource::GetCustomCurve(UnityEngine.AudioSourceCurveType)
extern void AudioSource_GetCustomCurve_m71F1A8BB2AD0B3B46CF86F6D1A598B92C7CAEBA6 (void);
// 0x00000072 System.Single UnityEngine.AudioSource::get_reverbZoneMix()
extern void AudioSource_get_reverbZoneMix_m28603F3061FD472444AC7DD54DFD57F597A78A51 (void);
// 0x00000073 System.Void UnityEngine.AudioSource::set_reverbZoneMix(System.Single)
extern void AudioSource_set_reverbZoneMix_m701BBB1343D14456A2959FBB5304A51DE1164795 (void);
// 0x00000074 System.Boolean UnityEngine.AudioSource::get_bypassEffects()
extern void AudioSource_get_bypassEffects_mBEF3FEFF96D5783F42C272CC080D9B92109CAD88 (void);
// 0x00000075 System.Void UnityEngine.AudioSource::set_bypassEffects(System.Boolean)
extern void AudioSource_set_bypassEffects_m85076AE15C98565868F8501C96F0F8E4BA6D45C0 (void);
// 0x00000076 System.Boolean UnityEngine.AudioSource::get_bypassListenerEffects()
extern void AudioSource_get_bypassListenerEffects_m78FAD90E57FE0BC9397F39D9F979074B7353EEE6 (void);
// 0x00000077 System.Void UnityEngine.AudioSource::set_bypassListenerEffects(System.Boolean)
extern void AudioSource_set_bypassListenerEffects_mA8A25D40D1DC6E822CD28A59B822C065BF26303E (void);
// 0x00000078 System.Boolean UnityEngine.AudioSource::get_bypassReverbZones()
extern void AudioSource_get_bypassReverbZones_m5807F15A20AE9501F336AA56A3876D070F072C5E (void);
// 0x00000079 System.Void UnityEngine.AudioSource::set_bypassReverbZones(System.Boolean)
extern void AudioSource_set_bypassReverbZones_m681F9D84EE44CC7E57B49865D3B1E3898B7D684D (void);
// 0x0000007A System.Single UnityEngine.AudioSource::get_dopplerLevel()
extern void AudioSource_get_dopplerLevel_mF7181F2CE97541C9C3E499A9CDD77E963B9A1B94 (void);
// 0x0000007B System.Void UnityEngine.AudioSource::set_dopplerLevel(System.Single)
extern void AudioSource_set_dopplerLevel_m3E3187EA638992BE0A7AF9D660DAB1B0D088C2A0 (void);
// 0x0000007C System.Single UnityEngine.AudioSource::get_spread()
extern void AudioSource_get_spread_mD3174C0186177B225EF3E27FBA4FC7CDAB02D733 (void);
// 0x0000007D System.Void UnityEngine.AudioSource::set_spread(System.Single)
extern void AudioSource_set_spread_m933BAB5C1122A350F18490CA208257F2ECAC5887 (void);
// 0x0000007E System.Int32 UnityEngine.AudioSource::get_priority()
extern void AudioSource_get_priority_m60F08C8F2D9DF0ED93CEB43F6DF6894873EF98FA (void);
// 0x0000007F System.Void UnityEngine.AudioSource::set_priority(System.Int32)
extern void AudioSource_set_priority_m0A24AC2400304023BC732399F26709789D4C7ED2 (void);
// 0x00000080 System.Boolean UnityEngine.AudioSource::get_mute()
extern void AudioSource_get_mute_m87FF3468F096AE68C817B885D9AAC246CA4F1AFE (void);
// 0x00000081 System.Void UnityEngine.AudioSource::set_mute(System.Boolean)
extern void AudioSource_set_mute_m69E2DFCF261D2D187ED756096B7E0DE622292C71 (void);
// 0x00000082 System.Single UnityEngine.AudioSource::get_minDistance()
extern void AudioSource_get_minDistance_mA46B8982F6111835096E125A7162A5EAFAED31B1 (void);
// 0x00000083 System.Void UnityEngine.AudioSource::set_minDistance(System.Single)
extern void AudioSource_set_minDistance_m9C1D3410330B09EACA69C64BF634018D71180F91 (void);
// 0x00000084 System.Single UnityEngine.AudioSource::get_maxDistance()
extern void AudioSource_get_maxDistance_mBCCA59FDF624C4162AB4E4BA8B5642BADE7FF2FD (void);
// 0x00000085 System.Void UnityEngine.AudioSource::set_maxDistance(System.Single)
extern void AudioSource_set_maxDistance_m8AF5831BC0B2D070B2D4E1B54CB27082240A5EA8 (void);
// 0x00000086 UnityEngine.AudioRolloffMode UnityEngine.AudioSource::get_rolloffMode()
extern void AudioSource_get_rolloffMode_mDE4AD9F22886B5709F5EAAC78176D15FA5478215 (void);
// 0x00000087 System.Void UnityEngine.AudioSource::set_rolloffMode(UnityEngine.AudioRolloffMode)
extern void AudioSource_set_rolloffMode_mCEE552C06C70A8E4E64500C5792AD67D33782B4A (void);
// 0x00000088 System.Void UnityEngine.AudioSource::GetOutputData(System.Single[],System.Int32)
extern void AudioSource_GetOutputData_mB236FC4E99BC03BFB9402FCC4140946DDFB0C849 (void);
// 0x00000089 System.Void UnityEngine.AudioSource::GetSpectrumData(System.Single[],System.Int32,UnityEngine.FFTWindow)
extern void AudioSource_GetSpectrumData_m534E9BDB8C2924A118A711A0F741581DCEB27266 (void);
// 0x0000008A System.Boolean UnityEngine.AudioSource::SetSpatializerFloat(System.Int32,System.Single)
extern void AudioSource_SetSpatializerFloat_m9EEBFE4F23D91F670FC5C3E2A3EF1C6454C935FE (void);
// 0x0000008B System.Boolean UnityEngine.AudioSource::GetSpatializerFloat(System.Int32,System.Single&)
extern void AudioSource_GetSpatializerFloat_m4563FA47677E3F0FD1F43B6951131FDD855D58BE (void);
// 0x0000008C System.Boolean UnityEngine.AudioSource::GetAmbisonicDecoderFloat(System.Int32,System.Single&)
extern void AudioSource_GetAmbisonicDecoderFloat_mDE10445884FDC97C0933D58944BE6C24166C1FD4 (void);
// 0x0000008D System.Boolean UnityEngine.AudioSource::SetAmbisonicDecoderFloat(System.Int32,System.Single)
extern void AudioSource_SetAmbisonicDecoderFloat_m7C55997736929F9B1208284F10313BC42DBF601B (void);
// 0x0000008E System.Void UnityEngine.AudioSource::.ctor()
extern void AudioSource__ctor_m1CDE1D0F46156589602C00D42F65ADE562D230A8 (void);
// 0x0000008F System.Int32 UnityEngine.Microphone::GetMicrophoneDeviceIDFromName(System.String)
extern void Microphone_GetMicrophoneDeviceIDFromName_mB8F3723AFC4B9636BF3C1BDB532410223F6A9F51 (void);
// 0x00000090 UnityEngine.AudioClip UnityEngine.Microphone::StartRecord(System.Int32,System.Boolean,System.Single,System.Int32)
extern void Microphone_StartRecord_m9B9A119ED858D9AC4665EE378D36D3F19451460E (void);
// 0x00000091 System.Void UnityEngine.Microphone::EndRecord(System.Int32)
extern void Microphone_EndRecord_m59704A5A11DD65930997572D419C9899B7AB9008 (void);
// 0x00000092 System.Boolean UnityEngine.Microphone::IsRecording(System.Int32)
extern void Microphone_IsRecording_m98F75D7466EDA4C8D149825E0C1F2AF04138F0A2 (void);
// 0x00000093 System.Int32 UnityEngine.Microphone::GetRecordPosition(System.Int32)
extern void Microphone_GetRecordPosition_m5171F6E8F89D07235F7B500049749D536E1465F3 (void);
// 0x00000094 UnityEngine.AudioClip UnityEngine.Microphone::Start(System.String,System.Boolean,System.Int32,System.Int32)
extern void Microphone_Start_m2F9AD1061F64F1F569B00503955CF728C1B16D06 (void);
// 0x00000095 System.Void UnityEngine.Microphone::End(System.String)
extern void Microphone_End_mA8355F6CFDE340AE7177821B8544CA99CCE8071B (void);
// 0x00000096 System.String[] UnityEngine.Microphone::get_devices()
extern void Microphone_get_devices_m4A9DF5F5C10340385BB060D5C0F4A6BCA19B05E5 (void);
// 0x00000097 System.Boolean UnityEngine.Microphone::IsRecording(System.String)
extern void Microphone_IsRecording_mFE8C01EC643515C463F5027CC3EA4FD5BDC9B0A4 (void);
// 0x00000098 System.Int32 UnityEngine.Microphone::GetPosition(System.String)
extern void Microphone_GetPosition_mAF3B9C0BAB393C2EE6337A916E86D24B7C69A4E1 (void);
// 0x00000099 UnityEngine.Playables.PlayableHandle UnityEngine.Audio.AudioClipPlayable::GetHandle()
extern void AudioClipPlayable_GetHandle_mBEB846B088961170B6DB961951B511C11B98E0B8 (void);
// 0x0000009A System.Boolean UnityEngine.Audio.AudioClipPlayable::Equals(UnityEngine.Audio.AudioClipPlayable)
extern void AudioClipPlayable_Equals_m52ECDD49AE6BD8AB4C0AC83C417A0C1B23E3E55E (void);
// 0x0000009B System.Boolean UnityEngine.Audio.AudioMixer::SetFloat(System.String,System.Single)
extern void AudioMixer_SetFloat_m305579F01374620674AF66DA63DDD4BDBC9089CE (void);
// 0x0000009C System.Boolean UnityEngine.Audio.AudioMixer::GetFloat(System.String,System.Single&)
extern void AudioMixer_GetFloat_m269CC0D94DFA272A6900F2A75AE94FF7B9D8E40C (void);
// 0x0000009D System.Void UnityEngine.Audio.AudioMixerGroup::.ctor()
extern void AudioMixerGroup__ctor_m46817B3CE6F248F277E9F376B3806BF9136902EF (void);
// 0x0000009E UnityEngine.Playables.PlayableHandle UnityEngine.Audio.AudioMixerPlayable::GetHandle()
extern void AudioMixerPlayable_GetHandle_m76EFC486A7639C4842F590F544B60988CF27BB17 (void);
// 0x0000009F System.Boolean UnityEngine.Audio.AudioMixerPlayable::Equals(UnityEngine.Audio.AudioMixerPlayable)
extern void AudioMixerPlayable_Equals_mB55D2602ACCD196F61AF3D1AE90B81930A9AB7E8 (void);
// 0x000000A0 System.Void UnityEngine.Experimental.Audio.AudioSampleProvider::InvokeSampleFramesAvailable(System.Int32)
extern void AudioSampleProvider_InvokeSampleFramesAvailable_mE6689CFA13C0621F305F389FEEE4D543B71BF236 (void);
// 0x000000A1 System.Void UnityEngine.Experimental.Audio.AudioSampleProvider::InvokeSampleFramesOverflow(System.Int32)
extern void AudioSampleProvider_InvokeSampleFramesOverflow_m998BEADD2A2B4BEF0906A31108B6DC486411CC78 (void);
// 0x000000A2 System.Void UnityEngine.Experimental.Audio.AudioSampleProvider/SampleFramesHandler::.ctor(System.Object,System.IntPtr)
extern void SampleFramesHandler__ctor_m389B32B949592BFD1BA53D0C0983CA6B5BA6AAC7 (void);
// 0x000000A3 System.Void UnityEngine.Experimental.Audio.AudioSampleProvider/SampleFramesHandler::Invoke(UnityEngine.Experimental.Audio.AudioSampleProvider,System.UInt32)
extern void SampleFramesHandler_Invoke_mCB6172CE3EF20C5E12A697A5CE5EEDED9A3B5779 (void);
// 0x000000A4 System.IAsyncResult UnityEngine.Experimental.Audio.AudioSampleProvider/SampleFramesHandler::BeginInvoke(UnityEngine.Experimental.Audio.AudioSampleProvider,System.UInt32,System.AsyncCallback,System.Object)
extern void SampleFramesHandler_BeginInvoke_m79D52B9AB6F6E72A9A9260F6608F7FA9F6A10ADC (void);
// 0x000000A5 System.Void UnityEngine.Experimental.Audio.AudioSampleProvider/SampleFramesHandler::EndInvoke(System.IAsyncResult)
extern void SampleFramesHandler_EndInvoke_mC5357233A55B232AC8507FA4A04E06271EDE0309 (void);
static Il2CppMethodPointer s_methodPointers[165] = 
{
	AudioSettings_InvokeOnAudioConfigurationChanged_mCEA4121A18F3A9147BDF5CE1A4796A068A80FCBB,
	AudioSettings_InvokeOnAudioSystemShuttingDown_mE72E9B5505532ACA8B18F44E2FC75CDE7C3E58B9,
	AudioSettings_InvokeOnAudioSystemStartedUp_mC13F04020607A761698C62C7544FE29EC96DB0D9,
	AudioSettings_StartAudioOutput_m927BDB1D0DB34E6E9BAEF0A35F32A092E3E9ACDF,
	AudioSettings_StopAudioOutput_m069991615A9A05152DC91665DD094F5B7831EA0D,
	AudioConfigurationChangeHandler__ctor_mB63AFBABA4712DF64F06A65CC7CE3C9E8C58080B,
	AudioConfigurationChangeHandler_Invoke_mDC001A19067B6A02B0DE21A4D66FC8D82529F911,
	AudioConfigurationChangeHandler_BeginInvoke_m2F17366BD1D34B7BB9FC60C1EF34EDC074D0003B,
	AudioConfigurationChangeHandler_EndInvoke_m78A4BB19DA6FBEACEC4F57A51D8076A337E02F4C,
	Mobile_get_muteState_m14B80E56C6AE9E2D13EE6B7DD3FA5DC9628AF7BE,
	Mobile_set_muteState_mDA127136DD611F6DB2E35576ECADC1205E95E9B8,
	Mobile_get_stopAudioOutputOnMute_mC2A8DD11430E953ABBB6379F657F4ADCCCEE27F2,
	Mobile_InvokeOnMuteStateChanged_mFB91D323BB710C162D0CECBA5CA45687434AE18D,
	Mobile_InvokeIsStopAudioOutputOnMuteEnabled_m07AC77CE4A68BB6B44EC8B7CDE8BB8B0E5BC8ACA,
	Mobile_StartAudioOutput_mB0931C33A4522DC5E2C051ACF6C58F7D4180973A,
	Mobile_StopAudioOutput_m20612FFD41E519B459FAA320D917DDD6D254EFF7,
	Mobile__cctor_m3F4AA2BF2C6A66DC729078D7239B4B810B0B345A,
	AudioClip__ctor_m25D83075F6C4735DF4E997F08E13E7EF6114C9B3,
	AudioClip_GetData_mE4AEC5F2F6F0AE633C1809D82B88A7BB9DC35FB0,
	AudioClip_SetData_mDF8EC2E91008AC18607FFA18B52A9B612BE93A1E,
	AudioClip_Construct_Internal_m3D090BB3A0767C33067D254D5EB68CA3212C7EF5,
	AudioClip_GetName_m7EC70BE0E037DDF4281B20EDDFEB9DD6D0CD541E,
	AudioClip_CreateUserSound_mD9A467D0EE65E7C08122A0751C3CBF748E7A680D,
	AudioClip_get_length_m2223F2281D853F847BE0048620BA6F61F26440E4,
	AudioClip_get_samples_m741BFBA562FBFDBE67AFE98A38B1B4A871D2D567,
	AudioClip_get_channels_m7592B378317BFA41DF2228636124E4DD5B86D3B8,
	AudioClip_get_frequency_m5346F253EE3761A2535B20E0E27B6AB0B875BFCD,
	AudioClip_get_loadType_mA61A7646B30D2F51889E8A70D69068AB27288D4C,
	AudioClip_LoadAudioData_m2A16166E36E999B940CC8D522E3003E9ED46DBBF,
	AudioClip_UnloadAudioData_m0520CBC31DB627C288772BEED55665B86DADDC6B,
	AudioClip_get_preloadAudioData_m285293402C858FBE933B251C339FDC868ECD8113,
	AudioClip_get_ambisonic_mF76D08F65799348092F7B72FBC39D460970AFD67,
	AudioClip_get_loadInBackground_mDE0B898CE96C8EAEE3AC446FBFCF2604C7453B03,
	AudioClip_get_loadState_m1FF966C14C813FEFD41778D79DC79D557BE67B06,
	AudioClip_GetData_m2D7410645789EBED93CAA8146D271C79156E2CB0,
	AudioClip_SetData_m3424F9C6C5B7A15491B79F961C30D8F0051E34F7,
	AudioClip_Create_m7B07E6C3D3F254CE8C9F135524C0768EE1680445,
	AudioClip_Create_mF0E9924D4E310F13C6E1F8B6C0C90313D9699037,
	AudioClip_Create_m270A4CE586596EA324A82BE38D036D110AFB31D3,
	AudioClip_add_m_PCMReaderCallback_m748B6F62E0A9B5B4E36E6B5E9E40C1D883F293E5,
	AudioClip_remove_m_PCMReaderCallback_m48FFC0B2A29E0A0ABF8340633DBB5A9EACD9D92E,
	AudioClip_add_m_PCMSetPositionCallback_mDFD28A1080ACD0B27D55AD632B758E1D5FA9A8DC,
	AudioClip_remove_m_PCMSetPositionCallback_mBFD5F6A665BEF29D72BF6C7E0CC82FD2729B472E,
	AudioClip_InvokePCMReaderCallback_Internal_m9CB2976CDC2C73A92479F8C11C30B17FAA05751F,
	AudioClip_InvokePCMSetPositionCallback_Internal_m9F3ACF3A244349568C0D0D1D40EE72EF013FB45D,
	PCMReaderCallback__ctor_mCA9CC5271DE0E4083B85759CA74EED1C1CD219F7,
	PCMReaderCallback_Invoke_mE5E7A777A52B9627F9A6A57A140E5C4AAB5A1387,
	PCMReaderCallback_BeginInvoke_mA4B5DF478312B7B7A00E13D869DF0AD4E01795A9,
	PCMReaderCallback_EndInvoke_m79DD97AB2919A10EBC91F78331213523EA5494DE,
	PCMSetPositionCallback__ctor_m0204C8557D7FB9E95F33168EDFD64182D9342002,
	PCMSetPositionCallback_Invoke_m1FBFFA5FC15B57601D6D13F4A574F7CAD2A93B7E,
	PCMSetPositionCallback_BeginInvoke_mF8CA8E07FDAF6B0821D2F1AE8F8545DC0FADC3A3,
	PCMSetPositionCallback_EndInvoke_m7096B994374E95A90C84B87625494BE1076B8574,
	AudioBehaviour__ctor_mCB1009464D3656F4D8E4C112A3A6A9FDC24B2340,
	AudioListener_get_volume_m335A63461B29AD922F7BE67B8223FE4F4753B31A,
	AudioListener_set_volume_mA0C47585895B6FEF38E90DA76C77129255B1B7CD,
	AudioSource_GetPitch_m2021D2F8359EC2533E1F70D4635067696CF78957,
	AudioSource_SetPitch_mF45373E9B32A01B6752906723B09A87E5F4720C4,
	AudioSource_PlayHelper_m0BB0F623080F7DDECD9E111C8854A586405A9F7A,
	AudioSource_Play_m1F0F190F0535691D80CB663A3EF67D14BAE78566,
	AudioSource_PlayOneShotHelper_mF4684736FC34FF733E442EB12771710A659DAA85,
	AudioSource_Stop_mD2260A580FFE563C64EFBD98DAC6376021ABF826,
	AudioSource_SetCustomCurveHelper_mC674DC5F71F6658C306FABACCFD953D663FE24EB,
	AudioSource_GetCustomCurveHelper_mB1FBB54475D0A55FF982FF2EAAE6AC9C7ACDB13E,
	AudioSource_GetOutputDataHelper_m1B65E98D9D376D51039E029FB3809E2B933A5881,
	AudioSource_GetSpectrumDataHelper_mC1D891C8E867B60240B3F974B01372F887CBDDF8,
	AudioSource_get_volume_mFD700932958E0A96199B012E28AE4E9F7D6C1556,
	AudioSource_set_volume_m37B6B2EACA7C2C18ABEE55EE5EA404085E94EE58,
	AudioSource_get_pitch_mB0A139A0693803E201D08B797BFD122B94453C7D,
	AudioSource_set_pitch_m9E9EEFADFAB44ADEC656573425CF34D0069A0CBC,
	AudioSource_get_time_m2185448A5B8A50225410220AF67BC34F5596D210,
	AudioSource_set_time_mE8F36DDE8E610126F9733D13F299C13F71BCE690,
	AudioSource_get_timeSamples_m2D16DA78011B58C24BCA679B44D2BF832DFCA0BE,
	AudioSource_set_timeSamples_m0C976854DF09CEF28A8A21C0E069905E55BC2F33,
	AudioSource_get_clip_mE4454E38D2C0A4C8CC780A435FC1DBD4D47D16DC,
	AudioSource_set_clip_mD1F50F7BA6EA3AF25B4922473352C5180CFF7B2B,
	AudioSource_get_outputAudioMixerGroup_mBB5231BEBD764B7D5AB5225470B8AC6423B18A5D,
	AudioSource_set_outputAudioMixerGroup_mE63B2B8C7E3F4E8C670E2474D1F22F9864DB71C8,
	AudioSource_Play_mED16664B8F8F3E4D68785C8C00FC96C4DF053AE1,
	AudioSource_Play_m76D05EE3D8A6A6B3EC4C18D3BF7DE3732826B515,
	AudioSource_PlayDelayed_m9C6234595614E2F4A8035CB4D501E83A8C793DD4,
	AudioSource_PlayScheduled_m80E8DC1432D331243D69F028D5751BFCA0BD89C7,
	AudioSource_PlayOneShot_mA90B136041A61C30909301D45D0315088CA7D796,
	AudioSource_PlayOneShot_mBFCD838C503CE4334501C9864C091FE0061CF024,
	AudioSource_SetScheduledStartTime_mC734241BC608A55787FFF503530225582A813473,
	AudioSource_SetScheduledEndTime_mEB26C97CBB00B5D490B99021D8A1F9E419B1F7F1,
	AudioSource_Stop_mADA564D223832A64F8CF3EFBDEB534C0D658810F,
	AudioSource_Pause_mC4F9932A77B6AA2CC3FB720721B7837CF57B675D,
	AudioSource_UnPause_m17BEE2B5A9607757EF0635450D210E4FD5E3EAF2,
	AudioSource_get_isPlaying_mEA69477C77D542971F7B454946EF25DFBE0AF6A8,
	AudioSource_get_isVirtual_mAB046E2B544DD465D460CC666D40F253427601A6,
	AudioSource_PlayClipAtPoint_mF22C2AFDB311114BC4DAFA31B7CB417DFDB8ACA5,
	AudioSource_PlayClipAtPoint_m9CD1C8E54064EBBE08F03B8B32E843180269FDE0,
	AudioSource_get_loop_m4CC6F50D9332DCC4B3CF380D12622F787BB8FB8D,
	AudioSource_set_loop_mDD9FB746D8A7392472E5484EEF8D0A667993E3E0,
	AudioSource_get_ignoreListenerVolume_m35CC0A1BECA356AD668B052B36C3D321712EB13E,
	AudioSource_set_ignoreListenerVolume_mDD129EDA08A2F41647CEAF64A505F55F33A342C2,
	AudioSource_get_playOnAwake_mDB89801F304962AA2577AB653A7BA3F38F8FFF18,
	AudioSource_set_playOnAwake_mBE8FAD0E1555C431C56E0DEB4BF7371E76E236F5,
	AudioSource_get_ignoreListenerPause_mAAB7CCE687F4B7C1B160C1E4D1F5ED24AEC93DF1,
	AudioSource_set_ignoreListenerPause_mE3962A93D28E974C6BD7CAB8AE98C09162B3B968,
	AudioSource_get_velocityUpdateMode_m883F604B18AB40C1AF65777C13BD05402FF351DB,
	AudioSource_set_velocityUpdateMode_m63201E0CAC0E5BC81B5F31F96F1796FD8A43982A,
	AudioSource_get_panStereo_m38649C79038F845D64A8A473A5CF1A083BF7BDB3,
	AudioSource_set_panStereo_mACEB98E6EBB22616F66175EEFC5ADEAFACB3209C,
	AudioSource_get_spatialBlend_m6DA88A44F069A8085C29490FEEDDD5CF9F5DF163,
	AudioSource_set_spatialBlend_m7DD3DA6F68BA6710EDB2EE8482F876D16F9A9BEC,
	AudioSource_get_spatialize_m2DA76AA88FBD2EEB8ED4E45CF497C5CA44FDA058,
	AudioSource_set_spatialize_m55AE6DFEBBB2A15A1A5A1ECFF0B8CFD38927BD41,
	AudioSource_get_spatializePostEffects_m6AD56340718E536C0FD40A95CE582F90D99A31CC,
	AudioSource_set_spatializePostEffects_mDEC6094ED4000B2C69BA4105D0D2A3ABE32C005A,
	AudioSource_SetCustomCurve_m7F9FDB65C9A26D7491E041600157340A09552D56,
	AudioSource_GetCustomCurve_m71F1A8BB2AD0B3B46CF86F6D1A598B92C7CAEBA6,
	AudioSource_get_reverbZoneMix_m28603F3061FD472444AC7DD54DFD57F597A78A51,
	AudioSource_set_reverbZoneMix_m701BBB1343D14456A2959FBB5304A51DE1164795,
	AudioSource_get_bypassEffects_mBEF3FEFF96D5783F42C272CC080D9B92109CAD88,
	AudioSource_set_bypassEffects_m85076AE15C98565868F8501C96F0F8E4BA6D45C0,
	AudioSource_get_bypassListenerEffects_m78FAD90E57FE0BC9397F39D9F979074B7353EEE6,
	AudioSource_set_bypassListenerEffects_mA8A25D40D1DC6E822CD28A59B822C065BF26303E,
	AudioSource_get_bypassReverbZones_m5807F15A20AE9501F336AA56A3876D070F072C5E,
	AudioSource_set_bypassReverbZones_m681F9D84EE44CC7E57B49865D3B1E3898B7D684D,
	AudioSource_get_dopplerLevel_mF7181F2CE97541C9C3E499A9CDD77E963B9A1B94,
	AudioSource_set_dopplerLevel_m3E3187EA638992BE0A7AF9D660DAB1B0D088C2A0,
	AudioSource_get_spread_mD3174C0186177B225EF3E27FBA4FC7CDAB02D733,
	AudioSource_set_spread_m933BAB5C1122A350F18490CA208257F2ECAC5887,
	AudioSource_get_priority_m60F08C8F2D9DF0ED93CEB43F6DF6894873EF98FA,
	AudioSource_set_priority_m0A24AC2400304023BC732399F26709789D4C7ED2,
	AudioSource_get_mute_m87FF3468F096AE68C817B885D9AAC246CA4F1AFE,
	AudioSource_set_mute_m69E2DFCF261D2D187ED756096B7E0DE622292C71,
	AudioSource_get_minDistance_mA46B8982F6111835096E125A7162A5EAFAED31B1,
	AudioSource_set_minDistance_m9C1D3410330B09EACA69C64BF634018D71180F91,
	AudioSource_get_maxDistance_mBCCA59FDF624C4162AB4E4BA8B5642BADE7FF2FD,
	AudioSource_set_maxDistance_m8AF5831BC0B2D070B2D4E1B54CB27082240A5EA8,
	AudioSource_get_rolloffMode_mDE4AD9F22886B5709F5EAAC78176D15FA5478215,
	AudioSource_set_rolloffMode_mCEE552C06C70A8E4E64500C5792AD67D33782B4A,
	AudioSource_GetOutputData_mB236FC4E99BC03BFB9402FCC4140946DDFB0C849,
	AudioSource_GetSpectrumData_m534E9BDB8C2924A118A711A0F741581DCEB27266,
	AudioSource_SetSpatializerFloat_m9EEBFE4F23D91F670FC5C3E2A3EF1C6454C935FE,
	AudioSource_GetSpatializerFloat_m4563FA47677E3F0FD1F43B6951131FDD855D58BE,
	AudioSource_GetAmbisonicDecoderFloat_mDE10445884FDC97C0933D58944BE6C24166C1FD4,
	AudioSource_SetAmbisonicDecoderFloat_m7C55997736929F9B1208284F10313BC42DBF601B,
	AudioSource__ctor_m1CDE1D0F46156589602C00D42F65ADE562D230A8,
	Microphone_GetMicrophoneDeviceIDFromName_mB8F3723AFC4B9636BF3C1BDB532410223F6A9F51,
	Microphone_StartRecord_m9B9A119ED858D9AC4665EE378D36D3F19451460E,
	Microphone_EndRecord_m59704A5A11DD65930997572D419C9899B7AB9008,
	Microphone_IsRecording_m98F75D7466EDA4C8D149825E0C1F2AF04138F0A2,
	Microphone_GetRecordPosition_m5171F6E8F89D07235F7B500049749D536E1465F3,
	Microphone_Start_m2F9AD1061F64F1F569B00503955CF728C1B16D06,
	Microphone_End_mA8355F6CFDE340AE7177821B8544CA99CCE8071B,
	Microphone_get_devices_m4A9DF5F5C10340385BB060D5C0F4A6BCA19B05E5,
	Microphone_IsRecording_mFE8C01EC643515C463F5027CC3EA4FD5BDC9B0A4,
	Microphone_GetPosition_mAF3B9C0BAB393C2EE6337A916E86D24B7C69A4E1,
	AudioClipPlayable_GetHandle_mBEB846B088961170B6DB961951B511C11B98E0B8,
	AudioClipPlayable_Equals_m52ECDD49AE6BD8AB4C0AC83C417A0C1B23E3E55E,
	AudioMixer_SetFloat_m305579F01374620674AF66DA63DDD4BDBC9089CE,
	AudioMixer_GetFloat_m269CC0D94DFA272A6900F2A75AE94FF7B9D8E40C,
	AudioMixerGroup__ctor_m46817B3CE6F248F277E9F376B3806BF9136902EF,
	AudioMixerPlayable_GetHandle_m76EFC486A7639C4842F590F544B60988CF27BB17,
	AudioMixerPlayable_Equals_mB55D2602ACCD196F61AF3D1AE90B81930A9AB7E8,
	AudioSampleProvider_InvokeSampleFramesAvailable_mE6689CFA13C0621F305F389FEEE4D543B71BF236,
	AudioSampleProvider_InvokeSampleFramesOverflow_m998BEADD2A2B4BEF0906A31108B6DC486411CC78,
	SampleFramesHandler__ctor_m389B32B949592BFD1BA53D0C0983CA6B5BA6AAC7,
	SampleFramesHandler_Invoke_mCB6172CE3EF20C5E12A697A5CE5EEDED9A3B5779,
	SampleFramesHandler_BeginInvoke_m79D52B9AB6F6E72A9A9260F6608F7FA9F6A10ADC,
	SampleFramesHandler_EndInvoke_mC5357233A55B232AC8507FA4A04E06271EDE0309,
};
extern void AudioClipPlayable_GetHandle_mBEB846B088961170B6DB961951B511C11B98E0B8_AdjustorThunk (void);
extern void AudioClipPlayable_Equals_m52ECDD49AE6BD8AB4C0AC83C417A0C1B23E3E55E_AdjustorThunk (void);
extern void AudioMixerPlayable_GetHandle_m76EFC486A7639C4842F590F544B60988CF27BB17_AdjustorThunk (void);
extern void AudioMixerPlayable_Equals_mB55D2602ACCD196F61AF3D1AE90B81930A9AB7E8_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[4] = 
{
	{ 0x06000099, AudioClipPlayable_GetHandle_mBEB846B088961170B6DB961951B511C11B98E0B8_AdjustorThunk },
	{ 0x0600009A, AudioClipPlayable_Equals_m52ECDD49AE6BD8AB4C0AC83C417A0C1B23E3E55E_AdjustorThunk },
	{ 0x0600009E, AudioMixerPlayable_GetHandle_m76EFC486A7639C4842F590F544B60988CF27BB17_AdjustorThunk },
	{ 0x0600009F, AudioMixerPlayable_Equals_mB55D2602ACCD196F61AF3D1AE90B81930A9AB7E8_AdjustorThunk },
};
static const int32_t s_InvokerIndices[165] = 
{
	6208,
	6289,
	6289,
	6256,
	6256,
	2169,
	3276,
	1067,
	3348,
	6256,
	6208,
	6256,
	6208,
	6256,
	6289,
	6289,
	6289,
	3967,
	4632,
	4632,
	6270,
	3916,
	397,
	3951,
	3893,
	3893,
	3893,
	3893,
	3863,
	3863,
	3863,
	3863,
	3863,
	3893,
	1474,
	1474,
	4485,
	4273,
	4162,
	3348,
	3348,
	3348,
	3348,
	3348,
	3313,
	2169,
	3348,
	1129,
	3348,
	2169,
	3313,
	1096,
	3348,
	3967,
	6281,
	6228,
	6139,
	5838,
	5839,
	3301,
	5338,
	3276,
	5328,
	5595,
	5335,
	4983,
	3951,
	3380,
	3951,
	3380,
	3951,
	3380,
	3893,
	3313,
	3916,
	3348,
	3916,
	3348,
	3967,
	3393,
	3380,
	3301,
	3348,
	2179,
	3301,
	3301,
	3967,
	3967,
	3967,
	3863,
	3863,
	5841,
	5352,
	3863,
	3276,
	3863,
	3276,
	3863,
	3276,
	3863,
	3276,
	3893,
	3313,
	3951,
	3380,
	3951,
	3380,
	3863,
	3276,
	3863,
	3276,
	1981,
	3044,
	3951,
	3380,
	3863,
	3276,
	3863,
	3276,
	3863,
	3276,
	3951,
	3380,
	3951,
	3380,
	3893,
	3313,
	3863,
	3276,
	3951,
	3380,
	3951,
	3380,
	3893,
	3313,
	2167,
	1324,
	1454,
	1448,
	1448,
	1454,
	3967,
	6005,
	4797,
	6213,
	5920,
	6000,
	4810,
	6218,
	6270,
	5923,
	6005,
	3922,
	2524,
	1476,
	1471,
	3967,
	3922,
	2525,
	3313,
	3313,
	2169,
	2183,
	726,
	3348,
};
extern const CustomAttributesCacheGenerator g_UnityEngine_AudioModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_AudioModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_AudioModule_CodeGenModule = 
{
	"UnityEngine.AudioModule.dll",
	165,
	s_methodPointers,
	4,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_UnityEngine_AudioModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

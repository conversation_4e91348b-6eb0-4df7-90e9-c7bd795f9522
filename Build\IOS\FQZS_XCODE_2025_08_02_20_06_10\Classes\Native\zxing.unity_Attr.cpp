﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>
#include <stdint.h>



// System.Char[]
struct CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34;
// System.Reflection.AssemblyCompanyAttribute
struct AssemblyCompanyAttribute_t642AAB097D7DEAAB623BEBE4664327E9B01D1DE4;
// System.Reflection.AssemblyCopyrightAttribute
struct AssemblyCopyrightAttribute_tA6A09319EF50B48D962810032000DEE7B12904EC;
// System.Reflection.AssemblyDescriptionAttribute
struct AssemblyDescriptionAttribute_tF4460CCB289F6E2F71841792BBC7E6907DF612B3;
// System.Reflection.AssemblyFileVersionAttribute
struct AssemblyFileVersionAttribute_tCC1036D0566155DC5688D9230EF3C07D82A1896F;
// System.Reflection.AssemblyProductAttribute
struct AssemblyProductAttribute_t6BB0E0F76C752E14A4C26B4D1E230019068601CA;
// System.Reflection.AssemblyTitleAttribute
struct AssemblyTitleAttribute_tABB894D0792C7F307694CC796C8AE5D6A20382E7;
// System.Reflection.AssemblyTrademarkAttribute
struct AssemblyTrademarkAttribute_t0602679435F8EBECC5DDB55CFE3A7A4A4CA2B5E2;
// System.ComponentModel.BrowsableAttribute
struct BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2;
// System.CLSCompliantAttribute
struct CLSCompliantAttribute_tA28EF6D4ADBD3C5C429DE9A70DD1E927C8906249;
// System.Runtime.CompilerServices.CompilationRelaxationsAttribute
struct CompilationRelaxationsAttribute_t661FDDC06629BDA607A42BD660944F039FE03AFF;
// System.Runtime.CompilerServices.CompilerGeneratedAttribute
struct CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C;
// System.Diagnostics.DebuggableAttribute
struct DebuggableAttribute_tA8054EBD0FC7511695D494B690B5771658E3191B;
// System.Reflection.DefaultMemberAttribute
struct DefaultMemberAttribute_t8C9B3330DEA69EE364962477FF14FD2CFE30D4B5;
// System.FlagsAttribute
struct FlagsAttribute_t511C558FACEF1CC64702A8FAB67CAF3CBA65DF36;
// System.Runtime.InteropServices.GuidAttribute
struct GuidAttribute_tBB494B31270577CCD589ABBB159C18CDAE20D063;
// System.Runtime.CompilerServices.InternalsVisibleToAttribute
struct InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C;
// System.ParamArrayAttribute
struct ParamArrayAttribute_t9DCEB4CDDB8EDDB1124171D4C51FA6069EEA5C5F;
// System.Runtime.CompilerServices.RuntimeCompatibilityAttribute
struct RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80;
// System.String
struct String_t;



IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif

// System.Object


// System.Attribute
struct Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71  : public RuntimeObject
{
public:

public:
};


// System.String
struct String_t  : public RuntimeObject
{
public:
	// System.Int32 System.String::m_stringLength
	int32_t ___m_stringLength_0;
	// System.Char System.String::m_firstChar
	Il2CppChar ___m_firstChar_1;

public:
	inline static int32_t get_offset_of_m_stringLength_0() { return static_cast<int32_t>(offsetof(String_t, ___m_stringLength_0)); }
	inline int32_t get_m_stringLength_0() const { return ___m_stringLength_0; }
	inline int32_t* get_address_of_m_stringLength_0() { return &___m_stringLength_0; }
	inline void set_m_stringLength_0(int32_t value)
	{
		___m_stringLength_0 = value;
	}

	inline static int32_t get_offset_of_m_firstChar_1() { return static_cast<int32_t>(offsetof(String_t, ___m_firstChar_1)); }
	inline Il2CppChar get_m_firstChar_1() const { return ___m_firstChar_1; }
	inline Il2CppChar* get_address_of_m_firstChar_1() { return &___m_firstChar_1; }
	inline void set_m_firstChar_1(Il2CppChar value)
	{
		___m_firstChar_1 = value;
	}
};


// System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52  : public RuntimeObject
{
public:

public:
};

// Native definition for P/Invoke marshalling of System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshaled_com
{
};

// System.Reflection.AssemblyCompanyAttribute
struct AssemblyCompanyAttribute_t642AAB097D7DEAAB623BEBE4664327E9B01D1DE4  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.String System.Reflection.AssemblyCompanyAttribute::m_company
	String_t* ___m_company_0;

public:
	inline static int32_t get_offset_of_m_company_0() { return static_cast<int32_t>(offsetof(AssemblyCompanyAttribute_t642AAB097D7DEAAB623BEBE4664327E9B01D1DE4, ___m_company_0)); }
	inline String_t* get_m_company_0() const { return ___m_company_0; }
	inline String_t** get_address_of_m_company_0() { return &___m_company_0; }
	inline void set_m_company_0(String_t* value)
	{
		___m_company_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_company_0), (void*)value);
	}
};


// System.Reflection.AssemblyCopyrightAttribute
struct AssemblyCopyrightAttribute_tA6A09319EF50B48D962810032000DEE7B12904EC  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.String System.Reflection.AssemblyCopyrightAttribute::m_copyright
	String_t* ___m_copyright_0;

public:
	inline static int32_t get_offset_of_m_copyright_0() { return static_cast<int32_t>(offsetof(AssemblyCopyrightAttribute_tA6A09319EF50B48D962810032000DEE7B12904EC, ___m_copyright_0)); }
	inline String_t* get_m_copyright_0() const { return ___m_copyright_0; }
	inline String_t** get_address_of_m_copyright_0() { return &___m_copyright_0; }
	inline void set_m_copyright_0(String_t* value)
	{
		___m_copyright_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_copyright_0), (void*)value);
	}
};


// System.Reflection.AssemblyDescriptionAttribute
struct AssemblyDescriptionAttribute_tF4460CCB289F6E2F71841792BBC7E6907DF612B3  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.String System.Reflection.AssemblyDescriptionAttribute::m_description
	String_t* ___m_description_0;

public:
	inline static int32_t get_offset_of_m_description_0() { return static_cast<int32_t>(offsetof(AssemblyDescriptionAttribute_tF4460CCB289F6E2F71841792BBC7E6907DF612B3, ___m_description_0)); }
	inline String_t* get_m_description_0() const { return ___m_description_0; }
	inline String_t** get_address_of_m_description_0() { return &___m_description_0; }
	inline void set_m_description_0(String_t* value)
	{
		___m_description_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_description_0), (void*)value);
	}
};


// System.Reflection.AssemblyFileVersionAttribute
struct AssemblyFileVersionAttribute_tCC1036D0566155DC5688D9230EF3C07D82A1896F  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.String System.Reflection.AssemblyFileVersionAttribute::_version
	String_t* ____version_0;

public:
	inline static int32_t get_offset_of__version_0() { return static_cast<int32_t>(offsetof(AssemblyFileVersionAttribute_tCC1036D0566155DC5688D9230EF3C07D82A1896F, ____version_0)); }
	inline String_t* get__version_0() const { return ____version_0; }
	inline String_t** get_address_of__version_0() { return &____version_0; }
	inline void set__version_0(String_t* value)
	{
		____version_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____version_0), (void*)value);
	}
};


// System.Reflection.AssemblyProductAttribute
struct AssemblyProductAttribute_t6BB0E0F76C752E14A4C26B4D1E230019068601CA  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.String System.Reflection.AssemblyProductAttribute::m_product
	String_t* ___m_product_0;

public:
	inline static int32_t get_offset_of_m_product_0() { return static_cast<int32_t>(offsetof(AssemblyProductAttribute_t6BB0E0F76C752E14A4C26B4D1E230019068601CA, ___m_product_0)); }
	inline String_t* get_m_product_0() const { return ___m_product_0; }
	inline String_t** get_address_of_m_product_0() { return &___m_product_0; }
	inline void set_m_product_0(String_t* value)
	{
		___m_product_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_product_0), (void*)value);
	}
};


// System.Reflection.AssemblyTitleAttribute
struct AssemblyTitleAttribute_tABB894D0792C7F307694CC796C8AE5D6A20382E7  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.String System.Reflection.AssemblyTitleAttribute::m_title
	String_t* ___m_title_0;

public:
	inline static int32_t get_offset_of_m_title_0() { return static_cast<int32_t>(offsetof(AssemblyTitleAttribute_tABB894D0792C7F307694CC796C8AE5D6A20382E7, ___m_title_0)); }
	inline String_t* get_m_title_0() const { return ___m_title_0; }
	inline String_t** get_address_of_m_title_0() { return &___m_title_0; }
	inline void set_m_title_0(String_t* value)
	{
		___m_title_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_title_0), (void*)value);
	}
};


// System.Reflection.AssemblyTrademarkAttribute
struct AssemblyTrademarkAttribute_t0602679435F8EBECC5DDB55CFE3A7A4A4CA2B5E2  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.String System.Reflection.AssemblyTrademarkAttribute::m_trademark
	String_t* ___m_trademark_0;

public:
	inline static int32_t get_offset_of_m_trademark_0() { return static_cast<int32_t>(offsetof(AssemblyTrademarkAttribute_t0602679435F8EBECC5DDB55CFE3A7A4A4CA2B5E2, ___m_trademark_0)); }
	inline String_t* get_m_trademark_0() const { return ___m_trademark_0; }
	inline String_t** get_address_of_m_trademark_0() { return &___m_trademark_0; }
	inline void set_m_trademark_0(String_t* value)
	{
		___m_trademark_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_trademark_0), (void*)value);
	}
};


// System.Boolean
struct Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37 
{
public:
	// System.Boolean System.Boolean::m_value
	bool ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37, ___m_value_0)); }
	inline bool get_m_value_0() const { return ___m_value_0; }
	inline bool* get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(bool value)
	{
		___m_value_0 = value;
	}
};


// System.ComponentModel.BrowsableAttribute
struct BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.Boolean System.ComponentModel.BrowsableAttribute::browsable
	bool ___browsable_3;

public:
	inline static int32_t get_offset_of_browsable_3() { return static_cast<int32_t>(offsetof(BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2, ___browsable_3)); }
	inline bool get_browsable_3() const { return ___browsable_3; }
	inline bool* get_address_of_browsable_3() { return &___browsable_3; }
	inline void set_browsable_3(bool value)
	{
		___browsable_3 = value;
	}
};


// System.CLSCompliantAttribute
struct CLSCompliantAttribute_tA28EF6D4ADBD3C5C429DE9A70DD1E927C8906249  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.Boolean System.CLSCompliantAttribute::m_compliant
	bool ___m_compliant_0;

public:
	inline static int32_t get_offset_of_m_compliant_0() { return static_cast<int32_t>(offsetof(CLSCompliantAttribute_tA28EF6D4ADBD3C5C429DE9A70DD1E927C8906249, ___m_compliant_0)); }
	inline bool get_m_compliant_0() const { return ___m_compliant_0; }
	inline bool* get_address_of_m_compliant_0() { return &___m_compliant_0; }
	inline void set_m_compliant_0(bool value)
	{
		___m_compliant_0 = value;
	}
};


// System.Runtime.CompilerServices.CompilationRelaxationsAttribute
struct CompilationRelaxationsAttribute_t661FDDC06629BDA607A42BD660944F039FE03AFF  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.Int32 System.Runtime.CompilerServices.CompilationRelaxationsAttribute::m_relaxations
	int32_t ___m_relaxations_0;

public:
	inline static int32_t get_offset_of_m_relaxations_0() { return static_cast<int32_t>(offsetof(CompilationRelaxationsAttribute_t661FDDC06629BDA607A42BD660944F039FE03AFF, ___m_relaxations_0)); }
	inline int32_t get_m_relaxations_0() const { return ___m_relaxations_0; }
	inline int32_t* get_address_of_m_relaxations_0() { return &___m_relaxations_0; }
	inline void set_m_relaxations_0(int32_t value)
	{
		___m_relaxations_0 = value;
	}
};


// System.Runtime.CompilerServices.CompilerGeneratedAttribute
struct CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:

public:
};


// System.Reflection.DefaultMemberAttribute
struct DefaultMemberAttribute_t8C9B3330DEA69EE364962477FF14FD2CFE30D4B5  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.String System.Reflection.DefaultMemberAttribute::m_memberName
	String_t* ___m_memberName_0;

public:
	inline static int32_t get_offset_of_m_memberName_0() { return static_cast<int32_t>(offsetof(DefaultMemberAttribute_t8C9B3330DEA69EE364962477FF14FD2CFE30D4B5, ___m_memberName_0)); }
	inline String_t* get_m_memberName_0() const { return ___m_memberName_0; }
	inline String_t** get_address_of_m_memberName_0() { return &___m_memberName_0; }
	inline void set_m_memberName_0(String_t* value)
	{
		___m_memberName_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_memberName_0), (void*)value);
	}
};


// System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA  : public ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52
{
public:

public:
};

// Native definition for P/Invoke marshalling of System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshaled_com
{
};

// System.FlagsAttribute
struct FlagsAttribute_t511C558FACEF1CC64702A8FAB67CAF3CBA65DF36  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:

public:
};


// System.Runtime.InteropServices.GuidAttribute
struct GuidAttribute_tBB494B31270577CCD589ABBB159C18CDAE20D063  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.String System.Runtime.InteropServices.GuidAttribute::_val
	String_t* ____val_0;

public:
	inline static int32_t get_offset_of__val_0() { return static_cast<int32_t>(offsetof(GuidAttribute_tBB494B31270577CCD589ABBB159C18CDAE20D063, ____val_0)); }
	inline String_t* get__val_0() const { return ____val_0; }
	inline String_t** get_address_of__val_0() { return &____val_0; }
	inline void set__val_0(String_t* value)
	{
		____val_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____val_0), (void*)value);
	}
};


// System.Runtime.CompilerServices.InternalsVisibleToAttribute
struct InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.String System.Runtime.CompilerServices.InternalsVisibleToAttribute::_assemblyName
	String_t* ____assemblyName_0;
	// System.Boolean System.Runtime.CompilerServices.InternalsVisibleToAttribute::_allInternalsVisible
	bool ____allInternalsVisible_1;

public:
	inline static int32_t get_offset_of__assemblyName_0() { return static_cast<int32_t>(offsetof(InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C, ____assemblyName_0)); }
	inline String_t* get__assemblyName_0() const { return ____assemblyName_0; }
	inline String_t** get_address_of__assemblyName_0() { return &____assemblyName_0; }
	inline void set__assemblyName_0(String_t* value)
	{
		____assemblyName_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____assemblyName_0), (void*)value);
	}

	inline static int32_t get_offset_of__allInternalsVisible_1() { return static_cast<int32_t>(offsetof(InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C, ____allInternalsVisible_1)); }
	inline bool get__allInternalsVisible_1() const { return ____allInternalsVisible_1; }
	inline bool* get_address_of__allInternalsVisible_1() { return &____allInternalsVisible_1; }
	inline void set__allInternalsVisible_1(bool value)
	{
		____allInternalsVisible_1 = value;
	}
};


// System.ParamArrayAttribute
struct ParamArrayAttribute_t9DCEB4CDDB8EDDB1124171D4C51FA6069EEA5C5F  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:

public:
};


// System.Runtime.CompilerServices.RuntimeCompatibilityAttribute
struct RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.Boolean System.Runtime.CompilerServices.RuntimeCompatibilityAttribute::m_wrapNonExceptionThrows
	bool ___m_wrapNonExceptionThrows_0;

public:
	inline static int32_t get_offset_of_m_wrapNonExceptionThrows_0() { return static_cast<int32_t>(offsetof(RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80, ___m_wrapNonExceptionThrows_0)); }
	inline bool get_m_wrapNonExceptionThrows_0() const { return ___m_wrapNonExceptionThrows_0; }
	inline bool* get_address_of_m_wrapNonExceptionThrows_0() { return &___m_wrapNonExceptionThrows_0; }
	inline void set_m_wrapNonExceptionThrows_0(bool value)
	{
		___m_wrapNonExceptionThrows_0 = value;
	}
};


// System.Void
struct Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5 
{
public:
	union
	{
		struct
		{
		};
		uint8_t Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5__padding[1];
	};

public:
};


// System.Diagnostics.DebuggableAttribute/DebuggingModes
struct DebuggingModes_t279D5B9C012ABA935887CB73C5A63A1F46AF08A8 
{
public:
	// System.Int32 System.Diagnostics.DebuggableAttribute/DebuggingModes::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(DebuggingModes_t279D5B9C012ABA935887CB73C5A63A1F46AF08A8, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// System.Diagnostics.DebuggableAttribute
struct DebuggableAttribute_tA8054EBD0FC7511695D494B690B5771658E3191B  : public Attribute_t037CA9D9F3B742C063DB364D2EEBBF9FC5772C71
{
public:
	// System.Diagnostics.DebuggableAttribute/DebuggingModes System.Diagnostics.DebuggableAttribute::m_debuggingModes
	int32_t ___m_debuggingModes_0;

public:
	inline static int32_t get_offset_of_m_debuggingModes_0() { return static_cast<int32_t>(offsetof(DebuggableAttribute_tA8054EBD0FC7511695D494B690B5771658E3191B, ___m_debuggingModes_0)); }
	inline int32_t get_m_debuggingModes_0() const { return ___m_debuggingModes_0; }
	inline int32_t* get_address_of_m_debuggingModes_0() { return &___m_debuggingModes_0; }
	inline void set_m_debuggingModes_0(int32_t value)
	{
		___m_debuggingModes_0 = value;
	}
};


// System.Object


// System.Object


// System.Attribute


// System.Attribute


// System.String

struct String_t_StaticFields
{
public:
	// System.String System.String::Empty
	String_t* ___Empty_5;

public:
	inline static int32_t get_offset_of_Empty_5() { return static_cast<int32_t>(offsetof(String_t_StaticFields, ___Empty_5)); }
	inline String_t* get_Empty_5() const { return ___Empty_5; }
	inline String_t** get_address_of_Empty_5() { return &___Empty_5; }
	inline void set_Empty_5(String_t* value)
	{
		___Empty_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Empty_5), (void*)value);
	}
};


// System.String


// System.ValueType


// System.ValueType


// System.Reflection.AssemblyCompanyAttribute


// System.Reflection.AssemblyCompanyAttribute


// System.Reflection.AssemblyCopyrightAttribute


// System.Reflection.AssemblyCopyrightAttribute


// System.Reflection.AssemblyDescriptionAttribute


// System.Reflection.AssemblyDescriptionAttribute


// System.Reflection.AssemblyFileVersionAttribute


// System.Reflection.AssemblyFileVersionAttribute


// System.Reflection.AssemblyProductAttribute


// System.Reflection.AssemblyProductAttribute


// System.Reflection.AssemblyTitleAttribute


// System.Reflection.AssemblyTitleAttribute


// System.Reflection.AssemblyTrademarkAttribute


// System.Reflection.AssemblyTrademarkAttribute


// System.Boolean

struct Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields
{
public:
	// System.String System.Boolean::TrueString
	String_t* ___TrueString_5;
	// System.String System.Boolean::FalseString
	String_t* ___FalseString_6;

public:
	inline static int32_t get_offset_of_TrueString_5() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields, ___TrueString_5)); }
	inline String_t* get_TrueString_5() const { return ___TrueString_5; }
	inline String_t** get_address_of_TrueString_5() { return &___TrueString_5; }
	inline void set_TrueString_5(String_t* value)
	{
		___TrueString_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___TrueString_5), (void*)value);
	}

	inline static int32_t get_offset_of_FalseString_6() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields, ___FalseString_6)); }
	inline String_t* get_FalseString_6() const { return ___FalseString_6; }
	inline String_t** get_address_of_FalseString_6() { return &___FalseString_6; }
	inline void set_FalseString_6(String_t* value)
	{
		___FalseString_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___FalseString_6), (void*)value);
	}
};


// System.Boolean


// System.ComponentModel.BrowsableAttribute

struct BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2_StaticFields
{
public:
	// System.ComponentModel.BrowsableAttribute System.ComponentModel.BrowsableAttribute::Yes
	BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2 * ___Yes_0;
	// System.ComponentModel.BrowsableAttribute System.ComponentModel.BrowsableAttribute::No
	BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2 * ___No_1;
	// System.ComponentModel.BrowsableAttribute System.ComponentModel.BrowsableAttribute::Default
	BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2 * ___Default_2;

public:
	inline static int32_t get_offset_of_Yes_0() { return static_cast<int32_t>(offsetof(BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2_StaticFields, ___Yes_0)); }
	inline BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2 * get_Yes_0() const { return ___Yes_0; }
	inline BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2 ** get_address_of_Yes_0() { return &___Yes_0; }
	inline void set_Yes_0(BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2 * value)
	{
		___Yes_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Yes_0), (void*)value);
	}

	inline static int32_t get_offset_of_No_1() { return static_cast<int32_t>(offsetof(BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2_StaticFields, ___No_1)); }
	inline BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2 * get_No_1() const { return ___No_1; }
	inline BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2 ** get_address_of_No_1() { return &___No_1; }
	inline void set_No_1(BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2 * value)
	{
		___No_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___No_1), (void*)value);
	}

	inline static int32_t get_offset_of_Default_2() { return static_cast<int32_t>(offsetof(BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2_StaticFields, ___Default_2)); }
	inline BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2 * get_Default_2() const { return ___Default_2; }
	inline BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2 ** get_address_of_Default_2() { return &___Default_2; }
	inline void set_Default_2(BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2 * value)
	{
		___Default_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Default_2), (void*)value);
	}
};


// System.ComponentModel.BrowsableAttribute


// System.CLSCompliantAttribute


// System.CLSCompliantAttribute


// System.Runtime.CompilerServices.CompilationRelaxationsAttribute


// System.Runtime.CompilerServices.CompilationRelaxationsAttribute


// System.Runtime.CompilerServices.CompilerGeneratedAttribute


// System.Runtime.CompilerServices.CompilerGeneratedAttribute


// System.Reflection.DefaultMemberAttribute


// System.Reflection.DefaultMemberAttribute


// System.Enum

struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_StaticFields
{
public:
	// System.Char[] System.Enum::enumSeperatorCharArray
	CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* ___enumSeperatorCharArray_0;

public:
	inline static int32_t get_offset_of_enumSeperatorCharArray_0() { return static_cast<int32_t>(offsetof(Enum_t23B90B40F60E677A8025267341651C94AE079CDA_StaticFields, ___enumSeperatorCharArray_0)); }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* get_enumSeperatorCharArray_0() const { return ___enumSeperatorCharArray_0; }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34** get_address_of_enumSeperatorCharArray_0() { return &___enumSeperatorCharArray_0; }
	inline void set_enumSeperatorCharArray_0(CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* value)
	{
		___enumSeperatorCharArray_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___enumSeperatorCharArray_0), (void*)value);
	}
};


// System.Enum


// System.FlagsAttribute


// System.FlagsAttribute


// System.Runtime.InteropServices.GuidAttribute


// System.Runtime.InteropServices.GuidAttribute


// System.Runtime.CompilerServices.InternalsVisibleToAttribute


// System.Runtime.CompilerServices.InternalsVisibleToAttribute


// System.ParamArrayAttribute


// System.ParamArrayAttribute


// System.Runtime.CompilerServices.RuntimeCompatibilityAttribute


// System.Runtime.CompilerServices.RuntimeCompatibilityAttribute


// System.Void


// System.Void


// System.Diagnostics.DebuggableAttribute/DebuggingModes


// System.Diagnostics.DebuggableAttribute/DebuggingModes


// System.Diagnostics.DebuggableAttribute


// System.Diagnostics.DebuggableAttribute

#ifdef __clang__
#pragma clang diagnostic pop
#endif



// System.Void System.Runtime.CompilerServices.InternalsVisibleToAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InternalsVisibleToAttribute__ctor_m420071A75DCEEC72356490C64B4B0B9270DA32B9 (InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C * __this, String_t* ___assemblyName0, const RuntimeMethod* method);
// System.Void System.CLSCompliantAttribute::.ctor(System.Boolean)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CLSCompliantAttribute__ctor_m340EDA4DA5E45506AD631FE84241ADFB6B3F0270 (CLSCompliantAttribute_tA28EF6D4ADBD3C5C429DE9A70DD1E927C8906249 * __this, bool ___isCompliant0, const RuntimeMethod* method);
// System.Void System.Reflection.AssemblyFileVersionAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssemblyFileVersionAttribute__ctor_mF855AEBC51CB72F4FF913499256741AE57B0F13D (AssemblyFileVersionAttribute_tCC1036D0566155DC5688D9230EF3C07D82A1896F * __this, String_t* ___version0, const RuntimeMethod* method);
// System.Void System.Reflection.AssemblyCompanyAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssemblyCompanyAttribute__ctor_m435C9FEC405646617645636E67860598A0C46FF0 (AssemblyCompanyAttribute_t642AAB097D7DEAAB623BEBE4664327E9B01D1DE4 * __this, String_t* ___company0, const RuntimeMethod* method);
// System.Void System.Reflection.AssemblyTrademarkAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssemblyTrademarkAttribute__ctor_m6FBD5AAE48F00120043AD8BECF2586896CFB6C02 (AssemblyTrademarkAttribute_t0602679435F8EBECC5DDB55CFE3A7A4A4CA2B5E2 * __this, String_t* ___trademark0, const RuntimeMethod* method);
// System.Void System.Reflection.AssemblyCopyrightAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssemblyCopyrightAttribute__ctor_mB0B5F5C1A7A8B172289CC694E2711F07A37CE3F3 (AssemblyCopyrightAttribute_tA6A09319EF50B48D962810032000DEE7B12904EC * __this, String_t* ___copyright0, const RuntimeMethod* method);
// System.Void System.Reflection.AssemblyProductAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssemblyProductAttribute__ctor_m26DF1EBC1C86E7DA4786C66B44123899BE8DBCB8 (AssemblyProductAttribute_t6BB0E0F76C752E14A4C26B4D1E230019068601CA * __this, String_t* ___product0, const RuntimeMethod* method);
// System.Void System.Runtime.InteropServices.GuidAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GuidAttribute__ctor_mCCEF3938DF601B23B5791CEE8F7AF05C98B6AFEA (GuidAttribute_tBB494B31270577CCD589ABBB159C18CDAE20D063 * __this, String_t* ___guid0, const RuntimeMethod* method);
// System.Void System.Runtime.CompilerServices.RuntimeCompatibilityAttribute::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RuntimeCompatibilityAttribute__ctor_m551DDF1438CE97A984571949723F30F44CF7317C (RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80 * __this, const RuntimeMethod* method);
// System.Void System.Runtime.CompilerServices.RuntimeCompatibilityAttribute::set_WrapNonExceptionThrows(System.Boolean)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void RuntimeCompatibilityAttribute_set_WrapNonExceptionThrows_m8562196F90F3EBCEC23B5708EE0332842883C490_inline (RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80 * __this, bool ___value0, const RuntimeMethod* method);
// System.Void System.Runtime.CompilerServices.CompilationRelaxationsAttribute::.ctor(System.Int32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CompilationRelaxationsAttribute__ctor_mAC3079EBC4EEAB474EED8208EF95DB39C922333B (CompilationRelaxationsAttribute_t661FDDC06629BDA607A42BD660944F039FE03AFF * __this, int32_t ___relaxations0, const RuntimeMethod* method);
// System.Void System.Reflection.AssemblyDescriptionAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssemblyDescriptionAttribute__ctor_m3A0BD500FF352A67235FBA499FBA58EFF15B1F25 (AssemblyDescriptionAttribute_tF4460CCB289F6E2F71841792BBC7E6907DF612B3 * __this, String_t* ___description0, const RuntimeMethod* method);
// System.Void System.Reflection.AssemblyTitleAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssemblyTitleAttribute__ctor_mE239F206B3B369C48AE1F3B4211688778FE99E8D (AssemblyTitleAttribute_tABB894D0792C7F307694CC796C8AE5D6A20382E7 * __this, String_t* ___title0, const RuntimeMethod* method);
// System.Void System.Diagnostics.DebuggableAttribute::.ctor(System.Diagnostics.DebuggableAttribute/DebuggingModes)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DebuggableAttribute__ctor_m7FF445C8435494A4847123A668D889E692E55550 (DebuggableAttribute_tA8054EBD0FC7511695D494B690B5771658E3191B * __this, int32_t ___modes0, const RuntimeMethod* method);
// System.Void System.Runtime.CompilerServices.CompilerGeneratedAttribute::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35 (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * __this, const RuntimeMethod* method);
// System.Void System.Reflection.DefaultMemberAttribute::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DefaultMemberAttribute__ctor_mA025B6F5B3A9292696E01108027840C8DFF7F4D7 (DefaultMemberAttribute_t8C9B3330DEA69EE364962477FF14FD2CFE30D4B5 * __this, String_t* ___memberName0, const RuntimeMethod* method);
// System.Void System.ComponentModel.BrowsableAttribute::.ctor(System.Boolean)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BrowsableAttribute__ctor_m67F6E4C4B28C23E90D4C0527D1BA41555D1B0DDE (BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2 * __this, bool ___browsable0, const RuntimeMethod* method);
// System.Void System.ParamArrayAttribute::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ParamArrayAttribute__ctor_mCC72AFF718185BA7B87FD8D9471F1274400C5719 (ParamArrayAttribute_t9DCEB4CDDB8EDDB1124171D4C51FA6069EEA5C5F * __this, const RuntimeMethod* method);
// System.Void System.FlagsAttribute::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlagsAttribute__ctor_mE8DCBA1BE0E6B0424FEF5E5F249733CF6A0E1229 (FlagsAttribute_t511C558FACEF1CC64702A8FAB67CAF3CBA65DF36 * __this, const RuntimeMethod* method);
static void zxing_unity_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C * tmp = (InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C *)cache->attributes[0];
		InternalsVisibleToAttribute__ctor_m420071A75DCEEC72356490C64B4B0B9270DA32B9(tmp, il2cpp_codegen_string_new_wrapper("\x7A\x78\x69\x6E\x67\x2E\x73\x6C\x34\x2E\x74\x65\x73\x74\x2C\x20\x50\x75\x62\x6C\x69\x63\x4B\x65\x79\x3D\x30\x30\x32\x34\x30\x30\x30\x30\x30\x34\x38\x30\x30\x30\x30\x30\x31\x34\x30\x31\x30\x30\x30\x30\x30\x36\x30\x32\x30\x30\x30\x30\x30\x30\x32\x34\x30\x30\x30\x30\x35\x32\x35\x33\x34\x31\x33\x31\x30\x30\x30\x38\x30\x30\x30\x30\x30\x31\x30\x30\x30\x31\x30\x30\x30\x31\x34\x63\x39\x61\x30\x31\x39\x35\x36\x66\x31\x33\x61\x33\x33\x39\x31\x33\x30\x36\x31\x36\x34\x37\x33\x66\x36\x39\x66\x39\x37\x35\x65\x30\x38\x36\x64\x39\x61\x33\x61\x35\x36\x32\x37\x38\x39\x33\x36\x62\x31\x32\x63\x34\x38\x63\x61\x34\x35\x61\x34\x64\x64\x66\x65\x65\x30\x35\x63\x32\x31\x63\x64\x63\x32\x32\x61\x65\x64\x64\x38\x34\x65\x39\x34\x36\x38\x32\x38\x33\x31\x32\x37\x61\x32\x30\x62\x62\x61\x34\x37\x36\x31\x63\x34\x65\x30\x64\x39\x38\x33\x36\x36\x32\x33\x66\x63\x39\x39\x31\x64\x35\x36\x32\x61\x35\x30\x38\x38\x34\x35\x66\x65\x33\x31\x34\x61\x34\x33\x35\x62\x64\x36\x63\x36\x66\x66\x34\x62\x30\x62\x31\x64\x37\x61\x31\x34\x31\x65\x66\x39\x33\x64\x63\x31\x63\x36\x32\x32\x35\x32\x34\x33\x38\x37\x32\x33\x66\x30\x66\x39\x33\x36\x36\x38\x32\x38\x38\x36\x37\x33\x65\x61\x36\x30\x34\x32\x65\x35\x38\x33\x62\x30\x65\x65\x64\x30\x34\x30\x65\x33\x36\x37\x33\x61\x63\x61\x35\x38\x34\x66\x39\x36\x64\x34\x64\x63\x61\x31\x39\x39\x33\x37\x66\x62\x65\x64\x33\x30\x65\x36\x63\x64\x33\x63\x30\x34\x30\x39\x64\x62\x38\x32\x64\x35\x63\x35\x64\x32\x30\x36\x37\x37\x31\x30\x64\x38\x64\x38\x36\x65\x30\x30\x38\x34\x34\x37\x32\x30\x31\x64\x39\x39\x32\x33\x38\x62\x39\x34\x64\x39\x31\x31\x37\x31\x62\x62\x30\x65\x64\x66\x33\x65\x38\x35\x34\x39\x38\x35\x36\x39\x33\x30\x35\x31\x62\x61\x35\x31\x36\x37\x63\x61\x36\x61\x65\x36\x35\x30\x61\x63\x61\x35\x64\x64\x36\x35\x34\x37\x31\x64\x36\x38\x38\x33\x35\x64\x62\x30\x30\x63\x65\x31\x37\x32\x38\x63\x35\x38\x63\x37\x62\x62\x66\x39\x61\x35\x64\x31\x35\x32\x66\x34\x39\x31\x31\x32\x33\x63\x61\x66\x39\x63\x30\x66\x36\x38\x36\x64\x63\x34\x65\x34\x38\x65\x31\x65\x66\x36\x33\x65\x61\x66\x37\x33\x38\x61\x31\x32\x62\x33\x37\x37\x31\x63\x32\x34\x64\x35\x39\x35\x63\x63\x35\x61\x35\x62\x35\x64\x61\x66\x32\x63\x63\x37\x36\x31\x31\x37\x35\x36\x65\x39\x62\x61\x33\x63\x63\x38\x39\x66\x30\x38\x66\x62\x39\x61\x64\x66\x33\x39\x36\x38\x35\x62\x64\x35\x33\x35\x36\x38\x35\x38\x63\x30\x31\x30\x65\x62\x39\x61\x61\x38\x61\x37\x36\x37\x65\x35\x65\x66\x30\x32\x30\x34\x30\x38\x65\x30\x63\x39\x37\x34\x36\x63\x62\x62\x35\x61\x38"), NULL);
	}
	{
		CLSCompliantAttribute_tA28EF6D4ADBD3C5C429DE9A70DD1E927C8906249 * tmp = (CLSCompliantAttribute_tA28EF6D4ADBD3C5C429DE9A70DD1E927C8906249 *)cache->attributes[1];
		CLSCompliantAttribute__ctor_m340EDA4DA5E45506AD631FE84241ADFB6B3F0270(tmp, true, NULL);
	}
	{
		AssemblyFileVersionAttribute_tCC1036D0566155DC5688D9230EF3C07D82A1896F * tmp = (AssemblyFileVersionAttribute_tCC1036D0566155DC5688D9230EF3C07D82A1896F *)cache->attributes[2];
		AssemblyFileVersionAttribute__ctor_mF855AEBC51CB72F4FF913499256741AE57B0F13D(tmp, il2cpp_codegen_string_new_wrapper("\x30\x2E\x31\x35\x2E\x30\x2E\x30"), NULL);
	}
	{
		AssemblyCompanyAttribute_t642AAB097D7DEAAB623BEBE4664327E9B01D1DE4 * tmp = (AssemblyCompanyAttribute_t642AAB097D7DEAAB623BEBE4664327E9B01D1DE4 *)cache->attributes[3];
		AssemblyCompanyAttribute__ctor_m435C9FEC405646617645636E67860598A0C46FF0(tmp, il2cpp_codegen_string_new_wrapper("\x5A\x58\x69\x6E\x67\x2E\x4E\x65\x74\x20\x44\x65\x76\x65\x6C\x6F\x70\x6D\x65\x6E\x74"), NULL);
	}
	{
		AssemblyTrademarkAttribute_t0602679435F8EBECC5DDB55CFE3A7A4A4CA2B5E2 * tmp = (AssemblyTrademarkAttribute_t0602679435F8EBECC5DDB55CFE3A7A4A4CA2B5E2 *)cache->attributes[4];
		AssemblyTrademarkAttribute__ctor_m6FBD5AAE48F00120043AD8BECF2586896CFB6C02(tmp, il2cpp_codegen_string_new_wrapper(""), NULL);
	}
	{
		AssemblyCopyrightAttribute_tA6A09319EF50B48D962810032000DEE7B12904EC * tmp = (AssemblyCopyrightAttribute_tA6A09319EF50B48D962810032000DEE7B12904EC *)cache->attributes[5];
		AssemblyCopyrightAttribute__ctor_mB0B5F5C1A7A8B172289CC694E2711F07A37CE3F3(tmp, il2cpp_codegen_string_new_wrapper("\x43\x6F\x70\x79\x72\x69\x67\x68\x74\x20\xC2\xA9\x20\x32\x30\x31\x32\x2D\x32\x30\x31\x37"), NULL);
	}
	{
		AssemblyProductAttribute_t6BB0E0F76C752E14A4C26B4D1E230019068601CA * tmp = (AssemblyProductAttribute_t6BB0E0F76C752E14A4C26B4D1E230019068601CA *)cache->attributes[6];
		AssemblyProductAttribute__ctor_m26DF1EBC1C86E7DA4786C66B44123899BE8DBCB8(tmp, il2cpp_codegen_string_new_wrapper("\x5A\x58\x69\x6E\x67\x2E\x4E\x65\x74"), NULL);
	}
	{
		InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C * tmp = (InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C *)cache->attributes[7];
		InternalsVisibleToAttribute__ctor_m420071A75DCEEC72356490C64B4B0B9270DA32B9(tmp, il2cpp_codegen_string_new_wrapper("\x7A\x78\x69\x6E\x67\x2E\x74\x65\x73\x74\x2C\x20\x50\x75\x62\x6C\x69\x63\x4B\x65\x79\x3D\x30\x30\x32\x34\x30\x30\x30\x30\x30\x34\x38\x30\x30\x30\x30\x30\x39\x34\x30\x30\x30\x30\x30\x30\x30\x36\x30\x32\x30\x30\x30\x30\x30\x30\x32\x34\x30\x30\x30\x30\x35\x32\x35\x33\x34\x31\x33\x31\x30\x30\x30\x34\x30\x30\x30\x30\x30\x31\x30\x30\x30\x31\x30\x30\x34\x35\x66\x62\x34\x61\x63\x62\x39\x35\x31\x63\x64\x35\x65\x33\x32\x61\x65\x39\x65\x35\x33\x61\x38\x32\x62\x31\x65\x36\x32\x62\x30\x32\x39\x34\x39\x35\x37\x62\x37\x33\x37\x34\x36\x31\x31\x38\x65\x61\x62\x30\x64\x39\x34\x65\x34\x39\x61\x35\x34\x36\x33\x66\x34\x39\x39\x34\x62\x30\x36\x30\x34\x33\x31\x62\x36\x33\x38\x39\x64\x66\x66\x31\x33\x38\x64\x63\x31\x62\x36\x39\x33\x32\x30\x30\x36\x62\x65\x32\x66\x65\x30\x30\x37\x61\x38\x32\x31\x37\x36\x32\x36\x31\x65\x64\x38\x38\x64\x33\x64\x39\x64\x64\x64\x64\x30\x65\x65\x35\x36\x38\x39\x38\x38\x62\x62\x39\x36\x66\x33\x36\x36\x37\x61\x61\x64\x38\x34\x63\x32\x62\x65\x65\x31\x33\x39\x35\x66\x39\x65\x63\x32\x33\x63\x66\x36\x31\x64\x64\x66\x35\x61\x37\x63\x61\x35\x34\x30\x65\x33\x34\x62\x34\x61\x32\x65\x34\x31\x39\x32\x30\x63\x36\x61\x32\x30\x34\x64\x33\x39\x63\x66\x37\x38\x65\x62\x66\x37\x64\x34\x33\x33\x31\x63\x38\x32\x33\x61\x39\x36\x63\x62\x33\x36\x61\x31\x64\x35\x62\x63\x31\x61\x38\x63\x36\x36\x36\x64\x62\x31\x65\x64\x65\x66\x34\x61\x62\x36\x37\x63\x31\x36\x37\x64\x63"), NULL);
	}
	{
		GuidAttribute_tBB494B31270577CCD589ABBB159C18CDAE20D063 * tmp = (GuidAttribute_tBB494B31270577CCD589ABBB159C18CDAE20D063 *)cache->attributes[8];
		GuidAttribute__ctor_mCCEF3938DF601B23B5791CEE8F7AF05C98B6AFEA(tmp, il2cpp_codegen_string_new_wrapper("\x45\x43\x45\x33\x41\x42\x37\x34\x2D\x39\x44\x44\x31\x2D\x34\x43\x46\x42\x2D\x39\x44\x34\x38\x2D\x46\x43\x42\x46\x42\x33\x30\x45\x30\x36\x44\x36"), NULL);
	}
	{
		RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80 * tmp = (RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80 *)cache->attributes[9];
		RuntimeCompatibilityAttribute__ctor_m551DDF1438CE97A984571949723F30F44CF7317C(tmp, NULL);
		RuntimeCompatibilityAttribute_set_WrapNonExceptionThrows_m8562196F90F3EBCEC23B5708EE0332842883C490_inline(tmp, true, NULL);
	}
	{
		CompilationRelaxationsAttribute_t661FDDC06629BDA607A42BD660944F039FE03AFF * tmp = (CompilationRelaxationsAttribute_t661FDDC06629BDA607A42BD660944F039FE03AFF *)cache->attributes[10];
		CompilationRelaxationsAttribute__ctor_mAC3079EBC4EEAB474EED8208EF95DB39C922333B(tmp, 8LL, NULL);
	}
	{
		InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C * tmp = (InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C *)cache->attributes[11];
		InternalsVisibleToAttribute__ctor_m420071A75DCEEC72356490C64B4B0B9270DA32B9(tmp, il2cpp_codegen_string_new_wrapper("\x7A\x78\x69\x6E\x67\x2E\x73\x6C\x34\x2E\x74\x65\x73\x74\x2C\x20\x50\x75\x62\x6C\x69\x63\x4B\x65\x79\x3D\x30\x30\x32\x34\x30\x30\x30\x30\x30\x34\x38\x30\x30\x30\x30\x30\x39\x34\x30\x30\x30\x30\x30\x30\x30\x36\x30\x32\x30\x30\x30\x30\x30\x30\x32\x34\x30\x30\x30\x30\x35\x32\x35\x33\x34\x31\x33\x31\x30\x30\x30\x34\x30\x30\x30\x30\x30\x31\x30\x30\x30\x31\x30\x30\x34\x35\x66\x62\x34\x61\x63\x62\x39\x35\x31\x63\x64\x35\x65\x33\x32\x61\x65\x39\x65\x35\x33\x61\x38\x32\x62\x31\x65\x36\x32\x62\x30\x32\x39\x34\x39\x35\x37\x62\x37\x33\x37\x34\x36\x31\x31\x38\x65\x61\x62\x30\x64\x39\x34\x65\x34\x39\x61\x35\x34\x36\x33\x66\x34\x39\x39\x34\x62\x30\x36\x30\x34\x33\x31\x62\x36\x33\x38\x39\x64\x66\x66\x31\x33\x38\x64\x63\x31\x62\x36\x39\x33\x32\x30\x30\x36\x62\x65\x32\x66\x65\x30\x30\x37\x61\x38\x32\x31\x37\x36\x32\x36\x31\x65\x64\x38\x38\x64\x33\x64\x39\x64\x64\x64\x64\x30\x65\x65\x35\x36\x38\x39\x38\x38\x62\x62\x39\x36\x66\x33\x36\x36\x37\x61\x61\x64\x38\x34\x63\x32\x62\x65\x65\x31\x33\x39\x35\x66\x39\x65\x63\x32\x33\x63\x66\x36\x31\x64\x64\x66\x35\x61\x37\x63\x61\x35\x34\x30\x65\x33\x34\x62\x34\x61\x32\x65\x34\x31\x39\x32\x30\x63\x36\x61\x32\x30\x34\x64\x33\x39\x63\x66\x37\x38\x65\x62\x66\x37\x64\x34\x33\x33\x31\x63\x38\x32\x33\x61\x39\x36\x63\x62\x33\x36\x61\x31\x64\x35\x62\x63\x31\x61\x38\x63\x36\x36\x36\x64\x62\x31\x65\x64\x65\x66\x34\x61\x62\x36\x37\x63\x31\x36\x37\x64\x63"), NULL);
	}
	{
		AssemblyDescriptionAttribute_tF4460CCB289F6E2F71841792BBC7E6907DF612B3 * tmp = (AssemblyDescriptionAttribute_tF4460CCB289F6E2F71841792BBC7E6907DF612B3 *)cache->attributes[12];
		AssemblyDescriptionAttribute__ctor_m3A0BD500FF352A67235FBA499FBA58EFF15B1F25(tmp, il2cpp_codegen_string_new_wrapper("\x70\x6F\x72\x74\x20\x6F\x66\x20\x74\x68\x65\x20\x6A\x61\x76\x61\x20\x62\x61\x73\x65\x64\x20\x62\x61\x72\x63\x6F\x64\x65\x20\x73\x63\x61\x6E\x6E\x69\x6E\x67\x20\x6C\x69\x62\x72\x61\x72\x79\x20\x66\x6F\x72\x20\x2E\x6E\x65\x74\x20\x28\x6A\x61\x76\x61\x20\x7A\x78\x69\x6E\x67\x20\x32\x37\x2E\x30\x32\x2E\x32\x30\x31\x37\x20\x31\x39\x3A\x32\x31\x3A\x35\x39\x29"), NULL);
	}
	{
		AssemblyTitleAttribute_tABB894D0792C7F307694CC796C8AE5D6A20382E7 * tmp = (AssemblyTitleAttribute_tABB894D0792C7F307694CC796C8AE5D6A20382E7 *)cache->attributes[13];
		AssemblyTitleAttribute__ctor_mE239F206B3B369C48AE1F3B4211688778FE99E8D(tmp, il2cpp_codegen_string_new_wrapper("\x7A\x78\x69\x6E\x67\x2E\x6E\x65\x74\x20\x66\x6F\x72\x20\x2E\x6E\x65\x74\x20\x33\x2E\x35\x20\x61\x6E\x64\x20\x75\x6E\x69\x74\x79\x20\x28\x77\x2F\x6F\x20\x53\x79\x73\x74\x65\x6D\x2E\x44\x72\x61\x77\x69\x6E\x67\x29"), NULL);
	}
	{
		DebuggableAttribute_tA8054EBD0FC7511695D494B690B5771658E3191B * tmp = (DebuggableAttribute_tA8054EBD0FC7511695D494B690B5771658E3191B *)cache->attributes[14];
		DebuggableAttribute__ctor_m7FF445C8435494A4847123A668D889E692E55550(tmp, 2LL, NULL);
	}
	{
		InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C * tmp = (InternalsVisibleToAttribute_t1D9772A02892BAC440952F880A43C257E6C3E68C *)cache->attributes[15];
		InternalsVisibleToAttribute__ctor_m420071A75DCEEC72356490C64B4B0B9270DA32B9(tmp, il2cpp_codegen_string_new_wrapper("\x7A\x78\x69\x6E\x67\x2E\x74\x65\x73\x74\x2C\x20\x50\x75\x62\x6C\x69\x63\x4B\x65\x79\x3D\x30\x30\x32\x34\x30\x30\x30\x30\x30\x34\x38\x30\x30\x30\x30\x30\x31\x34\x30\x31\x30\x30\x30\x30\x30\x36\x30\x32\x30\x30\x30\x30\x30\x30\x32\x34\x30\x30\x30\x30\x35\x32\x35\x33\x34\x31\x33\x31\x30\x30\x30\x38\x30\x30\x30\x30\x30\x31\x30\x30\x30\x31\x30\x30\x30\x31\x34\x63\x39\x61\x30\x31\x39\x35\x36\x66\x31\x33\x61\x33\x33\x39\x31\x33\x30\x36\x31\x36\x34\x37\x33\x66\x36\x39\x66\x39\x37\x35\x65\x30\x38\x36\x64\x39\x61\x33\x61\x35\x36\x32\x37\x38\x39\x33\x36\x62\x31\x32\x63\x34\x38\x63\x61\x34\x35\x61\x34\x64\x64\x66\x65\x65\x30\x35\x63\x32\x31\x63\x64\x63\x32\x32\x61\x65\x64\x64\x38\x34\x65\x39\x34\x36\x38\x32\x38\x33\x31\x32\x37\x61\x32\x30\x62\x62\x61\x34\x37\x36\x31\x63\x34\x65\x30\x64\x39\x38\x33\x36\x36\x32\x33\x66\x63\x39\x39\x31\x64\x35\x36\x32\x61\x35\x30\x38\x38\x34\x35\x66\x65\x33\x31\x34\x61\x34\x33\x35\x62\x64\x36\x63\x36\x66\x66\x34\x62\x30\x62\x31\x64\x37\x61\x31\x34\x31\x65\x66\x39\x33\x64\x63\x31\x63\x36\x32\x32\x35\x32\x34\x33\x38\x37\x32\x33\x66\x30\x66\x39\x33\x36\x36\x38\x32\x38\x38\x36\x37\x33\x65\x61\x36\x30\x34\x32\x65\x35\x38\x33\x62\x30\x65\x65\x64\x30\x34\x30\x65\x33\x36\x37\x33\x61\x63\x61\x35\x38\x34\x66\x39\x36\x64\x34\x64\x63\x61\x31\x39\x39\x33\x37\x66\x62\x65\x64\x33\x30\x65\x36\x63\x64\x33\x63\x30\x34\x30\x39\x64\x62\x38\x32\x64\x35\x63\x35\x64\x32\x30\x36\x37\x37\x31\x30\x64\x38\x64\x38\x36\x65\x30\x30\x38\x34\x34\x37\x32\x30\x31\x64\x39\x39\x32\x33\x38\x62\x39\x34\x64\x39\x31\x31\x37\x31\x62\x62\x30\x65\x64\x66\x33\x65\x38\x35\x34\x39\x38\x35\x36\x39\x33\x30\x35\x31\x62\x61\x35\x31\x36\x37\x63\x61\x36\x61\x65\x36\x35\x30\x61\x63\x61\x35\x64\x64\x36\x35\x34\x37\x31\x64\x36\x38\x38\x33\x35\x64\x62\x30\x30\x63\x65\x31\x37\x32\x38\x63\x35\x38\x63\x37\x62\x62\x66\x39\x61\x35\x64\x31\x35\x32\x66\x34\x39\x31\x31\x32\x33\x63\x61\x66\x39\x63\x30\x66\x36\x38\x36\x64\x63\x34\x65\x34\x38\x65\x31\x65\x66\x36\x33\x65\x61\x66\x37\x33\x38\x61\x31\x32\x62\x33\x37\x37\x31\x63\x32\x34\x64\x35\x39\x35\x63\x63\x35\x61\x35\x62\x35\x64\x61\x66\x32\x63\x63\x37\x36\x31\x31\x37\x35\x36\x65\x39\x62\x61\x33\x63\x63\x38\x39\x66\x30\x38\x66\x62\x39\x61\x64\x66\x33\x39\x36\x38\x35\x62\x64\x35\x33\x35\x36\x38\x35\x38\x63\x30\x31\x30\x65\x62\x39\x61\x61\x38\x61\x37\x36\x37\x65\x35\x65\x66\x30\x32\x30\x34\x30\x38\x65\x30\x63\x39\x37\x34\x36\x63\x62\x62\x35\x61\x38"), NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate10(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate11(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate12(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate13(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate14(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate15(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate16(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate17(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate18(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate19(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate1a(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate1b(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate1c(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate1d(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate1e(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__1_m9BF9719F06C4359A081BA925DFC7026F62A24B42(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__2_mCFB6E6C9A708EA2DD0625555C68E15701CD7D9EF(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__3_mCCF709582053223EE7637442358F3D927B67F5CD(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__4_m2AD4B9976A19005FF355508080F0A125122EB9E3(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__5_m8F8B7E2DFA4EAD798C7291C3AEDB325F93377977(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__6_m58FB4941EAA96FB85E7FF677AB4C543B5AC07717(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__7_m6EFD0B69369259690E5E94952881E718E816DC71(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__8_m694B3D8CBA0A0CB755006BBB73B34C46F91E74E5(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__9_mBC14C543C6798A75DBEFA79D06DE1FDE8D7145BF(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__a_m1886F305C81AEE7EE4F19F55D1937DBF8AA6C8BD(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__b_m727545DF2B142F5C49440CB85DB20E147D8694BA(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__c_mD672F1DCFB1A9E61EB6B11B35267FD5F25176810(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__d_m6CBCD20529F0ABC8302924657F268BAB8C06C57D(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__e_m7A1E58FD349C798C99C97EEBC34D87DCA3C0FC9D(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__f_m65E0C78DAA9B39A6C97C7CE142AF5C5357025165(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void BarcodeWriterGeneric_1_t620460DDBC5259833C395BDB567D8AE1E40D6F6E_CustomAttributesCacheGenerator_U3CFormatU3Ek__BackingField(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void BarcodeWriterGeneric_1_t620460DDBC5259833C395BDB567D8AE1E40D6F6E_CustomAttributesCacheGenerator_U3CEncoderU3Ek__BackingField(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void BarcodeWriterGeneric_1_t620460DDBC5259833C395BDB567D8AE1E40D6F6E_CustomAttributesCacheGenerator_U3CRendererU3Ek__BackingField(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void BarcodeWriterGeneric_1_t620460DDBC5259833C395BDB567D8AE1E40D6F6E_CustomAttributesCacheGenerator_BarcodeWriterGeneric_1_get_Format_m833EB0339C4CD279C40C4B456B88DBF6AA202913(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void BarcodeWriterGeneric_1_t620460DDBC5259833C395BDB567D8AE1E40D6F6E_CustomAttributesCacheGenerator_BarcodeWriterGeneric_1_set_Format_m09189E08902E93A3FFF4F74B48AC1B7369EC62E4(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void BarcodeWriterGeneric_1_t620460DDBC5259833C395BDB567D8AE1E40D6F6E_CustomAttributesCacheGenerator_BarcodeWriterGeneric_1_get_Encoder_m40E23970DCAEA682B1D96C4D9ED2AEDE03229645(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void BarcodeWriterGeneric_1_t620460DDBC5259833C395BDB567D8AE1E40D6F6E_CustomAttributesCacheGenerator_BarcodeWriterGeneric_1_get_Renderer_mF8BE715E8B25A562FBD348562D3B95AACAFB98E5(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void BarcodeWriterGeneric_1_t620460DDBC5259833C395BDB567D8AE1E40D6F6E_CustomAttributesCacheGenerator_BarcodeWriterGeneric_1_set_Renderer_m9038C6A456C0CB6A30439E11BA7578F96D98688F(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void IBarcodeWriter_t4C9C4ABE6C18950C31E24EC3A3BD46559D001FCD_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CLSCompliantAttribute_tA28EF6D4ADBD3C5C429DE9A70DD1E927C8906249 * tmp = (CLSCompliantAttribute_tA28EF6D4ADBD3C5C429DE9A70DD1E927C8906249 *)cache->attributes[0];
		CLSCompliantAttribute__ctor_m340EDA4DA5E45506AD631FE84241ADFB6B3F0270(tmp, false, NULL);
	}
}
static void BitArray_tD74DCC9A9DD55A5FA0F667C6095F5E62D649131C_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		DefaultMemberAttribute_t8C9B3330DEA69EE364962477FF14FD2CFE30D4B5 * tmp = (DefaultMemberAttribute_t8C9B3330DEA69EE364962477FF14FD2CFE30D4B5 *)cache->attributes[0];
		DefaultMemberAttribute__ctor_mA025B6F5B3A9292696E01108027840C8DFF7F4D7(tmp, il2cpp_codegen_string_new_wrapper("\x49\x74\x65\x6D"), NULL);
	}
}
static void EncodingOptions_t5FF49DF7C9BE1D3FC6B5D8999CCCF9ADCA8D132F_CustomAttributesCacheGenerator_U3CHintsU3Ek__BackingField(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void EncodingOptions_t5FF49DF7C9BE1D3FC6B5D8999CCCF9ADCA8D132F_CustomAttributesCacheGenerator_EncodingOptions_get_Hints_m11AC6CBF296B50CEEBBBF0A0C17C0064297EE3C6(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void EncodingOptions_t5FF49DF7C9BE1D3FC6B5D8999CCCF9ADCA8D132F_CustomAttributesCacheGenerator_EncodingOptions_set_Hints_m9423CF843812503DE489A3D7B511DD24287474BD(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void EncodingOptions_t5FF49DF7C9BE1D3FC6B5D8999CCCF9ADCA8D132F_CustomAttributesCacheGenerator_EncodingOptions_t5FF49DF7C9BE1D3FC6B5D8999CCCF9ADCA8D132F____Hints_PropertyInfo(CustomAttributesCache* cache)
{
	{
		BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2 * tmp = (BrowsableAttribute_t705E82089C10349DE28438D04F7CA2B41758B5A2 *)cache->attributes[0];
		BrowsableAttribute__ctor_m67F6E4C4B28C23E90D4C0527D1BA41555D1B0DDE(tmp, false, NULL);
	}
}
static void Version_tCB110CA71F3CCE0B9DB99D751294AAE79C75D75E_CustomAttributesCacheGenerator_Version__ctor_m632065CF5A1FD956582B79A7C137207E0AEF0FF1____ecBlocks2(CustomAttributesCache* cache)
{
	{
		ParamArrayAttribute_t9DCEB4CDDB8EDDB1124171D4C51FA6069EEA5C5F * tmp = (ParamArrayAttribute_t9DCEB4CDDB8EDDB1124171D4C51FA6069EEA5C5F *)cache->attributes[0];
		ParamArrayAttribute__ctor_mCC72AFF718185BA7B87FD8D9471F1274400C5719(tmp, NULL);
	}
}
static void ECBlocks_tD16A91D6A2E9CF6323A3A1E4A7A1DEB501CA02F4_CustomAttributesCacheGenerator_ECBlocks__ctor_m9D0FEA2A62AD87300386326630D71B2FE43D9011____ecBlocks1(CustomAttributesCache* cache)
{
	{
		ParamArrayAttribute_t9DCEB4CDDB8EDDB1124171D4C51FA6069EEA5C5F * tmp = (ParamArrayAttribute_t9DCEB4CDDB8EDDB1124171D4C51FA6069EEA5C5F *)cache->attributes[0];
		ParamArrayAttribute__ctor_mCC72AFF718185BA7B87FD8D9471F1274400C5719(tmp, NULL);
	}
}
static void DigitContainer_t54CF59B9FAD2F0D27764576030066E76496BCF3A_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		DefaultMemberAttribute_t8C9B3330DEA69EE364962477FF14FD2CFE30D4B5 * tmp = (DefaultMemberAttribute_t8C9B3330DEA69EE364962477FF14FD2CFE30D4B5 *)cache->attributes[0];
		DefaultMemberAttribute__ctor_mA025B6F5B3A9292696E01108027840C8DFF7F4D7(tmp, il2cpp_codegen_string_new_wrapper("\x49\x74\x65\x6D"), NULL);
	}
}
static void AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_U3CisCompactU3Ek__BackingField(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_U3CSizeU3Ek__BackingField(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_U3CLayersU3Ek__BackingField(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_U3CCodeWordsU3Ek__BackingField(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_U3CMatrixU3Ek__BackingField(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_AztecCode_set_isCompact_m597E612BC815EA9C72787E2D916F328FDA3F24BC(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_AztecCode_set_Size_m5C7C783E5F2D788C6684E23A49EC6CDF5A058495(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_AztecCode_set_Layers_m8B5FE0EF288E66B8C5F1B8A184C0E87D43B8F478(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_AztecCode_set_CodeWords_mB8C8D156EF6308F34AE895994B478B6138CCD2D9(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_AztecCode_get_Matrix_m89D879400229206B4C9084D174E17B0CEC53C6EF(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_AztecCode_set_Matrix_m2C412B37E380796D2FB9A4952937C0CD2270BDB8(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void ByteMatrix_t29F5FFB925F2DCAA16CF177733CA9175C20BCE9F_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		DefaultMemberAttribute_t8C9B3330DEA69EE364962477FF14FD2CFE30D4B5 * tmp = (DefaultMemberAttribute_t8C9B3330DEA69EE364962477FF14FD2CFE30D4B5 *)cache->attributes[0];
		DefaultMemberAttribute__ctor_mA025B6F5B3A9292696E01108027840C8DFF7F4D7(tmp, il2cpp_codegen_string_new_wrapper("\x49\x74\x65\x6D"), NULL);
	}
}
static void BarcodeRow_tCF2603F9135C343689546B3BAD9DC367735E23DA_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		DefaultMemberAttribute_t8C9B3330DEA69EE364962477FF14FD2CFE30D4B5 * tmp = (DefaultMemberAttribute_t8C9B3330DEA69EE364962477FF14FD2CFE30D4B5 *)cache->attributes[0];
		DefaultMemberAttribute__ctor_mA025B6F5B3A9292696E01108027840C8DFF7F4D7(tmp, il2cpp_codegen_string_new_wrapper("\x49\x74\x65\x6D"), NULL);
	}
}
static void Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB_CustomAttributesCacheGenerator_U3CForegroundU3Ek__BackingField(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB_CustomAttributesCacheGenerator_U3CBackgroundU3Ek__BackingField(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB_CustomAttributesCacheGenerator_Color32Renderer_get_Foreground_m834D2A31C6626724E141417D102E2BD6FDA9462C(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB_CustomAttributesCacheGenerator_Color32Renderer_set_Foreground_mDE1439A10E49A742C22D51F9D2FBA0CFAEADA2FB(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB_CustomAttributesCacheGenerator_Color32Renderer_get_Background_m51B69CE85809EB3420A58292A274BA1B56F4A4DF(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB_CustomAttributesCacheGenerator_Color32Renderer_set_Background_mCC0C1C48BC5E28A649930D77B256EEF9E398DE63(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB_CustomAttributesCacheGenerator_Color32Renderer_Render_m6FE05E98B75976AA9F41400B9C6C2215B7B2E40F(CustomAttributesCache* cache)
{
	{
		CLSCompliantAttribute_tA28EF6D4ADBD3C5C429DE9A70DD1E927C8906249 * tmp = (CLSCompliantAttribute_tA28EF6D4ADBD3C5C429DE9A70DD1E927C8906249 *)cache->attributes[0];
		CLSCompliantAttribute__ctor_m340EDA4DA5E45506AD631FE84241ADFB6B3F0270(tmp, false, NULL);
	}
}
static void Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB_CustomAttributesCacheGenerator_Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB____Foreground_PropertyInfo(CustomAttributesCache* cache)
{
	{
		CLSCompliantAttribute_tA28EF6D4ADBD3C5C429DE9A70DD1E927C8906249 * tmp = (CLSCompliantAttribute_tA28EF6D4ADBD3C5C429DE9A70DD1E927C8906249 *)cache->attributes[0];
		CLSCompliantAttribute__ctor_m340EDA4DA5E45506AD631FE84241ADFB6B3F0270(tmp, false, NULL);
	}
}
static void Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB_CustomAttributesCacheGenerator_Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB____Background_PropertyInfo(CustomAttributesCache* cache)
{
	{
		CLSCompliantAttribute_tA28EF6D4ADBD3C5C429DE9A70DD1E927C8906249 * tmp = (CLSCompliantAttribute_tA28EF6D4ADBD3C5C429DE9A70DD1E927C8906249 *)cache->attributes[0];
		CLSCompliantAttribute__ctor_m340EDA4DA5E45506AD631FE84241ADFB6B3F0270(tmp, false, NULL);
	}
}
static void BitMatrix_t62803B4E2B24B755B0428D226B88E62A6AC457C5_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		DefaultMemberAttribute_t8C9B3330DEA69EE364962477FF14FD2CFE30D4B5 * tmp = (DefaultMemberAttribute_t8C9B3330DEA69EE364962477FF14FD2CFE30D4B5 *)cache->attributes[0];
		DefaultMemberAttribute__ctor_mA025B6F5B3A9292696E01108027840C8DFF7F4D7(tmp, il2cpp_codegen_string_new_wrapper("\x49\x74\x65\x6D"), NULL);
	}
}
static void QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_U3CModeU3Ek__BackingField(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_U3CECLevelU3Ek__BackingField(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_U3CVersionU3Ek__BackingField(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_U3CMaskPatternU3Ek__BackingField(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_U3CMatrixU3Ek__BackingField(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_QRCode_get_Mode_m33D91E088D7AF12E3E1415A8FFD02DF427D4AC18(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_QRCode_set_Mode_m54400FA168DDB0166022CCD6BEBDC7D3BAD54960(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_QRCode_get_ECLevel_m9D85C676D8DDD7A8C2122A2E12F82BEF8E7696C2(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_QRCode_set_ECLevel_m992CE7CECA778D10C8E27038E625ED387DC2387D(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_QRCode_get_Version_m2BDFDC6D41ED5BB73ED800BC10F2D0608E4C5E22(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_QRCode_set_Version_mCA44F076712002D1BCACB7D1235D9B35EB462748(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_QRCode_get_MaskPattern_mC40211D514C99CAD309D0B364783A85F27D3080A(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_QRCode_set_MaskPattern_m4E89EF31D21F23D4BB15F0E0A52133FFFBF863D5(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_QRCode_get_Matrix_mC0E1FE9FD38C7194089615225BC7B337A94BC502(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_QRCode_set_Matrix_m58CF18CE0B649E241AE22DD283932C2FC9D166D2(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
static void BarcodeFormat_tB53F18F37A04DA3464DD266F939FD0A315544855_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		FlagsAttribute_t511C558FACEF1CC64702A8FAB67CAF3CBA65DF36 * tmp = (FlagsAttribute_t511C558FACEF1CC64702A8FAB67CAF3CBA65DF36 *)cache->attributes[0];
		FlagsAttribute__ctor_mE8DCBA1BE0E6B0424FEF5E5F249733CF6A0E1229(tmp, NULL);
	}
}
static void DigitContainer_tFDA46C9477308140571F6CF1B540B4D067CADC3C_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		DefaultMemberAttribute_t8C9B3330DEA69EE364962477FF14FD2CFE30D4B5 * tmp = (DefaultMemberAttribute_t8C9B3330DEA69EE364962477FF14FD2CFE30D4B5 *)cache->attributes[0];
		DefaultMemberAttribute__ctor_mA025B6F5B3A9292696E01108027840C8DFF7F4D7(tmp, il2cpp_codegen_string_new_wrapper("\x49\x74\x65\x6D"), NULL);
	}
}
static void U3CPrivateImplementationDetailsU3EU7BC85D4D1AU2D4642U2D4199U2DB42DU2D05D1B1D6E6C8U7D_t9C5557FD76538C206D4B7E794EA106C3F89A19B0_CustomAttributesCacheGenerator(CustomAttributesCache* cache)
{
	{
		CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C * tmp = (CompilerGeneratedAttribute_t39106AB982658D7A94C27DEF3C48DB2F5F7CD75C *)cache->attributes[0];
		CompilerGeneratedAttribute__ctor_m9DC3E4E2DA76FE93948D44199213E2E924DCBE35(tmp, NULL);
	}
}
IL2CPP_EXTERN_C const CustomAttributesCacheGenerator g_zxing_unity_AttributeGenerators[];
const CustomAttributesCacheGenerator g_zxing_unity_AttributeGenerators[89] = 
{
	IBarcodeWriter_t4C9C4ABE6C18950C31E24EC3A3BD46559D001FCD_CustomAttributesCacheGenerator,
	BitArray_tD74DCC9A9DD55A5FA0F667C6095F5E62D649131C_CustomAttributesCacheGenerator,
	DigitContainer_t54CF59B9FAD2F0D27764576030066E76496BCF3A_CustomAttributesCacheGenerator,
	ByteMatrix_t29F5FFB925F2DCAA16CF177733CA9175C20BCE9F_CustomAttributesCacheGenerator,
	BarcodeRow_tCF2603F9135C343689546B3BAD9DC367735E23DA_CustomAttributesCacheGenerator,
	BitMatrix_t62803B4E2B24B755B0428D226B88E62A6AC457C5_CustomAttributesCacheGenerator,
	BarcodeFormat_tB53F18F37A04DA3464DD266F939FD0A315544855_CustomAttributesCacheGenerator,
	DigitContainer_tFDA46C9477308140571F6CF1B540B4D067CADC3C_CustomAttributesCacheGenerator,
	U3CPrivateImplementationDetailsU3EU7BC85D4D1AU2D4642U2D4199U2DB42DU2D05D1B1D6E6C8U7D_t9C5557FD76538C206D4B7E794EA106C3F89A19B0_CustomAttributesCacheGenerator,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate10,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate11,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate12,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate13,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate14,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate15,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate16,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate17,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate18,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate19,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate1a,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate1b,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate1c,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate1d,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_CSU24U3CU3E9__CachedAnonymousMethodDelegate1e,
	BarcodeWriterGeneric_1_t620460DDBC5259833C395BDB567D8AE1E40D6F6E_CustomAttributesCacheGenerator_U3CFormatU3Ek__BackingField,
	BarcodeWriterGeneric_1_t620460DDBC5259833C395BDB567D8AE1E40D6F6E_CustomAttributesCacheGenerator_U3CEncoderU3Ek__BackingField,
	BarcodeWriterGeneric_1_t620460DDBC5259833C395BDB567D8AE1E40D6F6E_CustomAttributesCacheGenerator_U3CRendererU3Ek__BackingField,
	EncodingOptions_t5FF49DF7C9BE1D3FC6B5D8999CCCF9ADCA8D132F_CustomAttributesCacheGenerator_U3CHintsU3Ek__BackingField,
	AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_U3CisCompactU3Ek__BackingField,
	AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_U3CSizeU3Ek__BackingField,
	AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_U3CLayersU3Ek__BackingField,
	AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_U3CCodeWordsU3Ek__BackingField,
	AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_U3CMatrixU3Ek__BackingField,
	Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB_CustomAttributesCacheGenerator_U3CForegroundU3Ek__BackingField,
	Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB_CustomAttributesCacheGenerator_U3CBackgroundU3Ek__BackingField,
	QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_U3CModeU3Ek__BackingField,
	QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_U3CECLevelU3Ek__BackingField,
	QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_U3CVersionU3Ek__BackingField,
	QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_U3CMaskPatternU3Ek__BackingField,
	QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_U3CMatrixU3Ek__BackingField,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__1_m9BF9719F06C4359A081BA925DFC7026F62A24B42,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__2_mCFB6E6C9A708EA2DD0625555C68E15701CD7D9EF,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__3_mCCF709582053223EE7637442358F3D927B67F5CD,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__4_m2AD4B9976A19005FF355508080F0A125122EB9E3,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__5_m8F8B7E2DFA4EAD798C7291C3AEDB325F93377977,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__6_m58FB4941EAA96FB85E7FF677AB4C543B5AC07717,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__7_m6EFD0B69369259690E5E94952881E718E816DC71,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__8_m694B3D8CBA0A0CB755006BBB73B34C46F91E74E5,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__9_mBC14C543C6798A75DBEFA79D06DE1FDE8D7145BF,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__a_m1886F305C81AEE7EE4F19F55D1937DBF8AA6C8BD,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__b_m727545DF2B142F5C49440CB85DB20E147D8694BA,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__c_mD672F1DCFB1A9E61EB6B11B35267FD5F25176810,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__d_m6CBCD20529F0ABC8302924657F268BAB8C06C57D,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__e_m7A1E58FD349C798C99C97EEBC34D87DCA3C0FC9D,
	MultiFormatWriter_t3ED405B1BD47358B902939D5F9B6EB5806025953_CustomAttributesCacheGenerator_MultiFormatWriter_U3C_cctorU3Eb__f_m65E0C78DAA9B39A6C97C7CE142AF5C5357025165,
	BarcodeWriterGeneric_1_t620460DDBC5259833C395BDB567D8AE1E40D6F6E_CustomAttributesCacheGenerator_BarcodeWriterGeneric_1_get_Format_m833EB0339C4CD279C40C4B456B88DBF6AA202913,
	BarcodeWriterGeneric_1_t620460DDBC5259833C395BDB567D8AE1E40D6F6E_CustomAttributesCacheGenerator_BarcodeWriterGeneric_1_set_Format_m09189E08902E93A3FFF4F74B48AC1B7369EC62E4,
	BarcodeWriterGeneric_1_t620460DDBC5259833C395BDB567D8AE1E40D6F6E_CustomAttributesCacheGenerator_BarcodeWriterGeneric_1_get_Encoder_m40E23970DCAEA682B1D96C4D9ED2AEDE03229645,
	BarcodeWriterGeneric_1_t620460DDBC5259833C395BDB567D8AE1E40D6F6E_CustomAttributesCacheGenerator_BarcodeWriterGeneric_1_get_Renderer_mF8BE715E8B25A562FBD348562D3B95AACAFB98E5,
	BarcodeWriterGeneric_1_t620460DDBC5259833C395BDB567D8AE1E40D6F6E_CustomAttributesCacheGenerator_BarcodeWriterGeneric_1_set_Renderer_m9038C6A456C0CB6A30439E11BA7578F96D98688F,
	EncodingOptions_t5FF49DF7C9BE1D3FC6B5D8999CCCF9ADCA8D132F_CustomAttributesCacheGenerator_EncodingOptions_get_Hints_m11AC6CBF296B50CEEBBBF0A0C17C0064297EE3C6,
	EncodingOptions_t5FF49DF7C9BE1D3FC6B5D8999CCCF9ADCA8D132F_CustomAttributesCacheGenerator_EncodingOptions_set_Hints_m9423CF843812503DE489A3D7B511DD24287474BD,
	AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_AztecCode_set_isCompact_m597E612BC815EA9C72787E2D916F328FDA3F24BC,
	AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_AztecCode_set_Size_m5C7C783E5F2D788C6684E23A49EC6CDF5A058495,
	AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_AztecCode_set_Layers_m8B5FE0EF288E66B8C5F1B8A184C0E87D43B8F478,
	AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_AztecCode_set_CodeWords_mB8C8D156EF6308F34AE895994B478B6138CCD2D9,
	AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_AztecCode_get_Matrix_m89D879400229206B4C9084D174E17B0CEC53C6EF,
	AztecCode_t5B16475239F8472C6FA45CBD663E3880E3E0789A_CustomAttributesCacheGenerator_AztecCode_set_Matrix_m2C412B37E380796D2FB9A4952937C0CD2270BDB8,
	Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB_CustomAttributesCacheGenerator_Color32Renderer_get_Foreground_m834D2A31C6626724E141417D102E2BD6FDA9462C,
	Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB_CustomAttributesCacheGenerator_Color32Renderer_set_Foreground_mDE1439A10E49A742C22D51F9D2FBA0CFAEADA2FB,
	Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB_CustomAttributesCacheGenerator_Color32Renderer_get_Background_m51B69CE85809EB3420A58292A274BA1B56F4A4DF,
	Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB_CustomAttributesCacheGenerator_Color32Renderer_set_Background_mCC0C1C48BC5E28A649930D77B256EEF9E398DE63,
	Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB_CustomAttributesCacheGenerator_Color32Renderer_Render_m6FE05E98B75976AA9F41400B9C6C2215B7B2E40F,
	QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_QRCode_get_Mode_m33D91E088D7AF12E3E1415A8FFD02DF427D4AC18,
	QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_QRCode_set_Mode_m54400FA168DDB0166022CCD6BEBDC7D3BAD54960,
	QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_QRCode_get_ECLevel_m9D85C676D8DDD7A8C2122A2E12F82BEF8E7696C2,
	QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_QRCode_set_ECLevel_m992CE7CECA778D10C8E27038E625ED387DC2387D,
	QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_QRCode_get_Version_m2BDFDC6D41ED5BB73ED800BC10F2D0608E4C5E22,
	QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_QRCode_set_Version_mCA44F076712002D1BCACB7D1235D9B35EB462748,
	QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_QRCode_get_MaskPattern_mC40211D514C99CAD309D0B364783A85F27D3080A,
	QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_QRCode_set_MaskPattern_m4E89EF31D21F23D4BB15F0E0A52133FFFBF863D5,
	QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_QRCode_get_Matrix_mC0E1FE9FD38C7194089615225BC7B337A94BC502,
	QRCode_tE43D3BF179C86784CDC392CB6931E50D8362F7DA_CustomAttributesCacheGenerator_QRCode_set_Matrix_m58CF18CE0B649E241AE22DD283932C2FC9D166D2,
	Version_tCB110CA71F3CCE0B9DB99D751294AAE79C75D75E_CustomAttributesCacheGenerator_Version__ctor_m632065CF5A1FD956582B79A7C137207E0AEF0FF1____ecBlocks2,
	ECBlocks_tD16A91D6A2E9CF6323A3A1E4A7A1DEB501CA02F4_CustomAttributesCacheGenerator_ECBlocks__ctor_m9D0FEA2A62AD87300386326630D71B2FE43D9011____ecBlocks1,
	EncodingOptions_t5FF49DF7C9BE1D3FC6B5D8999CCCF9ADCA8D132F_CustomAttributesCacheGenerator_EncodingOptions_t5FF49DF7C9BE1D3FC6B5D8999CCCF9ADCA8D132F____Hints_PropertyInfo,
	Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB_CustomAttributesCacheGenerator_Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB____Foreground_PropertyInfo,
	Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB_CustomAttributesCacheGenerator_Color32Renderer_tBADA3015B142B174C00107CEF1A7FEB4BF09B7EB____Background_PropertyInfo,
	zxing_unity_CustomAttributesCacheGenerator,
};
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void RuntimeCompatibilityAttribute_set_WrapNonExceptionThrows_m8562196F90F3EBCEC23B5708EE0332842883C490_inline (RuntimeCompatibilityAttribute_tFF99AB2963098F9CBCD47A20D9FD3D51C17C1C80 * __this, bool ___value0, const RuntimeMethod* method)
{
	{
		bool L_0 = ___value0;
		__this->set_m_wrapNonExceptionThrows_0(L_0);
		return;
	}
}

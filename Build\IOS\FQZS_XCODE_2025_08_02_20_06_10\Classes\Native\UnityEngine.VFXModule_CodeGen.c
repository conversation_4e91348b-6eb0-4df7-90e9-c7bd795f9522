﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void UnityEngine.VFX.VFXEventAttribute::.ctor(System.IntPtr,System.Boolean,UnityEngine.VFX.VisualEffectAsset)
extern void VFXEventAttribute__ctor_mDE4833E90493250BDBF27A8A5AFAADE4E5A90964 (void);
// 0x00000002 System.IntPtr UnityEngine.VFX.VFXEventAttribute::Internal_Create()
extern void VFXEventAttribute_Internal_Create_mA5963ED38DB766B7AB2674A73CA7046D7470B794 (void);
// 0x00000003 UnityEngine.VFX.VFXEventAttribute UnityEngine.VFX.VFXEventAttribute::Internal_InstanciateVFXEventAttribute(UnityEngine.VFX.VisualEffectAsset)
extern void VFXEventAttribute_Internal_InstanciateVFXEventAttribute_m3AD160FECB74D898826FB11ECB937EA9BA0B47BA (void);
// 0x00000004 System.Void UnityEngine.VFX.VFXEventAttribute::Internal_InitFromAsset(UnityEngine.VFX.VisualEffectAsset)
extern void VFXEventAttribute_Internal_InitFromAsset_m5DC121D03DAA7A8E6FF348D98CDAAE209F1F8AE1 (void);
// 0x00000005 System.Void UnityEngine.VFX.VFXEventAttribute::Release()
extern void VFXEventAttribute_Release_m9AEDE0F9E70188F9737D68638733D70DB4814983 (void);
// 0x00000006 System.Void UnityEngine.VFX.VFXEventAttribute::Finalize()
extern void VFXEventAttribute_Finalize_mC4B4672CB30A8925BD10CF82CFB59395A2C0A83A (void);
// 0x00000007 System.Void UnityEngine.VFX.VFXEventAttribute::Dispose()
extern void VFXEventAttribute_Dispose_m56663028C3FD054C89339050249052E8C350D0AF (void);
// 0x00000008 System.Void UnityEngine.VFX.VFXEventAttribute::Internal_Destroy(System.IntPtr)
extern void VFXEventAttribute_Internal_Destroy_m4AE4B4E642D451D77F464E8D57985244940C6CAC (void);
// 0x00000009 System.Void UnityEngine.VFX.VFXExpressionValues::.ctor()
extern void VFXExpressionValues__ctor_m4B8043CAE5D33D062707D851DD4E62D9F7BB1320 (void);
// 0x0000000A UnityEngine.VFX.VFXExpressionValues UnityEngine.VFX.VFXExpressionValues::CreateExpressionValuesWrapper(System.IntPtr)
extern void VFXExpressionValues_CreateExpressionValuesWrapper_m0CCEFD12B45E894035502DA9B7CDAF716CFE2D2A (void);
// 0x0000000B System.Void UnityEngine.VFX.VFXSpawnerCallbacks::OnPlay(UnityEngine.VFX.VFXSpawnerState,UnityEngine.VFX.VFXExpressionValues,UnityEngine.VFX.VisualEffect)
// 0x0000000C System.Void UnityEngine.VFX.VFXSpawnerCallbacks::OnUpdate(UnityEngine.VFX.VFXSpawnerState,UnityEngine.VFX.VFXExpressionValues,UnityEngine.VFX.VisualEffect)
// 0x0000000D System.Void UnityEngine.VFX.VFXSpawnerCallbacks::OnStop(UnityEngine.VFX.VFXSpawnerState,UnityEngine.VFX.VFXExpressionValues,UnityEngine.VFX.VisualEffect)
// 0x0000000E System.Void UnityEngine.VFX.VFXSpawnerCallbacks::.ctor()
extern void VFXSpawnerCallbacks__ctor_m4F7269BAF4E8085E8628FF6C0EF0BD42BF43B072 (void);
// 0x0000000F System.Void UnityEngine.VFX.VFXSpawnerState::.ctor(System.IntPtr,System.Boolean)
extern void VFXSpawnerState__ctor_m2A95971A7A6D3701F39FD7A656451CD619C3010E (void);
// 0x00000010 UnityEngine.VFX.VFXSpawnerState UnityEngine.VFX.VFXSpawnerState::CreateSpawnerStateWrapper()
extern void VFXSpawnerState_CreateSpawnerStateWrapper_m350F2A13C5F5542667390CF7578E0CD9AC6A41E6 (void);
// 0x00000011 System.Void UnityEngine.VFX.VFXSpawnerState::SetWrapValue(System.IntPtr)
extern void VFXSpawnerState_SetWrapValue_mA673646567D2B54072EEC275FB79BEBF82C7EE31 (void);
// 0x00000012 System.Void UnityEngine.VFX.VFXSpawnerState::Release()
extern void VFXSpawnerState_Release_m9D420CAC0353FCBCBF0F2D59AA286ED3D0BC4649 (void);
// 0x00000013 System.Void UnityEngine.VFX.VFXSpawnerState::Finalize()
extern void VFXSpawnerState_Finalize_mDD2AA40919A974E7CE616FE003162A762B806CA5 (void);
// 0x00000014 System.Void UnityEngine.VFX.VFXSpawnerState::Dispose()
extern void VFXSpawnerState_Dispose_mE45940AB83108F8F02D81BE4CEDE8C738F1C47F5 (void);
// 0x00000015 System.Void UnityEngine.VFX.VFXSpawnerState::Internal_Destroy(System.IntPtr)
extern void VFXSpawnerState_Internal_Destroy_m847E1B71DBC4B8C289D718F62219EE94088FD0F9 (void);
// 0x00000016 System.Void UnityEngine.VFX.VisualEffectObject::.ctor()
extern void VisualEffectObject__ctor_mC6F115A9348A51326C366ACB2724884050A00B2D (void);
// 0x00000017 System.Void UnityEngine.VFX.VisualEffectAsset::.ctor()
extern void VisualEffectAsset__ctor_m61A7A279415031194C654C892DFB4A1AD19CCC7C (void);
// 0x00000018 System.Void UnityEngine.VFX.VisualEffectAsset::.cctor()
extern void VisualEffectAsset__cctor_m7363540DD8CE56375E38B8872AA7E6C23908DF66 (void);
// 0x00000019 System.Void UnityEngine.VFX.VFXOutputEventArgs::.ctor(System.Int32,UnityEngine.VFX.VFXEventAttribute)
extern void VFXOutputEventArgs__ctor_m7C49FC1B34F3582FE08D7A1CAA5024C09A86F7A2 (void);
// 0x0000001A UnityEngine.VFX.VisualEffectAsset UnityEngine.VFX.VisualEffect::get_visualEffectAsset()
extern void VisualEffect_get_visualEffectAsset_m019D107588568B2C5F1FFA77C626DC986A63AD96 (void);
// 0x0000001B UnityEngine.VFX.VFXEventAttribute UnityEngine.VFX.VisualEffect::CreateVFXEventAttribute()
extern void VisualEffect_CreateVFXEventAttribute_mE99FC6DA23D465D06A8547CC5B2502FB0B30D8D6 (void);
// 0x0000001C UnityEngine.VFX.VFXEventAttribute UnityEngine.VFX.VisualEffect::InvokeGetCachedEventAttributeForOutputEvent_Internal(UnityEngine.VFX.VisualEffect)
extern void VisualEffect_InvokeGetCachedEventAttributeForOutputEvent_Internal_m2535D29D566CCC8B69A8D276C4BF1FB74CDCF496 (void);
// 0x0000001D System.Void UnityEngine.VFX.VisualEffect::InvokeOutputEventReceived_Internal(UnityEngine.VFX.VisualEffect,System.Int32)
extern void VisualEffect_InvokeOutputEventReceived_Internal_mA75BD48DAF6822A61D161B5EBCB92EE386F8F403 (void);
static Il2CppMethodPointer s_methodPointers[29] = 
{
	VFXEventAttribute__ctor_mDE4833E90493250BDBF27A8A5AFAADE4E5A90964,
	VFXEventAttribute_Internal_Create_mA5963ED38DB766B7AB2674A73CA7046D7470B794,
	VFXEventAttribute_Internal_InstanciateVFXEventAttribute_m3AD160FECB74D898826FB11ECB937EA9BA0B47BA,
	VFXEventAttribute_Internal_InitFromAsset_m5DC121D03DAA7A8E6FF348D98CDAAE209F1F8AE1,
	VFXEventAttribute_Release_m9AEDE0F9E70188F9737D68638733D70DB4814983,
	VFXEventAttribute_Finalize_mC4B4672CB30A8925BD10CF82CFB59395A2C0A83A,
	VFXEventAttribute_Dispose_m56663028C3FD054C89339050249052E8C350D0AF,
	VFXEventAttribute_Internal_Destroy_m4AE4B4E642D451D77F464E8D57985244940C6CAC,
	VFXExpressionValues__ctor_m4B8043CAE5D33D062707D851DD4E62D9F7BB1320,
	VFXExpressionValues_CreateExpressionValuesWrapper_m0CCEFD12B45E894035502DA9B7CDAF716CFE2D2A,
	NULL,
	NULL,
	NULL,
	VFXSpawnerCallbacks__ctor_m4F7269BAF4E8085E8628FF6C0EF0BD42BF43B072,
	VFXSpawnerState__ctor_m2A95971A7A6D3701F39FD7A656451CD619C3010E,
	VFXSpawnerState_CreateSpawnerStateWrapper_m350F2A13C5F5542667390CF7578E0CD9AC6A41E6,
	VFXSpawnerState_SetWrapValue_mA673646567D2B54072EEC275FB79BEBF82C7EE31,
	VFXSpawnerState_Release_m9D420CAC0353FCBCBF0F2D59AA286ED3D0BC4649,
	VFXSpawnerState_Finalize_mDD2AA40919A974E7CE616FE003162A762B806CA5,
	VFXSpawnerState_Dispose_mE45940AB83108F8F02D81BE4CEDE8C738F1C47F5,
	VFXSpawnerState_Internal_Destroy_m847E1B71DBC4B8C289D718F62219EE94088FD0F9,
	VisualEffectObject__ctor_mC6F115A9348A51326C366ACB2724884050A00B2D,
	VisualEffectAsset__ctor_m61A7A279415031194C654C892DFB4A1AD19CCC7C,
	VisualEffectAsset__cctor_m7363540DD8CE56375E38B8872AA7E6C23908DF66,
	VFXOutputEventArgs__ctor_m7C49FC1B34F3582FE08D7A1CAA5024C09A86F7A2,
	VisualEffect_get_visualEffectAsset_m019D107588568B2C5F1FFA77C626DC986A63AD96,
	VisualEffect_CreateVFXEventAttribute_mE99FC6DA23D465D06A8547CC5B2502FB0B30D8D6,
	VisualEffect_InvokeGetCachedEventAttributeForOutputEvent_Internal_m2535D29D566CCC8B69A8D276C4BF1FB74CDCF496,
	VisualEffect_InvokeOutputEventReceived_Internal_mA75BD48DAF6822A61D161B5EBCB92EE386F8F403,
};
extern void VFXOutputEventArgs__ctor_m7C49FC1B34F3582FE08D7A1CAA5024C09A86F7A2_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[1] = 
{
	{ 0x06000019, VFXOutputEventArgs__ctor_m7C49FC1B34F3582FE08D7A1CAA5024C09A86F7A2_AdjustorThunk },
};
static const int32_t s_InvokerIndices[29] = 
{
	1302,
	6265,
	6091,
	3348,
	3967,
	3967,
	3967,
	6215,
	3967,
	6090,
	1337,
	1337,
	1337,
	3967,
	2103,
	6270,
	3315,
	3967,
	3967,
	3967,
	6215,
	3967,
	3967,
	6289,
	1981,
	3916,
	3916,
	6091,
	5830,
};
extern const CustomAttributesCacheGenerator g_UnityEngine_VFXModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_VFXModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_VFXModule_CodeGenModule = 
{
	"UnityEngine.VFXModule.dll",
	29,
	s_methodPointers,
	1,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_UnityEngine_VFXModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void UnityEngine.Yoga.BaselineFunction::.ctor(System.Object,System.IntPtr)
extern void BaselineFunction__ctor_m909E73515F8805EED0E15C6ED2CEC3A08CEB03B3 (void);
// 0x00000002 System.Single UnityEngine.Yoga.BaselineFunction::Invoke(UnityEngine.Yoga.YogaNode,System.Single,System.Single)
extern void BaselineFunction_Invoke_mAEC462B19D69EAC84C6F4D13058EF3DA25BBE647 (void);
// 0x00000003 System.IAsyncResult UnityEngine.Yoga.BaselineFunction::BeginInvoke(UnityEngine.Yoga.YogaNode,System.Single,System.Single,System.AsyncCallback,System.Object)
extern void BaselineFunction_BeginInvoke_mDAEF4BF76CDDDDDB8539AC46D039C2F0B8660F9B (void);
// 0x00000004 System.Single UnityEngine.Yoga.BaselineFunction::EndInvoke(System.IAsyncResult)
extern void BaselineFunction_EndInvoke_m410F2B18598638BEF28F275D98B166EDB7170D34 (void);
// 0x00000005 System.Void UnityEngine.Yoga.MeasureFunction::.ctor(System.Object,System.IntPtr)
extern void MeasureFunction__ctor_m7038E9D4841CB6E3D7FDF401F6FF179B9F2B35C4 (void);
// 0x00000006 UnityEngine.Yoga.YogaSize UnityEngine.Yoga.MeasureFunction::Invoke(UnityEngine.Yoga.YogaNode,System.Single,UnityEngine.Yoga.YogaMeasureMode,System.Single,UnityEngine.Yoga.YogaMeasureMode)
extern void MeasureFunction_Invoke_mEAAC465A891BDA61A7E1804BF40929FE9C34CB58 (void);
// 0x00000007 System.IAsyncResult UnityEngine.Yoga.MeasureFunction::BeginInvoke(UnityEngine.Yoga.YogaNode,System.Single,UnityEngine.Yoga.YogaMeasureMode,System.Single,UnityEngine.Yoga.YogaMeasureMode,System.AsyncCallback,System.Object)
extern void MeasureFunction_BeginInvoke_m57769D192664A7BEBA4F3FC778329F3270A6D0E4 (void);
// 0x00000008 UnityEngine.Yoga.YogaSize UnityEngine.Yoga.MeasureFunction::EndInvoke(System.IAsyncResult)
extern void MeasureFunction_EndInvoke_mAB0815931B1091B252D55E8763440AA1EC957AB8 (void);
// 0x00000009 System.Void UnityEngine.Yoga.Native::YGNodeMeasureInvoke(UnityEngine.Yoga.YogaNode,System.Single,UnityEngine.Yoga.YogaMeasureMode,System.Single,UnityEngine.Yoga.YogaMeasureMode,System.IntPtr)
extern void Native_YGNodeMeasureInvoke_mCB841C1598883440E10729768AA9D6E5563ECDC9 (void);
// 0x0000000A System.Void UnityEngine.Yoga.Native::YGNodeBaselineInvoke(UnityEngine.Yoga.YogaNode,System.Single,System.Single,System.IntPtr)
extern void Native_YGNodeBaselineInvoke_mAC5C386A3A6F434C316CC65E3E8AE95F139FDBD2 (void);
// 0x0000000B UnityEngine.Yoga.YogaSize UnityEngine.Yoga.YogaNode::MeasureInternal(UnityEngine.Yoga.YogaNode,System.Single,UnityEngine.Yoga.YogaMeasureMode,System.Single,UnityEngine.Yoga.YogaMeasureMode)
extern void YogaNode_MeasureInternal_m07C963C5FA5988382EF01EB5FC25F19067A15A3D (void);
// 0x0000000C System.Single UnityEngine.Yoga.YogaNode::BaselineInternal(UnityEngine.Yoga.YogaNode,System.Single,System.Single)
extern void YogaNode_BaselineInternal_mCF63312D71E6D0DF61C3B5DBD4312262100DA538 (void);
// 0x0000000D System.Void UnityEngine.UIElements.UIElementsRuntimeUtilityNative::RepaintOverlayPanels()
extern void UIElementsRuntimeUtilityNative_RepaintOverlayPanels_mE6773DF01922ADADD2D5861C9B7561D96B9FF069 (void);
// 0x0000000E System.Void UnityEngine.UIElements.UIElementsRuntimeUtilityNative::UpdateRuntimePanels()
extern void UIElementsRuntimeUtilityNative_UpdateRuntimePanels_mB58553B3A14684D00C310FD3FDE00F6F23E3B0F2 (void);
// 0x0000000F System.Void UnityEngine.UIElements.UIR.Utility::RaiseGraphicsResourcesRecreate(System.Boolean)
extern void Utility_RaiseGraphicsResourcesRecreate_m827C209CCF4819F5DFCA5299BBDFF8D2DC52B03B (void);
// 0x00000010 System.Void UnityEngine.UIElements.UIR.Utility::RaiseEngineUpdate()
extern void Utility_RaiseEngineUpdate_m368A10E2870099DF19444751D504AEDC6E331AC5 (void);
// 0x00000011 System.Void UnityEngine.UIElements.UIR.Utility::RaiseFlushPendingResources()
extern void Utility_RaiseFlushPendingResources_m07D0180349CE84C206366369F23193990F915F6F (void);
// 0x00000012 System.Void UnityEngine.UIElements.UIR.Utility::RaiseRegisterIntermediateRenderers(UnityEngine.Camera)
extern void Utility_RaiseRegisterIntermediateRenderers_m1680F4FA80537CBDB2FED1BB87F5A8E793BA9692 (void);
// 0x00000013 System.Void UnityEngine.UIElements.UIR.Utility::RaiseRenderNodeAdd(System.IntPtr)
extern void Utility_RaiseRenderNodeAdd_m46B097FD9E0BC626ADA2DAB65A91D2094E7F08E5 (void);
// 0x00000014 System.Void UnityEngine.UIElements.UIR.Utility::RaiseRenderNodeExecute(System.IntPtr)
extern void Utility_RaiseRenderNodeExecute_m266363F7C9FE19860296905202C5EE848677B325 (void);
// 0x00000015 System.Void UnityEngine.UIElements.UIR.Utility::RaiseRenderNodeCleanup(System.IntPtr)
extern void Utility_RaiseRenderNodeCleanup_mC6482CBE1B5625B0FB452756339CB3EDA159457A (void);
// 0x00000016 System.Void UnityEngine.UIElements.UIR.Utility::.cctor()
extern void Utility__cctor_mDC05CA6094751C209882F62D033C94CB9EEB07A7 (void);
static Il2CppMethodPointer s_methodPointers[22] = 
{
	BaselineFunction__ctor_m909E73515F8805EED0E15C6ED2CEC3A08CEB03B3,
	BaselineFunction_Invoke_mAEC462B19D69EAC84C6F4D13058EF3DA25BBE647,
	BaselineFunction_BeginInvoke_mDAEF4BF76CDDDDDB8539AC46D039C2F0B8660F9B,
	BaselineFunction_EndInvoke_m410F2B18598638BEF28F275D98B166EDB7170D34,
	MeasureFunction__ctor_m7038E9D4841CB6E3D7FDF401F6FF179B9F2B35C4,
	MeasureFunction_Invoke_mEAAC465A891BDA61A7E1804BF40929FE9C34CB58,
	MeasureFunction_BeginInvoke_m57769D192664A7BEBA4F3FC778329F3270A6D0E4,
	MeasureFunction_EndInvoke_mAB0815931B1091B252D55E8763440AA1EC957AB8,
	Native_YGNodeMeasureInvoke_mCB841C1598883440E10729768AA9D6E5563ECDC9,
	Native_YGNodeBaselineInvoke_mAC5C386A3A6F434C316CC65E3E8AE95F139FDBD2,
	YogaNode_MeasureInternal_m07C963C5FA5988382EF01EB5FC25F19067A15A3D,
	YogaNode_BaselineInternal_mCF63312D71E6D0DF61C3B5DBD4312262100DA538,
	UIElementsRuntimeUtilityNative_RepaintOverlayPanels_mE6773DF01922ADADD2D5861C9B7561D96B9FF069,
	UIElementsRuntimeUtilityNative_UpdateRuntimePanels_mB58553B3A14684D00C310FD3FDE00F6F23E3B0F2,
	Utility_RaiseGraphicsResourcesRecreate_m827C209CCF4819F5DFCA5299BBDFF8D2DC52B03B,
	Utility_RaiseEngineUpdate_m368A10E2870099DF19444751D504AEDC6E331AC5,
	Utility_RaiseFlushPendingResources_m07D0180349CE84C206366369F23193990F915F6F,
	Utility_RaiseRegisterIntermediateRenderers_m1680F4FA80537CBDB2FED1BB87F5A8E793BA9692,
	Utility_RaiseRenderNodeAdd_m46B097FD9E0BC626ADA2DAB65A91D2094E7F08E5,
	Utility_RaiseRenderNodeExecute_m266363F7C9FE19860296905202C5EE848677B325,
	Utility_RaiseRenderNodeCleanup_mC6482CBE1B5625B0FB452756339CB3EDA159457A,
	Utility__cctor_mDC05CA6094751C209882F62D033C94CB9EEB07A7,
};
static const int32_t s_InvokerIndices[22] = 
{
	2169,
	1219,
	344,
	3116,
	2169,
	445,
	123,
	3471,
	4338,
	4991,
	4595,
	5241,
	6289,
	6289,
	6208,
	6289,
	6289,
	6218,
	6215,
	6215,
	6215,
	6289,
};
extern const CustomAttributesCacheGenerator g_UnityEngine_UIElementsNativeModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_UIElementsNativeModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_UIElementsNativeModule_CodeGenModule = 
{
	"UnityEngine.UIElementsNativeModule.dll",
	22,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_UnityEngine_UIElementsNativeModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

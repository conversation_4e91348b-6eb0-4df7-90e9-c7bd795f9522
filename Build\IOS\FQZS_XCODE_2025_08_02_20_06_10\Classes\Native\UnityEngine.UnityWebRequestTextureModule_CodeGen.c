﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.IntPtr UnityEngine.Networking.DownloadHandlerTexture::Create(UnityEngine.Networking.DownloadHandlerTexture,System.Boolean)
extern void DownloadHandlerTexture_Create_mECE8B08E5A693F4E3F79773CD5AC58858A408A16 (void);
// 0x00000002 System.Void UnityEngine.Networking.DownloadHandlerTexture::InternalCreateTexture(System.Boolean)
extern void DownloadHandlerTexture_InternalCreateTexture_mB70E4684D082064A86FC6DF118CE9D5021F066A5 (void);
// 0x00000003 System.Void UnityEngine.Networking.DownloadHandlerTexture::.ctor(System.Boolean)
extern void DownloadHandlerTexture__ctor_m6EC47445C1283FBB2DBABE1FCC8D6A2ED31A77E8 (void);
// 0x00000004 System.Byte[] UnityEngine.Networking.DownloadHandlerTexture::GetData()
extern void DownloadHandlerTexture_GetData_m01C9E01D95D93023C278FE7E4F5275BF69A4F975 (void);
// 0x00000005 UnityEngine.Texture2D UnityEngine.Networking.DownloadHandlerTexture::get_texture()
extern void DownloadHandlerTexture_get_texture_m4A85047C91C2C15472D34043E1440845C87B709A (void);
// 0x00000006 UnityEngine.Texture2D UnityEngine.Networking.DownloadHandlerTexture::InternalGetTexture()
extern void DownloadHandlerTexture_InternalGetTexture_m3E4567A93CA63C69488E5632E55A7FEA366F24F8 (void);
// 0x00000007 UnityEngine.Texture2D UnityEngine.Networking.DownloadHandlerTexture::InternalGetTextureNative()
extern void DownloadHandlerTexture_InternalGetTextureNative_m010213A0592CE3F41D2637A4631987A6248E0E2F (void);
// 0x00000008 System.Void UnityEngine.Networking.DownloadHandlerTexture::ClearNativeTexture()
extern void DownloadHandlerTexture_ClearNativeTexture_m688EA377C62C9A98B56EA69E87C968404A790CC6 (void);
// 0x00000009 UnityEngine.Texture2D UnityEngine.Networking.DownloadHandlerTexture::GetContent(UnityEngine.Networking.UnityWebRequest)
extern void DownloadHandlerTexture_GetContent_m817C0C78EA6053B876803E6734AA90F5FEDA1851 (void);
static Il2CppMethodPointer s_methodPointers[9] = 
{
	DownloadHandlerTexture_Create_mECE8B08E5A693F4E3F79773CD5AC58858A408A16,
	DownloadHandlerTexture_InternalCreateTexture_mB70E4684D082064A86FC6DF118CE9D5021F066A5,
	DownloadHandlerTexture__ctor_m6EC47445C1283FBB2DBABE1FCC8D6A2ED31A77E8,
	DownloadHandlerTexture_GetData_m01C9E01D95D93023C278FE7E4F5275BF69A4F975,
	DownloadHandlerTexture_get_texture_m4A85047C91C2C15472D34043E1440845C87B709A,
	DownloadHandlerTexture_InternalGetTexture_m3E4567A93CA63C69488E5632E55A7FEA366F24F8,
	DownloadHandlerTexture_InternalGetTextureNative_m010213A0592CE3F41D2637A4631987A6248E0E2F,
	DownloadHandlerTexture_ClearNativeTexture_m688EA377C62C9A98B56EA69E87C968404A790CC6,
	DownloadHandlerTexture_GetContent_m817C0C78EA6053B876803E6734AA90F5FEDA1851,
};
static const int32_t s_InvokerIndices[9] = 
{
	5550,
	3276,
	3276,
	3916,
	3916,
	3916,
	3916,
	3967,
	6091,
};
extern const CustomAttributesCacheGenerator g_UnityEngine_UnityWebRequestTextureModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_UnityWebRequestTextureModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_UnityWebRequestTextureModule_CodeGenModule = 
{
	"UnityEngine.UnityWebRequestTextureModule.dll",
	9,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_UnityEngine_UnityWebRequestTextureModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void UnityEngine.ParticleSystem::Emit(UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Single,UnityEngine.Color32)
extern void ParticleSystem_Emit_mC489C467AAF3C3721AC3315AF78DC4CE469E7AAC (void);
// 0x00000002 System.Void UnityEngine.ParticleSystem::Emit(UnityEngine.ParticleSystem/Particle)
extern void ParticleSystem_Emit_mF1E108B9BF7E0094C35CF71870B5B0EA72ABB485 (void);
// 0x00000003 System.Single UnityEngine.ParticleSystem::get_startDelay()
extern void ParticleSystem_get_startDelay_m6661DA2328104EAAF1B3EED1A9AD5BA506B3DC57 (void);
// 0x00000004 System.Void UnityEngine.ParticleSystem::set_loop(System.Boolean)
extern void ParticleSystem_set_loop_mE7536AE1F42CCD5F8C410D719D3D80571C0054C6 (void);
// 0x00000005 System.Void UnityEngine.ParticleSystem::set_playOnAwake(System.Boolean)
extern void ParticleSystem_set_playOnAwake_m9AB94C2B3B742F4E3A1C7EFEBF30CDD33195D21D (void);
// 0x00000006 System.Single UnityEngine.ParticleSystem::get_duration()
extern void ParticleSystem_get_duration_m25C0E6C6B0EC084ED59977966AF2C2EDE529FA44 (void);
// 0x00000007 System.Void UnityEngine.ParticleSystem::set_emissionRate(System.Single)
extern void ParticleSystem_set_emissionRate_m62D12CC0B786ACD83632326F50A1082BE21EBA90 (void);
// 0x00000008 System.Single UnityEngine.ParticleSystem::get_startSpeed()
extern void ParticleSystem_get_startSpeed_mC6CEF0642452626DEE0687FE57CE96EE9DC65F41 (void);
// 0x00000009 System.Void UnityEngine.ParticleSystem::set_startSpeed(System.Single)
extern void ParticleSystem_set_startSpeed_m969B0FF3E7392F9E027D40C8BAAFF932171D9280 (void);
// 0x0000000A System.Single UnityEngine.ParticleSystem::get_startSize()
extern void ParticleSystem_get_startSize_m7327DF9701486C5476DB9E3296BD5692E2A7CB72 (void);
// 0x0000000B System.Void UnityEngine.ParticleSystem::set_startSize(System.Single)
extern void ParticleSystem_set_startSize_m2175FEE1F0ABA3F69C2DC4EC4D940E19A7FBEC6D (void);
// 0x0000000C System.Void UnityEngine.ParticleSystem::set_startColor(UnityEngine.Color)
extern void ParticleSystem_set_startColor_m58AE61985D5B25B108FB4A5E05D7DF4A0FFECAEC (void);
// 0x0000000D System.Single UnityEngine.ParticleSystem::get_startRotation()
extern void ParticleSystem_get_startRotation_m19EA2F6770518A7E2F9CB529943E23E083DEC5A0 (void);
// 0x0000000E System.Void UnityEngine.ParticleSystem::set_startRotation(System.Single)
extern void ParticleSystem_set_startRotation_mFF30EA836130E5105E8886B27F6051E87EE7DF49 (void);
// 0x0000000F System.Single UnityEngine.ParticleSystem::get_gravityModifier()
extern void ParticleSystem_get_gravityModifier_m8E0B14851B17FED84102ECC0A4871151C9DAF553 (void);
// 0x00000010 System.Void UnityEngine.ParticleSystem::set_gravityModifier(System.Single)
extern void ParticleSystem_set_gravityModifier_m0E1C7D97DD78C58931FB3E502B43920E4A810F1F (void);
// 0x00000011 System.Void UnityEngine.ParticleSystem::Play(System.Boolean)
extern void ParticleSystem_Play_m97D70BAF373265E633ACD91833E56D981B19958A (void);
// 0x00000012 System.Void UnityEngine.ParticleSystem::Play()
extern void ParticleSystem_Play_m28D27CC4CDC1D93195C75647E6F6DAECF8B6BC50 (void);
// 0x00000013 System.Void UnityEngine.ParticleSystem::Pause(System.Boolean)
extern void ParticleSystem_Pause_m116581497C92159D3210C733917CD2515399A0CD (void);
// 0x00000014 System.Void UnityEngine.ParticleSystem::Pause()
extern void ParticleSystem_Pause_mA5AE4D5A290E9DD75A0572738CB0910D6A7E2121 (void);
// 0x00000015 System.Void UnityEngine.ParticleSystem::Stop(System.Boolean,UnityEngine.ParticleSystemStopBehavior)
extern void ParticleSystem_Stop_m275B200BC21580C60987EC8FC8B2DD0FEB365C1A (void);
// 0x00000016 System.Void UnityEngine.ParticleSystem::Stop(System.Boolean)
extern void ParticleSystem_Stop_m5EC8B81A8BD1F8C90729E766789EE5D8D4EC64FB (void);
// 0x00000017 System.Void UnityEngine.ParticleSystem::Stop()
extern void ParticleSystem_Stop_m8CBF9268DE7B5A40952B4977462B857B5F5AFB9D (void);
// 0x00000018 System.Void UnityEngine.ParticleSystem::Clear(System.Boolean)
extern void ParticleSystem_Clear_m9E873134B8055ACFEE9DC66F7E4FABE178E27057 (void);
// 0x00000019 System.Boolean UnityEngine.ParticleSystem::IsAlive(System.Boolean)
extern void ParticleSystem_IsAlive_m43E2E84732EBDF5C4C9FD6575051A2A2B135BCF9 (void);
// 0x0000001A System.Boolean UnityEngine.ParticleSystem::IsAlive()
extern void ParticleSystem_IsAlive_m59D28AC79A1A5FD1D97523D83D687ACFA2510198 (void);
// 0x0000001B System.Void UnityEngine.ParticleSystem::Emit(System.Int32)
extern void ParticleSystem_Emit_m07EF0D2DA84EB04814DA7EE6B8618B008DE75F28 (void);
// 0x0000001C System.Void UnityEngine.ParticleSystem::Emit_Internal(System.Int32)
extern void ParticleSystem_Emit_Internal_m7C72C31F7F4875B54B00E255A450B045A4449646 (void);
// 0x0000001D System.Void UnityEngine.ParticleSystem::Emit(UnityEngine.ParticleSystem/EmitParams,System.Int32)
extern void ParticleSystem_Emit_m1598252E2EF701A5010EFA395A87368495E9F9F7 (void);
// 0x0000001E System.Void UnityEngine.ParticleSystem::EmitOld_Internal(UnityEngine.ParticleSystem/Particle&)
extern void ParticleSystem_EmitOld_Internal_m4F094DC523986298D5626F0F3F2335DFF596C993 (void);
// 0x0000001F UnityEngine.ParticleSystem/MainModule UnityEngine.ParticleSystem::get_main()
extern void ParticleSystem_get_main_m8F17DCC63679B15CE548BE83332FDB6635AE74A0 (void);
// 0x00000020 UnityEngine.ParticleSystem/EmissionModule UnityEngine.ParticleSystem::get_emission()
extern void ParticleSystem_get_emission_m0BAA1EDF775A8712DA7D62BF3C42A2B1A6C96CA1 (void);
// 0x00000021 System.Void UnityEngine.ParticleSystem::Emit_Injected(UnityEngine.ParticleSystem/EmitParams&,System.Int32)
extern void ParticleSystem_Emit_Injected_mC31E36D577A2D3135436438BFC27B6C76D9ADC72 (void);
// 0x00000022 System.Void UnityEngine.ParticleSystem/MainModule::.ctor(UnityEngine.ParticleSystem)
extern void MainModule__ctor_m34F626B568C6D3C80A036409049761C8316E6A76 (void);
// 0x00000023 System.Single UnityEngine.ParticleSystem/MainModule::get_duration()
extern void MainModule_get_duration_mD516595EFDD66C83A727BDD7EF495069B4EB6132 (void);
// 0x00000024 System.Void UnityEngine.ParticleSystem/MainModule::set_loop(System.Boolean)
extern void MainModule_set_loop_m9F43A092EFABBE92662DF9EDA73AF8D9C72DABB2 (void);
// 0x00000025 UnityEngine.ParticleSystem/MinMaxCurve UnityEngine.ParticleSystem/MainModule::get_startDelay()
extern void MainModule_get_startDelay_m6CA464DDA126D5A848376094643AEB097BDF204C (void);
// 0x00000026 System.Single UnityEngine.ParticleSystem/MainModule::get_startDelayMultiplier()
extern void MainModule_get_startDelayMultiplier_mC38890C8853463BB25F031BB8884F3D8B0AF581E (void);
// 0x00000027 System.Single UnityEngine.ParticleSystem/MainModule::get_startSpeedMultiplier()
extern void MainModule_get_startSpeedMultiplier_mE165D2B967AD8A8827D07DBD694F1C7167C80336 (void);
// 0x00000028 System.Void UnityEngine.ParticleSystem/MainModule::set_startSpeedMultiplier(System.Single)
extern void MainModule_set_startSpeedMultiplier_mA3C6B512351C3BFA8E64B40936B3353845A637F2 (void);
// 0x00000029 System.Single UnityEngine.ParticleSystem/MainModule::get_startSizeMultiplier()
extern void MainModule_get_startSizeMultiplier_m820351EE78DF731C1AFA8309ECB5ADF3E1166279 (void);
// 0x0000002A System.Void UnityEngine.ParticleSystem/MainModule::set_startSizeMultiplier(System.Single)
extern void MainModule_set_startSizeMultiplier_m231F400517E7F96EFF7C3C31E59B7B8DD0A1DB64 (void);
// 0x0000002B System.Single UnityEngine.ParticleSystem/MainModule::get_startRotationMultiplier()
extern void MainModule_get_startRotationMultiplier_m72C5648E64889B162553000EF3B7347F47B1F84A (void);
// 0x0000002C System.Void UnityEngine.ParticleSystem/MainModule::set_startRotationMultiplier(System.Single)
extern void MainModule_set_startRotationMultiplier_mB7085FB7FF45BDD4C71E08226C6D5F5635CB47EE (void);
// 0x0000002D System.Void UnityEngine.ParticleSystem/MainModule::set_startColor(UnityEngine.ParticleSystem/MinMaxGradient)
extern void MainModule_set_startColor_m19663CAE16C6A546B7BC4B949EBA0CCE3DD51A42 (void);
// 0x0000002E System.Single UnityEngine.ParticleSystem/MainModule::get_gravityModifierMultiplier()
extern void MainModule_get_gravityModifierMultiplier_m875B65CAEE457EB12A5CD763A5AA311DE014629A (void);
// 0x0000002F System.Void UnityEngine.ParticleSystem/MainModule::set_gravityModifierMultiplier(System.Single)
extern void MainModule_set_gravityModifierMultiplier_mBC64B6D0DEF548A94D0C0303855F43931A1F5684 (void);
// 0x00000030 System.Void UnityEngine.ParticleSystem/MainModule::set_playOnAwake(System.Boolean)
extern void MainModule_set_playOnAwake_m85E164C05D5C39B0B3C6139BE360BAB41B0D7FCF (void);
// 0x00000031 System.Single UnityEngine.ParticleSystem/MainModule::get_duration_Injected(UnityEngine.ParticleSystem/MainModule&)
extern void MainModule_get_duration_Injected_m08E86E140F6FE2EF74D5BCB6ED4EDA6D8CCCF9F2 (void);
// 0x00000032 System.Void UnityEngine.ParticleSystem/MainModule::set_loop_Injected(UnityEngine.ParticleSystem/MainModule&,System.Boolean)
extern void MainModule_set_loop_Injected_mD24050612DBFBD7B7A79B2F7760513B0EE9A7AF3 (void);
// 0x00000033 System.Void UnityEngine.ParticleSystem/MainModule::get_startDelay_Injected(UnityEngine.ParticleSystem/MainModule&,UnityEngine.ParticleSystem/MinMaxCurve&)
extern void MainModule_get_startDelay_Injected_mDD6C2E7E59BC1B25E80B95C4DE393F8DE8DFF144 (void);
// 0x00000034 System.Single UnityEngine.ParticleSystem/MainModule::get_startDelayMultiplier_Injected(UnityEngine.ParticleSystem/MainModule&)
extern void MainModule_get_startDelayMultiplier_Injected_mB24152E26E9659C193D865427EF8E7A3EBC641AF (void);
// 0x00000035 System.Single UnityEngine.ParticleSystem/MainModule::get_startSpeedMultiplier_Injected(UnityEngine.ParticleSystem/MainModule&)
extern void MainModule_get_startSpeedMultiplier_Injected_mC97A0366BFF2755423C31B972501A97B50FC28DB (void);
// 0x00000036 System.Void UnityEngine.ParticleSystem/MainModule::set_startSpeedMultiplier_Injected(UnityEngine.ParticleSystem/MainModule&,System.Single)
extern void MainModule_set_startSpeedMultiplier_Injected_mEED8B70DE56C7635854F9ECCA6C087DE203FACDF (void);
// 0x00000037 System.Single UnityEngine.ParticleSystem/MainModule::get_startSizeMultiplier_Injected(UnityEngine.ParticleSystem/MainModule&)
extern void MainModule_get_startSizeMultiplier_Injected_mC2BE6C1A12FE52ACCFFD66C2311D1212880DDF38 (void);
// 0x00000038 System.Void UnityEngine.ParticleSystem/MainModule::set_startSizeMultiplier_Injected(UnityEngine.ParticleSystem/MainModule&,System.Single)
extern void MainModule_set_startSizeMultiplier_Injected_mA84E13C555D78D24011A2CC67C5C2A08FC87517C (void);
// 0x00000039 System.Single UnityEngine.ParticleSystem/MainModule::get_startRotationMultiplier_Injected(UnityEngine.ParticleSystem/MainModule&)
extern void MainModule_get_startRotationMultiplier_Injected_mBD530D7A9173E37BA81DBE9B51EB33792C21EC29 (void);
// 0x0000003A System.Void UnityEngine.ParticleSystem/MainModule::set_startRotationMultiplier_Injected(UnityEngine.ParticleSystem/MainModule&,System.Single)
extern void MainModule_set_startRotationMultiplier_Injected_m4DC7683918E19D3CAC8489EB673C9511DA2ABFFA (void);
// 0x0000003B System.Void UnityEngine.ParticleSystem/MainModule::set_startColor_Injected(UnityEngine.ParticleSystem/MainModule&,UnityEngine.ParticleSystem/MinMaxGradient&)
extern void MainModule_set_startColor_Injected_m21E6A169EAB40535D1AC4F2A992EEF65FC06166C (void);
// 0x0000003C System.Single UnityEngine.ParticleSystem/MainModule::get_gravityModifierMultiplier_Injected(UnityEngine.ParticleSystem/MainModule&)
extern void MainModule_get_gravityModifierMultiplier_Injected_m283382932E31E04E4BE277A87416136F5D9BEC3B (void);
// 0x0000003D System.Void UnityEngine.ParticleSystem/MainModule::set_gravityModifierMultiplier_Injected(UnityEngine.ParticleSystem/MainModule&,System.Single)
extern void MainModule_set_gravityModifierMultiplier_Injected_m17E164B211C41FDCA7DB70F8979A5BB34A9B46E2 (void);
// 0x0000003E System.Void UnityEngine.ParticleSystem/MainModule::set_playOnAwake_Injected(UnityEngine.ParticleSystem/MainModule&,System.Boolean)
extern void MainModule_set_playOnAwake_Injected_m6B136779CEDF3E6A3F3AE6C898387ED93F9DE9E9 (void);
// 0x0000003F System.Void UnityEngine.ParticleSystem/EmissionModule::.ctor(UnityEngine.ParticleSystem)
extern void EmissionModule__ctor_m9DFD2A0BB8BFAD2191C54E6627FC3B0E641EDD51 (void);
// 0x00000040 System.Void UnityEngine.ParticleSystem/EmissionModule::set_rateOverTime(UnityEngine.ParticleSystem/MinMaxCurve)
extern void EmissionModule_set_rateOverTime_m4EE4643D8E8B8BEC37BCB16DB76C9B8E4E4C8F48 (void);
// 0x00000041 System.Void UnityEngine.ParticleSystem/EmissionModule::set_rateOverTime_Injected(UnityEngine.ParticleSystem/EmissionModule&,UnityEngine.ParticleSystem/MinMaxCurve&)
extern void EmissionModule_set_rateOverTime_Injected_m8514936A577D87FD3D5CC5B50F8E07C5F96B7F9B (void);
// 0x00000042 System.Void UnityEngine.ParticleSystem/Particle::set_lifetime(System.Single)
extern void Particle_set_lifetime_mCE97F9D17D1E660DB9D9F3244150CB6624A74DC3 (void);
// 0x00000043 System.Void UnityEngine.ParticleSystem/Particle::set_position(UnityEngine.Vector3)
extern void Particle_set_position_mB749E41CD3C4C6DF501C0D68B6211CF7E2217FC2 (void);
// 0x00000044 System.Void UnityEngine.ParticleSystem/Particle::set_velocity(UnityEngine.Vector3)
extern void Particle_set_velocity_m4894BD6A75E7A1FCD433927F93396AA29A59709B (void);
// 0x00000045 System.Void UnityEngine.ParticleSystem/Particle::set_remainingLifetime(System.Single)
extern void Particle_set_remainingLifetime_mE521DF9387892C00B3F21D4F76F3A55E77AFC6BE (void);
// 0x00000046 System.Void UnityEngine.ParticleSystem/Particle::set_startLifetime(System.Single)
extern void Particle_set_startLifetime_m7AC09262BBFE8818EA6B61BDB62264184CD27A82 (void);
// 0x00000047 System.Void UnityEngine.ParticleSystem/Particle::set_startColor(UnityEngine.Color32)
extern void Particle_set_startColor_mA7B0363E82B7A7CBB1C83F8C5D49FB7B7AF75554 (void);
// 0x00000048 System.Void UnityEngine.ParticleSystem/Particle::set_randomSeed(System.UInt32)
extern void Particle_set_randomSeed_m8FD7A4DB7F8E7EBDEF2C51A28197F8D9D7CB6E04 (void);
// 0x00000049 System.Void UnityEngine.ParticleSystem/Particle::set_startSize(System.Single)
extern void Particle_set_startSize_m7CDEE5B620B3D26B4CC5C1DA7C6E24ACCCF64466 (void);
// 0x0000004A System.Void UnityEngine.ParticleSystem/Particle::set_rotation3D(UnityEngine.Vector3)
extern void Particle_set_rotation3D_m0F402760524A81307FA4940751751C44DF2F77D0 (void);
// 0x0000004B System.Void UnityEngine.ParticleSystem/Particle::set_angularVelocity3D(UnityEngine.Vector3)
extern void Particle_set_angularVelocity3D_mE9A9544DF33CD0CCF4F1CA14994A2C436E4DF1F8 (void);
// 0x0000004C System.Void UnityEngine.ParticleSystem/MinMaxCurve::.ctor(System.Single)
extern void MinMaxCurve__ctor_mF14A2DBABFDCF1E855911241A555949CAF212AE3 (void);
// 0x0000004D System.Single UnityEngine.ParticleSystem/MinMaxCurve::get_constantMax()
extern void MinMaxCurve_get_constantMax_m602968BE6C8603F219BB3279114F0AF61358DD27 (void);
// 0x0000004E UnityEngine.ParticleSystem/MinMaxCurve UnityEngine.ParticleSystem/MinMaxCurve::op_Implicit(System.Single)
extern void MinMaxCurve_op_Implicit_mE0EB3651B6AFFB3E9776B49880C50BEF96059173 (void);
// 0x0000004F System.Void UnityEngine.ParticleSystem/MinMaxGradient::.ctor(UnityEngine.Color)
extern void MinMaxGradient__ctor_m8D0DE766F67FF065E4C2B6DCD841752C447977AA (void);
// 0x00000050 UnityEngine.ParticleSystem/MinMaxGradient UnityEngine.ParticleSystem/MinMaxGradient::op_Implicit(UnityEngine.Color)
extern void MinMaxGradient_op_Implicit_m3C6794BDFE2DA44AAAADF37A7E9E6493295C1A8B (void);
// 0x00000051 System.Int32 UnityEngine.ParticleSystemRenderer::GetMeshes(UnityEngine.Mesh[])
extern void ParticleSystemRenderer_GetMeshes_m1B36A6BFF152AAE5520D727976E3DA26722C3A75 (void);
static Il2CppMethodPointer s_methodPointers[81] = 
{
	ParticleSystem_Emit_mC489C467AAF3C3721AC3315AF78DC4CE469E7AAC,
	ParticleSystem_Emit_mF1E108B9BF7E0094C35CF71870B5B0EA72ABB485,
	ParticleSystem_get_startDelay_m6661DA2328104EAAF1B3EED1A9AD5BA506B3DC57,
	ParticleSystem_set_loop_mE7536AE1F42CCD5F8C410D719D3D80571C0054C6,
	ParticleSystem_set_playOnAwake_m9AB94C2B3B742F4E3A1C7EFEBF30CDD33195D21D,
	ParticleSystem_get_duration_m25C0E6C6B0EC084ED59977966AF2C2EDE529FA44,
	ParticleSystem_set_emissionRate_m62D12CC0B786ACD83632326F50A1082BE21EBA90,
	ParticleSystem_get_startSpeed_mC6CEF0642452626DEE0687FE57CE96EE9DC65F41,
	ParticleSystem_set_startSpeed_m969B0FF3E7392F9E027D40C8BAAFF932171D9280,
	ParticleSystem_get_startSize_m7327DF9701486C5476DB9E3296BD5692E2A7CB72,
	ParticleSystem_set_startSize_m2175FEE1F0ABA3F69C2DC4EC4D940E19A7FBEC6D,
	ParticleSystem_set_startColor_m58AE61985D5B25B108FB4A5E05D7DF4A0FFECAEC,
	ParticleSystem_get_startRotation_m19EA2F6770518A7E2F9CB529943E23E083DEC5A0,
	ParticleSystem_set_startRotation_mFF30EA836130E5105E8886B27F6051E87EE7DF49,
	ParticleSystem_get_gravityModifier_m8E0B14851B17FED84102ECC0A4871151C9DAF553,
	ParticleSystem_set_gravityModifier_m0E1C7D97DD78C58931FB3E502B43920E4A810F1F,
	ParticleSystem_Play_m97D70BAF373265E633ACD91833E56D981B19958A,
	ParticleSystem_Play_m28D27CC4CDC1D93195C75647E6F6DAECF8B6BC50,
	ParticleSystem_Pause_m116581497C92159D3210C733917CD2515399A0CD,
	ParticleSystem_Pause_mA5AE4D5A290E9DD75A0572738CB0910D6A7E2121,
	ParticleSystem_Stop_m275B200BC21580C60987EC8FC8B2DD0FEB365C1A,
	ParticleSystem_Stop_m5EC8B81A8BD1F8C90729E766789EE5D8D4EC64FB,
	ParticleSystem_Stop_m8CBF9268DE7B5A40952B4977462B857B5F5AFB9D,
	ParticleSystem_Clear_m9E873134B8055ACFEE9DC66F7E4FABE178E27057,
	ParticleSystem_IsAlive_m43E2E84732EBDF5C4C9FD6575051A2A2B135BCF9,
	ParticleSystem_IsAlive_m59D28AC79A1A5FD1D97523D83D687ACFA2510198,
	ParticleSystem_Emit_m07EF0D2DA84EB04814DA7EE6B8618B008DE75F28,
	ParticleSystem_Emit_Internal_m7C72C31F7F4875B54B00E255A450B045A4449646,
	ParticleSystem_Emit_m1598252E2EF701A5010EFA395A87368495E9F9F7,
	ParticleSystem_EmitOld_Internal_m4F094DC523986298D5626F0F3F2335DFF596C993,
	ParticleSystem_get_main_m8F17DCC63679B15CE548BE83332FDB6635AE74A0,
	ParticleSystem_get_emission_m0BAA1EDF775A8712DA7D62BF3C42A2B1A6C96CA1,
	ParticleSystem_Emit_Injected_mC31E36D577A2D3135436438BFC27B6C76D9ADC72,
	MainModule__ctor_m34F626B568C6D3C80A036409049761C8316E6A76,
	MainModule_get_duration_mD516595EFDD66C83A727BDD7EF495069B4EB6132,
	MainModule_set_loop_m9F43A092EFABBE92662DF9EDA73AF8D9C72DABB2,
	MainModule_get_startDelay_m6CA464DDA126D5A848376094643AEB097BDF204C,
	MainModule_get_startDelayMultiplier_mC38890C8853463BB25F031BB8884F3D8B0AF581E,
	MainModule_get_startSpeedMultiplier_mE165D2B967AD8A8827D07DBD694F1C7167C80336,
	MainModule_set_startSpeedMultiplier_mA3C6B512351C3BFA8E64B40936B3353845A637F2,
	MainModule_get_startSizeMultiplier_m820351EE78DF731C1AFA8309ECB5ADF3E1166279,
	MainModule_set_startSizeMultiplier_m231F400517E7F96EFF7C3C31E59B7B8DD0A1DB64,
	MainModule_get_startRotationMultiplier_m72C5648E64889B162553000EF3B7347F47B1F84A,
	MainModule_set_startRotationMultiplier_mB7085FB7FF45BDD4C71E08226C6D5F5635CB47EE,
	MainModule_set_startColor_m19663CAE16C6A546B7BC4B949EBA0CCE3DD51A42,
	MainModule_get_gravityModifierMultiplier_m875B65CAEE457EB12A5CD763A5AA311DE014629A,
	MainModule_set_gravityModifierMultiplier_mBC64B6D0DEF548A94D0C0303855F43931A1F5684,
	MainModule_set_playOnAwake_m85E164C05D5C39B0B3C6139BE360BAB41B0D7FCF,
	MainModule_get_duration_Injected_m08E86E140F6FE2EF74D5BCB6ED4EDA6D8CCCF9F2,
	MainModule_set_loop_Injected_mD24050612DBFBD7B7A79B2F7760513B0EE9A7AF3,
	MainModule_get_startDelay_Injected_mDD6C2E7E59BC1B25E80B95C4DE393F8DE8DFF144,
	MainModule_get_startDelayMultiplier_Injected_mB24152E26E9659C193D865427EF8E7A3EBC641AF,
	MainModule_get_startSpeedMultiplier_Injected_mC97A0366BFF2755423C31B972501A97B50FC28DB,
	MainModule_set_startSpeedMultiplier_Injected_mEED8B70DE56C7635854F9ECCA6C087DE203FACDF,
	MainModule_get_startSizeMultiplier_Injected_mC2BE6C1A12FE52ACCFFD66C2311D1212880DDF38,
	MainModule_set_startSizeMultiplier_Injected_mA84E13C555D78D24011A2CC67C5C2A08FC87517C,
	MainModule_get_startRotationMultiplier_Injected_mBD530D7A9173E37BA81DBE9B51EB33792C21EC29,
	MainModule_set_startRotationMultiplier_Injected_m4DC7683918E19D3CAC8489EB673C9511DA2ABFFA,
	MainModule_set_startColor_Injected_m21E6A169EAB40535D1AC4F2A992EEF65FC06166C,
	MainModule_get_gravityModifierMultiplier_Injected_m283382932E31E04E4BE277A87416136F5D9BEC3B,
	MainModule_set_gravityModifierMultiplier_Injected_m17E164B211C41FDCA7DB70F8979A5BB34A9B46E2,
	MainModule_set_playOnAwake_Injected_m6B136779CEDF3E6A3F3AE6C898387ED93F9DE9E9,
	EmissionModule__ctor_m9DFD2A0BB8BFAD2191C54E6627FC3B0E641EDD51,
	EmissionModule_set_rateOverTime_m4EE4643D8E8B8BEC37BCB16DB76C9B8E4E4C8F48,
	EmissionModule_set_rateOverTime_Injected_m8514936A577D87FD3D5CC5B50F8E07C5F96B7F9B,
	Particle_set_lifetime_mCE97F9D17D1E660DB9D9F3244150CB6624A74DC3,
	Particle_set_position_mB749E41CD3C4C6DF501C0D68B6211CF7E2217FC2,
	Particle_set_velocity_m4894BD6A75E7A1FCD433927F93396AA29A59709B,
	Particle_set_remainingLifetime_mE521DF9387892C00B3F21D4F76F3A55E77AFC6BE,
	Particle_set_startLifetime_m7AC09262BBFE8818EA6B61BDB62264184CD27A82,
	Particle_set_startColor_mA7B0363E82B7A7CBB1C83F8C5D49FB7B7AF75554,
	Particle_set_randomSeed_m8FD7A4DB7F8E7EBDEF2C51A28197F8D9D7CB6E04,
	Particle_set_startSize_m7CDEE5B620B3D26B4CC5C1DA7C6E24ACCCF64466,
	Particle_set_rotation3D_m0F402760524A81307FA4940751751C44DF2F77D0,
	Particle_set_angularVelocity3D_mE9A9544DF33CD0CCF4F1CA14994A2C436E4DF1F8,
	MinMaxCurve__ctor_mF14A2DBABFDCF1E855911241A555949CAF212AE3,
	MinMaxCurve_get_constantMax_m602968BE6C8603F219BB3279114F0AF61358DD27,
	MinMaxCurve_op_Implicit_mE0EB3651B6AFFB3E9776B49880C50BEF96059173,
	MinMaxGradient__ctor_m8D0DE766F67FF065E4C2B6DCD841752C447977AA,
	MinMaxGradient_op_Implicit_m3C6794BDFE2DA44AAAADF37A7E9E6493295C1A8B,
	ParticleSystemRenderer_GetMeshes_m1B36A6BFF152AAE5520D727976E3DA26722C3A75,
};
extern void MainModule__ctor_m34F626B568C6D3C80A036409049761C8316E6A76_AdjustorThunk (void);
extern void MainModule_get_duration_mD516595EFDD66C83A727BDD7EF495069B4EB6132_AdjustorThunk (void);
extern void MainModule_set_loop_m9F43A092EFABBE92662DF9EDA73AF8D9C72DABB2_AdjustorThunk (void);
extern void MainModule_get_startDelay_m6CA464DDA126D5A848376094643AEB097BDF204C_AdjustorThunk (void);
extern void MainModule_get_startDelayMultiplier_mC38890C8853463BB25F031BB8884F3D8B0AF581E_AdjustorThunk (void);
extern void MainModule_get_startSpeedMultiplier_mE165D2B967AD8A8827D07DBD694F1C7167C80336_AdjustorThunk (void);
extern void MainModule_set_startSpeedMultiplier_mA3C6B512351C3BFA8E64B40936B3353845A637F2_AdjustorThunk (void);
extern void MainModule_get_startSizeMultiplier_m820351EE78DF731C1AFA8309ECB5ADF3E1166279_AdjustorThunk (void);
extern void MainModule_set_startSizeMultiplier_m231F400517E7F96EFF7C3C31E59B7B8DD0A1DB64_AdjustorThunk (void);
extern void MainModule_get_startRotationMultiplier_m72C5648E64889B162553000EF3B7347F47B1F84A_AdjustorThunk (void);
extern void MainModule_set_startRotationMultiplier_mB7085FB7FF45BDD4C71E08226C6D5F5635CB47EE_AdjustorThunk (void);
extern void MainModule_set_startColor_m19663CAE16C6A546B7BC4B949EBA0CCE3DD51A42_AdjustorThunk (void);
extern void MainModule_get_gravityModifierMultiplier_m875B65CAEE457EB12A5CD763A5AA311DE014629A_AdjustorThunk (void);
extern void MainModule_set_gravityModifierMultiplier_mBC64B6D0DEF548A94D0C0303855F43931A1F5684_AdjustorThunk (void);
extern void MainModule_set_playOnAwake_m85E164C05D5C39B0B3C6139BE360BAB41B0D7FCF_AdjustorThunk (void);
extern void EmissionModule__ctor_m9DFD2A0BB8BFAD2191C54E6627FC3B0E641EDD51_AdjustorThunk (void);
extern void EmissionModule_set_rateOverTime_m4EE4643D8E8B8BEC37BCB16DB76C9B8E4E4C8F48_AdjustorThunk (void);
extern void Particle_set_lifetime_mCE97F9D17D1E660DB9D9F3244150CB6624A74DC3_AdjustorThunk (void);
extern void Particle_set_position_mB749E41CD3C4C6DF501C0D68B6211CF7E2217FC2_AdjustorThunk (void);
extern void Particle_set_velocity_m4894BD6A75E7A1FCD433927F93396AA29A59709B_AdjustorThunk (void);
extern void Particle_set_remainingLifetime_mE521DF9387892C00B3F21D4F76F3A55E77AFC6BE_AdjustorThunk (void);
extern void Particle_set_startLifetime_m7AC09262BBFE8818EA6B61BDB62264184CD27A82_AdjustorThunk (void);
extern void Particle_set_startColor_mA7B0363E82B7A7CBB1C83F8C5D49FB7B7AF75554_AdjustorThunk (void);
extern void Particle_set_randomSeed_m8FD7A4DB7F8E7EBDEF2C51A28197F8D9D7CB6E04_AdjustorThunk (void);
extern void Particle_set_startSize_m7CDEE5B620B3D26B4CC5C1DA7C6E24ACCCF64466_AdjustorThunk (void);
extern void Particle_set_rotation3D_m0F402760524A81307FA4940751751C44DF2F77D0_AdjustorThunk (void);
extern void Particle_set_angularVelocity3D_mE9A9544DF33CD0CCF4F1CA14994A2C436E4DF1F8_AdjustorThunk (void);
extern void MinMaxCurve__ctor_mF14A2DBABFDCF1E855911241A555949CAF212AE3_AdjustorThunk (void);
extern void MinMaxCurve_get_constantMax_m602968BE6C8603F219BB3279114F0AF61358DD27_AdjustorThunk (void);
extern void MinMaxGradient__ctor_m8D0DE766F67FF065E4C2B6DCD841752C447977AA_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[30] = 
{
	{ 0x06000022, MainModule__ctor_m34F626B568C6D3C80A036409049761C8316E6A76_AdjustorThunk },
	{ 0x06000023, MainModule_get_duration_mD516595EFDD66C83A727BDD7EF495069B4EB6132_AdjustorThunk },
	{ 0x06000024, MainModule_set_loop_m9F43A092EFABBE92662DF9EDA73AF8D9C72DABB2_AdjustorThunk },
	{ 0x06000025, MainModule_get_startDelay_m6CA464DDA126D5A848376094643AEB097BDF204C_AdjustorThunk },
	{ 0x06000026, MainModule_get_startDelayMultiplier_mC38890C8853463BB25F031BB8884F3D8B0AF581E_AdjustorThunk },
	{ 0x06000027, MainModule_get_startSpeedMultiplier_mE165D2B967AD8A8827D07DBD694F1C7167C80336_AdjustorThunk },
	{ 0x06000028, MainModule_set_startSpeedMultiplier_mA3C6B512351C3BFA8E64B40936B3353845A637F2_AdjustorThunk },
	{ 0x06000029, MainModule_get_startSizeMultiplier_m820351EE78DF731C1AFA8309ECB5ADF3E1166279_AdjustorThunk },
	{ 0x0600002A, MainModule_set_startSizeMultiplier_m231F400517E7F96EFF7C3C31E59B7B8DD0A1DB64_AdjustorThunk },
	{ 0x0600002B, MainModule_get_startRotationMultiplier_m72C5648E64889B162553000EF3B7347F47B1F84A_AdjustorThunk },
	{ 0x0600002C, MainModule_set_startRotationMultiplier_mB7085FB7FF45BDD4C71E08226C6D5F5635CB47EE_AdjustorThunk },
	{ 0x0600002D, MainModule_set_startColor_m19663CAE16C6A546B7BC4B949EBA0CCE3DD51A42_AdjustorThunk },
	{ 0x0600002E, MainModule_get_gravityModifierMultiplier_m875B65CAEE457EB12A5CD763A5AA311DE014629A_AdjustorThunk },
	{ 0x0600002F, MainModule_set_gravityModifierMultiplier_mBC64B6D0DEF548A94D0C0303855F43931A1F5684_AdjustorThunk },
	{ 0x06000030, MainModule_set_playOnAwake_m85E164C05D5C39B0B3C6139BE360BAB41B0D7FCF_AdjustorThunk },
	{ 0x0600003F, EmissionModule__ctor_m9DFD2A0BB8BFAD2191C54E6627FC3B0E641EDD51_AdjustorThunk },
	{ 0x06000040, EmissionModule_set_rateOverTime_m4EE4643D8E8B8BEC37BCB16DB76C9B8E4E4C8F48_AdjustorThunk },
	{ 0x06000042, Particle_set_lifetime_mCE97F9D17D1E660DB9D9F3244150CB6624A74DC3_AdjustorThunk },
	{ 0x06000043, Particle_set_position_mB749E41CD3C4C6DF501C0D68B6211CF7E2217FC2_AdjustorThunk },
	{ 0x06000044, Particle_set_velocity_m4894BD6A75E7A1FCD433927F93396AA29A59709B_AdjustorThunk },
	{ 0x06000045, Particle_set_remainingLifetime_mE521DF9387892C00B3F21D4F76F3A55E77AFC6BE_AdjustorThunk },
	{ 0x06000046, Particle_set_startLifetime_m7AC09262BBFE8818EA6B61BDB62264184CD27A82_AdjustorThunk },
	{ 0x06000047, Particle_set_startColor_mA7B0363E82B7A7CBB1C83F8C5D49FB7B7AF75554_AdjustorThunk },
	{ 0x06000048, Particle_set_randomSeed_m8FD7A4DB7F8E7EBDEF2C51A28197F8D9D7CB6E04_AdjustorThunk },
	{ 0x06000049, Particle_set_startSize_m7CDEE5B620B3D26B4CC5C1DA7C6E24ACCCF64466_AdjustorThunk },
	{ 0x0600004A, Particle_set_rotation3D_m0F402760524A81307FA4940751751C44DF2F77D0_AdjustorThunk },
	{ 0x0600004B, Particle_set_angularVelocity3D_mE9A9544DF33CD0CCF4F1CA14994A2C436E4DF1F8_AdjustorThunk },
	{ 0x0600004C, MinMaxCurve__ctor_mF14A2DBABFDCF1E855911241A555949CAF212AE3_AdjustorThunk },
	{ 0x0600004D, MinMaxCurve_get_constantMax_m602968BE6C8603F219BB3279114F0AF61358DD27_AdjustorThunk },
	{ 0x0600004F, MinMaxGradient__ctor_m8D0DE766F67FF065E4C2B6DCD841752C447977AA_AdjustorThunk },
};
static const int32_t s_InvokerIndices[81] = 
{
	444,
	3421,
	3951,
	3276,
	3276,
	3951,
	3380,
	3951,
	3380,
	3951,
	3380,
	3281,
	3951,
	3380,
	3951,
	3380,
	3276,
	3967,
	3276,
	3967,
	1832,
	3276,
	3967,
	3276,
	2534,
	3863,
	3313,
	3313,
	2229,
	3255,
	3989,
	3988,
	1824,
	3348,
	3951,
	3276,
	3990,
	3951,
	3951,
	3380,
	3951,
	3380,
	3951,
	3380,
	3420,
	3951,
	3380,
	3276,
	6130,
	5730,
	5729,
	6130,
	6130,
	5734,
	6130,
	5734,
	6130,
	5734,
	5729,
	6130,
	5734,
	5730,
	3348,
	3419,
	5729,
	3380,
	3398,
	3398,
	3380,
	3380,
	3283,
	3392,
	3380,
	3398,
	3398,
	3380,
	3951,
	6238,
	3281,
	6239,
	2901,
};
extern const CustomAttributesCacheGenerator g_UnityEngine_ParticleSystemModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_ParticleSystemModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_ParticleSystemModule_CodeGenModule = 
{
	"UnityEngine.ParticleSystemModule.dll",
	81,
	s_methodPointers,
	30,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_UnityEngine_ParticleSystemModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

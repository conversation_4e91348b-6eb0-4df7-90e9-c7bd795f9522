﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.String UnityEngineInternal.WebRequestUtils::RedirectTo(System.String,System.String)
extern void WebRequestUtils_RedirectTo_m302A7CAA44374907C165F4553CEA2309D689B189 (void);
// 0x00000002 System.String UnityEngineInternal.WebRequestUtils::MakeInitialUrl(System.String,System.String)
extern void WebRequestUtils_MakeInitialUrl_mD68C0247D99B4F6049A39AFE93CBFE4AC128F9E7 (void);
// 0x00000003 System.String UnityEngineInternal.WebRequestUtils::MakeUriString(System.Uri,System.String,System.Boolean)
extern void WebRequestUtils_MakeUriString_m3F74B391CA0D761E90C22E2E938293985665869A (void);
// 0x00000004 System.String UnityEngineInternal.WebRequestUtils::URLDecode(System.String)
extern void WebRequestUtils_URLDecode_mC3C84F4BE4A5F2D8FB7A3D835493B89A4CB2BD40 (void);
// 0x00000005 System.Void UnityEngineInternal.WebRequestUtils::.cctor()
extern void WebRequestUtils__cctor_mCE956748A8F3B1FA48893A7D263C466082815EEB (void);
// 0x00000006 System.Text.Encoding UnityEngine.WWWForm::get_DefaultEncoding()
extern void WWWForm_get_DefaultEncoding_m594475A7B07B708D7890FA67E33099EECF6E5D07 (void);
// 0x00000007 System.Void UnityEngine.WWWForm::.ctor()
extern void WWWForm__ctor_mA0987933892AF6FA9E01603B214E86EC52F6B055 (void);
// 0x00000008 System.Void UnityEngine.WWWForm::AddField(System.String,System.String)
extern void WWWForm_AddField_m8ACDB7B2124FA6EAD7FC120BB469C6352C7B7696 (void);
// 0x00000009 System.Void UnityEngine.WWWForm::AddField(System.String,System.String,System.Text.Encoding)
extern void WWWForm_AddField_m71508C882B6F77219D3430D8992DC3C340492976 (void);
// 0x0000000A System.Void UnityEngine.WWWForm::AddField(System.String,System.Int32)
extern void WWWForm_AddField_m20BED9E7C237D585E5D040A5D815D9C7DC16990C (void);
// 0x0000000B System.Collections.Generic.Dictionary`2<System.String,System.String> UnityEngine.WWWForm::get_headers()
extern void WWWForm_get_headers_m9F6A4222926792B110E9821AF5B23FF3FB9BD45A (void);
// 0x0000000C System.Byte[] UnityEngine.WWWForm::get_data()
extern void WWWForm_get_data_mCCE0C63DF2E0CA4F831F9AB78D6111BB8C669177 (void);
// 0x0000000D System.Byte UnityEngine.WWWTranscoder::Hex2Byte(System.Byte[],System.Int32)
extern void WWWTranscoder_Hex2Byte_m443DBE774241073A3B5AF834282856B675D5606E (void);
// 0x0000000E System.Byte[] UnityEngine.WWWTranscoder::Byte2Hex(System.Byte,System.Byte[])
extern void WWWTranscoder_Byte2Hex_m765D06937CF87AA809079FB827778888AA00F468 (void);
// 0x0000000F System.Byte[] UnityEngine.WWWTranscoder::URLEncode(System.Byte[])
extern void WWWTranscoder_URLEncode_m6F6AD6415A4BA795237F16161A043DBE7EFC2D50 (void);
// 0x00000010 System.Byte[] UnityEngine.WWWTranscoder::DataEncode(System.Byte[])
extern void WWWTranscoder_DataEncode_m2685B211BE3828A2C3F9923DC3554C741F484802 (void);
// 0x00000011 System.String UnityEngine.WWWTranscoder::QPEncode(System.String,System.Text.Encoding)
extern void WWWTranscoder_QPEncode_m15617417C9FF5577B8CEA69E7CE859254E049D7B (void);
// 0x00000012 System.Byte[] UnityEngine.WWWTranscoder::Encode(System.Byte[],System.Byte,System.Byte[],System.Byte[],System.Boolean)
extern void WWWTranscoder_Encode_m573CD30E0665757A9F1B2A03C404D1F5669CF5A8 (void);
// 0x00000013 System.Boolean UnityEngine.WWWTranscoder::ByteArrayContains(System.Byte[],System.Byte)
extern void WWWTranscoder_ByteArrayContains_mC2755D2E6DA603C283BD0DB8C6AEE8C72933DE53 (void);
// 0x00000014 System.Byte[] UnityEngine.WWWTranscoder::URLDecode(System.Byte[])
extern void WWWTranscoder_URLDecode_mF3500816E9A9E97BB5651FFACC80E41EFCAE22F4 (void);
// 0x00000015 System.Boolean UnityEngine.WWWTranscoder::ByteSubArrayEquals(System.Byte[],System.Int32,System.Byte[])
extern void WWWTranscoder_ByteSubArrayEquals_m8D67734F39C3F736E34FB8FFC39A638E5702AA6B (void);
// 0x00000016 System.Byte[] UnityEngine.WWWTranscoder::Decode(System.Byte[],System.Byte,System.Byte[])
extern void WWWTranscoder_Decode_m83FC67B11B0DF76C1C3C287737D182A95AE3E650 (void);
// 0x00000017 System.Boolean UnityEngine.WWWTranscoder::SevenBitClean(System.String,System.Text.Encoding)
extern void WWWTranscoder_SevenBitClean_m0727E1616FF0CB44CEDD879F91826A2D0CC712D6 (void);
// 0x00000018 System.Boolean UnityEngine.WWWTranscoder::SevenBitClean(System.Byte[])
extern void WWWTranscoder_SevenBitClean_mB4357C25AD5B75AEBDFB3AC3AFC7082FCCC9AAE3 (void);
// 0x00000019 System.Void UnityEngine.WWWTranscoder::.cctor()
extern void WWWTranscoder__cctor_mC6BE25CAAEDBF4C12B4A22822BAF5F4FC15C6023 (void);
// 0x0000001A System.Void UnityEngine.Networking.UnityWebRequestAsyncOperation::set_webRequest(UnityEngine.Networking.UnityWebRequest)
extern void UnityWebRequestAsyncOperation_set_webRequest_mAB1AE2613FEF8FAFB463E73D70043A89B2FC39C3 (void);
// 0x0000001B System.Void UnityEngine.Networking.UnityWebRequestAsyncOperation::.ctor()
extern void UnityWebRequestAsyncOperation__ctor_m9A74EA34FBE3BA2B4E4D8FAB3B44301ECEDBCB62 (void);
// 0x0000001C System.String UnityEngine.Networking.UnityWebRequest::GetWebErrorString(UnityEngine.Networking.UnityWebRequest/UnityWebRequestError)
extern void UnityWebRequest_GetWebErrorString_mA6003EB48A585B0F975B1545252DE3719F89761D (void);
// 0x0000001D System.String UnityEngine.Networking.UnityWebRequest::GetHTTPStatusString(System.Int64)
extern void UnityWebRequest_GetHTTPStatusString_m3DC28439F607E54EAAE7B423F1FB5DE9A2C1E199 (void);
// 0x0000001E System.Boolean UnityEngine.Networking.UnityWebRequest::get_disposeCertificateHandlerOnDispose()
extern void UnityWebRequest_get_disposeCertificateHandlerOnDispose_m47CB38750DEFA8A9FB426E650A78F62FEA68D0C0 (void);
// 0x0000001F System.Void UnityEngine.Networking.UnityWebRequest::set_disposeCertificateHandlerOnDispose(System.Boolean)
extern void UnityWebRequest_set_disposeCertificateHandlerOnDispose_mBF38D6481CE40882846C679F3E51C8D70B502E3C (void);
// 0x00000020 System.Boolean UnityEngine.Networking.UnityWebRequest::get_disposeDownloadHandlerOnDispose()
extern void UnityWebRequest_get_disposeDownloadHandlerOnDispose_m815A891A7C495D05DACB60147CB557C78CE89289 (void);
// 0x00000021 System.Void UnityEngine.Networking.UnityWebRequest::set_disposeDownloadHandlerOnDispose(System.Boolean)
extern void UnityWebRequest_set_disposeDownloadHandlerOnDispose_m6F638476FBD7ABDCEF8E7AD449266E505B85D20B (void);
// 0x00000022 System.Boolean UnityEngine.Networking.UnityWebRequest::get_disposeUploadHandlerOnDispose()
extern void UnityWebRequest_get_disposeUploadHandlerOnDispose_mBFAC496EFE17D72583D9D81B170EE0F2FABDC07B (void);
// 0x00000023 System.Void UnityEngine.Networking.UnityWebRequest::set_disposeUploadHandlerOnDispose(System.Boolean)
extern void UnityWebRequest_set_disposeUploadHandlerOnDispose_m4A63470C788CF2A03D34EEA3F8968B430A111397 (void);
// 0x00000024 System.IntPtr UnityEngine.Networking.UnityWebRequest::Create()
extern void UnityWebRequest_Create_mAEE2C12539E5839DB8B887A1A87B009C637F2756 (void);
// 0x00000025 System.Void UnityEngine.Networking.UnityWebRequest::Release()
extern void UnityWebRequest_Release_m9E5ABFEE29761B2AD53E92777882E4EFB97BEBBB (void);
// 0x00000026 System.Void UnityEngine.Networking.UnityWebRequest::InternalDestroy()
extern void UnityWebRequest_InternalDestroy_m9317079471CFF46A5274FFF1E360CA5237D9270D (void);
// 0x00000027 System.Void UnityEngine.Networking.UnityWebRequest::InternalSetDefaults()
extern void UnityWebRequest_InternalSetDefaults_mD7D5534B728DF7F2422999B58C98394253A2035C (void);
// 0x00000028 System.Void UnityEngine.Networking.UnityWebRequest::.ctor(System.String,System.String)
extern void UnityWebRequest__ctor_mC2ED369A4ACE53AFF2E70A38BE95EB48D68D4975 (void);
// 0x00000029 System.Void UnityEngine.Networking.UnityWebRequest::.ctor(System.String,System.String,UnityEngine.Networking.DownloadHandler,UnityEngine.Networking.UploadHandler)
extern void UnityWebRequest__ctor_m6F640D6320ABA5A1ED08C3B2A259DB67372DCECB (void);
// 0x0000002A System.Void UnityEngine.Networking.UnityWebRequest::Finalize()
extern void UnityWebRequest_Finalize_m0E1202F2844C73C643D8B8DA23794FC9948A6D92 (void);
// 0x0000002B System.Void UnityEngine.Networking.UnityWebRequest::Dispose()
extern void UnityWebRequest_Dispose_m8032472F6BC2EC4FEE017DE7E4C440078BC4E1C8 (void);
// 0x0000002C System.Void UnityEngine.Networking.UnityWebRequest::DisposeHandlers()
extern void UnityWebRequest_DisposeHandlers_mA28A13FCE2CBA777049A6C1A3FABA079B3820771 (void);
// 0x0000002D UnityEngine.Networking.UnityWebRequestAsyncOperation UnityEngine.Networking.UnityWebRequest::BeginWebRequest()
extern void UnityWebRequest_BeginWebRequest_m8F87C90D83FAC942DC2DE126E99D97A4CBCFD2D6 (void);
// 0x0000002E UnityEngine.AsyncOperation UnityEngine.Networking.UnityWebRequest::Send()
extern void UnityWebRequest_Send_mCE6413FFF8346F6A03B4F785D22F11887021EE22 (void);
// 0x0000002F UnityEngine.Networking.UnityWebRequestAsyncOperation UnityEngine.Networking.UnityWebRequest::SendWebRequest()
extern void UnityWebRequest_SendWebRequest_m990921023F56ECB8FF8C118894A317EB6E2F5B50 (void);
// 0x00000030 System.Void UnityEngine.Networking.UnityWebRequest::Abort()
extern void UnityWebRequest_Abort_m632DFDB95C422B9C7536D9E2CB04CC722FFF2E28 (void);
// 0x00000031 UnityEngine.Networking.UnityWebRequest/UnityWebRequestError UnityEngine.Networking.UnityWebRequest::SetMethod(UnityEngine.Networking.UnityWebRequest/UnityWebRequestMethod)
extern void UnityWebRequest_SetMethod_m1303F8415C7A460B16DDB5CC841832221A90ABED (void);
// 0x00000032 System.Void UnityEngine.Networking.UnityWebRequest::InternalSetMethod(UnityEngine.Networking.UnityWebRequest/UnityWebRequestMethod)
extern void UnityWebRequest_InternalSetMethod_mBBFC08EEC7A4A7F9DC510AEB10D5103475C6B4EB (void);
// 0x00000033 UnityEngine.Networking.UnityWebRequest/UnityWebRequestError UnityEngine.Networking.UnityWebRequest::SetCustomMethod(System.String)
extern void UnityWebRequest_SetCustomMethod_m86A1258AD72D36E5B4D9D2735045DD26FE7AE79D (void);
// 0x00000034 System.Void UnityEngine.Networking.UnityWebRequest::InternalSetCustomMethod(System.String)
extern void UnityWebRequest_InternalSetCustomMethod_mD7B421D1B4CCF480EA333CDE465C1C5D588715DA (void);
// 0x00000035 System.Void UnityEngine.Networking.UnityWebRequest::set_method(System.String)
extern void UnityWebRequest_set_method_m603E0D94414F046DE2A60E127A715B3303F46AA7 (void);
// 0x00000036 UnityEngine.Networking.UnityWebRequest/UnityWebRequestError UnityEngine.Networking.UnityWebRequest::GetError()
extern void UnityWebRequest_GetError_m6BF0FA43C1D180750DB0E955D0AE2DEB855C70F3 (void);
// 0x00000037 System.String UnityEngine.Networking.UnityWebRequest::get_error()
extern void UnityWebRequest_get_error_m32B69D2365C1FE2310B5936C7C295B71A92CC2B4 (void);
// 0x00000038 System.String UnityEngine.Networking.UnityWebRequest::get_url()
extern void UnityWebRequest_get_url_m802F6A7942362F28F2D856F17B2BDF8C2561734E (void);
// 0x00000039 System.Void UnityEngine.Networking.UnityWebRequest::set_url(System.String)
extern void UnityWebRequest_set_url_m27BCB349EB7FA610705EA03CC817A1A394BA392F (void);
// 0x0000003A System.String UnityEngine.Networking.UnityWebRequest::GetUrl()
extern void UnityWebRequest_GetUrl_mEFD2DC3DBFDCCCB5EE929CE879E8DF2D0C58E15A (void);
// 0x0000003B UnityEngine.Networking.UnityWebRequest/UnityWebRequestError UnityEngine.Networking.UnityWebRequest::SetUrl(System.String)
extern void UnityWebRequest_SetUrl_m46220F73F184F5205C1F7E44EBFB868700E89400 (void);
// 0x0000003C System.Void UnityEngine.Networking.UnityWebRequest::InternalSetUrl(System.String)
extern void UnityWebRequest_InternalSetUrl_mE850CF14CE4F5EE57063771AC9B0C686C4856F25 (void);
// 0x0000003D System.Int64 UnityEngine.Networking.UnityWebRequest::get_responseCode()
extern void UnityWebRequest_get_responseCode_m27D1260ADC92070608532D81B836CAA2742D1753 (void);
// 0x0000003E System.Single UnityEngine.Networking.UnityWebRequest::GetUploadProgress()
extern void UnityWebRequest_GetUploadProgress_m0D83B9BBA9B563715A3DB411DE06C9513A8450B6 (void);
// 0x0000003F System.Boolean UnityEngine.Networking.UnityWebRequest::IsExecuting()
extern void UnityWebRequest_IsExecuting_m6666F1528213020025DDAE3CDD50FE0C13C688D4 (void);
// 0x00000040 System.Single UnityEngine.Networking.UnityWebRequest::get_uploadProgress()
extern void UnityWebRequest_get_uploadProgress_m3B4D421CFB5D452DB687D438872DD72FA4A445AA (void);
// 0x00000041 System.Boolean UnityEngine.Networking.UnityWebRequest::get_isModifiable()
extern void UnityWebRequest_get_isModifiable_m5B9DC48172A00D5268B02F36AB4A9A0DFB38AD3A (void);
// 0x00000042 System.Boolean UnityEngine.Networking.UnityWebRequest::get_isDone()
extern void UnityWebRequest_get_isDone_mF8C92D10767B80877BCFE6D119CBE9090ACCDFBD (void);
// 0x00000043 UnityEngine.Networking.UnityWebRequest/Result UnityEngine.Networking.UnityWebRequest::get_result()
extern void UnityWebRequest_get_result_m4E9272AB25BD5CE7B927F4B1873763510476BDC6 (void);
// 0x00000044 System.Single UnityEngine.Networking.UnityWebRequest::GetDownloadProgress()
extern void UnityWebRequest_GetDownloadProgress_mC606595011ACB62AF71EBA355A28CB91095DD031 (void);
// 0x00000045 System.Single UnityEngine.Networking.UnityWebRequest::get_downloadProgress()
extern void UnityWebRequest_get_downloadProgress_m364504584506899F3E60F0D67017C290526D5636 (void);
// 0x00000046 System.UInt64 UnityEngine.Networking.UnityWebRequest::get_downloadedBytes()
extern void UnityWebRequest_get_downloadedBytes_mD4AF34C3D78C4BBD477B90246CBB33F99757A922 (void);
// 0x00000047 UnityEngine.Networking.UnityWebRequest/UnityWebRequestError UnityEngine.Networking.UnityWebRequest::SetChunked(System.Boolean)
extern void UnityWebRequest_SetChunked_m173E726FCC4BF15CCC70BE65C66FAD0DCA58AC57 (void);
// 0x00000048 System.Void UnityEngine.Networking.UnityWebRequest::set_chunkedTransfer(System.Boolean)
extern void UnityWebRequest_set_chunkedTransfer_mA743E172FDB5D5892DD6D3AA6836B66CD6233B5B (void);
// 0x00000049 UnityEngine.Networking.UnityWebRequest/UnityWebRequestError UnityEngine.Networking.UnityWebRequest::InternalSetRequestHeader(System.String,System.String)
extern void UnityWebRequest_InternalSetRequestHeader_m8D0FE7BF624976F2510A90F61A69CEB22739F2BE (void);
// 0x0000004A System.Void UnityEngine.Networking.UnityWebRequest::SetRequestHeader(System.String,System.String)
extern void UnityWebRequest_SetRequestHeader_m5ED4EFBACC106390DF5D81D19E4D4D9D59F13EFB (void);
// 0x0000004B System.String UnityEngine.Networking.UnityWebRequest::GetResponseHeader(System.String)
extern void UnityWebRequest_GetResponseHeader_mD876A4838C26726D95956FBE5F502BE5A62C21BF (void);
// 0x0000004C System.String[] UnityEngine.Networking.UnityWebRequest::GetResponseHeaderKeys()
extern void UnityWebRequest_GetResponseHeaderKeys_m62D9A46F6E5AD70D4F1DD13FFCE2A9D3C341FFF0 (void);
// 0x0000004D System.Collections.Generic.Dictionary`2<System.String,System.String> UnityEngine.Networking.UnityWebRequest::GetResponseHeaders()
extern void UnityWebRequest_GetResponseHeaders_mD42F316C2E0B8A8AD0F9E6F3A3D4103CAB92FB23 (void);
// 0x0000004E UnityEngine.Networking.UnityWebRequest/UnityWebRequestError UnityEngine.Networking.UnityWebRequest::SetUploadHandler(UnityEngine.Networking.UploadHandler)
extern void UnityWebRequest_SetUploadHandler_mDDEF10B06EF15A1B826F3402D786FA5E2B7B16BE (void);
// 0x0000004F UnityEngine.Networking.UploadHandler UnityEngine.Networking.UnityWebRequest::get_uploadHandler()
extern void UnityWebRequest_get_uploadHandler_mFB108ADD5C75C440300BF85B87D4A730A158CC38 (void);
// 0x00000050 System.Void UnityEngine.Networking.UnityWebRequest::set_uploadHandler(UnityEngine.Networking.UploadHandler)
extern void UnityWebRequest_set_uploadHandler_m8D5DF24FBE7F8F0DCF27E11CE3C6CF4363DF23BA (void);
// 0x00000051 UnityEngine.Networking.UnityWebRequest/UnityWebRequestError UnityEngine.Networking.UnityWebRequest::SetDownloadHandler(UnityEngine.Networking.DownloadHandler)
extern void UnityWebRequest_SetDownloadHandler_m2B4503FF83DB5348F5ADCC8F23144414429BD36A (void);
// 0x00000052 UnityEngine.Networking.DownloadHandler UnityEngine.Networking.UnityWebRequest::get_downloadHandler()
extern void UnityWebRequest_get_downloadHandler_mCE0A0C53A63419FE5AE25915AFB36EABE294C732 (void);
// 0x00000053 System.Void UnityEngine.Networking.UnityWebRequest::set_downloadHandler(UnityEngine.Networking.DownloadHandler)
extern void UnityWebRequest_set_downloadHandler_m7496D2C5F755BEB68651A4F33EA9BDA319D092C2 (void);
// 0x00000054 UnityEngine.Networking.CertificateHandler UnityEngine.Networking.UnityWebRequest::get_certificateHandler()
extern void UnityWebRequest_get_certificateHandler_mACA97B9158F17FFBEF489753C6E42302EEDF057D (void);
// 0x00000055 UnityEngine.Networking.UnityWebRequest UnityEngine.Networking.UnityWebRequest::Get(System.String)
extern void UnityWebRequest_Get_m742DAB13AD092C42430625D3857FC207986D3967 (void);
// 0x00000056 UnityEngine.Networking.UnityWebRequest UnityEngine.Networking.UnityWebRequest::Post(System.String,UnityEngine.WWWForm)
extern void UnityWebRequest_Post_m9E0327CBDBF33C696A6F2B721EADB1E2A923E3C0 (void);
// 0x00000057 System.Void UnityEngine.Networking.UnityWebRequest::SetupPost(UnityEngine.Networking.UnityWebRequest,UnityEngine.WWWForm)
extern void UnityWebRequest_SetupPost_mD93CFF0A0782AC399CF7418CEF0EBB936469B939 (void);
// 0x00000058 System.String UnityEngine.Networking.UnityWebRequest::EscapeURL(System.String,System.Text.Encoding)
extern void UnityWebRequest_EscapeURL_mAD92BE77F4EB1F8B40A9641EC512B6CCB49D1680 (void);
// 0x00000059 System.String UnityEngine.Networking.UnityWebRequest::UnEscapeURL(System.String,System.Text.Encoding)
extern void UnityWebRequest_UnEscapeURL_m457D92D70CDA1FF795D805B1E2B6778DA841B2B2 (void);
// 0x0000005A System.Void UnityEngine.Networking.CertificateHandler::Release()
extern void CertificateHandler_Release_m463507E5A95CB00F1F90CCF7215F51C611AFD495 (void);
// 0x0000005B System.Boolean UnityEngine.Networking.CertificateHandler::ValidateCertificate(System.Byte[])
extern void CertificateHandler_ValidateCertificate_mE76D686A7A10A86799FBF1F107EF3544D4A9D815 (void);
// 0x0000005C System.Boolean UnityEngine.Networking.CertificateHandler::ValidateCertificateNative(System.Byte[])
extern void CertificateHandler_ValidateCertificateNative_m7AA7136975CEB1635B201B08BB9B04059E7D0D16 (void);
// 0x0000005D System.Void UnityEngine.Networking.CertificateHandler::Dispose()
extern void CertificateHandler_Dispose_m460859ACFE411145B199D8261B5ADDA4DECCA493 (void);
// 0x0000005E System.Void UnityEngine.Networking.DownloadHandler::Release()
extern void DownloadHandler_Release_m3F88C5E48B7B7A80D993B575F5E906159AAEEDEA (void);
// 0x0000005F System.Void UnityEngine.Networking.DownloadHandler::.ctor()
extern void DownloadHandler__ctor_m8E441D5C617BA103B97FE41893F0A4A323701B0F (void);
// 0x00000060 System.Void UnityEngine.Networking.DownloadHandler::Finalize()
extern void DownloadHandler_Finalize_m433AE11AD2DCBB21800F0ED5497FD3B762098202 (void);
// 0x00000061 System.Void UnityEngine.Networking.DownloadHandler::Dispose()
extern void DownloadHandler_Dispose_mAB0AA5ADCEC7681FF429DA0F0D3E7A650A02C376 (void);
// 0x00000062 System.Byte[] UnityEngine.Networking.DownloadHandler::get_data()
extern void DownloadHandler_get_data_m3AE551AAE6BF21279435D386E76EA7084CC037D3 (void);
// 0x00000063 System.String UnityEngine.Networking.DownloadHandler::get_text()
extern void DownloadHandler_get_text_mD89D7125640800A8F5C4B9401C080C405953828A (void);
// 0x00000064 System.Byte[] UnityEngine.Networking.DownloadHandler::GetData()
extern void DownloadHandler_GetData_mBBC1BE90737613B3C322E4C1B3E971729924D62B (void);
// 0x00000065 System.String UnityEngine.Networking.DownloadHandler::GetText()
extern void DownloadHandler_GetText_mF8C51EFA7A82C9C198A3207D0FED84EEB8ED373F (void);
// 0x00000066 System.Text.Encoding UnityEngine.Networking.DownloadHandler::GetTextEncoder()
extern void DownloadHandler_GetTextEncoder_mEAABE10BCA8FFAADB30C5623A10A9072A21D002D (void);
// 0x00000067 System.String UnityEngine.Networking.DownloadHandler::GetContentType()
extern void DownloadHandler_GetContentType_m3B0EEFBDC91FF18736B3FC0BD7A6319104056A0D (void);
// 0x00000068 System.Void UnityEngine.Networking.DownloadHandler::ReceiveContentLengthHeader(System.UInt64)
extern void DownloadHandler_ReceiveContentLengthHeader_mD3B607EDDF946896B9877C91DE1EDD6C39B16CF3 (void);
// 0x00000069 System.Void UnityEngine.Networking.DownloadHandler::ReceiveContentLength(System.Int32)
extern void DownloadHandler_ReceiveContentLength_m7788739919D3BB09DBF3EEE1AE211C2701672316 (void);
// 0x0000006A T UnityEngine.Networking.DownloadHandler::GetCheckedDownloader(UnityEngine.Networking.UnityWebRequest)
// 0x0000006B System.Byte[] UnityEngine.Networking.DownloadHandler::InternalGetByteArray(UnityEngine.Networking.DownloadHandler)
extern void DownloadHandler_InternalGetByteArray_mA9257307CA61E9128FEB023AB77D2A49EB9F0543 (void);
// 0x0000006C System.IntPtr UnityEngine.Networking.DownloadHandlerBuffer::Create(UnityEngine.Networking.DownloadHandlerBuffer)
extern void DownloadHandlerBuffer_Create_m16C38EA59611AFBD0F9D38A99220C27A7DE1EA36 (void);
// 0x0000006D System.Void UnityEngine.Networking.DownloadHandlerBuffer::InternalCreateBuffer()
extern void DownloadHandlerBuffer_InternalCreateBuffer_mC53D69B05DEAEA785BD0187902DAD86C34EED256 (void);
// 0x0000006E System.Void UnityEngine.Networking.DownloadHandlerBuffer::.ctor()
extern void DownloadHandlerBuffer__ctor_m01FD35970E4B4FC45FC99A648408F53A8B164774 (void);
// 0x0000006F System.Byte[] UnityEngine.Networking.DownloadHandlerBuffer::GetData()
extern void DownloadHandlerBuffer_GetData_mAFE5BF2E12AE24B162769E9B736280A802E1A3AB (void);
// 0x00000070 System.Byte[] UnityEngine.Networking.DownloadHandlerBuffer::InternalGetData()
extern void DownloadHandlerBuffer_InternalGetData_m69989D8688328151EF518F3E705C019D02C045C8 (void);
// 0x00000071 System.Void UnityEngine.Networking.UploadHandler::Release()
extern void UploadHandler_Release_mE5F9BC6193A7285099D6409401414D886AB522C8 (void);
// 0x00000072 System.Void UnityEngine.Networking.UploadHandler::.ctor()
extern void UploadHandler__ctor_mC1A1A5EDEDD4F9D7A6A8E9854E0CC6943B51158F (void);
// 0x00000073 System.Void UnityEngine.Networking.UploadHandler::Finalize()
extern void UploadHandler_Finalize_mE43816DF1E8AFC6EA32518CE8C30A5EF99F06934 (void);
// 0x00000074 System.Void UnityEngine.Networking.UploadHandler::Dispose()
extern void UploadHandler_Dispose_mC449C0EDAD6666DDE349A6F9796CEC7C86154F41 (void);
// 0x00000075 System.Void UnityEngine.Networking.UploadHandler::set_contentType(System.String)
extern void UploadHandler_set_contentType_mAECD24AB554541300BD126E01C65329F0A29A328 (void);
// 0x00000076 System.Void UnityEngine.Networking.UploadHandler::SetContentType(System.String)
extern void UploadHandler_SetContentType_m95EBCDCE4E8BF9AB78953B37C89B7F4BB6F75DA7 (void);
// 0x00000077 System.Void UnityEngine.Networking.UploadHandler::InternalSetContentType(System.String)
extern void UploadHandler_InternalSetContentType_mD7BDE8CD2F2F06BA9959E478042A040DD015E27F (void);
// 0x00000078 System.IntPtr UnityEngine.Networking.UploadHandlerRaw::Create(UnityEngine.Networking.UploadHandlerRaw,System.Byte[])
extern void UploadHandlerRaw_Create_m0E055B739A76FFE6C4A0982D9353B4DBE6CC7D39 (void);
// 0x00000079 System.Void UnityEngine.Networking.UploadHandlerRaw::.ctor(System.Byte[])
extern void UploadHandlerRaw__ctor_mB46261D7AA64B605D5CA8FF9027A4A32E57A7BD9 (void);
static Il2CppMethodPointer s_methodPointers[121] = 
{
	WebRequestUtils_RedirectTo_m302A7CAA44374907C165F4553CEA2309D689B189,
	WebRequestUtils_MakeInitialUrl_mD68C0247D99B4F6049A39AFE93CBFE4AC128F9E7,
	WebRequestUtils_MakeUriString_m3F74B391CA0D761E90C22E2E938293985665869A,
	WebRequestUtils_URLDecode_mC3C84F4BE4A5F2D8FB7A3D835493B89A4CB2BD40,
	WebRequestUtils__cctor_mCE956748A8F3B1FA48893A7D263C466082815EEB,
	WWWForm_get_DefaultEncoding_m594475A7B07B708D7890FA67E33099EECF6E5D07,
	WWWForm__ctor_mA0987933892AF6FA9E01603B214E86EC52F6B055,
	WWWForm_AddField_m8ACDB7B2124FA6EAD7FC120BB469C6352C7B7696,
	WWWForm_AddField_m71508C882B6F77219D3430D8992DC3C340492976,
	WWWForm_AddField_m20BED9E7C237D585E5D040A5D815D9C7DC16990C,
	WWWForm_get_headers_m9F6A4222926792B110E9821AF5B23FF3FB9BD45A,
	WWWForm_get_data_mCCE0C63DF2E0CA4F831F9AB78D6111BB8C669177,
	WWWTranscoder_Hex2Byte_m443DBE774241073A3B5AF834282856B675D5606E,
	WWWTranscoder_Byte2Hex_m765D06937CF87AA809079FB827778888AA00F468,
	WWWTranscoder_URLEncode_m6F6AD6415A4BA795237F16161A043DBE7EFC2D50,
	WWWTranscoder_DataEncode_m2685B211BE3828A2C3F9923DC3554C741F484802,
	WWWTranscoder_QPEncode_m15617417C9FF5577B8CEA69E7CE859254E049D7B,
	WWWTranscoder_Encode_m573CD30E0665757A9F1B2A03C404D1F5669CF5A8,
	WWWTranscoder_ByteArrayContains_mC2755D2E6DA603C283BD0DB8C6AEE8C72933DE53,
	WWWTranscoder_URLDecode_mF3500816E9A9E97BB5651FFACC80E41EFCAE22F4,
	WWWTranscoder_ByteSubArrayEquals_m8D67734F39C3F736E34FB8FFC39A638E5702AA6B,
	WWWTranscoder_Decode_m83FC67B11B0DF76C1C3C287737D182A95AE3E650,
	WWWTranscoder_SevenBitClean_m0727E1616FF0CB44CEDD879F91826A2D0CC712D6,
	WWWTranscoder_SevenBitClean_mB4357C25AD5B75AEBDFB3AC3AFC7082FCCC9AAE3,
	WWWTranscoder__cctor_mC6BE25CAAEDBF4C12B4A22822BAF5F4FC15C6023,
	UnityWebRequestAsyncOperation_set_webRequest_mAB1AE2613FEF8FAFB463E73D70043A89B2FC39C3,
	UnityWebRequestAsyncOperation__ctor_m9A74EA34FBE3BA2B4E4D8FAB3B44301ECEDBCB62,
	UnityWebRequest_GetWebErrorString_mA6003EB48A585B0F975B1545252DE3719F89761D,
	UnityWebRequest_GetHTTPStatusString_m3DC28439F607E54EAAE7B423F1FB5DE9A2C1E199,
	UnityWebRequest_get_disposeCertificateHandlerOnDispose_m47CB38750DEFA8A9FB426E650A78F62FEA68D0C0,
	UnityWebRequest_set_disposeCertificateHandlerOnDispose_mBF38D6481CE40882846C679F3E51C8D70B502E3C,
	UnityWebRequest_get_disposeDownloadHandlerOnDispose_m815A891A7C495D05DACB60147CB557C78CE89289,
	UnityWebRequest_set_disposeDownloadHandlerOnDispose_m6F638476FBD7ABDCEF8E7AD449266E505B85D20B,
	UnityWebRequest_get_disposeUploadHandlerOnDispose_mBFAC496EFE17D72583D9D81B170EE0F2FABDC07B,
	UnityWebRequest_set_disposeUploadHandlerOnDispose_m4A63470C788CF2A03D34EEA3F8968B430A111397,
	UnityWebRequest_Create_mAEE2C12539E5839DB8B887A1A87B009C637F2756,
	UnityWebRequest_Release_m9E5ABFEE29761B2AD53E92777882E4EFB97BEBBB,
	UnityWebRequest_InternalDestroy_m9317079471CFF46A5274FFF1E360CA5237D9270D,
	UnityWebRequest_InternalSetDefaults_mD7D5534B728DF7F2422999B58C98394253A2035C,
	UnityWebRequest__ctor_mC2ED369A4ACE53AFF2E70A38BE95EB48D68D4975,
	UnityWebRequest__ctor_m6F640D6320ABA5A1ED08C3B2A259DB67372DCECB,
	UnityWebRequest_Finalize_m0E1202F2844C73C643D8B8DA23794FC9948A6D92,
	UnityWebRequest_Dispose_m8032472F6BC2EC4FEE017DE7E4C440078BC4E1C8,
	UnityWebRequest_DisposeHandlers_mA28A13FCE2CBA777049A6C1A3FABA079B3820771,
	UnityWebRequest_BeginWebRequest_m8F87C90D83FAC942DC2DE126E99D97A4CBCFD2D6,
	UnityWebRequest_Send_mCE6413FFF8346F6A03B4F785D22F11887021EE22,
	UnityWebRequest_SendWebRequest_m990921023F56ECB8FF8C118894A317EB6E2F5B50,
	UnityWebRequest_Abort_m632DFDB95C422B9C7536D9E2CB04CC722FFF2E28,
	UnityWebRequest_SetMethod_m1303F8415C7A460B16DDB5CC841832221A90ABED,
	UnityWebRequest_InternalSetMethod_mBBFC08EEC7A4A7F9DC510AEB10D5103475C6B4EB,
	UnityWebRequest_SetCustomMethod_m86A1258AD72D36E5B4D9D2735045DD26FE7AE79D,
	UnityWebRequest_InternalSetCustomMethod_mD7B421D1B4CCF480EA333CDE465C1C5D588715DA,
	UnityWebRequest_set_method_m603E0D94414F046DE2A60E127A715B3303F46AA7,
	UnityWebRequest_GetError_m6BF0FA43C1D180750DB0E955D0AE2DEB855C70F3,
	UnityWebRequest_get_error_m32B69D2365C1FE2310B5936C7C295B71A92CC2B4,
	UnityWebRequest_get_url_m802F6A7942362F28F2D856F17B2BDF8C2561734E,
	UnityWebRequest_set_url_m27BCB349EB7FA610705EA03CC817A1A394BA392F,
	UnityWebRequest_GetUrl_mEFD2DC3DBFDCCCB5EE929CE879E8DF2D0C58E15A,
	UnityWebRequest_SetUrl_m46220F73F184F5205C1F7E44EBFB868700E89400,
	UnityWebRequest_InternalSetUrl_mE850CF14CE4F5EE57063771AC9B0C686C4856F25,
	UnityWebRequest_get_responseCode_m27D1260ADC92070608532D81B836CAA2742D1753,
	UnityWebRequest_GetUploadProgress_m0D83B9BBA9B563715A3DB411DE06C9513A8450B6,
	UnityWebRequest_IsExecuting_m6666F1528213020025DDAE3CDD50FE0C13C688D4,
	UnityWebRequest_get_uploadProgress_m3B4D421CFB5D452DB687D438872DD72FA4A445AA,
	UnityWebRequest_get_isModifiable_m5B9DC48172A00D5268B02F36AB4A9A0DFB38AD3A,
	UnityWebRequest_get_isDone_mF8C92D10767B80877BCFE6D119CBE9090ACCDFBD,
	UnityWebRequest_get_result_m4E9272AB25BD5CE7B927F4B1873763510476BDC6,
	UnityWebRequest_GetDownloadProgress_mC606595011ACB62AF71EBA355A28CB91095DD031,
	UnityWebRequest_get_downloadProgress_m364504584506899F3E60F0D67017C290526D5636,
	UnityWebRequest_get_downloadedBytes_mD4AF34C3D78C4BBD477B90246CBB33F99757A922,
	UnityWebRequest_SetChunked_m173E726FCC4BF15CCC70BE65C66FAD0DCA58AC57,
	UnityWebRequest_set_chunkedTransfer_mA743E172FDB5D5892DD6D3AA6836B66CD6233B5B,
	UnityWebRequest_InternalSetRequestHeader_m8D0FE7BF624976F2510A90F61A69CEB22739F2BE,
	UnityWebRequest_SetRequestHeader_m5ED4EFBACC106390DF5D81D19E4D4D9D59F13EFB,
	UnityWebRequest_GetResponseHeader_mD876A4838C26726D95956FBE5F502BE5A62C21BF,
	UnityWebRequest_GetResponseHeaderKeys_m62D9A46F6E5AD70D4F1DD13FFCE2A9D3C341FFF0,
	UnityWebRequest_GetResponseHeaders_mD42F316C2E0B8A8AD0F9E6F3A3D4103CAB92FB23,
	UnityWebRequest_SetUploadHandler_mDDEF10B06EF15A1B826F3402D786FA5E2B7B16BE,
	UnityWebRequest_get_uploadHandler_mFB108ADD5C75C440300BF85B87D4A730A158CC38,
	UnityWebRequest_set_uploadHandler_m8D5DF24FBE7F8F0DCF27E11CE3C6CF4363DF23BA,
	UnityWebRequest_SetDownloadHandler_m2B4503FF83DB5348F5ADCC8F23144414429BD36A,
	UnityWebRequest_get_downloadHandler_mCE0A0C53A63419FE5AE25915AFB36EABE294C732,
	UnityWebRequest_set_downloadHandler_m7496D2C5F755BEB68651A4F33EA9BDA319D092C2,
	UnityWebRequest_get_certificateHandler_mACA97B9158F17FFBEF489753C6E42302EEDF057D,
	UnityWebRequest_Get_m742DAB13AD092C42430625D3857FC207986D3967,
	UnityWebRequest_Post_m9E0327CBDBF33C696A6F2B721EADB1E2A923E3C0,
	UnityWebRequest_SetupPost_mD93CFF0A0782AC399CF7418CEF0EBB936469B939,
	UnityWebRequest_EscapeURL_mAD92BE77F4EB1F8B40A9641EC512B6CCB49D1680,
	UnityWebRequest_UnEscapeURL_m457D92D70CDA1FF795D805B1E2B6778DA841B2B2,
	CertificateHandler_Release_m463507E5A95CB00F1F90CCF7215F51C611AFD495,
	CertificateHandler_ValidateCertificate_mE76D686A7A10A86799FBF1F107EF3544D4A9D815,
	CertificateHandler_ValidateCertificateNative_m7AA7136975CEB1635B201B08BB9B04059E7D0D16,
	CertificateHandler_Dispose_m460859ACFE411145B199D8261B5ADDA4DECCA493,
	DownloadHandler_Release_m3F88C5E48B7B7A80D993B575F5E906159AAEEDEA,
	DownloadHandler__ctor_m8E441D5C617BA103B97FE41893F0A4A323701B0F,
	DownloadHandler_Finalize_m433AE11AD2DCBB21800F0ED5497FD3B762098202,
	DownloadHandler_Dispose_mAB0AA5ADCEC7681FF429DA0F0D3E7A650A02C376,
	DownloadHandler_get_data_m3AE551AAE6BF21279435D386E76EA7084CC037D3,
	DownloadHandler_get_text_mD89D7125640800A8F5C4B9401C080C405953828A,
	DownloadHandler_GetData_mBBC1BE90737613B3C322E4C1B3E971729924D62B,
	DownloadHandler_GetText_mF8C51EFA7A82C9C198A3207D0FED84EEB8ED373F,
	DownloadHandler_GetTextEncoder_mEAABE10BCA8FFAADB30C5623A10A9072A21D002D,
	DownloadHandler_GetContentType_m3B0EEFBDC91FF18736B3FC0BD7A6319104056A0D,
	DownloadHandler_ReceiveContentLengthHeader_mD3B607EDDF946896B9877C91DE1EDD6C39B16CF3,
	DownloadHandler_ReceiveContentLength_m7788739919D3BB09DBF3EEE1AE211C2701672316,
	NULL,
	DownloadHandler_InternalGetByteArray_mA9257307CA61E9128FEB023AB77D2A49EB9F0543,
	DownloadHandlerBuffer_Create_m16C38EA59611AFBD0F9D38A99220C27A7DE1EA36,
	DownloadHandlerBuffer_InternalCreateBuffer_mC53D69B05DEAEA785BD0187902DAD86C34EED256,
	DownloadHandlerBuffer__ctor_m01FD35970E4B4FC45FC99A648408F53A8B164774,
	DownloadHandlerBuffer_GetData_mAFE5BF2E12AE24B162769E9B736280A802E1A3AB,
	DownloadHandlerBuffer_InternalGetData_m69989D8688328151EF518F3E705C019D02C045C8,
	UploadHandler_Release_mE5F9BC6193A7285099D6409401414D886AB522C8,
	UploadHandler__ctor_mC1A1A5EDEDD4F9D7A6A8E9854E0CC6943B51158F,
	UploadHandler_Finalize_mE43816DF1E8AFC6EA32518CE8C30A5EF99F06934,
	UploadHandler_Dispose_mC449C0EDAD6666DDE349A6F9796CEC7C86154F41,
	UploadHandler_set_contentType_mAECD24AB554541300BD126E01C65329F0A29A328,
	UploadHandler_SetContentType_m95EBCDCE4E8BF9AB78953B37C89B7F4BB6F75DA7,
	UploadHandler_InternalSetContentType_mD7BDE8CD2F2F06BA9959E478042A040DD015E27F,
	UploadHandlerRaw_Create_m0E055B739A76FFE6C4A0982D9353B4DBE6CC7D39,
	UploadHandlerRaw__ctor_mB46261D7AA64B605D5CA8FF9027A4A32E57A7BD9,
};
static const int32_t s_InvokerIndices[121] = 
{
	5599,
	5599,
	5183,
	6091,
	6289,
	6270,
	3967,
	2171,
	1337,
	2167,
	3916,
	3916,
	5432,
	5572,
	6091,
	6091,
	5599,
	4482,
	5431,
	6091,
	5037,
	5164,
	5433,
	5923,
	6289,
	3348,
	3967,
	6088,
	6089,
	3863,
	3276,
	3863,
	3276,
	3863,
	3276,
	6265,
	3967,
	3967,
	3967,
	2171,
	898,
	3967,
	3967,
	3967,
	3916,
	3916,
	3916,
	3967,
	2884,
	3313,
	2901,
	3348,
	3348,
	3893,
	3916,
	3916,
	3348,
	3916,
	2901,
	3348,
	3894,
	3951,
	3863,
	3951,
	3863,
	3863,
	3893,
	3951,
	3951,
	3962,
	2856,
	3276,
	1607,
	2171,
	3047,
	3916,
	3916,
	2901,
	3916,
	3348,
	2901,
	3916,
	3348,
	3916,
	6091,
	5599,
	5834,
	5599,
	5599,
	3967,
	2591,
	2591,
	3967,
	3967,
	3967,
	3967,
	3967,
	3916,
	3916,
	3916,
	3916,
	3916,
	3916,
	3393,
	3313,
	-1,
	6091,
	6032,
	3967,
	3967,
	3916,
	3916,
	3967,
	3967,
	3967,
	3967,
	3348,
	3348,
	3348,
	5553,
	3348,
};
static const Il2CppTokenRangePair s_rgctxIndices[1] = 
{
	{ 0x0600006A, { 0, 1 } },
};
static const Il2CppRGCTXDefinition s_rgctxValues[1] = 
{
	{ (Il2CppRGCTXDataType)2, 98 },
};
extern const CustomAttributesCacheGenerator g_UnityEngine_UnityWebRequestModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_UnityWebRequestModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_UnityWebRequestModule_CodeGenModule = 
{
	"UnityEngine.UnityWebRequestModule.dll",
	121,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	1,
	s_rgctxIndices,
	1,
	s_rgctxValues,
	NULL,
	g_UnityEngine_UnityWebRequestModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.String UnityEngine.PhysicsScene2D::ToString()
extern void PhysicsScene2D_ToString_mDA6F499BD218AA31A450D918BB6C1890A9CE1109 (void);
// 0x00000002 System.Int32 UnityEngine.PhysicsScene2D::GetHashCode()
extern void PhysicsScene2D_GetHashCode_m4B5D8DCBA0AD6E5767C4D7A6AD6BC789EB19C8F5 (void);
// 0x00000003 System.Boolean UnityEngine.PhysicsScene2D::Equals(System.Object)
extern void PhysicsScene2D_Equals_m0C61F175C3B1BB308ADBC2AB323CEC45D1B5E99C (void);
// 0x00000004 System.Boolean UnityEngine.PhysicsScene2D::Equals(UnityEngine.PhysicsScene2D)
extern void PhysicsScene2D_Equals_m581586F404E7A3BD3B6F0A05362974A6B523A2DA (void);
// 0x00000005 UnityEngine.RaycastHit2D UnityEngine.PhysicsScene2D::Raycast(UnityEngine.Vector2,UnityEngine.Vector2,System.Single,System.Int32)
extern void PhysicsScene2D_Raycast_m22F55CAAA1B34A02757A5C6E2B573F6464B32723 (void);
// 0x00000006 UnityEngine.RaycastHit2D UnityEngine.PhysicsScene2D::Raycast(UnityEngine.Vector2,UnityEngine.Vector2,System.Single,UnityEngine.ContactFilter2D)
extern void PhysicsScene2D_Raycast_m167DAEC271F46133013EB0AAF2C7807064EBB3F2 (void);
// 0x00000007 UnityEngine.RaycastHit2D UnityEngine.PhysicsScene2D::Raycast_Internal(UnityEngine.PhysicsScene2D,UnityEngine.Vector2,UnityEngine.Vector2,System.Single,UnityEngine.ContactFilter2D)
extern void PhysicsScene2D_Raycast_Internal_mB78C9A1BA2638CA23C86CAC509EEA18A4DDDDDD2 (void);
// 0x00000008 System.Int32 UnityEngine.PhysicsScene2D::Raycast(UnityEngine.Vector2,UnityEngine.Vector2,System.Single,UnityEngine.ContactFilter2D,UnityEngine.RaycastHit2D[])
extern void PhysicsScene2D_Raycast_m3B011D4A6CA691739178E253665799A7AD0CB423 (void);
// 0x00000009 System.Int32 UnityEngine.PhysicsScene2D::RaycastArray_Internal(UnityEngine.PhysicsScene2D,UnityEngine.Vector2,UnityEngine.Vector2,System.Single,UnityEngine.ContactFilter2D,UnityEngine.RaycastHit2D[])
extern void PhysicsScene2D_RaycastArray_Internal_m9FDAC65FD7CE17757CF79F077D659AE5E561968A (void);
// 0x0000000A System.Int32 UnityEngine.PhysicsScene2D::Raycast(UnityEngine.Vector2,UnityEngine.Vector2,System.Single,UnityEngine.ContactFilter2D,System.Collections.Generic.List`1<UnityEngine.RaycastHit2D>)
extern void PhysicsScene2D_Raycast_m6FB2BBC4E3BE53114D7F6EFA975F5AF703ADCDC0 (void);
// 0x0000000B System.Int32 UnityEngine.PhysicsScene2D::RaycastList_Internal(UnityEngine.PhysicsScene2D,UnityEngine.Vector2,UnityEngine.Vector2,System.Single,UnityEngine.ContactFilter2D,System.Collections.Generic.List`1<UnityEngine.RaycastHit2D>)
extern void PhysicsScene2D_RaycastList_Internal_m6CBBE8A1D09F0252C2A4BDD0D457A5A397960EA6 (void);
// 0x0000000C UnityEngine.RaycastHit2D UnityEngine.PhysicsScene2D::CircleCast(UnityEngine.Vector2,System.Single,UnityEngine.Vector2,System.Single,UnityEngine.ContactFilter2D)
extern void PhysicsScene2D_CircleCast_m**************************************** (void);
// 0x0000000D UnityEngine.RaycastHit2D UnityEngine.PhysicsScene2D::CircleCast_Internal(UnityEngine.PhysicsScene2D,UnityEngine.Vector2,System.Single,UnityEngine.Vector2,System.Single,UnityEngine.ContactFilter2D)
extern void PhysicsScene2D_CircleCast_Internal_m**************************************** (void);
// 0x0000000E System.Int32 UnityEngine.PhysicsScene2D::GetRayIntersection(UnityEngine.Ray,System.Single,UnityEngine.RaycastHit2D[],System.Int32)
extern void PhysicsScene2D_GetRayIntersection_mB6F14C8BB95609094BE9BDB218483EAAC4117B5A (void);
// 0x0000000F System.Int32 UnityEngine.PhysicsScene2D::GetRayIntersectionArray_Internal(UnityEngine.PhysicsScene2D,UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Int32,UnityEngine.RaycastHit2D[])
extern void PhysicsScene2D_GetRayIntersectionArray_Internal_m0B3286A34048B64C4BA18262AB7FF0940DCA3149 (void);
// 0x00000010 UnityEngine.Collider2D UnityEngine.PhysicsScene2D::OverlapPoint(UnityEngine.Vector2,UnityEngine.ContactFilter2D)
extern void PhysicsScene2D_OverlapPoint_mD32EDA10783DDB83BA2889FCBB7A89186E0B43F0 (void);
// 0x00000011 UnityEngine.Collider2D UnityEngine.PhysicsScene2D::OverlapPoint_Internal(UnityEngine.PhysicsScene2D,UnityEngine.Vector2,UnityEngine.ContactFilter2D)
extern void PhysicsScene2D_OverlapPoint_Internal_m778B6343B3DCE95875A5D3C1EAB272D184087394 (void);
// 0x00000012 System.Int32 UnityEngine.PhysicsScene2D::OverlapPoint(UnityEngine.Vector2,UnityEngine.ContactFilter2D,UnityEngine.Collider2D[])
extern void PhysicsScene2D_OverlapPoint_m253E6EEA2E3C53873A1259817DCA8C9B3364F97D (void);
// 0x00000013 System.Int32 UnityEngine.PhysicsScene2D::OverlapPointArray_Internal(UnityEngine.PhysicsScene2D,UnityEngine.Vector2,UnityEngine.ContactFilter2D,UnityEngine.Collider2D[])
extern void PhysicsScene2D_OverlapPointArray_Internal_m673CD8AB0F45A4AF6EEF990B6D5C4D7591866478 (void);
// 0x00000014 System.Void UnityEngine.PhysicsScene2D::Raycast_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector2&,UnityEngine.Vector2&,System.Single,UnityEngine.ContactFilter2D&,UnityEngine.RaycastHit2D&)
extern void PhysicsScene2D_Raycast_Internal_Injected_mAC812B5A63C160F6D05024FF7D4D497EF89ED7B5 (void);
// 0x00000015 System.Int32 UnityEngine.PhysicsScene2D::RaycastArray_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector2&,UnityEngine.Vector2&,System.Single,UnityEngine.ContactFilter2D&,UnityEngine.RaycastHit2D[])
extern void PhysicsScene2D_RaycastArray_Internal_Injected_mC84C0FB26E6E586FF0CC6E2EAF2DF0774ED5EEE5 (void);
// 0x00000016 System.Int32 UnityEngine.PhysicsScene2D::RaycastList_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector2&,UnityEngine.Vector2&,System.Single,UnityEngine.ContactFilter2D&,System.Collections.Generic.List`1<UnityEngine.RaycastHit2D>)
extern void PhysicsScene2D_RaycastList_Internal_Injected_m2B17C3C3E823E27EEB20BCA02274EBAFCFB9C0A2 (void);
// 0x00000017 System.Void UnityEngine.PhysicsScene2D::CircleCast_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector2&,System.Single,UnityEngine.Vector2&,System.Single,UnityEngine.ContactFilter2D&,UnityEngine.RaycastHit2D&)
extern void PhysicsScene2D_CircleCast_Internal_Injected_m**************************************** (void);
// 0x00000018 System.Int32 UnityEngine.PhysicsScene2D::GetRayIntersectionArray_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,System.Int32,UnityEngine.RaycastHit2D[])
extern void PhysicsScene2D_GetRayIntersectionArray_Internal_Injected_mC4EB4FECE49A1D38C1D4E38BE16749839F0E4AC6 (void);
// 0x00000019 UnityEngine.Collider2D UnityEngine.PhysicsScene2D::OverlapPoint_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector2&,UnityEngine.ContactFilter2D&)
extern void PhysicsScene2D_OverlapPoint_Internal_Injected_mF6785A066BC9BFC02C3F8E2D4C741AB251D86A2C (void);
// 0x0000001A System.Int32 UnityEngine.PhysicsScene2D::OverlapPointArray_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector2&,UnityEngine.ContactFilter2D&,UnityEngine.Collider2D[])
extern void PhysicsScene2D_OverlapPointArray_Internal_Injected_m372BA00AAB4FDC276BF500F6C8ED036D42A73449 (void);
// 0x0000001B UnityEngine.PhysicsScene2D UnityEngine.Physics2D::get_defaultPhysicsScene()
extern void Physics2D_get_defaultPhysicsScene_m229B7F5325C13EF4170DFE4110BEE6AB6E28A887 (void);
// 0x0000001C System.Boolean UnityEngine.Physics2D::get_queriesHitTriggers()
extern void Physics2D_get_queriesHitTriggers_m799BFBB4A3F711661EE0FCD570BD6CF36B529949 (void);
// 0x0000001D System.Void UnityEngine.Physics2D::IgnoreCollision(UnityEngine.Collider2D,UnityEngine.Collider2D)
extern void Physics2D_IgnoreCollision_m9F4792711E248B5ABAD555BF5A74A5B8DC67E68B (void);
// 0x0000001E System.Void UnityEngine.Physics2D::IgnoreCollision(UnityEngine.Collider2D,UnityEngine.Collider2D,System.Boolean)
extern void Physics2D_IgnoreCollision_m428EBF68F58C0C48C08C15E2967AF69AE2AC8750 (void);
// 0x0000001F UnityEngine.RaycastHit2D UnityEngine.Physics2D::Raycast(UnityEngine.Vector2,UnityEngine.Vector2)
extern void Physics2D_Raycast_m32FE24C048CA831A45C16BC97DABF0419B9262E9 (void);
// 0x00000020 UnityEngine.RaycastHit2D UnityEngine.Physics2D::Raycast(UnityEngine.Vector2,UnityEngine.Vector2,System.Single)
extern void Physics2D_Raycast_mE585245B02AECC99BFAF035418CD46506B6B68F8 (void);
// 0x00000021 UnityEngine.RaycastHit2D UnityEngine.Physics2D::Raycast(UnityEngine.Vector2,UnityEngine.Vector2,System.Single,System.Int32)
extern void Physics2D_Raycast_m534D6FF7F823CF237C2CB7786587399F3BCEE6AD (void);
// 0x00000022 UnityEngine.RaycastHit2D UnityEngine.Physics2D::Raycast(UnityEngine.Vector2,UnityEngine.Vector2,System.Single,System.Int32,System.Single)
extern void Physics2D_Raycast_m8A061CCEF534EE47A4BB54FC1862C1AD87A15392 (void);
// 0x00000023 UnityEngine.RaycastHit2D UnityEngine.Physics2D::Raycast(UnityEngine.Vector2,UnityEngine.Vector2,System.Single,System.Int32,System.Single,System.Single)
extern void Physics2D_Raycast_m5C80748705F8C164CC6607A30C318AD4EC731DF7 (void);
// 0x00000024 System.Int32 UnityEngine.Physics2D::Raycast(UnityEngine.Vector2,UnityEngine.Vector2,UnityEngine.ContactFilter2D,UnityEngine.RaycastHit2D[])
extern void Physics2D_Raycast_m59DD98F3E138545349E0D53EF411AB2BF2105EA9 (void);
// 0x00000025 System.Int32 UnityEngine.Physics2D::Raycast(UnityEngine.Vector2,UnityEngine.Vector2,UnityEngine.ContactFilter2D,UnityEngine.RaycastHit2D[],System.Single)
extern void Physics2D_Raycast_mA7B66B224626233CE226AB1C2EBA456CFE0B680D (void);
// 0x00000026 System.Int32 UnityEngine.Physics2D::Raycast(UnityEngine.Vector2,UnityEngine.Vector2,UnityEngine.ContactFilter2D,System.Collections.Generic.List`1<UnityEngine.RaycastHit2D>,System.Single)
extern void Physics2D_Raycast_mE51C8B4D6E936969B6A1A625CED083BE52143D71 (void);
// 0x00000027 UnityEngine.RaycastHit2D UnityEngine.Physics2D::CircleCast(UnityEngine.Vector2,System.Single,UnityEngine.Vector2,System.Single,System.Int32)
extern void Physics2D_CircleCast_m**************************************** (void);
// 0x00000028 UnityEngine.RaycastHit2D[] UnityEngine.Physics2D::GetRayIntersectionAll(UnityEngine.Ray)
extern void Physics2D_GetRayIntersectionAll_m62DEA481DAC2D73C176DDD71B527A392A8534141 (void);
// 0x00000029 UnityEngine.RaycastHit2D[] UnityEngine.Physics2D::GetRayIntersectionAll(UnityEngine.Ray,System.Single)
extern void Physics2D_GetRayIntersectionAll_m7570FAD655B942FAC58D512A1358D97B6356FE9A (void);
// 0x0000002A UnityEngine.RaycastHit2D[] UnityEngine.Physics2D::GetRayIntersectionAll(UnityEngine.Ray,System.Single,System.Int32)
extern void Physics2D_GetRayIntersectionAll_m6A809028A55DD179F1813B427E3ADA507DE69160 (void);
// 0x0000002B UnityEngine.RaycastHit2D[] UnityEngine.Physics2D::GetRayIntersectionAll_Internal(UnityEngine.PhysicsScene2D,UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Int32)
extern void Physics2D_GetRayIntersectionAll_Internal_mCC82148312B653E8A37AC1772A7D5F54DE3623A5 (void);
// 0x0000002C System.Int32 UnityEngine.Physics2D::GetRayIntersectionNonAlloc(UnityEngine.Ray,UnityEngine.RaycastHit2D[])
extern void Physics2D_GetRayIntersectionNonAlloc_mBEA738AC1BEAAA682A0D28820B2F4D5971DA8469 (void);
// 0x0000002D System.Int32 UnityEngine.Physics2D::GetRayIntersectionNonAlloc(UnityEngine.Ray,UnityEngine.RaycastHit2D[],System.Single)
extern void Physics2D_GetRayIntersectionNonAlloc_m8B48DB9FE5B47A9D943E23349C6BC0F1B764BA02 (void);
// 0x0000002E System.Int32 UnityEngine.Physics2D::GetRayIntersectionNonAlloc(UnityEngine.Ray,UnityEngine.RaycastHit2D[],System.Single,System.Int32)
extern void Physics2D_GetRayIntersectionNonAlloc_mBD15FAD30E398E6E568EC1EEE20650FF31BF5C47 (void);
// 0x0000002F UnityEngine.Collider2D UnityEngine.Physics2D::OverlapPoint(UnityEngine.Vector2,System.Int32)
extern void Physics2D_OverlapPoint_m6EF32AD5A115E7C0ED0041ED1959E96D4AC1F8F5 (void);
// 0x00000030 System.Int32 UnityEngine.Physics2D::OverlapPointNonAlloc(UnityEngine.Vector2,UnityEngine.Collider2D[],System.Int32)
extern void Physics2D_OverlapPointNonAlloc_m1D37468831AF3BDC65C3105003611756DFA326FD (void);
// 0x00000031 System.Void UnityEngine.Physics2D::.cctor()
extern void Physics2D__cctor_m84E6BF1DDF3530A4A43A775DAADB6047D993E6A1 (void);
// 0x00000032 UnityEngine.RaycastHit2D[] UnityEngine.Physics2D::GetRayIntersectionAll_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,System.Int32)
extern void Physics2D_GetRayIntersectionAll_Internal_Injected_mF15566343788554608F269979DEA87BD72EF0D3E (void);
// 0x00000033 System.Void UnityEngine.ContactFilter2D::CheckConsistency()
extern void ContactFilter2D_CheckConsistency_m4B6DAA0197DC017E3B7A8B8F661729431504C5D1 (void);
// 0x00000034 System.Void UnityEngine.ContactFilter2D::SetLayerMask(UnityEngine.LayerMask)
extern void ContactFilter2D_SetLayerMask_m925C98BC2EEAA78349B3ED3654BC3C362743BBDE (void);
// 0x00000035 System.Void UnityEngine.ContactFilter2D::SetDepth(System.Single,System.Single)
extern void ContactFilter2D_SetDepth_m63872B3F8BBDB962AF44D064BA328599C74D840F (void);
// 0x00000036 UnityEngine.ContactFilter2D UnityEngine.ContactFilter2D::CreateLegacyFilter(System.Int32,System.Single,System.Single)
extern void ContactFilter2D_CreateLegacyFilter_mC4B0C3BBEA07C14C5FD498C3313140B3DF247CE6 (void);
// 0x00000037 System.Void UnityEngine.ContactFilter2D::CheckConsistency_Injected(UnityEngine.ContactFilter2D&)
extern void ContactFilter2D_CheckConsistency_Injected_m3942B7D0F0086DCF85ECC5847ED9DD374036DAD1 (void);
// 0x00000038 UnityEngine.Collider2D UnityEngine.Collision2D::get_collider()
extern void Collision2D_get_collider_mA7687EDB0D47A2F211BFE8DB89266B9AA05CFDDD (void);
// 0x00000039 UnityEngine.Rigidbody2D UnityEngine.Collision2D::get_rigidbody()
extern void Collision2D_get_rigidbody_m82AF533E110DFDBDED6D6C74EB479902E813D42E (void);
// 0x0000003A UnityEngine.GameObject UnityEngine.Collision2D::get_gameObject()
extern void Collision2D_get_gameObject_m6F07B9CA1FAD187933EB6D8E44BD9F870658F89F (void);
// 0x0000003B System.Void UnityEngine.JointAngleLimits2D::set_min(System.Single)
extern void JointAngleLimits2D_set_min_m7700A6023C026FAF2ED4CEDF83BFF6F5D11691A5 (void);
// 0x0000003C System.Void UnityEngine.JointAngleLimits2D::set_max(System.Single)
extern void JointAngleLimits2D_set_max_mA87719606BE27E237FCFA1795A666DE62B17AD23 (void);
// 0x0000003D UnityEngine.Vector2 UnityEngine.RaycastHit2D::get_point()
extern void RaycastHit2D_get_point_m10D5AB3B26EAE62583BE35CFA13A3E40BDAF30AE (void);
// 0x0000003E UnityEngine.Vector2 UnityEngine.RaycastHit2D::get_normal()
extern void RaycastHit2D_get_normal_m6F8B9F4018EFA126CC33126E8E42B09BB5A82637 (void);
// 0x0000003F System.Single UnityEngine.RaycastHit2D::get_distance()
extern void RaycastHit2D_get_distance_mA910B45BD349A8F70139F6BC1E686F47F40A1662 (void);
// 0x00000040 UnityEngine.Collider2D UnityEngine.RaycastHit2D::get_collider()
extern void RaycastHit2D_get_collider_m00F7EC55C36F39E2ED64B31354FB4D9C8938D563 (void);
// 0x00000041 UnityEngine.Vector2 UnityEngine.Rigidbody2D::get_position()
extern void Rigidbody2D_get_position_mEC7D07E3478BEF5A2A0E22C91CA54935376F84C2 (void);
// 0x00000042 System.Void UnityEngine.Rigidbody2D::set_position(UnityEngine.Vector2)
extern void Rigidbody2D_set_position_m1604084713EB195D44B8B411D4BCAFA5941E3413 (void);
// 0x00000043 System.Single UnityEngine.Rigidbody2D::get_rotation()
extern void Rigidbody2D_get_rotation_mD58E62EDB334FCDF7914A94C940F7903E8ADBBFF (void);
// 0x00000044 System.Void UnityEngine.Rigidbody2D::MovePosition(UnityEngine.Vector2)
extern void Rigidbody2D_MovePosition_mB4493BFC30B2FEBB02C7819AAE626871939D5BD9 (void);
// 0x00000045 System.Void UnityEngine.Rigidbody2D::MoveRotation(System.Single)
extern void Rigidbody2D_MoveRotation_mB550025C3B09DB909CB895609BF642EF768C5B22 (void);
// 0x00000046 System.Void UnityEngine.Rigidbody2D::MoveRotation_Angle(System.Single)
extern void Rigidbody2D_MoveRotation_Angle_mE1BD3C755F9FB7782274F50D55496441F8675245 (void);
// 0x00000047 System.Single UnityEngine.Rigidbody2D::get_mass()
extern void Rigidbody2D_get_mass_mE62570BBAB2CE7ACC2FA3B20EE5EB8FBDBEF019F (void);
// 0x00000048 System.Void UnityEngine.Rigidbody2D::set_mass(System.Single)
extern void Rigidbody2D_set_mass_mF66267DE577C5EC36F27FBF2F48FA405DFA24791 (void);
// 0x00000049 System.Void UnityEngine.Rigidbody2D::set_gravityScale(System.Single)
extern void Rigidbody2D_set_gravityScale_mEDC82EE2ED74DA9C5AB8A0C4A929B09149522BBF (void);
// 0x0000004A System.Void UnityEngine.Rigidbody2D::set_bodyType(UnityEngine.RigidbodyType2D)
extern void Rigidbody2D_set_bodyType_m8D34999918D42B2DF16FAAB4F237A8663EA8406B (void);
// 0x0000004B System.Void UnityEngine.Rigidbody2D::set_isKinematic(System.Boolean)
extern void Rigidbody2D_set_isKinematic_m82FBA7C4F4EB2569AC77D4767061F9CE0F8C3FEB (void);
// 0x0000004C System.Void UnityEngine.Rigidbody2D::.ctor()
extern void Rigidbody2D__ctor_m6CE72A013ED172CA657C0E00E8C04C0B523C78C5 (void);
// 0x0000004D System.Void UnityEngine.Rigidbody2D::get_position_Injected(UnityEngine.Vector2&)
extern void Rigidbody2D_get_position_Injected_m1B97DC47EDF69F0506CEC24603C3A330B7E8FAB4 (void);
// 0x0000004E System.Void UnityEngine.Rigidbody2D::set_position_Injected(UnityEngine.Vector2&)
extern void Rigidbody2D_set_position_Injected_mB05AB62CD1D243D071F60E48C9905587ACB94933 (void);
// 0x0000004F System.Void UnityEngine.Rigidbody2D::MovePosition_Injected(UnityEngine.Vector2&)
extern void Rigidbody2D_MovePosition_Injected_mCD46D7FE92C344BB29C67C5D23068982445AB549 (void);
// 0x00000050 System.Void UnityEngine.Collider2D::set_isTrigger(System.Boolean)
extern void Collider2D_set_isTrigger_m129C3059CC21789EFA198E153A3C82662E3BFF52 (void);
// 0x00000051 UnityEngine.Vector2 UnityEngine.Collider2D::get_offset()
extern void Collider2D_get_offset_m63144560DFF782608BA6627DE192D6337821995C (void);
// 0x00000052 System.Void UnityEngine.Collider2D::set_offset(UnityEngine.Vector2)
extern void Collider2D_set_offset_m496DDE5487C59E9E8D6B468E8918EE1EFC625E38 (void);
// 0x00000053 System.Void UnityEngine.Collider2D::.ctor()
extern void Collider2D__ctor_mB3AFF9CA18C8FA8E4F75DC45CA2D4A423A40F23D (void);
// 0x00000054 System.Void UnityEngine.Collider2D::get_offset_Injected(UnityEngine.Vector2&)
extern void Collider2D_get_offset_Injected_m0A0435270B2E92920E1DE0A3DDCEC5AE150D39FD (void);
// 0x00000055 System.Void UnityEngine.Collider2D::set_offset_Injected(UnityEngine.Vector2&)
extern void Collider2D_set_offset_Injected_mB9F00193F0479308070EDB19FD82490340B17A8A (void);
// 0x00000056 System.Void UnityEngine.CircleCollider2D::set_radius(System.Single)
extern void CircleCollider2D_set_radius_m**************************************** (void);
// 0x00000057 UnityEngine.Vector2 UnityEngine.BoxCollider2D::get_size()
extern void BoxCollider2D_get_size_m011E7AA7861BF58898A64D986A4235C1E2061BF9 (void);
// 0x00000058 System.Void UnityEngine.BoxCollider2D::set_size(UnityEngine.Vector2)
extern void BoxCollider2D_set_size_m8460A38ADDD4BE82BE1F416DE3D7AFB87EBA6760 (void);
// 0x00000059 System.Void UnityEngine.BoxCollider2D::get_size_Injected(UnityEngine.Vector2&)
extern void BoxCollider2D_get_size_Injected_m84DAF1A8224C23D55F083622BD9C6CE7A6891CD4 (void);
// 0x0000005A System.Void UnityEngine.BoxCollider2D::set_size_Injected(UnityEngine.Vector2&)
extern void BoxCollider2D_set_size_Injected_mB8DEA9F8BBB15256BAE3092D37FDA1AE9D081401 (void);
// 0x0000005B System.Void UnityEngine.PolygonCollider2D::SetPath(System.Int32,UnityEngine.Vector2[])
extern void PolygonCollider2D_SetPath_m77B8966A290430F608BE6B207652B55B522151CF (void);
// 0x0000005C System.Void UnityEngine.PolygonCollider2D::SetPath_Internal(System.Int32,UnityEngine.Vector2[])
extern void PolygonCollider2D_SetPath_Internal_mD78EEDF737B1B7A9C7393F68F226CF85BA5C753E (void);
// 0x0000005D UnityEngine.Rigidbody2D UnityEngine.Joint2D::get_connectedBody()
extern void Joint2D_get_connectedBody_mEC1420DC0F40F31D35494B44B8100C706A93AECF (void);
// 0x0000005E System.Void UnityEngine.Joint2D::set_connectedBody(UnityEngine.Rigidbody2D)
extern void Joint2D_set_connectedBody_mDFAD1EA275D93EDFBCCD52FC5562DED5A556C4F7 (void);
// 0x0000005F System.Void UnityEngine.AnchoredJoint2D::set_connectedAnchor(UnityEngine.Vector2)
extern void AnchoredJoint2D_set_connectedAnchor_m550390539879112FF40C86FC72CE11658A87823E (void);
// 0x00000060 System.Void UnityEngine.AnchoredJoint2D::set_connectedAnchor_Injected(UnityEngine.Vector2&)
extern void AnchoredJoint2D_set_connectedAnchor_Injected_mF12556AF2EA89E3B8E999606A1FCE4E7A6291271 (void);
// 0x00000061 System.Void UnityEngine.HingeJoint2D::set_useLimits(System.Boolean)
extern void HingeJoint2D_set_useLimits_mF3BB12F8FDA2415A8C2CBEC57681B12571129B94 (void);
// 0x00000062 System.Void UnityEngine.HingeJoint2D::set_limits(UnityEngine.JointAngleLimits2D)
extern void HingeJoint2D_set_limits_mF9B0EB84DF9990EDF97F503BA379D32BF7699DC3 (void);
// 0x00000063 System.Void UnityEngine.HingeJoint2D::set_limits_Injected(UnityEngine.JointAngleLimits2D&)
extern void HingeJoint2D_set_limits_Injected_m3038861DECBE7611F0DFB76DE7B8751008563052 (void);
static Il2CppMethodPointer s_methodPointers[99] = 
{
	PhysicsScene2D_ToString_mDA6F499BD218AA31A450D918BB6C1890A9CE1109,
	PhysicsScene2D_GetHashCode_m4B5D8DCBA0AD6E5767C4D7A6AD6BC789EB19C8F5,
	PhysicsScene2D_Equals_m0C61F175C3B1BB308ADBC2AB323CEC45D1B5E99C,
	PhysicsScene2D_Equals_m581586F404E7A3BD3B6F0A05362974A6B523A2DA,
	PhysicsScene2D_Raycast_m22F55CAAA1B34A02757A5C6E2B573F6464B32723,
	PhysicsScene2D_Raycast_m167DAEC271F46133013EB0AAF2C7807064EBB3F2,
	PhysicsScene2D_Raycast_Internal_mB78C9A1BA2638CA23C86CAC509EEA18A4DDDDDD2,
	PhysicsScene2D_Raycast_m3B011D4A6CA691739178E253665799A7AD0CB423,
	PhysicsScene2D_RaycastArray_Internal_m9FDAC65FD7CE17757CF79F077D659AE5E561968A,
	PhysicsScene2D_Raycast_m6FB2BBC4E3BE53114D7F6EFA975F5AF703ADCDC0,
	PhysicsScene2D_RaycastList_Internal_m6CBBE8A1D09F0252C2A4BDD0D457A5A397960EA6,
	PhysicsScene2D_CircleCast_m****************************************,
	PhysicsScene2D_CircleCast_Internal_m****************************************,
	PhysicsScene2D_GetRayIntersection_mB6F14C8BB95609094BE9BDB218483EAAC4117B5A,
	PhysicsScene2D_GetRayIntersectionArray_Internal_m0B3286A34048B64C4BA18262AB7FF0940DCA3149,
	PhysicsScene2D_OverlapPoint_mD32EDA10783DDB83BA2889FCBB7A89186E0B43F0,
	PhysicsScene2D_OverlapPoint_Internal_m778B6343B3DCE95875A5D3C1EAB272D184087394,
	PhysicsScene2D_OverlapPoint_m253E6EEA2E3C53873A1259817DCA8C9B3364F97D,
	PhysicsScene2D_OverlapPointArray_Internal_m673CD8AB0F45A4AF6EEF990B6D5C4D7591866478,
	PhysicsScene2D_Raycast_Internal_Injected_mAC812B5A63C160F6D05024FF7D4D497EF89ED7B5,
	PhysicsScene2D_RaycastArray_Internal_Injected_mC84C0FB26E6E586FF0CC6E2EAF2DF0774ED5EEE5,
	PhysicsScene2D_RaycastList_Internal_Injected_m2B17C3C3E823E27EEB20BCA02274EBAFCFB9C0A2,
	PhysicsScene2D_CircleCast_Internal_Injected_m****************************************,
	PhysicsScene2D_GetRayIntersectionArray_Internal_Injected_mC4EB4FECE49A1D38C1D4E38BE16749839F0E4AC6,
	PhysicsScene2D_OverlapPoint_Internal_Injected_mF6785A066BC9BFC02C3F8E2D4C741AB251D86A2C,
	PhysicsScene2D_OverlapPointArray_Internal_Injected_m372BA00AAB4FDC276BF500F6C8ED036D42A73449,
	Physics2D_get_defaultPhysicsScene_m229B7F5325C13EF4170DFE4110BEE6AB6E28A887,
	Physics2D_get_queriesHitTriggers_m799BFBB4A3F711661EE0FCD570BD6CF36B529949,
	Physics2D_IgnoreCollision_m9F4792711E248B5ABAD555BF5A74A5B8DC67E68B,
	Physics2D_IgnoreCollision_m428EBF68F58C0C48C08C15E2967AF69AE2AC8750,
	Physics2D_Raycast_m32FE24C048CA831A45C16BC97DABF0419B9262E9,
	Physics2D_Raycast_mE585245B02AECC99BFAF035418CD46506B6B68F8,
	Physics2D_Raycast_m534D6FF7F823CF237C2CB7786587399F3BCEE6AD,
	Physics2D_Raycast_m8A061CCEF534EE47A4BB54FC1862C1AD87A15392,
	Physics2D_Raycast_m5C80748705F8C164CC6607A30C318AD4EC731DF7,
	Physics2D_Raycast_m59DD98F3E138545349E0D53EF411AB2BF2105EA9,
	Physics2D_Raycast_mA7B66B224626233CE226AB1C2EBA456CFE0B680D,
	Physics2D_Raycast_mE51C8B4D6E936969B6A1A625CED083BE52143D71,
	Physics2D_CircleCast_m****************************************,
	Physics2D_GetRayIntersectionAll_m62DEA481DAC2D73C176DDD71B527A392A8534141,
	Physics2D_GetRayIntersectionAll_m7570FAD655B942FAC58D512A1358D97B6356FE9A,
	Physics2D_GetRayIntersectionAll_m6A809028A55DD179F1813B427E3ADA507DE69160,
	Physics2D_GetRayIntersectionAll_Internal_mCC82148312B653E8A37AC1772A7D5F54DE3623A5,
	Physics2D_GetRayIntersectionNonAlloc_mBEA738AC1BEAAA682A0D28820B2F4D5971DA8469,
	Physics2D_GetRayIntersectionNonAlloc_m8B48DB9FE5B47A9D943E23349C6BC0F1B764BA02,
	Physics2D_GetRayIntersectionNonAlloc_mBD15FAD30E398E6E568EC1EEE20650FF31BF5C47,
	Physics2D_OverlapPoint_m6EF32AD5A115E7C0ED0041ED1959E96D4AC1F8F5,
	Physics2D_OverlapPointNonAlloc_m1D37468831AF3BDC65C3105003611756DFA326FD,
	Physics2D__cctor_m84E6BF1DDF3530A4A43A775DAADB6047D993E6A1,
	Physics2D_GetRayIntersectionAll_Internal_Injected_mF15566343788554608F269979DEA87BD72EF0D3E,
	ContactFilter2D_CheckConsistency_m4B6DAA0197DC017E3B7A8B8F661729431504C5D1,
	ContactFilter2D_SetLayerMask_m925C98BC2EEAA78349B3ED3654BC3C362743BBDE,
	ContactFilter2D_SetDepth_m63872B3F8BBDB962AF44D064BA328599C74D840F,
	ContactFilter2D_CreateLegacyFilter_mC4B0C3BBEA07C14C5FD498C3313140B3DF247CE6,
	ContactFilter2D_CheckConsistency_Injected_m3942B7D0F0086DCF85ECC5847ED9DD374036DAD1,
	Collision2D_get_collider_mA7687EDB0D47A2F211BFE8DB89266B9AA05CFDDD,
	Collision2D_get_rigidbody_m82AF533E110DFDBDED6D6C74EB479902E813D42E,
	Collision2D_get_gameObject_m6F07B9CA1FAD187933EB6D8E44BD9F870658F89F,
	JointAngleLimits2D_set_min_m7700A6023C026FAF2ED4CEDF83BFF6F5D11691A5,
	JointAngleLimits2D_set_max_mA87719606BE27E237FCFA1795A666DE62B17AD23,
	RaycastHit2D_get_point_m10D5AB3B26EAE62583BE35CFA13A3E40BDAF30AE,
	RaycastHit2D_get_normal_m6F8B9F4018EFA126CC33126E8E42B09BB5A82637,
	RaycastHit2D_get_distance_mA910B45BD349A8F70139F6BC1E686F47F40A1662,
	RaycastHit2D_get_collider_m00F7EC55C36F39E2ED64B31354FB4D9C8938D563,
	Rigidbody2D_get_position_mEC7D07E3478BEF5A2A0E22C91CA54935376F84C2,
	Rigidbody2D_set_position_m1604084713EB195D44B8B411D4BCAFA5941E3413,
	Rigidbody2D_get_rotation_mD58E62EDB334FCDF7914A94C940F7903E8ADBBFF,
	Rigidbody2D_MovePosition_mB4493BFC30B2FEBB02C7819AAE626871939D5BD9,
	Rigidbody2D_MoveRotation_mB550025C3B09DB909CB895609BF642EF768C5B22,
	Rigidbody2D_MoveRotation_Angle_mE1BD3C755F9FB7782274F50D55496441F8675245,
	Rigidbody2D_get_mass_mE62570BBAB2CE7ACC2FA3B20EE5EB8FBDBEF019F,
	Rigidbody2D_set_mass_mF66267DE577C5EC36F27FBF2F48FA405DFA24791,
	Rigidbody2D_set_gravityScale_mEDC82EE2ED74DA9C5AB8A0C4A929B09149522BBF,
	Rigidbody2D_set_bodyType_m8D34999918D42B2DF16FAAB4F237A8663EA8406B,
	Rigidbody2D_set_isKinematic_m82FBA7C4F4EB2569AC77D4767061F9CE0F8C3FEB,
	Rigidbody2D__ctor_m6CE72A013ED172CA657C0E00E8C04C0B523C78C5,
	Rigidbody2D_get_position_Injected_m1B97DC47EDF69F0506CEC24603C3A330B7E8FAB4,
	Rigidbody2D_set_position_Injected_mB05AB62CD1D243D071F60E48C9905587ACB94933,
	Rigidbody2D_MovePosition_Injected_mCD46D7FE92C344BB29C67C5D23068982445AB549,
	Collider2D_set_isTrigger_m129C3059CC21789EFA198E153A3C82662E3BFF52,
	Collider2D_get_offset_m63144560DFF782608BA6627DE192D6337821995C,
	Collider2D_set_offset_m496DDE5487C59E9E8D6B468E8918EE1EFC625E38,
	Collider2D__ctor_mB3AFF9CA18C8FA8E4F75DC45CA2D4A423A40F23D,
	Collider2D_get_offset_Injected_m0A0435270B2E92920E1DE0A3DDCEC5AE150D39FD,
	Collider2D_set_offset_Injected_mB9F00193F0479308070EDB19FD82490340B17A8A,
	CircleCollider2D_set_radius_m****************************************,
	BoxCollider2D_get_size_m011E7AA7861BF58898A64D986A4235C1E2061BF9,
	BoxCollider2D_set_size_m8460A38ADDD4BE82BE1F416DE3D7AFB87EBA6760,
	BoxCollider2D_get_size_Injected_m84DAF1A8224C23D55F083622BD9C6CE7A6891CD4,
	BoxCollider2D_set_size_Injected_mB8DEA9F8BBB15256BAE3092D37FDA1AE9D081401,
	PolygonCollider2D_SetPath_m77B8966A290430F608BE6B207652B55B522151CF,
	PolygonCollider2D_SetPath_Internal_mD78EEDF737B1B7A9C7393F68F226CF85BA5C753E,
	Joint2D_get_connectedBody_mEC1420DC0F40F31D35494B44B8100C706A93AECF,
	Joint2D_set_connectedBody_mDFAD1EA275D93EDFBCCD52FC5562DED5A556C4F7,
	AnchoredJoint2D_set_connectedAnchor_m550390539879112FF40C86FC72CE11658A87823E,
	AnchoredJoint2D_set_connectedAnchor_Injected_mF12556AF2EA89E3B8E999606A1FCE4E7A6291271,
	HingeJoint2D_set_useLimits_mF3BB12F8FDA2415A8C2CBEC57681B12571129B94,
	HingeJoint2D_set_limits_mF9B0EB84DF9990EDF97F503BA379D32BF7699DC3,
	HingeJoint2D_set_limits_Injected_m3038861DECBE7611F0DFB76DE7B8751008563052,
};
extern void PhysicsScene2D_ToString_mDA6F499BD218AA31A450D918BB6C1890A9CE1109_AdjustorThunk (void);
extern void PhysicsScene2D_GetHashCode_m4B5D8DCBA0AD6E5767C4D7A6AD6BC789EB19C8F5_AdjustorThunk (void);
extern void PhysicsScene2D_Equals_m0C61F175C3B1BB308ADBC2AB323CEC45D1B5E99C_AdjustorThunk (void);
extern void PhysicsScene2D_Equals_m581586F404E7A3BD3B6F0A05362974A6B523A2DA_AdjustorThunk (void);
extern void PhysicsScene2D_Raycast_m22F55CAAA1B34A02757A5C6E2B573F6464B32723_AdjustorThunk (void);
extern void PhysicsScene2D_Raycast_m167DAEC271F46133013EB0AAF2C7807064EBB3F2_AdjustorThunk (void);
extern void PhysicsScene2D_Raycast_m3B011D4A6CA691739178E253665799A7AD0CB423_AdjustorThunk (void);
extern void PhysicsScene2D_Raycast_m6FB2BBC4E3BE53114D7F6EFA975F5AF703ADCDC0_AdjustorThunk (void);
extern void PhysicsScene2D_CircleCast_m****************************************_AdjustorThunk (void);
extern void PhysicsScene2D_GetRayIntersection_mB6F14C8BB95609094BE9BDB218483EAAC4117B5A_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapPoint_mD32EDA10783DDB83BA2889FCBB7A89186E0B43F0_AdjustorThunk (void);
extern void PhysicsScene2D_OverlapPoint_m253E6EEA2E3C53873A1259817DCA8C9B3364F97D_AdjustorThunk (void);
extern void ContactFilter2D_CheckConsistency_m4B6DAA0197DC017E3B7A8B8F661729431504C5D1_AdjustorThunk (void);
extern void ContactFilter2D_SetLayerMask_m925C98BC2EEAA78349B3ED3654BC3C362743BBDE_AdjustorThunk (void);
extern void ContactFilter2D_SetDepth_m63872B3F8BBDB962AF44D064BA328599C74D840F_AdjustorThunk (void);
extern void JointAngleLimits2D_set_min_m7700A6023C026FAF2ED4CEDF83BFF6F5D11691A5_AdjustorThunk (void);
extern void JointAngleLimits2D_set_max_mA87719606BE27E237FCFA1795A666DE62B17AD23_AdjustorThunk (void);
extern void RaycastHit2D_get_point_m10D5AB3B26EAE62583BE35CFA13A3E40BDAF30AE_AdjustorThunk (void);
extern void RaycastHit2D_get_normal_m6F8B9F4018EFA126CC33126E8E42B09BB5A82637_AdjustorThunk (void);
extern void RaycastHit2D_get_distance_mA910B45BD349A8F70139F6BC1E686F47F40A1662_AdjustorThunk (void);
extern void RaycastHit2D_get_collider_m00F7EC55C36F39E2ED64B31354FB4D9C8938D563_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[21] = 
{
	{ 0x06000001, PhysicsScene2D_ToString_mDA6F499BD218AA31A450D918BB6C1890A9CE1109_AdjustorThunk },
	{ 0x06000002, PhysicsScene2D_GetHashCode_m4B5D8DCBA0AD6E5767C4D7A6AD6BC789EB19C8F5_AdjustorThunk },
	{ 0x06000003, PhysicsScene2D_Equals_m0C61F175C3B1BB308ADBC2AB323CEC45D1B5E99C_AdjustorThunk },
	{ 0x06000004, PhysicsScene2D_Equals_m581586F404E7A3BD3B6F0A05362974A6B523A2DA_AdjustorThunk },
	{ 0x06000005, PhysicsScene2D_Raycast_m22F55CAAA1B34A02757A5C6E2B573F6464B32723_AdjustorThunk },
	{ 0x06000006, PhysicsScene2D_Raycast_m167DAEC271F46133013EB0AAF2C7807064EBB3F2_AdjustorThunk },
	{ 0x06000008, PhysicsScene2D_Raycast_m3B011D4A6CA691739178E253665799A7AD0CB423_AdjustorThunk },
	{ 0x0600000A, PhysicsScene2D_Raycast_m6FB2BBC4E3BE53114D7F6EFA975F5AF703ADCDC0_AdjustorThunk },
	{ 0x0600000C, PhysicsScene2D_CircleCast_m****************************************_AdjustorThunk },
	{ 0x0600000E, PhysicsScene2D_GetRayIntersection_mB6F14C8BB95609094BE9BDB218483EAAC4117B5A_AdjustorThunk },
	{ 0x06000010, PhysicsScene2D_OverlapPoint_mD32EDA10783DDB83BA2889FCBB7A89186E0B43F0_AdjustorThunk },
	{ 0x06000012, PhysicsScene2D_OverlapPoint_m253E6EEA2E3C53873A1259817DCA8C9B3364F97D_AdjustorThunk },
	{ 0x06000033, ContactFilter2D_CheckConsistency_m4B6DAA0197DC017E3B7A8B8F661729431504C5D1_AdjustorThunk },
	{ 0x06000034, ContactFilter2D_SetLayerMask_m925C98BC2EEAA78349B3ED3654BC3C362743BBDE_AdjustorThunk },
	{ 0x06000035, ContactFilter2D_SetDepth_m63872B3F8BBDB962AF44D064BA328599C74D840F_AdjustorThunk },
	{ 0x0600003B, JointAngleLimits2D_set_min_m7700A6023C026FAF2ED4CEDF83BFF6F5D11691A5_AdjustorThunk },
	{ 0x0600003C, JointAngleLimits2D_set_max_mA87719606BE27E237FCFA1795A666DE62B17AD23_AdjustorThunk },
	{ 0x0600003D, RaycastHit2D_get_point_m10D5AB3B26EAE62583BE35CFA13A3E40BDAF30AE_AdjustorThunk },
	{ 0x0600003E, RaycastHit2D_get_normal_m6F8B9F4018EFA126CC33126E8E42B09BB5A82637_AdjustorThunk },
	{ 0x0600003F, RaycastHit2D_get_distance_mA910B45BD349A8F70139F6BC1E686F47F40A1662_AdjustorThunk },
	{ 0x06000040, RaycastHit2D_get_collider_m00F7EC55C36F39E2ED64B31354FB4D9C8938D563_AdjustorThunk },
};
static const int32_t s_InvokerIndices[99] = 
{
	3916,
	3893,
	2591,
	2596,
	788,
	787,
	4540,
	307,
	4255,
	307,
	4255,
	354,
	4304,
	581,
	4256,
	1741,
	5209,
	1046,
	4766,
	4317,
	4240,
	4240,
	4184,
	4241,
	5138,
	4664,
	6272,
	6256,
	5834,
	5334,
	5634,
	5229,
	4913,
	4542,
	4305,
	4769,
	4460,
	4460,
	4541,
	6092,
	5606,
	5210,
	4532,
	5529,
	5111,
	4767,
	5616,
	5114,
	6289,
	4471,
	3967,
	3321,
	2201,
	5063,
	6206,
	3916,
	3916,
	3916,
	3380,
	3380,
	3963,
	3963,
	3951,
	3916,
	3963,
	3396,
	3951,
	3396,
	3380,
	3380,
	3951,
	3380,
	3380,
	3313,
	3276,
	3967,
	3255,
	3255,
	3255,
	3276,
	3963,
	3396,
	3967,
	3255,
	3255,
	3380,
	3963,
	3396,
	3255,
	3255,
	1981,
	1981,
	3916,
	3348,
	3396,
	3255,
	3276,
	3318,
	3255,
};
extern const CustomAttributesCacheGenerator g_UnityEngine_Physics2DModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_Physics2DModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_Physics2DModule_CodeGenModule = 
{
	"UnityEngine.Physics2DModule.dll",
	99,
	s_methodPointers,
	21,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_UnityEngine_Physics2DModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

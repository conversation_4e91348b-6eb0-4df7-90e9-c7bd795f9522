﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void UnityEngine.GridLayout::DoNothing()
extern void GridLayout_DoNothing_m36201F4787709460C994921B16CAC95CD490E0A1 (void);
static Il2CppMethodPointer s_methodPointers[1] = 
{
	GridLayout_DoNothing_m36201F4787709460C994921B16CAC95CD490E0A1,
};
static const int32_t s_InvokerIndices[1] = 
{
	3967,
};
extern const CustomAttributesCacheGenerator g_UnityEngine_GridModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_GridModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_GridModule_CodeGenModule = 
{
	"UnityEngine.GridModule.dll",
	1,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_UnityEngine_GridModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

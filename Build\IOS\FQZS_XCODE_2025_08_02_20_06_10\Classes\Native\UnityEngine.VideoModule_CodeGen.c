﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 UnityEngine.Playables.PlayableHandle UnityEngine.Experimental.Video.VideoClipPlayable::GetHandle()
extern void VideoClipPlayable_GetHandle_mE4D1CCE51DFBB2228B54187915C00D4E5FAD4F86 (void);
// 0x00000002 System.Boolean UnityEngine.Experimental.Video.VideoClipPlayable::Equals(UnityEngine.Experimental.Video.VideoClipPlayable)
extern void VideoClipPlayable_Equals_m8BFF58C5788058758A88D0C2E0295D994BAC1E1E (void);
// 0x00000003 System.Int64 UnityEngine.Video.VideoPlayer::get_frame()
extern void VideoPlayer_get_frame_m86205149C0DB9B9A42C1177F846950B495E6882E (void);
// 0x00000004 System.UInt64 UnityEngine.Video.VideoPlayer::get_frameCount()
extern void VideoPlayer_get_frameCount_mE7B49C44E393A2EC65424C29F2D6563A130D9044 (void);
// 0x00000005 System.Void UnityEngine.Video.VideoPlayer::InvokePrepareCompletedCallback_Internal(UnityEngine.Video.VideoPlayer)
extern void VideoPlayer_InvokePrepareCompletedCallback_Internal_m86BE54C3B869C54C107705498D6777E9C4192D12 (void);
// 0x00000006 System.Void UnityEngine.Video.VideoPlayer::InvokeFrameReadyCallback_Internal(UnityEngine.Video.VideoPlayer,System.Int64)
extern void VideoPlayer_InvokeFrameReadyCallback_Internal_m1A79A34257FE437BB3F20BEA492C6DB06DE33769 (void);
// 0x00000007 System.Void UnityEngine.Video.VideoPlayer::InvokeLoopPointReachedCallback_Internal(UnityEngine.Video.VideoPlayer)
extern void VideoPlayer_InvokeLoopPointReachedCallback_Internal_m04A345F7C6C60BAFCBCD8CC992E0350A65CCE3E3 (void);
// 0x00000008 System.Void UnityEngine.Video.VideoPlayer::InvokeStartedCallback_Internal(UnityEngine.Video.VideoPlayer)
extern void VideoPlayer_InvokeStartedCallback_Internal_m8FC0E6FC21A6B50448A02D7C2CC8C421E55D15C5 (void);
// 0x00000009 System.Void UnityEngine.Video.VideoPlayer::InvokeFrameDroppedCallback_Internal(UnityEngine.Video.VideoPlayer)
extern void VideoPlayer_InvokeFrameDroppedCallback_Internal_m6AAB41354502AFCD116F8059AE9B8E05EEDC96B9 (void);
// 0x0000000A System.Void UnityEngine.Video.VideoPlayer::InvokeErrorReceivedCallback_Internal(UnityEngine.Video.VideoPlayer,System.String)
extern void VideoPlayer_InvokeErrorReceivedCallback_Internal_m56224B0CB1C6DCC8E6E1269CD0577AC88382C6EF (void);
// 0x0000000B System.Void UnityEngine.Video.VideoPlayer::InvokeSeekCompletedCallback_Internal(UnityEngine.Video.VideoPlayer)
extern void VideoPlayer_InvokeSeekCompletedCallback_Internal_m6E6B5D15AB9E4D3897EE820042FBC85DCC57EE3C (void);
// 0x0000000C System.Void UnityEngine.Video.VideoPlayer::InvokeClockResyncOccurredCallback_Internal(UnityEngine.Video.VideoPlayer,System.Double)
extern void VideoPlayer_InvokeClockResyncOccurredCallback_Internal_mE2E1415D1F018F6EE3B386F6D332EB7449EB86E0 (void);
// 0x0000000D System.Void UnityEngine.Video.VideoPlayer/EventHandler::.ctor(System.Object,System.IntPtr)
extern void EventHandler__ctor_mA1A6722C36644D8F49E5A5008B68352561E03400 (void);
// 0x0000000E System.Void UnityEngine.Video.VideoPlayer/EventHandler::Invoke(UnityEngine.Video.VideoPlayer)
extern void EventHandler_Invoke_m0B3144B6BE309B7644E15FB9668F595F92F4C684 (void);
// 0x0000000F System.IAsyncResult UnityEngine.Video.VideoPlayer/EventHandler::BeginInvoke(UnityEngine.Video.VideoPlayer,System.AsyncCallback,System.Object)
extern void EventHandler_BeginInvoke_m3C25E8FC4549E9B4C9A801E4F81D481B6528F1AB (void);
// 0x00000010 System.Void UnityEngine.Video.VideoPlayer/EventHandler::EndInvoke(System.IAsyncResult)
extern void EventHandler_EndInvoke_mD3FAA4D9AB6E36C74DF8DADD464FD3330FAE24F7 (void);
// 0x00000011 System.Void UnityEngine.Video.VideoPlayer/ErrorEventHandler::.ctor(System.Object,System.IntPtr)
extern void ErrorEventHandler__ctor_m655F594F6CC7F8735011F785B0A0429BC08A7502 (void);
// 0x00000012 System.Void UnityEngine.Video.VideoPlayer/ErrorEventHandler::Invoke(UnityEngine.Video.VideoPlayer,System.String)
extern void ErrorEventHandler_Invoke_mF6EEEB7A362D50D2FE7B757648470E3B1F809414 (void);
// 0x00000013 System.IAsyncResult UnityEngine.Video.VideoPlayer/ErrorEventHandler::BeginInvoke(UnityEngine.Video.VideoPlayer,System.String,System.AsyncCallback,System.Object)
extern void ErrorEventHandler_BeginInvoke_mD229D40406E6FF4A9E70A55AD9A97A0C4D1B24E3 (void);
// 0x00000014 System.Void UnityEngine.Video.VideoPlayer/ErrorEventHandler::EndInvoke(System.IAsyncResult)
extern void ErrorEventHandler_EndInvoke_m71707244BC66E8031D49D3D23FDC4F2D9361FC79 (void);
// 0x00000015 System.Void UnityEngine.Video.VideoPlayer/FrameReadyEventHandler::.ctor(System.Object,System.IntPtr)
extern void FrameReadyEventHandler__ctor_mA7834775230CC8D9C0337941FEF38222CF5AD9D6 (void);
// 0x00000016 System.Void UnityEngine.Video.VideoPlayer/FrameReadyEventHandler::Invoke(UnityEngine.Video.VideoPlayer,System.Int64)
extern void FrameReadyEventHandler_Invoke_m4BA6EA29ED4CF35A85BAC6AD123D809474FBD325 (void);
// 0x00000017 System.IAsyncResult UnityEngine.Video.VideoPlayer/FrameReadyEventHandler::BeginInvoke(UnityEngine.Video.VideoPlayer,System.Int64,System.AsyncCallback,System.Object)
extern void FrameReadyEventHandler_BeginInvoke_m11D1FDEF49CAED27A4E4FACDC9D281B79F13698E (void);
// 0x00000018 System.Void UnityEngine.Video.VideoPlayer/FrameReadyEventHandler::EndInvoke(System.IAsyncResult)
extern void FrameReadyEventHandler_EndInvoke_mBE6AA092028120E465EAFF6125BAD2574CBF45D5 (void);
// 0x00000019 System.Void UnityEngine.Video.VideoPlayer/TimeEventHandler::.ctor(System.Object,System.IntPtr)
extern void TimeEventHandler__ctor_m085DCE43EA4D04D7146A1A8BFA8175082862E1F5 (void);
// 0x0000001A System.Void UnityEngine.Video.VideoPlayer/TimeEventHandler::Invoke(UnityEngine.Video.VideoPlayer,System.Double)
extern void TimeEventHandler_Invoke_m2AEC774186134B8F0CEF7B61C5754F4AA3D4A60C (void);
// 0x0000001B System.IAsyncResult UnityEngine.Video.VideoPlayer/TimeEventHandler::BeginInvoke(UnityEngine.Video.VideoPlayer,System.Double,System.AsyncCallback,System.Object)
extern void TimeEventHandler_BeginInvoke_mE8C2EB6FF93C322A58952CE6735611162D194A8D (void);
// 0x0000001C System.Void UnityEngine.Video.VideoPlayer/TimeEventHandler::EndInvoke(System.IAsyncResult)
extern void TimeEventHandler_EndInvoke_m746D841C545DCBBB09A5FF25346DAEB2FD2DE37C (void);
static Il2CppMethodPointer s_methodPointers[28] = 
{
	VideoClipPlayable_GetHandle_mE4D1CCE51DFBB2228B54187915C00D4E5FAD4F86,
	VideoClipPlayable_Equals_m8BFF58C5788058758A88D0C2E0295D994BAC1E1E,
	VideoPlayer_get_frame_m86205149C0DB9B9A42C1177F846950B495E6882E,
	VideoPlayer_get_frameCount_mE7B49C44E393A2EC65424C29F2D6563A130D9044,
	VideoPlayer_InvokePrepareCompletedCallback_Internal_m86BE54C3B869C54C107705498D6777E9C4192D12,
	VideoPlayer_InvokeFrameReadyCallback_Internal_m1A79A34257FE437BB3F20BEA492C6DB06DE33769,
	VideoPlayer_InvokeLoopPointReachedCallback_Internal_m04A345F7C6C60BAFCBCD8CC992E0350A65CCE3E3,
	VideoPlayer_InvokeStartedCallback_Internal_m8FC0E6FC21A6B50448A02D7C2CC8C421E55D15C5,
	VideoPlayer_InvokeFrameDroppedCallback_Internal_m6AAB41354502AFCD116F8059AE9B8E05EEDC96B9,
	VideoPlayer_InvokeErrorReceivedCallback_Internal_m56224B0CB1C6DCC8E6E1269CD0577AC88382C6EF,
	VideoPlayer_InvokeSeekCompletedCallback_Internal_m6E6B5D15AB9E4D3897EE820042FBC85DCC57EE3C,
	VideoPlayer_InvokeClockResyncOccurredCallback_Internal_mE2E1415D1F018F6EE3B386F6D332EB7449EB86E0,
	EventHandler__ctor_mA1A6722C36644D8F49E5A5008B68352561E03400,
	EventHandler_Invoke_m0B3144B6BE309B7644E15FB9668F595F92F4C684,
	EventHandler_BeginInvoke_m3C25E8FC4549E9B4C9A801E4F81D481B6528F1AB,
	EventHandler_EndInvoke_mD3FAA4D9AB6E36C74DF8DADD464FD3330FAE24F7,
	ErrorEventHandler__ctor_m655F594F6CC7F8735011F785B0A0429BC08A7502,
	ErrorEventHandler_Invoke_mF6EEEB7A362D50D2FE7B757648470E3B1F809414,
	ErrorEventHandler_BeginInvoke_mD229D40406E6FF4A9E70A55AD9A97A0C4D1B24E3,
	ErrorEventHandler_EndInvoke_m71707244BC66E8031D49D3D23FDC4F2D9361FC79,
	FrameReadyEventHandler__ctor_mA7834775230CC8D9C0337941FEF38222CF5AD9D6,
	FrameReadyEventHandler_Invoke_m4BA6EA29ED4CF35A85BAC6AD123D809474FBD325,
	FrameReadyEventHandler_BeginInvoke_m11D1FDEF49CAED27A4E4FACDC9D281B79F13698E,
	FrameReadyEventHandler_EndInvoke_mBE6AA092028120E465EAFF6125BAD2574CBF45D5,
	TimeEventHandler__ctor_m085DCE43EA4D04D7146A1A8BFA8175082862E1F5,
	TimeEventHandler_Invoke_m2AEC774186134B8F0CEF7B61C5754F4AA3D4A60C,
	TimeEventHandler_BeginInvoke_mE8C2EB6FF93C322A58952CE6735611162D194A8D,
	TimeEventHandler_EndInvoke_m746D841C545DCBBB09A5FF25346DAEB2FD2DE37C,
};
extern void VideoClipPlayable_GetHandle_mE4D1CCE51DFBB2228B54187915C00D4E5FAD4F86_AdjustorThunk (void);
extern void VideoClipPlayable_Equals_m8BFF58C5788058758A88D0C2E0295D994BAC1E1E_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[2] = 
{
	{ 0x06000001, VideoClipPlayable_GetHandle_mE4D1CCE51DFBB2228B54187915C00D4E5FAD4F86_AdjustorThunk },
	{ 0x06000002, VideoClipPlayable_Equals_m8BFF58C5788058758A88D0C2E0295D994BAC1E1E_AdjustorThunk },
};
static const int32_t s_InvokerIndices[28] = 
{
	3922,
	2644,
	3894,
	3962,
	6218,
	5831,
	6218,
	6218,
	6218,
	5834,
	6218,
	5829,
	2169,
	3348,
	1129,
	3348,
	2169,
	2171,
	722,
	3348,
	2169,
	2168,
	711,
	3348,
	2169,
	2164,
	701,
	3348,
};
extern const CustomAttributesCacheGenerator g_UnityEngine_VideoModule_AttributeGenerators[];
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_VideoModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_VideoModule_CodeGenModule = 
{
	"UnityEngine.VideoModule.dll",
	28,
	s_methodPointers,
	2,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	g_UnityEngine_VideoModule_AttributeGenerators,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};

﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>
#include <stdint.h>


template <typename R>
struct VirtFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};

// System.Action`1<UnityEngine.AsyncOperation>
struct Action_1_tC1348BEB2C677FD60E4B65764CA3A1CAFF6DFB31;
// System.Collections.Generic.IEnumerable`1<UnityEngine.AssetBundle>
struct IEnumerable_1_t818A45F2EEE49E624492783498040DCC50A34E66;
// UnityEngine.AssetBundle[]
struct AssetBundleU5BU5D_t049219716584A9B3A1A6136429402193C7EB3A4D;
// System.Byte[]
struct ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726;
// System.Char[]
struct CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34;
// System.Delegate[]
struct DelegateU5BU5D_t677D8FE08A5F99E8EE49150B73966CD6E9BF7DB8;
// System.IntPtr[]
struct IntPtrU5BU5D_t27FC72B0409D75AAF33EC42498E8094E95FEE9A6;
// UnityEngine.Object[]
struct ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873;
// System.Diagnostics.StackTrace[]
struct StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971;
// System.String[]
struct StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A;
// System.Type[]
struct TypeU5BU5D_t85B10489E46F06CEC7C4B1CCBD0E01FAB6649755;
// System.ArgumentException
struct ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00;
// System.ArgumentNullException
struct ArgumentNullException_tFB5C4621957BC53A7D1B4FDD5C38B4D6E15DB8FB;
// UnityEngine.AssetBundle
struct AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4;
// UnityEngine.AssetBundleCreateRequest
struct AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A;
// UnityEngine.AssetBundleManifest
struct AssetBundleManifest_tC4C999A85A1FC2B60215566D0462EE645674160A;
// UnityEngine.AssetBundleRecompressOperation
struct AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31;
// UnityEngine.AssetBundleRequest
struct AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A;
// System.AsyncCallback
struct AsyncCallback_tA7921BEF974919C46FF8F9D9867C567B200BB0EA;
// UnityEngine.AsyncOperation
struct AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86;
// System.Reflection.Binder
struct Binder_t2BEE27FD84737D1E79BC47FD67F6D3DD2F2DDA30;
// System.DelegateData
struct DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288;
// System.IAsyncResult
struct IAsyncResult_tC9F97BF36FCF122D29D3101D80642278297BF370;
// System.Collections.IDictionary
struct IDictionary_t99871C56B8EC2452AC5C4CF3831695E617B89D3A;
// System.Reflection.MemberFilter
struct MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81;
// System.Reflection.MethodInfo
struct MethodInfo_t;
// System.NullReferenceException
struct NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724;
// UnityEngine.Object
struct Object_tF2F3778131EFF286AF62B7B013A170F95A91571A;
// UnityEngine.ResourceRequest
struct ResourceRequest_tD2D09E98C844087E6AB0F04532B7AA139558CBAD;
// System.Runtime.Serialization.SafeSerializationManager
struct SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F;
// System.Threading.SemaphoreSlim
struct SemaphoreSlim_t3EF85FC980AE57957BEBB6B78E81DE2E3233D385;
// System.IO.Stream
struct Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB;
// System.String
struct String_t;
// System.Type
struct Type_t;
// System.Void
struct Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5;
// System.IO.Stream/ReadWriteTask
struct ReadWriteTask_t32CD2C230786712954C1DB518DBE420A1F4C7974;

IL2CPP_EXTERN_C RuntimeClass* Action_1_tC1348BEB2C677FD60E4B65764CA3A1CAFF6DFB31_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ArgumentNullException_tFB5C4621957BC53A7D1B4FDD5C38B4D6E15DB8FB_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Math_tA269614262430118C9FC5C4D9EF4F61C812568F0_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Type_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral358518C7B9669F4CD21363772C3317136923413A;
IL2CPP_EXTERN_C String_t* _stringLiteral51AFAC88FE0F5AA20A5F618F48C4CEAB7CC3DB34;
IL2CPP_EXTERN_C String_t* _stringLiteral7C527C571BCCF5DF8ADCF7BF9EED0FE0DC9AF069;
IL2CPP_EXTERN_C String_t* _stringLiteral820724398B4A28E6B958D19B3FC28BB8BF2787D3;
IL2CPP_EXTERN_C String_t* _stringLiteralAD3DDCBBB6118D9DAC3520876EC8EA0BCBCD6C23;
IL2CPP_EXTERN_C String_t* _stringLiteralD6B4B2A0E6284497D6C035D6CC8503F491C92098;
IL2CPP_EXTERN_C String_t* _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709;
IL2CPP_EXTERN_C String_t* _stringLiteralF7E6C53D86D8ADC0DB8EFC0A2CD9877CC8BC9914;
IL2CPP_EXTERN_C const RuntimeMethod* AssetBundle_LoadAllAssetsAsync_m213D935C1F40DB2098F00BD852B5799F00542137_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AssetBundle_LoadAllAssets_mAB1A820C148FF25D811D16DC5C8F5D39882D480A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AssetBundle_LoadAssetAsync_m63C7C5654FA8D0824DC920A1B1530C37CCB3DF6E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AssetBundle_LoadAssetWithSubAssetsAsync_m655947AD33D64C1F71F822AEFDE3D2A57C809AF5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AssetBundle_LoadAssetWithSubAssets_mAABE29A8F1719A85DC474932FF12A44B29CACB45_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AssetBundle_LoadAsset_m9139320F8B6D3E43B7D29AA7A60030306AE0A2C6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AssetBundle_ValidateLoadFromStream_m39F4CB6F1717C48017F09FFC1E537704BB47A5A1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_0_0_0_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct AssetBundleU5BU5D_t049219716584A9B3A1A6136429402193C7EB3A4D;
struct ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726;
struct ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873;
struct StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif

// <Module>
struct U3CModuleU3E_t7988782848E87B4A172D2199D2BAC372F93351FD 
{
public:

public:
};


// System.Object

struct Il2CppArrayBounds;

// System.Array


// UnityEngine.AssetBundleLoadingCache
struct AssetBundleLoadingCache_tB27042ED591063710423C2A149578B3C5255C22B  : public RuntimeObject
{
public:

public:
};


// System.MarshalByRefObject
struct MarshalByRefObject_tD4DF91B488B284F899417EC468D8E50E933306A8  : public RuntimeObject
{
public:
	// System.Object System.MarshalByRefObject::_identity
	RuntimeObject * ____identity_0;

public:
	inline static int32_t get_offset_of__identity_0() { return static_cast<int32_t>(offsetof(MarshalByRefObject_tD4DF91B488B284F899417EC468D8E50E933306A8, ____identity_0)); }
	inline RuntimeObject * get__identity_0() const { return ____identity_0; }
	inline RuntimeObject ** get_address_of__identity_0() { return &____identity_0; }
	inline void set__identity_0(RuntimeObject * value)
	{
		____identity_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____identity_0), (void*)value);
	}
};

// Native definition for P/Invoke marshalling of System.MarshalByRefObject
struct MarshalByRefObject_tD4DF91B488B284F899417EC468D8E50E933306A8_marshaled_pinvoke
{
	Il2CppIUnknown* ____identity_0;
};
// Native definition for COM marshalling of System.MarshalByRefObject
struct MarshalByRefObject_tD4DF91B488B284F899417EC468D8E50E933306A8_marshaled_com
{
	Il2CppIUnknown* ____identity_0;
};

// System.Reflection.MemberInfo
struct MemberInfo_t  : public RuntimeObject
{
public:

public:
};


// System.String
struct String_t  : public RuntimeObject
{
public:
	// System.Int32 System.String::m_stringLength
	int32_t ___m_stringLength_0;
	// System.Char System.String::m_firstChar
	Il2CppChar ___m_firstChar_1;

public:
	inline static int32_t get_offset_of_m_stringLength_0() { return static_cast<int32_t>(offsetof(String_t, ___m_stringLength_0)); }
	inline int32_t get_m_stringLength_0() const { return ___m_stringLength_0; }
	inline int32_t* get_address_of_m_stringLength_0() { return &___m_stringLength_0; }
	inline void set_m_stringLength_0(int32_t value)
	{
		___m_stringLength_0 = value;
	}

	inline static int32_t get_offset_of_m_firstChar_1() { return static_cast<int32_t>(offsetof(String_t, ___m_firstChar_1)); }
	inline Il2CppChar get_m_firstChar_1() const { return ___m_firstChar_1; }
	inline Il2CppChar* get_address_of_m_firstChar_1() { return &___m_firstChar_1; }
	inline void set_m_firstChar_1(Il2CppChar value)
	{
		___m_firstChar_1 = value;
	}
};


// System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52  : public RuntimeObject
{
public:

public:
};

// Native definition for P/Invoke marshalling of System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.ValueType
struct ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52_marshaled_com
{
};

// UnityEngine.YieldInstruction
struct YieldInstruction_tB0B4E05316710E51ECCC1E57174C27FE6DEBBEAF  : public RuntimeObject
{
public:

public:
};

// Native definition for P/Invoke marshalling of UnityEngine.YieldInstruction
struct YieldInstruction_tB0B4E05316710E51ECCC1E57174C27FE6DEBBEAF_marshaled_pinvoke
{
};
// Native definition for COM marshalling of UnityEngine.YieldInstruction
struct YieldInstruction_tB0B4E05316710E51ECCC1E57174C27FE6DEBBEAF_marshaled_com
{
};

// System.Boolean
struct Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37 
{
public:
	// System.Boolean System.Boolean::m_value
	bool ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37, ___m_value_0)); }
	inline bool get_m_value_0() const { return ___m_value_0; }
	inline bool* get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(bool value)
	{
		___m_value_0 = value;
	}
};


// System.Byte
struct Byte_t0111FAB8B8685667EDDAF77683F0D8F86B659056 
{
public:
	// System.Byte System.Byte::m_value
	uint8_t ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(Byte_t0111FAB8B8685667EDDAF77683F0D8F86B659056, ___m_value_0)); }
	inline uint8_t get_m_value_0() const { return ___m_value_0; }
	inline uint8_t* get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(uint8_t value)
	{
		___m_value_0 = value;
	}
};


// System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA  : public ValueType_tDBF999C1B75C48C68621878250DBF6CDBCF51E52
{
public:

public:
};

// Native definition for P/Invoke marshalling of System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.Enum
struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_marshaled_com
{
};

// UnityEngine.Hash128
struct Hash128_t1858EA099934FD6F2B769E5661C17A276A2AFE7A 
{
public:
	// System.UInt64 UnityEngine.Hash128::u64_0
	uint64_t ___u64_0_0;
	// System.UInt64 UnityEngine.Hash128::u64_1
	uint64_t ___u64_1_1;

public:
	inline static int32_t get_offset_of_u64_0_0() { return static_cast<int32_t>(offsetof(Hash128_t1858EA099934FD6F2B769E5661C17A276A2AFE7A, ___u64_0_0)); }
	inline uint64_t get_u64_0_0() const { return ___u64_0_0; }
	inline uint64_t* get_address_of_u64_0_0() { return &___u64_0_0; }
	inline void set_u64_0_0(uint64_t value)
	{
		___u64_0_0 = value;
	}

	inline static int32_t get_offset_of_u64_1_1() { return static_cast<int32_t>(offsetof(Hash128_t1858EA099934FD6F2B769E5661C17A276A2AFE7A, ___u64_1_1)); }
	inline uint64_t get_u64_1_1() const { return ___u64_1_1; }
	inline uint64_t* get_address_of_u64_1_1() { return &___u64_1_1; }
	inline void set_u64_1_1(uint64_t value)
	{
		___u64_1_1 = value;
	}
};


// System.Int32
struct Int32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046 
{
public:
	// System.Int32 System.Int32::m_value
	int32_t ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(Int32_tFDE5F8CD43D10453F6A2E0C77FE48C6CC7009046, ___m_value_0)); }
	inline int32_t get_m_value_0() const { return ___m_value_0; }
	inline int32_t* get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(int32_t value)
	{
		___m_value_0 = value;
	}
};


// System.IntPtr
struct IntPtr_t 
{
public:
	// System.Void* System.IntPtr::m_value
	void* ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(IntPtr_t, ___m_value_0)); }
	inline void* get_m_value_0() const { return ___m_value_0; }
	inline void** get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(void* value)
	{
		___m_value_0 = value;
	}
};


// System.IO.Stream
struct Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB  : public MarshalByRefObject_tD4DF91B488B284F899417EC468D8E50E933306A8
{
public:
	// System.IO.Stream/ReadWriteTask System.IO.Stream::_activeReadWriteTask
	ReadWriteTask_t32CD2C230786712954C1DB518DBE420A1F4C7974 * ____activeReadWriteTask_3;
	// System.Threading.SemaphoreSlim System.IO.Stream::_asyncActiveSemaphore
	SemaphoreSlim_t3EF85FC980AE57957BEBB6B78E81DE2E3233D385 * ____asyncActiveSemaphore_4;

public:
	inline static int32_t get_offset_of__activeReadWriteTask_3() { return static_cast<int32_t>(offsetof(Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB, ____activeReadWriteTask_3)); }
	inline ReadWriteTask_t32CD2C230786712954C1DB518DBE420A1F4C7974 * get__activeReadWriteTask_3() const { return ____activeReadWriteTask_3; }
	inline ReadWriteTask_t32CD2C230786712954C1DB518DBE420A1F4C7974 ** get_address_of__activeReadWriteTask_3() { return &____activeReadWriteTask_3; }
	inline void set__activeReadWriteTask_3(ReadWriteTask_t32CD2C230786712954C1DB518DBE420A1F4C7974 * value)
	{
		____activeReadWriteTask_3 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____activeReadWriteTask_3), (void*)value);
	}

	inline static int32_t get_offset_of__asyncActiveSemaphore_4() { return static_cast<int32_t>(offsetof(Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB, ____asyncActiveSemaphore_4)); }
	inline SemaphoreSlim_t3EF85FC980AE57957BEBB6B78E81DE2E3233D385 * get__asyncActiveSemaphore_4() const { return ____asyncActiveSemaphore_4; }
	inline SemaphoreSlim_t3EF85FC980AE57957BEBB6B78E81DE2E3233D385 ** get_address_of__asyncActiveSemaphore_4() { return &____asyncActiveSemaphore_4; }
	inline void set__asyncActiveSemaphore_4(SemaphoreSlim_t3EF85FC980AE57957BEBB6B78E81DE2E3233D385 * value)
	{
		____asyncActiveSemaphore_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____asyncActiveSemaphore_4), (void*)value);
	}
};


// System.UInt32
struct UInt32_tE60352A06233E4E69DD198BCC67142159F686B15 
{
public:
	// System.UInt32 System.UInt32::m_value
	uint32_t ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(UInt32_tE60352A06233E4E69DD198BCC67142159F686B15, ___m_value_0)); }
	inline uint32_t get_m_value_0() const { return ___m_value_0; }
	inline uint32_t* get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(uint32_t value)
	{
		___m_value_0 = value;
	}
};


// System.UInt64
struct UInt64_tEC57511B3E3CA2DBA1BEBD434C6983E31C943281 
{
public:
	// System.UInt64 System.UInt64::m_value
	uint64_t ___m_value_0;

public:
	inline static int32_t get_offset_of_m_value_0() { return static_cast<int32_t>(offsetof(UInt64_tEC57511B3E3CA2DBA1BEBD434C6983E31C943281, ___m_value_0)); }
	inline uint64_t get_m_value_0() const { return ___m_value_0; }
	inline uint64_t* get_address_of_m_value_0() { return &___m_value_0; }
	inline void set_m_value_0(uint64_t value)
	{
		___m_value_0 = value;
	}
};


// System.Void
struct Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5 
{
public:
	union
	{
		struct
		{
		};
		uint8_t Void_t700C6383A2A510C2CF4DD86DABD5CA9FF70ADAC5__padding[1];
	};

public:
};


// UnityEngine.AsyncOperation
struct AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86  : public YieldInstruction_tB0B4E05316710E51ECCC1E57174C27FE6DEBBEAF
{
public:
	// System.IntPtr UnityEngine.AsyncOperation::m_Ptr
	intptr_t ___m_Ptr_0;
	// System.Action`1<UnityEngine.AsyncOperation> UnityEngine.AsyncOperation::m_completeCallback
	Action_1_tC1348BEB2C677FD60E4B65764CA3A1CAFF6DFB31 * ___m_completeCallback_1;

public:
	inline static int32_t get_offset_of_m_Ptr_0() { return static_cast<int32_t>(offsetof(AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86, ___m_Ptr_0)); }
	inline intptr_t get_m_Ptr_0() const { return ___m_Ptr_0; }
	inline intptr_t* get_address_of_m_Ptr_0() { return &___m_Ptr_0; }
	inline void set_m_Ptr_0(intptr_t value)
	{
		___m_Ptr_0 = value;
	}

	inline static int32_t get_offset_of_m_completeCallback_1() { return static_cast<int32_t>(offsetof(AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86, ___m_completeCallback_1)); }
	inline Action_1_tC1348BEB2C677FD60E4B65764CA3A1CAFF6DFB31 * get_m_completeCallback_1() const { return ___m_completeCallback_1; }
	inline Action_1_tC1348BEB2C677FD60E4B65764CA3A1CAFF6DFB31 ** get_address_of_m_completeCallback_1() { return &___m_completeCallback_1; }
	inline void set_m_completeCallback_1(Action_1_tC1348BEB2C677FD60E4B65764CA3A1CAFF6DFB31 * value)
	{
		___m_completeCallback_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_completeCallback_1), (void*)value);
	}
};

// Native definition for P/Invoke marshalling of UnityEngine.AsyncOperation
struct AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86_marshaled_pinvoke : public YieldInstruction_tB0B4E05316710E51ECCC1E57174C27FE6DEBBEAF_marshaled_pinvoke
{
	intptr_t ___m_Ptr_0;
	Il2CppMethodPointer ___m_completeCallback_1;
};
// Native definition for COM marshalling of UnityEngine.AsyncOperation
struct AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86_marshaled_com : public YieldInstruction_tB0B4E05316710E51ECCC1E57174C27FE6DEBBEAF_marshaled_com
{
	intptr_t ___m_Ptr_0;
	Il2CppMethodPointer ___m_completeCallback_1;
};

// System.Reflection.BindingFlags
struct BindingFlags_tAAAB07D9AC588F0D55D844E51D7035E96DF94733 
{
public:
	// System.Int32 System.Reflection.BindingFlags::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(BindingFlags_tAAAB07D9AC588F0D55D844E51D7035E96DF94733, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// UnityEngine.CompressionLevel
struct CompressionLevel_tACE3F8D2D352975092CA86A798BFB53221EE2B10 
{
public:
	// System.Int32 UnityEngine.CompressionLevel::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(CompressionLevel_tACE3F8D2D352975092CA86A798BFB53221EE2B10, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// UnityEngine.CompressionType
struct CompressionType_t32109D255D2D2009636110C09E98CC584101B426 
{
public:
	// System.Int32 UnityEngine.CompressionType::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(CompressionType_t32109D255D2D2009636110C09E98CC584101B426, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// System.Delegate
struct Delegate_t  : public RuntimeObject
{
public:
	// System.IntPtr System.Delegate::method_ptr
	Il2CppMethodPointer ___method_ptr_0;
	// System.IntPtr System.Delegate::invoke_impl
	intptr_t ___invoke_impl_1;
	// System.Object System.Delegate::m_target
	RuntimeObject * ___m_target_2;
	// System.IntPtr System.Delegate::method
	intptr_t ___method_3;
	// System.IntPtr System.Delegate::delegate_trampoline
	intptr_t ___delegate_trampoline_4;
	// System.IntPtr System.Delegate::extra_arg
	intptr_t ___extra_arg_5;
	// System.IntPtr System.Delegate::method_code
	intptr_t ___method_code_6;
	// System.Reflection.MethodInfo System.Delegate::method_info
	MethodInfo_t * ___method_info_7;
	// System.Reflection.MethodInfo System.Delegate::original_method_info
	MethodInfo_t * ___original_method_info_8;
	// System.DelegateData System.Delegate::data
	DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 * ___data_9;
	// System.Boolean System.Delegate::method_is_virtual
	bool ___method_is_virtual_10;

public:
	inline static int32_t get_offset_of_method_ptr_0() { return static_cast<int32_t>(offsetof(Delegate_t, ___method_ptr_0)); }
	inline Il2CppMethodPointer get_method_ptr_0() const { return ___method_ptr_0; }
	inline Il2CppMethodPointer* get_address_of_method_ptr_0() { return &___method_ptr_0; }
	inline void set_method_ptr_0(Il2CppMethodPointer value)
	{
		___method_ptr_0 = value;
	}

	inline static int32_t get_offset_of_invoke_impl_1() { return static_cast<int32_t>(offsetof(Delegate_t, ___invoke_impl_1)); }
	inline intptr_t get_invoke_impl_1() const { return ___invoke_impl_1; }
	inline intptr_t* get_address_of_invoke_impl_1() { return &___invoke_impl_1; }
	inline void set_invoke_impl_1(intptr_t value)
	{
		___invoke_impl_1 = value;
	}

	inline static int32_t get_offset_of_m_target_2() { return static_cast<int32_t>(offsetof(Delegate_t, ___m_target_2)); }
	inline RuntimeObject * get_m_target_2() const { return ___m_target_2; }
	inline RuntimeObject ** get_address_of_m_target_2() { return &___m_target_2; }
	inline void set_m_target_2(RuntimeObject * value)
	{
		___m_target_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_target_2), (void*)value);
	}

	inline static int32_t get_offset_of_method_3() { return static_cast<int32_t>(offsetof(Delegate_t, ___method_3)); }
	inline intptr_t get_method_3() const { return ___method_3; }
	inline intptr_t* get_address_of_method_3() { return &___method_3; }
	inline void set_method_3(intptr_t value)
	{
		___method_3 = value;
	}

	inline static int32_t get_offset_of_delegate_trampoline_4() { return static_cast<int32_t>(offsetof(Delegate_t, ___delegate_trampoline_4)); }
	inline intptr_t get_delegate_trampoline_4() const { return ___delegate_trampoline_4; }
	inline intptr_t* get_address_of_delegate_trampoline_4() { return &___delegate_trampoline_4; }
	inline void set_delegate_trampoline_4(intptr_t value)
	{
		___delegate_trampoline_4 = value;
	}

	inline static int32_t get_offset_of_extra_arg_5() { return static_cast<int32_t>(offsetof(Delegate_t, ___extra_arg_5)); }
	inline intptr_t get_extra_arg_5() const { return ___extra_arg_5; }
	inline intptr_t* get_address_of_extra_arg_5() { return &___extra_arg_5; }
	inline void set_extra_arg_5(intptr_t value)
	{
		___extra_arg_5 = value;
	}

	inline static int32_t get_offset_of_method_code_6() { return static_cast<int32_t>(offsetof(Delegate_t, ___method_code_6)); }
	inline intptr_t get_method_code_6() const { return ___method_code_6; }
	inline intptr_t* get_address_of_method_code_6() { return &___method_code_6; }
	inline void set_method_code_6(intptr_t value)
	{
		___method_code_6 = value;
	}

	inline static int32_t get_offset_of_method_info_7() { return static_cast<int32_t>(offsetof(Delegate_t, ___method_info_7)); }
	inline MethodInfo_t * get_method_info_7() const { return ___method_info_7; }
	inline MethodInfo_t ** get_address_of_method_info_7() { return &___method_info_7; }
	inline void set_method_info_7(MethodInfo_t * value)
	{
		___method_info_7 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___method_info_7), (void*)value);
	}

	inline static int32_t get_offset_of_original_method_info_8() { return static_cast<int32_t>(offsetof(Delegate_t, ___original_method_info_8)); }
	inline MethodInfo_t * get_original_method_info_8() const { return ___original_method_info_8; }
	inline MethodInfo_t ** get_address_of_original_method_info_8() { return &___original_method_info_8; }
	inline void set_original_method_info_8(MethodInfo_t * value)
	{
		___original_method_info_8 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___original_method_info_8), (void*)value);
	}

	inline static int32_t get_offset_of_data_9() { return static_cast<int32_t>(offsetof(Delegate_t, ___data_9)); }
	inline DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 * get_data_9() const { return ___data_9; }
	inline DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 ** get_address_of_data_9() { return &___data_9; }
	inline void set_data_9(DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 * value)
	{
		___data_9 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___data_9), (void*)value);
	}

	inline static int32_t get_offset_of_method_is_virtual_10() { return static_cast<int32_t>(offsetof(Delegate_t, ___method_is_virtual_10)); }
	inline bool get_method_is_virtual_10() const { return ___method_is_virtual_10; }
	inline bool* get_address_of_method_is_virtual_10() { return &___method_is_virtual_10; }
	inline void set_method_is_virtual_10(bool value)
	{
		___method_is_virtual_10 = value;
	}
};

// Native definition for P/Invoke marshalling of System.Delegate
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr_0;
	intptr_t ___invoke_impl_1;
	Il2CppIUnknown* ___m_target_2;
	intptr_t ___method_3;
	intptr_t ___delegate_trampoline_4;
	intptr_t ___extra_arg_5;
	intptr_t ___method_code_6;
	MethodInfo_t * ___method_info_7;
	MethodInfo_t * ___original_method_info_8;
	DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 * ___data_9;
	int32_t ___method_is_virtual_10;
};
// Native definition for COM marshalling of System.Delegate
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr_0;
	intptr_t ___invoke_impl_1;
	Il2CppIUnknown* ___m_target_2;
	intptr_t ___method_3;
	intptr_t ___delegate_trampoline_4;
	intptr_t ___extra_arg_5;
	intptr_t ___method_code_6;
	MethodInfo_t * ___method_info_7;
	MethodInfo_t * ___original_method_info_8;
	DelegateData_t17DD30660E330C49381DAA99F934BE75CB11F288 * ___data_9;
	int32_t ___method_is_virtual_10;
};

// System.Exception
struct Exception_t  : public RuntimeObject
{
public:
	// System.String System.Exception::_className
	String_t* ____className_1;
	// System.String System.Exception::_message
	String_t* ____message_2;
	// System.Collections.IDictionary System.Exception::_data
	RuntimeObject* ____data_3;
	// System.Exception System.Exception::_innerException
	Exception_t * ____innerException_4;
	// System.String System.Exception::_helpURL
	String_t* ____helpURL_5;
	// System.Object System.Exception::_stackTrace
	RuntimeObject * ____stackTrace_6;
	// System.String System.Exception::_stackTraceString
	String_t* ____stackTraceString_7;
	// System.String System.Exception::_remoteStackTraceString
	String_t* ____remoteStackTraceString_8;
	// System.Int32 System.Exception::_remoteStackIndex
	int32_t ____remoteStackIndex_9;
	// System.Object System.Exception::_dynamicMethods
	RuntimeObject * ____dynamicMethods_10;
	// System.Int32 System.Exception::_HResult
	int32_t ____HResult_11;
	// System.String System.Exception::_source
	String_t* ____source_12;
	// System.Runtime.Serialization.SafeSerializationManager System.Exception::_safeSerializationManager
	SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F * ____safeSerializationManager_13;
	// System.Diagnostics.StackTrace[] System.Exception::captured_traces
	StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971* ___captured_traces_14;
	// System.IntPtr[] System.Exception::native_trace_ips
	IntPtrU5BU5D_t27FC72B0409D75AAF33EC42498E8094E95FEE9A6* ___native_trace_ips_15;

public:
	inline static int32_t get_offset_of__className_1() { return static_cast<int32_t>(offsetof(Exception_t, ____className_1)); }
	inline String_t* get__className_1() const { return ____className_1; }
	inline String_t** get_address_of__className_1() { return &____className_1; }
	inline void set__className_1(String_t* value)
	{
		____className_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____className_1), (void*)value);
	}

	inline static int32_t get_offset_of__message_2() { return static_cast<int32_t>(offsetof(Exception_t, ____message_2)); }
	inline String_t* get__message_2() const { return ____message_2; }
	inline String_t** get_address_of__message_2() { return &____message_2; }
	inline void set__message_2(String_t* value)
	{
		____message_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____message_2), (void*)value);
	}

	inline static int32_t get_offset_of__data_3() { return static_cast<int32_t>(offsetof(Exception_t, ____data_3)); }
	inline RuntimeObject* get__data_3() const { return ____data_3; }
	inline RuntimeObject** get_address_of__data_3() { return &____data_3; }
	inline void set__data_3(RuntimeObject* value)
	{
		____data_3 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____data_3), (void*)value);
	}

	inline static int32_t get_offset_of__innerException_4() { return static_cast<int32_t>(offsetof(Exception_t, ____innerException_4)); }
	inline Exception_t * get__innerException_4() const { return ____innerException_4; }
	inline Exception_t ** get_address_of__innerException_4() { return &____innerException_4; }
	inline void set__innerException_4(Exception_t * value)
	{
		____innerException_4 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____innerException_4), (void*)value);
	}

	inline static int32_t get_offset_of__helpURL_5() { return static_cast<int32_t>(offsetof(Exception_t, ____helpURL_5)); }
	inline String_t* get__helpURL_5() const { return ____helpURL_5; }
	inline String_t** get_address_of__helpURL_5() { return &____helpURL_5; }
	inline void set__helpURL_5(String_t* value)
	{
		____helpURL_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____helpURL_5), (void*)value);
	}

	inline static int32_t get_offset_of__stackTrace_6() { return static_cast<int32_t>(offsetof(Exception_t, ____stackTrace_6)); }
	inline RuntimeObject * get__stackTrace_6() const { return ____stackTrace_6; }
	inline RuntimeObject ** get_address_of__stackTrace_6() { return &____stackTrace_6; }
	inline void set__stackTrace_6(RuntimeObject * value)
	{
		____stackTrace_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____stackTrace_6), (void*)value);
	}

	inline static int32_t get_offset_of__stackTraceString_7() { return static_cast<int32_t>(offsetof(Exception_t, ____stackTraceString_7)); }
	inline String_t* get__stackTraceString_7() const { return ____stackTraceString_7; }
	inline String_t** get_address_of__stackTraceString_7() { return &____stackTraceString_7; }
	inline void set__stackTraceString_7(String_t* value)
	{
		____stackTraceString_7 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____stackTraceString_7), (void*)value);
	}

	inline static int32_t get_offset_of__remoteStackTraceString_8() { return static_cast<int32_t>(offsetof(Exception_t, ____remoteStackTraceString_8)); }
	inline String_t* get__remoteStackTraceString_8() const { return ____remoteStackTraceString_8; }
	inline String_t** get_address_of__remoteStackTraceString_8() { return &____remoteStackTraceString_8; }
	inline void set__remoteStackTraceString_8(String_t* value)
	{
		____remoteStackTraceString_8 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____remoteStackTraceString_8), (void*)value);
	}

	inline static int32_t get_offset_of__remoteStackIndex_9() { return static_cast<int32_t>(offsetof(Exception_t, ____remoteStackIndex_9)); }
	inline int32_t get__remoteStackIndex_9() const { return ____remoteStackIndex_9; }
	inline int32_t* get_address_of__remoteStackIndex_9() { return &____remoteStackIndex_9; }
	inline void set__remoteStackIndex_9(int32_t value)
	{
		____remoteStackIndex_9 = value;
	}

	inline static int32_t get_offset_of__dynamicMethods_10() { return static_cast<int32_t>(offsetof(Exception_t, ____dynamicMethods_10)); }
	inline RuntimeObject * get__dynamicMethods_10() const { return ____dynamicMethods_10; }
	inline RuntimeObject ** get_address_of__dynamicMethods_10() { return &____dynamicMethods_10; }
	inline void set__dynamicMethods_10(RuntimeObject * value)
	{
		____dynamicMethods_10 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____dynamicMethods_10), (void*)value);
	}

	inline static int32_t get_offset_of__HResult_11() { return static_cast<int32_t>(offsetof(Exception_t, ____HResult_11)); }
	inline int32_t get__HResult_11() const { return ____HResult_11; }
	inline int32_t* get_address_of__HResult_11() { return &____HResult_11; }
	inline void set__HResult_11(int32_t value)
	{
		____HResult_11 = value;
	}

	inline static int32_t get_offset_of__source_12() { return static_cast<int32_t>(offsetof(Exception_t, ____source_12)); }
	inline String_t* get__source_12() const { return ____source_12; }
	inline String_t** get_address_of__source_12() { return &____source_12; }
	inline void set__source_12(String_t* value)
	{
		____source_12 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____source_12), (void*)value);
	}

	inline static int32_t get_offset_of__safeSerializationManager_13() { return static_cast<int32_t>(offsetof(Exception_t, ____safeSerializationManager_13)); }
	inline SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F * get__safeSerializationManager_13() const { return ____safeSerializationManager_13; }
	inline SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F ** get_address_of__safeSerializationManager_13() { return &____safeSerializationManager_13; }
	inline void set__safeSerializationManager_13(SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F * value)
	{
		____safeSerializationManager_13 = value;
		Il2CppCodeGenWriteBarrier((void**)(&____safeSerializationManager_13), (void*)value);
	}

	inline static int32_t get_offset_of_captured_traces_14() { return static_cast<int32_t>(offsetof(Exception_t, ___captured_traces_14)); }
	inline StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971* get_captured_traces_14() const { return ___captured_traces_14; }
	inline StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971** get_address_of_captured_traces_14() { return &___captured_traces_14; }
	inline void set_captured_traces_14(StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971* value)
	{
		___captured_traces_14 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___captured_traces_14), (void*)value);
	}

	inline static int32_t get_offset_of_native_trace_ips_15() { return static_cast<int32_t>(offsetof(Exception_t, ___native_trace_ips_15)); }
	inline IntPtrU5BU5D_t27FC72B0409D75AAF33EC42498E8094E95FEE9A6* get_native_trace_ips_15() const { return ___native_trace_ips_15; }
	inline IntPtrU5BU5D_t27FC72B0409D75AAF33EC42498E8094E95FEE9A6** get_address_of_native_trace_ips_15() { return &___native_trace_ips_15; }
	inline void set_native_trace_ips_15(IntPtrU5BU5D_t27FC72B0409D75AAF33EC42498E8094E95FEE9A6* value)
	{
		___native_trace_ips_15 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___native_trace_ips_15), (void*)value);
	}
};

// Native definition for P/Invoke marshalling of System.Exception
struct Exception_t_marshaled_pinvoke
{
	char* ____className_1;
	char* ____message_2;
	RuntimeObject* ____data_3;
	Exception_t_marshaled_pinvoke* ____innerException_4;
	char* ____helpURL_5;
	Il2CppIUnknown* ____stackTrace_6;
	char* ____stackTraceString_7;
	char* ____remoteStackTraceString_8;
	int32_t ____remoteStackIndex_9;
	Il2CppIUnknown* ____dynamicMethods_10;
	int32_t ____HResult_11;
	char* ____source_12;
	SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F * ____safeSerializationManager_13;
	StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971* ___captured_traces_14;
	Il2CppSafeArray/*NONE*/* ___native_trace_ips_15;
};
// Native definition for COM marshalling of System.Exception
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className_1;
	Il2CppChar* ____message_2;
	RuntimeObject* ____data_3;
	Exception_t_marshaled_com* ____innerException_4;
	Il2CppChar* ____helpURL_5;
	Il2CppIUnknown* ____stackTrace_6;
	Il2CppChar* ____stackTraceString_7;
	Il2CppChar* ____remoteStackTraceString_8;
	int32_t ____remoteStackIndex_9;
	Il2CppIUnknown* ____dynamicMethods_10;
	int32_t ____HResult_11;
	Il2CppChar* ____source_12;
	SafeSerializationManager_tDE44F029589A028F8A3053C5C06153FAB4AAE29F * ____safeSerializationManager_13;
	StackTraceU5BU5D_t4AD999C288CB6D1F38A299D12B1598D606588971* ___captured_traces_14;
	Il2CppSafeArray/*NONE*/* ___native_trace_ips_15;
};

// UnityEngine.Object
struct Object_tF2F3778131EFF286AF62B7B013A170F95A91571A  : public RuntimeObject
{
public:
	// System.IntPtr UnityEngine.Object::m_CachedPtr
	intptr_t ___m_CachedPtr_0;

public:
	inline static int32_t get_offset_of_m_CachedPtr_0() { return static_cast<int32_t>(offsetof(Object_tF2F3778131EFF286AF62B7B013A170F95A91571A, ___m_CachedPtr_0)); }
	inline intptr_t get_m_CachedPtr_0() const { return ___m_CachedPtr_0; }
	inline intptr_t* get_address_of_m_CachedPtr_0() { return &___m_CachedPtr_0; }
	inline void set_m_CachedPtr_0(intptr_t value)
	{
		___m_CachedPtr_0 = value;
	}
};

// Native definition for P/Invoke marshalling of UnityEngine.Object
struct Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr_0;
};
// Native definition for COM marshalling of UnityEngine.Object
struct Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_marshaled_com
{
	intptr_t ___m_CachedPtr_0;
};

// System.RuntimeTypeHandle
struct RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9 
{
public:
	// System.IntPtr System.RuntimeTypeHandle::value
	intptr_t ___value_0;

public:
	inline static int32_t get_offset_of_value_0() { return static_cast<int32_t>(offsetof(RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9, ___value_0)); }
	inline intptr_t get_value_0() const { return ___value_0; }
	inline intptr_t* get_address_of_value_0() { return &___value_0; }
	inline void set_value_0(intptr_t value)
	{
		___value_0 = value;
	}
};


// UnityEngine.ThreadPriority
struct ThreadPriority_t86C240DDF46F222398ED5E74385AA5A162C1F6CF 
{
public:
	// System.Int32 UnityEngine.ThreadPriority::value__
	int32_t ___value___2;

public:
	inline static int32_t get_offset_of_value___2() { return static_cast<int32_t>(offsetof(ThreadPriority_t86C240DDF46F222398ED5E74385AA5A162C1F6CF, ___value___2)); }
	inline int32_t get_value___2() const { return ___value___2; }
	inline int32_t* get_address_of_value___2() { return &___value___2; }
	inline void set_value___2(int32_t value)
	{
		___value___2 = value;
	}
};


// UnityEngine.AssetBundle
struct AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4  : public Object_tF2F3778131EFF286AF62B7B013A170F95A91571A
{
public:

public:
};


// UnityEngine.AssetBundleCreateRequest
struct AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A  : public AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86
{
public:

public:
};

// Native definition for P/Invoke marshalling of UnityEngine.AssetBundleCreateRequest
struct AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A_marshaled_pinvoke : public AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86_marshaled_pinvoke
{
};
// Native definition for COM marshalling of UnityEngine.AssetBundleCreateRequest
struct AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A_marshaled_com : public AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86_marshaled_com
{
};

// UnityEngine.AssetBundleManifest
struct AssetBundleManifest_tC4C999A85A1FC2B60215566D0462EE645674160A  : public Object_tF2F3778131EFF286AF62B7B013A170F95A91571A
{
public:

public:
};


// UnityEngine.AssetBundleRecompressOperation
struct AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31  : public AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86
{
public:

public:
};

// Native definition for P/Invoke marshalling of UnityEngine.AssetBundleRecompressOperation
struct AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31_marshaled_pinvoke : public AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86_marshaled_pinvoke
{
};
// Native definition for COM marshalling of UnityEngine.AssetBundleRecompressOperation
struct AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31_marshaled_com : public AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86_marshaled_com
{
};

// UnityEngine.BuildCompression
struct BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F 
{
public:
	// UnityEngine.CompressionType UnityEngine.BuildCompression::_compression
	int32_t ____compression_5;
	// UnityEngine.CompressionLevel UnityEngine.BuildCompression::_level
	int32_t ____level_6;
	// System.UInt32 UnityEngine.BuildCompression::_blockSize
	uint32_t ____blockSize_7;

public:
	inline static int32_t get_offset_of__compression_5() { return static_cast<int32_t>(offsetof(BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F, ____compression_5)); }
	inline int32_t get__compression_5() const { return ____compression_5; }
	inline int32_t* get_address_of__compression_5() { return &____compression_5; }
	inline void set__compression_5(int32_t value)
	{
		____compression_5 = value;
	}

	inline static int32_t get_offset_of__level_6() { return static_cast<int32_t>(offsetof(BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F, ____level_6)); }
	inline int32_t get__level_6() const { return ____level_6; }
	inline int32_t* get_address_of__level_6() { return &____level_6; }
	inline void set__level_6(int32_t value)
	{
		____level_6 = value;
	}

	inline static int32_t get_offset_of__blockSize_7() { return static_cast<int32_t>(offsetof(BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F, ____blockSize_7)); }
	inline uint32_t get__blockSize_7() const { return ____blockSize_7; }
	inline uint32_t* get_address_of__blockSize_7() { return &____blockSize_7; }
	inline void set__blockSize_7(uint32_t value)
	{
		____blockSize_7 = value;
	}
};


// System.MulticastDelegate
struct MulticastDelegate_t  : public Delegate_t
{
public:
	// System.Delegate[] System.MulticastDelegate::delegates
	DelegateU5BU5D_t677D8FE08A5F99E8EE49150B73966CD6E9BF7DB8* ___delegates_11;

public:
	inline static int32_t get_offset_of_delegates_11() { return static_cast<int32_t>(offsetof(MulticastDelegate_t, ___delegates_11)); }
	inline DelegateU5BU5D_t677D8FE08A5F99E8EE49150B73966CD6E9BF7DB8* get_delegates_11() const { return ___delegates_11; }
	inline DelegateU5BU5D_t677D8FE08A5F99E8EE49150B73966CD6E9BF7DB8** get_address_of_delegates_11() { return &___delegates_11; }
	inline void set_delegates_11(DelegateU5BU5D_t677D8FE08A5F99E8EE49150B73966CD6E9BF7DB8* value)
	{
		___delegates_11 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___delegates_11), (void*)value);
	}
};

// Native definition for P/Invoke marshalling of System.MulticastDelegate
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates_11;
};
// Native definition for COM marshalling of System.MulticastDelegate
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates_11;
};

// UnityEngine.ResourceRequest
struct ResourceRequest_tD2D09E98C844087E6AB0F04532B7AA139558CBAD  : public AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86
{
public:
	// System.String UnityEngine.ResourceRequest::m_Path
	String_t* ___m_Path_2;
	// System.Type UnityEngine.ResourceRequest::m_Type
	Type_t * ___m_Type_3;

public:
	inline static int32_t get_offset_of_m_Path_2() { return static_cast<int32_t>(offsetof(ResourceRequest_tD2D09E98C844087E6AB0F04532B7AA139558CBAD, ___m_Path_2)); }
	inline String_t* get_m_Path_2() const { return ___m_Path_2; }
	inline String_t** get_address_of_m_Path_2() { return &___m_Path_2; }
	inline void set_m_Path_2(String_t* value)
	{
		___m_Path_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_Path_2), (void*)value);
	}

	inline static int32_t get_offset_of_m_Type_3() { return static_cast<int32_t>(offsetof(ResourceRequest_tD2D09E98C844087E6AB0F04532B7AA139558CBAD, ___m_Type_3)); }
	inline Type_t * get_m_Type_3() const { return ___m_Type_3; }
	inline Type_t ** get_address_of_m_Type_3() { return &___m_Type_3; }
	inline void set_m_Type_3(Type_t * value)
	{
		___m_Type_3 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_Type_3), (void*)value);
	}
};

// Native definition for P/Invoke marshalling of UnityEngine.ResourceRequest
struct ResourceRequest_tD2D09E98C844087E6AB0F04532B7AA139558CBAD_marshaled_pinvoke : public AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86_marshaled_pinvoke
{
	char* ___m_Path_2;
	Type_t * ___m_Type_3;
};
// Native definition for COM marshalling of UnityEngine.ResourceRequest
struct ResourceRequest_tD2D09E98C844087E6AB0F04532B7AA139558CBAD_marshaled_com : public AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86_marshaled_com
{
	Il2CppChar* ___m_Path_2;
	Type_t * ___m_Type_3;
};

// System.SystemException
struct SystemException_tC551B4D6EE3772B5F32C71EE8C719F4B43ECCC62  : public Exception_t
{
public:

public:
};


// System.Type
struct Type_t  : public MemberInfo_t
{
public:
	// System.RuntimeTypeHandle System.Type::_impl
	RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9  ____impl_9;

public:
	inline static int32_t get_offset_of__impl_9() { return static_cast<int32_t>(offsetof(Type_t, ____impl_9)); }
	inline RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9  get__impl_9() const { return ____impl_9; }
	inline RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9 * get_address_of__impl_9() { return &____impl_9; }
	inline void set__impl_9(RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9  value)
	{
		____impl_9 = value;
	}
};


// System.Action`1<UnityEngine.AsyncOperation>
struct Action_1_tC1348BEB2C677FD60E4B65764CA3A1CAFF6DFB31  : public MulticastDelegate_t
{
public:

public:
};


// System.ArgumentException
struct ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00  : public SystemException_tC551B4D6EE3772B5F32C71EE8C719F4B43ECCC62
{
public:
	// System.String System.ArgumentException::m_paramName
	String_t* ___m_paramName_17;

public:
	inline static int32_t get_offset_of_m_paramName_17() { return static_cast<int32_t>(offsetof(ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00, ___m_paramName_17)); }
	inline String_t* get_m_paramName_17() const { return ___m_paramName_17; }
	inline String_t** get_address_of_m_paramName_17() { return &___m_paramName_17; }
	inline void set_m_paramName_17(String_t* value)
	{
		___m_paramName_17 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___m_paramName_17), (void*)value);
	}
};


// UnityEngine.AssetBundleRequest
struct AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A  : public ResourceRequest_tD2D09E98C844087E6AB0F04532B7AA139558CBAD
{
public:

public:
};

// Native definition for P/Invoke marshalling of UnityEngine.AssetBundleRequest
struct AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A_marshaled_pinvoke : public ResourceRequest_tD2D09E98C844087E6AB0F04532B7AA139558CBAD_marshaled_pinvoke
{
};
// Native definition for COM marshalling of UnityEngine.AssetBundleRequest
struct AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A_marshaled_com : public ResourceRequest_tD2D09E98C844087E6AB0F04532B7AA139558CBAD_marshaled_com
{
};

// System.NullReferenceException
struct NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724  : public SystemException_tC551B4D6EE3772B5F32C71EE8C719F4B43ECCC62
{
public:

public:
};


// System.ArgumentNullException
struct ArgumentNullException_tFB5C4621957BC53A7D1B4FDD5C38B4D6E15DB8FB  : public ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00
{
public:

public:
};


// <Module>


// <Module>


// System.Object


// System.Object

struct Il2CppArrayBounds;

// System.Array

struct Il2CppArrayBounds;

// System.Array


// UnityEngine.AssetBundleLoadingCache


// UnityEngine.AssetBundleLoadingCache


// System.MarshalByRefObject


// System.MarshalByRefObject


// System.Reflection.MemberInfo


// System.Reflection.MemberInfo


// System.String

struct String_t_StaticFields
{
public:
	// System.String System.String::Empty
	String_t* ___Empty_5;

public:
	inline static int32_t get_offset_of_Empty_5() { return static_cast<int32_t>(offsetof(String_t_StaticFields, ___Empty_5)); }
	inline String_t* get_Empty_5() const { return ___Empty_5; }
	inline String_t** get_address_of_Empty_5() { return &___Empty_5; }
	inline void set_Empty_5(String_t* value)
	{
		___Empty_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Empty_5), (void*)value);
	}
};


// System.String


// System.ValueType


// System.ValueType


// UnityEngine.YieldInstruction


// UnityEngine.YieldInstruction


// System.Boolean

struct Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields
{
public:
	// System.String System.Boolean::TrueString
	String_t* ___TrueString_5;
	// System.String System.Boolean::FalseString
	String_t* ___FalseString_6;

public:
	inline static int32_t get_offset_of_TrueString_5() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields, ___TrueString_5)); }
	inline String_t* get_TrueString_5() const { return ___TrueString_5; }
	inline String_t** get_address_of_TrueString_5() { return &___TrueString_5; }
	inline void set_TrueString_5(String_t* value)
	{
		___TrueString_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___TrueString_5), (void*)value);
	}

	inline static int32_t get_offset_of_FalseString_6() { return static_cast<int32_t>(offsetof(Boolean_t07D1E3F34E4813023D64F584DFF7B34C9D922F37_StaticFields, ___FalseString_6)); }
	inline String_t* get_FalseString_6() const { return ___FalseString_6; }
	inline String_t** get_address_of_FalseString_6() { return &___FalseString_6; }
	inline void set_FalseString_6(String_t* value)
	{
		___FalseString_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___FalseString_6), (void*)value);
	}
};


// System.Boolean


// System.Byte


// System.Byte


// System.Enum

struct Enum_t23B90B40F60E677A8025267341651C94AE079CDA_StaticFields
{
public:
	// System.Char[] System.Enum::enumSeperatorCharArray
	CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* ___enumSeperatorCharArray_0;

public:
	inline static int32_t get_offset_of_enumSeperatorCharArray_0() { return static_cast<int32_t>(offsetof(Enum_t23B90B40F60E677A8025267341651C94AE079CDA_StaticFields, ___enumSeperatorCharArray_0)); }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* get_enumSeperatorCharArray_0() const { return ___enumSeperatorCharArray_0; }
	inline CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34** get_address_of_enumSeperatorCharArray_0() { return &___enumSeperatorCharArray_0; }
	inline void set_enumSeperatorCharArray_0(CharU5BU5D_t7B7FC5BC8091AA3B9CB0B29CDD80B5EE9254AA34* value)
	{
		___enumSeperatorCharArray_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___enumSeperatorCharArray_0), (void*)value);
	}
};


// System.Enum


// UnityEngine.Hash128


// UnityEngine.Hash128


// System.Int32


// System.Int32


// System.IntPtr

struct IntPtr_t_StaticFields
{
public:
	// System.IntPtr System.IntPtr::Zero
	intptr_t ___Zero_1;

public:
	inline static int32_t get_offset_of_Zero_1() { return static_cast<int32_t>(offsetof(IntPtr_t_StaticFields, ___Zero_1)); }
	inline intptr_t get_Zero_1() const { return ___Zero_1; }
	inline intptr_t* get_address_of_Zero_1() { return &___Zero_1; }
	inline void set_Zero_1(intptr_t value)
	{
		___Zero_1 = value;
	}
};


// System.IntPtr


// System.IO.Stream

struct Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB_StaticFields
{
public:
	// System.IO.Stream System.IO.Stream::Null
	Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * ___Null_1;

public:
	inline static int32_t get_offset_of_Null_1() { return static_cast<int32_t>(offsetof(Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB_StaticFields, ___Null_1)); }
	inline Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * get_Null_1() const { return ___Null_1; }
	inline Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB ** get_address_of_Null_1() { return &___Null_1; }
	inline void set_Null_1(Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * value)
	{
		___Null_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Null_1), (void*)value);
	}
};


// System.IO.Stream


// System.UInt32


// System.UInt32


// System.UInt64


// System.UInt64


// System.Void


// System.Void


// UnityEngine.AsyncOperation


// UnityEngine.AsyncOperation


// System.Reflection.BindingFlags


// System.Reflection.BindingFlags


// UnityEngine.CompressionLevel


// UnityEngine.CompressionLevel


// UnityEngine.CompressionType


// UnityEngine.CompressionType


// System.Delegate


// System.Delegate


// System.Exception

struct Exception_t_StaticFields
{
public:
	// System.Object System.Exception::s_EDILock
	RuntimeObject * ___s_EDILock_0;

public:
	inline static int32_t get_offset_of_s_EDILock_0() { return static_cast<int32_t>(offsetof(Exception_t_StaticFields, ___s_EDILock_0)); }
	inline RuntimeObject * get_s_EDILock_0() const { return ___s_EDILock_0; }
	inline RuntimeObject ** get_address_of_s_EDILock_0() { return &___s_EDILock_0; }
	inline void set_s_EDILock_0(RuntimeObject * value)
	{
		___s_EDILock_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___s_EDILock_0), (void*)value);
	}
};


// System.Exception


// UnityEngine.Object

struct Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_StaticFields
{
public:
	// System.Int32 UnityEngine.Object::OffsetOfInstanceIDInCPlusPlusObject
	int32_t ___OffsetOfInstanceIDInCPlusPlusObject_1;

public:
	inline static int32_t get_offset_of_OffsetOfInstanceIDInCPlusPlusObject_1() { return static_cast<int32_t>(offsetof(Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_StaticFields, ___OffsetOfInstanceIDInCPlusPlusObject_1)); }
	inline int32_t get_OffsetOfInstanceIDInCPlusPlusObject_1() const { return ___OffsetOfInstanceIDInCPlusPlusObject_1; }
	inline int32_t* get_address_of_OffsetOfInstanceIDInCPlusPlusObject_1() { return &___OffsetOfInstanceIDInCPlusPlusObject_1; }
	inline void set_OffsetOfInstanceIDInCPlusPlusObject_1(int32_t value)
	{
		___OffsetOfInstanceIDInCPlusPlusObject_1 = value;
	}
};


// UnityEngine.Object


// System.RuntimeTypeHandle


// System.RuntimeTypeHandle


// UnityEngine.ThreadPriority


// UnityEngine.ThreadPriority


// UnityEngine.AssetBundle


// UnityEngine.AssetBundle


// UnityEngine.AssetBundleCreateRequest


// UnityEngine.AssetBundleCreateRequest


// UnityEngine.AssetBundleManifest


// UnityEngine.AssetBundleManifest


// UnityEngine.AssetBundleRecompressOperation


// UnityEngine.AssetBundleRecompressOperation


// UnityEngine.BuildCompression

struct BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F_StaticFields
{
public:
	// UnityEngine.BuildCompression UnityEngine.BuildCompression::Uncompressed
	BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  ___Uncompressed_0;
	// UnityEngine.BuildCompression UnityEngine.BuildCompression::LZ4
	BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  ___LZ4_1;
	// UnityEngine.BuildCompression UnityEngine.BuildCompression::LZMA
	BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  ___LZMA_2;
	// UnityEngine.BuildCompression UnityEngine.BuildCompression::UncompressedRuntime
	BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  ___UncompressedRuntime_3;
	// UnityEngine.BuildCompression UnityEngine.BuildCompression::LZ4Runtime
	BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  ___LZ4Runtime_4;

public:
	inline static int32_t get_offset_of_Uncompressed_0() { return static_cast<int32_t>(offsetof(BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F_StaticFields, ___Uncompressed_0)); }
	inline BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  get_Uncompressed_0() const { return ___Uncompressed_0; }
	inline BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F * get_address_of_Uncompressed_0() { return &___Uncompressed_0; }
	inline void set_Uncompressed_0(BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  value)
	{
		___Uncompressed_0 = value;
	}

	inline static int32_t get_offset_of_LZ4_1() { return static_cast<int32_t>(offsetof(BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F_StaticFields, ___LZ4_1)); }
	inline BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  get_LZ4_1() const { return ___LZ4_1; }
	inline BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F * get_address_of_LZ4_1() { return &___LZ4_1; }
	inline void set_LZ4_1(BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  value)
	{
		___LZ4_1 = value;
	}

	inline static int32_t get_offset_of_LZMA_2() { return static_cast<int32_t>(offsetof(BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F_StaticFields, ___LZMA_2)); }
	inline BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  get_LZMA_2() const { return ___LZMA_2; }
	inline BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F * get_address_of_LZMA_2() { return &___LZMA_2; }
	inline void set_LZMA_2(BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  value)
	{
		___LZMA_2 = value;
	}

	inline static int32_t get_offset_of_UncompressedRuntime_3() { return static_cast<int32_t>(offsetof(BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F_StaticFields, ___UncompressedRuntime_3)); }
	inline BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  get_UncompressedRuntime_3() const { return ___UncompressedRuntime_3; }
	inline BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F * get_address_of_UncompressedRuntime_3() { return &___UncompressedRuntime_3; }
	inline void set_UncompressedRuntime_3(BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  value)
	{
		___UncompressedRuntime_3 = value;
	}

	inline static int32_t get_offset_of_LZ4Runtime_4() { return static_cast<int32_t>(offsetof(BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F_StaticFields, ___LZ4Runtime_4)); }
	inline BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  get_LZ4Runtime_4() const { return ___LZ4Runtime_4; }
	inline BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F * get_address_of_LZ4Runtime_4() { return &___LZ4Runtime_4; }
	inline void set_LZ4Runtime_4(BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  value)
	{
		___LZ4Runtime_4 = value;
	}
};


// UnityEngine.BuildCompression


// System.MulticastDelegate


// System.MulticastDelegate


// UnityEngine.ResourceRequest


// UnityEngine.ResourceRequest


// System.SystemException


// System.SystemException


// System.Type

struct Type_t_StaticFields
{
public:
	// System.Reflection.MemberFilter System.Type::FilterAttribute
	MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * ___FilterAttribute_0;
	// System.Reflection.MemberFilter System.Type::FilterName
	MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * ___FilterName_1;
	// System.Reflection.MemberFilter System.Type::FilterNameIgnoreCase
	MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * ___FilterNameIgnoreCase_2;
	// System.Object System.Type::Missing
	RuntimeObject * ___Missing_3;
	// System.Char System.Type::Delimiter
	Il2CppChar ___Delimiter_4;
	// System.Type[] System.Type::EmptyTypes
	TypeU5BU5D_t85B10489E46F06CEC7C4B1CCBD0E01FAB6649755* ___EmptyTypes_5;
	// System.Reflection.Binder System.Type::defaultBinder
	Binder_t2BEE27FD84737D1E79BC47FD67F6D3DD2F2DDA30 * ___defaultBinder_6;

public:
	inline static int32_t get_offset_of_FilterAttribute_0() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___FilterAttribute_0)); }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * get_FilterAttribute_0() const { return ___FilterAttribute_0; }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 ** get_address_of_FilterAttribute_0() { return &___FilterAttribute_0; }
	inline void set_FilterAttribute_0(MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * value)
	{
		___FilterAttribute_0 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___FilterAttribute_0), (void*)value);
	}

	inline static int32_t get_offset_of_FilterName_1() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___FilterName_1)); }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * get_FilterName_1() const { return ___FilterName_1; }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 ** get_address_of_FilterName_1() { return &___FilterName_1; }
	inline void set_FilterName_1(MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * value)
	{
		___FilterName_1 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___FilterName_1), (void*)value);
	}

	inline static int32_t get_offset_of_FilterNameIgnoreCase_2() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___FilterNameIgnoreCase_2)); }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * get_FilterNameIgnoreCase_2() const { return ___FilterNameIgnoreCase_2; }
	inline MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 ** get_address_of_FilterNameIgnoreCase_2() { return &___FilterNameIgnoreCase_2; }
	inline void set_FilterNameIgnoreCase_2(MemberFilter_t48D0AA10105D186AF42428FA532D4B4332CF8B81 * value)
	{
		___FilterNameIgnoreCase_2 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___FilterNameIgnoreCase_2), (void*)value);
	}

	inline static int32_t get_offset_of_Missing_3() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___Missing_3)); }
	inline RuntimeObject * get_Missing_3() const { return ___Missing_3; }
	inline RuntimeObject ** get_address_of_Missing_3() { return &___Missing_3; }
	inline void set_Missing_3(RuntimeObject * value)
	{
		___Missing_3 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___Missing_3), (void*)value);
	}

	inline static int32_t get_offset_of_Delimiter_4() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___Delimiter_4)); }
	inline Il2CppChar get_Delimiter_4() const { return ___Delimiter_4; }
	inline Il2CppChar* get_address_of_Delimiter_4() { return &___Delimiter_4; }
	inline void set_Delimiter_4(Il2CppChar value)
	{
		___Delimiter_4 = value;
	}

	inline static int32_t get_offset_of_EmptyTypes_5() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___EmptyTypes_5)); }
	inline TypeU5BU5D_t85B10489E46F06CEC7C4B1CCBD0E01FAB6649755* get_EmptyTypes_5() const { return ___EmptyTypes_5; }
	inline TypeU5BU5D_t85B10489E46F06CEC7C4B1CCBD0E01FAB6649755** get_address_of_EmptyTypes_5() { return &___EmptyTypes_5; }
	inline void set_EmptyTypes_5(TypeU5BU5D_t85B10489E46F06CEC7C4B1CCBD0E01FAB6649755* value)
	{
		___EmptyTypes_5 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___EmptyTypes_5), (void*)value);
	}

	inline static int32_t get_offset_of_defaultBinder_6() { return static_cast<int32_t>(offsetof(Type_t_StaticFields, ___defaultBinder_6)); }
	inline Binder_t2BEE27FD84737D1E79BC47FD67F6D3DD2F2DDA30 * get_defaultBinder_6() const { return ___defaultBinder_6; }
	inline Binder_t2BEE27FD84737D1E79BC47FD67F6D3DD2F2DDA30 ** get_address_of_defaultBinder_6() { return &___defaultBinder_6; }
	inline void set_defaultBinder_6(Binder_t2BEE27FD84737D1E79BC47FD67F6D3DD2F2DDA30 * value)
	{
		___defaultBinder_6 = value;
		Il2CppCodeGenWriteBarrier((void**)(&___defaultBinder_6), (void*)value);
	}
};


// System.Type


// System.Action`1<UnityEngine.AsyncOperation>


// System.Action`1<UnityEngine.AsyncOperation>


// System.ArgumentException


// System.ArgumentException


// UnityEngine.AssetBundleRequest


// UnityEngine.AssetBundleRequest


// System.NullReferenceException


// System.NullReferenceException


// System.ArgumentNullException


// System.ArgumentNullException

#ifdef __clang__
#pragma clang diagnostic pop
#endif
// UnityEngine.AssetBundle[]
struct AssetBundleU5BU5D_t049219716584A9B3A1A6136429402193C7EB3A4D  : public RuntimeArray
{
public:
	ALIGN_FIELD (8) AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * m_Items[1];

public:
	inline AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 ** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 ** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
// System.Byte[]
struct ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726  : public RuntimeArray
{
public:
	ALIGN_FIELD (8) uint8_t m_Items[1];

public:
	inline uint8_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline uint8_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, uint8_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline uint8_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline uint8_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, uint8_t value)
	{
		m_Items[index] = value;
	}
};
// UnityEngine.Object[]
struct ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873  : public RuntimeArray
{
public:
	ALIGN_FIELD (8) Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * m_Items[1];

public:
	inline Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Object_tF2F3778131EFF286AF62B7B013A170F95A91571A ** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Object_tF2F3778131EFF286AF62B7B013A170F95A91571A ** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
// System.String[]
struct StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A  : public RuntimeArray
{
public:
	ALIGN_FIELD (8) String_t* m_Items[1];

public:
	inline String_t* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline String_t** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, String_t* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline String_t* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline String_t** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, String_t* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};



// System.Void UnityEngine.Object::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_m4DCF5CDB32C2C69290894101A81F473865169279 (Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * __this, const RuntimeMethod* method);
// UnityEngine.Object UnityEngine.AssetBundle::returnMainAsset(UnityEngine.AssetBundle)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * AssetBundle_returnMainAsset_mEADFB842D3D38FDAFF6052F93E178302C0A41FD6 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * ___bundle0, const RuntimeMethod* method);
// UnityEngine.AssetBundle[] UnityEngine.AssetBundle::GetAllLoadedAssetBundles_Native()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleU5BU5D_t049219716584A9B3A1A6136429402193C7EB3A4D* AssetBundle_GetAllLoadedAssetBundles_Native_mA2641B193E24C35A242E8125B49AEDF333E4BDCF (const RuntimeMethod* method);
// UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromFileAsync_Internal(System.String,System.UInt32,System.UInt64)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * AssetBundle_LoadFromFileAsync_Internal_m7DF7319EA7A245F43B9AC65483A4112CBD169F29 (String_t* ___path0, uint32_t ___crc1, uint64_t ___offset2, const RuntimeMethod* method);
// UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromFile_Internal(System.String,System.UInt32,System.UInt64)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * AssetBundle_LoadFromFile_Internal_mC5A549D462272D3E202D62CF295BDAF44F4E4B7C (String_t* ___path0, uint32_t ___crc1, uint64_t ___offset2, const RuntimeMethod* method);
// UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromMemoryAsync_Internal(System.Byte[],System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * AssetBundle_LoadFromMemoryAsync_Internal_m3722CC697D35ADA206E6574527A4F6681882D5D4 (ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726* ___binary0, uint32_t ___crc1, const RuntimeMethod* method);
// UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromMemory_Internal(System.Byte[],System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * AssetBundle_LoadFromMemory_Internal_mBF6BF6BFDE5DA4A76735766C969591424BB9649A (ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726* ___binary0, uint32_t ___crc1, const RuntimeMethod* method);
// System.Void System.ArgumentNullException::.ctor(System.String,System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentNullException__ctor_mAD2F05A24C92A657CBCA8C43A9A373C53739A283 (ArgumentNullException_tFB5C4621957BC53A7D1B4FDD5C38B4D6E15DB8FB * __this, String_t* ___paramName0, String_t* ___message1, const RuntimeMethod* method);
// System.Void System.ArgumentException::.ctor(System.String,System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentException__ctor_m71044C2110E357B71A1C30D2561C3F861AF1DC0D (ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00 * __this, String_t* ___message0, String_t* ___paramName1, const RuntimeMethod* method);
// System.Void UnityEngine.AssetBundle::ValidateLoadFromStream(System.IO.Stream)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssetBundle_ValidateLoadFromStream_m39F4CB6F1717C48017F09FFC1E537704BB47A5A1 (Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * ___stream0, const RuntimeMethod* method);
// UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromStreamAsyncInternal(System.IO.Stream,System.UInt32,System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * AssetBundle_LoadFromStreamAsyncInternal_m8B64B48FD5D92F632AD5D755C5B9F6B48E5D1F51 (Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * ___stream0, uint32_t ___crc1, uint32_t ___managedReadBufferSize2, const RuntimeMethod* method);
// UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromStreamInternal(System.IO.Stream,System.UInt32,System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * AssetBundle_LoadFromStreamInternal_m463BAC1F32549D1B4AE88A5185A135FE7676619A (Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * ___stream0, uint32_t ___crc1, uint32_t ___managedReadBufferSize2, const RuntimeMethod* method);
// System.Type System.Type::GetTypeFromHandle(System.RuntimeTypeHandle)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t * Type_GetTypeFromHandle_m180706985100778663E9C75D4FC001D367C0120F (RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9  ___handle0, const RuntimeMethod* method);
// UnityEngine.Object UnityEngine.AssetBundle::LoadAsset(System.String,System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * AssetBundle_LoadAsset_m9139320F8B6D3E43B7D29AA7A60030306AE0A2C6 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, String_t* ___name0, Type_t * ___type1, const RuntimeMethod* method);
// System.Void System.NullReferenceException::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NullReferenceException__ctor_mF1733893E10358B400E817297D686A48AB3FB7B3 (NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724 * __this, String_t* ___message0, const RuntimeMethod* method);
// System.Int32 System.String::get_Length()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t String_get_Length_m129FC0ADA02FECBED3C0B1A809AE84A5AEE1CF09_inline (String_t* __this, const RuntimeMethod* method);
// System.Void System.ArgumentException::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentException__ctor_m2D35EAD113C2ADC99EB17B940A2097A93FD23EFC (ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00 * __this, String_t* ___message0, const RuntimeMethod* method);
// UnityEngine.Object UnityEngine.AssetBundle::LoadAsset_Internal(System.String,System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * AssetBundle_LoadAsset_Internal_mFB165539087545C4B5763BA8B590D84318C6FE1B (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, String_t* ___name0, Type_t * ___type1, const RuntimeMethod* method);
// UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAssetAsync(System.String,System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * AssetBundle_LoadAssetAsync_m63C7C5654FA8D0824DC920A1B1530C37CCB3DF6E (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, String_t* ___name0, Type_t * ___type1, const RuntimeMethod* method);
// UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAssetAsync_Internal(System.String,System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * AssetBundle_LoadAssetAsync_Internal_m89D45EFB48D3C4CF7E481EF41F7ACF24500E7019 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, String_t* ___name0, Type_t * ___type1, const RuntimeMethod* method);
// UnityEngine.Object[] UnityEngine.AssetBundle::LoadAssetWithSubAssets(System.String,System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* AssetBundle_LoadAssetWithSubAssets_mAABE29A8F1719A85DC474932FF12A44B29CACB45 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, String_t* ___name0, Type_t * ___type1, const RuntimeMethod* method);
// UnityEngine.Object[] UnityEngine.AssetBundle::LoadAssetWithSubAssets_Internal(System.String,System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* AssetBundle_LoadAssetWithSubAssets_Internal_m70FFE92F072F934C4121DF328D6C8E1D19D93E51 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, String_t* ___name0, Type_t * ___type1, const RuntimeMethod* method);
// UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAssetWithSubAssetsAsync(System.String,System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * AssetBundle_LoadAssetWithSubAssetsAsync_m655947AD33D64C1F71F822AEFDE3D2A57C809AF5 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, String_t* ___name0, Type_t * ___type1, const RuntimeMethod* method);
// UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAssetWithSubAssetsAsync_Internal(System.String,System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * AssetBundle_LoadAssetWithSubAssetsAsync_Internal_mD671A7674C5E61F00AF0127C2D92F56DD34E3DCB (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, String_t* ___name0, Type_t * ___type1, const RuntimeMethod* method);
// UnityEngine.Object[] UnityEngine.AssetBundle::LoadAllAssets(System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* AssetBundle_LoadAllAssets_mAB1A820C148FF25D811D16DC5C8F5D39882D480A (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, Type_t * ___type0, const RuntimeMethod* method);
// UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAllAssetsAsync(System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * AssetBundle_LoadAllAssetsAsync_m213D935C1F40DB2098F00BD852B5799F00542137 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, Type_t * ___type0, const RuntimeMethod* method);
// UnityEngine.AssetBundleRecompressOperation UnityEngine.AssetBundle::RecompressAssetBundleAsync_Internal(System.String,System.String,UnityEngine.BuildCompression,System.UInt32,UnityEngine.ThreadPriority)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31 * AssetBundle_RecompressAssetBundleAsync_Internal_mEDC6D9885F3E663C7F2B2B57D3C52AD2435BE95A (String_t* ___inputPath0, String_t* ___outputPath1, BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  ___method2, uint32_t ___expectedCRC3, int32_t ___priority4, const RuntimeMethod* method);
// UnityEngine.AssetBundleRecompressOperation UnityEngine.AssetBundle::RecompressAssetBundleAsync_Internal_Injected(System.String,System.String,UnityEngine.BuildCompression&,System.UInt32,UnityEngine.ThreadPriority)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31 * AssetBundle_RecompressAssetBundleAsync_Internal_Injected_m3BFD8CEF04345BCD8F54C83993E5F587B9A16402 (String_t* ___inputPath0, String_t* ___outputPath1, BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F * ___method2, uint32_t ___expectedCRC3, int32_t ___priority4, const RuntimeMethod* method);
// System.UInt32 UnityEngine.AssetBundleLoadingCache::get_memoryBudgetKB()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t AssetBundleLoadingCache_get_memoryBudgetKB_m46A6F2C3142E554E68F9C2F804434938BBC5BBA0 (const RuntimeMethod* method);
// System.Void UnityEngine.AssetBundleLoadingCache::set_memoryBudgetKB(System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssetBundleLoadingCache_set_memoryBudgetKB_mDBA50BB9E1F49290FB7EACE70C2CD6087C3A1D45 (uint32_t ___value0, const RuntimeMethod* method);
// System.Void UnityEngine.AsyncOperation::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AsyncOperation__ctor_mFC0E13622A23CD19A631B9ABBA506683B71A2E4A (AsyncOperation_tB6913CEC83169F22E96067CE8C7117A221E51A86 * __this, const RuntimeMethod* method);
// System.UInt32 UnityEngine.AssetBundleLoadingCache::get_blockCount()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t AssetBundleLoadingCache_get_blockCount_m1C4097A665614846401024BDF05CE6AF86079962 (const RuntimeMethod* method);
// System.UInt32 UnityEngine.AssetBundleLoadingCache::get_blockSize()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t AssetBundleLoadingCache_get_blockSize_mE576EAB7984D4E402963633A1BB85A9464175F5B (const RuntimeMethod* method);
// System.UInt32 System.Math::Max(System.UInt32,System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t Math_Max_m234A4B8AAD69E87A26F691B80AFA892662F57159 (uint32_t ___val10, uint32_t ___val21, const RuntimeMethod* method);
// System.UInt32 UnityEngine.AssetBundleLoadingCache::get_maxBlocksPerFile()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t AssetBundleLoadingCache_get_maxBlocksPerFile_m1103DED6546DF3CABC67E610DA226C367FC6177F (const RuntimeMethod* method);
// System.Void UnityEngine.AssetBundleLoadingCache::set_blockCount(System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssetBundleLoadingCache_set_blockCount_mE518D94AB7139C672039F1B45A124F7B0CC87B98 (uint32_t ___value0, const RuntimeMethod* method);
// System.Void UnityEngine.AssetBundleLoadingCache::set_maxBlocksPerFile(System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssetBundleLoadingCache_set_maxBlocksPerFile_m9C8BCD2338E2A8E43FEB8541877C81052711EF79 (uint32_t ___value0, const RuntimeMethod* method);
// System.Void UnityEngine.AssetBundleManifest::GetAssetBundleHash_Injected(System.String,UnityEngine.Hash128&)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssetBundleManifest_GetAssetBundleHash_Injected_m99060916778ACCDF8476EB4B97F095FA93144D58 (AssetBundleManifest_tC4C999A85A1FC2B60215566D0462EE645674160A * __this, String_t* ___assetBundleName0, Hash128_t1858EA099934FD6F2B769E5661C17A276A2AFE7A * ___ret1, const RuntimeMethod* method);
// System.Void UnityEngine.ResourceRequest::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ResourceRequest__ctor_m3E6B88D53CD7B4275A16194A6D94264BFE79D039 (ResourceRequest_tD2D09E98C844087E6AB0F04532B7AA139558CBAD * __this, const RuntimeMethod* method);
// System.Void UnityEngine.BuildCompression::set_compression(UnityEngine.CompressionType)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BuildCompression_set_compression_mC4B164F707B54FB8D8CAFF6BBBE2AF251BCCF462 (BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F * __this, int32_t ___value0, const RuntimeMethod* method);
// System.Void UnityEngine.BuildCompression::set_level(UnityEngine.CompressionLevel)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BuildCompression_set_level_m02EF1CDC04A51C053146A6F2FE2889197F751F98 (BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F * __this, int32_t ___value0, const RuntimeMethod* method);
// System.Void UnityEngine.BuildCompression::set_blockSize(System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BuildCompression_set_blockSize_mF978E734073EC3863F93A845CD2AF4091B21F8AF (BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F * __this, uint32_t ___value0, const RuntimeMethod* method);
// System.Void UnityEngine.BuildCompression::.ctor(UnityEngine.CompressionType,UnityEngine.CompressionLevel,System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BuildCompression__ctor_m6A5C664DDDCD0559A817D0C48B7AF10729B00B27 (BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F * __this, int32_t ___in_compression0, int32_t ___in_level1, uint32_t ___in_blockSize2, const RuntimeMethod* method);
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void UnityEngine.AssetBundle::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssetBundle__ctor_mCE6DB7758AAD0EDDB044FC67C5BC7EC987BF3F71 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IL2CPP_RUNTIME_CLASS_INIT(Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_il2cpp_TypeInfo_var);
		Object__ctor_m4DCF5CDB32C2C69290894101A81F473865169279(__this, /*hidden argument*/NULL);
		return;
	}
}
// UnityEngine.Object UnityEngine.AssetBundle::get_mainAsset()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * AssetBundle_get_mainAsset_mDAF39948DFB48C4C8DA324C11A6E7C9FF2F7F58A (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, const RuntimeMethod* method)
{
	Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * V_0 = NULL;
	{
		Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * L_0;
		L_0 = AssetBundle_returnMainAsset_mEADFB842D3D38FDAFF6052F93E178302C0A41FD6(__this, /*hidden argument*/NULL);
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * L_1 = V_0;
		return L_1;
	}
}
// UnityEngine.Object UnityEngine.AssetBundle::returnMainAsset(UnityEngine.AssetBundle)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * AssetBundle_returnMainAsset_mEADFB842D3D38FDAFF6052F93E178302C0A41FD6 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * ___bundle0, const RuntimeMethod* method)
{
	typedef Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * (*AssetBundle_returnMainAsset_mEADFB842D3D38FDAFF6052F93E178302C0A41FD6_ftn) (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 *);
	static AssetBundle_returnMainAsset_mEADFB842D3D38FDAFF6052F93E178302C0A41FD6_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundle_returnMainAsset_mEADFB842D3D38FDAFF6052F93E178302C0A41FD6_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundle::returnMainAsset(UnityEngine.AssetBundle)");
	Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * icallRetVal = _il2cpp_icall_func(___bundle0);
	return icallRetVal;
}
// System.Void UnityEngine.AssetBundle::UnloadAllAssetBundles(System.Boolean)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssetBundle_UnloadAllAssetBundles_mDC192BDAF0AEA5F6B1E9EFEEE11AB28B105CF521 (bool ___unloadAllObjects0, const RuntimeMethod* method)
{
	typedef void (*AssetBundle_UnloadAllAssetBundles_mDC192BDAF0AEA5F6B1E9EFEEE11AB28B105CF521_ftn) (bool);
	static AssetBundle_UnloadAllAssetBundles_mDC192BDAF0AEA5F6B1E9EFEEE11AB28B105CF521_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundle_UnloadAllAssetBundles_mDC192BDAF0AEA5F6B1E9EFEEE11AB28B105CF521_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundle::UnloadAllAssetBundles(System.Boolean)");
	_il2cpp_icall_func(___unloadAllObjects0);
}
// UnityEngine.AssetBundle[] UnityEngine.AssetBundle::GetAllLoadedAssetBundles_Native()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleU5BU5D_t049219716584A9B3A1A6136429402193C7EB3A4D* AssetBundle_GetAllLoadedAssetBundles_Native_mA2641B193E24C35A242E8125B49AEDF333E4BDCF (const RuntimeMethod* method)
{
	typedef AssetBundleU5BU5D_t049219716584A9B3A1A6136429402193C7EB3A4D* (*AssetBundle_GetAllLoadedAssetBundles_Native_mA2641B193E24C35A242E8125B49AEDF333E4BDCF_ftn) ();
	static AssetBundle_GetAllLoadedAssetBundles_Native_mA2641B193E24C35A242E8125B49AEDF333E4BDCF_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundle_GetAllLoadedAssetBundles_Native_mA2641B193E24C35A242E8125B49AEDF333E4BDCF_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundle::GetAllLoadedAssetBundles_Native()");
	AssetBundleU5BU5D_t049219716584A9B3A1A6136429402193C7EB3A4D* icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
// System.Collections.Generic.IEnumerable`1<UnityEngine.AssetBundle> UnityEngine.AssetBundle::GetAllLoadedAssetBundles()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* AssetBundle_GetAllLoadedAssetBundles_mF192A985FCBCB5C8123A0E0BDF615BBBB7A45179 (const RuntimeMethod* method)
{
	RuntimeObject* V_0 = NULL;
	{
		AssetBundleU5BU5D_t049219716584A9B3A1A6136429402193C7EB3A4D* L_0;
		L_0 = AssetBundle_GetAllLoadedAssetBundles_Native_mA2641B193E24C35A242E8125B49AEDF333E4BDCF(/*hidden argument*/NULL);
		V_0 = (RuntimeObject*)L_0;
		goto IL_0009;
	}

IL_0009:
	{
		RuntimeObject* L_1 = V_0;
		return L_1;
	}
}
// UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromFileAsync_Internal(System.String,System.UInt32,System.UInt64)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * AssetBundle_LoadFromFileAsync_Internal_m7DF7319EA7A245F43B9AC65483A4112CBD169F29 (String_t* ___path0, uint32_t ___crc1, uint64_t ___offset2, const RuntimeMethod* method)
{
	typedef AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * (*AssetBundle_LoadFromFileAsync_Internal_m7DF7319EA7A245F43B9AC65483A4112CBD169F29_ftn) (String_t*, uint32_t, uint64_t);
	static AssetBundle_LoadFromFileAsync_Internal_m7DF7319EA7A245F43B9AC65483A4112CBD169F29_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundle_LoadFromFileAsync_Internal_m7DF7319EA7A245F43B9AC65483A4112CBD169F29_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundle::LoadFromFileAsync_Internal(System.String,System.UInt32,System.UInt64)");
	AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * icallRetVal = _il2cpp_icall_func(___path0, ___crc1, ___offset2);
	return icallRetVal;
}
// UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromFileAsync(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * AssetBundle_LoadFromFileAsync_mDF90143DECFE5149A8CF7D192E3D7ACBFEBE37D8 (String_t* ___path0, const RuntimeMethod* method)
{
	AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * V_0 = NULL;
	{
		String_t* L_0 = ___path0;
		AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * L_1;
		L_1 = AssetBundle_LoadFromFileAsync_Internal_m7DF7319EA7A245F43B9AC65483A4112CBD169F29(L_0, 0, ((int64_t)((int64_t)0)), /*hidden argument*/NULL);
		V_0 = L_1;
		goto IL_000d;
	}

IL_000d:
	{
		AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * L_2 = V_0;
		return L_2;
	}
}
// UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromFileAsync(System.String,System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * AssetBundle_LoadFromFileAsync_mCD630216EE5541040DEFF14F4FDC27DB348B42BC (String_t* ___path0, uint32_t ___crc1, const RuntimeMethod* method)
{
	AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * V_0 = NULL;
	{
		String_t* L_0 = ___path0;
		uint32_t L_1 = ___crc1;
		AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * L_2;
		L_2 = AssetBundle_LoadFromFileAsync_Internal_m7DF7319EA7A245F43B9AC65483A4112CBD169F29(L_0, L_1, ((int64_t)((int64_t)0)), /*hidden argument*/NULL);
		V_0 = L_2;
		goto IL_000d;
	}

IL_000d:
	{
		AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * L_3 = V_0;
		return L_3;
	}
}
// UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromFileAsync(System.String,System.UInt32,System.UInt64)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * AssetBundle_LoadFromFileAsync_mD919D804F62FBF3AF71410E77AC9940205375EA2 (String_t* ___path0, uint32_t ___crc1, uint64_t ___offset2, const RuntimeMethod* method)
{
	AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * V_0 = NULL;
	{
		String_t* L_0 = ___path0;
		uint32_t L_1 = ___crc1;
		uint64_t L_2 = ___offset2;
		AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * L_3;
		L_3 = AssetBundle_LoadFromFileAsync_Internal_m7DF7319EA7A245F43B9AC65483A4112CBD169F29(L_0, L_1, L_2, /*hidden argument*/NULL);
		V_0 = L_3;
		goto IL_000c;
	}

IL_000c:
	{
		AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * L_4 = V_0;
		return L_4;
	}
}
// UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromFile_Internal(System.String,System.UInt32,System.UInt64)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * AssetBundle_LoadFromFile_Internal_mC5A549D462272D3E202D62CF295BDAF44F4E4B7C (String_t* ___path0, uint32_t ___crc1, uint64_t ___offset2, const RuntimeMethod* method)
{
	typedef AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * (*AssetBundle_LoadFromFile_Internal_mC5A549D462272D3E202D62CF295BDAF44F4E4B7C_ftn) (String_t*, uint32_t, uint64_t);
	static AssetBundle_LoadFromFile_Internal_mC5A549D462272D3E202D62CF295BDAF44F4E4B7C_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundle_LoadFromFile_Internal_mC5A549D462272D3E202D62CF295BDAF44F4E4B7C_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundle::LoadFromFile_Internal(System.String,System.UInt32,System.UInt64)");
	AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * icallRetVal = _il2cpp_icall_func(___path0, ___crc1, ___offset2);
	return icallRetVal;
}
// UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromFile(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * AssetBundle_LoadFromFile_mE9B8D7BC74F2F67AE710881EFBBB541E8A8F6B3A (String_t* ___path0, const RuntimeMethod* method)
{
	AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * V_0 = NULL;
	{
		String_t* L_0 = ___path0;
		AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * L_1;
		L_1 = AssetBundle_LoadFromFile_Internal_mC5A549D462272D3E202D62CF295BDAF44F4E4B7C(L_0, 0, ((int64_t)((int64_t)0)), /*hidden argument*/NULL);
		V_0 = L_1;
		goto IL_000d;
	}

IL_000d:
	{
		AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * L_2 = V_0;
		return L_2;
	}
}
// UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromFile(System.String,System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * AssetBundle_LoadFromFile_m4CE03165FB7013C3B3664B082A5B4FD455009DE1 (String_t* ___path0, uint32_t ___crc1, const RuntimeMethod* method)
{
	AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * V_0 = NULL;
	{
		String_t* L_0 = ___path0;
		uint32_t L_1 = ___crc1;
		AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * L_2;
		L_2 = AssetBundle_LoadFromFile_Internal_mC5A549D462272D3E202D62CF295BDAF44F4E4B7C(L_0, L_1, ((int64_t)((int64_t)0)), /*hidden argument*/NULL);
		V_0 = L_2;
		goto IL_000d;
	}

IL_000d:
	{
		AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * L_3 = V_0;
		return L_3;
	}
}
// UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromFile(System.String,System.UInt32,System.UInt64)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * AssetBundle_LoadFromFile_m21CB874684AF8C9F77252362CA4E3E43440FF851 (String_t* ___path0, uint32_t ___crc1, uint64_t ___offset2, const RuntimeMethod* method)
{
	AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * V_0 = NULL;
	{
		String_t* L_0 = ___path0;
		uint32_t L_1 = ___crc1;
		uint64_t L_2 = ___offset2;
		AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * L_3;
		L_3 = AssetBundle_LoadFromFile_Internal_mC5A549D462272D3E202D62CF295BDAF44F4E4B7C(L_0, L_1, L_2, /*hidden argument*/NULL);
		V_0 = L_3;
		goto IL_000c;
	}

IL_000c:
	{
		AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * L_4 = V_0;
		return L_4;
	}
}
// UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromMemoryAsync_Internal(System.Byte[],System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * AssetBundle_LoadFromMemoryAsync_Internal_m3722CC697D35ADA206E6574527A4F6681882D5D4 (ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726* ___binary0, uint32_t ___crc1, const RuntimeMethod* method)
{
	typedef AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * (*AssetBundle_LoadFromMemoryAsync_Internal_m3722CC697D35ADA206E6574527A4F6681882D5D4_ftn) (ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726*, uint32_t);
	static AssetBundle_LoadFromMemoryAsync_Internal_m3722CC697D35ADA206E6574527A4F6681882D5D4_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundle_LoadFromMemoryAsync_Internal_m3722CC697D35ADA206E6574527A4F6681882D5D4_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundle::LoadFromMemoryAsync_Internal(System.Byte[],System.UInt32)");
	AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * icallRetVal = _il2cpp_icall_func(___binary0, ___crc1);
	return icallRetVal;
}
// UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromMemoryAsync(System.Byte[])
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * AssetBundle_LoadFromMemoryAsync_m62DAF5E226F11D690E8DF1FC6A32625D7A7A733D (ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726* ___binary0, const RuntimeMethod* method)
{
	AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * V_0 = NULL;
	{
		ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726* L_0 = ___binary0;
		AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * L_1;
		L_1 = AssetBundle_LoadFromMemoryAsync_Internal_m3722CC697D35ADA206E6574527A4F6681882D5D4(L_0, 0, /*hidden argument*/NULL);
		V_0 = L_1;
		goto IL_000b;
	}

IL_000b:
	{
		AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * L_2 = V_0;
		return L_2;
	}
}
// UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromMemoryAsync(System.Byte[],System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * AssetBundle_LoadFromMemoryAsync_m75E26133C7B2665F8B0F522A530B2FC6C3A106A9 (ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726* ___binary0, uint32_t ___crc1, const RuntimeMethod* method)
{
	AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * V_0 = NULL;
	{
		ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726* L_0 = ___binary0;
		uint32_t L_1 = ___crc1;
		AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * L_2;
		L_2 = AssetBundle_LoadFromMemoryAsync_Internal_m3722CC697D35ADA206E6574527A4F6681882D5D4(L_0, L_1, /*hidden argument*/NULL);
		V_0 = L_2;
		goto IL_000b;
	}

IL_000b:
	{
		AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * L_3 = V_0;
		return L_3;
	}
}
// UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromMemory_Internal(System.Byte[],System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * AssetBundle_LoadFromMemory_Internal_mBF6BF6BFDE5DA4A76735766C969591424BB9649A (ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726* ___binary0, uint32_t ___crc1, const RuntimeMethod* method)
{
	typedef AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * (*AssetBundle_LoadFromMemory_Internal_mBF6BF6BFDE5DA4A76735766C969591424BB9649A_ftn) (ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726*, uint32_t);
	static AssetBundle_LoadFromMemory_Internal_mBF6BF6BFDE5DA4A76735766C969591424BB9649A_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundle_LoadFromMemory_Internal_mBF6BF6BFDE5DA4A76735766C969591424BB9649A_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundle::LoadFromMemory_Internal(System.Byte[],System.UInt32)");
	AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * icallRetVal = _il2cpp_icall_func(___binary0, ___crc1);
	return icallRetVal;
}
// UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromMemory(System.Byte[])
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * AssetBundle_LoadFromMemory_m435A5FC269478E3E4809AB7355878B166D712E14 (ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726* ___binary0, const RuntimeMethod* method)
{
	AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * V_0 = NULL;
	{
		ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726* L_0 = ___binary0;
		AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * L_1;
		L_1 = AssetBundle_LoadFromMemory_Internal_mBF6BF6BFDE5DA4A76735766C969591424BB9649A(L_0, 0, /*hidden argument*/NULL);
		V_0 = L_1;
		goto IL_000b;
	}

IL_000b:
	{
		AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * L_2 = V_0;
		return L_2;
	}
}
// UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromMemory(System.Byte[],System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * AssetBundle_LoadFromMemory_m58D3EB0E61463CDF550EA9E9C2F6D962FF16457B (ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726* ___binary0, uint32_t ___crc1, const RuntimeMethod* method)
{
	AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * V_0 = NULL;
	{
		ByteU5BU5D_tDBBEB0E8362242FA7223000D978B0DD19D4B0726* L_0 = ___binary0;
		uint32_t L_1 = ___crc1;
		AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * L_2;
		L_2 = AssetBundle_LoadFromMemory_Internal_mBF6BF6BFDE5DA4A76735766C969591424BB9649A(L_0, L_1, /*hidden argument*/NULL);
		V_0 = L_2;
		goto IL_000b;
	}

IL_000b:
	{
		AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * L_3 = V_0;
		return L_3;
	}
}
// System.Void UnityEngine.AssetBundle::ValidateLoadFromStream(System.IO.Stream)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssetBundle_ValidateLoadFromStream_m39F4CB6F1717C48017F09FFC1E537704BB47A5A1 (Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * ___stream0, const RuntimeMethod* method)
{
	bool V_0 = false;
	bool V_1 = false;
	bool V_2 = false;
	{
		Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * L_0 = ___stream0;
		V_0 = (bool)((((RuntimeObject*)(Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB *)L_0) == ((RuntimeObject*)(RuntimeObject *)NULL))? 1 : 0);
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0019;
		}
	}
	{
		ArgumentNullException_tFB5C4621957BC53A7D1B4FDD5C38B4D6E15DB8FB * L_2 = (ArgumentNullException_tFB5C4621957BC53A7D1B4FDD5C38B4D6E15DB8FB *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentNullException_tFB5C4621957BC53A7D1B4FDD5C38B4D6E15DB8FB_il2cpp_TypeInfo_var)));
		ArgumentNullException__ctor_mAD2F05A24C92A657CBCA8C43A9A373C53739A283(L_2, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral7C527C571BCCF5DF8ADCF7BF9EED0FE0DC9AF069)), ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7E6C53D86D8ADC0DB8EFC0A2CD9877CC8BC9914)), /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_2, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&AssetBundle_ValidateLoadFromStream_m39F4CB6F1717C48017F09FFC1E537704BB47A5A1_RuntimeMethod_var)));
	}

IL_0019:
	{
		Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * L_3 = ___stream0;
		NullCheck(L_3);
		bool L_4;
		L_4 = VirtFuncInvoker0< bool >::Invoke(7 /* System.Boolean System.IO.Stream::get_CanRead() */, L_3);
		V_1 = (bool)((((int32_t)L_4) == ((int32_t)0))? 1 : 0);
		bool L_5 = V_1;
		if (!L_5)
		{
			goto IL_0036;
		}
	}
	{
		ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00 * L_6 = (ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00 *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00_il2cpp_TypeInfo_var)));
		ArgumentException__ctor_m71044C2110E357B71A1C30D2561C3F861AF1DC0D(L_6, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralD6B4B2A0E6284497D6C035D6CC8503F491C92098)), ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7E6C53D86D8ADC0DB8EFC0A2CD9877CC8BC9914)), /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_6, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&AssetBundle_ValidateLoadFromStream_m39F4CB6F1717C48017F09FFC1E537704BB47A5A1_RuntimeMethod_var)));
	}

IL_0036:
	{
		Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * L_7 = ___stream0;
		NullCheck(L_7);
		bool L_8;
		L_8 = VirtFuncInvoker0< bool >::Invoke(8 /* System.Boolean System.IO.Stream::get_CanSeek() */, L_7);
		V_2 = (bool)((((int32_t)L_8) == ((int32_t)0))? 1 : 0);
		bool L_9 = V_2;
		if (!L_9)
		{
			goto IL_0053;
		}
	}
	{
		ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00 * L_10 = (ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00 *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00_il2cpp_TypeInfo_var)));
		ArgumentException__ctor_m71044C2110E357B71A1C30D2561C3F861AF1DC0D(L_10, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralAD3DDCBBB6118D9DAC3520876EC8EA0BCBCD6C23)), ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralF7E6C53D86D8ADC0DB8EFC0A2CD9877CC8BC9914)), /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_10, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&AssetBundle_ValidateLoadFromStream_m39F4CB6F1717C48017F09FFC1E537704BB47A5A1_RuntimeMethod_var)));
	}

IL_0053:
	{
		return;
	}
}
// UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromStreamAsync(System.IO.Stream,System.UInt32,System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * AssetBundle_LoadFromStreamAsync_m4891FFAF61A3100FEFC5BDBBA60D2D946A7FBFEB (Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * ___stream0, uint32_t ___crc1, uint32_t ___managedReadBufferSize2, const RuntimeMethod* method)
{
	AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * V_0 = NULL;
	{
		Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * L_0 = ___stream0;
		AssetBundle_ValidateLoadFromStream_m39F4CB6F1717C48017F09FFC1E537704BB47A5A1(L_0, /*hidden argument*/NULL);
		Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * L_1 = ___stream0;
		uint32_t L_2 = ___crc1;
		uint32_t L_3 = ___managedReadBufferSize2;
		AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * L_4;
		L_4 = AssetBundle_LoadFromStreamAsyncInternal_m8B64B48FD5D92F632AD5D755C5B9F6B48E5D1F51(L_1, L_2, L_3, /*hidden argument*/NULL);
		V_0 = L_4;
		goto IL_0013;
	}

IL_0013:
	{
		AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * L_5 = V_0;
		return L_5;
	}
}
// UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromStreamAsync(System.IO.Stream,System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * AssetBundle_LoadFromStreamAsync_m750F83983CD25FB7C26E103BFCED35AA04725A97 (Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * ___stream0, uint32_t ___crc1, const RuntimeMethod* method)
{
	AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * V_0 = NULL;
	{
		Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * L_0 = ___stream0;
		AssetBundle_ValidateLoadFromStream_m39F4CB6F1717C48017F09FFC1E537704BB47A5A1(L_0, /*hidden argument*/NULL);
		Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * L_1 = ___stream0;
		uint32_t L_2 = ___crc1;
		AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * L_3;
		L_3 = AssetBundle_LoadFromStreamAsyncInternal_m8B64B48FD5D92F632AD5D755C5B9F6B48E5D1F51(L_1, L_2, 0, /*hidden argument*/NULL);
		V_0 = L_3;
		goto IL_0013;
	}

IL_0013:
	{
		AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * L_4 = V_0;
		return L_4;
	}
}
// UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromStreamAsync(System.IO.Stream)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * AssetBundle_LoadFromStreamAsync_mC33AE0A0A1C96EDE7F23968397868A45DAFE01F2 (Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * ___stream0, const RuntimeMethod* method)
{
	AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * V_0 = NULL;
	{
		Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * L_0 = ___stream0;
		AssetBundle_ValidateLoadFromStream_m39F4CB6F1717C48017F09FFC1E537704BB47A5A1(L_0, /*hidden argument*/NULL);
		Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * L_1 = ___stream0;
		AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * L_2;
		L_2 = AssetBundle_LoadFromStreamAsyncInternal_m8B64B48FD5D92F632AD5D755C5B9F6B48E5D1F51(L_1, 0, 0, /*hidden argument*/NULL);
		V_0 = L_2;
		goto IL_0013;
	}

IL_0013:
	{
		AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * L_3 = V_0;
		return L_3;
	}
}
// UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromStream(System.IO.Stream,System.UInt32,System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * AssetBundle_LoadFromStream_mB07F4D123259845295A21A4D14D7EC2B7BB647DF (Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * ___stream0, uint32_t ___crc1, uint32_t ___managedReadBufferSize2, const RuntimeMethod* method)
{
	AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * V_0 = NULL;
	{
		Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * L_0 = ___stream0;
		AssetBundle_ValidateLoadFromStream_m39F4CB6F1717C48017F09FFC1E537704BB47A5A1(L_0, /*hidden argument*/NULL);
		Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * L_1 = ___stream0;
		uint32_t L_2 = ___crc1;
		uint32_t L_3 = ___managedReadBufferSize2;
		AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * L_4;
		L_4 = AssetBundle_LoadFromStreamInternal_m463BAC1F32549D1B4AE88A5185A135FE7676619A(L_1, L_2, L_3, /*hidden argument*/NULL);
		V_0 = L_4;
		goto IL_0013;
	}

IL_0013:
	{
		AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * L_5 = V_0;
		return L_5;
	}
}
// UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromStream(System.IO.Stream,System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * AssetBundle_LoadFromStream_m4BCF56F96EE96106529A6D1366EA9C8B633A4895 (Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * ___stream0, uint32_t ___crc1, const RuntimeMethod* method)
{
	AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * V_0 = NULL;
	{
		Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * L_0 = ___stream0;
		AssetBundle_ValidateLoadFromStream_m39F4CB6F1717C48017F09FFC1E537704BB47A5A1(L_0, /*hidden argument*/NULL);
		Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * L_1 = ___stream0;
		uint32_t L_2 = ___crc1;
		AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * L_3;
		L_3 = AssetBundle_LoadFromStreamInternal_m463BAC1F32549D1B4AE88A5185A135FE7676619A(L_1, L_2, 0, /*hidden argument*/NULL);
		V_0 = L_3;
		goto IL_0013;
	}

IL_0013:
	{
		AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * L_4 = V_0;
		return L_4;
	}
}
// UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromStream(System.IO.Stream)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * AssetBundle_LoadFromStream_mAE868802265CD014AAB9E8ACABB8F94DF3B6AE84 (Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * ___stream0, const RuntimeMethod* method)
{
	AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * V_0 = NULL;
	{
		Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * L_0 = ___stream0;
		AssetBundle_ValidateLoadFromStream_m39F4CB6F1717C48017F09FFC1E537704BB47A5A1(L_0, /*hidden argument*/NULL);
		Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * L_1 = ___stream0;
		AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * L_2;
		L_2 = AssetBundle_LoadFromStreamInternal_m463BAC1F32549D1B4AE88A5185A135FE7676619A(L_1, 0, 0, /*hidden argument*/NULL);
		V_0 = L_2;
		goto IL_0013;
	}

IL_0013:
	{
		AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * L_3 = V_0;
		return L_3;
	}
}
// UnityEngine.AssetBundleCreateRequest UnityEngine.AssetBundle::LoadFromStreamAsyncInternal(System.IO.Stream,System.UInt32,System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * AssetBundle_LoadFromStreamAsyncInternal_m8B64B48FD5D92F632AD5D755C5B9F6B48E5D1F51 (Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * ___stream0, uint32_t ___crc1, uint32_t ___managedReadBufferSize2, const RuntimeMethod* method)
{
	typedef AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * (*AssetBundle_LoadFromStreamAsyncInternal_m8B64B48FD5D92F632AD5D755C5B9F6B48E5D1F51_ftn) (Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB *, uint32_t, uint32_t);
	static AssetBundle_LoadFromStreamAsyncInternal_m8B64B48FD5D92F632AD5D755C5B9F6B48E5D1F51_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundle_LoadFromStreamAsyncInternal_m8B64B48FD5D92F632AD5D755C5B9F6B48E5D1F51_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundle::LoadFromStreamAsyncInternal(System.IO.Stream,System.UInt32,System.UInt32)");
	AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * icallRetVal = _il2cpp_icall_func(___stream0, ___crc1, ___managedReadBufferSize2);
	return icallRetVal;
}
// UnityEngine.AssetBundle UnityEngine.AssetBundle::LoadFromStreamInternal(System.IO.Stream,System.UInt32,System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * AssetBundle_LoadFromStreamInternal_m463BAC1F32549D1B4AE88A5185A135FE7676619A (Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB * ___stream0, uint32_t ___crc1, uint32_t ___managedReadBufferSize2, const RuntimeMethod* method)
{
	typedef AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * (*AssetBundle_LoadFromStreamInternal_m463BAC1F32549D1B4AE88A5185A135FE7676619A_ftn) (Stream_t5DC87DD578C2C5298D98E7802E92DEABB66E2ECB *, uint32_t, uint32_t);
	static AssetBundle_LoadFromStreamInternal_m463BAC1F32549D1B4AE88A5185A135FE7676619A_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundle_LoadFromStreamInternal_m463BAC1F32549D1B4AE88A5185A135FE7676619A_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundle::LoadFromStreamInternal(System.IO.Stream,System.UInt32,System.UInt32)");
	AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * icallRetVal = _il2cpp_icall_func(___stream0, ___crc1, ___managedReadBufferSize2);
	return icallRetVal;
}
// System.Boolean UnityEngine.AssetBundle::get_isStreamedSceneAssetBundle()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AssetBundle_get_isStreamedSceneAssetBundle_m0F0FDECFC88F31CA01E7E36532E186224174A7A4 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, const RuntimeMethod* method)
{
	typedef bool (*AssetBundle_get_isStreamedSceneAssetBundle_m0F0FDECFC88F31CA01E7E36532E186224174A7A4_ftn) (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 *);
	static AssetBundle_get_isStreamedSceneAssetBundle_m0F0FDECFC88F31CA01E7E36532E186224174A7A4_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundle_get_isStreamedSceneAssetBundle_m0F0FDECFC88F31CA01E7E36532E186224174A7A4_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundle::get_isStreamedSceneAssetBundle()");
	bool icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
// System.Boolean UnityEngine.AssetBundle::Contains(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AssetBundle_Contains_m00D431624B6594F6222D946F50A7DCA8559B1B44 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, String_t* ___name0, const RuntimeMethod* method)
{
	typedef bool (*AssetBundle_Contains_m00D431624B6594F6222D946F50A7DCA8559B1B44_ftn) (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 *, String_t*);
	static AssetBundle_Contains_m00D431624B6594F6222D946F50A7DCA8559B1B44_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundle_Contains_m00D431624B6594F6222D946F50A7DCA8559B1B44_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundle::Contains(System.String)");
	bool icallRetVal = _il2cpp_icall_func(__this, ___name0);
	return icallRetVal;
}
// UnityEngine.Object UnityEngine.AssetBundle::LoadAsset(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * AssetBundle_LoadAsset_m598BB4E86B07C2BADAE6ED6FFBE5BB5E7A3392D5 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, String_t* ___name0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * V_0 = NULL;
	{
		String_t* L_0 = ___name0;
		RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9  L_1 = { reinterpret_cast<intptr_t> (Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_0_0_0_var) };
		IL2CPP_RUNTIME_CLASS_INIT(Type_t_il2cpp_TypeInfo_var);
		Type_t * L_2;
		L_2 = Type_GetTypeFromHandle_m180706985100778663E9C75D4FC001D367C0120F(L_1, /*hidden argument*/NULL);
		Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * L_3;
		L_3 = AssetBundle_LoadAsset_m9139320F8B6D3E43B7D29AA7A60030306AE0A2C6(__this, L_0, L_2, /*hidden argument*/NULL);
		V_0 = L_3;
		goto IL_0015;
	}

IL_0015:
	{
		Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * L_4 = V_0;
		return L_4;
	}
}
// UnityEngine.Object UnityEngine.AssetBundle::LoadAsset(System.String,System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * AssetBundle_LoadAsset_m9139320F8B6D3E43B7D29AA7A60030306AE0A2C6 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, String_t* ___name0, Type_t * ___type1, const RuntimeMethod* method)
{
	bool V_0 = false;
	bool V_1 = false;
	bool V_2 = false;
	Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * V_3 = NULL;
	{
		String_t* L_0 = ___name0;
		V_0 = (bool)((((RuntimeObject*)(String_t*)L_0) == ((RuntimeObject*)(RuntimeObject *)NULL))? 1 : 0);
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0015;
		}
	}
	{
		NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724 * L_2 = (NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724 *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724_il2cpp_TypeInfo_var)));
		NullReferenceException__ctor_mF1733893E10358B400E817297D686A48AB3FB7B3(L_2, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral51AFAC88FE0F5AA20A5F618F48C4CEAB7CC3DB34)), /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_2, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&AssetBundle_LoadAsset_m9139320F8B6D3E43B7D29AA7A60030306AE0A2C6_RuntimeMethod_var)));
	}

IL_0015:
	{
		String_t* L_3 = ___name0;
		NullCheck(L_3);
		int32_t L_4;
		L_4 = String_get_Length_m129FC0ADA02FECBED3C0B1A809AE84A5AEE1CF09_inline(L_3, /*hidden argument*/NULL);
		V_1 = (bool)((((int32_t)L_4) == ((int32_t)0))? 1 : 0);
		bool L_5 = V_1;
		if (!L_5)
		{
			goto IL_002e;
		}
	}
	{
		ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00 * L_6 = (ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00 *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00_il2cpp_TypeInfo_var)));
		ArgumentException__ctor_m2D35EAD113C2ADC99EB17B940A2097A93FD23EFC(L_6, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral358518C7B9669F4CD21363772C3317136923413A)), /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_6, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&AssetBundle_LoadAsset_m9139320F8B6D3E43B7D29AA7A60030306AE0A2C6_RuntimeMethod_var)));
	}

IL_002e:
	{
		Type_t * L_7 = ___type1;
		V_2 = (bool)((((RuntimeObject*)(Type_t *)L_7) == ((RuntimeObject*)(RuntimeObject *)NULL))? 1 : 0);
		bool L_8 = V_2;
		if (!L_8)
		{
			goto IL_0042;
		}
	}
	{
		NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724 * L_9 = (NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724 *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724_il2cpp_TypeInfo_var)));
		NullReferenceException__ctor_mF1733893E10358B400E817297D686A48AB3FB7B3(L_9, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral820724398B4A28E6B958D19B3FC28BB8BF2787D3)), /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_9, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&AssetBundle_LoadAsset_m9139320F8B6D3E43B7D29AA7A60030306AE0A2C6_RuntimeMethod_var)));
	}

IL_0042:
	{
		String_t* L_10 = ___name0;
		Type_t * L_11 = ___type1;
		Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * L_12;
		L_12 = AssetBundle_LoadAsset_Internal_mFB165539087545C4B5763BA8B590D84318C6FE1B(__this, L_10, L_11, /*hidden argument*/NULL);
		V_3 = L_12;
		goto IL_004d;
	}

IL_004d:
	{
		Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * L_13 = V_3;
		return L_13;
	}
}
// UnityEngine.Object UnityEngine.AssetBundle::LoadAsset_Internal(System.String,System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * AssetBundle_LoadAsset_Internal_mFB165539087545C4B5763BA8B590D84318C6FE1B (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, String_t* ___name0, Type_t * ___type1, const RuntimeMethod* method)
{
	typedef Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * (*AssetBundle_LoadAsset_Internal_mFB165539087545C4B5763BA8B590D84318C6FE1B_ftn) (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 *, String_t*, Type_t *);
	static AssetBundle_LoadAsset_Internal_mFB165539087545C4B5763BA8B590D84318C6FE1B_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundle_LoadAsset_Internal_mFB165539087545C4B5763BA8B590D84318C6FE1B_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundle::LoadAsset_Internal(System.String,System.Type)");
	Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * icallRetVal = _il2cpp_icall_func(__this, ___name0, ___type1);
	return icallRetVal;
}
// UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAssetAsync(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * AssetBundle_LoadAssetAsync_m033CE66F7C38A5A46C627004B0D3AD830C4CE4F3 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, String_t* ___name0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * V_0 = NULL;
	{
		String_t* L_0 = ___name0;
		RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9  L_1 = { reinterpret_cast<intptr_t> (Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_0_0_0_var) };
		IL2CPP_RUNTIME_CLASS_INIT(Type_t_il2cpp_TypeInfo_var);
		Type_t * L_2;
		L_2 = Type_GetTypeFromHandle_m180706985100778663E9C75D4FC001D367C0120F(L_1, /*hidden argument*/NULL);
		AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * L_3;
		L_3 = AssetBundle_LoadAssetAsync_m63C7C5654FA8D0824DC920A1B1530C37CCB3DF6E(__this, L_0, L_2, /*hidden argument*/NULL);
		V_0 = L_3;
		goto IL_0015;
	}

IL_0015:
	{
		AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * L_4 = V_0;
		return L_4;
	}
}
// UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAssetAsync(System.String,System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * AssetBundle_LoadAssetAsync_m63C7C5654FA8D0824DC920A1B1530C37CCB3DF6E (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, String_t* ___name0, Type_t * ___type1, const RuntimeMethod* method)
{
	bool V_0 = false;
	bool V_1 = false;
	bool V_2 = false;
	AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * V_3 = NULL;
	{
		String_t* L_0 = ___name0;
		V_0 = (bool)((((RuntimeObject*)(String_t*)L_0) == ((RuntimeObject*)(RuntimeObject *)NULL))? 1 : 0);
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0015;
		}
	}
	{
		NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724 * L_2 = (NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724 *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724_il2cpp_TypeInfo_var)));
		NullReferenceException__ctor_mF1733893E10358B400E817297D686A48AB3FB7B3(L_2, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral51AFAC88FE0F5AA20A5F618F48C4CEAB7CC3DB34)), /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_2, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&AssetBundle_LoadAssetAsync_m63C7C5654FA8D0824DC920A1B1530C37CCB3DF6E_RuntimeMethod_var)));
	}

IL_0015:
	{
		String_t* L_3 = ___name0;
		NullCheck(L_3);
		int32_t L_4;
		L_4 = String_get_Length_m129FC0ADA02FECBED3C0B1A809AE84A5AEE1CF09_inline(L_3, /*hidden argument*/NULL);
		V_1 = (bool)((((int32_t)L_4) == ((int32_t)0))? 1 : 0);
		bool L_5 = V_1;
		if (!L_5)
		{
			goto IL_002e;
		}
	}
	{
		ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00 * L_6 = (ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00 *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00_il2cpp_TypeInfo_var)));
		ArgumentException__ctor_m2D35EAD113C2ADC99EB17B940A2097A93FD23EFC(L_6, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral358518C7B9669F4CD21363772C3317136923413A)), /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_6, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&AssetBundle_LoadAssetAsync_m63C7C5654FA8D0824DC920A1B1530C37CCB3DF6E_RuntimeMethod_var)));
	}

IL_002e:
	{
		Type_t * L_7 = ___type1;
		V_2 = (bool)((((RuntimeObject*)(Type_t *)L_7) == ((RuntimeObject*)(RuntimeObject *)NULL))? 1 : 0);
		bool L_8 = V_2;
		if (!L_8)
		{
			goto IL_0042;
		}
	}
	{
		NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724 * L_9 = (NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724 *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724_il2cpp_TypeInfo_var)));
		NullReferenceException__ctor_mF1733893E10358B400E817297D686A48AB3FB7B3(L_9, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral820724398B4A28E6B958D19B3FC28BB8BF2787D3)), /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_9, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&AssetBundle_LoadAssetAsync_m63C7C5654FA8D0824DC920A1B1530C37CCB3DF6E_RuntimeMethod_var)));
	}

IL_0042:
	{
		String_t* L_10 = ___name0;
		Type_t * L_11 = ___type1;
		AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * L_12;
		L_12 = AssetBundle_LoadAssetAsync_Internal_m89D45EFB48D3C4CF7E481EF41F7ACF24500E7019(__this, L_10, L_11, /*hidden argument*/NULL);
		V_3 = L_12;
		goto IL_004d;
	}

IL_004d:
	{
		AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * L_13 = V_3;
		return L_13;
	}
}
// UnityEngine.Object[] UnityEngine.AssetBundle::LoadAssetWithSubAssets(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* AssetBundle_LoadAssetWithSubAssets_mB25B0117E71DABE78028CE638F61D6C6C9152DDC (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, String_t* ___name0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* V_0 = NULL;
	{
		String_t* L_0 = ___name0;
		RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9  L_1 = { reinterpret_cast<intptr_t> (Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_0_0_0_var) };
		IL2CPP_RUNTIME_CLASS_INIT(Type_t_il2cpp_TypeInfo_var);
		Type_t * L_2;
		L_2 = Type_GetTypeFromHandle_m180706985100778663E9C75D4FC001D367C0120F(L_1, /*hidden argument*/NULL);
		ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* L_3;
		L_3 = AssetBundle_LoadAssetWithSubAssets_mAABE29A8F1719A85DC474932FF12A44B29CACB45(__this, L_0, L_2, /*hidden argument*/NULL);
		V_0 = L_3;
		goto IL_0015;
	}

IL_0015:
	{
		ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* L_4 = V_0;
		return L_4;
	}
}
// UnityEngine.Object[] UnityEngine.AssetBundle::LoadAssetWithSubAssets(System.String,System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* AssetBundle_LoadAssetWithSubAssets_mAABE29A8F1719A85DC474932FF12A44B29CACB45 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, String_t* ___name0, Type_t * ___type1, const RuntimeMethod* method)
{
	bool V_0 = false;
	bool V_1 = false;
	bool V_2 = false;
	ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* V_3 = NULL;
	{
		String_t* L_0 = ___name0;
		V_0 = (bool)((((RuntimeObject*)(String_t*)L_0) == ((RuntimeObject*)(RuntimeObject *)NULL))? 1 : 0);
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0015;
		}
	}
	{
		NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724 * L_2 = (NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724 *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724_il2cpp_TypeInfo_var)));
		NullReferenceException__ctor_mF1733893E10358B400E817297D686A48AB3FB7B3(L_2, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral51AFAC88FE0F5AA20A5F618F48C4CEAB7CC3DB34)), /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_2, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&AssetBundle_LoadAssetWithSubAssets_mAABE29A8F1719A85DC474932FF12A44B29CACB45_RuntimeMethod_var)));
	}

IL_0015:
	{
		String_t* L_3 = ___name0;
		NullCheck(L_3);
		int32_t L_4;
		L_4 = String_get_Length_m129FC0ADA02FECBED3C0B1A809AE84A5AEE1CF09_inline(L_3, /*hidden argument*/NULL);
		V_1 = (bool)((((int32_t)L_4) == ((int32_t)0))? 1 : 0);
		bool L_5 = V_1;
		if (!L_5)
		{
			goto IL_002e;
		}
	}
	{
		ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00 * L_6 = (ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00 *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00_il2cpp_TypeInfo_var)));
		ArgumentException__ctor_m2D35EAD113C2ADC99EB17B940A2097A93FD23EFC(L_6, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral358518C7B9669F4CD21363772C3317136923413A)), /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_6, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&AssetBundle_LoadAssetWithSubAssets_mAABE29A8F1719A85DC474932FF12A44B29CACB45_RuntimeMethod_var)));
	}

IL_002e:
	{
		Type_t * L_7 = ___type1;
		V_2 = (bool)((((RuntimeObject*)(Type_t *)L_7) == ((RuntimeObject*)(RuntimeObject *)NULL))? 1 : 0);
		bool L_8 = V_2;
		if (!L_8)
		{
			goto IL_0042;
		}
	}
	{
		NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724 * L_9 = (NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724 *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724_il2cpp_TypeInfo_var)));
		NullReferenceException__ctor_mF1733893E10358B400E817297D686A48AB3FB7B3(L_9, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral820724398B4A28E6B958D19B3FC28BB8BF2787D3)), /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_9, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&AssetBundle_LoadAssetWithSubAssets_mAABE29A8F1719A85DC474932FF12A44B29CACB45_RuntimeMethod_var)));
	}

IL_0042:
	{
		String_t* L_10 = ___name0;
		Type_t * L_11 = ___type1;
		ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* L_12;
		L_12 = AssetBundle_LoadAssetWithSubAssets_Internal_m70FFE92F072F934C4121DF328D6C8E1D19D93E51(__this, L_10, L_11, /*hidden argument*/NULL);
		V_3 = L_12;
		goto IL_004d;
	}

IL_004d:
	{
		ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* L_13 = V_3;
		return L_13;
	}
}
// UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAssetWithSubAssetsAsync(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * AssetBundle_LoadAssetWithSubAssetsAsync_m48EEBA8BAC4B511F44F8B878882E6102D50E6843 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, String_t* ___name0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * V_0 = NULL;
	{
		String_t* L_0 = ___name0;
		RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9  L_1 = { reinterpret_cast<intptr_t> (Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_0_0_0_var) };
		IL2CPP_RUNTIME_CLASS_INIT(Type_t_il2cpp_TypeInfo_var);
		Type_t * L_2;
		L_2 = Type_GetTypeFromHandle_m180706985100778663E9C75D4FC001D367C0120F(L_1, /*hidden argument*/NULL);
		AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * L_3;
		L_3 = AssetBundle_LoadAssetWithSubAssetsAsync_m655947AD33D64C1F71F822AEFDE3D2A57C809AF5(__this, L_0, L_2, /*hidden argument*/NULL);
		V_0 = L_3;
		goto IL_0015;
	}

IL_0015:
	{
		AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * L_4 = V_0;
		return L_4;
	}
}
// UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAssetWithSubAssetsAsync(System.String,System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * AssetBundle_LoadAssetWithSubAssetsAsync_m655947AD33D64C1F71F822AEFDE3D2A57C809AF5 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, String_t* ___name0, Type_t * ___type1, const RuntimeMethod* method)
{
	bool V_0 = false;
	bool V_1 = false;
	bool V_2 = false;
	AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * V_3 = NULL;
	{
		String_t* L_0 = ___name0;
		V_0 = (bool)((((RuntimeObject*)(String_t*)L_0) == ((RuntimeObject*)(RuntimeObject *)NULL))? 1 : 0);
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0015;
		}
	}
	{
		NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724 * L_2 = (NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724 *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724_il2cpp_TypeInfo_var)));
		NullReferenceException__ctor_mF1733893E10358B400E817297D686A48AB3FB7B3(L_2, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral51AFAC88FE0F5AA20A5F618F48C4CEAB7CC3DB34)), /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_2, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&AssetBundle_LoadAssetWithSubAssetsAsync_m655947AD33D64C1F71F822AEFDE3D2A57C809AF5_RuntimeMethod_var)));
	}

IL_0015:
	{
		String_t* L_3 = ___name0;
		NullCheck(L_3);
		int32_t L_4;
		L_4 = String_get_Length_m129FC0ADA02FECBED3C0B1A809AE84A5AEE1CF09_inline(L_3, /*hidden argument*/NULL);
		V_1 = (bool)((((int32_t)L_4) == ((int32_t)0))? 1 : 0);
		bool L_5 = V_1;
		if (!L_5)
		{
			goto IL_002e;
		}
	}
	{
		ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00 * L_6 = (ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00 *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentException_t505FA8C11E883F2D96C797AD9D396490794DEE00_il2cpp_TypeInfo_var)));
		ArgumentException__ctor_m2D35EAD113C2ADC99EB17B940A2097A93FD23EFC(L_6, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral358518C7B9669F4CD21363772C3317136923413A)), /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_6, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&AssetBundle_LoadAssetWithSubAssetsAsync_m655947AD33D64C1F71F822AEFDE3D2A57C809AF5_RuntimeMethod_var)));
	}

IL_002e:
	{
		Type_t * L_7 = ___type1;
		V_2 = (bool)((((RuntimeObject*)(Type_t *)L_7) == ((RuntimeObject*)(RuntimeObject *)NULL))? 1 : 0);
		bool L_8 = V_2;
		if (!L_8)
		{
			goto IL_0042;
		}
	}
	{
		NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724 * L_9 = (NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724 *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724_il2cpp_TypeInfo_var)));
		NullReferenceException__ctor_mF1733893E10358B400E817297D686A48AB3FB7B3(L_9, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral820724398B4A28E6B958D19B3FC28BB8BF2787D3)), /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_9, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&AssetBundle_LoadAssetWithSubAssetsAsync_m655947AD33D64C1F71F822AEFDE3D2A57C809AF5_RuntimeMethod_var)));
	}

IL_0042:
	{
		String_t* L_10 = ___name0;
		Type_t * L_11 = ___type1;
		AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * L_12;
		L_12 = AssetBundle_LoadAssetWithSubAssetsAsync_Internal_mD671A7674C5E61F00AF0127C2D92F56DD34E3DCB(__this, L_10, L_11, /*hidden argument*/NULL);
		V_3 = L_12;
		goto IL_004d;
	}

IL_004d:
	{
		AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * L_13 = V_3;
		return L_13;
	}
}
// UnityEngine.Object[] UnityEngine.AssetBundle::LoadAllAssets()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* AssetBundle_LoadAllAssets_mB132E05BAFF4776C59BC80082A2FE68060741603 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* V_0 = NULL;
	{
		RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9  L_0 = { reinterpret_cast<intptr_t> (Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_0_0_0_var) };
		IL2CPP_RUNTIME_CLASS_INIT(Type_t_il2cpp_TypeInfo_var);
		Type_t * L_1;
		L_1 = Type_GetTypeFromHandle_m180706985100778663E9C75D4FC001D367C0120F(L_0, /*hidden argument*/NULL);
		ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* L_2;
		L_2 = AssetBundle_LoadAllAssets_mAB1A820C148FF25D811D16DC5C8F5D39882D480A(__this, L_1, /*hidden argument*/NULL);
		V_0 = L_2;
		goto IL_0014;
	}

IL_0014:
	{
		ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* L_3 = V_0;
		return L_3;
	}
}
// UnityEngine.Object[] UnityEngine.AssetBundle::LoadAllAssets(System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* AssetBundle_LoadAllAssets_mAB1A820C148FF25D811D16DC5C8F5D39882D480A (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, Type_t * ___type0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* V_1 = NULL;
	{
		Type_t * L_0 = ___type0;
		V_0 = (bool)((((RuntimeObject*)(Type_t *)L_0) == ((RuntimeObject*)(RuntimeObject *)NULL))? 1 : 0);
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0015;
		}
	}
	{
		NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724 * L_2 = (NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724 *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724_il2cpp_TypeInfo_var)));
		NullReferenceException__ctor_mF1733893E10358B400E817297D686A48AB3FB7B3(L_2, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral820724398B4A28E6B958D19B3FC28BB8BF2787D3)), /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_2, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&AssetBundle_LoadAllAssets_mAB1A820C148FF25D811D16DC5C8F5D39882D480A_RuntimeMethod_var)));
	}

IL_0015:
	{
		Type_t * L_3 = ___type0;
		ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* L_4;
		L_4 = AssetBundle_LoadAssetWithSubAssets_Internal_m70FFE92F072F934C4121DF328D6C8E1D19D93E51(__this, _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709, L_3, /*hidden argument*/NULL);
		V_1 = L_4;
		goto IL_0024;
	}

IL_0024:
	{
		ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* L_5 = V_1;
		return L_5;
	}
}
// UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAllAssetsAsync()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * AssetBundle_LoadAllAssetsAsync_mFBD03164799EA8A821309FBE8C4C08340943A5E5 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * V_0 = NULL;
	{
		RuntimeTypeHandle_tC33965ADA3E041E0C94AF05E5CB527B56482CEF9  L_0 = { reinterpret_cast<intptr_t> (Object_tF2F3778131EFF286AF62B7B013A170F95A91571A_0_0_0_var) };
		IL2CPP_RUNTIME_CLASS_INIT(Type_t_il2cpp_TypeInfo_var);
		Type_t * L_1;
		L_1 = Type_GetTypeFromHandle_m180706985100778663E9C75D4FC001D367C0120F(L_0, /*hidden argument*/NULL);
		AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * L_2;
		L_2 = AssetBundle_LoadAllAssetsAsync_m213D935C1F40DB2098F00BD852B5799F00542137(__this, L_1, /*hidden argument*/NULL);
		V_0 = L_2;
		goto IL_0014;
	}

IL_0014:
	{
		AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * L_3 = V_0;
		return L_3;
	}
}
// UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAllAssetsAsync(System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * AssetBundle_LoadAllAssetsAsync_m213D935C1F40DB2098F00BD852B5799F00542137 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, Type_t * ___type0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * V_1 = NULL;
	{
		Type_t * L_0 = ___type0;
		V_0 = (bool)((((RuntimeObject*)(Type_t *)L_0) == ((RuntimeObject*)(RuntimeObject *)NULL))? 1 : 0);
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0015;
		}
	}
	{
		NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724 * L_2 = (NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724 *)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NullReferenceException_t44B4F3CDE3111E74591952B8BE8707B28866D724_il2cpp_TypeInfo_var)));
		NullReferenceException__ctor_mF1733893E10358B400E817297D686A48AB3FB7B3(L_2, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral820724398B4A28E6B958D19B3FC28BB8BF2787D3)), /*hidden argument*/NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_2, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&AssetBundle_LoadAllAssetsAsync_m213D935C1F40DB2098F00BD852B5799F00542137_RuntimeMethod_var)));
	}

IL_0015:
	{
		Type_t * L_3 = ___type0;
		AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * L_4;
		L_4 = AssetBundle_LoadAssetWithSubAssetsAsync_Internal_mD671A7674C5E61F00AF0127C2D92F56DD34E3DCB(__this, _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709, L_3, /*hidden argument*/NULL);
		V_1 = L_4;
		goto IL_0024;
	}

IL_0024:
	{
		AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * L_5 = V_1;
		return L_5;
	}
}
// UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAssetAsync_Internal(System.String,System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * AssetBundle_LoadAssetAsync_Internal_m89D45EFB48D3C4CF7E481EF41F7ACF24500E7019 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, String_t* ___name0, Type_t * ___type1, const RuntimeMethod* method)
{
	typedef AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * (*AssetBundle_LoadAssetAsync_Internal_m89D45EFB48D3C4CF7E481EF41F7ACF24500E7019_ftn) (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 *, String_t*, Type_t *);
	static AssetBundle_LoadAssetAsync_Internal_m89D45EFB48D3C4CF7E481EF41F7ACF24500E7019_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundle_LoadAssetAsync_Internal_m89D45EFB48D3C4CF7E481EF41F7ACF24500E7019_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundle::LoadAssetAsync_Internal(System.String,System.Type)");
	AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * icallRetVal = _il2cpp_icall_func(__this, ___name0, ___type1);
	return icallRetVal;
}
// System.Void UnityEngine.AssetBundle::Unload(System.Boolean)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssetBundle_Unload_m0DEBACB284F6CECA8DF21486D1BBE1189F6A5D66 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, bool ___unloadAllLoadedObjects0, const RuntimeMethod* method)
{
	typedef void (*AssetBundle_Unload_m0DEBACB284F6CECA8DF21486D1BBE1189F6A5D66_ftn) (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 *, bool);
	static AssetBundle_Unload_m0DEBACB284F6CECA8DF21486D1BBE1189F6A5D66_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundle_Unload_m0DEBACB284F6CECA8DF21486D1BBE1189F6A5D66_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundle::Unload(System.Boolean)");
	_il2cpp_icall_func(__this, ___unloadAllLoadedObjects0);
}
// System.String[] UnityEngine.AssetBundle::GetAllAssetNames()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* AssetBundle_GetAllAssetNames_m7012B92C4E0BDF3975EBC22DE2515AFEB0E9D409 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, const RuntimeMethod* method)
{
	typedef StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* (*AssetBundle_GetAllAssetNames_m7012B92C4E0BDF3975EBC22DE2515AFEB0E9D409_ftn) (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 *);
	static AssetBundle_GetAllAssetNames_m7012B92C4E0BDF3975EBC22DE2515AFEB0E9D409_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundle_GetAllAssetNames_m7012B92C4E0BDF3975EBC22DE2515AFEB0E9D409_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundle::GetAllAssetNames()");
	StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
// System.String[] UnityEngine.AssetBundle::GetAllScenePaths()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* AssetBundle_GetAllScenePaths_mFE0D5927A3BEEC04BDB62FA436FA9051299B7BC6 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, const RuntimeMethod* method)
{
	typedef StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* (*AssetBundle_GetAllScenePaths_mFE0D5927A3BEEC04BDB62FA436FA9051299B7BC6_ftn) (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 *);
	static AssetBundle_GetAllScenePaths_mFE0D5927A3BEEC04BDB62FA436FA9051299B7BC6_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundle_GetAllScenePaths_mFE0D5927A3BEEC04BDB62FA436FA9051299B7BC6_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundle::GetAllScenePaths()");
	StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
// UnityEngine.Object[] UnityEngine.AssetBundle::LoadAssetWithSubAssets_Internal(System.String,System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* AssetBundle_LoadAssetWithSubAssets_Internal_m70FFE92F072F934C4121DF328D6C8E1D19D93E51 (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, String_t* ___name0, Type_t * ___type1, const RuntimeMethod* method)
{
	typedef ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* (*AssetBundle_LoadAssetWithSubAssets_Internal_m70FFE92F072F934C4121DF328D6C8E1D19D93E51_ftn) (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 *, String_t*, Type_t *);
	static AssetBundle_LoadAssetWithSubAssets_Internal_m70FFE92F072F934C4121DF328D6C8E1D19D93E51_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundle_LoadAssetWithSubAssets_Internal_m70FFE92F072F934C4121DF328D6C8E1D19D93E51_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundle::LoadAssetWithSubAssets_Internal(System.String,System.Type)");
	ObjectU5BU5D_t1256A8B00BB71C7F582BF08257BE4F826FF64873* icallRetVal = _il2cpp_icall_func(__this, ___name0, ___type1);
	return icallRetVal;
}
// UnityEngine.AssetBundleRequest UnityEngine.AssetBundle::LoadAssetWithSubAssetsAsync_Internal(System.String,System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * AssetBundle_LoadAssetWithSubAssetsAsync_Internal_mD671A7674C5E61F00AF0127C2D92F56DD34E3DCB (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * __this, String_t* ___name0, Type_t * ___type1, const RuntimeMethod* method)
{
	typedef AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * (*AssetBundle_LoadAssetWithSubAssetsAsync_Internal_mD671A7674C5E61F00AF0127C2D92F56DD34E3DCB_ftn) (AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 *, String_t*, Type_t *);
	static AssetBundle_LoadAssetWithSubAssetsAsync_Internal_mD671A7674C5E61F00AF0127C2D92F56DD34E3DCB_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundle_LoadAssetWithSubAssetsAsync_Internal_mD671A7674C5E61F00AF0127C2D92F56DD34E3DCB_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundle::LoadAssetWithSubAssetsAsync_Internal(System.String,System.Type)");
	AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * icallRetVal = _il2cpp_icall_func(__this, ___name0, ___type1);
	return icallRetVal;
}
// UnityEngine.AssetBundleRecompressOperation UnityEngine.AssetBundle::RecompressAssetBundleAsync(System.String,System.String,UnityEngine.BuildCompression,System.UInt32,UnityEngine.ThreadPriority)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31 * AssetBundle_RecompressAssetBundleAsync_m18E3EE37DA690233BED8E8E266D3C605BD77D762 (String_t* ___inputPath0, String_t* ___outputPath1, BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  ___method2, uint32_t ___expectedCRC3, int32_t ___priority4, const RuntimeMethod* method)
{
	AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31 * V_0 = NULL;
	{
		String_t* L_0 = ___inputPath0;
		String_t* L_1 = ___outputPath1;
		BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  L_2 = ___method2;
		uint32_t L_3 = ___expectedCRC3;
		int32_t L_4 = ___priority4;
		AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31 * L_5;
		L_5 = AssetBundle_RecompressAssetBundleAsync_Internal_mEDC6D9885F3E663C7F2B2B57D3C52AD2435BE95A(L_0, L_1, L_2, L_3, L_4, /*hidden argument*/NULL);
		V_0 = L_5;
		goto IL_000f;
	}

IL_000f:
	{
		AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31 * L_6 = V_0;
		return L_6;
	}
}
// UnityEngine.AssetBundleRecompressOperation UnityEngine.AssetBundle::RecompressAssetBundleAsync_Internal(System.String,System.String,UnityEngine.BuildCompression,System.UInt32,UnityEngine.ThreadPriority)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31 * AssetBundle_RecompressAssetBundleAsync_Internal_mEDC6D9885F3E663C7F2B2B57D3C52AD2435BE95A (String_t* ___inputPath0, String_t* ___outputPath1, BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  ___method2, uint32_t ___expectedCRC3, int32_t ___priority4, const RuntimeMethod* method)
{
	{
		String_t* L_0 = ___inputPath0;
		String_t* L_1 = ___outputPath1;
		uint32_t L_2 = ___expectedCRC3;
		int32_t L_3 = ___priority4;
		AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31 * L_4;
		L_4 = AssetBundle_RecompressAssetBundleAsync_Internal_Injected_m3BFD8CEF04345BCD8F54C83993E5F587B9A16402(L_0, L_1, (BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F *)(&___method2), L_2, L_3, /*hidden argument*/NULL);
		return L_4;
	}
}
// System.UInt32 UnityEngine.AssetBundle::get_memoryBudgetKB()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t AssetBundle_get_memoryBudgetKB_m9F3859B7BCBC164757D2F767B2DA2D41F1A3CB3E (const RuntimeMethod* method)
{
	uint32_t V_0 = 0;
	{
		uint32_t L_0;
		L_0 = AssetBundleLoadingCache_get_memoryBudgetKB_m46A6F2C3142E554E68F9C2F804434938BBC5BBA0(/*hidden argument*/NULL);
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		uint32_t L_1 = V_0;
		return L_1;
	}
}
// System.Void UnityEngine.AssetBundle::set_memoryBudgetKB(System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssetBundle_set_memoryBudgetKB_m8C040A2AD6DD4ACB9EFD541BA54150AEC50BB9F0 (uint32_t ___value0, const RuntimeMethod* method)
{
	{
		uint32_t L_0 = ___value0;
		AssetBundleLoadingCache_set_memoryBudgetKB_mDBA50BB9E1F49290FB7EACE70C2CD6087C3A1D45(L_0, /*hidden argument*/NULL);
		return;
	}
}
// UnityEngine.AssetBundleRecompressOperation UnityEngine.AssetBundle::RecompressAssetBundleAsync_Internal_Injected(System.String,System.String,UnityEngine.BuildCompression&,System.UInt32,UnityEngine.ThreadPriority)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31 * AssetBundle_RecompressAssetBundleAsync_Internal_Injected_m3BFD8CEF04345BCD8F54C83993E5F587B9A16402 (String_t* ___inputPath0, String_t* ___outputPath1, BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F * ___method2, uint32_t ___expectedCRC3, int32_t ___priority4, const RuntimeMethod* method)
{
	typedef AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31 * (*AssetBundle_RecompressAssetBundleAsync_Internal_Injected_m3BFD8CEF04345BCD8F54C83993E5F587B9A16402_ftn) (String_t*, String_t*, BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F *, uint32_t, int32_t);
	static AssetBundle_RecompressAssetBundleAsync_Internal_Injected_m3BFD8CEF04345BCD8F54C83993E5F587B9A16402_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundle_RecompressAssetBundleAsync_Internal_Injected_m3BFD8CEF04345BCD8F54C83993E5F587B9A16402_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundle::RecompressAssetBundleAsync_Internal_Injected(System.String,System.String,UnityEngine.BuildCompression&,System.UInt32,UnityEngine.ThreadPriority)");
	AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31 * icallRetVal = _il2cpp_icall_func(___inputPath0, ___outputPath1, ___method2, ___expectedCRC3, ___priority4);
	return icallRetVal;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// Conversion methods for marshalling of: UnityEngine.AssetBundleCreateRequest
IL2CPP_EXTERN_C void AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A_marshal_pinvoke(const AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A& unmarshaled, AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A_marshaled_pinvoke& marshaled)
{
	marshaled.___m_Ptr_0 = unmarshaled.get_m_Ptr_0();
	marshaled.___m_completeCallback_1 = il2cpp_codegen_marshal_delegate(reinterpret_cast<MulticastDelegate_t*>(unmarshaled.get_m_completeCallback_1()));
}
IL2CPP_EXTERN_C void AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A_marshal_pinvoke_back(const AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A_marshaled_pinvoke& marshaled, AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tC1348BEB2C677FD60E4B65764CA3A1CAFF6DFB31_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t unmarshaled_m_Ptr_temp_0;
	memset((&unmarshaled_m_Ptr_temp_0), 0, sizeof(unmarshaled_m_Ptr_temp_0));
	unmarshaled_m_Ptr_temp_0 = marshaled.___m_Ptr_0;
	unmarshaled.set_m_Ptr_0(unmarshaled_m_Ptr_temp_0);
	unmarshaled.set_m_completeCallback_1(il2cpp_codegen_marshal_function_ptr_to_delegate<Action_1_tC1348BEB2C677FD60E4B65764CA3A1CAFF6DFB31>(marshaled.___m_completeCallback_1, Action_1_tC1348BEB2C677FD60E4B65764CA3A1CAFF6DFB31_il2cpp_TypeInfo_var));
}
// Conversion method for clean up from marshalling of: UnityEngine.AssetBundleCreateRequest
IL2CPP_EXTERN_C void AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A_marshal_pinvoke_cleanup(AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A_marshaled_pinvoke& marshaled)
{
}
// Conversion methods for marshalling of: UnityEngine.AssetBundleCreateRequest
IL2CPP_EXTERN_C void AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A_marshal_com(const AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A& unmarshaled, AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A_marshaled_com& marshaled)
{
	marshaled.___m_Ptr_0 = unmarshaled.get_m_Ptr_0();
	marshaled.___m_completeCallback_1 = il2cpp_codegen_marshal_delegate(reinterpret_cast<MulticastDelegate_t*>(unmarshaled.get_m_completeCallback_1()));
}
IL2CPP_EXTERN_C void AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A_marshal_com_back(const AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A_marshaled_com& marshaled, AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tC1348BEB2C677FD60E4B65764CA3A1CAFF6DFB31_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t unmarshaled_m_Ptr_temp_0;
	memset((&unmarshaled_m_Ptr_temp_0), 0, sizeof(unmarshaled_m_Ptr_temp_0));
	unmarshaled_m_Ptr_temp_0 = marshaled.___m_Ptr_0;
	unmarshaled.set_m_Ptr_0(unmarshaled_m_Ptr_temp_0);
	unmarshaled.set_m_completeCallback_1(il2cpp_codegen_marshal_function_ptr_to_delegate<Action_1_tC1348BEB2C677FD60E4B65764CA3A1CAFF6DFB31>(marshaled.___m_completeCallback_1, Action_1_tC1348BEB2C677FD60E4B65764CA3A1CAFF6DFB31_il2cpp_TypeInfo_var));
}
// Conversion method for clean up from marshalling of: UnityEngine.AssetBundleCreateRequest
IL2CPP_EXTERN_C void AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A_marshal_com_cleanup(AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A_marshaled_com& marshaled)
{
}
// UnityEngine.AssetBundle UnityEngine.AssetBundleCreateRequest::get_assetBundle()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * AssetBundleCreateRequest_get_assetBundle_m608C1516A7DC8E4B1F9D63EDCF6EE8D6C2CFF013 (AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * __this, const RuntimeMethod* method)
{
	typedef AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * (*AssetBundleCreateRequest_get_assetBundle_m608C1516A7DC8E4B1F9D63EDCF6EE8D6C2CFF013_ftn) (AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A *);
	static AssetBundleCreateRequest_get_assetBundle_m608C1516A7DC8E4B1F9D63EDCF6EE8D6C2CFF013_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundleCreateRequest_get_assetBundle_m608C1516A7DC8E4B1F9D63EDCF6EE8D6C2CFF013_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundleCreateRequest::get_assetBundle()");
	AssetBundle_t4D34D7FDF0F230DC641DC1FCFA2C0E7E9E628FA4 * icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
// System.Void UnityEngine.AssetBundleCreateRequest::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssetBundleCreateRequest__ctor_m7B04FFC9566D1B0F820DDE9844BA2822A74881B6 (AssetBundleCreateRequest_t6AB0C8676D1DAA5F624663445F46FAB7D63EAA3A * __this, const RuntimeMethod* method)
{
	{
		AsyncOperation__ctor_mFC0E13622A23CD19A631B9ABBA506683B71A2E4A(__this, /*hidden argument*/NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.UInt32 UnityEngine.AssetBundleLoadingCache::get_maxBlocksPerFile()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t AssetBundleLoadingCache_get_maxBlocksPerFile_m1103DED6546DF3CABC67E610DA226C367FC6177F (const RuntimeMethod* method)
{
	typedef uint32_t (*AssetBundleLoadingCache_get_maxBlocksPerFile_m1103DED6546DF3CABC67E610DA226C367FC6177F_ftn) ();
	static AssetBundleLoadingCache_get_maxBlocksPerFile_m1103DED6546DF3CABC67E610DA226C367FC6177F_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundleLoadingCache_get_maxBlocksPerFile_m1103DED6546DF3CABC67E610DA226C367FC6177F_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundleLoadingCache::get_maxBlocksPerFile()");
	uint32_t icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
// System.Void UnityEngine.AssetBundleLoadingCache::set_maxBlocksPerFile(System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssetBundleLoadingCache_set_maxBlocksPerFile_m9C8BCD2338E2A8E43FEB8541877C81052711EF79 (uint32_t ___value0, const RuntimeMethod* method)
{
	typedef void (*AssetBundleLoadingCache_set_maxBlocksPerFile_m9C8BCD2338E2A8E43FEB8541877C81052711EF79_ftn) (uint32_t);
	static AssetBundleLoadingCache_set_maxBlocksPerFile_m9C8BCD2338E2A8E43FEB8541877C81052711EF79_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundleLoadingCache_set_maxBlocksPerFile_m9C8BCD2338E2A8E43FEB8541877C81052711EF79_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundleLoadingCache::set_maxBlocksPerFile(System.UInt32)");
	_il2cpp_icall_func(___value0);
}
// System.UInt32 UnityEngine.AssetBundleLoadingCache::get_blockCount()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t AssetBundleLoadingCache_get_blockCount_m1C4097A665614846401024BDF05CE6AF86079962 (const RuntimeMethod* method)
{
	typedef uint32_t (*AssetBundleLoadingCache_get_blockCount_m1C4097A665614846401024BDF05CE6AF86079962_ftn) ();
	static AssetBundleLoadingCache_get_blockCount_m1C4097A665614846401024BDF05CE6AF86079962_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundleLoadingCache_get_blockCount_m1C4097A665614846401024BDF05CE6AF86079962_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundleLoadingCache::get_blockCount()");
	uint32_t icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
// System.Void UnityEngine.AssetBundleLoadingCache::set_blockCount(System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssetBundleLoadingCache_set_blockCount_mE518D94AB7139C672039F1B45A124F7B0CC87B98 (uint32_t ___value0, const RuntimeMethod* method)
{
	typedef void (*AssetBundleLoadingCache_set_blockCount_mE518D94AB7139C672039F1B45A124F7B0CC87B98_ftn) (uint32_t);
	static AssetBundleLoadingCache_set_blockCount_mE518D94AB7139C672039F1B45A124F7B0CC87B98_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundleLoadingCache_set_blockCount_mE518D94AB7139C672039F1B45A124F7B0CC87B98_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundleLoadingCache::set_blockCount(System.UInt32)");
	_il2cpp_icall_func(___value0);
}
// System.UInt32 UnityEngine.AssetBundleLoadingCache::get_blockSize()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t AssetBundleLoadingCache_get_blockSize_mE576EAB7984D4E402963633A1BB85A9464175F5B (const RuntimeMethod* method)
{
	typedef uint32_t (*AssetBundleLoadingCache_get_blockSize_mE576EAB7984D4E402963633A1BB85A9464175F5B_ftn) ();
	static AssetBundleLoadingCache_get_blockSize_mE576EAB7984D4E402963633A1BB85A9464175F5B_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundleLoadingCache_get_blockSize_mE576EAB7984D4E402963633A1BB85A9464175F5B_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundleLoadingCache::get_blockSize()");
	uint32_t icallRetVal = _il2cpp_icall_func();
	return icallRetVal;
}
// System.UInt32 UnityEngine.AssetBundleLoadingCache::get_memoryBudgetKB()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t AssetBundleLoadingCache_get_memoryBudgetKB_m46A6F2C3142E554E68F9C2F804434938BBC5BBA0 (const RuntimeMethod* method)
{
	uint32_t V_0 = 0;
	{
		uint32_t L_0;
		L_0 = AssetBundleLoadingCache_get_blockCount_m1C4097A665614846401024BDF05CE6AF86079962(/*hidden argument*/NULL);
		uint32_t L_1;
		L_1 = AssetBundleLoadingCache_get_blockSize_mE576EAB7984D4E402963633A1BB85A9464175F5B(/*hidden argument*/NULL);
		V_0 = ((int32_t)il2cpp_codegen_multiply((int32_t)L_0, (int32_t)L_1));
		goto IL_000f;
	}

IL_000f:
	{
		uint32_t L_2 = V_0;
		return L_2;
	}
}
// System.Void UnityEngine.AssetBundleLoadingCache::set_memoryBudgetKB(System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssetBundleLoadingCache_set_memoryBudgetKB_mDBA50BB9E1F49290FB7EACE70C2CD6087C3A1D45 (uint32_t ___value0, const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tA269614262430118C9FC5C4D9EF4F61C812568F0_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	uint32_t V_0 = 0;
	uint32_t V_1 = 0;
	bool V_2 = false;
	int32_t G_B3_0 = 0;
	{
		uint32_t L_0 = ___value0;
		uint32_t L_1;
		L_1 = AssetBundleLoadingCache_get_blockSize_mE576EAB7984D4E402963633A1BB85A9464175F5B(/*hidden argument*/NULL);
		IL2CPP_RUNTIME_CLASS_INIT(Math_tA269614262430118C9FC5C4D9EF4F61C812568F0_il2cpp_TypeInfo_var);
		uint32_t L_2;
		L_2 = Math_Max_m234A4B8AAD69E87A26F691B80AFA892662F57159(((int32_t)((uint32_t)(int32_t)L_0/(uint32_t)(int32_t)L_1)), 2, /*hidden argument*/NULL);
		V_0 = L_2;
		uint32_t L_3;
		L_3 = AssetBundleLoadingCache_get_blockCount_m1C4097A665614846401024BDF05CE6AF86079962(/*hidden argument*/NULL);
		uint32_t L_4;
		L_4 = Math_Max_m234A4B8AAD69E87A26F691B80AFA892662F57159(((int32_t)((uint32_t)(int32_t)L_3/(uint32_t)(int32_t)4)), 2, /*hidden argument*/NULL);
		V_1 = L_4;
		uint32_t L_5 = V_0;
		uint32_t L_6;
		L_6 = AssetBundleLoadingCache_get_blockCount_m1C4097A665614846401024BDF05CE6AF86079962(/*hidden argument*/NULL);
		if ((!(((uint32_t)L_5) == ((uint32_t)L_6))))
		{
			goto IL_0032;
		}
	}
	{
		uint32_t L_7 = V_1;
		uint32_t L_8;
		L_8 = AssetBundleLoadingCache_get_maxBlocksPerFile_m1103DED6546DF3CABC67E610DA226C367FC6177F(/*hidden argument*/NULL);
		G_B3_0 = ((((int32_t)((((int32_t)L_7) == ((int32_t)L_8))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		goto IL_0033;
	}

IL_0032:
	{
		G_B3_0 = 1;
	}

IL_0033:
	{
		V_2 = (bool)G_B3_0;
		bool L_9 = V_2;
		if (!L_9)
		{
			goto IL_0047;
		}
	}
	{
		uint32_t L_10 = V_0;
		AssetBundleLoadingCache_set_blockCount_mE518D94AB7139C672039F1B45A124F7B0CC87B98(L_10, /*hidden argument*/NULL);
		uint32_t L_11 = V_1;
		AssetBundleLoadingCache_set_maxBlocksPerFile_m9C8BCD2338E2A8E43FEB8541877C81052711EF79(L_11, /*hidden argument*/NULL);
	}

IL_0047:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.String[] UnityEngine.AssetBundleManifest::GetAllAssetBundles()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* AssetBundleManifest_GetAllAssetBundles_m88F1FE72F516B548E430E76EC3E75669BDE822FF (AssetBundleManifest_tC4C999A85A1FC2B60215566D0462EE645674160A * __this, const RuntimeMethod* method)
{
	typedef StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* (*AssetBundleManifest_GetAllAssetBundles_m88F1FE72F516B548E430E76EC3E75669BDE822FF_ftn) (AssetBundleManifest_tC4C999A85A1FC2B60215566D0462EE645674160A *);
	static AssetBundleManifest_GetAllAssetBundles_m88F1FE72F516B548E430E76EC3E75669BDE822FF_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundleManifest_GetAllAssetBundles_m88F1FE72F516B548E430E76EC3E75669BDE822FF_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundleManifest::GetAllAssetBundles()");
	StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
// System.String[] UnityEngine.AssetBundleManifest::GetAllAssetBundlesWithVariant()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* AssetBundleManifest_GetAllAssetBundlesWithVariant_m5FA63E47B38707E48A758DF594B4A9E407AA0091 (AssetBundleManifest_tC4C999A85A1FC2B60215566D0462EE645674160A * __this, const RuntimeMethod* method)
{
	typedef StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* (*AssetBundleManifest_GetAllAssetBundlesWithVariant_m5FA63E47B38707E48A758DF594B4A9E407AA0091_ftn) (AssetBundleManifest_tC4C999A85A1FC2B60215566D0462EE645674160A *);
	static AssetBundleManifest_GetAllAssetBundlesWithVariant_m5FA63E47B38707E48A758DF594B4A9E407AA0091_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundleManifest_GetAllAssetBundlesWithVariant_m5FA63E47B38707E48A758DF594B4A9E407AA0091_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundleManifest::GetAllAssetBundlesWithVariant()");
	StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
// UnityEngine.Hash128 UnityEngine.AssetBundleManifest::GetAssetBundleHash(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Hash128_t1858EA099934FD6F2B769E5661C17A276A2AFE7A  AssetBundleManifest_GetAssetBundleHash_m3FB11557D4B1D4B52F2CF27B4E5BBCA298FFB07A (AssetBundleManifest_tC4C999A85A1FC2B60215566D0462EE645674160A * __this, String_t* ___assetBundleName0, const RuntimeMethod* method)
{
	Hash128_t1858EA099934FD6F2B769E5661C17A276A2AFE7A  V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		String_t* L_0 = ___assetBundleName0;
		AssetBundleManifest_GetAssetBundleHash_Injected_m99060916778ACCDF8476EB4B97F095FA93144D58(__this, L_0, (Hash128_t1858EA099934FD6F2B769E5661C17A276A2AFE7A *)(&V_0), /*hidden argument*/NULL);
		Hash128_t1858EA099934FD6F2B769E5661C17A276A2AFE7A  L_1 = V_0;
		return L_1;
	}
}
// System.String[] UnityEngine.AssetBundleManifest::GetAllDependencies(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* AssetBundleManifest_GetAllDependencies_mF331B5591D058286EA4D5792A5DCA3556E29C033 (AssetBundleManifest_tC4C999A85A1FC2B60215566D0462EE645674160A * __this, String_t* ___assetBundleName0, const RuntimeMethod* method)
{
	typedef StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* (*AssetBundleManifest_GetAllDependencies_mF331B5591D058286EA4D5792A5DCA3556E29C033_ftn) (AssetBundleManifest_tC4C999A85A1FC2B60215566D0462EE645674160A *, String_t*);
	static AssetBundleManifest_GetAllDependencies_mF331B5591D058286EA4D5792A5DCA3556E29C033_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundleManifest_GetAllDependencies_mF331B5591D058286EA4D5792A5DCA3556E29C033_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundleManifest::GetAllDependencies(System.String)");
	StringU5BU5D_tACEBFEDE350025B554CD507C9AE8FFE49359549A* icallRetVal = _il2cpp_icall_func(__this, ___assetBundleName0);
	return icallRetVal;
}
// System.Void UnityEngine.AssetBundleManifest::GetAssetBundleHash_Injected(System.String,UnityEngine.Hash128&)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssetBundleManifest_GetAssetBundleHash_Injected_m99060916778ACCDF8476EB4B97F095FA93144D58 (AssetBundleManifest_tC4C999A85A1FC2B60215566D0462EE645674160A * __this, String_t* ___assetBundleName0, Hash128_t1858EA099934FD6F2B769E5661C17A276A2AFE7A * ___ret1, const RuntimeMethod* method)
{
	typedef void (*AssetBundleManifest_GetAssetBundleHash_Injected_m99060916778ACCDF8476EB4B97F095FA93144D58_ftn) (AssetBundleManifest_tC4C999A85A1FC2B60215566D0462EE645674160A *, String_t*, Hash128_t1858EA099934FD6F2B769E5661C17A276A2AFE7A *);
	static AssetBundleManifest_GetAssetBundleHash_Injected_m99060916778ACCDF8476EB4B97F095FA93144D58_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundleManifest_GetAssetBundleHash_Injected_m99060916778ACCDF8476EB4B97F095FA93144D58_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundleManifest::GetAssetBundleHash_Injected(System.String,UnityEngine.Hash128&)");
	_il2cpp_icall_func(__this, ___assetBundleName0, ___ret1);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// Conversion methods for marshalling of: UnityEngine.AssetBundleRecompressOperation
IL2CPP_EXTERN_C void AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31_marshal_pinvoke(const AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31& unmarshaled, AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31_marshaled_pinvoke& marshaled)
{
	marshaled.___m_Ptr_0 = unmarshaled.get_m_Ptr_0();
	marshaled.___m_completeCallback_1 = il2cpp_codegen_marshal_delegate(reinterpret_cast<MulticastDelegate_t*>(unmarshaled.get_m_completeCallback_1()));
}
IL2CPP_EXTERN_C void AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31_marshal_pinvoke_back(const AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31_marshaled_pinvoke& marshaled, AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tC1348BEB2C677FD60E4B65764CA3A1CAFF6DFB31_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t unmarshaled_m_Ptr_temp_0;
	memset((&unmarshaled_m_Ptr_temp_0), 0, sizeof(unmarshaled_m_Ptr_temp_0));
	unmarshaled_m_Ptr_temp_0 = marshaled.___m_Ptr_0;
	unmarshaled.set_m_Ptr_0(unmarshaled_m_Ptr_temp_0);
	unmarshaled.set_m_completeCallback_1(il2cpp_codegen_marshal_function_ptr_to_delegate<Action_1_tC1348BEB2C677FD60E4B65764CA3A1CAFF6DFB31>(marshaled.___m_completeCallback_1, Action_1_tC1348BEB2C677FD60E4B65764CA3A1CAFF6DFB31_il2cpp_TypeInfo_var));
}
// Conversion method for clean up from marshalling of: UnityEngine.AssetBundleRecompressOperation
IL2CPP_EXTERN_C void AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31_marshal_pinvoke_cleanup(AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31_marshaled_pinvoke& marshaled)
{
}
// Conversion methods for marshalling of: UnityEngine.AssetBundleRecompressOperation
IL2CPP_EXTERN_C void AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31_marshal_com(const AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31& unmarshaled, AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31_marshaled_com& marshaled)
{
	marshaled.___m_Ptr_0 = unmarshaled.get_m_Ptr_0();
	marshaled.___m_completeCallback_1 = il2cpp_codegen_marshal_delegate(reinterpret_cast<MulticastDelegate_t*>(unmarshaled.get_m_completeCallback_1()));
}
IL2CPP_EXTERN_C void AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31_marshal_com_back(const AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31_marshaled_com& marshaled, AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_tC1348BEB2C677FD60E4B65764CA3A1CAFF6DFB31_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	intptr_t unmarshaled_m_Ptr_temp_0;
	memset((&unmarshaled_m_Ptr_temp_0), 0, sizeof(unmarshaled_m_Ptr_temp_0));
	unmarshaled_m_Ptr_temp_0 = marshaled.___m_Ptr_0;
	unmarshaled.set_m_Ptr_0(unmarshaled_m_Ptr_temp_0);
	unmarshaled.set_m_completeCallback_1(il2cpp_codegen_marshal_function_ptr_to_delegate<Action_1_tC1348BEB2C677FD60E4B65764CA3A1CAFF6DFB31>(marshaled.___m_completeCallback_1, Action_1_tC1348BEB2C677FD60E4B65764CA3A1CAFF6DFB31_il2cpp_TypeInfo_var));
}
// Conversion method for clean up from marshalling of: UnityEngine.AssetBundleRecompressOperation
IL2CPP_EXTERN_C void AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31_marshal_com_cleanup(AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31_marshaled_com& marshaled)
{
}
// System.Void UnityEngine.AssetBundleRecompressOperation::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssetBundleRecompressOperation__ctor_m80DDD7E70B5E1A1C4A52E3A3BCB8DC4DA8C37EF7 (AssetBundleRecompressOperation_t960AA4671D6EB0A10A041FA29B8C2A7D70C07D31 * __this, const RuntimeMethod* method)
{
	{
		AsyncOperation__ctor_mFC0E13622A23CD19A631B9ABBA506683B71A2E4A(__this, /*hidden argument*/NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// Conversion methods for marshalling of: UnityEngine.AssetBundleRequest
IL2CPP_EXTERN_C void AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A_marshal_pinvoke(const AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A& unmarshaled, AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A_marshaled_pinvoke& marshaled)
{
	Exception_t* ___m_Type_3Exception = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field 'm_Type' of type 'AssetBundleRequest': Reference type field marshaling is not supported.");
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_Type_3Exception, NULL);
}
IL2CPP_EXTERN_C void AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A_marshal_pinvoke_back(const AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A_marshaled_pinvoke& marshaled, AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A& unmarshaled)
{
	Exception_t* ___m_Type_3Exception = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field 'm_Type' of type 'AssetBundleRequest': Reference type field marshaling is not supported.");
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_Type_3Exception, NULL);
}
// Conversion method for clean up from marshalling of: UnityEngine.AssetBundleRequest
IL2CPP_EXTERN_C void AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A_marshal_pinvoke_cleanup(AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A_marshaled_pinvoke& marshaled)
{
}
// Conversion methods for marshalling of: UnityEngine.AssetBundleRequest
IL2CPP_EXTERN_C void AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A_marshal_com(const AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A& unmarshaled, AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A_marshaled_com& marshaled)
{
	Exception_t* ___m_Type_3Exception = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field 'm_Type' of type 'AssetBundleRequest': Reference type field marshaling is not supported.");
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_Type_3Exception, NULL);
}
IL2CPP_EXTERN_C void AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A_marshal_com_back(const AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A_marshaled_com& marshaled, AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A& unmarshaled)
{
	Exception_t* ___m_Type_3Exception = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field 'm_Type' of type 'AssetBundleRequest': Reference type field marshaling is not supported.");
	IL2CPP_RAISE_MANAGED_EXCEPTION(___m_Type_3Exception, NULL);
}
// Conversion method for clean up from marshalling of: UnityEngine.AssetBundleRequest
IL2CPP_EXTERN_C void AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A_marshal_com_cleanup(AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A_marshaled_com& marshaled)
{
}
// UnityEngine.Object UnityEngine.AssetBundleRequest::GetResult()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * AssetBundleRequest_GetResult_m792F2C703230D18A0B6B18C86636961125B1B9A2 (AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * __this, const RuntimeMethod* method)
{
	typedef Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * (*AssetBundleRequest_GetResult_m792F2C703230D18A0B6B18C86636961125B1B9A2_ftn) (AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A *);
	static AssetBundleRequest_GetResult_m792F2C703230D18A0B6B18C86636961125B1B9A2_ftn _il2cpp_icall_func;
	if (!_il2cpp_icall_func)
	_il2cpp_icall_func = (AssetBundleRequest_GetResult_m792F2C703230D18A0B6B18C86636961125B1B9A2_ftn)il2cpp_codegen_resolve_icall ("UnityEngine.AssetBundleRequest::GetResult()");
	Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * icallRetVal = _il2cpp_icall_func(__this);
	return icallRetVal;
}
// UnityEngine.Object UnityEngine.AssetBundleRequest::get_asset()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * AssetBundleRequest_get_asset_mB0A96FBC026D143638E467DEB37228ACD55F1813 (AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * __this, const RuntimeMethod* method)
{
	Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * V_0 = NULL;
	{
		Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * L_0;
		L_0 = VirtFuncInvoker0< Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * >::Invoke(4 /* UnityEngine.Object UnityEngine.ResourceRequest::GetResult() */, __this);
		V_0 = L_0;
		goto IL_000a;
	}

IL_000a:
	{
		Object_tF2F3778131EFF286AF62B7B013A170F95A91571A * L_1 = V_0;
		return L_1;
	}
}
// System.Void UnityEngine.AssetBundleRequest::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AssetBundleRequest__ctor_mD09AF030644EF7F3386ABB3B5C593F61ADE25017 (AssetBundleRequest_tBCF59D1FD408125E4C2C937EC23AB0ABB7E4051A * __this, const RuntimeMethod* method)
{
	{
		ResourceRequest__ctor_m3E6B88D53CD7B4275A16194A6D94264BFE79D039(__this, /*hidden argument*/NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void UnityEngine.BuildCompression::set_compression(UnityEngine.CompressionType)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BuildCompression_set_compression_mC4B164F707B54FB8D8CAFF6BBBE2AF251BCCF462 (BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F * __this, int32_t ___value0, const RuntimeMethod* method)
{
	{
		int32_t L_0 = ___value0;
		__this->set__compression_5(L_0);
		return;
	}
}
IL2CPP_EXTERN_C  void BuildCompression_set_compression_mC4B164F707B54FB8D8CAFF6BBBE2AF251BCCF462_AdjustorThunk (RuntimeObject * __this, int32_t ___value0, const RuntimeMethod* method)
{
	int32_t _offset = 1;
	BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F * _thisAdjusted = reinterpret_cast<BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F *>(__this + _offset);
	BuildCompression_set_compression_mC4B164F707B54FB8D8CAFF6BBBE2AF251BCCF462(_thisAdjusted, ___value0, method);
}
// System.Void UnityEngine.BuildCompression::set_level(UnityEngine.CompressionLevel)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BuildCompression_set_level_m02EF1CDC04A51C053146A6F2FE2889197F751F98 (BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F * __this, int32_t ___value0, const RuntimeMethod* method)
{
	{
		int32_t L_0 = ___value0;
		__this->set__level_6(L_0);
		return;
	}
}
IL2CPP_EXTERN_C  void BuildCompression_set_level_m02EF1CDC04A51C053146A6F2FE2889197F751F98_AdjustorThunk (RuntimeObject * __this, int32_t ___value0, const RuntimeMethod* method)
{
	int32_t _offset = 1;
	BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F * _thisAdjusted = reinterpret_cast<BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F *>(__this + _offset);
	BuildCompression_set_level_m02EF1CDC04A51C053146A6F2FE2889197F751F98(_thisAdjusted, ___value0, method);
}
// System.Void UnityEngine.BuildCompression::set_blockSize(System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BuildCompression_set_blockSize_mF978E734073EC3863F93A845CD2AF4091B21F8AF (BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F * __this, uint32_t ___value0, const RuntimeMethod* method)
{
	{
		uint32_t L_0 = ___value0;
		__this->set__blockSize_7(L_0);
		return;
	}
}
IL2CPP_EXTERN_C  void BuildCompression_set_blockSize_mF978E734073EC3863F93A845CD2AF4091B21F8AF_AdjustorThunk (RuntimeObject * __this, uint32_t ___value0, const RuntimeMethod* method)
{
	int32_t _offset = 1;
	BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F * _thisAdjusted = reinterpret_cast<BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F *>(__this + _offset);
	BuildCompression_set_blockSize_mF978E734073EC3863F93A845CD2AF4091B21F8AF(_thisAdjusted, ___value0, method);
}
// System.Void UnityEngine.BuildCompression::.ctor(UnityEngine.CompressionType,UnityEngine.CompressionLevel,System.UInt32)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BuildCompression__ctor_m6A5C664DDDCD0559A817D0C48B7AF10729B00B27 (BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F * __this, int32_t ___in_compression0, int32_t ___in_level1, uint32_t ___in_blockSize2, const RuntimeMethod* method)
{
	{
		il2cpp_codegen_initobj(__this, sizeof(BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F ));
		int32_t L_0 = ___in_compression0;
		BuildCompression_set_compression_mC4B164F707B54FB8D8CAFF6BBBE2AF251BCCF462((BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F *)__this, L_0, /*hidden argument*/NULL);
		int32_t L_1 = ___in_level1;
		BuildCompression_set_level_m02EF1CDC04A51C053146A6F2FE2889197F751F98((BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F *)__this, L_1, /*hidden argument*/NULL);
		uint32_t L_2 = ___in_blockSize2;
		BuildCompression_set_blockSize_mF978E734073EC3863F93A845CD2AF4091B21F8AF((BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F *)__this, L_2, /*hidden argument*/NULL);
		return;
	}
}
IL2CPP_EXTERN_C  void BuildCompression__ctor_m6A5C664DDDCD0559A817D0C48B7AF10729B00B27_AdjustorThunk (RuntimeObject * __this, int32_t ___in_compression0, int32_t ___in_level1, uint32_t ___in_blockSize2, const RuntimeMethod* method)
{
	int32_t _offset = 1;
	BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F * _thisAdjusted = reinterpret_cast<BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F *>(__this + _offset);
	BuildCompression__ctor_m6A5C664DDDCD0559A817D0C48B7AF10729B00B27(_thisAdjusted, ___in_compression0, ___in_level1, ___in_blockSize2, method);
}
// System.Void UnityEngine.BuildCompression::.cctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BuildCompression__cctor_mFD1297745EB6A0C83DADF8470F430036802EA81F (const RuntimeMethod* method)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  L_0;
		memset((&L_0), 0, sizeof(L_0));
		BuildCompression__ctor_m6A5C664DDDCD0559A817D0C48B7AF10729B00B27((&L_0), 0, 5, ((int32_t)131072), /*hidden argument*/NULL);
		((BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F_StaticFields*)il2cpp_codegen_static_fields_for(BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F_il2cpp_TypeInfo_var))->set_Uncompressed_0(L_0);
		BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  L_1;
		memset((&L_1), 0, sizeof(L_1));
		BuildCompression__ctor_m6A5C664DDDCD0559A817D0C48B7AF10729B00B27((&L_1), 3, 5, ((int32_t)131072), /*hidden argument*/NULL);
		((BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F_StaticFields*)il2cpp_codegen_static_fields_for(BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F_il2cpp_TypeInfo_var))->set_LZ4_1(L_1);
		BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  L_2;
		memset((&L_2), 0, sizeof(L_2));
		BuildCompression__ctor_m6A5C664DDDCD0559A817D0C48B7AF10729B00B27((&L_2), 1, 5, ((int32_t)131072), /*hidden argument*/NULL);
		((BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F_StaticFields*)il2cpp_codegen_static_fields_for(BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F_il2cpp_TypeInfo_var))->set_LZMA_2(L_2);
		BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  L_3 = ((BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F_StaticFields*)il2cpp_codegen_static_fields_for(BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F_il2cpp_TypeInfo_var))->get_Uncompressed_0();
		((BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F_StaticFields*)il2cpp_codegen_static_fields_for(BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F_il2cpp_TypeInfo_var))->set_UncompressedRuntime_3(L_3);
		BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F  L_4;
		memset((&L_4), 0, sizeof(L_4));
		BuildCompression__ctor_m6A5C664DDDCD0559A817D0C48B7AF10729B00B27((&L_4), 2, 5, ((int32_t)131072), /*hidden argument*/NULL);
		((BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F_StaticFields*)il2cpp_codegen_static_fields_for(BuildCompression_t087855D9CA58769969BA27ADFC31DD8CE80D389F_il2cpp_TypeInfo_var))->set_LZ4Runtime_4(L_4);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t String_get_Length_m129FC0ADA02FECBED3C0B1A809AE84A5AEE1CF09_inline (String_t* __this, const RuntimeMethod* method)
{
	{
		int32_t L_0 = __this->get_m_stringLength_0();
		return L_0;
	}
}
